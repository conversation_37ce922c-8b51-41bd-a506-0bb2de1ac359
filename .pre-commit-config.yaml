repos:
  - repo: ***************************:PRPT/gitlint.git
    rev: v0.13.1
    hooks:
      - id: gitlint
  - repo: https://github.com/PyCQA/flake8.git
    rev: 4.0.1
    hooks:
      - id: flake8
  - repo: ***************************:PRPT/pre-commit-hooks.git
    rev: v3.1.0
    hooks:
      - id: check-added-large-files
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-json
      - id: check-merge-conflict
      - id: check-yaml
        args: ["--unsafe"]
      - id: debug-statements
      - id: end-of-file-fixer
      - id: requirements-txt-fixer
      - id: trailing-whitespace
  - repo: ***************************:base-detection/autoflake.git
    rev: v1.4
    hooks:
      - id: autoflake
        args: ["--exclude=__init__.py", "--in-place", "--recursive", "--remove-all-unused-imports"]
  - repo: ***************************:PRPT/pre-commit-xenon.git
    rev: v0.1
    hooks:
      - id: xenon
        args: ["--max-modules=F", "--max-absolute=F"]
  - repo: ***************************:PRPT/black.git
    rev: 20.8b1
    hooks:
      - id: black
        args: ["--line-length=120"]
        additional_dependencies: [click==8.0.4]
  - repo: ***************************:PRPT/prpt-extra-check.git
    rev: v0.0.1
    hooks:
      - id: check-readme-exists
      - id: check-requirements-exists
