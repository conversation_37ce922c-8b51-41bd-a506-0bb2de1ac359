## Prerequisites

- Python 3.6+
- PyTorch 1.9.0
- CUDA 10.2

## Install

1. clone repo to ${root_dir}
    ```shell
    git clone --recursive ***************************:transformer/Perceptron.git
    ```

2.  in ${root_dir}, install
    * install requirements
        ```shell
        pip3 install -r requirements.txt
        ```
    * setup
        ```shell
        python3 setup.py develop --user
        ```
    * [Option] install mmde3d, only required by 3D experiment
        ```shell
        rlaunch --cpu 8 --gpu 1 --memory 10240 -- bash (mmcv, mmdet3d安装需要cuda)
        pip3 install mmcv-full==1.4.2 -f https://download.openmmlab.com/mmcv/dist/cu102/torch1.9.0/index.html
        pip3 install mmdet==2.20.0
        pip3 install mmsegmentation==0.20.2
        oss cp s3://generalDetection/opensource_wheels/mmdet3d-0.18.0-cp36-cp36m-linux_x86_64.whl .
        pip3 install mmdet3d-0.18.0-cp36-cp36m-linux_x86_64.whl
        ```
