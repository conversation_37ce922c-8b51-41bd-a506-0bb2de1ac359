⏺ 数据加载流程分析

  从配置文件可以看出，这是一个多任务联合训练实验，让我详细分析其数据加载和处理流程：

  1. 三个主要数据集

  OD数据集 (train_dataset_z10_od)

  - 数据源: OD_Z10_10W_BSL
  - 标注类型: 3D检测框 (box)
  - 传感器配置:
    - 激光雷达: ["rfu_front_2_lidar", "fuser_lidar"]
    - 相机可见状态: [0, 1, 2, 3]
  - 数据特征: 只使用关键帧 (only_key_frame=True)

  OCC数据集 (train_dataset_z10_occ_jixie)

  - 数据源: OCC_Z10_10W_BSL
  - 标注类型: 3D占用栅格 (occ)
  - 传感器配置:
    - 激光雷达: ["rfu_front_2_lidar"]
    - 参考雷达: "middle_lidar"

  MAP数据集 (train_dataset_map_mix)

  - 数据源: 混合数据集
    - z10_v5_train_0520gpfs (普通地图数据)
    - hd_z10_v2check_11w (高清地图数据)
  - 标注类型: 高精地图 (map)
  - 传感器配置: 无激光雷达，纯视觉

⏺ 2. 数据集组装流程

  # 1. 创建三个独立的数据集
  train_dataset_z10_od = PrivateE2EDataset(**self.data_train_cfg_z10_od)
  train_dataset_z10_occ_jixie = PrivateE2EDataset(**self.data_train_cfg_z10_occ_jixie)
  train_dataset_map = PrivateE2EDataset(**self.data_train_cfg_z10_map)
  train_dataset_maphd = PrivateE2EDataset(**self.data_train_cfg_z10_map_hd)

  # 2. 混合MAP数据集
  train_dataset_map_mix = torch.utils.data.ConcatDataset([train_dataset_map, train_dataset_maphd])

  # 3. 计算数据集边界
  dataset_boundaries = get_dataset_boundaries([
      train_dataset_z10_od,
      train_dataset_z10_occ_jixie,
      train_dataset_map_mix
  ])

  # 4. 合并所有数据集
  train_dataset = torch.utils.data.ConcatDataset([
      train_dataset_z10_od,
      train_dataset_z10_occ_jixie,
      train_dataset_map_mix
  ])

  3. 采样器配置 (FlexibleMultiTaskIntervalSamplerV2)

  sampler=FlexibleMultiTaskIntervalSamplerV2(
      train_dataset,
      dataset_boundaries,
      per_gpu_dataset_batch_sizes=[2, 2, 2],  # 每个GPU从3个任务各采样2个
      shuffle=True,
      intervals=[130, 850, 480],  # 采样间隔，控制数据使用比例
      drop_last=False,
  )

  采样间隔含义:
  - OD任务: 间隔130，使用数据集的 1/130
  - OCC任务: 间隔850，使用数据集的 1/850
  - MAP任务: 间隔480，使用数据集的 1/480

⏺ 4. 数据处理和标定流程

  让我查看 PrivateE2EDataset 的实现来了解数据处理流程：

⏺ Search(pattern: "class PrivateE2EDataset")
  ⎿  Found 1 file (ctrl+r to expand)

⏺ Read(perceptron/data/det3d/private/private_multimodal.py)
  ⎿  Read 100 lines (ctrl+r to expand)

⏺ Read(perceptron/data/det3d/private/private_multimodal.py)
  ⎿  Read 100 lines (ctrl+r to expand)

⏺ 完整的数据加载、处理和标定流程图

  基于分析，我整理出以下完整流程：

  数据加载、处理和标定流程
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 1. 数据集初始化阶段                                                              │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  创建三个独立数据集：                                                              │
  │  ├── OD数据集 (3D检测)                                                           │
  │  │   ├── 数据源: OD_Z10_10W_BSL                                                  │
  │  │   ├── 传感器: ["rfu_front_2_lidar", "fuser_lidar"] + 相机                    │
  │  │   └── 标注: 3D检测框                                                          │
  │  ├── OCC数据集 (3D占用栅格)                                                      │
  │  │   ├── 数据源: OCC_Z10_10W_BSL                                                 │
  │  │   ├── 传感器: ["rfu_front_2_lidar"] + 相机                                    │
  │  │   └── 标注: 3D占用栅格                                                        │
  │  └── MAP数据集 (高精地图)                                                        │
  │      ├── 数据源: z10_v5_train_0520gpfs + hd_z10_v2check_11w                     │
  │      ├── 传感器: 纯视觉 (无激光雷达)                                             │
  │      └── 标注: 高精地图                                                          │
  └─────────────────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 2. 数据集组装阶段                                                                │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  ├── 混合MAP数据集: ConcatDataset([普通地图, 高清地图])                          │
  │  ├── 计算数据集边界: get_dataset_boundaries()                                    │
  │  └── 合并所有数据集: ConcatDataset([OD, OCC, MAP混合])                          │
  └─────────────────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 3. DataLoader配置阶段                                                           │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  FlexibleMultiTaskIntervalSamplerV2 参数:                                       │
  │  ├── per_gpu_dataset_batch_sizes=[2, 2, 2] (每GPU从3任务各采样2个)              │
  │  ├── intervals=[130, 850, 480] (采样间隔，控制数据使用比例)                      │
  │  ├── shuffle=True (打乱数据)                                                    │
  │  └── drop_last=False (保留不完整批次)                                           │
  └─────────────────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 4. 数据采样阶段 (PrivateE2EDataset.__getitem__)                                 │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  ┌─────────────────────────────────────────────────────────────────────────────┐ │
  │  │ 数据获取循环:                                                                 │ │
  │  │ ├── 获取帧ID: frame_idx = self.loader_output["frame_index"][index]           │ │
  │  │ ├── 获取标注: annos = self.annotation.get_annos(index, data_dict)           │ │
  │  │ ├── 获取图像: img_info = self.image.get_images(index, data_dict)            │ │
  │  │ ├── 获取激光雷达: lidar_info = self.lidar.get_lidars(index, data_dict)      │ │
  │  │ ├── 获取雷达: radar_info = self.radar.get_radars(index, data_dict)           │ │
  │  │ └── 数据增强: data_dict = self.pipeline(data_dict)                           │ │
  │  └─────────────────────────────────────────────────────────────────────────────┘ │
  └─────────────────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 5. 标定处理阶段 (多传感器标定)                                                  │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  ├── 相机内参: camera_intrinsics (3x3矩阵)                                       │
  │  ├── 激光雷达到相机: lidar2camera (4x4外参矩阵)                                   │
  │  ├── 激光雷达到自车: lidar2ego (4x4外参矩阵)                                      │
  │  ├── 自车到全局: ego2global (4x4外参矩阵)                                         │
  │  ├── 图像增强矩阵: ida_mats (4x4矩阵)                                            │
  │  ├── BEV增强矩阵: bda_mat (4x4矩阵)                                              │
  │  └── ROI掩码: roi_mask (区域感兴趣掩码)                                          │
  └─────────────────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 6. 批次处理阶段 (collate_fn_wrapper)                                            │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  输出数据字典包含:                                                               │
  │  ├── 基础信息: frame_id, timestamp, meta_info                                   │
  │  ├── 图像数据: imgs (多视图图像)                                                 │
  │  ├── 点云数据: points (激光雷达点云)                                             │
  │  ├── 标注数据:                                                                  │
  │  │   ├── gt_boxes (3D检测框, Nx7)                                              │
  │  │   ├── gt_labels (类别标签, Nx1)                                              │
  │  │   ├── gt_semantic (占用栅格, HxW)                                            │
  │  │   └── gt_map (高精地图标注)                                                  │
  │  ├── 标定矩阵:                                                                  │
  │  │   ├── camera_intrinsics, lidar2camera                                        │
  │  │   ├── lidar2ego, ego2global                                                  │
  │  │   ├── ida_mats, bda_mat                                                      │
  │  │   └── roi_mask                                                              │
  │  └── 任务列表: task_list (["box", "occ", "map"])                                 │
  └─────────────────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
  ┌─────────────────────────────────────────────────────────────────────────────────┐
  │ 7. 模型输入阶段                                                                  │
  ├─────────────────────────────────────────────────────────────────────────────────┤
  │  批次数据送入多任务模型 VisionEncoder_MultiTask:                                │
  │  ├── 共享编码器: 提取多模态特征                                                  │
  │  ├── 任务特定解码器:                                                             │
  │  │   ├── 3D检测头: 处理gt_boxes, gt_labels                                      │
  │  │   ├── 占用栅格头: 处理gt_semantic                                             │
  │  │   └── 高精地图头: 处理gt_map                                                │
  │  └── 损失计算: 多任务联合损失                                                    │
  └─────────────────────────────────────────────────────────────────────────────────┘

⏺ 关键特点总结

  多任务数据平衡策略

  - 间隔采样: 通过 intervals=[130, 850, 480] 实现不同任务的数据量平衡
  - 批次分配: 每个GPU从3个任务各采样2个样本，保证任务均衡
  - 数据过滤: 训练时自动过滤无效样本（如无标注框的帧）

  多传感器标定处理

  - 统一坐标系: 所有传感器数据转换到统一的坐标系
  - 实时标定: 支持在线标定矩阵应用
  - ROI掩码: 通过 roi_mask 限制感兴趣区域

  数据增强流程

  - 图像增强: 包括仿射变换、去畸变等GPU加速处理
  - 点云增强: 包括旋转、缩放、平移等
  - 多模态同步: 保证图像和点云增强的一致性

  内存优化

  - 动态padding: 按批次最大尺寸进行padding，减少内存浪费
  - 稀疏采样: 通过间隔采样减少实际数据使用量
  - 批处理优化: 专门的collate函数处理变长数据

  这个实验设计了一个完整的多任务、多模态数据处理流水线，能够高效地处理大规模的自动驾驶感知任务。