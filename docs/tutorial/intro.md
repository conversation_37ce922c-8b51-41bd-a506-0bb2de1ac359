# 介绍

## 什么是 Perceptron?
Perceptron 是用于 3D 感知的 codebase.

## 代码结构

```shell
├── docs                                  文档,   某一个发版做记录
├── test                                  单元测试, 路径和 det3d 包一一对应
└── perceptron                            包含3D.2D 的所有感知
    ├── layers                            提供 1: .all,  2: "deprecated 6mouths"
    |   ├── losses
    |   |   ├── det2d
    |   |   ├── seg2d
    |   ├── blocks.py
    |
    ├── models
    |   ├── cls
    |   |   ├── resnet.py
    |   ├── det2d
    |   │   ├── third_impl
    |   ├── det3d
    |   │   ├── third_impl
    |   ├── seg2d
    |   │   ├── third_impl
    |   │   ├── bise_net.py
    |
    ├── exps
    │   ├── base_exp.py                   继承自lightning_model
    │   ├── det2d
    |   │   ├── cvpack2
    |   |   │   ├── retina_net_exp.py
    |   │   ├── detectron2
    │   ├── det3d
    |   │   ├── openpcdet
    |   |   │   ├──fcos3d_exp.py
    │   ├── seg2d
    |   |   ├──bise_net_exp.py
    |
    ├── Readme.md
    ├── engine                            callbacks 组成 trainer, evaluator 组成 CLI (command line interface)
    ├── utils
    ├── data
```
