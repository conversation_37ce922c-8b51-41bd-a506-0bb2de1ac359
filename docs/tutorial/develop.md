# 开发教程

## 开发流程
1. 安装开发所需依赖(已经安装了可跳过):

    ```
    pip install -r requirements-dev.txt
    pre-commit install
    ```
2. 开发功能:

    1. 先想好样例，或者先写好对拍的正确code，再coding，因为最后也会push大家写`unittest`.
    2. 先想好代码的输入输出，想好代码的大概流程，再coding.
    3. 一个函数最好先做输入的检查，而不是先写逻辑。

3. 写完功能后， 检查风格错误、检查语法错误、跑单元测试以及生成文档：

    ```
    # 检查代码风格错误
    make style_check
    # 自动format所有代码
    make format
    # 检查代码的语法错误 (会出一些不合规的建议，但不强求都按照建议修改)
    make lint
    # 跑单元测试
    make unittest
    ```

    >另外可以用`pydocstyle`来检查python文件中的`docstring` 是否规范。

    ```bash
    pydocstyle xxx.py
    ```

## Git 提交流程:

1.  提交commit请按照以下步骤:

    1. 请安装 `pre-commit install`。
    2. 提交前请逐个文件的`git diff`，并逐个文件 `git add xxx`, 请不要使用 `git add -A` 等提交方式。
    3. commit message 的写法:


2. 格式:

    ```
    `<type>(<scope>): <subject>`,
    where `<scope>` is optional
    ```

3. 举例:

    ```
    feat: add hat wobble
    ^--^  ^------------^
    |     |
    |     +-> Summary in present tense.
    |
    +-------> Type: chore, docs, feat, fix, refactor, style, or test.
    ```

4. 更多例子:

    - `feat`: (new feature for the user, not a new feature for build script)
    - `fix`: (bug fix for the user, not a fix to a build script)
    - `docs`: (changes to the documentation)
    - `style`: (formatting, missing semi colons, etc; no production code change)
    - `refactor`: (refactoring production code, eg. renaming a variable)
    - `test`: (adding missing tests, refactoring tests; no production code change)
    - `chore`: (updating grunt tasks etc; no production code change)

4. 参考:

    - https://gist.github.com/joshbuchea/6f47e86d2510bce28f8e7f42ae84c716
    - https://www.conventionalcommits.org/
    - https://seesparkbox.com/foundry/semantic_commit_messages
    - http://karma-runner.github.io/1.0/dev/git-commit-msg.html


## 文档开发流程

> 注意: ci里面已经设置了自动构建功能，除了 `master` 和 `docs` 分支, 其他分支 请不要 `push` 文档的内容

1. 更改文档内容 -> 提交 -> 合并：

    1. 从 `docs` 分支 `checkout` 新分支
    2. 修改了文档内容后，先在本地浏览，确保没有问题
    3. 如果本地没有问题了，单独只push文档修改的内容 （不要和别的代码开发 一同 `push` 了).
    4. 给`docs`分支提交MR

2. 本地 `浏览文档`, 默认会到 `docs/_build/html` , 可以在该目录下使用 `python3 -m http.server`来预览。
    ```bash
    cd docs && make html
    ```


## 代码风格

1. `代码风格` 请参考:  [google python coding style](https://zh-google-styleguide.readthedocs.io/en/latest/google-python-styleguide/python_style_rules/)

2. `docstring`可以参考: [sphinxcontrib-napoleon](https://sphinxcontrib-napoleon.readthedocs.io/en/latest/index.html)

3. 特殊的:  行长被限制在120个字符。
