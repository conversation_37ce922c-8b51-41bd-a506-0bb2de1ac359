Experiments manager for 3D Perceptron

[![Doc](https://img.shields.io/badge/doc-sphinx-blue)](http://transformer.pages-git-core.megvii-inc.com/Perceptron/)
[![Build](https://git-core.megvii-inc.com/base-detection/det3d/badges/master/pipeline.svg)](https://git-core.megvii-inc.com/transformer/Perceptron/pipelines)
[![Coverage](https://git-core.megvii-inc.com/transformer/Perceptron/badges/master/coverage.svg)](http://transformer.pages-git-core.megvii-inc.com/Perceptron/cov/)
## Install

Please refer to [INSTALL.md](INSTALL.md) for installation.

## Usage
E.g., 跑`resnet`:

```
# 训练：
# -d 指定卡，例如: '0', '0,1,2,3', '0-3'
# -b 指定，每张卡放多少图
python3 perceptron/exps/cls2d/resnet_exp.py -d 0-7 -b 32
```

```
# 测试：
# --eval 执行测试, 需要同时用--ckpt 指定权重文件
python3 perceptron/exps/cls2d/resnet_exp.py -d 0-7 -b 32 --eval --ckpt xxx.pth
```

### 环境变量

| 环境变量 | 类型 | 含义 | 默认值 |
| -------  | ---- | ---- | ------ |
| `E2E_USE_UNIFIED_CUDA_IMAGE_PROCESSOR` | boolean | 设置为 `true` 时, 使用车端图片前处理算法对图片 remap 和 resize; 设置为 `false` 时, 使用原有基于 PyTorch 的实现 | `false` |

## 开发
请严格 Follow [开发教程](http://transformer.pages-git-core.megvii-inc.com/Perceptron/tutorial/develop.html)

## 理念：
1. 除了`exp` 知道configure，其他的layers 和models 都不应该知道 具体配置.
2. 代码的执行 都以 `exp` 为入口,
