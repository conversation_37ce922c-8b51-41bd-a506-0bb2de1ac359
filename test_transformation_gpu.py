#!/usr/bin/env python3
"""
Test script to verify the modified transformation_gpu.py functionality.
"""

import torch
import numpy as np
from datetime import datetime
import os
import sys

# Add the perceptron path to sys.path if needed
sys.path.insert(0, '/Users/<USER>/projects/e2ead/perceptron')

try:
    from perceptron.data.det3d.modules.pipelines.transformation_gpu import ImageAffineTransGPU, ImageUndistortGPU
    print("Successfully imported transformation classes")
except ImportError as e:
    print("Failed to import:", str(e))
    sys.exit(1)

def test_image_affine_trans_gpu():
    """Test ImageAffineTransGPU class initialization and save functionality."""
    print("\n=== Testing ImageAffineTransGPU ===")
    
    try:
        # Initialize the class
        transformer = ImageAffineTransGPU(mode="train", img_norm=True)
        print("ImageAffineTransGPU initialized successfully")
        print("  - Save directory:", transformer.save_dir)
        print("  - Save enabled:", transformer.save_enabled)
        print("  - Save counter:", transformer.save_counter)

        # Check if save method exists
        if hasattr(transformer, '_save_augmented_data'):
            print("_save_augmented_data method exists")
        else:
            print("_save_augmented_data method missing")
            
    except Exception as e:
        print(f"Error testing ImageAffineTransGPU: {e}")

def test_image_undistort_gpu():
    """Test ImageUndistortGPU class initialization and save functionality."""
    print("\n=== Testing ImageUndistortGPU ===")

    try:
        # Initialize the class
        undistorter = ImageUndistortGPU(mode="train")
        print(f"ImageUndistortGPU initialized successfully")
        print(f"  - Save directory: {undistorter.save_dir}")
        print(f"  - Save enabled: {undistorter.save_enabled}")
        print(f"  - Save counter: {undistorter.save_counter}")

        # Check if save method exists
        if hasattr(undistorter, '_save_undistorted_data'):
            print("_save_undistorted_data method exists")
        else:
            print("_save_undistorted_data method missing")

    except Exception as e:
        print(f"Error testing ImageUndistortGPU: {e}")

def test_save_directory_creation():
    """Test if save directories can be created."""
    print("\n=== Testing Save Directory Creation ===")
    
    try:
        transformer = ImageAffineTransGPU()
        undistorter = ImageUndistortGPU()
        
        # Test directory creation
        os.makedirs(transformer.save_dir, exist_ok=True)
        os.makedirs(undistorter.save_dir, exist_ok=True)
        
        if os.path.exists(transformer.save_dir):
            print(f"Transformer save directory created: {transformer.save_dir}")
        else:
            print(f"Failed to create transformer save directory")

        if os.path.exists(undistorter.save_dir):
            print(f"Undistorter save directory created: {undistorter.save_dir}")
        else:
            print(f"Failed to create undistorter save directory")

    except Exception as e:
        print(f"Error testing directory creation: {e}")

if __name__ == "__main__":
    print("Testing modified transformation_gpu.py")
    print("=" * 50)
    
    test_image_affine_trans_gpu()
    test_image_undistort_gpu()
    test_save_directory_creation()
    
    print("\n" + "=" * 50)
    print("Test completed!")
