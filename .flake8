[pycodestyle]
max_line_length = 120

[flake8]
max-line-length = 120
max-doc-length = 120
ignore =
    # <PERSON><PERSON><PERSON> ignored errors by flake8
    E121, E123, E126, E226, E24, E704,
    # F401 module imported but unused
    F401, F403, F405
    # E203	whitespace before ‘:’ (conflict with black)
    E203,
    # E231 missing whitespace after ',' (conflict with black)
    E231,
    # E501	line too long (82 > 79 characters) (conflict with black)
    E501,
    # E741	do not use variables named ‘l’, ‘O’, or ‘I’
    E741,
    # W503	line break before binary operator (conflict with black)
    W503,
    # W504	line break after binary operator (conflict with black)
    W504,
    # W505	doc line too long (82 > 79 characters) (conflict with black)
    W505,
    # W605  invalid escape sequence ‘x’ (conflict with the use of latex within documentation comments)
    W605,
