```
└── perceptron
    ├── layers
    |   ├── losses
    |   |   ├── seg2d
    |   |   ├── det3d                     我们自己的det3d losses
    |   |   ├── mmdet2d                   mmdet2d 适配的 losses
    |   |   ├── mmdet3d                   mmdet3d 适配的 losses
    |   ├── head                          e.g. CenterPointHead
    |   |   ├── det3d
    |   |   |   ├── center_point_head.py
    |   |   |
    |   |   ├── mmdet2d
    |   |   |
    |   |   ├── mmdet3d
    |   |   |   ├── center_point_head.py
    |   |
    |   ├── blocks_3d                           放置backbone和其依赖的 各种block(比如 bottleneck),  另外输入要用到 3D 的信息,
    |   |   ├── det3d
    |   |   ├── mmdet3d
    |   |   |   ├── voxel_backbone.py           输入point_cloud, 输出 voxel feature.
    |   |   |   ├── point_pilar_backbone.py     输入point_cloud, 输出 2d feature map.
    |   |   |   ├── bevdet_backbone.py          输入2d feature map(或image) + rotation 矩阵, 输出一个bev系的2d feature map
    |   |
    |   ├── blocks_2d                           放置2d backbone和其 依赖的各种block, 另外输入2d image, 输出一层feature.
    |   |   ├── mmdet2d
    |   |   |   ├── resnet_block.py
    |   |   |   ├── mobilenet_block.py
```
