import math
import numpy as np
from functools import partial
import torch
from bisect import bisect_right

__all__ = [
    "OnecycleLRScheduler",
    "CosineLRScheduler",
    "WarmCosineLRScheduler",
    "YoloxWarmCosineLRScheduler",
    "StepLRScheduler",
    "GroupLRScheduler",
    "OnecycleLRScheduler",
    "TorchLRSchedulerWraperForStep",
]


class _LRScheduler:
    def __init__(self, optimizer, lr, iters_per_epoch, total_epochs):
        self._optimizer = optimizer
        self.lr = lr
        self.iters_per_epoch = iters_per_epoch
        self.total_epochs = total_epochs
        self.total_iters = iters_per_epoch * total_epochs
        self._get_lr = self._get_lr_func()
        self.step(0)

    def state_dict(self):
        return None

    def load_state_dict(self, state_dict):
        pass

    def step(self, iters):
        return self.update_lr(iters)

    def update_lr(self, iters):
        lr = self._get_lr(iters)
        for param_group in self._optimizer.param_groups:
            param_group["lr"] = lr
        return lr

    def _get_lr_func(self):
        raise NotImplementedError


class CosineLRScheduler(_LRScheduler):
    def __init__(self, optimizer, lr, iters_per_epoch, total_epochs, end_lr=0.0):
        self.end_lr = end_lr
        super(CosineLRScheduler, self).__init__(optimizer, lr, iters_per_epoch, total_epochs)

    def _get_lr_func(self):
        end_lr = self.end_lr
        if end_lr > 0:
            lr_func = partial(cos_w_end_lr, self.lr, self.total_iters, end_lr)
        else:
            lr_func = partial(cos_lr, self.lr, self.total_iters)
        return lr_func


class WarmCosineLRScheduler(CosineLRScheduler):
    def __init__(
        self, optimizer, lr, iters_per_epoch, total_epochs, warmup_epochs=0.0, warmup_lr_start=1e-6, end_lr=0.0
    ):
        self.warmup_epochs = warmup_epochs
        self.warmup_lr_start = warmup_lr_start
        self.current_iters = 0  # 新增：记录当前迭代次数
        self.load_from_dict = True
        super(WarmCosineLRScheduler, self).__init__(optimizer, lr, iters_per_epoch, total_epochs, end_lr)

    def _get_lr_func(self):
        warmup_total_iters = self.iters_per_epoch * self.warmup_epochs
        if warmup_total_iters == 0:
            return super(WarmCosineLRScheduler, self)._get_lr_func()
        warmup_lr_start = self.warmup_lr_start
        end_lr = self.end_lr
        if end_lr > 0:
            lr_func = partial(warm_cos_w_end_lr, self.lr, self.total_iters, warmup_total_iters, warmup_lr_start, end_lr)
        else:
            lr_func = partial(warm_cos_lr, self.lr, self.total_iters, warmup_total_iters, warmup_lr_start)
        return lr_func

    def state_dict(self):
        """返回需要保存的状态字典"""
        return {
            "current_iters": self.current_iters,  # 关键：保存当前进度
            "warmup_epochs": self.warmup_epochs,
            "warmup_lr_start": self.warmup_lr_start,
            "end_lr": self.end_lr,
            "lr": self.lr,
            "total_iters": self.total_iters,
            "iters_per_epoch": self.iters_per_epoch,
        }

    def load_state_dict(self, state_dict):
        """从状态字典恢复"""
        self.current_iters = state_dict["current_iters"]
        self.warmup_epochs = state_dict["warmup_epochs"]
        self.warmup_lr_start = state_dict["warmup_lr_start"]
        self.end_lr = state_dict["end_lr"]
        self.lr = state_dict["lr"]
        self.total_iters = state_dict["total_iters"]
        self.iters_per_epoch = state_dict["iters_per_epoch"]
        # 重建 lr_func
        self._get_lr = self._get_lr_func()
        # 恢复优化器的学习率
        self.update_lr(self.current_iters)

    def step(self, iters):
        self.current_iters = iters  # 更新当前迭代次数
        return self.update_lr(iters)


class YoloxWarmCosineLRScheduler(WarmCosineLRScheduler):
    def __init__(
        self,
        optimizer,
        lr,
        iters_per_epoch,
        total_epochs,
        warmup_epochs=0.0,
        warmup_lr_start=1e-6,
        no_aug_epochs=0,
        min_lr_ratio=0.2,
    ):
        self.no_aug_epochs = no_aug_epochs
        self.min_lr_ratio = min_lr_ratio
        super(YoloxWarmCosineLRScheduler, self).__init__(
            optimizer, lr, iters_per_epoch, total_epochs, warmup_epochs, warmup_lr_start, end_lr=0.0
        )

    def _get_lr_func(self):
        warmup_total_iters = self.iters_per_epoch * self.warmup_epochs
        if warmup_total_iters == 0:
            return super(WarmCosineLRScheduler, self)._get_lr_func()
        warmup_lr_start = self.warmup_lr_start
        no_aug_iters = self.iters_per_epoch * self.no_aug_epochs
        lr_func = partial(
            yolox_warm_cos_lr,
            self.lr,
            self.min_lr_ratio,
            self.total_iters,
            warmup_total_iters,
            warmup_lr_start,
            no_aug_iters,
        )
        return lr_func


class StepLRScheduler(_LRScheduler):
    def __init__(self, optimizer, lr, iters_per_epoch, total_epochs, milestones, gamma=0.1):
        self.milestones = milestones
        self.gamma = gamma
        super(StepLRScheduler, self).__init__(optimizer, lr, iters_per_epoch, total_epochs)

    def _get_lr_func(self):
        milestones = [int(self.total_iters * milestone / self.total_epochs) for milestone in self.milestones]
        gamma = self.gamma
        lr_func = partial(multistep_lr, self.lr, milestones, gamma)
        return lr_func


class WarmupStepLRScheduler(StepLRScheduler):
    def __init__(
        self,
        optimizer,
        lr,
        iters_per_epoch,
        total_epochs,
        milestones,
        warmup_total_iter=1000,
        warmup_factor=1.0 / 1000,
        gamma=0.1,
    ):
        self.milestones = milestones
        self.gamma = gamma
        self.warmup_total_iter = warmup_total_iter
        self.warmup_factor = warmup_factor
        super(WarmupStepLRScheduler, self).__init__(optimizer, lr, iters_per_epoch, total_epochs, milestones, gamma)

    def _get_lr_func(self):
        warmup_total_iters = min(self.iters_per_epoch, self.warmup_total_iter)
        if warmup_total_iters == 0:
            return super()._get_lr_func()

        milestones = [int(self.total_iters * milestone / self.total_epochs) for milestone in self.milestones]
        gamma = self.gamma
        warmup_factor = self.warmup_factor
        lr_func = partial(warm_linear_lr, self.lr, milestones, gamma, warmup_total_iters, warmup_factor)
        return lr_func


class ConstantLRScheduler(_LRScheduler):
    def __init__(self, optimizer, lr, iters_per_epoch, total_epochs):
        super(ConstantLRScheduler, self).__init__(optimizer, lr, iters_per_epoch, total_epochs)

    def _get_lr_func(self):
        return lambda _: self.lr


def cos_lr(lr, total_iters, iters):
    """Cosine learning rate"""
    lr *= 0.5 * (1.0 + math.cos(math.pi * iters / total_iters))
    return lr


def warm_cos_lr(lr, total_iters, warmup_total_iters, warmup_lr_start, iters):
    """Cosine learning rate with warm up."""
    if iters < warmup_total_iters:
        lr = (lr - warmup_lr_start) * iters / float(warmup_total_iters) + warmup_lr_start
    else:
        lr *= 0.5 * (1.0 + math.cos(math.pi * (iters - warmup_total_iters) / (total_iters - warmup_total_iters)))
    return lr


def yolox_warm_cos_lr(lr, min_lr_ratio, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter, iters):
    """Cosine learning rate with warm up."""
    min_lr = lr * min_lr_ratio
    if iters < warmup_total_iters:
        lr = (lr - warmup_lr_start) * pow(iters / float(warmup_total_iters), 2) + warmup_lr_start
    elif iters >= total_iters - no_aug_iter:
        lr = min_lr
    else:
        lr = min_lr + 0.5 * (lr - min_lr) * (
            1.0 + math.cos(math.pi * (iters - warmup_total_iters) / (total_iters - warmup_total_iters - no_aug_iter))
        )
    return lr


def multistep_lr(lr, milestones, gamma, iters):
    """MultiStep learning rate"""
    for milestone in milestones:
        lr *= gamma if iters >= milestone else 1.0
    return lr


def warm_linear_lr(lr, milestones, gamma, warmup_total_iters, warmup_factor, iters):
    if iters >= warmup_total_iters:
        lr = multistep_lr(lr, milestones, gamma, iters)
    else:
        alpha = float(iters) / warmup_total_iters
        lr *= warmup_factor * (1 - alpha) + alpha
    return lr


def warm_cos_w_end_lr(lr, total_iters, warmup_total_iters, warmup_lr_start, end_lr, iters):
    """Cosine learning rate with warm up."""
    if iters < warmup_total_iters:
        lr = (lr - warmup_lr_start) * iters / float(warmup_total_iters) + warmup_lr_start
    else:
        q = 0.5 * (1.0 + math.cos(math.pi * (iters - warmup_total_iters) / (total_iters - warmup_total_iters)))
        lr = lr * q + end_lr * (1 - q)
    return lr


def cos_w_end_lr(lr, total_iters, end_lr, iters):
    """Cosine learning rate"""
    q = 0.5 * (1.0 + math.cos(math.pi * iters / total_iters))
    lr = lr * q + end_lr * (1 - q)
    return lr


class GroupLRScheduler(_LRScheduler):
    """
    attach params_groups in optimizer with different lr_scheduler
    scheduler_dict: {"group1": scheduler1;, "group2": ...}
    """

    def __init__(self, scheduler_dict, optimizer, lr_dict, iters_per_epoch, total_epochs, **kwargs):
        assert isinstance(scheduler_dict, dict)
        self.scheduler_dict = scheduler_dict
        self.lr_dict = lr_dict
        self._current_lr_dict = lr_dict.copy()
        super(GroupLRScheduler, self).__init__(optimizer, lr_dict, iters_per_epoch, total_epochs)

    def update_lr(self, iters):
        for param_group in self._optimizer.param_groups:
            name = param_group["name"]
            lr = self._get_lr[name](iters)
            if isinstance(lr, tuple):
                lr, mom = lr
                if "momentum" in param_group.keys():
                    param_group["momentum"] = mom
                elif "betas" in param_group.keys():
                    ori_beta = param_group["betas"]
                    param_group["betas"] = (mom, ori_beta[1])
            param_group["lr"] = lr
            self._current_lr_dict[name] = lr
        return self._current_lr_dict

    def _get_lr_func(self):
        lr_func_dict = {}
        for name in self.scheduler_dict:
            lr_scheduler = self.scheduler_dict[name]
            assert isinstance(lr_scheduler, _LRScheduler)
            lr_func_dict[name] = lr_scheduler._get_lr
        return lr_func_dict


def onecycle_lr_and_mom(lr, lr_phases, mom_phases, iters):
    for start, end, func in lr_phases:
        if iters >= start:
            lr = func((iters - start) / (end - start))
    for start, end, func in mom_phases:
        if iters >= start:
            mom = func((iters - start) / (end - start))
    return lr, mom


class OnecycleLRSchedulerOrigin(_LRScheduler):
    def __init__(self, optimizer, lr, iters_per_epoch, total_epochs, moms, div_factor, pct_start):
        self.moms = moms
        self.div_factor = div_factor
        self.pct_start = pct_start
        super(OnecycleLRSchedulerOrigin, self).__init__(optimizer, lr, iters_per_epoch, total_epochs)

    def _get_lr_func(self):
        lr_phases = (
            (0, int(self.total_iters * self.pct_start), partial(annealing_cos, self.lr / self.div_factor, self.lr)),
            (
                int(self.total_iters * self.pct_start),
                self.total_iters,
                partial(annealing_cos, self.lr, self.lr / self.div_factor / 1e4),
            ),
        )
        mom_phases = (
            (0, int(self.total_iters * self.pct_start), partial(annealing_cos, *self.moms)),
            (int(self.total_iters * self.pct_start), self.total_iters, partial(annealing_cos, *self.moms[::-1])),
        )
        lr_func = partial(onecycle_lr_and_mom, self.lr, lr_phases, mom_phases)
        return lr_func

    def update_lr(self, iters):
        lr, mom = self._get_lr(iters)
        for param_group in self._optimizer.param_groups:
            param_group["lr"] = lr
            if "momentum" in param_group.keys():
                param_group["momentum"] = mom
            elif "betas" in param_group.keys():
                ori_beta = param_group["betas"]
                param_group["betas"] = (mom, ori_beta[1])
        return lr


class OnecycleLRScheduler(_LRScheduler):
    def __init__(self, optimizer, lr, iters_per_epoch, total_epochs, moms, div_factor, pct_start):
        self.moms = moms
        self.div_factor = div_factor
        self.pct_start = pct_start
        self.total_iters = iters_per_epoch * total_epochs
        optimizer.lr, optimizer.mom = lr, self.moms[0]

        self.lr_phases = (
            (0, int(self.total_iters * self.pct_start), partial(annealing_cos, lr / self.div_factor, lr)),
            (
                int(self.total_iters * self.pct_start),
                self.total_iters,
                partial(annealing_cos, lr, lr / self.div_factor / 1e4),
            ),
        )
        self.mom_phases = (
            (0, int(self.total_iters * self.pct_start), partial(annealing_cos, *self.moms)),
            (int(self.total_iters * self.pct_start), self.total_iters, partial(annealing_cos, *self.moms[::-1])),
        )
        super(OnecycleLRScheduler, self).__init__(optimizer, lr, iters_per_epoch, total_epochs)

    def _get_lr_func(self):
        pass

    def step(self, iters):
        return self.update_lr(iters)

    def update_lr(self, iters):
        for start, end, func in self.lr_phases:
            if iters >= start:
                lr = func((iters - start) / (end - start))
                self._optimizer.lr = lr
        for start, end, func in self.mom_phases:
            if iters >= start:
                mom = func((iters - start) / (end - start))
                self._optimizer.mom = mom
        return lr, mom


def annealing_cos(start, end, pct):
    # print(pct, start, end)
    "Cosine anneal from `start` to `end` as pct goes from 0.0 to 1.0."
    cos_out = np.cos(np.pi * pct) + 1
    return end + (start - end) / 2 * cos_out


class TorchLRSchedulerWraper(_LRScheduler):
    def __init__(self, torch_lr_scheduler, iters_per_epoch, total_epochs):
        self.torch_lr_scheduler = torch_lr_scheduler
        self.iters_per_epoch = iters_per_epoch
        self.total_epochs = total_epochs

    def step(self, iters):
        if iters % self.iters_per_epoch == 0:
            self.torch_lr_scheduler.step(iters // self.iters_per_epoch)
        lr = self.torch_lr_scheduler.get_last_lr()
        return lr

    def state_dict(self):
        return self.torch_lr_scheduler.state_dict()

    def load_state_dict(self, state_dict):
        return self.torch_lr_scheduler.load_state_dict(state_dict)

    def update_lr(self, iters):
        pass

    def _get_lr_func(self):
        pass


class TorchLRSchedulerWraperForStep(_LRScheduler):
    def __init__(self, torch_lr_scheduler, iters_per_epoch, total_epochs):
        self.torch_lr_scheduler = torch_lr_scheduler
        self.iters_per_epoch = iters_per_epoch
        self.total_epochs = total_epochs

    def step(self, iters):
        # if iters % self.iters_per_epoch == 0:
        #     self.torch_lr_scheduler.step(iters // self.iters_per_epoch)
        self.torch_lr_scheduler.step()
        lr = self.torch_lr_scheduler.get_last_lr()
        return lr

    def state_dict(self):
        return self.torch_lr_scheduler.state_dict()

    def load_state_dict(self, state_dict):
        return self.torch_lr_scheduler.load_state_dict(state_dict)

    def update_lr(self, iters):
        pass

    def _get_lr_func(self):
        pass


class SequentialLR(torch.optim.lr_scheduler._LRScheduler):
    def __init__(self, optimizer, schedulers, milestones, last_epoch=-1, verbose=False):
        for scheduler_idx in range(1, len(schedulers)):
            if schedulers[scheduler_idx].optimizer != schedulers[0].optimizer:
                raise ValueError(
                    "Sequential Schedulers expects all schedulers to belong to the same optimizer, but "
                    "got schedulers at index {} and {} to be different".format(0, scheduler_idx)
                )
        if len(milestones) != len(schedulers) - 1:
            raise ValueError(
                "Sequential Schedulers expects number of schedulers provided to be one more "
                "than the number of milestone points, but got number of schedulers {} and the "
                "number of milestones to be equal to {}".format(len(schedulers), len(milestones))
            )
        self._schedulers = schedulers
        self._milestones = milestones
        self.last_epoch = last_epoch + 1
        self.optimizer = optimizer
        self._last_lr = schedulers[0].get_last_lr()

    def step(self):
        self.last_epoch += 1
        idx = bisect_right(self._milestones, self.last_epoch)
        if idx > 0 and self._milestones[idx - 1] == self.last_epoch:
            self._schedulers[idx].step(0)
        else:
            self._schedulers[idx].step()
        self._last_lr = self._schedulers[idx].get_last_lr()

    def state_dict(self):
        """Returns the state of the scheduler as a :class:`dict`.

        It contains an entry for every variable in self.__dict__ which
        is not the optimizer.
        The wrapped scheduler states will also be saved.
        """
        state_dict = {key: value for key, value in self.__dict__.items() if key not in ("optimizer", "_schedulers")}
        state_dict["_schedulers"] = [None] * len(self._schedulers)

        for idx, s in enumerate(self._schedulers):
            state_dict["_schedulers"][idx] = s.state_dict()

        return state_dict

    def load_state_dict(self, state_dict):
        """Loads the schedulers state.

        Args:
            state_dict (dict): scheduler state. Should be an object returned
                from a call to :meth:`state_dict`.
        """
        _schedulers = state_dict.pop("_schedulers")
        self.__dict__.update(state_dict)
        # Restore state_dict keys in order to prevent side effects
        # https://github.com/pytorch/pytorch/issues/32756
        state_dict["_schedulers"] = _schedulers

        for idx, s in enumerate(_schedulers):
            self._schedulers[idx].load_state_dict(s)
