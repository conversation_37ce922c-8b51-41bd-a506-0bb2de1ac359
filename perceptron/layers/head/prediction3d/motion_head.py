import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast as autocast
from einops.einops import rearrange
from mmcv.cnn import Linear
from mmdet.models.utils import build_transformer

from perceptron.utils.e2e_utils.instance import Instances
from perceptron.layers.blocks_3d.prediction3d.modules import (
    PositionWiseFeedForward,
    SelfAttentional,
)
from perceptron.utils.e2e_utils.utils import time_position_embedding


class MotionHead(nn.Module):
    def __init__(
        self,
        hist_len=3,
        pred_len=60,
        num_reg_fcs=2,
        embed_dims=256,
        pc_range=[-51.2, -51.2, -5.0, 51.2, 51.2, 3.0],
        traj_transformer=None,
        use_hist_locs=False,
        use_map=False,
        apply_traj_social=False,
        apply_feat_transform=False,
        apply_autoencoder=False,
        fix=None,
        **kwargs,
    ):
        """
        Args:
            apply_traj_social: whether to apply social interaction for hist-traj feature, default False.
            apply_feat_transform: whether to apply coordinate transform for query feature, default False.
            apply_autoencoder: whether to apply hist-trajectory reconstruction when apply_feat_transform is True, default False.
        """
        super(MotionHead, self).__init__()

        self.hist_len = hist_len
        self.pred_len = pred_len
        self.interval = self.pred_len // 8
        self.num_reg_fcs = num_reg_fcs
        self.embed_dims = embed_dims
        self.pc_range = pc_range

        self.traj_transformer = traj_transformer
        self.use_hist_locs = use_hist_locs
        self.use_map = use_map

        self.apply_traj_social = apply_traj_social
        self.apply_feat_transform = apply_feat_transform
        self.apply_autoencoder = apply_autoencoder
        self.fix = fix

        self.init_params_and_layers()
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def init_params_and_layers(self):
        if self.use_hist_locs:
            # Modules for Trajectory Encoding
            self.locs_encoder = PositionWiseFeedForward(
                [3, self.embed_dims // 2, self.embed_dims],
                has_ln=True,
            )
            self.locs_time_wise_mixer = PositionWiseFeedForward(
                [20, self.embed_dims, 20],
                has_ln=True,
            )
            # Trajectory transformer with hist_locs / temporal pe
            self.ts_query_embed = nn.Sequential(
                nn.Linear(self.embed_dims, self.embed_dims),
                nn.ReLU(),
                nn.Linear(self.embed_dims, self.embed_dims),
            )
            self.traj_transformer = build_transformer(self.traj_transformer)

        # Modules for Agent Interaction
        if self.apply_traj_social:
            self.social_interactor = SelfAttentional(self.embed_dims, num_heads=4, layer_num=2)

        # Modules for Map Interaction
        if self.use_map:
            NotImplementedError

        # ffn for Trajectory Decoding
        self.fc1 = nn.Linear(self.embed_dims, self.embed_dims)
        self.layer_norm_1 = nn.LayerNorm(self.embed_dims)
        self.fc2 = nn.Linear(self.embed_dims, self.pred_len // self.interval * self.embed_dims)
        self.layer_norm_2 = nn.LayerNorm(self.pred_len * self.embed_dims // self.interval)

        # regression head
        reg_branch = []
        for _ in range(self.num_reg_fcs):
            reg_branch.append(Linear(self.embed_dims, self.embed_dims))
            reg_branch.append(nn.LayerNorm(self.embed_dims))
            reg_branch.append(nn.ReLU(inplace=True))
        reg_branch.append(Linear(self.embed_dims, 3))
        self.traj_reg = nn.Sequential(*reg_branch)

        if self.apply_autoencoder:
            his_traj_reg = []
            for _ in range(self.num_reg_fcs):
                his_traj_reg.append(Linear(self.embed_dims, self.embed_dims))
                his_traj_reg.append(nn.LayerNorm(self.embed_dims))
                his_traj_reg.append(nn.ReLU(inplace=True))
            his_traj_reg.append(Linear(self.embed_dims, 3))
            self.his_traj_reg = nn.Sequential(*his_traj_reg)

        return

    def forward(self, track_instances, lane_instances=None):
        # 1. Prepare for Motion Prediction
        track_instances = self.frame_shift(track_instances)

        track_instances = self.motion_prediction(track_instances, lane_instances)
        track_instances = self.motion_regression(track_instances)

        return track_instances

    def motion_prediction(self, track_instances, lane_instances):
        track_num = len(track_instances)
        device = track_instances.cache_query_feats.device

        if self.use_hist_locs:
            traj_hist_feat = self.encode_hist_locs(track_instances)  # 历史轨迹

            ts_pe = time_position_embedding(
                track_instances.fut_embeds.shape[0],
                traj_hist_feat.shape[1] + track_instances.fut_embeds.shape[1],
                self.embed_dims,
                device,
            )
            ts_pe = self.ts_query_embed(ts_pe)
            traj_embeds = self.traj_transformer(
                target=track_instances.fut_embeds,
                x=traj_hist_feat,
                query_embed=ts_pe[
                    :, traj_hist_feat.shape[1] : traj_hist_feat.shape[1] + track_instances.fut_embeds.shape[1], :
                ],
                pos_embed=ts_pe[:, : traj_hist_feat.shape[1], :],
                key_padding_mask=(track_instances.history_masks == 0).type(
                    torch.int
                ),  # Maybe a aug now, correct mask format is "track_instances.history_masks == 0"
            )

        if self.apply_traj_social:
            traj_embeds = self.social_interactor(traj_embeds.transpose(0, 1)).transpose(0, 1)

        if self.use_map and lane_instances is not None and len(lane_instances.enc_features) > 0:
            pass

        traj_embeds = F.relu(self.layer_norm_1(self.fc1(traj_embeds)))
        traj_embeds = F.relu(self.layer_norm_2(self.fc2(traj_embeds))).view(
            track_num, -1, self.interval, self.embed_dims
        )
        track_instances.traj_embeds = traj_embeds.transpose(1, 2).reshape(track_num, -1, self.embed_dims)
        return track_instances

    @autocast(False)
    def motion_regression(self, track_instances):
        """ Future Motion Regression """
        motion_preds = self.traj_reg(track_instances.traj_embeds)  # 500, 8, 256
        track_instances.traj_predictions = motion_preds
        return track_instances

    def encode_hist_locs(self, track_instances):
        N, T, C = track_instances.history_locs.shape
        input_data = track_instances.history_locs * track_instances.history_masks[..., None]
        locs_feat = self.locs_encoder(input_data)
        locs_feat = rearrange(locs_feat, "n t c -> n c t")
        locs_feat = self.locs_time_wise_mixer(locs_feat)
        locs_feat = rearrange(locs_feat, "n c t -> n t c")
        return locs_feat
        res = torch.sum(locs_feat, dim=1, keepdim=True)
        return res

    def frame_shift(self, track_instances: Instances):
        track_instances.traj_embeds = torch.cat(
            (track_instances.traj_embeds[:, 1:, :], torch.zeros_like(track_instances.traj_embeds[:, 0:1, :])), dim=1
        )
        track_instances.traj_predictions = torch.cat(
            (
                track_instances.traj_predictions[:, 1:, :],
                torch.zeros_like(track_instances.traj_predictions[:, 0:1, :]),
            ),
            dim=1,
        )
        track_instances.cache_traj_predictions = torch.cat(
            (
                track_instances.cache_traj_predictions[:, 1:, :],
                torch.zeros_like(track_instances.cache_traj_predictions[:, 0:1, :]),
            ),
            dim=1,
        )

        if not self.training and self.use_hist_locs:
            track_instances.history_locs = torch.cat(
                (track_instances.history_locs[:, 1:, :], track_instances.cache_bboxes[:, None, [0, 1, 4]]), dim=1
            )
            track_instances.history_masks = torch.cat(
                (
                    track_instances.history_masks[:, 1:],
                    torch.ones((len(track_instances), 1), dtype=torch.float, device=track_instances.query_feats.device),
                ),
                dim=1,
            )

        return track_instances
