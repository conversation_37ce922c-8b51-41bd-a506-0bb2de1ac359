import random
import torch
import torch.nn.functional as F
from torch import nn

from mmdet.models import build_loss
from mmdet.core import multi_apply

from perceptron.models.text.base import TextEncoder


class AttentionPool2d(nn.Module):
    def __init__(self, spacial_dim: int, embed_dim: int, num_heads: int, output_dim: int = None):
        super().__init__()
        """
        https://github.com/openai/CLIP/blob/main/clip/model.py#L8
        """
        self.positional_embedding = nn.Parameter(torch.randn(spacial_dim + 1, embed_dim) / embed_dim ** 0.5)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.c_proj = nn.Linear(embed_dim, output_dim or embed_dim)
        self.num_heads = num_heads

    def forward(self, x):
        x = x.flatten(start_dim=2).permute(2, 0, 1)  # NCHW -> (HW)NC
        x = torch.cat([x.mean(dim=0, keepdim=True), x], dim=0)  # (HW+1)NC
        x = x + self.positional_embedding[:, None, :].to(x.dtype)  # (HW+1)NC
        x, _ = F.multi_head_attention_forward(
            query=x[:1],
            key=x,
            value=x,
            embed_dim_to_check=x.shape[-1],
            num_heads=self.num_heads,
            q_proj_weight=self.q_proj.weight,
            k_proj_weight=self.k_proj.weight,
            v_proj_weight=self.v_proj.weight,
            in_proj_weight=None,
            in_proj_bias=torch.cat([self.q_proj.bias, self.k_proj.bias, self.v_proj.bias]),
            bias_k=None,
            bias_v=None,
            add_zero_attn=False,
            dropout_p=0,
            out_proj_weight=self.c_proj.weight,
            out_proj_bias=self.c_proj.bias,
            use_separate_proj_weight=True,
            training=self.training,
            need_weights=False,
        )
        return x.squeeze(0)


class TextAlignHead(nn.Module):
    """
    https://github.com/openai/CLIP/blob/main/clip/model.py#L10
    """

    def __init__(
        self,
        text_encoder_cfg=dict(),
        img_project_cfg=dict(),
        lidar_project_cfg=None,
        radar_project_cfg=None,
        img_aligned_cfg=dict(),
        loss_img_aligned_cfg=dict(type="", loss_weight=1.0),
        loss_lidar_aligned_cfg=None,
        loss_radar_aligned_cfg=None,
        class_names=[],
        text_prompt_template=[],
        pos_text_num=1,
        neg_text_num=1,
        init_cfg=None,
    ):
        assert init_cfg is None
        super(TextAlignHead, self).__init__()  # init_cfg=init_cfg
        # self.cfg = cfg
        self.img_aligned_cfg = img_aligned_cfg
        self.class_names = class_names
        self.text_prompt_template = text_prompt_template
        self.img_project_cfg = img_project_cfg
        self.pos_text_num = pos_text_num
        self.neg_text_num = neg_text_num

        self.img_project = self.get_img_project()
        self.text_encoder = TextEncoder(**text_encoder_cfg)
        self.loss_img_aligned_cfg = loss_img_aligned_cfg
        if lidar_project_cfg is not None:
            self.lidar_project = self.get_lidar_project()
            self.loss_lidar_aligned = build_loss(loss_lidar_aligned_cfg)
        if radar_project_cfg is not None:
            self.radar_project = self.get_radar_project()
            self.loss_radar_aligned = build_loss(loss_radar_aligned_cfg)

        self.logit_scale = nn.Parameter(torch.randn(1))
        self.logit_bias = nn.Parameter(torch.randn(1))

    def forward(
        self,
        data_shape,
        img_feats,
        radar_points=None,
        radar_output=None,
        lidar_feats=None,
        labels=None,
        bev_embedding=None,
        img_metas=None,
        **kwargs
    ):
        forward_ret_dict = {}  # 可用于存放其他需要的结果，暂时不需要

        img_aligned_feats = self.get_img_aligned_feat(data_shape, img_feats, bev_embedding, img_metas)
        aligned_feats = {"img_aligned_feats": img_aligned_feats}
        if hasattr(self, "lidar_project") and self.lidar_project is not None and lidar_feats is not None:
            lidar_aligned_feats = self.lidar_project(lidar_feats)
            aligned_feats["lidar_aligned_feats"] = lidar_aligned_feats
        if hasattr(self, "radar_project") and self.radar_project is not None and radar_output is not None:
            radar_aligned_feats = self.radar_project(radar_output)
            aligned_feats["radar_aligned_feats"] = radar_aligned_feats
        if self.training:
            texts = self.get_target_text("3d", img_metas)
            text_feat = self.text_encoder(texts)
            loss_dict = self.get_loss(text_feat, aligned_feats)
            return forward_ret_dict, loss_dict
        else:
            return forward_ret_dict, {}

    def _get_target_3d_single(self, gt_labels_3d):
        class_id, class_num = torch.unique(gt_labels_3d, return_counts=True)
        return class_id, class_num

    def _get_target_2d_single(self, gt_bboxes_3d, gt_labels_3d, img_meta):
        pass

    def get_target_text(self, target_type, img_metas=None):
        ff_gt_bboxes_list = img_metas["ff_gt_bboxes_list"]
        ff_gt_labels_list = img_metas["ff_gt_labels_list"]
        batch_size = len(ff_gt_bboxes_list)

        gt_boxes_3d_list = [ff_gt_bboxes_list[i] for i in range(batch_size)]
        gt_labels_3d_list = [ff_gt_labels_list[i] - 1 for i in range(batch_size)]

        if target_type == "3d":
            class_id_list, class_num_list = multi_apply(self._get_target_3d_single, gt_labels_3d_list)
        elif target_type == "2d":
            class_id_list, class_num_list = multi_apply(
                self._get_target_2d_single, gt_boxes_3d_list, gt_labels_3d_list, img_metas
            )
        else:
            assert False, "unknow get_text_target_type {}".format(target_type)
            return None
        texts = self.get_texts(class_id_list, class_num_list)  # N * len(class_num)
        return texts

    def get_texts(self, class_id_list, class_num_list):
        texts = []
        for class_ids, class_nums in zip(class_id_list, class_num_list):
            pos_text, neg_text = self.get_single_texts(class_ids, class_nums)
            texts.append(pos_text + neg_text)
        return texts

    def get_single_texts(self, class_ids, class_nums):
        pos_pair, neg_pair = [], []
        class_ids, class_nums = list(class_ids), list(class_nums)
        for class_id in range(len(self.class_names)):
            class_name = self.class_names[class_id]
            if class_id in class_ids:
                neg_pair.append(self.text_prompt_template[0].replace("CLASS_TOKEN", class_name))
                class_num = class_nums[class_ids.index(class_id)]
                if class_num == 1:
                    pos_pair.append(self.text_prompt_template[1].replace("CLASS_TOKEN", class_name))
                else:
                    # print(type(self.text_prompt_template[2]), class_name, type(class_name), class_num, type(class_num))
                    pos_pair.append(
                        self.text_prompt_template[2]
                        .replace("CLASS_TOKEN", class_name)
                        .replace("NUM_TOKEN", str(int(class_num)))
                    )
                    for ii in range(1, class_num):
                        pos_pair.append(
                            self.text_prompt_template[3]
                            .replace("CLASS_TOKEN", class_name)
                            .replace("NUM_TOKEN", str(ii))
                        )
                        neg_pair.append(
                            self.text_prompt_template[4]
                            .replace("CLASS_TOKEN", class_name)
                            .replace("NUM_TOKEN", str(ii))
                        )
                    for ii in range(class_num + 1, class_num + 5):
                        pos_pair.append(
                            self.text_prompt_template[4]
                            .replace("CLASS_TOKEN", class_name)
                            .replace("NUM_TOKEN", str(ii))
                        )
                        neg_pair.append(
                            self.text_prompt_template[3]
                            .replace("CLASS_TOKEN", class_name)
                            .replace("NUM_TOKEN", str(ii))
                        )
            else:
                pos_pair.append(self.text_prompt_template[0].replace("CLASS_TOKEN", class_name))
                neg_pair.append(self.text_prompt_template[1].replace("CLASS_TOKEN", class_name))
                neg_pair.append(
                    self.text_prompt_template[2]
                    .replace("CLASS_TOKEN", class_name)
                    .replace("NUM_TOKEN", str(random.randint(1, 10)))
                )

        random_num = 5
        while len(neg_pair) < self.neg_text_num or random_num > 0:
            random_num -= 1
            class_name = random.choice(self.class_names)
            class_id = self.class_names.index(class_name)
            class_num = random.randint(1, 15)
            real_class_num = class_nums[class_ids.index(class_id)] if class_id in class_ids else 0
            if class_num == real_class_num:
                continue
            if class_num > real_class_num:
                neg_pair.append(
                    self.text_prompt_template[3]
                    .replace("CLASS_TOKEN", class_name)
                    .replace("NUM_TOKEN", str(int(class_num)))
                )
            else:
                neg_pair.append(
                    self.text_prompt_template[4]
                    .replace("CLASS_TOKEN", class_name)
                    .replace("NUM_TOKEN", str(int(class_num)))
                )

        if len(neg_pair) < self.neg_text_num:
            neg_pair += random.sample(neg_pair, self.neg_text_num - len(neg_pair))
        return random.sample(pos_pair, self.pos_text_num), random.sample(neg_pair, self.neg_text_num)

    def get_loss(self, text_feat, aligned_feats):
        loss_img_aligned = self.get_img_aligned_loss(text_feat, aligned_feats["img_aligned_feats"])
        loss_dict = {"loss_img_aligned": loss_img_aligned}
        if aligned_feats.get("lidar_aligned_feats", None):
            loss_lidar_aligned = self.get_lidar_aligned_loss(text_feat, aligned_feats["lidar_aligned_feats"])
            loss_dict["loss_lidar_aligned"] = loss_lidar_aligned
        if aligned_feats.get("radar_aligned_feats", None):
            loss_radar_aligned = self.get_radar_aligned_loss(text_feat, aligned_feats["radar_aligned_feats"])
            loss_dict["loss_radar_aligned"] = loss_radar_aligned
        return loss_dict

    def get_img_aligned_loss(self, text_embeds, image_embeds):
        # print("text_embeds, image_embeds idx ", text_embeds.size(), image_embeds.size())
        all_loss = 0
        for bs_idx in range(len(image_embeds)):
            text_embed = text_embeds[bs_idx]
            image_embed = image_embeds[bs_idx : bs_idx + 1]
            # print("text_embeds, image_embeds idx ", bs_idx, text_embed.size(), image_embed.size())
            image_embed = image_embed / image_embed.norm(p=2, dim=-1, keepdim=True)
            text_embed = text_embed / text_embed.norm(p=2, dim=-1, keepdim=True)

            # cosine similarity as logits
            logits_per_text = torch.matmul(text_embed, image_embed.t().to(text_embed.device))
            # print("logits_per_text model", logits_per_text.size())
            logit_scale, logit_bias = self.logit_scale.to(text_embed.device), self.logit_bias.to(text_embed.device)
            logits_per_text = logits_per_text * logit_scale.exp() + logit_bias

            logits_per_image = logits_per_text.t()

            loss = 0
            if "text2img" in self.loss_img_aligned_cfg["loss_type"]:
                m1_diag1 = -torch.ones_like(logits_per_text)
                m1_diag1[0] = 1
                loglik1 = torch.nn.functional.logsigmoid(m1_diag1 * logits_per_text)
                nll1 = -torch.sum(loglik1, dim=-1)
                loss = loss + nll1.mean()
            if "img2text" in self.loss_img_aligned_cfg["loss_type"]:
                m1_diag2 = -torch.ones_like(logits_per_image)
                m1_diag2[0] = 1
                loglik2 = torch.nn.functional.logsigmoid(m1_diag2 * logits_per_image)
                nll2 = -torch.sum(loglik2, dim=-1)
                loss = loss + nll2.mean()
            all_loss = all_loss + loss
        loss_weight = self.loss_img_aligned_cfg.get("loss_weight", 1.0)
        all_loss = all_loss * loss_weight
        return all_loss

    def get_img_aligned_feat(self, data_shape, img_feats, bev_embedding, img_metas):
        img_feat_type = self.img_aligned_cfg["img_feat_type"]
        img_aligned_type = self.img_aligned_cfg["img_aligned_type"]
        if img_feat_type == "8v" and img_aligned_type == "3d":
            # img_feats =  # N * C * H * W
            bs, num_frame, num_cam, _, _, _ = data_shape
            # print("img_feats", img_feats.size())
            if img_feats.dim() == 4:
                N, C, H, W = img_feats.size()  # 16, 256, 32, 60
            elif img_feats.dim() == 5:
                bs, N, C, H, W = img_feats.size()
            img_feats = img_feats.view(bs, num_cam * C, H, W)
            img_aligned_feats = self.img_project(img_feats)
            return img_aligned_feats
        elif img_feat_type == "8v:4_2" and img_aligned_type == "3d":
            # img_feats =  # N * C * H * W
            bs, num_frame, num_cam, _, _, _ = data_shape
            if img_feats.dim() == 4:
                N, C, H, W = img_feats.size()  # 16, 256, 32, 60
            elif img_feats.dim() == 5:
                bs, N, C, H, W = img_feats.size()
            # N, C, H, W = img_feats.size()  # 16, 256, 32, 60
            img_feats = img_feats.view(bs, C, H * 4, W * 2)
            # print("img_feats ", img_feats.size())
            img_aligned_feats = self.img_project(img_feats)
            return img_aligned_feats

    def get_img_project(
        self,
    ):
        if self.img_project_cfg["type"] == "AttentionPool2d":
            self.img_project_cfg.pop("type")
            return AttentionPool2d(**self.img_project_cfg)
        else:
            assert False, "unknoew img_project type {}".format(self.img_project_cfg["type"])
            return None

    def get_radar_project(
        self,
    ):
        pass

    def get_lidar_project(
        self,
    ):
        pass
