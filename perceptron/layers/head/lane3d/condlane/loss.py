import copy
import torch
import torch.nn as nn
import torch.nn.functional as F
from perceptron.layers.head.lane3d.condlane.memory_utils import retry_if_cuda_oom
from perceptron.layers.head.lane3d.condlane.matcher import HungarianMatcher
from perceptron.layers.head.lane3d.condlane.misc import get_world_size, is_dist_avail_and_initialized


class SegCriterion(nn.Module):
    def __init__(self, ratio=0.5, ohem=True, ohem_ratio=0.3):
        super().__init__()
        self.ratio = ratio
        self.ohem = ohem
        self.ohem_ratio = ohem_ratio
        self.criterion = nn.CrossEntropyLoss()

    def forward(self, pred, gt):
        gt = gt.long()
        loss = self.criterion(pred, gt)
        if self.ohem:
            ohem_cls_loss = F.cross_entropy(pred, gt, reduction="none", ignore_index=-1)
            ohem_cls_loss = ohem_cls_loss.reshape(-1)
            keep_num = int(self.ohem_ratio * ohem_cls_loss.shape[0])
            _, idx = torch.sort(ohem_cls_loss, descending=True)
            keep_idx_cuda = idx[:keep_num]
            # 保留到需要keep的数目
            ohem_cls_loss = ohem_cls_loss[keep_idx_cuda]
            ohem_loss = ohem_cls_loss.sum() / keep_num
            return loss + ohem_loss
        else:
            return loss


class SetCriterion(nn.Module):
    """This class computes the loss for DETR.
    The process happens in two steps:
        1) we compute hungarian assignment between ground truth boxes and the outputs of the model
        2) we supervise each pair of matched ground-truth / prediction (supervise class and box)
    """

    def __init__(self, weight_dict, eos_coef=1.0):
        """Create the criterion.
        Parameters:
            eos_coef: relative classification weight applied to the no-object category
            matcher: module able to compute a matching between targets and proposals
        """
        super().__init__()
        self.obj_weight = weight_dict["obj_weight"]
        self.cls_weight = weight_dict["cls_weight"]
        self.loc_weight = weight_dict["loc_weight"]
        self.reg_weight = weight_dict["reg_weight"]
        self.rng_weight = weight_dict["rng_weight"]
        self.matcher = HungarianMatcher(**weight_dict)

        empty_weight = torch.ones(2)
        empty_weight[-1] = eos_coef
        self.register_buffer("empty_weight", empty_weight)

        catgr_weight = torch.tensor([[1.0, 10.0], [1.0, 30.0], [1.0, 20.0], [1.0, 10.0], [2.0, 1.0]])
        self.register_buffer("catgr_weight", catgr_weight)

    def loss_obj(self, logits, targets, indices):
        """Classification loss (NLL)
        targets dicts must contain the key "labels" containing a tensor of dim [nb_target_boxes]
        """
        device = logits.device
        idx = self._get_src_permutation_idx(indices)
        target_classes_o = torch.cat(
            [tgt["gt_label_obj"][J].long() for tgt, (_, J) in zip(targets, indices)]
        )  # (M1 + M2,)
        target_classes = torch.full(logits.shape[:2], 1, dtype=torch.int64, device=device)  # (N, L)
        target_classes[idx] = target_classes_o  # (M1 + M2,)
        loss_obj = F.cross_entropy(logits.transpose(1, 2), target_classes, self.empty_weight)  # (N, C, L) (N, L)
        return loss_obj

    def loss_cls(self, logits, targets, indices, num_lanes):
        """Classification loss (NLL)
        targets dicts must contain the key "labels" containing a tensor of dim [nb_target_boxes]
        """
        # logits: (N, L, 4, 2)
        idx = self._get_src_permutation_idx(indices)
        src_logits = logits[idx]  # (M1 + M2, 4, 2)
        tgt_labels = torch.cat([tgt["gt_label_cls"][i] for tgt, (_, i) in zip(targets, indices)])  # (M1 + M2, 4)

        loss_cls = 0
        for i in range(src_logits.shape[1]):
            loss_cls += F.cross_entropy(
                src_logits[:, i], tgt_labels[:, i], weight=self.catgr_weight[i], ignore_index=255, reduction="sum"
            )
        loss_cls = loss_cls / num_lanes
        return loss_cls

    @torch.no_grad()
    def loss_cardinality(self, logits, targets):
        """Compute the cardinality error, ie the absolute error in the number of predicted non-empty boxes
        This is not really a loss, it is intended for logging purposes only. It doesn't propagate gradients
        """
        device = logits.device
        tgt_lengths = torch.as_tensor([tgt["gt_label"].shape[0] for tgt in targets], device=device)
        card_pred = (logits.argmax(-1) != logits.shape[-1] - 1).sum(1)  # (N,)
        card_err = F.l1_loss(card_pred.float(), tgt_lengths.float())
        return card_err

    def loss_loc(self, masks, targets, indices, num_lanes):
        """Compute the losses related to the bounding boxes, the L1 regression loss and the GIoU loss
        targets dicts must contain the key "boxes" containing a tensor of dim [nb_target_boxes, 4]
        The targets boxes are expected in format (center_x, center_y, h, w), normalized by the image size.
        """
        device = masks.device
        # masks: (N, L, H, W)
        row_msks_x = masks.softmax(dim=3)  # (N, L, H, W)
        row_locs_x = torch.arange(0, row_msks_x.size(3), dtype=torch.float32, device=device)  # (W,)
        row_locs_x = (row_locs_x * row_msks_x).sum(dim=3)  # (N, L, H)

        idx = self._get_src_permutation_idx(indices)

        # softmax loss
        src_row_locs = row_locs_x[idx]  # (M1 + M2, H)
        tgt_row_locs = torch.cat([tgt["gt_row_loc_x"][i] for tgt, (_, i) in zip(targets, indices)])  # (M1 + M2, H)
        row_loc_msks = torch.cat([tgt["row_loc_mask"][i] for tgt, (_, i) in zip(targets, indices)])  # (M1 + M2, H)
        loss_row_loc = F.l1_loss(src_row_locs, tgt_row_locs, reduction="none")  # (M1 + M2, H)

        loss_row_loc = loss_row_loc * row_loc_msks  # (M1 + M2, H)
        loss_row_loc = loss_row_loc.sum(dim=1) / row_loc_msks.sum(dim=1).clamp(min=1.0)  # (M1 + M2)
        loss_row_loc = loss_row_loc.sum() / num_lanes

        # classification loss
        src_row_msks = masks[idx]  # (M1 + M2, H, W)
        loss_row_cls = F.cross_entropy(
            src_row_msks.permute(0, 2, 1), tgt_row_locs.long(), reduction="none"
        )  # (M1 + M2, H)
        loss_row_cls = loss_row_cls * row_loc_msks  # (M1 + M2, H)
        loss_row_cls = loss_row_cls.sum(dim=1) / row_loc_msks.sum(dim=1).clamp(min=1.0)  # (M1 + M2)
        loss_row_cls = loss_row_cls.sum() / num_lanes

        # mask difference
        diff_row_msk = src_row_msks[:, :-1] - src_row_msks[:, 1:]  # (M1 + M2, H-1, W)
        diff_row_msk = F.smooth_l1_loss(
            diff_row_msk, torch.zeros_like(diff_row_msk), reduction="none"
        )  # (M1 + M2, H-1, W)
        diff_row_msk = diff_row_msk.mean(dim=2)  # (M1 + M2, H-1)

        diff_row_msk = diff_row_msk * row_loc_msks[:, :-1]  # (M1 + M2, H-1)
        diff_row_msk = diff_row_msk.sum(dim=1) / row_loc_msks[:, :-1].sum(dim=1).clamp(min=1.0)  # (M1 + M2)
        diff_row_msk = diff_row_msk.sum() / num_lanes

        # location difference
        dif1_row_loc = F.l1_loss(src_row_locs[:, :-1], src_row_locs[:, 1:], reduction="none")  # (M1 + M2, H-1)
        dif1_row_loc = dif1_row_loc * row_loc_msks[:, :-1]  # (M1 + M2, H-1)
        dif1_row_loc = dif1_row_loc.sum(dim=1) / row_loc_msks[:, :-1].sum(dim=1).clamp(min=1.0)  # (M1 + M2, H-1)
        dif1_row_loc = dif1_row_loc.sum() / num_lanes

        dif2_row_loc = F.l1_loss(
            src_row_locs[:, :-2] + src_row_locs[:, 2:], src_row_locs[:, 1:-1] * 2.0, reduction="none"
        )  # (M1 + M2, H-2)
        dif2_row_loc = dif2_row_loc * row_loc_msks[:, :-2]  # (M1 + M2, H-2)
        dif2_row_loc = dif2_row_loc.sum(dim=1) / row_loc_msks[:, :-2].sum(dim=1).clamp(min=1.0)  # (M1 + M2)
        dif2_row_loc = dif2_row_loc.sum() / num_lanes

        loss_loc = loss_row_loc + loss_row_cls * 5.0 + diff_row_msk + dif1_row_loc + dif2_row_loc
        return loss_loc

    def loss_reg(self, regs, targets, indices, num_lanes):
        """Compute the losses related to the bounding boxes, the L1 regression loss and the GIoU loss
        targets dicts must contain the key "boxes" containing a tensor of dim [nb_target_boxes, 4]
        The targets boxes are expected in format (center_x, center_y, h, w), normalized by the image size.
        """
        # regs: (N, L * 2, H, W)
        idx = self._get_src_permutation_idx(indices)

        regs_x, regs_z = regs.chunk(2, dim=1)

        src_row_regs_x = regs_x[idx]  # (M1 + M2, H, W)
        tgt_row_regs_x = torch.cat([tgt["gt_row_reg_x"][i] for tgt, (_, i) in zip(targets, indices)])  # (M1 + M2, H, W)
        loss_row_reg_x = F.l1_loss(src_row_regs_x, tgt_row_regs_x, reduction="none")  # (M1 + M2, H, W)

        src_row_regs_z = regs_z[idx]  # (M1 + M2, H, W)
        tgt_row_regs_z = torch.cat([tgt["gt_row_reg_z"][i] for tgt, (_, i) in zip(targets, indices)])  # (M1 + M2, H, W)
        loss_row_reg_z = F.l1_loss(src_row_regs_z, tgt_row_regs_z, reduction="none")  # (M1 + M2, H, W)

        loss_row_reg = loss_row_reg_x + loss_row_reg_z  # (M1 + M2, H, W)
        row_reg_msks = torch.cat([tgt["row_reg_mask"][i] for tgt, (_, i) in zip(targets, indices)])  # (M1 + M2, H, W)
        loss_row_reg = loss_row_reg * row_reg_msks  # (M1 + M2, H, W)
        loss_row_reg = loss_row_reg.sum(dim=(1, 2)) / row_reg_msks.sum(dim=(1, 2)).clamp(min=1.0)  # (M1 + M2)

        loss_row_reg = loss_row_reg.sum() / num_lanes

        loss_reg = loss_row_reg
        return loss_reg

    def loss_rng(self, lane_ranges, targets, indices, num_lanes):
        # lane_ranges: (N, L, 2)
        row_rngs = lane_ranges  # (N, L, 2)

        idx = self._get_src_permutation_idx(indices)

        src_row_rngs = row_rngs[idx]  # (M1 + M2, 2)
        tgt_row_rngs = torch.cat(
            [tgt["gt_row_range"][i] for tgt, (_, i) in zip(targets, indices)], dim=0
        )  # (M1 + M2, 2)
        loss_row_rng = F.l1_loss(src_row_rngs, tgt_row_rngs, reduction="sum")
        loss_row_rng = loss_row_rng / num_lanes

        loss_rng = loss_row_rng
        return loss_rng

    @staticmethod
    def _get_src_permutation_idx(indices):
        # permute predictions following indices
        batch_idx = torch.cat([torch.full_like(src, i) for i, (src, _) in enumerate(indices)])  # (N * L,)
        src_idx = torch.cat([src for (src, _) in indices])  # (N * L,)
        return batch_idx, src_idx

    @staticmethod
    def _get_tgt_permutation_idx(indices):
        # permute targets following indices
        batch_idx = torch.cat([torch.full_like(tgt, i) for i, (_, tgt) in enumerate(indices)])  # (N * L,)
        tgt_idx = torch.cat([tgt for (_, tgt) in indices])  # (N * L,)
        return batch_idx, tgt_idx

    def forward(self, logits_obj, logits_cls, regs, masks, lane_ranges, targets):
        """This performs the loss computation.
        :param logits_obj:
        :param logits_cls:
        :param regs:
        :param masks:
        :param lane_ranges:
        :param targets:
        :return:
        """
        # Retrieve the matching between the outputs of the last layer and the targets
        device = logits_obj.device
        indices = retry_if_cuda_oom(self.matcher)(logits_obj, logits_cls, regs, masks, lane_ranges, targets)

        # Compute the average number of targets boxes accross all nodes, for normalization purposes
        num_lanes = sum(tgt["gt_label_obj"].shape[0] for tgt in targets)
        num_lanes = torch.as_tensor([num_lanes], dtype=torch.float, device=device)
        if is_dist_avail_and_initialized():
            torch.distributed.all_reduce(num_lanes)
        num_lanes = torch.clamp(num_lanes / get_world_size(), min=1).item()

        # Compute all the requested losses
        loss_obj = self.obj_weight * self.loss_obj(logits_obj, targets, indices)
        loss_cls = self.cls_weight * self.loss_cls(logits_cls, targets, indices, num_lanes)
        loss_loc = self.loc_weight * self.loss_loc(masks, targets, indices, num_lanes)
        loss_reg = self.reg_weight * self.loss_reg(regs, targets, indices, num_lanes)
        loss_rng = self.rng_weight * self.loss_rng(lane_ranges, targets, indices, num_lanes)

        return loss_obj, loss_cls, loss_loc, loss_reg, loss_rng


class CondLane3DLoss(nn.Module):
    def __init__(self, weight_dict, num_attributes=1, min_points=10, line_width=13, target_shape=(400, 200)):
        """
        :param weight_dict: 不同损失的比重
        :param num_attributes: 车道线属性的数量
        :param min_points: (无论DT还是GT)用于避免出现水平的车道线
        :param line_width: 用于计算offset
        :param target_shape: 在哪个尺寸上计算损失/匹配
        """
        super(CondLane3DLoss, self).__init__()
        self.num_attributes = num_attributes
        self.min_points = min_points
        self.line_width = line_width
        self.target_shape = target_shape
        self.rv_seg_weight = weight_dict["rv_seg_weight"]
        weight_dict_cp = copy.deepcopy(weight_dict)  # pop是非常危险的操作
        weight_dict_cp.pop("rv_seg_weight")
        self.criterion = SetCriterion(weight_dict=weight_dict_cp)
        self.seg_c = SegCriterion()

    @torch.no_grad()
    def preprocess(self, gt_masks):
        # gt_masks: (B, M, H, W)  M=7 -> 1 is x-y, 1 is z, 5 is attrs
        # TODO: move this function to dataset.py
        device = gt_masks.device
        # gt_masks = F.interpolate(gt_masks.float(), size=self.target_shape, mode='nearest')  # (N, D, H, W)
        mask_h, mask_w = self.target_shape

        targets = []
        for gt_mask in gt_masks:

            id_mask = gt_mask[0]  # (H, W)
            id_mask = F.one_hot(id_mask.long(), num_classes=-1).permute(2, 0, 1)[1:].float()  # (M, H, W)
            if len(id_mask) > 0:
                gt_keep = (id_mask.max(dim=2)[0].sum(dim=1) >= self.min_points) | (
                    id_mask.max(dim=1)[0].sum(dim=1) >= self.min_points
                )  # (M,)
                id_mask = id_mask[gt_keep]  # (M, H, W)

            if len(id_mask) > 0:
                label_cls = id_mask.flatten(1, 2).mm(gt_mask[2:].flatten(1, 2).t())  # (M, C)
                label_cls = label_cls / id_mask.sum(dim=(1, 2))[:, None]  # (M, C)
                label_cls = label_cls.long()  # (M, C)
                label_obj = torch.zeros(len(id_mask), dtype=torch.int64, device=device)  # (M,)

                row_crd_x = torch.arange(0, mask_w, dtype=torch.float32, device=device)  # (W,)
                row_loc_x = (id_mask * row_crd_x).sum(dim=2) / id_mask.sum(dim=2).clamp(min=1.0)  # (M, H)
                row_valid = id_mask.max(dim=2)[0]  # (M, H)

                row_start = row_valid.argmax(dim=1)  # (M,)
                row_end = row_valid.flip(1).argmax(dim=1)  # (M,)
                row_end = row_valid.size(1) - 1 - row_end  # (M,)
                row_range = torch.stack([row_start, row_end], dim=1) / mask_h  # (M, 2)

                row_reg_x = row_loc_x[:, :, None] - row_crd_x  # (M, H, W)
                row_reg_m = (
                    (row_reg_x >= -self.line_width) & (row_reg_x <= self.line_width) & row_valid[:, :, None].bool()
                )
                row_reg_x[~row_reg_m] = 0.0

                row_loc_z = (id_mask * gt_mask[1]).sum(dim=2) / id_mask.sum(dim=2).clamp(min=1.0)  # (M, H)
                row_reg_z = row_loc_z[:, :, None].repeat(1, 1, mask_w)  # (M, H, W)
                row_reg_z[~row_reg_m] = 0.0  # (M, H, W)

                row_loc_mask = row_valid  # (M, H)
                row_reg_mask = row_reg_m.float()  # (M, H, W)
            else:
                label_obj = torch.zeros((0,), dtype=torch.int64, device=device)
                label_cls = torch.zeros((0, self.num_attributes), dtype=torch.int64, device=device)
                row_range = torch.zeros((0, 2), dtype=torch.float32, device=device)
                row_loc_x = torch.zeros((0, mask_h), dtype=torch.float32, device=device)
                row_loc_z = torch.zeros((0, mask_h), dtype=torch.float32, device=device)
                row_loc_mask = torch.zeros((0, mask_h), dtype=torch.float32, device=device)
                row_reg_x = torch.zeros((0, mask_h, mask_w), dtype=torch.float32, device=device)
                row_reg_z = torch.zeros((0, mask_h, mask_w), dtype=torch.float32, device=device)
                row_reg_mask = torch.zeros((0, mask_h, mask_w), dtype=torch.float32, device=device)

            target = dict(
                gt_label_obj=label_obj,
                gt_label_cls=label_cls,
                gt_row_range=row_range,
                gt_row_loc_x=row_loc_x,
                gt_row_loc_z=row_loc_z,
                gt_row_reg_x=row_reg_x,
                gt_row_reg_z=row_reg_z,
                row_loc_mask=row_loc_mask,
                row_reg_mask=row_reg_mask,
            )
            targets.append(target)
        return targets

    def forward(self, inputs):

        # 目前只对lane进行适配，(B, C, M, H, W), C=1, which index is 0
        with torch.cuda.amp.autocast(enabled=False):
            targets = self.preprocess(inputs["targets"]["masks_bev"][:, 0].float())  # convert gt (B, M, H, W) to target

        loss_obj, loss_att, loss_loc, loss_reg, loss_rng, loss_rv_seg = 0, 0, 0, 0, 0, 0
        net_outputs = inputs["net_outputs"]
        for i in range(net_outputs["num_decoders"]):
            loss_obj_, loss_att_, loss_loc_, loss_reg_, loss_rng_ = self.criterion(
                net_outputs["object"][i],
                net_outputs["attrib"][i],
                net_outputs["offsets"][i],
                net_outputs["masks"][i],
                net_outputs["ranges"][i],
                targets,
            )
            loss_obj += loss_obj_
            loss_att += loss_att_
            loss_reg += loss_reg_
            loss_loc += loss_loc_
            loss_rng += loss_rng_

        if "masks_rv" in inputs["targets"].keys():
            loss_rv_seg += self.rv_seg_weight * self.seg_c(inputs["rv_seg_res"], inputs["targets"]["masks_rv"])

        return loss_obj, loss_att, loss_loc, loss_reg, loss_rng, loss_rv_seg
