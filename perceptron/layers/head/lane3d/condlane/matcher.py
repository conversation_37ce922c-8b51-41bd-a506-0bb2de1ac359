# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
"""
Modules to compute the matching cost and solve the corresponding LSAP.
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy.optimize import linear_sum_assignment


class HungarianMatcher(nn.Module):
    """This class computes an assignment between the targets and the predictions of the network

    For efficiency reasons, the targets don't include the no_object. Because of this, in general,
    there are more predictions than targets. In this case, we do a 1-to-1 matching of the best predictions,
    while the others are un-matched (and thus treated as non-objects).
    """

    def __init__(
        self,
        obj_weight: float = 1,
        cls_weight: float = 1,
        loc_weight: float = 1,
        reg_weight: float = 1,
        rng_weight: float = 1,
    ):
        """Creates the matcher"""
        super().__init__()
        self.obj_weight = obj_weight
        self.cls_weight = cls_weight
        self.loc_weight = loc_weight
        self.reg_weight = reg_weight
        self.rng_weight = rng_weight

    @torch.no_grad()
    def forward(self, logits_obj, logits_cls, regs, masks, lane_ranges, targets):
        """Performs the matching"""
        bs, num_queries = logits_obj.shape[:2]
        device = logits_obj.device

        # We flatten to compute the cost matrices in a batch
        out_prob = logits_obj.flatten(0, 1).softmax(-1)  # (N * L, 2)
        tgt_idxs = torch.cat([tgt["gt_label_obj"] for tgt in targets]).long()  # (M1 + M2,)

        # Compute the classification cost. Contrary to the loss, we don't use the NLL,
        # but approximate it in 1 - proba[targets class].
        # The 1 is a constant that doesn't change the matching, it can be ommitted.
        cost_obj = -out_prob[:, tgt_idxs]  # (N * L, M1 + M2)

        # Compute the attribute cost.
        # logits_cls: (N, L, 4, 2), Contrary to the loss, we don't use the NLL
        tgt_labels = torch.cat([tgt["gt_label_cls"] for tgt in targets])  # (M1 + M2, 4)
        out_logits = logits_cls.flatten(0, 1).softmax(-1)  # (N * L, 4, 2)
        out_logits = out_logits.unsqueeze(1).repeat(1, tgt_labels.size(0), 1, 1)  # (N * L, M1 + M2, 4, 2)
        tgt_labels = tgt_labels.unsqueeze(0).repeat(out_logits.size(0), 1, 1)  # (N * L, M1 + M2, 4)

        tgt_mask = tgt_labels == 255  # (N * L, M1 + M2, 4)
        tgt_labels[tgt_mask] = 0  # (N * L, M1 + M2, 4)
        cost_cls = -out_logits.gather(dim=-1, index=tgt_labels.unsqueeze(-1)).squeeze(-1)  # (N * L, M1 + M2, 4)
        cost_cls = (cost_cls * (1 - tgt_mask.float())).sum(dim=2)  # (N * L, M1 + M2)

        # Compute the location cost.
        # masks: (N, L, H, W)
        row_msks_x = masks.softmax(dim=3)  # (N, L, H, W)
        row_locs_x = torch.arange(0, row_msks_x.size(3), dtype=torch.float32, device=device)  # (W,)
        row_locs_x = (row_locs_x * row_msks_x).sum(dim=3)  # (N, L, H)

        out_row_locs = row_locs_x.flatten(0, 1)  # (N * L, H)
        tgt_row_locs = torch.cat([tgt["gt_row_loc_x"] for tgt in targets])  # (M1 + M2, H)
        row_loc_msks = torch.cat([tgt["row_loc_mask"] for tgt in targets])  # (M1 + M2, H)

        out_row_locs = out_row_locs.unsqueeze(1).repeat(1, tgt_row_locs.size(0), 1)  # (N * L, M1 + M2, H)
        tgt_row_locs = tgt_row_locs.unsqueeze(0).repeat(out_row_locs.size(0), 1, 1)  # (N * L, M1 + M2, H)
        cost_row_loc = F.l1_loss(out_row_locs, tgt_row_locs, reduction="none")  # (N * L, M1 + M2, H)
        cost_row_loc = cost_row_loc * row_loc_msks  # (N * L, M1 + M2, H)
        cost_row_loc = cost_row_loc.sum(dim=2) / row_loc_msks.sum(dim=1).clamp(min=1.0)  # (N * L, M1 + M2)

        out_row_msks = row_msks_x.flatten(0, 1)  # (N * L, H, W)
        out_row_msks = out_row_msks.unsqueeze(1).repeat(1, tgt_idxs.size(0), 1, 1)  # (N * L, M1 + M2, H, W)
        cost_row_cls = -out_row_msks.gather(dim=3, index=tgt_row_locs.long().unsqueeze(3))  # (N * L, M1 + M2, H, 1)
        cost_row_cls = cost_row_cls.squeeze(3) * row_loc_msks  # (N * L, M1 + M2, H)
        cost_row_cls = cost_row_cls.sum(dim=2) / row_loc_msks.sum(dim=1).clamp(min=1.0)  # (N * L, M1 + M2)

        cost_loc = cost_row_loc + cost_row_cls  # (N * L, M1 + M2)

        regs_x, regs_z = regs.chunk(2, dim=1)

        # Compute the regression loss
        out_row_regs_x = regs_x.flatten(0, 1)  # (N * L, H, W)
        tgt_row_regs_x = torch.cat([tgt["gt_row_reg_x"] for tgt in targets])  # (M1 + M2, H, W)
        out_row_regs_x = out_row_regs_x.unsqueeze(1).repeat(1, tgt_row_regs_x.size(0), 1, 1)  # (N * L, M1 + M2, H, W)
        tgt_row_regs_x = tgt_row_regs_x.unsqueeze(0).repeat(out_row_regs_x.size(0), 1, 1, 1)  # (N * L, M1 + M2, H, W)
        cost_row_reg_x = F.l1_loss(out_row_regs_x, tgt_row_regs_x, reduction="none")  # (N * L, M1 + M2, H, W)

        cost_row_reg = cost_row_reg_x
        row_reg_msks = torch.cat([tgt["row_reg_mask"] for tgt in targets])  # (M1 + M2, H, W)
        cost_row_reg = cost_row_reg * row_reg_msks  # (N * L, M1 + M2, H, W)
        cost_row_reg = cost_row_reg.sum(dim=(2, 3)) / row_reg_msks.sum(dim=(1, 2)).clamp(min=1.0)  # (N * L, M1 + M2)

        cost_reg = cost_row_reg

        # Compute the range cost.
        # lane_ranges: (N, L, 2 * 2)
        row_rngs = lane_ranges  # (N, L, 2)

        out_row_rngs = row_rngs.flatten(0, 1)  # (N * L, 2)
        tgt_row_rngs = torch.cat([tgt["gt_row_range"] for tgt in targets])  # (M1 + M2, 2)
        out_row_rngs = out_row_rngs.unsqueeze(1).repeat(1, tgt_row_rngs.size(0), 1)  # (N * L, M1 + M2, 2)
        tgt_row_rngs = tgt_row_rngs.unsqueeze(0).repeat(out_row_rngs.size(0), 1, 1)  # (N * L, M1 + M2, 2)
        cost_row_rng = F.l1_loss(out_row_rngs, tgt_row_rngs, reduction="none")  # (N * L, M1 + M2, 2)
        cost_row_rng = cost_row_rng.sum(dim=2)  # (N * L, M1 + M2)

        cost_rng = cost_row_rng  # (N * L, M1 + M2)

        # Final cost matrix
        C = (
            self.obj_weight * cost_obj
            + self.cls_weight * cost_cls
            + self.loc_weight * cost_loc
            + self.reg_weight * cost_reg
            + self.rng_weight * cost_rng
        )  # (N * L, M1 + M2)
        C = C.view(bs, num_queries, -1).cpu()  # (N, L, M1 + M2)

        sizes = [tgt["gt_label_obj"].shape[0] for tgt in targets]  # [M1, M2]
        # C.split(sizes, -1): [(N, L, M1), (N, L, M2)]
        # c[i] (L, Mi)
        # indices: [(L, L), (L, L), ...]
        indices = [linear_sum_assignment(c[i]) for i, c in enumerate(C.split(sizes, -1))]
        return [(torch.as_tensor(i, dtype=torch.int64), torch.as_tensor(j, dtype=torch.int64)) for i, j in indices]
