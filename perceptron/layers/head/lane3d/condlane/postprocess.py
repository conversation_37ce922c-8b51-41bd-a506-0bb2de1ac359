import torch


class CondLane3DPost(object):
    def __init__(
        self,
        num_attributes,
        min_points=5,
        line_width=13,
        target_shape=(400, 200),
        score_thresh=0.3,
        use_offset=True,
        map_range=(65, 5, 15, -15),
        map_resolution=(0.5, 0.15),
    ):
        super(CondLane3DPost, self).__init__()
        self.num_attributes = num_attributes
        self.min_points = min_points
        self.line_width = line_width
        self.target_shape = target_shape
        self.score_thresh = score_thresh
        self.use_offset = use_offset
        self.map_range = map_range
        self.map_resolution = map_resolution

    def __call__(self, inputs):

        net_outputs = inputs["net_outputs"]
        logits_obj = net_outputs["object"][-1]
        logits_att = net_outputs["attrib"][-1]
        regs = net_outputs["offsets"][-1]
        masks = net_outputs["masks"][-1]
        lane_ranges = net_outputs["ranges"][-1]
        device = logits_obj.device

        mask_h, mask_w = masks.shape[-2:]

        scores_obj = logits_obj.softmax(-1)[:, :, 0]  # (N, L, 2)
        labels_cls = logits_att.argmax(-1)  # (N, L, 5)

        regs_x, regs_z = regs.chunk(2, dim=1)

        # masks_x: (N, L, H, W)
        row_msks_x = masks.softmax(dim=3)  # (N, L, H, W)
        row_locs_x = torch.arange(0, row_msks_x.size(3), dtype=torch.float32, device=device)  # (W,)
        row_locs_x = (row_locs_x * row_msks_x).sum(dim=3)  # (N, L, H)

        if self.use_offset:
            row_regs_x = regs_x.gather(dim=3, index=row_locs_x.round().long().unsqueeze(3)).squeeze(3)  # (N, L, H)
            row_locs_x = row_locs_x.round().long().float() + row_regs_x  # (N, L, H)

        row_locs_z = regs_z.gather(dim=3, index=row_locs_x.round().long().unsqueeze(3)).squeeze(3)  # (N, L, H)

        row_locs = torch.stack([row_locs_x, row_locs_z], dim=-1)  # (N, L, H, 2)
        row_rngs = lane_ranges  # (N, L, 2)

        lan_vld_scores, lane_points, lane_attrib = [], [], []

        for i, (row_loc, row_rng, score_obj, lbl_cls) in enumerate(zip(row_locs, row_rngs, scores_obj, labels_cls)):

            lan_vld = score_obj >= self.score_thresh  # (L,)
            row_loc = row_loc[lan_vld]  # (M, H, 2)
            row_rng = row_rng[lan_vld]  # (M, 2)
            lbl_cls = lbl_cls[lan_vld]  # (M, 4)
            lan_vld_score = score_obj[lan_vld]  # (M, 2)

            row_sta = (row_rng[:, 0] * mask_h).round().clamp(min=0, max=mask_h - 1).long()  # (M,)
            row_end = (row_rng[:, 1] * mask_h).round().clamp(min=0, max=mask_h - 1).long()  # (M,)
            row_rng = torch.stack([row_sta, row_end], dim=1)  # (M, 2)

            lan_vld = (row_end - row_sta + 1) >= self.min_points
            row_loc = row_loc[lan_vld]  # (M, H, 2)
            row_rng = row_rng[lan_vld]  # (M, 2)
            lbl_cls = lbl_cls[lan_vld]  # (M, 4)
            lan_vld_score = lan_vld_score[lan_vld]  # (M, 2)

            num_lanes = row_loc.size(0)

            points_list, attrib_list = [], []
            for lane_idx in range(num_lanes):
                selected_ys = torch.arange(
                    row_rng[lane_idx, 0].item(), row_rng[lane_idx, 1].item() + 1, dtype=torch.float32, device=device
                )  # (P,)
                selected_xz = row_loc[lane_idx, selected_ys.long()]  # (P, 2)

                points = torch.cat([selected_ys[:, None], selected_xz], dim=1).cpu().numpy()  # (P, 3)
                attrib = lbl_cls[lane_idx].cpu().numpy()

                points[:, 0] = self.map_range[0] - points[:, 0] * self.map_resolution[0] - self.map_range[1]
                points[:, 1] = self.map_range[2] - points[:, 1] * self.map_resolution[1]
                points[:, 2] = points[:, 2] / 10.0

                points_list.append(points)
                attrib_list.append(attrib)

            lane_points.append(points_list)
            lane_attrib.append(attrib_list)
            lan_vld_scores.append(lan_vld_score.cpu().numpy().tolist())

        return lan_vld_scores, lane_points, lane_attrib
