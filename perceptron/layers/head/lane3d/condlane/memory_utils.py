from contextlib import contextmanager
from functools import wraps
import torch

__all__ = ["retry_if_cuda_oom"]


@contextmanager
def _ignore_torch_cuda_oom():
    try:
        yield
    except RuntimeError as e:
        if "CUDA out of memory. " in str(e):
            pass
        else:
            raise


def retry_if_cuda_oom(func):
    @wraps(func)
    def wrapped(*args, **kwargs):
        with _ignore_torch_cuda_oom():
            return func(*args, **kwargs)
        torch.cuda.empty_cache()
        return func(*args, **kwargs)

    return wrapped
