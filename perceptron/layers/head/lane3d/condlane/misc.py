import torch
import torch.distributed as dist
from torch import Tensor
from typing import List


def cal_num_params(num_layers, disable_coords, in_channels, channels, out_channels=1):
    weight_nums, bias_nums = [], []
    for l in range(num_layers):
        if l == num_layers - 1:
            if num_layers == 1 and not disable_coords:
                weight_nums.append((in_channels + 2) * out_channels)
            else:
                weight_nums.append(in_channels * out_channels)
            bias_nums.append(out_channels)
        elif l == 0:
            if not disable_coords:
                weight_nums.append((in_channels + 2) * channels)
            else:
                weight_nums.append(in_channels * channels)
            bias_nums.append(channels)
        else:
            weight_nums.append(channels * channels)
            bias_nums.append(channels)
    return weight_nums, bias_nums


def compute_locations(h, w, stride, device):
    shifts_x = torch.arange(0, w * stride, step=stride, dtype=torch.float32, device=device)
    shifts_y = torch.arange(0, h * stride, step=stride, dtype=torch.float32, device=device)
    shift_y, shift_x = torch.meshgrid(shifts_y, shifts_x, indexing="ij")
    shift_x = shift_x.reshape(-1)
    shift_y = shift_y.reshape(-1)
    locations = torch.stack((shift_x, shift_y), dim=1) + stride // 2
    return locations


def is_dist_avail_and_initialized():
    if not dist.is_available():
        return False
    if not dist.is_initialized():
        return False
    return True


def get_world_size():
    if not is_dist_avail_and_initialized():
        return 1
    return dist.get_world_size()


@torch.no_grad()
def accuracy(output, target, topk=(1,)):
    """Computes the precision@k for the specified values of k"""
    if target.numel() == 0:
        return [torch.zeros([], device=output.device)]
    maxk = max(topk)
    batch_size = target.size(0)

    _, pred = output.topk(maxk, 1, True, True)
    pred = pred.t()
    correct = pred.eq(target.view(1, -1).expand_as(pred))

    res = []
    for k in topk:
        correct_k = correct[:k].view(-1).float().sum(0)
        res.append(correct_k.mul_(100.0 / batch_size))
    return res


def _max_by_axis(the_list):
    # type: (List[List[int]]) -> List[int]
    maxes = the_list[0]
    for sublist in the_list[1:]:
        for index, item in enumerate(sublist):
            maxes[index] = max(maxes[index], item)
    return maxes


def nested_tensor_from_tensor_list(tensor_list: List[Tensor]):
    # if torchvision._is_tracing():
    #     # nested_tensor_from_tensor_list() does not export well to ONNX
    #     # call _onnx_nested_tensor_from_tensor_list() instead
    #     return _onnx_nested_tensor_from_tensor_list(tensor_list)
    max_size = _max_by_axis([list(img.shape) for img in tensor_list])
    batch_shape = [len(tensor_list)] + max_size
    dtype = tensor_list[0].dtype
    device = tensor_list[0].device
    tensor = torch.zeros(batch_shape, dtype=dtype, device=device)
    for i, img in enumerate(tensor_list):
        tensor[i, ..., : img.shape[-2], : img.shape[-1]].copy_(img)
    return tensor
