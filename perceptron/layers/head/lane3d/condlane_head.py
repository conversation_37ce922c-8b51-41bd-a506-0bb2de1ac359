import torch
import torch.nn as nn
import torch.nn.functional as F
from perceptron.layers.head.lane3d.condlane import compute_locations, cal_num_params
from perceptron.layers.head.lane3d.condlane import CondLane3D<PERSON>oss, CondLane3DPost


class RVSegmentationHead(nn.Module):
    def __init__(self, feat_upsample, feat_dim, category_num=2):
        super(RVSegmentationHead, self).__init__()
        self.feat_upsample = feat_upsample
        self.seg_head = nn.Conv2d(feat_dim, category_num, kernel_size=1, padding=0)
        self.upsample = nn.Upsample(scale_factor=feat_upsample, mode="bilinear")

    def forward(self, inputs):
        seg_mask = self.seg_head(inputs["im_rv_features"])
        seg_mask = self.upsample(seg_mask)
        return seg_mask


class DynamicMaskHead(nn.Module):
    def __init__(self, num_layers, channels, weight_nums, bias_nums, disable_coords, out_channels):
        super(DynamicMaskHead, self).__init__()
        self.num_layers = num_layers
        self.channels = channels
        self.disable_coords = disable_coords
        self.weight_nums = weight_nums
        self.bias_nums = bias_nums
        self.out_channels = out_channels

    def forward(self, x, mask_head_params, is_mask=True):
        N, _, H, W = x.size()
        if not self.disable_coords:
            locations = compute_locations(x.size(2), x.size(3), stride=1, device=x.device)
            locations = locations.unsqueeze(0).permute(0, 2, 1).contiguous().float().view(1, 2, H, W)
            locations[:, 0, :, :] /= H
            locations[:, 1, :, :] /= W
            locations = locations.repeat(N, 1, 1, 1)
            x = torch.cat([locations, x], dim=1)
        weights, biases = self.parse_dynamic_params(mask_head_params, mask=is_mask)
        mask_logits = self.mask_heads_forward(x, weights, biases)
        return mask_logits

    def parse_dynamic_params(self, params, mask=True):
        assert params.dim() == 3
        assert len(self.weight_nums) == len(self.bias_nums)
        assert params.size(2) == sum(self.weight_nums) + sum(self.bias_nums)

        batch_size, num_ins = params.shape[:2]
        num_layers = len(self.weight_nums)
        params_list = list(params.split(self.weight_nums + self.bias_nums, dim=2))

        assert num_layers == 1, "If num_layers > 1, you cannot use this accelerated form of dynamic conv!"

        weight_list = params_list[:num_layers]
        bias_list = params_list[num_layers:]
        if mask:
            bias_list[-1] = bias_list[-1] - 2.19
        for l in range(num_layers):
            if l < num_layers - 1:
                weight_list[l] = weight_list[l].reshape(batch_size, num_ins * self.channels, -1).contiguous()
                bias_list[l] = bias_list[l].reshape(batch_size, num_ins * self.channels, 1).contiguous()
            else:
                weight_list[l] = weight_list[l].reshape(batch_size, num_ins * self.out_channels, -1).contiguous()
                bias_list[l] = bias_list[l].reshape(batch_size, num_ins * self.out_channels, 1).contiguous()
        return weight_list, bias_list

    @staticmethod
    def mask_heads_forward(features, weights, biases):
        """
        :param features:
        :param weights:
        :param biases:
        :return:
        """
        assert features.dim() == 4
        N, C, H, W = features.size()
        n_layers = len(weights)
        x = features.view(N, C, H * W)
        for i, (w, b) in enumerate(zip(weights, biases)):
            x = w.bmm(x) + b
            if i < n_layers - 1:
                x = F.relu(x)
        x = x.view(N, -1, H, W)
        return x


class CTNetHead(nn.Module):
    def __init__(self, heads_conf, in_channels, mid_channels=256):
        super(CTNetHead, self).__init__()
        self.heads_conf = heads_conf
        for head_name in self.heads_conf:
            out_channels = self.heads_conf[head_name]
            if mid_channels > 0:
                fc = nn.Sequential(
                    nn.Linear(in_channels, mid_channels, bias=True),
                    nn.ReLU(inplace=False),
                    nn.Linear(mid_channels, out_channels, bias=True),
                )
            else:
                fc = nn.Linear(in_channels, out_channels, bias=True)
            self.__setattr__(head_name, fc)

    def forward(self, x):
        out = {}
        for head_name in self.heads_conf:
            out[head_name] = self.__getattr__(head_name)(x)
        return out


class CondLane3DHead(nn.Module):
    def __init__(
        self,
        in_channels,
        num_attributes,
        head_layers=1,
        disable_coords=False,
        branch_channels=64,
        loss_weights=None,
        target_shape=(200, 100),
        min_points=5,
        line_width=13,
        score_thresh=0.3,
        use_offset=True,
        map_range=(65, 5, 15, -15),
        map_resolution=(0.5, 0.15),
    ):
        super(CondLane3DHead, self).__init__()
        self.target_shape = target_shape  # 模型在这个尺寸上计算损失
        self.num_attributes = num_attributes

        # Output Head
        self.mask_weight_nums, self.mask_bias_nums = cal_num_params(
            head_layers, disable_coords, in_channels[0], branch_channels, out_channels=3
        )
        self.num_mask_params = sum(self.mask_weight_nums) + sum(self.mask_bias_nums)
        heads_conf = {"object": 2, "attrib": 2 * num_attributes, "ranges": 2, "params": self.num_mask_params}
        self.ctnet_head = CTNetHead(heads_conf, in_channels=in_channels[1], mid_channels=branch_channels)
        self.mask_head = DynamicMaskHead(
            head_layers,
            branch_channels,
            self.mask_weight_nums,
            self.mask_bias_nums,
            disable_coords=disable_coords,
            out_channels=3,
        )

        # Loss
        self.criterion = CondLane3DLoss(loss_weights, num_attributes, min_points, line_width, target_shape)

        # Post-Process
        self.postprocess = CondLane3DPost(
            num_attributes,
            min_points,
            line_width,
            target_shape,
            score_thresh,
            use_offset,
            map_range,
            map_resolution,
        )

    def forward(self, inputs):
        inputs["net_outputs"] = self._forward_head(inputs)
        inputs.update(self._forward_postprocessor(inputs))
        return inputs

    def _forward_head(self, inputs):
        bev_features = inputs["im_bev_features"][0]  # (B, C, H, W)
        height, width = bev_features.shape[-2:]
        if not self.training:
            inputs["instance_features"] = inputs["instance_features"][-1:]  # 测试仅使用最后一个decoder出来的特征
        outputs = {k: [] for k in ["masks", "offsets", "ranges", "attrib", "object"]}
        outputs["num_decoders"] = len(inputs["instance_features"])
        for ins_features in inputs["instance_features"]:  # (B, C', L, 1)
            ins_features = ins_features.flatten(2).transpose(1, 2)  # (B, L, C')
            batch_size, num_queries = ins_features.shape[:2]
            out_status = self.ctnet_head(ins_features)
            masks = self.mask_head(bev_features, out_status["params"])
            masks = masks.view(batch_size, -1, height, width)  # (N, L * 3, h, w)
            masks = F.interpolate(masks, size=self.target_shape, mode="bilinear", align_corners=True)
            masks, offsets = masks.split([num_queries, 2 * num_queries], dim=1)
            outputs["masks"].append(masks)
            outputs["offsets"].append(offsets)
            outputs["ranges"].append(out_status["ranges"].sigmoid())
            outputs["attrib"].append(out_status["attrib"].view(batch_size, num_queries, self.num_attributes, -1))
            outputs["object"].append(out_status["object"])
        return outputs

    def _forward_postprocessor(self, inputs):
        if self.training:
            loss_obj, loss_cls, loss_loc, loss_reg, loss_rng, loss_rv_seg = self.criterion(inputs)
            loss_dict = {
                "obj": loss_obj,
                "cls": loss_cls,
                "loc": loss_loc,
                "reg": loss_reg,
                "rng": loss_rng,
                "rv_seg": loss_rv_seg,
            }
            loss_dict.update({"losses": sum([v for v in loss_dict.values()])})
            return loss_dict
        else:
            lane_scores, lane_point, lane_attrib = self.postprocess(inputs)
            results = {"lane_scores": lane_scores, "pts_pred": lane_point, "att_pred": lane_attrib}
            return {"results": results}
