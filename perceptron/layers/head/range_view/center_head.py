from perceptron.layers.head.det3d import BaseGenProposals
from perceptron.layers.head.det3d import BaseAssigner
from perceptron.layers.head.det3d import CenterHead, CenterHeadIouAware
from perceptron.layers.losses.det3d import _transpose_and_gather_feat
import torch
from perceptron.utils.det3d_utils import box_utils
from perceptron.utils.torch_dist import reduce_mean
from perceptron.layers.head.range_view.target_assigner.rv_seg_assigner_ota_ignore import RVSegOtaAssignerIgnore


class CenterHead_RV(CenterHead):
    def __init__(
        self,
        dataset_name,
        tasks,
        target_assigner: BaseAssigner,
        proposal_layer: BaseGenProposals,
        input_channels,
        grid_size,
        point_cloud_range,
        code_weights,
        loc_weight,
        share_conv_channel,
        common_heads,
        upsample_for_pedestrian=False,
        predict_boxes_when_training=False,
        mode="3d",
        init_bias=-2.19,
    ):
        super().__init__(
            dataset_name,
            tasks,
            target_assigner,
            proposal_layer,
            input_channels,
            grid_size,
            point_cloud_range,
            code_weights,
            loc_weight,
            share_conv_channel,
            common_heads,
            upsample_for_pedestrian,
            predict_boxes_when_training,
            mode,
            init_bias,
        )

    def assign_targets(
        self,
        rv_coords,
        gt_boxes,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
        forward_ret_dict=None,
    ):
        """
        Args:
            rv_coords: (B, 3, H, W)
            gt_boxes: (B, M, 8)
        Returns:

        """
        if isinstance(self.target_assigner, (RVSegOtaAssignerIgnore,)):
            targets_dict = self.target_assigner.assign_targets(
                rv_coords, gt_boxes, incl_array, ncols, rv_col_offset, rv_mv_origin, rv_range, forward_ret_dict
            )
        else:
            targets_dict = self.target_assigner.assign_targets(
                rv_coords, gt_boxes, incl_array, ncols, rv_col_offset, rv_mv_origin, rv_range
            )
        return targets_dict

    def forward(
        self,
        rv_coords,
        spatial_features_2d,
        gt_boxes=None,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
    ):
        """
        rc_coords: 原始xyz坐标

        2022/06/20 updated:
        新增 incl_array, 适配二号车， assign过程按需传入incl_array。兼容老代码，如果传入
        None， 默认使用一号车incl_array.

        rv_range: 可视化使用
        """
        multi_head_features = []
        forward_ret_dict = {}
        spatial_features_2d = self.shared_conv(spatial_features_2d)

        if self.upsample_for_pedestrian:
            spatial_features_2d = self.upsample_conv(spatial_features_2d)

        for task_id, task in enumerate(self.tasks):
            multi_head_features.append(task(spatial_features_2d))

        forward_ret_dict["multi_head_features"] = multi_head_features

        if self.training:
            # import IPython; IPython.embed()
            targets_dict = self.assign_targets(
                rv_coords, gt_boxes, incl_array, ncols, rv_col_offset, rv_mv_origin, rv_range, forward_ret_dict
            )
            forward_ret_dict.update(targets_dict)
            return forward_ret_dict

        # set to True when exporting trt
        if False:
            keys = ["reg", "height", "dim", "rot", "hm"]
            results = [[] for _ in keys]

            for task in self.tasks:
                task_res = task(spatial_features_2d)
                for key, result in zip(keys, results):
                    result.append(task_res[key])

            results = [torch.stack(x, dim=0) for x in results]
            return results

        if not self.training or self.predict_boxes_when_training:
            # 这里我们额外传入rv_mv_origin
            data_dict = self.proposal_layer.generate_predicted_boxes(
                forward_ret_dict,
                {
                    "rv_coords": rv_coords[:, :3, :, :],
                    "rv_mv_origin": rv_mv_origin,
                },
            )

            return data_dict


class CenterHeadIouAware_RV(CenterHeadIouAware):
    def __init__(
        self,
        dataset_name,
        tasks,
        target_assigner,
        proposal_layer,
        out_size_factor,
        input_channels,
        grid_size,
        point_cloud_range,
        code_weights,
        loc_weight,
        iou_weight,
        share_conv_channel,
        common_heads,
        upsample_for_pedestrian=False,
        predict_boxes_when_training=False,
        mode="3d",
        init_bias=-2.19,
    ):
        super().__init__(
            dataset_name,
            tasks,
            target_assigner,
            proposal_layer,
            out_size_factor,
            input_channels,
            grid_size,
            point_cloud_range,
            code_weights,
            loc_weight,
            iou_weight,
            share_conv_channel,
            common_heads,
            upsample_for_pedestrian,
            predict_boxes_when_training,
            mode,
            init_bias,
        )

    def assign_targets(
        self,
        rv_coords,
        gt_boxes,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
        forward_ret_dict=None,
    ):
        """
        Args:
            rv_coords: (B, 3, H, W)
            gt_boxes: (B, M, 8)
        Returns:

        """
        if isinstance(self.target_assigner, (RVSegOtaAssignerIgnore,)):
            targets_dict = self.target_assigner.assign_targets(
                rv_coords, gt_boxes, incl_array, ncols, rv_col_offset, rv_mv_origin, rv_range, forward_ret_dict
            )
        else:
            targets_dict = self.target_assigner.assign_targets(
                rv_coords, gt_boxes, incl_array, ncols, rv_col_offset, rv_mv_origin, rv_range
            )
        return targets_dict

    def forward(
        self,
        rv_coords,
        spatial_features_2d,
        gt_boxes=None,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
    ):
        """
        rc_coords: 原始xyz坐标

        2022/06/20 updated:
        新增 incl_array, 适配二号车， assign过程按需传入incl_array。兼容老代码，如果传入
        None， 默认使用一号车incl_array.

        rv_range: 可视化使用
        """
        multi_head_features = []
        forward_ret_dict = {}
        spatial_features_2d = self.shared_conv(spatial_features_2d)

        if self.upsample_for_pedestrian:
            spatial_features_2d = self.upsample_conv(spatial_features_2d)

        for task_id, task in enumerate(self.tasks):
            multi_head_features.append(task(spatial_features_2d))

        forward_ret_dict["multi_head_features"] = multi_head_features

        if self.training:
            # import IPython; IPython.embed()
            targets_dict = self.assign_targets(
                rv_coords, gt_boxes, incl_array, ncols, rv_col_offset, rv_mv_origin, rv_range, forward_ret_dict
            )
            forward_ret_dict.update(targets_dict)
            return forward_ret_dict

        if not self.training or self.predict_boxes_when_training:
            # 这里我们额外传入rv_mv_origin
            data_dict = self.proposal_layer.generate_predicted_boxes(
                forward_ret_dict,
                {
                    "rv_coords": rv_coords[:, :3, :, :],
                    "rv_mv_origin": rv_mv_origin,
                },
            )

            return data_dict

    def _get_iou_loss(self, batch_preds, batch_targets, proposal_inds, pos_mask, stride, voxel_size):
        pred = _transpose_and_gather_feat(batch_preds, proposal_inds)  # bs, num_proposal, 8+1

        target_x_offset = batch_targets[..., 0:1].reshape(-1, 1)  # (bs*num_proposal, 1)
        target_y_offset = batch_targets[..., 1:2].reshape(-1, 1)

        target_whl = torch.exp(batch_targets[..., 3:6]).reshape(-1, 3)
        target_whl = torch.clamp(target_whl, min=0.001, max=30)
        targe_rot = torch.atan2(batch_targets[..., 6], batch_targets[..., 7]).reshape(-1, 1)

        target_z_offset = batch_targets[..., 2].reshape(-1, 1)
        target_bbox3d = torch.cat([target_x_offset, target_y_offset, target_z_offset, target_whl, targe_rot], dim=-1)

        pred_x_offset = pred[..., 0:1].reshape(-1, 1)
        pred_y_offset = pred[..., 1:2].reshape(-1, 1)

        pred_whl = torch.exp(pred[..., 3:6]).reshape(-1, 3)
        pred_whl = torch.clamp(pred_whl, min=0.001, max=30)
        pred_rot = torch.atan2(pred[..., 6], pred[..., 7]).reshape(-1, 1)

        pred_z_offset = pred[..., 2].reshape(-1, 1)
        pre_bbox3d = torch.cat([pred_x_offset, pred_y_offset, pred_z_offset, pred_whl, pred_rot], dim=-1)

        # iou loss
        iou = self._get_3d_iou(
            target_x_offset,
            target_y_offset,
            target_whl,
            target_z_offset,
            pred_x_offset,
            pred_y_offset,
            pred_whl,
            pred_z_offset,
        )

        iou_pos = torch.clamp(iou[pos_mask.flatten()], 0, 1)
        iou_loss = 1 - iou_pos
        # iou_loss = iou_loss.sum() / max(1, len(iou_pos))
        num_pos = pos_mask.float().sum()
        num_pos = reduce_mean(num_pos)
        iou_loss = iou_loss.sum() / max(1, num_pos)

        # iou aware loss
        iou = box_utils.boxes3d_nearest_bev_iou(target_bbox3d, pre_bbox3d.clone().detach())
        tril = range(len(iou))
        tar_iou_pred = 2 * (iou[tril, tril].reshape(*pos_mask.shape, 1) - 0.5)
        iou_aware_loss = self.crit_iou_aware(batch_preds[:, 8:9, :, :], pos_mask, proposal_inds, tar_iou_pred)

        return iou_loss, iou_aware_loss.sum()
