import torch
from perceptron.layers.head.det3d import IouAwareGenProposals
from perceptron.layers.head.range_view.generate_proposals.wnms import (
    nms,
    weighted_nms,
    multiclass_wnms,
    multiclass_nms,
)


class IouAwareGenProposals_RV(IouAwareGenProposals):
    def __init__(
        self,
        dataset_name,
        class_names,
        post_center_limit_range,
        score_threshold,
        pc_range,
        out_size_factor,
        voxel_size,
        no_log,
        nms_iou_threshold_train,
        nms_pre_max_size_train,
        nms_post_max_size_train,
        nms_iou_threshold_test,
        nms_pre_max_size_test,
        nms_post_max_size_test,
        iou_aware_list,
        nms_type="nms",
        nms_iou_pbsi_threshold_test=0.45,
        rv_range_mask=True,
        iou_rescore=False,
        use_nmsscore=True,
        post_process_nms_post_maxsize=500,
    ):
        """IouAwareGenProposals_RV

        Args:
            nms_type: one of ["nms", "multiclass_nms", "wnms", "multiclass_wnms"]
            nms_iou_pbsi_threshold_test: used for "wnms" and "multiclass_wnms"
            rv_range_mask: 过滤invalid_anchor对应的输出
            iou_rescore: 是否使用nms_score选取topk
            use_nmsscore: 是否将score使用nms_score进行替换
            post_process_nms_post_maxsize: 每个head输出的最多的框数量, 用于`multiclass_nms`与`multiclass_wnms`. 单head设置为500, 5heads设置为100.
        """
        super().__init__(
            dataset_name,
            class_names,
            post_center_limit_range,
            score_threshold,
            pc_range,
            out_size_factor,
            voxel_size,
            no_log,
            nms_iou_threshold_train,
            nms_pre_max_size_train,
            nms_post_max_size_train,
            nms_iou_threshold_test,
            nms_pre_max_size_test,
            nms_post_max_size_test,
            iou_aware_list,
        )
        self.nms_iou_threshold_pbsi = nms_iou_pbsi_threshold_test
        self.nms_type = nms_type
        assert nms_type in [
            "nms",
            "multiclass_nms",
            "wnms",
            "multiclass_wnms",
        ]
        self.rv_range_mask = rv_range_mask
        self.iou_rescore = iou_rescore
        self.use_nmsscore = use_nmsscore
        self.post_process_nms_post_maxsize = post_process_nms_post_maxsize

    @torch.no_grad()
    def proposal_layer(
        self,
        rv_coords,
        heat,
        rots,
        rotc,
        hei,
        dim,
        vel,
        reg=None,
        raw_rot=False,
        task_id=-1,
        iouhm=None,
        rv_mv_origin=[0, 0, 0],
    ):

        batch, cat, _, _ = heat.size()
        K = self.nms_pre_max_size_use  # topK selected

        # mask invalid pixels by rv_coords on heat
        if self.rv_range_mask:
            invalid_mask = (rv_coords == -1).all(dim=1, keepdim=True)
            heat[invalid_mask.expand(-1, cat, -1, -1)] = 0.0

        if not self.iou_rescore:
            scores, inds, clses, ys, xs = self._topk(heat, K=K)
            iouscore = self._transpose_and_gather_feat(iouhm, inds).view(batch, K)
            iouscore = torch.clamp(iouscore / 2 + 0.5, 1e-3, 1)
            nms_scores = (scores ** (1 - self.iou_aware_list[task_id])).mul(iouscore ** self.iou_aware_list[task_id])
            if self.use_nmsscore:
                scores = nms_scores
        else:
            iouscore = torch.clamp(iouhm / 2 + 0.5, 0.0, 1.0)
            heat = (heat ** (1 - self.iou_aware_list[task_id])).mul(iouscore ** self.iou_aware_list[task_id])
            scores, inds, clses, ys, xs = self._topk(heat, K=K)
            nms_scores = scores

        assert reg is not None
        reg = self._transpose_and_gather_feat(reg, inds)
        reg = reg.view(batch, K, 2)

        rv_coords = self._transpose_and_gather_feat(rv_coords, inds)
        rv_coords = rv_coords.view(batch, K, 3)  # B, K, 3 (xyz)

        xs = rv_coords[:, :, 0:1]  # B, K, 1
        ys = rv_coords[:, :, 1:2]  # B, K, 1
        zs = rv_coords[:, :, 2:3]  # B, K, 1

        assert raw_rot is False
        rots = self._transpose_and_gather_feat(rots, inds)
        rots = rots.view(batch, K, 1)
        rotc = self._transpose_and_gather_feat(rotc, inds)
        rotc = rotc.view(batch, K, 1)
        rot = torch.atan2(rots, rotc)
        # height in the bev
        hei = self._transpose_and_gather_feat(hei, inds)
        hei = hei.view(batch, K, 1)
        # dim of the box
        dim = self._transpose_and_gather_feat(dim, inds)
        dim = dim.view(batch, K, 3)
        # class label
        clses = clses.view(batch, K).float()
        scores = scores.view(batch, K)

        # center location for rv image.
        xs = xs.view(batch, K, 1) + reg[:, :, 0:1]
        ys = ys.view(batch, K, 1) + reg[:, :, 1:2]
        zs = zs.view(batch, K, 1) + hei

        # 二号车平移补偿（1号车应为全0）
        xs = xs + rv_mv_origin[0]
        ys = ys + rv_mv_origin[1]
        zs = zs + rv_mv_origin[2]

        if self.dataset_name == "nuscenes":
            vel = self._transpose_and_gather_feat(vel, inds)
            vel = vel.view(batch, K, 2)
            # vel after rot
            final_box_preds = torch.cat([xs, ys, hei, dim, rot, vel], dim=2)
        else:
            final_box_preds = torch.cat([xs, ys, zs, dim, rot], dim=2)

        final_scores = scores
        final_preds = clses

        # restrict center range
        post_center_range = self.post_center_limit_range
        assert post_center_range is not None
        post_center_range = torch.tensor(post_center_range).to(final_box_preds.device)
        mask = (final_box_preds[..., :3] >= post_center_range[:3]).all(2)
        mask &= (final_box_preds[..., :3] <= post_center_range[3:]).all(2)
        # use score threshold
        assert self.score_threshold is not None
        thresh_mask = final_scores > self.score_threshold
        mask &= thresh_mask

        predictions_dicts = []
        for i in range(batch):
            cmask = mask[i, :]
            boxes3d = final_box_preds[i, cmask]
            scores = final_scores[i, cmask]
            labels = final_preds[i, cmask]
            nms_score = nms_scores[i, cmask]
            boxes3d, labels, scores = self.do_nms(boxes3d, labels, scores, nms_score)

            predictions_dict = {
                "boxes": boxes3d,
                "scores": scores,
                "labels": labels.long(),
            }
            predictions_dicts.append(predictions_dict)
        return predictions_dicts

    @torch.no_grad()
    def generate_predicted_boxes(self, forward_ret_dict, data_dict):
        """
        Generate box predictions with decode, topk and circular_nms
        For single-stage-detector, another post-processing (nms) is needed
        For two-stage-detector, no need for proposal layer in roi_head
        Returns:
        """
        pred_dicts = forward_ret_dict["multi_head_features"]

        if self.training:
            self.nms_iou_threshold_use = self.nms_iou_threshold_train
            self.nms_pre_max_size_use = self.nms_pre_max_size_train
            self.nms_post_max_size_use = self.nms_post_max_size_train
        else:
            self.nms_iou_threshold_use = self.nms_iou_threshold_test
            self.nms_pre_max_size_use = self.nms_pre_max_size_test
            self.nms_post_max_size_use = self.nms_post_max_size_test

        task_box_preds = {}
        task_score_preds = {}
        task_label_preds = {}
        for task_id, pred_dict in enumerate(pred_dicts):
            batch_size = pred_dict["hm"].shape[0]

            # batch_hm = pred_dict['hm'].sigmoid_() inplace may cause errors
            batch_hm = pred_dict["hm"].sigmoid()
            batch_reg = pred_dict["reg"]
            batch_hei = pred_dict["height"]
            batch_iousocre = pred_dict["iou"]

            if not self.no_log:
                batch_dim = torch.exp(pred_dict["dim"])
                # add clamp for good init, otherwise we will get inf with exp
                batch_dim = torch.clamp(batch_dim, min=0.001, max=30)
            else:
                batch_dim = pred_dict["dim"]
            batch_rots = pred_dict["rot"][:, 0].unsqueeze(1)
            batch_rotc = pred_dict["rot"][:, 1].unsqueeze(1)

            if self.dataset_name == "nuscenes":
                batch_vel = pred_dict["vel"]
            else:
                batch_vel = None

            # decode
            boxes = self.proposal_layer(
                data_dict["rv_coords"],
                batch_hm,
                batch_rots,
                batch_rotc,
                batch_hei,
                batch_dim,
                batch_vel,
                reg=batch_reg,
                task_id=task_id,
                iouhm=batch_iousocre,
                rv_mv_origin=data_dict["rv_mv_origin"],
            )
            task_box_preds[task_id] = [box["boxes"] for box in boxes]
            task_score_preds[task_id] = [box["scores"] for box in boxes]
            task_label_preds[task_id] = [box["labels"] for box in boxes]  # labels are local here

        pred_dicts = []
        batch_size = len(task_box_preds[0])

        for batch_idx in range(batch_size):
            offset = 1  # class label start from 1
            final_boxes, final_scores, final_labels = [], [], []
            for task_id, class_name in enumerate(self.class_names):
                final_boxes.append(task_box_preds[task_id][batch_idx])
                final_scores.append(task_score_preds[task_id][batch_idx])
                # convert to global labels
                final_global_label = task_label_preds[task_id][batch_idx] + offset
                offset += len(class_name)
                final_labels.append(final_global_label)

            final_boxes = torch.cat(final_boxes)
            final_scores = torch.cat(final_scores)
            final_labels = torch.cat(final_labels)

            record_dict = {
                "pred_boxes": final_boxes,
                "pred_scores": final_scores,
                "pred_labels": final_labels,
            }
            pred_dicts.append(record_dict)

        data_dict["pred_dicts"] = pred_dicts

        data_dict["has_class_labels"] = True  # Force to be true
        data_dict.pop("batch_index", None)
        return data_dict

    def do_nms(self, boxes3d, labels, scores, nms_score):
        if self.nms_type == "nms":
            boxes3d, labels, scores = nms(
                boxes3d,
                labels,
                scores,
                nms_score,
                thresh=self.nms_iou_threshold_use,
                pre_maxsize=self.nms_pre_max_size_use,
                post_max_size=self.nms_post_max_size_use,
            )
        elif self.nms_type == "multiclass_nms":
            boxes3d, labels, scores = multiclass_nms(
                boxes3d,
                labels,
                scores,
                nms_score,
                thresh=self.nms_iou_threshold_use,
                pre_maxsize=self.nms_pre_max_size_use,
                post_max_size=self.nms_post_max_size_use,
                post_process_nms_post_maxsize=self.post_process_nms_post_maxsize,
            )
        else:
            nms_param = [
                self.nms_iou_threshold_use,
                self.nms_pre_max_size_use,
                self.nms_post_max_size_use,
                self.nms_iou_threshold_pbsi,
            ]
            if self.nms_type == "wnms":
                boxes3d, labels, scores = weighted_nms(boxes3d, labels, scores, nms_score, *nms_param)
            elif self.nms_type == "multiclass_wnms":
                boxes3d, labels, scores = multiclass_wnms(
                    boxes3d,
                    labels,
                    scores,
                    nms_score,
                    *nms_param,
                    post_process_nms_post_maxsize=self.post_process_nms_post_maxsize,
                )

        return boxes3d, labels, scores


class IouAwareGenProposalsClsReg_RV(IouAwareGenProposals_RV):
    def __init__(
        self,
        dataset_name,
        class_names,
        post_center_limit_range,
        score_threshold,
        pc_range,
        out_size_factor,
        voxel_size,
        no_log,
        nms_iou_threshold_train,
        nms_pre_max_size_train,
        nms_post_max_size_train,
        nms_iou_threshold_test,
        nms_pre_max_size_test,
        nms_post_max_size_test,
        iou_aware_list,
        nms_type="nms",
        nms_iou_pbsi_threshold_test=0.45,
        rv_range_mask=True,
        use_nmsscore=True,
        post_process_nms_post_maxsize=500,
    ):
        super().__init__(
            dataset_name,
            class_names,
            post_center_limit_range,
            score_threshold,
            pc_range,
            out_size_factor,
            voxel_size,
            no_log,
            nms_iou_threshold_train,
            nms_pre_max_size_train,
            nms_post_max_size_train,
            nms_iou_threshold_test,
            nms_pre_max_size_test,
            nms_post_max_size_test,
            iou_aware_list,
            nms_type,
            nms_iou_pbsi_threshold_test,
            rv_range_mask,
            use_nmsscore=use_nmsscore,
            post_process_nms_post_maxsize=post_process_nms_post_maxsize,
        )

    @torch.no_grad()
    def proposal_layer(
        self,
        rv_coords,
        heat,
        rots,
        rotc,
        hei,
        dim,
        vel,
        reg=None,
        raw_rot=False,
        task_id=-1,
        iouhm=None,
        rv_mv_origin=[0, 0, 0],
    ):

        batch, cat, H, W = heat.size()
        K = self.nms_pre_max_size_use  # topK selected

        # mask invalid pixels by rv_coords on heat
        if self.rv_range_mask:
            invalid_mask = (rv_coords == -1).all(dim=1, keepdim=True)
            heat[invalid_mask.expand(-1, cat, -1, -1)] = 0.0

        scores, inds, clses, ys, xs = self._topk(heat, K=K)
        inds_old = inds
        inds = inds_old + clses * H * W

        iouscore = self._transpose_and_gather_feat(iouhm, inds).view(batch, K)
        iouscore = torch.clamp(iouscore / 2 + 0.5, 1e-3, 1)
        nms_scores = (scores ** (1 - self.iou_aware_list[task_id])).mul(iouscore ** self.iou_aware_list[task_id])
        if self.use_nmsscore:
            scores = nms_scores

        assert reg is not None
        reg = self._transpose_and_gather_feat(reg, inds)
        reg = reg.view(batch, K, 2)

        rv_coords = self._transpose_and_gather_feat(rv_coords, inds_old)
        rv_coords = rv_coords.view(batch, K, 3)  # B, K, 3 (xyz)

        xs = rv_coords[:, :, 0:1]  # B, K, 1
        ys = rv_coords[:, :, 1:2]  # B, K, 1
        zs = rv_coords[:, :, 2:3]  # B, K, 1

        assert raw_rot is False
        rots = self._transpose_and_gather_feat(rots, inds)
        rots = rots.view(batch, K, 1)
        rotc = self._transpose_and_gather_feat(rotc, inds)
        rotc = rotc.view(batch, K, 1)
        rot = torch.atan2(rots, rotc)
        # height in the bev
        hei = self._transpose_and_gather_feat(hei, inds)
        hei = hei.view(batch, K, 1)
        # dim of the box
        dim = self._transpose_and_gather_feat(dim, inds)
        dim = dim.view(batch, K, 3)
        # class label
        clses = clses.view(batch, K).float()
        scores = scores.view(batch, K)

        # center location for rv image.
        xs = xs.view(batch, K, 1) + reg[:, :, 0:1]
        ys = ys.view(batch, K, 1) + reg[:, :, 1:2]
        zs = zs.view(batch, K, 1) + hei

        # 二号车平移补偿（1号车应为全0）
        xs = xs + rv_mv_origin[0]
        ys = ys + rv_mv_origin[1]
        zs = zs + rv_mv_origin[2]

        if self.dataset_name == "nuscenes":
            vel = self._transpose_and_gather_feat(vel, inds)
            vel = vel.view(batch, K, 2)
            # vel after rot
            final_box_preds = torch.cat([xs, ys, hei, dim, rot, vel], dim=2)
        else:
            final_box_preds = torch.cat([xs, ys, zs, dim, rot], dim=2)

        final_scores = scores
        final_preds = clses

        # restrict center range
        post_center_range = self.post_center_limit_range
        assert post_center_range is not None
        post_center_range = torch.tensor(post_center_range).to(final_box_preds.device)
        mask = (final_box_preds[..., :3] >= post_center_range[:3]).all(2)
        mask &= (final_box_preds[..., :3] <= post_center_range[3:]).all(2)
        # use score threshold
        assert self.score_threshold is not None
        thresh_mask = final_scores > self.score_threshold
        mask &= thresh_mask

        predictions_dicts = []
        for i in range(batch):
            cmask = mask[i, :]
            boxes3d = final_box_preds[i, cmask]
            scores = final_scores[i, cmask]
            labels = final_preds[i, cmask]
            nms_score = nms_scores[i, cmask]
            boxes3d, labels, scores = self.do_nms(boxes3d, labels, scores, nms_score)

            predictions_dict = {
                "boxes": boxes3d,
                "scores": scores,
                "labels": labels.long(),
            }
            predictions_dicts.append(predictions_dict)
        return predictions_dicts

    @torch.no_grad()
    def generate_predicted_boxes(self, forward_ret_dict, data_dict):
        """
        Generate box predictions with decode, topk and circular_nms
        For single-stage-detector, another post-processing (nms) is needed
        For two-stage-detector, no need for proposal layer in roi_head
        Returns:
        """
        pred_dicts = forward_ret_dict["multi_head_features"]

        if self.training:
            self.nms_iou_threshold_use = self.nms_iou_threshold_train
            self.nms_pre_max_size_use = self.nms_pre_max_size_train
            self.nms_post_max_size_use = self.nms_post_max_size_train
        else:
            self.nms_iou_threshold_use = self.nms_iou_threshold_test
            self.nms_pre_max_size_use = self.nms_pre_max_size_test
            self.nms_post_max_size_use = self.nms_post_max_size_test

        task_box_preds = {}
        task_score_preds = {}
        task_label_preds = {}
        for task_id, pred_dict in enumerate(pred_dicts):
            batch_size = pred_dict["hm"].shape[0]

            # batch_hm = pred_dict['hm'].sigmoid_() inplace may cause errors
            batch_hm = pred_dict["hm"].sigmoid()
            B, _, H, W = batch_hm.shape
            num_class = len(self.class_names[task_id])
            batch_reg = pred_dict["reg"].reshape(B, -1, num_class * H, W)
            batch_hei = pred_dict["height"].reshape(B, -1, num_class * H, W)
            batch_iousocre = pred_dict["iou"].reshape(B, -1, num_class * H, W)

            if not self.no_log:
                batch_dim = torch.exp(pred_dict["dim"].reshape(B, -1, num_class * H, W))
                # add clamp for good init, otherwise we will get inf with exp
                batch_dim = torch.clamp(batch_dim, min=0.001, max=30)
            else:
                batch_dim = pred_dict["dim"].reshape(B, -1, num_class * H, W)
            rot = pred_dict["rot"].reshape(B, -1, num_class * H, W)
            batch_rots = rot[:, 0:1]
            batch_rotc = rot[:, 1:2]

            if self.dataset_name == "nuscenes":
                batch_vel = pred_dict["vel"]
            else:
                batch_vel = None

            # decode
            boxes = self.proposal_layer(
                data_dict["rv_coords"],
                batch_hm,
                batch_rots,
                batch_rotc,
                batch_hei,
                batch_dim,
                batch_vel,
                reg=batch_reg,
                task_id=task_id,
                iouhm=batch_iousocre,
                rv_mv_origin=data_dict["rv_mv_origin"],
            )
            task_box_preds[task_id] = [box["boxes"] for box in boxes]
            task_score_preds[task_id] = [box["scores"] for box in boxes]
            task_label_preds[task_id] = [box["labels"] for box in boxes]  # labels are local here

        pred_dicts = []
        batch_size = len(task_box_preds[0])

        for batch_idx in range(batch_size):
            offset = 1  # class label start from 1
            final_boxes, final_scores, final_labels = [], [], []
            for task_id, class_name in enumerate(self.class_names):
                final_boxes.append(task_box_preds[task_id][batch_idx])
                final_scores.append(task_score_preds[task_id][batch_idx])
                # convert to global labels
                final_global_label = task_label_preds[task_id][batch_idx] + offset
                offset += len(class_name)
                final_labels.append(final_global_label)

            final_boxes = torch.cat(final_boxes)
            final_scores = torch.cat(final_scores)
            final_labels = torch.cat(final_labels)

            record_dict = {
                "pred_boxes": final_boxes,
                "pred_scores": final_scores,
                "pred_labels": final_labels,
            }
            pred_dicts.append(record_dict)

        data_dict["pred_dicts"] = pred_dicts

        data_dict["has_class_labels"] = True  # Force to be true
        data_dict.pop("batch_index", None)
        return data_dict
