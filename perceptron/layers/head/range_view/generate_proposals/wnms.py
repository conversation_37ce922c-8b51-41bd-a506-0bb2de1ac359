from functools import partial
import torch
import numpy as np
from perceptron_ops.iou3d_nms.iou3d_nms_utils import boxes_iou3d_gpu
from perceptron_ops.iou3d_nms import iou3d_nms_cuda


def weighted_nms(boxes3d, labels, scores, nms_scores, filter_thresh, pre_maxsize, post_max_size, pbsi_thresh):
    """Weight NMS

    Args:
        boxes3d: (N, 7) torch.tensor, x, y, z, lx, ly, lz, yaw
        labels: (N) torch.tensor
        scores: (N) torch.tensor, class score
        nms_scores: (N) torch.tensor, nms_scores
        filter_thresh: float, boxes whose iou <= filter_thresh will be kept, same as thresh used in nms.
        pre_max_size: pre max size for wnms
        post_max_size: post max size for wnms
        pbsi_thresh: boxes whose iou >= filter_thresh will be used in wbf
    """
    assert boxes3d.ndim == 2 and boxes3d.shape[1] == 7, f"{boxes3d.shape}"
    assert labels.ndim == 1, f"{labels.shape}"
    assert scores.ndim == 1 and nms_scores.ndim == 1, f"{labels.shape}, {nms_scores.shape}"
    assert scores.shape[0] == labels.shape[0] == boxes3d.shape[0] == nms_scores.shape[0], "shape[0] should be same."

    # 首先按照score从大到小排序
    order = torch.argsort(nms_scores, descending=True)[:pre_maxsize]
    keep = []
    new_boxes = []

    while order.size(0) > 0:
        i = order[0].item()

        iou_matrix = boxes_iou3d_gpu(boxes3d[i : i + 1], boxes3d[order])[0]  # 1xN -> N

        # iou大于指定阈值的框，进行wbf
        tmp_inds_pbsi = torch.where(iou_matrix >= pbsi_thresh)[0]  # possibly be same instance
        inds_pbsi = order[tmp_inds_pbsi]  # FIXME: include current box

        # if len(inds_pbsi) == 0:  # 找不到可合并框，跳过
        #     order = order[1:]
        #     continue

        keep.append(i)  # 只有当能够找到可合并框时，才保留

        tmp = torch.sum(scores[inds_pbsi])
        # xyzwhl, (1, 6)
        xyzwhl_avg = torch.sum(scores[inds_pbsi, None] * boxes3d[inds_pbsi, 0:6], dim=0, keepdim=True) / tmp
        yaw_tmp = boxes3d[inds_pbsi][..., 6:7]
        yaw_sin = torch.sum(torch.sin(yaw_tmp) * scores[inds_pbsi, None], dim=0, keepdim=True)
        yaw_cos = torch.sum(torch.cos(yaw_tmp) * scores[inds_pbsi, None], dim=0, keepdim=True)
        yaw_avg = (torch.atan2(yaw_sin, yaw_cos) + np.pi * 2) % (2 * np.pi)
        # (1, 7)
        bbox_avg = torch.cat([xyzwhl_avg, yaw_avg], dim=-1)

        new_boxes.append(bbox_avg)

        # 计算与所有其他框的iou, 保留iou小于指定阈值的框。
        inds = torch.where(iou_matrix <= filter_thresh)[0]  # M
        order = order[inds]

    keep = keep[:post_max_size]
    if len(new_boxes) > 0:
        boxes3d = torch.cat(new_boxes[:post_max_size], dim=0)  # Nx7
    else:
        boxes3d = boxes3d[[]]
    labels = labels[keep]
    scores = scores[keep]

    return boxes3d, labels, scores


def _nms_gpu_3d(boxes, scores, thresh, pre_maxsize=None, post_max_size=None):
    """NMS

    Args
        boxes: (N, 7) [x, y, z, dx, dy, dz, heading]
        scores: (N)
        thresh:
    Return:
        selected: (M) torch.long
    """
    assert boxes.shape[1] == 7
    order = scores.sort(0, descending=True)[1]
    if pre_maxsize is not None:
        order = order[:pre_maxsize]

    boxes = boxes[order].contiguous()
    keep = torch.LongTensor(boxes.size(0))
    num_out = iou3d_nms_cuda.nms_gpu(boxes, keep, thresh)
    selected = order[keep[:num_out].cuda()].contiguous()

    if post_max_size is not None:
        selected = selected[:post_max_size]

    return selected


def nms(boxes3d, labels, scores, nms_scores, thresh, pre_maxsize=None, post_max_size=None):
    top_scores = nms_scores
    if top_scores.shape[0] != 0:
        selected = _nms_gpu_3d(
            boxes3d[:, :7],
            top_scores,
            thresh=thresh,
            pre_maxsize=pre_maxsize,
            post_max_size=post_max_size,
        )
    else:
        selected = []
    boxes3d = boxes3d[selected]
    labels = labels[selected]
    scores = scores[selected]
    return boxes3d, labels, scores


def multiclass_warpper(nms_func_partial, boxes3d, labels, scores, nms_scores, class_num, post_process_nms_post_maxsize):
    """Multiclass warpper function.

    Args:
        nms_func_partial: partial function of `nms` or `weighted_nms`
        boxes3d, labels, scores, nms_scores: same as `multiclass_nms`.
    Returns:
        same as `multiclass_nms`
    """
    if len(labels) == 0:
        return boxes3d, labels, scores

    assert boxes3d.ndim == 2 and boxes3d.shape[1] == 7, f"{boxes3d.shape}"
    assert labels.ndim == 1, f"{labels.shape}"
    assert scores.ndim == 1 and nms_scores.ndim == 1, f"{labels.shape}, {nms_scores.shape}"
    assert scores.shape[0] == labels.shape[0] == boxes3d.shape[0] == nms_scores.shape[0], "shape[0] should be same."

    if class_num is None:
        class_num = labels.max().int().item() + 1

    boxes3d_all = boxes3d
    labels_all = labels
    scores_all = scores
    nms_score_all = nms_scores

    boxes3d_out = []
    labels_out = []
    scores_out = []
    for i in range(class_num):
        # use_iou_3d_nms
        class_mask = labels_all == i
        boxes3d = boxes3d_all[class_mask]
        if len(boxes3d) == 0:
            continue
        labels = labels_all[class_mask]
        scores = scores_all[class_mask]

        nms_scores = nms_score_all[[class_mask]]
        boxes3d, labels, scores = nms_func_partial(boxes3d, labels, scores, nms_scores)
        boxes3d_out.append(boxes3d)
        labels_out.append(labels)
        scores_out.append(scores)

    if len(boxes3d_out):
        scores = torch.cat(scores_out, dim=0)
        boxes3d = torch.cat(boxes3d_out, dim=0)
        labels = torch.cat(labels_out, dim=0)
        # post_max
        if len(scores) > post_process_nms_post_maxsize:
            order = scores.sort(0, descending=True)[1]
            order = order[:post_process_nms_post_maxsize]
            scores = scores[order]
            boxes3d = boxes3d[order]
            labels = labels[order]
    else:
        boxes3d = boxes3d_all[:0]
        labels = labels_all[:0]
        scores = scores[:0]

    return boxes3d, labels, scores


def multiclass_nms(
    boxes3d,
    labels,
    scores,
    nms_scores,
    thresh,
    pre_maxsize,
    post_max_size,
    class_num=None,
    post_process_nms_post_maxsize=500,
):
    """Multiclass weight NMS.

    Args:
        boxes3d: (N, 7) torch.tensor, x, y, z, lx, ly, lz, yaw
        labels: (N) torch.tensor
        scores: (N) torch.tensor, class score
        nms_scores: (N) torch.tensor, nms_scores
        filter_thresh, pre_maxsize, post_max_size, pbsi_thresh: same as `weighted_nms`
        class_num: int, class num in labels, default is None
        post_process_nms_post_maxsize: same as multiclass_nms
    Returns:
        boxes3d, labels, scores
    """
    nms_patial_func = partial(
        nms,
        thresh=thresh,
        pre_maxsize=pre_maxsize,
        post_max_size=post_max_size,
    )
    return multiclass_warpper(
        nms_patial_func,
        boxes3d,
        labels,
        scores,
        nms_scores,
        class_num=class_num,
        post_process_nms_post_maxsize=post_process_nms_post_maxsize,
    )


def multiclass_wnms(
    boxes3d,
    labels,
    scores,
    nms_scores,
    filter_thresh,
    pre_maxsize,
    post_max_size,
    pbsi_thresh,
    class_num=None,
    post_process_nms_post_maxsize=500,
):
    """Multiclass weight NMS.

    Args:
        boxes3d: (N, 7) torch.tensor, x, y, z, lx, ly, lz, yaw
        labels: (N) torch.tensor
        scores: (N) torch.tensor, class score
        nms_scores: (N) torch.tensor, nms_scores
        filter_thresh, pre_maxsize, post_max_size, pbsi_thresh: same as `weighted_nms`
        class_num: int, class num in labels, default is None
        post_process_nms_post_maxsize: same as multiclass_nms
    """
    wnms_patial_func = partial(
        weighted_nms,
        filter_thresh=filter_thresh,
        pre_maxsize=pre_maxsize,
        post_max_size=post_max_size,
        pbsi_thresh=pbsi_thresh,
    )
    return multiclass_warpper(
        wnms_patial_func,
        boxes3d,
        labels,
        scores,
        nms_scores,
        class_num=class_num,
        post_process_nms_post_maxsize=post_process_nms_post_maxsize,
    )
