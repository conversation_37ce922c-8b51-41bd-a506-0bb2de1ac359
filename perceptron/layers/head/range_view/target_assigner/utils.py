# Update at 2022/05/20
# 默认使用ROS/真实lidar坐标系进行rv image映射

import torch
import numpy as np


def generate_rvcoords(points, incl_array=None, rv_rows=80, rv_cols=1800, rv_col_offset=0, device=None):
    """
    生成输入点云的rv坐标
    Parameters:
    @ points: Nx(3+C), xyz and extra infos.
    @ incl_array: 垂直倾斜角
    @ rv_rows: 雷达线数
    @ rv_cols: 扫描一周发射线数。
    """

    if isinstance(points, torch.Tensor):
        points = points.cpu().numpy()

    if incl_array is None:
        # 垂直高度角
        incl_array = np.array(
            [
                -13.565,
                -1.09,
                -4.39,
                -0.29,
                -3.59,
                -5.79,
                0.51,
                -2.79,
                3.51,
                -4.99,
                -1.99,
                5.06,
                -4.19,
                -19.582,
                -1.29,
                -3.39,
                -7.15,
                -0.49,
                -2.59,
                -5.99,
                0.31,
                -1.79,
                -5.19,
                -0.99,
                -25.0,
                -0.19,
                -7.65,
                0.61,
                -2.69,
                1.41,
                -1.89,
                -16.042,
                -1.19,
                -6.85,
                -0.39,
                0.41,
                -2.89,
                6.56,
                1.21,
                -2.09,
                -8.532,
                -0.69,
                -3.99,
                -6.19,
                0.11,
                -3.19,
                -5.39,
                0.91,
                -2.39,
                -4.59,
                -1.59,
                -3.79,
                2.51,
                -10.346,
                -0.89,
                -2.99,
                -0.09,
                -2.19,
                -5.59,
                0.71,
                -1.39,
                11.5,
                -4.79,
                -0.59,
                -11.742,
                0.21,
                -6.5,
                1.01,
                -2.29,
                1.81,
                -1.49,
                9.0,
                -9.244,
                -0.79,
                0.01,
                0.81,
                -2.49,
                15.0,
                1.61,
                -1.69,
            ]
        )

        incl_array = np.sort(incl_array)[::-1]  # decreasing order

        # 角度转弧度
        incl_array = incl_array / 180 * np.pi

    # 生成range view相关信息：range、incl、azimu
    # points_range = np.sqrt(points[:, 0] ** 2 + points[:, 1] ** 2 + points[:, 2] ** 2)  # N

    points_incl = np.arctan2(
        points[:, 2], np.sqrt(points[:, 0] ** 2 + points[:, 1] ** 2)
    )  # N, arctan(z, sqrt(x^2 + y^2))

    points_azimu = np.arctan2(points[:, 1], points[:, 0])  # N, arctan2(y, x)

    error_list = []
    for i in range(rv_rows):
        theta = incl_array[i]
        error = np.abs(theta - points_incl)  # N
        error_list.append(error)
    all_error = np.stack(error_list, axis=-1)  # N, N_INCL
    row_inds = np.argmin(all_error, axis=-1)  # N

    # height = rv_rows
    width = rv_cols
    col_inds = width - 1.0 + 0.5 - (points_azimu + np.pi) / (2.0 * np.pi) * width  # N
    col_inds = np.round(col_inds).astype(np.int32)
    col_inds[col_inds == width] = width - 1
    col_inds[col_inds < 0] = 0

    # 补偿 col offset
    col_inds_old = col_inds.copy()

    col_inds[col_inds_old < rv_col_offset] = col_inds[col_inds < rv_col_offset] + rv_cols - rv_col_offset
    col_inds[col_inds_old >= rv_col_offset] = col_inds[col_inds_old >= rv_col_offset] - rv_col_offset

    if device is not None:
        row_inds = torch.from_numpy(row_inds).int()
        col_inds = torch.from_numpy(col_inds).int()

        row_inds = row_inds.to(device)
        col_inds = col_inds.to(device)

    return row_inds, col_inds


def vis_range_view(rv_data):
    """
    根据range/intensity/elgation 可视化range image。
    Parameters:
    @rv_data: H, W or 1, H, W

    """
    if rv_data.ndim == 3:
        rv_data = rv_data.squeeze(0)

    max_range = 200
    rv_data[rv_data == -1] = max_range  # 处理nan。
    rv_data[rv_data > max_range] = max_range

    rv_data = rv_data / max_range * 255
    rv_data = rv_data.int()

    rv_data = rv_data.unsqueeze(-1).repeat(1, 1, 3)  # HW3

    return rv_data.cpu().numpy()
