import torch
import math
from perceptron.layers.head.range_view.target_assigner.rv_seg_topk_assigner import RVSegTopkAssigner
from perceptron_ops.roiaware_pool3d.roiaware_pool3d_utils import points_in_boxes_gpu


class RVSegAssignerInvWeight(RVSegTopkAssigner):
    """
    将target投影到球坐标系后映射到range view上。注意基于笛卡尔坐标系下的欧氏距离进行匹配是不稳定的
    选择 *落在gt_box内部的* topk个点。
    """

    def __init__(
        self,
        out_size_factor,
        tasks,
        dense_reg,
        gaussian_overlap,
        max_objs,
        min_radius,
        mapping,
        grid_size,
        pc_range,
        voxel_size,
        assign_topk,
        no_log=False,
    ):
        super().__init__(
            out_size_factor,
            tasks,
            dense_reg,
            gaussian_overlap,
            max_objs,
            min_radius,
            mapping,
            grid_size,
            pc_range,
            voxel_size,
            assign_topk,
            no_log,
        )

    def assign_targets(
        self,
        rv_coords,
        gt_boxes,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
    ):
        """
        Args:
            rv_coords: B, 3, H, W
            gt_boxes: (B, M, C + cls)

            Updated at 2022/06/20
            incl_array:

        Returns:

        """
        max_objs = self._max_objs * self.dense_reg
        feature_map_size = (rv_coords.shape[3], rv_coords.shape[2])  # W, H

        grids_x, grids_y = self.generate_anchor_grid(feature_map_size, 0.0, stride=1, device=gt_boxes.device)
        anchor_points = torch.cat(
            [grids_y.unsqueeze(1), grids_x.unsqueeze(1)], dim=1
        )  # (HW, 2). 存放顺序是 (col_idx, row_idx) / (x, y)

        batch_size = gt_boxes.shape[0]
        gt_classes = gt_boxes[:, :, -1]  # begin from 1
        gt_boxes = gt_boxes[:, :, :-1]

        heatmaps = {}
        gt_inds = {}
        gt_masks = {}
        gt_box_encodings = {}
        gt_cats = {}
        heatmap_weights = {}
        for task_id, task in enumerate(self.tasks):
            heatmaps[task_id] = []
            gt_inds[task_id] = []
            gt_masks[task_id] = []
            gt_box_encodings[task_id] = []
            gt_cats[task_id] = []
            heatmap_weights[task_id] = []

        for k in range(batch_size):
            cur_gt = gt_boxes[k]
            cnt = cur_gt.__len__() - 1
            while cnt > 0 and cur_gt[cnt].sum() == 0:
                cnt -= 1
            cur_gt = cur_gt[: cnt + 1]
            cur_gt_classes = gt_classes[k][: cnt + 1].int()

            # 过滤掉无效anchor
            cur_rv_coords = rv_coords[k]  # 3, H, W.  存放的是xyz
            nr_coord = cur_rv_coords.shape[0]
            cur_rv_coords = cur_rv_coords.view(nr_coord, -1).transpose(0, 1).contiguous()  # (3, HW) -> (HW, 3)
            invalid_anchor_mask = (cur_rv_coords == -1).all(dim=1)  # xyz均为-1的行
            valid_anchor_mask = ~invalid_anchor_mask

            for task_id, task in enumerate(self.tasks):
                heatmap = torch.zeros(
                    (len(task.class_names), feature_map_size[1], feature_map_size[0]), dtype=torch.float32
                ).to(cur_gt.device)
                gt_ind = torch.zeros(max_objs, dtype=torch.long).to(cur_gt.device)
                gt_mask = torch.zeros(max_objs, dtype=torch.bool).to(cur_gt.device)
                gt_cat = torch.zeros(max_objs, dtype=torch.long).to(cur_gt.device)
                gt_box_encoding = torch.zeros((max_objs, 8), dtype=torch.float32).to(cur_gt.device)

                cur_gts_of_task = []
                cur_classes_of_task = []
                class_offset = 0
                for class_name in task.class_names:
                    class_idx = self.class_to_idx[class_name]
                    class_mask = cur_gt_classes == class_idx
                    cur_gt_of_task = cur_gt[class_mask]
                    cur_class_of_task = cur_gt.new_full((cur_gt_of_task.shape[0],), class_offset).long()
                    cur_gts_of_task.append(cur_gt_of_task)
                    cur_classes_of_task.append(cur_class_of_task)
                    class_offset += 1

                cur_gts_of_task = torch.cat(cur_gts_of_task, dim=0)  # GT, 7
                cur_classes_of_task = torch.cat(cur_classes_of_task, dim=0)  # GT
                # num_boxes_of_task = cur_gts_of_task.shape[0]
                if len(cur_classes_of_task) == 0:
                    heatmaps[task_id].append(heatmap)
                    gt_inds[task_id].append(gt_ind)
                    gt_cats[task_id].append(gt_cat)
                    gt_masks[task_id].append(gt_mask)
                    gt_box_encodings[task_id].append(gt_box_encoding)
                    heatmap_weights[task_id].append(
                        heatmap.new_ones((1, feature_map_size[1], feature_map_size[0]), dtype=torch.float)
                    )
                    continue

                cur_gts_of_task[:, 6] = self.limit_period(cur_gts_of_task[:, 6], offset=0.5, period=math.pi * 2)
                # 2. 选择框内的topk个anchor作为positive
                assign_ids = anchor_points.new_ones((anchor_points.shape[0]), dtype=torch.long)

                # 过滤掉无效anchor
                cur_rv_coords = rv_coords[k]  # 3, H, W.  存放的是xyz
                nr_coord = cur_rv_coords.shape[0]
                cur_rv_coords = cur_rv_coords.view(nr_coord, -1).transpose(0, 1).contiguous()  # (3, HW) -> (HW, 3)
                invalid_anchor_mask = (cur_rv_coords == -1).all(dim=1)  # xyz均为-1的行
                valid_anchor_mask = ~invalid_anchor_mask

                # 只选择出现在gt box内部的点

                inbox_mask_int = points_in_boxes_gpu(
                    cur_rv_coords[None, :, :3].contiguous(), cur_gts_of_task[None].contiguous()
                )  # (1, ANC)

                inbox_mask = inbox_mask_int.new_zeros(
                    (cur_rv_coords.shape[0], cur_gts_of_task.shape[0] + 1), dtype=torch.bool
                )
                inbox_mask = torch.scatter(inbox_mask, 1, (inbox_mask_int + 1).permute(1, 0).long(), 1)  # (ANC, #box)

                # make sure that inbox points is valid anchor.
                inbox_mask = inbox_mask & valid_anchor_mask[:, None]
                uni_count = inbox_mask.sum(dim=0)
                box_weight = self.assign_topk / (uni_count.float() + 1e-3)

                box_weight[0] = 1.0

                inbox_mask = inbox_mask[:, 1:].contiguous()

                heatmap_weight = box_weight[(inbox_mask_int + 1).permute(1, 0).long()]  # (ANC, 1)

                # 对每个gt box，挑选前topk个落在框内的点。
                pos_mask = torch.zeros(anchor_points.shape[0]).to(anchor_points.device)
                pos_mask = pos_mask.bool()
                nr_gt_boxes = inbox_mask.shape[1]
                for gt_idx in range(nr_gt_boxes):

                    in_certain_box_mask = inbox_mask[:, gt_idx]  # (ANCHOR_)

                    certain_positive_mask = in_certain_box_mask.bool()

                    pos_mask = pos_mask | certain_positive_mask
                    assign_ids[certain_positive_mask] = gt_idx

                pos_mask = pos_mask.bool()  # ANC

                # 4. 每个anchor point assign给最近的gt
                pos_gt_ids = assign_ids[pos_mask]

                # 5. 生成targets
                gt_cat = cur_classes_of_task[pos_gt_ids]  # POS,
                gt_mask = torch.ones_like(pos_gt_ids)  # POS,
                gt_ind = torch.where(pos_mask == 1)[0]  # POS,

                heatmap = torch.zeros(
                    anchor_points.shape[0], len(task.class_names), device=gt_cat.device
                )  # （ANC, num_class)
                heatmap[pos_mask] = heatmap[pos_mask].scatter_(1, gt_cat.unsqueeze(1), 1)

                heatmap = heatmap.transpose(1, 0).reshape(
                    len(task.class_names), feature_map_size[1], feature_map_size[0]
                )

                loc_targets = cur_gts_of_task[pos_gt_ids]  # POS, 7
                pos_anchor_points = cur_rv_coords[pos_mask]  # POS, 2
                # pos_anchor_points = anchor_points[pos_mask]  # POS, 2
                gt_box_encoding = torch.cat(
                    [
                        loc_targets[:, 0:3] - pos_anchor_points,  # x, y, z
                        torch.log(loc_targets[:, 3:4]),
                        torch.log(loc_targets[:, 4:5]),
                        torch.log(loc_targets[:, 5:6]),
                        torch.sin(loc_targets[:, 6:7]),
                        torch.cos(loc_targets[:, 6:7]),
                    ],
                    dim=1,
                ).to(
                    heatmap.device
                )  # POS, 7

                # padding to fixed shape
                gt_cat = self.padding_to_maxobjs(gt_cat, max_objs).long()
                gt_mask = self.padding_to_maxobjs(gt_mask, max_objs).bool()
                gt_ind = self.padding_to_maxobjs(gt_ind, max_objs).long()
                gt_box_encoding = self.padding_to_maxobjs(gt_box_encoding, max_objs).float()

                heatmap_weight = heatmap_weight.transpose(1, 0).reshape(1, feature_map_size[1], feature_map_size[0])
                heatmap_weights[task_id].append(heatmap_weight)
                heatmaps[task_id].append(heatmap)
                gt_inds[task_id].append(gt_ind)
                gt_cats[task_id].append(gt_cat)
                gt_masks[task_id].append(gt_mask)
                gt_box_encodings[task_id].append(gt_box_encoding)

        for task_id, tasks in enumerate(self.tasks):
            heatmaps[task_id] = torch.stack(heatmaps[task_id], dim=0).contiguous()
            heatmap_weights[task_id] = torch.stack(heatmap_weights[task_id], dim=0).contiguous()
            gt_inds[task_id] = torch.stack(gt_inds[task_id], dim=0).contiguous()
            gt_masks[task_id] = torch.stack(gt_masks[task_id], dim=0).contiguous()
            gt_cats[task_id] = torch.stack(gt_cats[task_id], dim=0).contiguous()
            gt_box_encodings[task_id] = torch.stack(gt_box_encodings[task_id], dim=0).contiguous()

        target_dict = {
            "heatmap": heatmaps,
            "ind": gt_inds,
            "mask": gt_masks,
            "cat": gt_cats,
            "box_encoding": gt_box_encodings,
            "heatmap_weights": heatmap_weights,
        }
        return target_dict
