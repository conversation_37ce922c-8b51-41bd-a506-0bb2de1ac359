import torch
import math
from perceptron.layers.head.det3d.target_assigner.fcos_assigner import FCOSAssigner
from perceptron_ops.roiaware_pool3d.roiaware_pool3d_utils import points_in_boxes_gpu


class RVSegTopkAssignerPosMask3DTopK(FCOSAssigner):
    """
    将target投影到球坐标系后映射到range view上。注意基于笛卡尔坐标系下的欧氏距离进行匹配是不稳定的
    选择 *落在gt_box内部的* topk个点。
    """

    def __init__(
        self,
        out_size_factor,
        tasks,
        dense_reg,
        gaussian_overlap,
        max_objs,
        min_radius,
        mapping,
        grid_size,
        pc_range,
        voxel_size,
        assign_topk,
        no_log=False,
    ):
        super().__init__(
            out_size_factor,
            tasks,
            dense_reg,
            gaussian_overlap,
            max_objs,
            min_radius,
            mapping,
            grid_size,
            pc_range,
            voxel_size,
            assign_topk,
            no_log,
        )
        # self.target_list  = [[] for _ in self.tasks]

    def generate_anchor_grid(self, featmap_size, offsets, stride, device):
        step_x, step_y = featmap_size
        shift = offsets * stride
        grid_x = torch.linspace(shift, (step_x - 1) * stride + shift, steps=step_x, device=device)
        grid_y = torch.linspace(shift, (step_y - 1) * stride + shift, steps=step_y, device=device)
        grids_x, grids_y = torch.meshgrid(grid_y, grid_x)
        return grids_x.reshape(-1), grids_y.reshape(-1)

    def assign_targets(
        self,
        rv_coords,
        gt_boxes,
        incl_array=None,
        ncols=1800,
        rv_col_offset=1350,
        rv_mv_origin=[0, 0, 0],
        rv_range=None,
    ):
        """
        Args:
            rv_coords: B, 3, H, W
            gt_boxes: (B, M, C + cls)

            Updated at 2022/06/20
            incl_array:

        Returns:

        """
        max_objs = self._max_objs * self.dense_reg
        feature_map_size = (rv_coords.shape[3], rv_coords.shape[2])  # W, H
        ANC = feature_map_size[0] * feature_map_size[1]
        device = rv_coords.device

        # grids_x, grids_y = self.generate_anchor_grid(feature_map_size, 0.0, stride=1, device=gt_boxes.device)
        # anchor_points = torch.cat(
        #     [grids_y.unsqueeze(1), grids_x.unsqueeze(1)], dim=1
        # )  # (HW, 2). 存放顺序是 (col_idx, row_idx) / (x, y)

        batch_size = gt_boxes.shape[0]
        gt_classes = gt_boxes[:, :, -1]  # begin from 1
        gt_boxes = gt_boxes[:, :, :-1]

        heatmaps = {}
        gt_inds = {}
        gt_masks = {}
        gt_box_encodings = {}
        gt_cats = {}
        for task_id, task in enumerate(self.tasks):
            heatmaps[task_id] = []
            gt_inds[task_id] = []
            gt_masks[task_id] = []
            gt_box_encodings[task_id] = []
            gt_cats[task_id] = []

        for k in range(batch_size):
            cur_gt = gt_boxes[k]
            cnt = cur_gt.__len__() - 1
            while cnt > 0 and cur_gt[cnt].sum() == 0:
                cnt -= 1
            cur_gt = cur_gt[: cnt + 1]
            cur_gt_classes = gt_classes[k][: cnt + 1].int()

            for task_id, task in enumerate(self.tasks):
                heatmap = torch.zeros(
                    (len(task.class_names), feature_map_size[1], feature_map_size[0]), dtype=torch.float32
                ).to(cur_gt.device)
                gt_ind = torch.zeros(max_objs, dtype=torch.long).to(cur_gt.device)
                gt_mask = torch.zeros(max_objs, dtype=torch.bool).to(cur_gt.device)
                gt_cat = torch.zeros(max_objs, dtype=torch.long).to(cur_gt.device)
                gt_box_encoding = torch.zeros((max_objs, 8), dtype=torch.float32).to(cur_gt.device)

                cur_gts_of_task = []
                cur_classes_of_task = []
                class_offset = 0
                for class_name in task.class_names:
                    class_idx = self.class_to_idx[class_name]
                    class_mask = cur_gt_classes == class_idx
                    cur_gt_of_task = cur_gt[class_mask]
                    cur_class_of_task = cur_gt.new_full((cur_gt_of_task.shape[0],), class_offset).long()
                    cur_gts_of_task.append(cur_gt_of_task)
                    cur_classes_of_task.append(cur_class_of_task)
                    class_offset += 1

                cur_gts_of_task = torch.cat(cur_gts_of_task, dim=0)  # GT, 7
                cur_classes_of_task = torch.cat(cur_classes_of_task, dim=0)  # GT
                # num_boxes_of_task = cur_gts_of_task.shape[0]
                if len(cur_classes_of_task) == 0:
                    heatmaps[task_id].append(heatmap)
                    gt_inds[task_id].append(gt_ind)
                    gt_cats[task_id].append(gt_cat)
                    gt_masks[task_id].append(gt_mask)
                    gt_box_encodings[task_id].append(gt_box_encoding)
                    continue

                # cur_gts_rv_row_idxs, cur_gts_rc_col_idxs = generate_rvcoords(
                #     cur_gts_of_task[:, :3],
                #     incl_array=incl_array,
                #     rv_rows=len(incl_array),
                #     rv_cols=ncols,
                #     rv_col_offset=rv_col_offset,
                #     device=device,
                # )
                # cur_gts_rv_coords = torch.stack((cur_gts_rc_col_idxs, cur_gts_rv_row_idxs), dim=-1)  # GT, 2
                cur_gts_of_task[:, 6] = self.limit_period(cur_gts_of_task[:, 6], offset=0.5, period=math.pi * 2)

                # x1y1 = cur_gts_of_task[:, 0:2] - cur_gts_of_task[:, 3:5] / 2
                # x2y2 = cur_gts_of_task[:, 0:2] + cur_gts_of_task[:, 3:5] / 2

                # 1. 在gt框内容的anchor point作为正样本（小目标掉点，故去掉）
                # ltrb = \
                #     torch.cat([
                #         anchor_points.unsqueeze(1) - x1y1.unsqueeze(0),
                #         x2y2.unsqueeze(0) - anchor_points.unsqueeze(1)
                #         ], dim=2)   # (ANC, GT, 4)

                # in_gt_matrix = ltrb.min(dim=2)[0] >= 0    # (ANC, GT)
                # in_gt_mask = in_gt_matrix.max(dim=1)[1] > 0    # ANC,

                # 2. 选择框内的topk个anchor作为positive
                topk = self.assign_topk
                # center_offsets = torch.pow(anchor_points.unsqueeze(1) - cur_gts_rv_coords.unsqueeze(0), 2).sum(
                #     dim=2
                # )  # (ANC, GT)
                assign_ids = rv_coords.new_ones((ANC), dtype=torch.long)

                # 过滤掉无效anchor
                cur_rv_coords = rv_coords[k]  # 3, H, W.  存放的是xyz
                nr_coord = cur_rv_coords.shape[0]
                cur_rv_coords = cur_rv_coords.view(nr_coord, -1).transpose(0, 1).contiguous()  # (3, HW) -> (HW, 3)
                invalid_anchor_mask = (cur_rv_coords == -1).all(dim=1)  # xyz均为-1的行
                valid_anchor_mask = ~invalid_anchor_mask

                # 只选择出现在gt box内部的点
                # inbox_mask = points_in_boxes_cpu(
                #     cur_rv_coords[:, :3].cpu(), cur_gts_of_task.cpu()
                # ).T  # (ANC, GT). 1 if in box. (int)
                # inbox_mask = inbox_mask.to(device)
                # inbox_mask = inbox_mask.bool()

                inbox_mask_int = points_in_boxes_gpu(
                    cur_rv_coords[None, :, :3].contiguous(), cur_gts_of_task[None].contiguous()
                )
                inbox_mask = inbox_mask_int.new_zeros(
                    (cur_rv_coords.shape[0], cur_gts_of_task.shape[0] + 1), dtype=torch.bool
                )
                inbox_mask = torch.scatter(inbox_mask, 1, (inbox_mask_int + 1).permute(1, 0).long(), 1)
                inbox_mask = inbox_mask[:, 1:].contiguous()

                # import pickle
                # with open('points_in_box_data.pkl', 'wb') as f:
                #     pickle.dump([cur_rv_coords[:, :3].cpu(), cur_gts_of_task.cpu(), inbox_mask], f)
                # 对每个gt box，挑选前topk个落在框内的点。
                pos_mask = torch.zeros((ANC), device=device)
                pos_mask = pos_mask.bool()
                nr_gt_boxes = inbox_mask.shape[1]
                for gt_idx in range(nr_gt_boxes):
                    # certain_gt_box_coord = cur_gts_rv_coords[gt_idx : gt_idx + 1, :]  # (1, 2)
                    certain_gt_box_xyz = cur_gts_of_task[gt_idx : gt_idx + 1, :3]  # (1, 3)
                    in_certain_box_mask = inbox_mask[:, gt_idx]  # (ANCHOR_)
                    # in_certain_box_anchors = anchor_points[in_certain_box_mask]  # (ANCHOR_, 2) 落在特定gt框内的所有anchors
                    in_certain_box_anchors = cur_rv_coords[in_certain_box_mask]  # (ANC, 3)
                    certain_center_offsets_xyz = torch.norm(
                        in_certain_box_anchors.unsqueeze(1) - certain_gt_box_xyz.unsqueeze(0), dim=2
                    )  # (ANC, 1)
                    # certain_center_offsets = torch.pow(
                    #     in_certain_box_anchors.unsqueeze(1) - certain_gt_box_coord.unsqueeze(0), 2
                    # ).sum(
                    #     dim=2
                    # )  # (ANC, 1) # 落在特定gt框内的anchor与该gt框的距离。
                    # _, tmp_topk_inds = torch.topk(
                    #     certain_center_offsets.t(), min(topk, in_certain_box_anchors.shape[0]), largest=False
                    # )  # (1, topk)
                    _, tmp_topk_inds = torch.topk(
                        certain_center_offsets_xyz.t(), min(topk, in_certain_box_anchors.shape[0]), largest=False
                    )  # (1, topk)
                    tmp_topk_inds = tmp_topk_inds[0]  # topk

                    in_centain_box_inds = torch.where(in_certain_box_mask)[0]

                    certain_positive_anchor_inds = in_centain_box_inds[tmp_topk_inds]  # 特定gt框内对应positive anchor的索引。

                    certain_positive_mask = torch.zeros((ANC), device=device)  # ANC,
                    certain_positive_mask[certain_positive_anchor_inds.flatten()] = 1
                    certain_positive_mask = (certain_positive_mask * valid_anchor_mask).bool()

                    pos_mask = pos_mask | certain_positive_mask
                    assign_ids[certain_positive_mask] = gt_idx

                pos_mask = pos_mask.bool()  # ANC

                # 3. 强制匹配top1，有topk保证其实就不需要了
                # _, top1_inds = torch.topk(center_offsets.t(), 1, largest=False)  # (GT, 1)
                # pos_mask[top1_inds.flatten()] = True

                # 4. 每个anchor point assign给最近的gt
                # _, gt_ids = center_offsets.min(dim=1)  # ANC,
                # pos_gt_ids = gt_ids[pos_mask]  # POS,
                pos_gt_ids = assign_ids[pos_mask]

                # 5. 生成targets
                gt_cat = cur_classes_of_task[pos_gt_ids]  # POS,
                gt_mask = torch.ones_like(pos_gt_ids)  # POS,
                gt_ind = torch.where(pos_mask == 1)[0]  # POS,

                heatmap = torch.zeros((ANC, len(task.class_names)), device=gt_cat.device)  # （ANC, num_class)
                heatmap[pos_mask] = heatmap[pos_mask].scatter_(1, gt_cat.unsqueeze(1), 1)
                heatmap = heatmap.transpose(1, 0).reshape(
                    len(task.class_names), feature_map_size[1], feature_map_size[0]
                )

                loc_targets = cur_gts_of_task[pos_gt_ids]  # POS, 7
                pos_anchor_points = cur_rv_coords[pos_mask]  # POS, 2
                # pos_anchor_points = anchor_points[pos_mask]  # POS, 2
                gt_box_encoding = torch.cat(
                    [
                        loc_targets[:, 0:3] - pos_anchor_points,  # x, y, z
                        torch.log(loc_targets[:, 3:4]),
                        torch.log(loc_targets[:, 4:5]),
                        torch.log(loc_targets[:, 5:6]),
                        torch.sin(loc_targets[:, 6:7]),
                        torch.cos(loc_targets[:, 6:7]),
                    ],
                    dim=1,
                ).to(
                    heatmap.device
                )  # POS, 7

                # padding to fixed shape
                gt_cat = self.padding_to_maxobjs(gt_cat, max_objs).long()
                gt_mask = self.padding_to_maxobjs(gt_mask, max_objs).bool()
                gt_ind = self.padding_to_maxobjs(gt_ind, max_objs).long()
                gt_box_encoding = self.padding_to_maxobjs(gt_box_encoding, max_objs).float()

                # # 可视化label assign
                # import cv2
                # import numpy as np
                # rv_img = vis_range_view(rv_range[k])
                # img = rv_img.copy()
                # border = np.zeros((5, ncols, 3)).astype(np.uint8)
                # border[:, :, 0] = 255

                # # import IPython; IPython.embed()

                # img = np.concatenate((img, border, rv_img), axis=0)

                # pos_anchor_points_ = anchor_points[pos_mask]
                # for x, y in pos_anchor_points_:
                #     cv2.circle(img, (int(x), int(y)), 2, (0, 0, 255), 2)

                # # gt 框左上角，右下角坐标。
                # x1y1z1 = cur_gts_of_task[:, 0:3] - cur_gts_of_task[:, 3:6] / 2 # N_G, 3
                # x2y2z2 = cur_gts_of_task[:, 0:3] + cur_gts_of_task[:, 3:6] / 2 # N_G, 3

                # gt_lt_rows, gt_lt_cols = generate_rvcoords(x1y1z1,
                #     incl_array=incl_array,
                #     rv_rows=len(incl_array),
                #     rv_cols=ncols,
                #     rv_col_offset=rv_col_offset) # N_G left top

                # gt_rb_rows, gt_rb_cols = generate_rvcoords(x2y2z2,
                #     incl_array=incl_array,
                #     rv_rows=len(incl_array),
                #     rv_cols=ncols,
                #     rv_col_offset=rv_col_offset) # N_G right bottom

                # gtboxes = np.stack([gt_lt_cols, gt_lt_rows, gt_rb_cols, gt_rb_rows], axis=-1)
                # for box in gtboxes:
                #     x1, y1, x2, y2 = box
                #     cv2.rectangle(img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                # for x, y in zip(cur_gts_rc_col_idxs, cur_gts_rv_row_idxs):
                #     cv2.circle(img, (int(x), int(y)), 2, (255, 0, 0), 2)

                # cv2.imwrite(f'./car2_sphere{k}.png', img)
                # from IPython import embed; embed()

                heatmaps[task_id].append(heatmap)
                gt_inds[task_id].append(gt_ind)
                gt_cats[task_id].append(gt_cat)
                gt_masks[task_id].append(gt_mask)
                gt_box_encodings[task_id].append(gt_box_encoding)

        for task_id, tasks in enumerate(self.tasks):
            heatmaps[task_id] = torch.stack(heatmaps[task_id], dim=0).contiguous()
            gt_inds[task_id] = torch.stack(gt_inds[task_id], dim=0).contiguous()
            gt_masks[task_id] = torch.stack(gt_masks[task_id], dim=0).contiguous()
            gt_cats[task_id] = torch.stack(gt_cats[task_id], dim=0).contiguous()
            gt_box_encodings[task_id] = torch.stack(gt_box_encodings[task_id], dim=0).contiguous()

            # if len(self.target_list[task_id]) > 200:
            #     import pickle
            #     with open('target_list.pkl', 'wb') as f:
            #         pickle.dump(self.target_list, f)
            #     exit()
            # self.target_list[task_id].append(gt_box_encodings[task_id].cpu())

        target_dict = {
            "heatmap": heatmaps,
            "ind": gt_inds,
            "mask": gt_masks,
            "cat": gt_cats,
            "box_encoding": gt_box_encodings,
        }
        return target_dict
