from __future__ import with_statement
import copy
import torch
import torch.nn as nn
from mmcv.runner import BaseModule, force_fp32
from mmcv.cnn import ConvModule, build_conv_layer
from mmdet.core import build_assigner, build_sampler, multi_apply, reduce_mean, build_bbox_coder
from mmdet.models.utils import build_transformer
from mmdet.models import build_loss
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet3d.models import builder
import collections
from perceptron.layers.head.det3d.bbox.util import normalize_bbox
from perceptron.layers.blocks_3d.det3d import CMTTransformer  # noqa3
from mmdet.models import HEADS
from einops import rearrange
from mmdet3d.core import draw_heatmap_gaussian, gaussian_radius
from mmdet3d.models.utils.clip_sigmoid import clip_sigmoid
import torch.nn.functional as F
from scipy.spatial.transform import RotationSpline, Rotation
from scipy.interpolate import CubicSpline
from mmcv.cnn import bias_init_with_prob
from perceptron.layers.blocks_3d.prediction3d.modules import SinePositionalEncoding3D
from perceptron.utils.e2e_utils.utils import pos2posemb3d, pos2embed


@HEADS.register_module()
class SeparateMlpHead(BaseModule):
    """SeparateHead for CenterHead.

    Args:
        in_channels (int): Input channels for conv_layer.
        heads (dict): Conv information.
        head_conv (int): Output channels.
            Default: 64.
        final_kernal (int): Kernal size for the last conv layer.
            Deafult: 1.
        init_bias (float): Initial bias. Default: -2.19.
        conv_cfg (dict): Config of conv layer.
            Default: dict(type='Conv2d')
        norm_cfg (dict): Config of norm layer.
            Default: dict(type='BN2d').
        bias (str): Type of bias. Default: 'auto'.
    """

    def __init__(
        self,
        in_channels,
        heads,
        groups=1,
        head_conv=64,
        final_kernel=1,
        init_bias=-2.19,
        conv_cfg=dict(type="Conv1d"),
        norm_cfg=dict(type="BN1d"),
        bias="auto",
        init_cfg=None,
        **kwargs,
    ):
        assert init_cfg is None, "To prevent abnormal initialization " "behavior, init_cfg is not allowed to be set"
        super(SeparateMlpHead, self).__init__(init_cfg=init_cfg)
        self.heads = heads
        self.init_bias = init_bias
        for head in self.heads:
            classes, num_conv = self.heads[head]

            conv_layers = []
            c_in = in_channels
            for i in range(num_conv - 1):
                conv_layers.append(
                    ConvModule(
                        c_in,
                        head_conv,
                        kernel_size=final_kernel,
                        stride=1,
                        padding=final_kernel // 2,
                        bias=bias,
                        conv_cfg=conv_cfg,
                        norm_cfg=norm_cfg,
                    )
                )
                c_in = head_conv

            conv_layers.append(
                build_conv_layer(
                    conv_cfg,
                    head_conv,
                    classes,
                    kernel_size=final_kernel,
                    stride=1,
                    padding=final_kernel // 2,
                    bias=True,
                )
            )
            conv_layers = nn.Sequential(*conv_layers)

            self.__setattr__(head, conv_layers)

            if init_cfg is None:
                self.init_cfg = dict(type="Kaiming", layer="Conv1d")

    def init_weights(self):
        """Initialize weights."""
        super().init_weights()
        for head in self.heads:
            if head == "cls_logits":
                self.__getattr__(head)[-1].bias.data.fill_(self.init_bias)

    def forward(self, x):
        """Forward function for SepHead.

        Args:
            x (torch.Tensor): Input feature map with the shape of
                [N, B, query, C].

        Returns:
            dict[str: torch.Tensor]: contains the following keys:

                -reg （torch.Tensor): 2D regression value with the \
                    shape of [N, B, query, 2].
                -height (torch.Tensor): Height value with the \
                    shape of [N, B, query, 1].
                -dim (torch.Tensor): Size value with the shape \
                    of [N, B, query, 3].
                -rot (torch.Tensor): Rotation value with the \
                    shape of [N, B, query, 2].
                -vel (torch.Tensor): Velocity value with the \
                    shape of [N, B, query, 2].
        """
        N, B, query_num, c1 = x.shape
        x = rearrange(x, "n b q c -> (n b) c q")
        ret_dict = dict()

        for head in self.heads:
            head_output = self.__getattr__(head)(x)
            ret_dict[head] = head_output.transpose(2, 1).reshape(N, B, query_num, head_output.shape[1])

        return ret_dict


class LayerNormFunction(torch.autograd.Function):
    @staticmethod
    def forward(ctx, x, weight, bias, groups, eps):
        ctx.groups = groups
        ctx.eps = eps
        N, C, L = x.size()
        x = x.view(N, groups, C // groups, L)
        mu = x.mean(2, keepdim=True)
        var = (x - mu).pow(2).mean(2, keepdim=True)
        y = (x - mu) / (var + eps).sqrt()
        ctx.save_for_backward(y, var, weight)
        y = weight.view(1, C, 1) * y.view(N, C, L) + bias.view(1, C, 1)
        return y

    @staticmethod
    def backward(ctx, grad_output):
        groups = ctx.groups
        eps = ctx.eps

        N, C, L = grad_output.size()
        y, var, weight = ctx.saved_variables
        g = grad_output * weight.view(1, C, 1)
        g = g.view(N, groups, C // groups, L)
        mean_g = g.mean(dim=2, keepdim=True)
        mean_gy = (g * y).mean(dim=2, keepdim=True)
        gx = 1.0 / torch.sqrt(var + eps) * (g - y * mean_gy - mean_g)
        return (
            gx.view(N, C, L),
            (grad_output * y.view(N, C, L)).sum(dim=2).sum(dim=0),
            grad_output.sum(dim=2).sum(dim=0),
            None,
            None,
        )


class GroupLayerNorm1d(nn.Module):
    def __init__(self, channels, groups=1, eps=1e-6):
        super(GroupLayerNorm1d, self).__init__()
        self.register_parameter("weight", nn.Parameter(torch.ones(channels)))
        self.register_parameter("bias", nn.Parameter(torch.zeros(channels)))
        self.groups = groups
        self.eps = eps

    def forward(self, x):
        return LayerNormFunction.apply(x, self.weight, self.bias, self.groups, self.eps)


@HEADS.register_module()
class SeparateTaskHead(BaseModule):
    """SeparateHead for CenterHead.

    Args:
        in_channels (int): Input channels for conv_layer.
        heads (dict): Conv information.
        head_conv (int): Output channels.
            Default: 64.
        final_kernal (int): Kernal size for the last conv layer.
            Deafult: 1.
        init_bias (float): Initial bias. Default: -2.19.
        conv_cfg (dict): Config of conv layer.
            Default: dict(type='Conv2d')
        norm_cfg (dict): Config of norm layer.
            Default: dict(type='BN2d').
        bias (str): Type of bias. Default: 'auto'.
    """

    def __init__(
        self, in_channels, heads, groups=1, head_conv=64, final_kernel=1, init_bias=-2.19, init_cfg=None, **kwargs
    ):
        assert init_cfg is None, "To prevent abnormal initialization " "behavior, init_cfg is not allowed to be set"
        super(SeparateTaskHead, self).__init__(init_cfg=init_cfg)
        self.heads = heads
        self.groups = groups
        self.init_bias = init_bias
        for head in self.heads:
            classes, num_conv = self.heads[head]

            conv_layers = []
            c_in = in_channels
            for i in range(num_conv - 1):
                conv_layers.extend(
                    [
                        nn.Conv1d(
                            c_in * groups,
                            head_conv * groups,
                            kernel_size=final_kernel,
                            stride=1,
                            padding=final_kernel // 2,
                            groups=groups,
                            bias=False,
                        ),
                        GroupLayerNorm1d(head_conv * groups, groups=groups),
                        nn.ReLU(inplace=True),
                    ]
                )
                c_in = head_conv

            conv_layers.append(
                nn.Conv1d(
                    head_conv * groups,
                    classes * groups,
                    kernel_size=final_kernel,
                    stride=1,
                    padding=final_kernel // 2,
                    groups=groups,
                    bias=True,
                )
            )
            conv_layers = nn.Sequential(*conv_layers)

            self.__setattr__(head, conv_layers)

            if init_cfg is None:
                self.init_cfg = dict(type="Kaiming", layer="Conv1d")

    def init_weights(self):
        """Initialize weights."""
        super().init_weights()
        for head in self.heads:
            if head == "cls_logits":
                self.__getattr__(head)[-1].bias.data.fill_(self.init_bias)

    def forward(self, x):
        """Forward function for SepHead.

        Args:
            x (torch.Tensor): Input feature map with the shape of
                [N, B, query, C].

        Returns:
            dict[str: torch.Tensor]: contains the following keys:

                -reg （torch.Tensor): 2D regression value with the \
                    shape of [N, B, query, 2].
                -height (torch.Tensor): Height value with the \
                    shape of [N, B, query, 1].
                -dim (torch.Tensor): Size value with the shape \
                    of [N, B, query, 3].
                -rot (torch.Tensor): Rotation value with the \
                    shape of [N, B, query, 2].
                -vel (torch.Tensor): Velocity value with the \
                    shape of [N, B, query, 2].
        """
        N, B, query_num, c1 = x.shape
        x = rearrange(x, "n b q c -> b (n c) q")
        ret_dict = dict()

        for head in self.heads:
            head_output = self.__getattr__(head)(x)
            ret_dict[head] = rearrange(head_output, "b (n c) q -> n b q c", n=N)

        return ret_dict


class CMTFusionHead(BaseModule):
    def __init__(
        self,
        in_channels,
        modal="Fusion",
        depth_num=64,
        num_query=900,
        init_radar_num_query=0,
        hidden_dim=128,
        grid_size=[1440, 1440, 40],
        norm_bbox=True,
        downsample_scale=8,
        scalar=10,
        noise_scale=1.0,
        noise_trans=0.0,
        dn_weight=1.0,
        split=0.75,
        #  assigner_cfg=None,
        train_cfg=None,
        test_cfg=None,
        common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
        tasks=[
            dict(num_class=1, class_names=["car"]),
            dict(num_class=2, class_names=["truck", "construction_vehicle"]),
            dict(num_class=2, class_names=["bus", "trailer"]),
            dict(num_class=1, class_names=["barrier"]),
            dict(num_class=2, class_names=["motorcycle", "bicycle"]),
            dict(num_class=2, class_names=["pedestrian", "traffic_cone"]),
        ],
        transformer=None,
        bbox_coder=None,
        loss_cls=dict(type="FocalLoss", use_sigmoid=True, reduction="mean", gamma=2, alpha=0.25, loss_weight=1.0),
        loss_bbox=dict(
            type="L1Loss",
            reduction="mean",
            loss_weight=0.25,
        ),
        loss_heatmap=dict(type="GuassianFocalLoss", reduction="mean"),
        separate_head=dict(type="SeparateMlpHead", init_bias=-2.19, final_kernel=3),
        init_cfg=None,
        repaired_timestamp=False,
        alpha=0.2,  # htt: 需要标记注明一下alpha的含义
        use_roi_mask=False,
        with_multiview=False,
        bg_cls_weight=0.1,
        **kwargs,
    ):
        """CMTFusionHead
        Args:
            with_multiview: 是否使用multiview PE, 在PETR v2中引入
            bg_cls_weight: float, 在计算avg_cls_factor时, 对背景数量;
                具体计算方式为: cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.bg_cls_weight
                该代码在DETR repo中引入, 但在PETR v2中设置为0
                TODO: 在之前的private代码中默认均为0.1, 后续需验证是否设置为0更佳
        """

        self.bg_cls_weight = bg_cls_weight
        if loss_cls is not None and "bg_cls_weight" in loss_cls:
            assert (
                loss_cls["bg_cls_weight"] == bg_cls_weight
            ), f"bg_cls_weight in loss_cls: {loss_cls['bg_cls_weight']} != bg_cls_weight in {self.__class__}: {bg_cls_weight}, please use bg_cls_weight in {self.__class__}"

        assert init_cfg is None
        assert isinstance(modal, list)
        assert set(modal).issubset(["Camera", "Lidar", "LiDAR", "Radar"])

        super(CMTFusionHead, self).__init__(init_cfg=init_cfg)
        self.num_classes = [len(t["class_names"]) for t in tasks]
        self.class_names = [t["class_names"] for t in tasks]
        self.hidden_dim = hidden_dim
        # self.assigner_cfg = assigner_cfg
        self.train_cfg = train_cfg
        self.grid_size = grid_size
        self.test_cfg = test_cfg
        self.num_query = num_query
        self.init_radar_num_query = init_radar_num_query
        self.in_channels = in_channels
        self.norm_bbox = norm_bbox
        self.downsample_scale = downsample_scale
        self.scalar = scalar
        self.bbox_noise_scale = noise_scale
        self.bbox_noise_trans = noise_trans
        self.dn_weight = dn_weight
        self.split = split

        self.loss_cls = build_loss(loss_cls)
        self.loss_bbox = build_loss(loss_bbox)
        self.loss_heatmap = build_loss(loss_heatmap)
        self.bbox_coder = build_bbox_coder(bbox_coder)
        self.pc_range = self.bbox_coder.pc_range
        self.fp16_enabled = False
        # timestamp problem
        self.repaired_timestamp = repaired_timestamp
        self.alpha = alpha
        # roi mask
        self.use_roi_mask = use_roi_mask
        # transformer
        self.transformer = build_transformer(transformer)

        # init lidar embedding  # abandoned in e2e
        if "Lidar" in modal and False:
            self.shared_conv = ConvModule(
                in_channels,
                hidden_dim,
                kernel_size=3,
                padding=1,
                conv_cfg=dict(type="Conv2d"),
                norm_cfg=dict(type="BN2d"),
            )
            self.bev_embedding = nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim), nn.ReLU(inplace=True), nn.Linear(hidden_dim, hidden_dim)
            )
        if "Camera" in modal:
            self.depth_num = depth_num
            self.rv_embedding = nn.Sequential(
                nn.Linear(self.depth_num * 3, self.hidden_dim * 4),
                nn.ReLU(inplace=True),
                nn.Linear(self.hidden_dim * 4, self.hidden_dim),
            )
        # htt: 此处待修改
        if "Radar" in modal:
            self.bev_embedding = nn.Sequential(
                nn.Linear(self.hidden_dim * 2, self.hidden_dim),
                nn.ReLU(inplace=True),
                nn.Linear(self.hidden_dim, self.hidden_dim),
            )
        # task head
        self.task_heads = nn.ModuleList()
        for num_cls in self.num_classes:
            heads = copy.deepcopy(common_heads)
            heads.update(dict(cls_logits=(num_cls, 2)))
            separate_head.update(
                in_channels=hidden_dim, heads=heads, num_cls=num_cls, groups=transformer.decoder.num_layers
            )
            self.task_heads.append(builder.build_head(separate_head))

        # assigner
        if train_cfg:
            self.assigner = build_assigner(train_cfg["assigner"])
            sampler_cfg = dict(type="PseudoSampler")
            self.sampler = build_sampler(sampler_cfg, context=self)

        self.with_multiview = with_multiview

        if self.with_multiview:
            self.adapt_pos3d = nn.Sequential(
                nn.Conv2d(self.hidden_dim * 3 // 2, self.hidden_dim * 4, kernel_size=1, stride=1, padding=0),
                nn.ReLU(),
                nn.Conv2d(self.hidden_dim * 4, self.hidden_dim, kernel_size=1, stride=1, padding=0),
            )
            self.positional_encoding = SinePositionalEncoding3D(num_feats=self.hidden_dim // 2, normalize=True)

        self.init_weights()

    def init_weights(self):
        super(CMTFusionHead, self).init_weights()
        self.reference_points = nn.Embedding(self.num_query - self.init_radar_num_query, 3)
        nn.init.uniform_(self.reference_points.weight.data, 0, 1)

    @property
    def coords_bev(self):
        # feature map size: [H, W]
        # lidar bev coord: [x, y]
        # H→y, W→x
        x_size, y_size = (
            int(self.grid_size[0] // self.downsample_scale),
            int(self.grid_size[1] // self.downsample_scale),
        )
        meshgrid = [[0, y_size - 1, y_size], [0, x_size - 1, x_size]]
        batch_y, batch_x = torch.meshgrid(*[torch.linspace(it[0], it[1], it[2]) for it in meshgrid])
        batch_x = (batch_x + 0.5) / x_size
        batch_y = (batch_y + 0.5) / y_size
        coord_base = torch.cat([batch_x[None], batch_y[None]], dim=0)
        coord_base = coord_base.view(2, -1).transpose(1, 0)  # (H*W, 2)
        return coord_base

    def prepare_for_dn(self, batch_size, reference_points, img_metas):
        """Generate de_noising query from gt_boxes
        Args:
            batch_size: int
            reference_points: (bs, n, c or 3)
            img_metas: dict{
                "gt_boxes": tensor, pos labels start from 1. # TODO: need to support LidarBoxes3d.

            }
        """
        if self.training:
            if "gt_boxes" in img_metas:
                gt_boxes = img_metas["gt_boxes"]
                mask = torch.any(gt_boxes[..., :9], dim=2)
                targets = [gt_boxes[i, mask[i, :], :9] for i in range(batch_size)]
                labels = [gt_boxes[i, mask[i, :], 9] - 1 for i in range(batch_size)]
            elif "ff_gt_bboxes_list" in img_metas:
                gt_boxes = img_metas["ff_gt_bboxes_list"]  # 10 dims
                labels = [x - 1 for x in img_metas["ff_gt_labels_list"]]  # cmt里面增加dn得-1
                targets = gt_boxes
            # add noise
            known = [(torch.ones_like(t)).cuda() for t in labels]
            know_idx = known
            unmask_bbox = unmask_label = torch.cat(known)
            known_num = [t.size(0) for t in targets]
            labels = torch.cat([t for t in labels])
            boxes = torch.cat([t for t in targets])
            batch_idx = torch.cat([torch.full((t.size(0),), i) for i, t in enumerate(targets)])

            known_indice = torch.nonzero(unmask_label + unmask_bbox)
            known_indice = known_indice.view(-1)

            groups = min(self.scalar, self.num_query // max(known_num))
            known_indice = known_indice.repeat(groups, 1).view(-1)
            known_labels = labels.repeat(groups, 1).view(-1).long().to(reference_points.device)
            known_labels_raw = labels.repeat(groups, 1).view(-1).long().to(reference_points.device)
            known_bid = batch_idx.repeat(groups, 1).view(-1)
            known_bboxs = boxes.repeat(groups, 1).to(reference_points.device)
            known_bbox_center = known_bboxs[:, :3].clone()
            known_bbox_scale = known_bboxs[:, 3:6].clone()
            if self.bbox_noise_scale > 0:
                diff = known_bbox_scale / 2 + self.bbox_noise_trans
                rand_prob = torch.rand_like(known_bbox_center) * 2 - 1.0
                known_bbox_center += torch.mul(rand_prob, diff) * self.bbox_noise_scale
                known_bbox_center[..., 0:1] = (known_bbox_center[..., 0:1] - self.pc_range[0]) / (
                    self.pc_range[3] - self.pc_range[0]
                )
                known_bbox_center[..., 1:2] = (known_bbox_center[..., 1:2] - self.pc_range[1]) / (
                    self.pc_range[4] - self.pc_range[1]
                )
                known_bbox_center[..., 2:3] = (known_bbox_center[..., 2:3] - self.pc_range[2]) / (
                    self.pc_range[5] - self.pc_range[2]
                )
                known_bbox_center = known_bbox_center.clamp(min=0.0, max=1.0)
                mask = torch.norm(rand_prob, 2, 1) > self.split
                known_labels[mask] = sum(self.num_classes)

            single_pad = int(max(known_num))
            pad_size = int(single_pad * groups)
            padding_bbox = torch.zeros(batch_size, pad_size, 3).to(reference_points.device)
            padded_reference_points = torch.cat([padding_bbox, reference_points], dim=1)

            if len(known_num):
                map_known_indice = torch.cat([torch.tensor(range(num)) for num in known_num])  # [1,2, 1,2,3]
                map_known_indice = torch.cat([map_known_indice + single_pad * i for i in range(groups)]).long()
            if len(known_bid):
                padded_reference_points[(known_bid.long(), map_known_indice)] = known_bbox_center.to(
                    reference_points.device
                )

            tgt_size = pad_size + self.num_query
            attn_mask = torch.ones(tgt_size, tgt_size).to(reference_points.device) < 0
            # match query cannot see the reconstruct
            attn_mask[pad_size:, :pad_size] = True
            # reconstruct cannot see each other
            for i in range(groups):
                if i == 0:
                    attn_mask[single_pad * i : single_pad * (i + 1), single_pad * (i + 1) : pad_size] = True
                if i == groups - 1:
                    attn_mask[single_pad * i : single_pad * (i + 1), : single_pad * i] = True
                else:
                    attn_mask[single_pad * i : single_pad * (i + 1), single_pad * (i + 1) : pad_size] = True
                    attn_mask[single_pad * i : single_pad * (i + 1), : single_pad * i] = True

            mask_dict = {
                "known_indice": torch.as_tensor(
                    known_indice
                ).long(),  # indices of gt bboxes correponding to dn query, shape [batch_size*pad_size]
                "batch_idx": torch.as_tensor(
                    batch_idx
                ).long(),  # batch indices of dn query, shape [batch_size*pad_size/group]
                "map_known_indice": torch.as_tensor(
                    map_known_indice
                ).long(),  # total indices of dn query shape [batch_size*pad_size]
                "known_lbs_bboxes": (
                    known_labels,
                    known_bboxs,
                ),  # target of dn query, known_labels include positive and negative label, shape tuple ([batch_size*pad_size],[batch_size*pad_size,9])
                "known_labels_raw": known_labels_raw,  # raw labels of dn query(corresponding gt bbox) , shape [batch_size*pad_size]
                "know_idx": know_idx,  # lables mask (unpad) ,shape tuple ([sample_0_gt_num],[sample_1_gt_num],...[sample_(batch_size-1)_gt_num])
                "pad_size": pad_size,  # total size of dn query per batch after padding shape [1]
            }

        else:
            padded_reference_points = reference_points
            attn_mask = None
            mask_dict = None

        return padded_reference_points, attn_mask, mask_dict

    def _rv_pe(self, img_feats, img_metas):
        BN, C, H, W = img_feats.shape
        pad_h, pad_w = img_metas["pad_shape"]
        # generate grid
        coords_h = torch.arange(H, device=img_feats[0].device).float() * pad_h / H
        coords_w = torch.arange(W, device=img_feats[0].device).float() * pad_w / W
        coords_d = (
            1
            + torch.arange(self.depth_num, device=img_feats[0].device).float() * (self.pc_range[4] - 1) / self.depth_num
        )
        coords_h, coords_w, coords_d = torch.meshgrid([coords_h, coords_w, coords_d])
        coords = torch.stack([coords_w, coords_h, coords_d, coords_h.new_ones(coords_h.shape)], dim=-1)
        H, W, D, C = coords.shape
        # inverse ida
        batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        coords = (
            img_metas["ida_mats"]
            .view(batch_size, sweep * num_cams, 1, 1, 1, 4, 4)
            .inverse()
            .matmul(coords.unsqueeze(-1))
        )
        # cam_to_ego
        # coords shape [B, N, H, W, D, 4, 1]
        coords = torch.cat((coords[..., :2, :] * coords[..., 2:3, :], coords[..., 2:, :]), 5)
        coords = torch.inverse(img_metas["lidar2imgs"]).view(batch_size, num_cams, 1, 1, 1, 4, 4).matmul(coords)
        # inverse bda
        if "bda_mat" in img_metas:
            coords = (
                img_metas["bda_mat"].unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 1, 4, 4)
                @ coords
            ).squeeze(-1)
        else:
            coords = coords.squeeze(-1)
        coords_3d = coords.view(batch_size * num_cams, H, W, D, C)
        # normalization
        coords_3d = (coords_3d[..., :3] - coords_3d.new_tensor(self.pc_range[:3])) / (
            coords_3d.new_tensor(self.pc_range[3:]) - coords_3d.new_tensor(self.pc_range[:3])
        )
        if self.with_multiview:
            coords_3d = inverse_sigmoid(coords_3d)
        # rv_pos_embeds: torch.Size([BN, H, W, C])
        rv_pos_embeds = self.rv_embedding(coords_3d.reshape(*coords_3d.shape[:-2], -1))
        if self.with_multiview:
            batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
            BN, C, H, W = img_feats.shape
            masks = img_feats.new_zeros((batch_size, num_cams, H, W))
            sin_embed = self.positional_encoding(masks)
            sin_embed = self.adapt_pos3d(sin_embed.flatten(0, 1))  # (BN, C, H, W )
            sin_embed = sin_embed.permute((0, 2, 3, 1))
            rv_pos_embeds = rv_pos_embeds + sin_embed
        return rv_pos_embeds

    def _rv_query_embed(self, ref_points, img_metas):

        pad_h, pad_w = img_metas["pad_shape"]
        ref_points = ref_points * (
            ref_points.new_tensor(self.pc_range[3:]) - ref_points.new_tensor(self.pc_range[:3])
        ) + ref_points.new_tensor(self.pc_range[:3])

        points = torch.cat([ref_points, ref_points.new_ones(*ref_points.shape[:-1], 1)], dim=-1)
        # map 3d ref points into 2d img
        sweep = 1
        batch_size, num_cams, _, _ = img_metas["lidar2imgs"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        if "bda_mat" in img_metas:
            points = img_metas["bda_mat"].inverse().view(batch_size, 1, 4, 4).matmul(points.unsqueeze(-1))
        else:
            points = points.unsqueeze(-1)
        points = points.unsqueeze(1)
        points = img_metas["lidar2imgs"].view(batch_size, num_cams, 1, 4, 4).matmul(points)
        proj_points_clone = points.clone()

        # select valid ref point on img
        z_mask = proj_points_clone[..., 2:3, :].detach() > 0
        proj_points_clone[..., :3, :] = points[..., :3, :] / (
            points[..., 2:3, :].detach() + z_mask * 1e-6 - (~z_mask) * 1e-6
        )
        proj_points_clone = img_metas["ida_mats"].view(batch_size, num_cams, 1, 4, 4).matmul(proj_points_clone)
        proj_points_clone = proj_points_clone.squeeze(-1)
        mask = (
            (proj_points_clone[..., 0] < pad_w)
            & (proj_points_clone[..., 0] >= 0)
            & (proj_points_clone[..., 1] < pad_h)
            & (proj_points_clone[..., 1] >= 0)
        )
        mask &= z_mask.view(*mask.shape)

        # map 2d ref points back to 3d with multi depth
        coords_d = (
            1 + torch.arange(self.depth_num, device=ref_points.device).float() * (self.pc_range[4] - 1) / self.depth_num
        )
        batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        projback_points = (
            img_metas["ida_mats"]
            .view(batch_size, num_cams, 1, 4, 4)
            .inverse()
            .matmul(proj_points_clone.unsqueeze(-1))
            .squeeze(-1)
        )
        projback_points = torch.einsum("bvnc, d -> bvndc", projback_points, coords_d)
        projback_points = torch.cat(
            [projback_points[..., :3], projback_points.new_ones(*projback_points.shape[:-1], 1)], dim=-1
        ).unsqueeze(-1)

        projback_points = (
            torch.inverse(img_metas["lidar2imgs"]).view(batch_size, num_cams, 1, 1, 4, 4).matmul(projback_points)
        )
        if "bda_mat" in img_metas:
            projback_points = (
                img_metas["bda_mat"].unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 4, 4)
                @ projback_points
            ).squeeze(-1)
        else:
            projback_points = projback_points.squeeze(-1)
        projback_points = (projback_points[..., :3] - projback_points.new_tensor(self.pc_range[:3])) / (
            projback_points.new_tensor(self.pc_range[3:]) - projback_points.new_tensor(self.pc_range[:3])
        )
        rv_embeds = self.rv_embedding(projback_points.reshape(*projback_points.shape[:-2], -1))
        rv_embeds = (rv_embeds * mask.unsqueeze(-1)).sum(dim=1)
        return rv_embeds

    def _bev_query_embed(self, ref_points, img_metas):
        bev_embeds = self.bev_embedding(pos2embed(ref_points, num_pos_feats=self.hidden_dim))
        return bev_embeds

    def _get_embeds(self, ref_points, radar_points, x, x_img, x_radar, img_metas, device):
        ref_points = inverse_sigmoid(ref_points.clone()).sigmoid()

        bev_pos_embeds = None  # Lidar BEV Pos Emd
        radar_bev_pos_embeds = None  # Radar BEV Pos Emd
        rv_pos_embeds = None  # Camera Pos Emd

        query_embeds = None  # Query Pos Emd
        if x is not None:
            bev_pos_embeds = self.bev_embedding(pos2embed(self.coords_bev.to(device), num_pos_feats=self.hidden_dim))
            bev_query_embeds = self._bev_query_embed(ref_points, img_metas)
            query_embeds = bev_query_embeds

        if x_radar is not None:
            radar_bev_pos_embeds = self.bev_embedding(pos2embed(radar_points, num_pos_feats=self.hidden_dim))
            radar_bev_query_embeds = self._bev_query_embed(ref_points, img_metas)
            if query_embeds is not None:
                query_embeds = query_embeds + radar_bev_query_embeds
            else:
                query_embeds = radar_bev_query_embeds

        if x_img is not None:
            if isinstance(x_img, list):  # input x_img list = [x_frontback_imgs, x_side_imgs]
                assert len(x_img) == len(img_metas)
                rv_pos_embeds, rv_query_embeds = list(), None
                for x_img_per, img_meta_per in zip(x_img, img_metas):
                    rv_pos_embeds.append(self._rv_pe(x_img_per, img_meta_per))
                    if rv_query_embeds is not None:
                        rv_query_embeds += self._rv_query_embed(ref_points, img_meta_per)
                    else:
                        rv_query_embeds = self._rv_query_embed(ref_points, img_meta_per)
            else:
                rv_pos_embeds = self._rv_pe(x_img, img_metas)
                rv_query_embeds = self._rv_query_embed(ref_points, img_metas)
            if query_embeds is not None:
                query_embeds += rv_query_embeds
            else:
                query_embeds = rv_query_embeds

        assert query_embeds is not None
        return bev_pos_embeds, rv_pos_embeds, query_embeds, radar_bev_pos_embeds

    def get_radar_pos_init(self, radar_points):
        range_x_min = self.pc_range[0]
        range_x_max = self.pc_range[3]
        range_y_min = self.pc_range[1]
        range_y_max = self.pc_range[4]
        range_z_min = self.pc_range[2]
        range_z_max = self.pc_range[5]

        radar_bev = radar_points
        bs = radar_bev.shape[0]
        for i in range(bs):
            radar_bev[i, :, 0] = (radar_bev[i, :, 0] - range_x_min) / (range_x_max - range_x_min)
            radar_bev[i, :, 1] = (radar_bev[i, :, 1] - range_y_min) / (range_y_max - range_y_min)
            radar_bev[i, :, 2] = (radar_bev[i, :, 2] - range_z_min) / (range_z_max - range_z_min)

        radar_query = torch.zeros(radar_bev.shape[0], self.init_radar_num_query, 3).cuda()
        radar_query[:, : radar_bev.shape[1], :] = radar_bev[:, :, :]
        return radar_query

    def forward_single(self, x, x_img, img_metas, x_radar, radar_points):
        """
        x: [bs c h w]
        return List(dict(head_name: [num_dec x bs x num_query * head_dim]) ) x task_num
        """
        if x is not None:
            batch_size = x.shape[0]
            device = x.device
            x = self.shared_conv(x)
        elif x_img is not None:
            batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
            device = x_img.device
        else:
            raise NotImplementedError

        ret_dicts = []

        reference_points = self.reference_points.weight
        reference_points = reference_points.unsqueeze(0).repeat(batch_size, 1, 1)

        if x_radar is not None:
            init_radar_query = self.get_radar_pos_init(radar_points)
            reference_points = torch.cat([init_radar_query, reference_points], dim=1)
        else:
            init_radar_query = None
        reference_points, attn_mask, mask_dict = self.prepare_for_dn(batch_size, reference_points, img_metas)

        bev_pos_embeds, rv_pos_embeds, query_embeds, radar_bev_pos_embeds = self._get_embeds(
            reference_points, init_radar_query, x, x_img, x_radar, img_metas, device
        )

        outs_dec, _ = self.transformer(
            x,
            x_img,
            query_embeds,
            bev_pos_embeds,
            rv_pos_embeds,
            attn_masks=attn_mask,
            x_radar=x_radar,
            radar_bev_pos_embed=radar_bev_pos_embeds,
        )
        outs_dec = torch.nan_to_num(outs_dec)
        reference = inverse_sigmoid(reference_points.clone())

        if self.repaired_timestamp:
            timestamp_dict = self.initialize_interpolation_dict(img_metas)

        flag = 0
        for task_id, task in enumerate(self.task_heads, 0):
            outs = task(outs_dec)
            center = (outs["center"] + reference[None, :, :, :2]).sigmoid()
            height = (outs["height"] + reference[None, :, :, 2:3]).sigmoid()
            _center, _height = center.new_zeros(center.shape), height.new_zeros(height.shape)
            _center[..., 0:1] = center[..., 0:1] * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            _center[..., 1:2] = center[..., 1:2] * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            _height[..., 0:1] = height[..., 0:1] * (self.pc_range[5] - self.pc_range[2]) + self.pc_range[2]
            outs["center"] = _center
            outs["height"] = _height
            _reference = center.new_zeros(center.shape)
            _reference[..., 0:1] = (
                reference[None, :, :, 0:1].sigmoid() * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            )
            _reference[..., 1:2] = (
                reference[None, :, :, 1:2].sigmoid() * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            )
            outs["reference"] = _reference

            if self.repaired_timestamp:
                pts = torch.cat([_center, _height, torch.ones_like(_height).to(_height.device)], dim=-1)
                outs = self.use_timediff_refinement(outs, pts, timestamp_dict, img_metas)

            if mask_dict and mask_dict["pad_size"] > 0:
                task_mask_dict = copy.deepcopy(mask_dict)
                class_name = self.class_names[task_id]

                known_lbs_bboxes_label = task_mask_dict["known_lbs_bboxes"][0]
                known_labels_raw = task_mask_dict["known_labels_raw"]
                new_lbs_bboxes_label = known_lbs_bboxes_label.new_zeros(known_lbs_bboxes_label.shape)
                new_lbs_bboxes_label[:] = len(class_name)
                new_labels_raw = known_labels_raw.new_zeros(known_labels_raw.shape)
                new_labels_raw[:] = len(class_name)
                task_masks = [torch.where(known_lbs_bboxes_label == class_name.index(i) + flag) for i in class_name]
                task_masks_raw = [torch.where(known_labels_raw == class_name.index(i) + flag) for i in class_name]
                for cname, task_mask, task_mask_raw in zip(class_name, task_masks, task_masks_raw):
                    new_lbs_bboxes_label[task_mask] = class_name.index(cname)
                    new_labels_raw[task_mask_raw] = class_name.index(cname)
                task_mask_dict["known_lbs_bboxes"] = (new_lbs_bboxes_label, task_mask_dict["known_lbs_bboxes"][1])
                task_mask_dict["known_labels_raw"] = new_labels_raw
                flag += len(class_name)

                for key in list(outs.keys()):
                    outs["dn_" + key] = outs[key][:, :, : mask_dict["pad_size"], :]
                    outs[key] = outs[key][:, :, mask_dict["pad_size"] :, :]
                outs["dn_mask_dict"] = task_mask_dict

            ret_dicts.append(outs)

        return ret_dicts

    def interpolate(self, y_lidar, y_camera, timediff, timediff_large):
        if y_lidar > y_camera:
            y = (y_lidar - y_camera) * (timediff_large - timediff) / timediff_large
            y += y_camera
        else:
            y = (y_camera - y_lidar) * timediff / timediff_large
            y += y_lidar
        return y

    def initialize_interpolation_dict(self, img_metas):
        timestamp_dict = {}
        timestamps = img_metas["timestamp"].squeeze(-1).cpu().numpy()
        trans = img_metas["trans"].cpu().numpy()
        quats = img_metas["rot"].cpu().numpy()

        for i in range(img_metas["timestamp"].shape[0]):
            mask = timestamps[i, ...] >= 0
            times = timestamps[i, ...][mask]
            tran = trans[i, ...][mask]
            quat = quats[i, ...][mask]
            rotations = Rotation.from_quat(quat)
            odom_translation_spline = CubicSpline(times, tran, extrapolate=False)
            odom_rotation_spline = RotationSpline(times, rotations)
            timestamp_dict[i] = {"trans": odom_translation_spline, "rot": odom_rotation_spline}

        return timestamp_dict

    def use_timediff_refinement(self, outs, pts, timestamp_dict, img_metas):
        timediff = (outs["tdiff"].sigmoid() - 0.5) * self.alpha
        timediff = timediff.mean(dim=2).mean(dim=0)
        fin_pts = []
        for i in range(pts.shape[1]):
            lidar_time = img_metas["lidar_timestamp"][i, 0].cpu().numpy()
            if timediff[i, 0] > 0:
                timediff_large = timediff.detach().cpu().numpy()[i, 0] + 0.0001
            else:
                timediff_large = timediff.detach().cpu().numpy()[i, 0] - 0.0001

            cam_time = timediff_large + lidar_time

            lidar_time = max(min(lidar_time, timestamp_dict[i]["trans"].x.max()), timestamp_dict[i]["trans"].x.min())
            cam_time = max(min(cam_time, timestamp_dict[i]["trans"].x.max()), timestamp_dict[i]["trans"].x.min())

            lidar_trans = timestamp_dict[i]["trans"](lidar_time)
            cam_trans = timestamp_dict[i]["trans"](cam_time)

            fin_trans = []
            for j in range(3):
                fin_trans.append(self.interpolate(lidar_trans[j], cam_trans[j], timediff[i, 0], timediff_large))

            fin_trans = torch.stack(fin_trans).unsqueeze(1).to(outs["tdiff"].device)
            lidar_trans = torch.tensor(lidar_trans).unsqueeze(1).to(outs["tdiff"].device)
            fin_rot = torch.tensor(timestamp_dict[i]["rot"](cam_time).as_matrix()).to(outs["tdiff"].device)
            lidar_rot = torch.tensor(timestamp_dict[i]["rot"](lidar_time).as_matrix()).to(outs["tdiff"].device)
            fin_matrix = torch.cat(
                [torch.cat([fin_rot, fin_trans], dim=-1), torch.ones((1, 4)).to(outs["tdiff"].device)], dim=0
            ).float()
            lidar_matrix = torch.cat(
                [torch.cat([lidar_rot, lidar_trans], dim=-1), torch.ones((1, 4)).to(outs["tdiff"].device)], dim=0
            ).float()
            fin_pts.append(pts[:, i, :, :].unsqueeze(-2) @ fin_matrix @ torch.linalg.inv(lidar_matrix))
        fin_pts = torch.stack(fin_pts, dim=1).squeeze(-2)
        outs["center"] = fin_pts[..., :2]
        outs["height"] = fin_pts[..., 2:3]

        return outs

    def forward(
        self,
        pts_feats,
        img_feats=None,
        img_metas=None,
        gt_boxes=None,
        radar_feats=None,
        radar_points=None,
    ):
        """
        list([bs, c, h, w])
        input: pts_feats: list
            img_feats:list
            img_metas:dict
            gt_boxes:tensor
        """
        input_info = {
            "gt_boxes": copy.deepcopy(gt_boxes),
        }
        if img_metas is None:
            img_metas = dict()
        img_metas.update(input_info)
        img_metas = [img_metas for _ in range(len(pts_feats))]
        return multi_apply(self.forward_single, pts_feats, img_feats, img_metas, [radar_feats], [radar_points])

    def _get_targets_single(
        self, gt_bboxes_3d, gt_labels_3d, pred_bboxes, pred_logits, roi_mask, query_bboxes, fov_boardline
    ):
        """ "Compute regression and classification targets for one image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:

            gt_bboxes_3d (Tensor):  LiDARInstance3DBoxes(num_gts, 9)
            gt_labels_3d (Tensor): Ground truth class indices (num_gts, )
            pred_bboxes (list[Tensor]): num_tasks x (num_query, 10)
            pred_logits (list[Tensor]): num_tasks x (num_query, task_classes)
        Returns:
            tuple[Tensor]: a tuple containing the following.
                - labels_tasks (list[Tensor]): num_tasks x (num_query, ).
                - label_weights_tasks (list[Tensor]): num_tasks x (num_query, ).
                - bbox_targets_tasks (list[Tensor]): num_tasks x (num_query, 9).
                - bbox_weights_tasks (list[Tensor]): num_tasks x (num_query, 10).
                - pos_inds (list[Tensor]): num_tasks x Sampled positive indices.
                - neg_inds (Tensor): num_tasks x Sampled negative indices.
        """
        device = gt_labels_3d.device
        task_masks = []
        flag = 0
        for class_name in self.class_names:
            task_masks.append([torch.where(gt_labels_3d == class_name.index(i) + flag) for i in class_name])
            flag += len(class_name)

        task_boxes = []
        task_classes = []
        task_roi_mask = []
        task_fov_boardline = []
        flag2 = 0
        for idx, mask in enumerate(task_masks):
            task_box = []
            task_class = []
            for m in mask:
                task_box.append(gt_bboxes_3d[m])
                task_class.append(gt_labels_3d[m] - flag2)
            task_boxes.append(torch.cat(task_box, dim=0).to(device))
            task_classes.append(torch.cat(task_class).long().to(device))
            task_roi_mask.append(roi_mask)
            task_fov_boardline.append(fov_boardline)
            flag2 += len(mask)

        def task_assign(
            bbox_pred, logits_pred, gt_bboxes, gt_labels, num_classes, roi_mask, query_bboxes, fov_boardline
        ):
            num_bboxes = bbox_pred.shape[0]
            if self.use_roi_mask:
                assign_results = self.assigner.assign(
                    bbox_pred,
                    logits_pred,
                    gt_bboxes,
                    gt_labels,
                    roi_mask=roi_mask,
                    query_bboxes=query_bboxes,
                    fov_boardline=fov_boardline,
                )
            else:
                assign_results = self.assigner.assign(
                    bbox_pred, logits_pred, gt_bboxes, gt_labels, roi_mask=None, query_bboxes=None, fov_boardline=None
                )
            sampling_result = self.sampler.sample(assign_results, bbox_pred, gt_bboxes)
            pos_inds, neg_inds = sampling_result.pos_inds, sampling_result.neg_inds
            # label targets
            labels = gt_bboxes.new_full((num_bboxes,), num_classes, dtype=torch.long)
            labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds]
            if self.use_roi_mask:
                label_weights = gt_bboxes.new_zeros(num_bboxes)  # num_query
                label_weights[pos_inds] = 1
                label_weights[neg_inds] = 1
            else:
                label_weights = gt_bboxes.new_ones(num_bboxes)
            # bbox_targets
            code_size = gt_bboxes.shape[1]
            bbox_targets = torch.zeros_like(bbox_pred)[..., :code_size]
            bbox_weights = torch.zeros_like(bbox_pred)
            bbox_weights[pos_inds] = 1.0

            if len(sampling_result.pos_gt_bboxes) > 0:
                bbox_targets[pos_inds] = sampling_result.pos_gt_bboxes
            return labels, label_weights, bbox_targets, bbox_weights, pos_inds, neg_inds

        (
            labels_tasks,
            labels_weights_tasks,
            bbox_targets_tasks,
            bbox_weights_tasks,
            pos_inds_tasks,
            neg_inds_tasks,
        ) = multi_apply(
            task_assign,
            pred_bboxes,
            pred_logits,
            task_boxes,
            task_classes,
            self.num_classes,
            task_roi_mask,
            query_bboxes,
            task_fov_boardline,
        )

        return (
            labels_tasks,
            labels_weights_tasks,
            bbox_targets_tasks,
            bbox_weights_tasks,
            pos_inds_tasks,
            neg_inds_tasks,
        )

    def get_targets(
        self, gt_bboxes_3d, gt_labels_3d, preds_bboxes, preds_logits, roi_mask, query_bboxes, fov_boardline
    ):
        """ "Compute regression and classification targets for a batch image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            gt_bboxes_3d (list[LiDARInstance3DBoxes]): batch_size * (num_gts, 9)
            gt_labels_3d (list[Tensor]): Ground truth class indices. batch_size * (num_gts, )
            pred_bboxes (list[list[Tensor]]): batch_size x num_task x [num_query, 10].
            pred_logits (list[list[Tensor]]): batch_size x num_task x [num_query, task_classes]
        Returns:
            tuple: a tuple containing the following targets.
                - task_labels_list (list(list[Tensor])): num_tasks x batch_size x (num_query, ).
                - task_labels_weight_list (list[Tensor]): num_tasks x batch_size x (num_query, )
                - task_bbox_targets_list (list[Tensor]): num_tasks x batch_size x (num_query, 9)
                - task_bbox_weights_list (list[Tensor]): num_tasks x batch_size x (num_query, 10)
                - num_total_pos_tasks (list[int]): num_tasks x Number of positive samples
                - num_total_neg_tasks (list[int]): num_tasks x Number of negative samples.
        """
        if fov_boardline is None:
            fov_boardline = [None for _ in range(len(roi_mask))]
        # gt_labels_3d = [ gt - 1 for gt in gt_labels_3d]
        (
            labels_list,
            labels_weight_list,
            bbox_targets_list,
            bbox_weights_list,
            pos_inds_list,
            neg_inds_list,
        ) = multi_apply(
            self._get_targets_single,
            gt_bboxes_3d,
            gt_labels_3d,
            preds_bboxes,
            preds_logits,
            roi_mask,
            query_bboxes,
            fov_boardline,
        )
        task_num = len(labels_list[0])
        num_total_pos_tasks, num_total_neg_tasks = [], []
        task_labels_list, task_labels_weight_list, task_bbox_targets_list, task_bbox_weights_list = [], [], [], []

        for task_id in range(task_num):
            num_total_pos_task = sum((inds[task_id].numel() for inds in pos_inds_list))
            num_total_neg_task = sum((inds[task_id].numel() for inds in neg_inds_list))
            num_total_pos_tasks.append(num_total_pos_task)
            num_total_neg_tasks.append(num_total_neg_task)
            task_labels_list.append([labels_list[batch_idx][task_id] for batch_idx in range(len(gt_bboxes_3d))])
            task_labels_weight_list.append(
                [labels_weight_list[batch_idx][task_id] for batch_idx in range(len(gt_bboxes_3d))]
            )
            task_bbox_targets_list.append(
                [bbox_targets_list[batch_idx][task_id] for batch_idx in range(len(gt_bboxes_3d))]
            )
            task_bbox_weights_list.append(
                [bbox_weights_list[batch_idx][task_id] for batch_idx in range(len(gt_bboxes_3d))]
            )

        return (
            task_labels_list,
            task_labels_weight_list,
            task_bbox_targets_list,
            task_bbox_weights_list,
            num_total_pos_tasks,
            num_total_neg_tasks,
        )

    def _loss_single_task(
        self,
        pred_bboxes,
        pred_logits,
        labels_list,
        labels_weights_list,
        bbox_targets_list,
        bbox_weights_list,
        num_total_pos,
        num_total_neg,
    ):
        """ "Compute loss for single task.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            pred_bboxes (Tensor): (batch_size, num_query, 10)
            pred_logits (Tensor): (batch_size, num_query, task_classes)
            labels_list (list[Tensor]): batch_size x (num_query, )
            labels_weights_list (list[Tensor]): batch_size x (num_query, )
            bbox_targets_list(list[Tensor]): batch_size x (num_query, 9)
            bbox_weights_list(list[Tensor]): batch_size x (num_query, 10)
            num_total_pos: int
            num_total_neg: int
        Returns:
            loss_cls
            loss_bbox
        """
        labels = torch.cat(labels_list, dim=0)
        labels_weights = torch.cat(labels_weights_list, dim=0)
        bbox_targets = torch.cat(bbox_targets_list, dim=0)
        bbox_weights = torch.cat(bbox_weights_list, dim=0)

        pred_bboxes_flatten = pred_bboxes.flatten(0, 1)
        pred_logits_flatten = pred_logits.flatten(0, 1)

        cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.bg_cls_weight
        cls_avg_factor = max(cls_avg_factor, 1)

        loss_cls = self.loss_cls(pred_logits_flatten, labels, labels_weights, avg_factor=cls_avg_factor)

        normalized_bbox_targets = normalize_bbox(bbox_targets, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        bbox_weights = bbox_weights * bbox_weights.new_tensor(self.train_cfg.code_weights)[None, :]

        loss_bbox = self.loss_bbox(
            pred_bboxes_flatten[isnotnan, :10],
            normalized_bbox_targets[isnotnan, :10],
            bbox_weights[isnotnan, :10],
            avg_factor=num_total_pos,
        )

        loss_cls = torch.nan_to_num(loss_cls)
        loss_bbox = torch.nan_to_num(loss_bbox)
        return loss_cls, loss_bbox

    def loss_single(
        self, pred_bboxes, pred_logits, gt_bboxes_3d, gt_labels_3d, roi_mask, query_bboxes, fov_boardline=None
    ):
        """ "Loss function for outputs from a single decoder layer of a single
        feature level.
        Args:
            pred_bboxes (list[Tensor]): num_tasks x [bs, num_query, 10].
            pred_logits (list(Tensor]): num_tasks x [bs, num_query, task_classes]
            gt_bboxes_3d (list[tensor]): batch_size * (num_gts, 9)
            gt_labels_list (list[Tensor]): Ground truth class indices. batch_size * (num_gts, )
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """
        batch_size = pred_bboxes[0].shape[0]
        pred_bboxes_list, pred_logits_list = [], []
        query_bboxes_list = []
        for idx in range(batch_size):
            pred_bboxes_list.append([task_pred_bbox[idx] for task_pred_bbox in pred_bboxes])
            pred_logits_list.append([task_pred_logits[idx] for task_pred_logits in pred_logits])
            query_bboxes_list.append([task_query_bbox[idx] for task_query_bbox in query_bboxes])
        cls_reg_targets = self.get_targets(
            gt_bboxes_3d, gt_labels_3d, pred_bboxes_list, pred_logits_list, roi_mask, query_bboxes_list, fov_boardline
        )
        (
            labels_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            num_total_pos,
            num_total_neg,
        ) = cls_reg_targets
        loss_cls_tasks, loss_bbox_tasks = multi_apply(
            self._loss_single_task,
            pred_bboxes,
            pred_logits,
            labels_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            num_total_pos,
            num_total_neg,
        )

        return sum(loss_cls_tasks), sum(loss_bbox_tasks)

    def _dn_loss_single_task(self, pred_bboxes, pred_logits, mask_dict):
        if mask_dict is not None:
            known_labels, known_bboxs = mask_dict["known_lbs_bboxes"]
            map_known_indice = mask_dict["map_known_indice"].long()
            known_indice = mask_dict["known_indice"].long()
            batch_idx = mask_dict["batch_idx"].long()
            bid = batch_idx[known_indice]
            known_labels_raw = mask_dict["known_labels_raw"]
            # trans openpcdet sytle to mmdet style
            # known_labels -= 1
            pred_logits = pred_logits[(bid, map_known_indice)]  # 1x12x9
            pred_bboxes = pred_bboxes[(bid, map_known_indice)]  # 1x12x10
            num_tgt = known_indice.numel()

            # filter task bbox
            task_mask = known_labels_raw != pred_logits.shape[-1]
            task_mask_sum = task_mask.sum()

            if task_mask_sum > 0:
                # pred_logits = pred_logits[task_mask]
                # known_labels = known_labels[task_mask]
                pred_bboxes = pred_bboxes[task_mask]
                known_bboxs = known_bboxs[task_mask]

            # classification loss
            # construct weighted avg_factor to match with the official DETR repo
            cls_avg_factor = num_tgt * 3.14159 / 6 * self.split * self.split * self.split

            label_weights = torch.ones_like(known_labels)
            cls_avg_factor = max(cls_avg_factor, 1)

        else:
            # pred_logits 12x9
            # known_labels 12
            # label_weights 12
            # cls_avg_factor num

            cls_avg_factor = 1
            pred_logits = pred_logits[:, :10].flatten(0, 1)
            pred_bboxes = pred_bboxes[:, :10].flatten(0, 1)
            known_labels = torch.ones(len(pred_logits), device="cuda").long() * -1
            known_bboxs = torch.ones_like(pred_bboxes, device="cuda")
            label_weights = torch.zeros_like(known_labels, device="cuda")
            num_tgt = 1
            task_mask_sum = 0
        loss_cls = self.loss_cls(pred_logits, known_labels.long(), label_weights, avg_factor=cls_avg_factor)

        # Compute the average number of gt boxes accross all gpus, for
        # normalization purposes
        num_tgt = loss_cls.new_tensor([num_tgt])
        num_tgt = torch.clamp(reduce_mean(num_tgt), min=1).item()

        # regression L1 loss
        normalized_bbox_targets = normalize_bbox(known_bboxs, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        if mask_dict is not None:
            bbox_weights = torch.ones_like(pred_bboxes)
        else:
            bbox_weights = torch.zeros_like(pred_bboxes)
        bbox_weights = bbox_weights * bbox_weights.new_tensor(self.train_cfg.code_weights)[None, :]
        # bbox_weights[:, 6:8] = 0
        # vel is dummy input for private
        if self.test_cfg["dataset"] == "Private":
            bbox_weights[:, 8:10] = 0

        loss_bbox = self.loss_bbox(
            pred_bboxes[isnotnan, :10],
            normalized_bbox_targets[isnotnan, :10],
            bbox_weights[isnotnan, :10],
            avg_factor=num_tgt,
        )

        loss_cls = torch.nan_to_num(loss_cls)
        loss_bbox = torch.nan_to_num(loss_bbox)

        if task_mask_sum == 0:
            # loss_cls = loss_cls * 0.0
            loss_bbox = loss_bbox * 0.0

        return self.dn_weight * loss_cls, self.dn_weight * loss_bbox

    def dn_loss_single(self, pred_bboxes, pred_logits, dn_mask_dict):
        loss_cls_tasks, loss_bbox_tasks = multi_apply(self._dn_loss_single_task, pred_bboxes, pred_logits, dn_mask_dict)
        return sum(loss_cls_tasks), sum(loss_bbox_tasks)

    @force_fp32(apply_to=("preds_dicts"))
    def loss(self, gt_bboxes_3d, preds_dicts, roi_mask, **kwargs):
        """ "Loss function.
        Args:
            gt_bboxes_3d (tensor): (batch_size, num_gts, 10). gt_boxes are padding with 0 to align.
                                gt_bboxes_3d[:,:,9] are the gt_labels
            preds_dicts(tuple[list[dict]]): nb_tasks x num_lvl
                center: (num_dec, batch_size, num_query, 2)
                height: (num_dec, batch_size, num_query, 1)
                dim: (num_dec, batch_size, num_query, 3)
                rot: (num_dec, batch_size, num_query, 2)
                vel: (num_dec, batch_size, num_query, 2)
                cls_logits: (num_dec, batch_size, num_query, task_classes)
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """

        batch_size = gt_bboxes_3d.shape[0]
        mask = torch.any(gt_bboxes_3d[..., :9], dim=2)
        gt_boxes_3d_list = [gt_bboxes_3d[i, mask[i, :], :9] for i in range(batch_size)]
        gt_labels_3d_list = [gt_bboxes_3d[i, mask[i, :], 9] - 1 for i in range(batch_size)]

        num_decoder = preds_dicts[0][0]["center"].shape[0]
        all_pred_bboxes, all_pred_logits = collections.defaultdict(list), collections.defaultdict(list)
        all_query_bboxes = collections.defaultdict(list)
        for task_id, preds_dict in enumerate(preds_dicts, 0):
            for dec_id in range(num_decoder):
                pred_bbox = torch.cat(
                    (
                        preds_dict[0]["center"][dec_id],
                        preds_dict[0]["height"][dec_id],
                        preds_dict[0]["dim"][dec_id],
                        preds_dict[0]["rot"][dec_id],
                        preds_dict[0]["vel"][dec_id],
                    ),
                    dim=-1,
                )
                all_pred_bboxes[dec_id].append(pred_bbox)
                all_pred_logits[dec_id].append(preds_dict[0]["cls_logits"][dec_id])
                all_query_bboxes[dec_id].append(preds_dict[0]["reference"][dec_id])
        all_pred_bboxes = [all_pred_bboxes[idx] for idx in range(num_decoder)]
        all_pred_logits = [all_pred_logits[idx] for idx in range(num_decoder)]
        all_query_bboxes = [all_query_bboxes[idx] for idx in range(num_decoder)]
        loss_cls, loss_bbox = multi_apply(
            self.loss_single,
            all_pred_bboxes,
            all_pred_logits,
            [gt_boxes_3d_list for _ in range(num_decoder)],
            [gt_labels_3d_list for _ in range(num_decoder)],
            [roi_mask for _ in range(num_decoder)],
            all_query_bboxes,
        )

        loss_dict = dict()
        loss_dict["loss_cls"] = loss_cls[-1]
        loss_dict["loss_bbox"] = loss_bbox[-1]

        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(loss_cls[:-1], loss_bbox[:-1]):
            loss_dict[f"d{num_dec_layer}.loss_cls"] = loss_cls_i
            loss_dict[f"d{num_dec_layer}.loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        dn_pred_bboxes, dn_pred_logits = collections.defaultdict(list), collections.defaultdict(list)
        dn_mask_dicts = collections.defaultdict(list)
        for task_id, preds_dict in enumerate(preds_dicts, 0):
            for dec_id in range(num_decoder):
                pred_bbox = torch.cat(
                    (
                        preds_dict[0]["dn_center"][dec_id],
                        preds_dict[0]["dn_height"][dec_id],
                        preds_dict[0]["dn_dim"][dec_id],
                        preds_dict[0]["dn_rot"][dec_id],
                        preds_dict[0]["dn_vel"][dec_id],
                    ),
                    dim=-1,
                )
                dn_pred_bboxes[dec_id].append(pred_bbox)
                dn_pred_logits[dec_id].append(preds_dict[0]["dn_cls_logits"][dec_id])
                dn_mask_dicts[dec_id].append(preds_dict[0]["dn_mask_dict"])
        dn_pred_bboxes = [dn_pred_bboxes[idx] for idx in range(num_decoder)]
        dn_pred_logits = [dn_pred_logits[idx] for idx in range(num_decoder)]
        dn_mask_dicts = [dn_mask_dicts[idx] for idx in range(num_decoder)]
        dn_loss_cls, dn_loss_bbox = multi_apply(self.dn_loss_single, dn_pred_bboxes, dn_pred_logits, dn_mask_dicts)

        loss_dict["dn_loss_cls"] = dn_loss_cls[-1]
        loss_dict["dn_loss_bbox"] = dn_loss_bbox[-1]
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(dn_loss_cls[:-1], dn_loss_bbox[:-1]):
            loss_dict[f"d{num_dec_layer}.dn_loss_cls"] = loss_cls_i
            loss_dict[f"d{num_dec_layer}.dn_loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        return loss_dict

    @force_fp32(apply_to=("preds_dicts"))
    def get_bboxes(self, preds_dicts, img_metas, img=None, rescale=False):
        preds_dicts = self.bbox_coder.decode(preds_dicts)
        num_samples = len(preds_dicts)

        ret_list = []
        for i in range(num_samples):
            preds = preds_dicts[i]
            bboxes = preds["bboxes"]
            bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 5] * 0.5
            bboxes = img_metas[i]["box_type_3d"](bboxes, bboxes.size(-1))
            scores = preds["scores"]
            labels = preds["labels"]
            ret_list.append([bboxes, scores, labels])
        return ret_list


class CMTE2EHead(CMTFusionHead):
    def __init__(
        self,
        use_dn=False,
        lidar_use_query_mlp=True,
        **kwargs,
    ):
        super(CMTE2EHead, self).__init__(**kwargs)
        self.use_dn = use_dn
        # assert "Lidar" not in kwargs["modal"], f"Not support Lidar, but given modal is {kwargs['modal']}"
        if "Radar" in kwargs["modal"]:
            self.bev_embedding = nn.Sequential(
                nn.Linear(384, kwargs["hidden_dim"]),
                nn.ReLU(inplace=True),
                nn.Linear(kwargs["hidden_dim"], kwargs["hidden_dim"]),
            )
        if "Lidar" in kwargs["modal"]:

            self.shared_conv = ConvModule(
                kwargs["hidden_dim"] * 3 // 2,
                kwargs["hidden_dim"],
                kernel_size=3,
                stride=1,  # 下采样x2
                padding=1,
                conv_cfg=dict(type="Conv2d"),
                norm_cfg=dict(type="BN2d"),
            )
            self.bev_embedding = nn.Sequential(
                nn.Linear(kwargs["hidden_dim"] * 3 // 2, kwargs["hidden_dim"]),
                nn.ReLU(inplace=True),
                nn.Linear(kwargs["hidden_dim"], kwargs["hidden_dim"]),
            )
            self.front_lidar_grid_size = kwargs.get("front_lidar_grid_size", None)
        self.lidar_use_query_mlp = lidar_use_query_mlp

    def init_weights(self):
        super(CMTFusionHead, self).init_weights()
        if self.loss_cls.use_sigmoid:
            bias_init = bias_init_with_prob(0.01)
            for i in range(len(self.task_heads)):
                nn.init.constant_(self.task_heads[i].cls_logits[-1].bias, bias_init)

    # 提取前视lidar 的bev, 只有lidar 会用，但需要对应到整个空间
    @property
    def coords_bev(self):
        # feature map size: [H, W]
        # lidar bev coord: [x, y]
        # H→y, W→x
        x_size, y_size = (
            int(self.grid_size[0] // self.downsample_scale),
            int(self.grid_size[1] // self.downsample_scale),
        )
        if self.front_lidar_grid_size is not None:
            front_lidar_y_start = y_size - int(self.front_lidar_grid_size[1] // self.downsample_scale)
            meshgrid = [
                [front_lidar_y_start, y_size - 1, int(self.front_lidar_grid_size[1] // self.downsample_scale)],
                [0, x_size - 1, x_size],
            ]
        else:
            meshgrid = [[0, y_size - 1, y_size], [0, x_size - 1, x_size]]
        batch_y, batch_x = torch.meshgrid(*[torch.linspace(it[0], it[1], it[2]) for it in meshgrid])
        batch_x = (batch_x + 0.5) / x_size
        batch_y = (batch_y + 0.5) / y_size
        coord_base = torch.cat([batch_x[None], batch_y[None]], dim=0)
        coord_base = coord_base.view(2, -1).transpose(1, 0)  # (H*W, 2)
        return coord_base

    def _get_embeds(self, ref_points, radar_points, x_lidar, x_img, x_radar, img_metas, device, query_embedding):
        """计算radar/lidar bev PE与camera PE"""
        radar_bev_pos_embeds = None  # Radar BEV Pos Emd
        rv_pos_embeds = None  # Camera Pos Emd
        lidar_bev_pos_embeds = None
        if x_img is not None:
            if isinstance(x_img, list):  # input x_img list = [x_frontback_imgs, x_side_imgs]
                assert len(x_img) == len(img_metas)
                rv_pos_embeds = list()
                for x_img_per, img_meta_per in zip(x_img, img_metas):
                    rv_pos_embeds.append(self._rv_pe(x_img_per, img_meta_per))
            else:
                rv_pos_embeds = self._rv_pe(x_img, img_metas)

        if x_radar is not None:

            # radar_bev_pos_embeds = self.bev_embedding(pos2posemb3d(radar_points, num_pos_feats=self.hidden_dim // 2))
            radar_bev_pos_embeds = self.bev_embedding(
                pos2posemb3d(radar_points)
            )  # [ -6.6795,  11.3906,   0.5000] 坐标系不需要转换？
        if x_lidar is not None:
            # lidar_bev_pos_embeds = self.lidar_bev_embedding(pos2embed(self.coords_bev.to(device)))
            if self.lidar_use_query_mlp:
                # lidar_bev_pos_embeds = self.lidar_bev_embedding(pos2posemb3d(torch.cat((self.coords_bev,torch.ones(len(self.coords_bev),1)*0.5),-1).to(device)))
                lidar_bev_pos_embeds = query_embedding(
                    pos2posemb3d(torch.cat((self.coords_bev, torch.ones(len(self.coords_bev), 1) * 0.5), -1).to(device))
                )
            else:
                lidar_bev_pos_embeds = self.bev_embedding(
                    pos2posemb3d(torch.cat((self.coords_bev, torch.ones(len(self.coords_bev), 1) * 0.5), -1).to(device))
                )
            # lidar_bev_pos_embeds = query_embedding(pos2embed(self.coords_bev.to(device)))
        return rv_pos_embeds, radar_bev_pos_embeds, lidar_bev_pos_embeds  # , query_embeds

    def forward(
        self,
        img_feats,
        img_metas,
        query_feats,
        query_embeds,
        reference_points,
        attn_mask=None,
        mask_dict=None,
        radar_points=None,
        x_radar=None,
        lidar_feats=None,
        query_embedding=None,
        query_padding_masks=None,
    ):
        """
        x: [bs c h w]
        reference_points: [num_query,3] / [bs, num_query+dn_query, 3]
        return List(dict(head_name: [num_dec x bs x num_query * head_dim]) ) x task_num
        """
        # batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        batch_size = len(img_metas["ida_mats"])
        device = img_feats.device if img_feats is not None else lidar_feats.device
        if lidar_feats is not None:
            lidar_feats = self.shared_conv(lidar_feats)

        ret_dicts = []
        if reference_points.ndim == 2:
            reference_points = reference_points.unsqueeze(0).repeat(batch_size, 1, 1)
        elif reference_points.ndim != 3:
            raise ValueError(f"unexcepted dim for reference points: {reference_points.shape}")

        rv_pos_embeds, radar_bev_pos_embeds, lidar_bev_pos_embeds = self._get_embeds(
            reference_points, radar_points, lidar_feats, img_feats, x_radar, img_metas, device, query_embedding
        )

        outs_dec, _ = self.transformer(
            lidar_feats,
            img_feats,
            query_embeds,
            lidar_bev_pos_embeds,
            rv_pos_embeds,
            attn_masks=attn_mask,
            x_radar=x_radar,
            radar_bev_pos_embed=radar_bev_pos_embeds,
            target=query_feats,
            query_key_padding_mask=query_padding_masks,
        )

        outs_dec = torch.nan_to_num(outs_dec)

        reference = inverse_sigmoid(reference_points.clone())

        flag = 0
        for task_id, task in enumerate(self.task_heads, 0):
            outs = task(outs_dec)
            center = (outs["center"] + reference[None, :, :, :2]).sigmoid()
            height = (outs["dim"][..., 1:2] + reference[None, :, :, 2:3]).sigmoid()  # 对应第4:5维
            _center, _height = center.new_zeros(center.shape), height.new_zeros(height.shape)
            last_reference_points = torch.cat((center[-1], height[-1]), dim=-1)
            _center[..., 0:1] = center[..., 0:1] * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            _center[..., 1:2] = center[..., 1:2] * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            _height[..., 0:1] = height[..., 0:1] * (self.pc_range[5] - self.pc_range[2]) + self.pc_range[2]
            outs["center"] = _center
            outs["dim"][..., 1:2] = _height

            _reference = center.new_zeros(center.shape)
            _reference[..., 0:1] = (
                reference[None, :, :, 0:1].sigmoid() * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            )
            _reference[..., 1:2] = (
                reference[None, :, :, 1:2].sigmoid() * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            )
            # roi_mask
            outs["reference"] = _reference

            all_bbox_preds = torch.cat(
                (  # look like cx, cy, cz, w, l, h, rot, but is cx, cy, w, l, cz, h, rot
                    outs["center"],  # preds_dicts[0]: only support task_num = 0
                    outs["height"],
                    outs["dim"],
                    outs["rot"],
                    outs["vel"],
                ),
                dim=-1,
            ).to(outs["center"].dtype)

            if mask_dict and mask_dict["pad_size"] > 0:
                task_mask_dict = copy.deepcopy(mask_dict)
                class_name = self.class_names[task_id]

                known_lbs_bboxes_label = task_mask_dict["known_lbs_bboxes"][0]
                known_labels_raw = task_mask_dict["known_labels_raw"]
                new_lbs_bboxes_label = known_lbs_bboxes_label.new_zeros(known_lbs_bboxes_label.shape)
                new_lbs_bboxes_label[:] = len(class_name)
                new_labels_raw = known_labels_raw.new_zeros(known_labels_raw.shape)
                new_labels_raw[:] = len(class_name)
                task_masks = [torch.where(known_lbs_bboxes_label == class_name.index(i) + flag) for i in class_name]
                task_masks_raw = [torch.where(known_labels_raw == class_name.index(i) + flag) for i in class_name]
                for cname, task_mask, task_mask_raw in zip(class_name, task_masks, task_masks_raw):
                    new_lbs_bboxes_label[task_mask] = class_name.index(cname)
                    new_labels_raw[task_mask_raw] = class_name.index(cname)
                task_mask_dict["known_lbs_bboxes"] = (new_lbs_bboxes_label, task_mask_dict["known_lbs_bboxes"][1])
                task_mask_dict["known_labels_raw"] = new_labels_raw
                flag += len(class_name)

                for key in list(outs.keys()):
                    outs["dn_" + key] = outs[key][:, :, : mask_dict["pad_size"], :]
                    outs[key] = outs[key][:, :, mask_dict["pad_size"] :, :]
                outs["dn_mask_dict"] = task_mask_dict

            outs.update(
                {
                    "all_cls_scores": outs["cls_logits"].to(outs["center"].dtype),
                    "all_bbox_preds": all_bbox_preds,
                    "enc_cls_scores": None,
                    "enc_bbox_preds": None,
                    "query_feats": outs_dec[-1],
                    "reference_points": last_reference_points,
                }
            )

            ret_dicts.append(outs)

        return ret_dicts

    @force_fp32(apply_to=("preds_dicts"))
    def loss(self, gt_bboxes_3d, preds_dicts, roi_mask, fov_boardline=None, **kwargs):
        """ "Loss function.
        Args:
            gt_bboxes_3d (dict): (batch_size, num_gts, 10).
                                ff_gt_bboxes_list: gt_boxes
                                ff_gt_labels_list: gt_labels.
                                    same as CMTFusionHead, ff_gt_labels_list starts from 1
            preds_dicts(tuple[list[dict]]): nb_tasks x num_lvl
                center: (num_dec, batch_size, num_query, 2)
                height: (num_dec, batch_size, num_query, 1)
                dim: (num_dec, batch_size, num_query, 3)
                rot: (num_dec, batch_size, num_query, 2)
                vel: (num_dec, batch_size, num_query, 2)
                cls_logits: (num_dec, batch_size, num_query, task_classes)
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """

        ff_gt_bboxes_list = gt_bboxes_3d["ff_gt_bboxes_list"]
        ff_gt_labels_list = gt_bboxes_3d["ff_gt_labels_list"]
        batch_size = len(ff_gt_bboxes_list)

        gt_boxes_3d_list = [ff_gt_bboxes_list[i] for i in range(batch_size)]
        gt_labels_3d_list = [ff_gt_labels_list[i] - 1 for i in range(batch_size)]

        num_decoder = preds_dicts[0]["center"].shape[0]
        all_pred_bboxes, all_pred_logits, all_query_bboxes = (
            collections.defaultdict(list),
            collections.defaultdict(list),
            collections.defaultdict(list),
        )

        for dec_id in range(num_decoder):
            pred_bbox = torch.cat(
                (  # look like cx, cy, cz, w, l, h, rot, but is cx, cy, w, l, cz, h, rot
                    preds_dicts[0]["center"][dec_id],  # preds_dicts[0]: only support task_num = 0
                    preds_dicts[0]["height"][dec_id],
                    preds_dicts[0]["dim"][dec_id],
                    preds_dicts[0]["rot"][dec_id],
                    preds_dicts[0]["vel"][dec_id],
                ),
                dim=-1,
            )
            all_pred_bboxes[dec_id].append(pred_bbox)
            all_pred_logits[dec_id].append(preds_dicts[0]["cls_logits"][dec_id])
            all_query_bboxes[dec_id].append(preds_dicts[0]["reference"][dec_id])
        all_pred_bboxes = [all_pred_bboxes[idx] for idx in range(num_decoder)]
        all_pred_logits = [all_pred_logits[idx] for idx in range(num_decoder)]
        all_query_bboxes = [all_query_bboxes[idx] for idx in range(num_decoder)]

        loss_cls, loss_bbox = multi_apply(
            self.loss_single,
            all_pred_bboxes,
            all_pred_logits,
            [gt_boxes_3d_list for _ in range(num_decoder)],
            [gt_labels_3d_list for _ in range(num_decoder)],
            [roi_mask for _ in range(num_decoder)],
            all_query_bboxes,
            [fov_boardline for _ in range(num_decoder)],
        )

        loss_dict = dict()
        loss_dict["loss_cls"] = loss_cls[-1]
        loss_dict["loss_bbox"] = loss_bbox[-1]

        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(loss_cls[:-1], loss_bbox[:-1]):
            loss_dict[f"d{num_dec_layer}.loss_cls"] = loss_cls_i
            loss_dict[f"d{num_dec_layer}.loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        if self.use_dn:  # and "dn_mask_dict" in preds_dicts[0]:
            dn_pred_bboxes, dn_pred_logits = collections.defaultdict(list), collections.defaultdict(list)
            dn_mask_dicts = collections.defaultdict(list)
            for dec_id in range(num_decoder):
                if "dn_mask_dict" in preds_dicts[0]:
                    pred_bbox = torch.cat(
                        (
                            preds_dicts[0]["dn_center"][dec_id],
                            preds_dicts[0]["dn_height"][dec_id],
                            preds_dicts[0]["dn_dim"][dec_id],
                            preds_dicts[0]["dn_rot"][dec_id],
                            preds_dicts[0]["dn_vel"][dec_id],
                        ),
                        dim=-1,
                    )
                    dn_pred_bboxes[dec_id].append(pred_bbox)
                    dn_pred_logits[dec_id].append(preds_dicts[0]["dn_cls_logits"][dec_id])

                    dn_mask_dicts[dec_id].append(preds_dicts[0]["dn_mask_dict"])
                else:
                    # 用来传参
                    pred_bbox = torch.cat(
                        (  # look like cx, cy, cz, w, l, h, rot, but is cx, cy, w, l, cz, h, rot
                            preds_dicts[0]["center"][dec_id],  # preds_dicts[0]: only support task_num = 0
                            preds_dicts[0]["height"][dec_id],
                            preds_dicts[0]["dim"][dec_id],
                            preds_dicts[0]["rot"][dec_id],
                            preds_dicts[0]["vel"][dec_id],
                        ),
                        dim=-1,
                    )
                    dn_pred_bboxes[dec_id].append(pred_bbox)
                    dn_pred_logits[dec_id].append(preds_dicts[0]["cls_logits"][dec_id])
                    dn_mask_dicts[dec_id].append(None)

            dn_pred_bboxes = [dn_pred_bboxes[idx] for idx in range(num_decoder)]
            dn_pred_logits = [dn_pred_logits[idx] for idx in range(num_decoder)]
            dn_mask_dicts = [dn_mask_dicts[idx] for idx in range(num_decoder)]

            dn_loss_cls, dn_loss_bbox = multi_apply(self.dn_loss_single, dn_pred_bboxes, dn_pred_logits, dn_mask_dicts)

            loss_dict["dn_loss_cls"] = dn_loss_cls[-1]
            loss_dict["dn_loss_bbox"] = dn_loss_bbox[-1]
            num_dec_layer = 0
            for loss_cls_i, loss_bbox_i in zip(dn_loss_cls[:-1], dn_loss_bbox[:-1]):
                loss_dict[f"d{num_dec_layer}.dn_loss_cls"] = loss_cls_i
                loss_dict[f"d{num_dec_layer}.dn_loss_bbox"] = loss_bbox_i
                num_dec_layer += 1

        return loss_dict

    @force_fp32(apply_to=("preds_dicts"))
    def get_e2e_bboxes(self, preds_dicts, img_metas, img=None, rescale=False):
        preds_dicts = self.bbox_coder.decode(preds_dicts)
        num_samples = len(preds_dicts)

        ret_list = []
        for i in range(num_samples):
            preds = preds_dicts[i]
            bboxes = preds["bboxes"]
            bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 5] * 0.5
            bboxes = img_metas["box_type_3d"][i](bboxes, bboxes.size(-1))
            scores = preds["scores"]
            labels = preds["labels"]
            obj_idxes = preds["obj_idxes"]
            track_scores = preds["track_scores"]
            forecasting = preds["forecasting"]
            velocity = preds["velocity"]
            if velocity is not None:
                ret_list.append([bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity])
            else:
                ret_list.append([bboxes, scores, labels, obj_idxes, track_scores, forecasting])
        return ret_list


class CenterCMTFusionHead(CMTFusionHead):
    """
    Remove ClsEmb
    Only init ref point for a part of query, instead of all
    """

    def __init__(
        self,
        num_lidar_init_query=200,
        nms_kernel_size=3,
        **kwargs,
    ):
        super(CenterCMTFusionHead, self).__init__(**kwargs)
        self.nms_kernel_size = nms_kernel_size
        self.num_lidar_init_query = num_lidar_init_query
        assert self.num_lidar_init_query < self.num_query, "number of init query must less than number of query"
        self.reference_points = nn.Embedding(self.num_query - self.num_lidar_init_query, 3)
        # centerhead
        layers = []
        layers.append(
            ConvModule(
                self.hidden_dim,
                self.hidden_dim,
                kernel_size=3,
                padding=1,
                bias="auto",
                conv_cfg=dict(type="Conv2d"),
                norm_cfg=dict(type="BN2d"),
            )
        )
        layers.append(
            build_conv_layer(
                dict(type="Conv2d"),
                self.hidden_dim,
                sum(self.num_classes),
                kernel_size=3,
                padding=1,
                bias="auto",
            )
        )
        self.heatmap_head = nn.Sequential(*layers)

    def get_lidar_pos_init(self, x, bev_pos):
        batch_size = x.shape[0]
        dense_heatmap = self.heatmap_head(x)
        heatmap = dense_heatmap.detach().sigmoid()
        padding = self.nms_kernel_size // 2
        local_max = torch.zeros_like(heatmap)
        # equals to nms radius = voxel_size * out_size_factor * kenel_size
        local_max_inner = F.max_pool2d(heatmap, kernel_size=self.nms_kernel_size, stride=1, padding=0)
        local_max[:, :, padding:(-padding), padding:(-padding)] = local_max_inner
        # for Pedestrian in Private (todo: 如果使用mult-head，需要修改)
        if self.test_cfg["dataset"] == "Private":
            local_max[:, 8] = F.max_pool2d(heatmap[:, 8], kernel_size=1, stride=1, padding=0)
        elif self.test_cfg["dataset"] == "nuScenes":
            local_max[:, 8] = F.max_pool2d(heatmap[:, 8], kernel_size=1, stride=1, padding=0)
            local_max[:, 9] = F.max_pool2d(heatmap[:, 9], kernel_size=1, stride=1, padding=0)

        heatmap = heatmap * (heatmap == local_max)
        heatmap = heatmap.view(batch_size, heatmap.shape[1], -1)

        # top #num_proposals among all classes
        top_proposals = heatmap.view(batch_size, -1).topk(
            k=self.num_lidar_init_query, dim=-1, largest=True, sorted=False
        )[1]
        top_proposals_class = top_proposals // heatmap.shape[-1]
        top_proposals_index = top_proposals % heatmap.shape[-1]
        self.query_labels = top_proposals_class
        query_pos = bev_pos.gather(
            index=top_proposals_index[:, None, :].permute(0, 2, 1).expand(-1, -1, bev_pos.shape[-1]), dim=1
        )
        return query_pos, dense_heatmap

    def forward_single(self, x, x_img, img_metas, x_radar, radar_points):
        """
        x: [bs c h w]
        return List(dict(head_name: [num_dec x bs x num_query * head_dim]) ) x task_num
        """
        assert x is not None, "Lidar is necessary for CenterCMT!"
        ret_dicts = []
        x = self.shared_conv(x)
        device = x.device
        batch_size = x.shape[0]
        bev_pos = self.coords_bev.repeat(batch_size, 1, 1).to(x.device)
        lidar_query_pos, dense_heatmap = self.get_lidar_pos_init(x, bev_pos)
        reference_points = self.reference_points.weight
        reference_points = reference_points.unsqueeze(0).repeat(batch_size, 1, 1)
        init_reference_points = torch.cat(
            [lidar_query_pos, 0.5 * torch.ones([*lidar_query_pos.shape[:-1], 1]).to(lidar_query_pos.device)], dim=-1
        )
        reference_points = torch.cat([init_reference_points, reference_points], dim=1)
        reference_points, attn_mask, mask_dict = self.prepare_for_dn(x.shape[0], reference_points, img_metas)
        bev_pos_embeds, rv_pos_embeds, query_embeds, radar_bev_pos_embeds = self._get_embeds(
            ref_points=reference_points,
            radar_points=None,
            x=x,
            x_img=x_img,
            x_radar=x_radar,
            img_metas=img_metas,
            device=device,
        )

        outs_dec, _ = self.transformer(x, x_img, query_embeds, bev_pos_embeds, rv_pos_embeds, attn_masks=attn_mask)
        outs_dec = torch.nan_to_num(outs_dec)

        reference = inverse_sigmoid(reference_points.clone())

        flag = 0
        for task_id, task in enumerate(self.task_heads, 0):
            outs = task(outs_dec)
            center = (outs["center"] + reference[None, :, :, :2]).sigmoid()
            height = (outs["height"] + reference[None, :, :, 2:3]).sigmoid()
            _center, _height = center.new_zeros(center.shape), height.new_zeros(height.shape)
            _center[..., 0:1] = center[..., 0:1] * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            _center[..., 1:2] = center[..., 1:2] * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            _height[..., 0:1] = height[..., 0:1] * (self.pc_range[5] - self.pc_range[2]) + self.pc_range[2]
            outs["center"] = _center
            outs["height"] = _height

            if mask_dict and mask_dict["pad_size"] > 0:
                task_mask_dict = copy.deepcopy(mask_dict)
                class_name = self.class_names[task_id]

                known_lbs_bboxes_label = task_mask_dict["known_lbs_bboxes"][0]
                known_labels_raw = task_mask_dict["known_labels_raw"]
                new_lbs_bboxes_label = known_lbs_bboxes_label.new_zeros(known_lbs_bboxes_label.shape)
                new_lbs_bboxes_label[:] = len(class_name)
                new_labels_raw = known_labels_raw.new_zeros(known_labels_raw.shape)
                new_labels_raw[:] = len(class_name)
                task_masks = [torch.where(known_lbs_bboxes_label == class_name.index(i) + flag) for i in class_name]
                task_masks_raw = [torch.where(known_labels_raw == class_name.index(i) + flag) for i in class_name]
                for cname, task_mask, task_mask_raw in zip(class_name, task_masks, task_masks_raw):
                    new_lbs_bboxes_label[task_mask] = class_name.index(cname)
                    new_labels_raw[task_mask_raw] = class_name.index(cname)
                task_mask_dict["known_lbs_bboxes"] = (new_lbs_bboxes_label, task_mask_dict["known_lbs_bboxes"][1])
                task_mask_dict["known_labels_raw"] = new_labels_raw
                flag += len(class_name)

                for key in list(outs.keys()):
                    outs["dn_" + key] = outs[key][:, :, : mask_dict["pad_size"], :]
                    outs[key] = outs[key][:, :, mask_dict["pad_size"] :, :]
                outs["dn_mask_dict"] = task_mask_dict

            ret_dicts.append(outs)

        ret_dicts[0]["dense_heatmap"] = dense_heatmap
        return ret_dicts

    def prepare_for_dn(self, batch_size, reference_points, img_metas):
        if self.training:
            gt_boxes = img_metas["gt_boxes"]
            mask = torch.any(gt_boxes[..., :9], dim=2)
            targets = [gt_boxes[i, mask[i, :], :9] for i in range(batch_size)]
            labels = [gt_boxes[i, mask[i, :], 9] - 1 for i in range(batch_size)]

            known = [(torch.ones_like(t)).cuda() for t in labels]
            know_idx = known
            unmask_bbox = unmask_label = torch.cat(known)
            known_num = [t.size(0) for t in targets]
            labels = torch.cat([t for t in labels])
            boxes = torch.cat([t for t in targets])
            batch_idx = torch.cat([torch.full((t.size(0),), i) for i, t in enumerate(targets)])

            known_indice = torch.nonzero(unmask_label + unmask_bbox)
            known_indice = known_indice.view(-1)
            groups = min(self.scalar, self.num_query // max(known_num))
            known_indice = known_indice.repeat(groups, 1).view(-1)
            known_labels = labels.repeat(groups, 1).view(-1).long().to(reference_points.device)
            known_labels_raw = labels.repeat(groups, 1).view(-1).long().to(reference_points.device)
            known_bid = batch_idx.repeat(groups, 1).view(-1)
            known_bboxs = boxes.repeat(groups, 1).to(reference_points.device)
            known_bbox_center = known_bboxs[:, :3].clone()
            known_bbox_scale = known_bboxs[:, 3:6].clone()
            if self.bbox_noise_scale > 0:
                diff = known_bbox_scale / 2 + self.bbox_noise_trans
                rand_prob = torch.rand_like(known_bbox_center) * 2 - 1.0
                known_bbox_center += torch.mul(rand_prob, diff) * self.bbox_noise_scale
                known_bbox_center[..., 0:1] = (known_bbox_center[..., 0:1] - self.pc_range[0]) / (
                    self.pc_range[3] - self.pc_range[0]
                )
                known_bbox_center[..., 1:2] = (known_bbox_center[..., 1:2] - self.pc_range[1]) / (
                    self.pc_range[4] - self.pc_range[1]
                )
                known_bbox_center[..., 2:3] = (known_bbox_center[..., 2:3] - self.pc_range[2]) / (
                    self.pc_range[5] - self.pc_range[2]
                )
                known_bbox_center = known_bbox_center.clamp(min=0.0, max=1.0)
                mask = torch.norm(rand_prob, 2, 1) > self.split
                known_labels[mask] = sum(self.num_classes)

            single_pad = int(max(known_num))
            pad_size = int(single_pad * groups)
            padding_bbox = torch.zeros(batch_size, pad_size, 3).to(reference_points.device)
            padded_reference_points = torch.cat([padding_bbox, reference_points], dim=1)

            if len(known_num):
                map_known_indice = torch.cat([torch.tensor(range(num)) for num in known_num])
                map_known_indice = torch.cat([map_known_indice + single_pad * i for i in range(groups)]).long()
            if len(known_bid):
                padded_reference_points[(known_bid.long(), map_known_indice)] = known_bbox_center.to(
                    reference_points.device
                )

            tgt_size = pad_size + self.num_query
            attn_mask = torch.ones(tgt_size, tgt_size).to(reference_points.device) < 0
            attn_mask[pad_size:, :pad_size] = True
            for i in range(groups):
                if i == 0:
                    attn_mask[single_pad * i : single_pad * (i + 1), single_pad * (i + 1) : pad_size] = True
                if i == groups - 1:
                    attn_mask[single_pad * i : single_pad * (i + 1), : single_pad * i] = True
                else:
                    attn_mask[single_pad * i : single_pad * (i + 1), single_pad * (i + 1) : pad_size] = True
                    attn_mask[single_pad * i : single_pad * (i + 1), : single_pad * i] = True

            mask_dict = {
                "known_indice": torch.as_tensor(known_indice).long(),
                "batch_idx": torch.as_tensor(batch_idx).long(),
                "map_known_indice": torch.as_tensor(map_known_indice).long(),
                "known_lbs_bboxes": (known_labels, known_bboxs),
                "known_labels_raw": known_labels_raw,
                "know_idx": know_idx,
                "pad_size": pad_size,
            }

        else:
            padded_reference_points = reference_points
            attn_mask = None
            mask_dict = None

        return padded_reference_points, attn_mask, mask_dict

    def hp_target_single(self, gt_bboxes_3d, gt_labels_3d):
        device = gt_labels_3d.device
        grid_size = torch.tensor(self.train_cfg["grid_size"])
        pc_range = torch.tensor(self.train_cfg["point_cloud_range"])
        voxel_size = torch.tensor(self.train_cfg["voxel_size"])
        feature_map_size = grid_size[:2] // self.train_cfg["out_size_factor"]  # [x_len, y_len]
        heatmap = gt_bboxes_3d.new_zeros((self.num_classes[0], feature_map_size[1], feature_map_size[0]))
        for idx in range(len(gt_bboxes_3d)):
            length = gt_bboxes_3d[idx][3]
            width = gt_bboxes_3d[idx][4]
            length = length / voxel_size[0] / self.train_cfg["out_size_factor"]
            width = width / voxel_size[1] / self.train_cfg["out_size_factor"]
            if width > 0 and length > 0:
                radius = gaussian_radius((length, width), min_overlap=self.train_cfg["gaussian_overlap"])
                radius = max(self.train_cfg["min_radius"], int(radius))
                x, y = gt_bboxes_3d[idx][0], gt_bboxes_3d[idx][1]

                coor_x = (x - pc_range[0]) / voxel_size[0] / self.train_cfg["out_size_factor"]
                coor_y = (y - pc_range[1]) / voxel_size[1] / self.train_cfg["out_size_factor"]

                center = torch.tensor([coor_x, coor_y], dtype=torch.float32, device=device)
                center_int = center.to(torch.int32)
                draw_heatmap_gaussian(heatmap[gt_labels_3d[idx].to(torch.int)], center_int, radius)

        return [heatmap]

    @force_fp32(apply_to=("preds_dicts"))
    def loss(self, gt_bboxes_3d, preds_dicts, **kwargs):
        """ "Loss function.
        Args:
            gt_bboxes_3d (tensor): (batch_size, num_gts, 10). gt_boxes are padding with 0 to align.
                                gt_bboxes_3d[:,:,9] are the gt_labels
            preds_dicts(tuple[list[dict]]): nb_tasks x num_lvl
                center: (num_dec, batch_size, num_query, 2)
                height: (num_dec, batch_size, num_query, 1)
                dim: (num_dec, batch_size, num_query, 3)
                rot: (num_dec, batch_size, num_query, 2)
                vel: (num_dec, batch_size, num_query, 2)
                cls_logits: (num_dec, batch_size, num_query, task_classes)
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """

        batch_size = gt_bboxes_3d.shape[0]
        mask = torch.any(gt_bboxes_3d[..., :9], dim=2)
        gt_boxes_3d_list = [gt_bboxes_3d[i, mask[i, :], :9] for i in range(batch_size)]
        gt_labels_3d_list = [gt_bboxes_3d[i, mask[i, :], 9] - 1 for i in range(batch_size)]

        num_decoder = preds_dicts[0][0]["center"].shape[0]
        all_pred_bboxes, all_pred_logits = collections.defaultdict(list), collections.defaultdict(list)

        for task_id, preds_dict in enumerate(preds_dicts, 0):
            for dec_id in range(num_decoder):
                pred_bbox = torch.cat(
                    (
                        preds_dict[0]["center"][dec_id],
                        preds_dict[0]["height"][dec_id],
                        preds_dict[0]["dim"][dec_id],
                        preds_dict[0]["rot"][dec_id],
                        preds_dict[0]["vel"][dec_id],
                    ),
                    dim=-1,
                )
                all_pred_bboxes[dec_id].append(pred_bbox)
                all_pred_logits[dec_id].append(preds_dict[0]["cls_logits"][dec_id])
        all_pred_bboxes = [all_pred_bboxes[idx] for idx in range(num_decoder)]
        all_pred_logits = [all_pred_logits[idx] for idx in range(num_decoder)]

        loss_cls, loss_bbox = multi_apply(
            self.loss_single,
            all_pred_bboxes,
            all_pred_logits,
            [gt_boxes_3d_list for _ in range(num_decoder)],
            [gt_labels_3d_list for _ in range(num_decoder)],
        )

        loss_dict = dict()
        loss_dict["loss_cls"] = loss_cls[-1]
        loss_dict["loss_bbox"] = loss_bbox[-1]

        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(loss_cls[:-1], loss_bbox[:-1]):
            loss_dict[f"d{num_dec_layer}.loss_cls"] = loss_cls_i
            loss_dict[f"d{num_dec_layer}.loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        dn_pred_bboxes, dn_pred_logits = collections.defaultdict(list), collections.defaultdict(list)
        dn_mask_dicts = collections.defaultdict(list)
        for task_id, preds_dict in enumerate(preds_dicts, 0):
            for dec_id in range(num_decoder):
                pred_bbox = torch.cat(
                    (
                        preds_dict[0]["dn_center"][dec_id],
                        preds_dict[0]["dn_height"][dec_id],
                        preds_dict[0]["dn_dim"][dec_id],
                        preds_dict[0]["dn_rot"][dec_id],
                        preds_dict[0]["dn_vel"][dec_id],
                    ),
                    dim=-1,
                )
                dn_pred_bboxes[dec_id].append(pred_bbox)
                dn_pred_logits[dec_id].append(preds_dict[0]["dn_cls_logits"][dec_id])
                dn_mask_dicts[dec_id].append(preds_dict[0]["dn_mask_dict"])
        dn_pred_bboxes = [dn_pred_bboxes[idx] for idx in range(num_decoder)]
        dn_pred_logits = [dn_pred_logits[idx] for idx in range(num_decoder)]
        dn_mask_dicts = [dn_mask_dicts[idx] for idx in range(num_decoder)]
        dn_loss_cls, dn_loss_bbox = multi_apply(self.dn_loss_single, dn_pred_bboxes, dn_pred_logits, dn_mask_dicts)

        loss_dict["dn_loss_cls"] = dn_loss_cls[-1]
        loss_dict["dn_loss_bbox"] = dn_loss_bbox[-1]
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(dn_loss_cls[:-1], dn_loss_bbox[:-1]):
            loss_dict[f"d{num_dec_layer}.dn_loss_cls"] = loss_cls_i
            loss_dict[f"d{num_dec_layer}.dn_loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        hp_target = multi_apply(
            self.hp_target_single,
            gt_boxes_3d_list,
            gt_labels_3d_list,
        )
        hp_target = [t.unsqueeze(0) for t in hp_target[0]]
        heatmap_target = torch.cat(hp_target, dim=0)
        loss_heatmap = self.loss_heatmap(
            clip_sigmoid(preds_dict[0]["dense_heatmap"]),
            heatmap_target,
            avg_factor=max(heatmap_target.eq(1).float().sum().item(), 1),
        )
        loss_dict["loss_heatmap"] = loss_heatmap

        return loss_dict


class CMTFusionHeadSide(CMTFusionHead):
    def _generate_img_metas(self, img_metas, num_frontback_cams):
        frontback_img_metas, side_img_metas = dict(), dict()
        frontback_img_metas["sensor2ego_mats"] = img_metas["sensor2ego_mats"][
            :, :, :num_frontback_cams, :, :
        ].contiguous()
        frontback_img_metas["intrin_mats"] = img_metas["intrin_mats"][:, :, :num_frontback_cams, :, :].contiguous()
        frontback_img_metas["ida_mats"] = img_metas["ida_mats"].contiguous()
        frontback_img_metas["bda_mat"] = img_metas["bda_mat"]
        frontback_img_metas["gt_boxes"] = img_metas["gt_boxes"]
        frontback_img_metas["pad_shape"] = img_metas["pad_shape"]

        side_img_metas["sensor2ego_mats"] = img_metas["sensor2ego_mats"][:, :, num_frontback_cams:, :, :].contiguous()
        side_img_metas["intrin_mats"] = img_metas["intrin_mats"][:, :, num_frontback_cams:, :, :].contiguous()
        side_img_metas["ida_mats"] = img_metas["side_ida_mats"].contiguous()
        side_img_metas["bda_mat"] = img_metas["bda_mat"]
        side_img_metas["gt_boxes"] = img_metas["gt_boxes"]
        side_img_metas["pad_shape"] = img_metas["side_pad_shape"]
        return frontback_img_metas, side_img_metas

    def forward_single(self, x, x_img, img_metas, x_radar, radar_points):
        """
        x: [bs c h w]
        return List(dict(head_name: [num_dec x bs x num_query * head_dim]) ) x task_num
        """
        assert (x_img is not None) and ("side_ida_mats" in img_metas)
        if x is not None:
            batch_size = x.shape[0]
            device = x.device
            x = self.shared_conv(x)
        elif x_img is not None:
            batch_size, sweep, num_frontback_cams, _, _ = img_metas["ida_mats"].shape
            _, _, num_side_cams, _, _ = img_metas["side_ida_mats"].shape
            device = x_img[0].device
        else:
            raise NotImplementedError

        ret_dicts = []
        reference_points = self.reference_points.weight
        # check
        reference_points = reference_points.unsqueeze(0).repeat(batch_size, 1, 1)

        if x_radar is not None:
            init_radar_query = self.get_radar_pos_init(radar_points)
            reference_points = torch.cat([init_radar_query, reference_points], dim=1)
        else:
            init_radar_query = None

        reference_points, attn_mask, mask_dict = self.prepare_for_dn(batch_size, reference_points, img_metas)

        x_frontback_img, x_side_img = x_img[0], x_img[1]
        frontback_img_metas, side_img_metas = self._generate_img_metas(img_metas, num_frontback_cams)

        bev_pos_embeds, rv_pos_embeds, query_embeds, radar_bev_pos_embeds = self._get_embeds(
            reference_points,
            init_radar_query,
            x,
            [x_frontback_img, x_side_img],
            x_radar,
            [frontback_img_metas, side_img_metas],
            device,
        )

        # flatten img_tokens and token rv_pos_embeds
        frontback_img = rearrange(x_frontback_img, "(bs v) c h w -> (v h w) bs c", bs=batch_size)
        side_img = rearrange(x_side_img, "(bs v) c h w -> (v h w) bs c", bs=batch_size)
        x_img = torch.cat([frontback_img, side_img], 0)
        frontback_rv_pos_embeds = rearrange(rv_pos_embeds[0], "(bs v) h w c -> (v h w) bs c", bs=batch_size)
        side_rv_pos_embeds = rearrange(rv_pos_embeds[1], "(bs v) h w c -> (v h w) bs c", bs=batch_size)
        rv_pos_embeds = torch.cat([frontback_rv_pos_embeds, side_rv_pos_embeds], 0)

        outs_dec, _ = self.transformer(
            x,
            x_img,
            query_embeds,
            bev_pos_embeds,
            rv_pos_embeds,
            attn_masks=attn_mask,
            x_radar=x_radar,
            radar_bev_pos_embed=radar_bev_pos_embeds,
        )
        outs_dec = torch.nan_to_num(outs_dec)

        reference = inverse_sigmoid(reference_points.clone())

        if self.repaired_timestamp:
            timestamp_dict = self.initialize_interpolation_dict(img_metas)

        flag = 0
        for task_id, task in enumerate(self.task_heads, 0):
            outs = task(outs_dec)
            center = (outs["center"] + reference[None, :, :, :2]).sigmoid()
            height = (outs["height"] + reference[None, :, :, 2:3]).sigmoid()
            _center, _height = center.new_zeros(center.shape), height.new_zeros(height.shape)
            _center[..., 0:1] = center[..., 0:1] * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            _center[..., 1:2] = center[..., 1:2] * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            _height[..., 0:1] = height[..., 0:1] * (self.pc_range[5] - self.pc_range[2]) + self.pc_range[2]
            outs["center"] = _center
            outs["height"] = _height
            _reference = center.new_zeros(center.shape)
            _reference[..., 0:1] = (
                reference[None, :, :, 0:1].sigmoid() * (self.pc_range[3] - self.pc_range[0]) + self.pc_range[0]
            )
            _reference[..., 1:2] = (
                reference[None, :, :, 1:2].sigmoid() * (self.pc_range[4] - self.pc_range[1]) + self.pc_range[1]
            )
            outs["reference"] = _reference

            if self.repaired_timestamp:
                pts = torch.cat([_center, _height, torch.ones_like(_height).to(_height.device)], dim=-1)
                outs = self.use_timediff_refinement(outs, pts, timestamp_dict, img_metas)

            if mask_dict and mask_dict["pad_size"] > 0:
                task_mask_dict = copy.deepcopy(mask_dict)
                class_name = self.class_names[task_id]

                known_lbs_bboxes_label = task_mask_dict["known_lbs_bboxes"][0]
                known_labels_raw = task_mask_dict["known_labels_raw"]
                new_lbs_bboxes_label = known_lbs_bboxes_label.new_zeros(known_lbs_bboxes_label.shape)
                new_lbs_bboxes_label[:] = len(class_name)
                new_labels_raw = known_labels_raw.new_zeros(known_labels_raw.shape)
                new_labels_raw[:] = len(class_name)
                task_masks = [torch.where(known_lbs_bboxes_label == class_name.index(i) + flag) for i in class_name]
                task_masks_raw = [torch.where(known_labels_raw == class_name.index(i) + flag) for i in class_name]
                for cname, task_mask, task_mask_raw in zip(class_name, task_masks, task_masks_raw):
                    new_lbs_bboxes_label[task_mask] = class_name.index(cname)
                    new_labels_raw[task_mask_raw] = class_name.index(cname)
                task_mask_dict["known_lbs_bboxes"] = (new_lbs_bboxes_label, task_mask_dict["known_lbs_bboxes"][1])
                task_mask_dict["known_labels_raw"] = new_labels_raw
                flag += len(class_name)

                for key in list(outs.keys()):
                    outs["dn_" + key] = outs[key][:, :, : mask_dict["pad_size"], :]
                    outs[key] = outs[key][:, :, mask_dict["pad_size"] :, :]
                outs["dn_mask_dict"] = task_mask_dict

            ret_dicts.append(outs)

        return ret_dicts
