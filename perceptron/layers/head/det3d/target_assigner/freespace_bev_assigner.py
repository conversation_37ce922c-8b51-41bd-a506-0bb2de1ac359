import torch

from .base_assigner import BaseAssigner

from perceptron.data.det3d.preprocess.voxelization import Voxelization


class FreepaceBevAssigner(BaseAssigner):
    def __init__(self, data_config, model_config, freespace_labels=["driveable_surface"]):
        super().__init__()
        self.data_config = data_config
        self.model_config = model_config
        self.N_grid = model_config.N_grid
        self.freespace_labels = []
        for k, v in model_config.labels.items():
            if v in freespace_labels:
                self.freespace_labels.append(k)

    def assign_targets(self, label_meta):
        """
        main
        Args:
            label_meta(Tuple): (voxel_labels, voxel_coords, voxel_num_points)
        Return
            target_dict:
                "bev_labels":  torch.tensor,  shape = [bs,1, H_bev, W_bev]
        """
        voxel_labels, voxel_coords, voxel_num_points = label_meta
        voxel_labels, voxel_coords, voxel_num_points = self.split_meta(
            voxel_labels=voxel_labels, voxel_coords=voxel_coords, voxel_num_points=voxel_num_points
        )
        bev_labels = []
        for label, coord, num_point in zip(voxel_labels, voxel_coords, voxel_num_points):  # per frame in batch
            bev_label = self._gt_to_bev_labels_freespace(label, coord, num_point)
            bev_labels.append(bev_label)

        bev_labels = torch.stack(bev_labels, dim=0)

        target_dict = {
            "bev_labels": bev_labels.permute(0, 3, 1, 2),  # consistent with seg_head
        }
        return target_dict

    def split_meta(self, voxel_labels, voxel_coords, voxel_num_points):
        """
        label 信息按帧切开
        """
        batchsize = int(voxel_coords[:, 0].max()) + 1
        voxel_labels_list, voxel_coords_list, voxel_num_points_list = [], [], []
        for i in range(batchsize):
            idx = voxel_coords[:, 0] == i
            voxel_labels_list.append(voxel_labels[idx])
            voxel_coords_list.append(voxel_coords[idx])
            voxel_num_points_list.append(voxel_num_points[idx])
        return voxel_labels_list, voxel_coords_list, voxel_num_points_list

    def _gt_to_bev_labels_freespace(self, point_label, voxel_coords, voxel_num_points):
        """
        只有指定类别(self.freespace_labels)作为可行驶区域，其他都是障碍物。
        1. 只要voxel里面有障碍物，整个voxel就不可通行。
        2. 对高于车身通行高度的格子，如果下方还有格子，整个pillar的label由下方格子确定。
        Args:
            point_label(tensor): point-wise segmentation label, NxMx1
            voxel_coords(tensor): point coords of voxel, Nx3
            voxel_num_points(tensor): corresponding number of points in voxel, Nx1
        Return:
            bev_label(tensor): 每个pillar的freespace label
        """
        H_bev, W_bev = (
            self.data_config.grid_size[1],
            self.data_config.grid_size[0],
        )  # lidar坐标系下, H对应的是y, W对应的是x
        Z = self.data_config.grid_size[2]
        freespace_coord_h = self.data_config.freespace_grid

        voxel_label = torch.ones((H_bev, W_bev, Z, 1), dtype=torch.int8).cuda() * -1
        z_idx, h_idx, w_idx = voxel_coords[:, 1].long(), voxel_coords[:, 2].long(), voxel_coords[:, 3].long()
        # 每个pillar内的label assign
        n_freespace = torch.zeros_like(voxel_num_points)
        for label in self.freespace_labels:
            n_freespace += (point_label == label).sum(1)
        object_label = n_freespace != voxel_num_points  # not all points belongs to freespace

        voxel_label[h_idx, w_idx, z_idx] = object_label.to(torch.int8).view(-1, 1)
        voxel_label = voxel_label.view(H_bev, W_bev, Z)
        if freespace_coord_h >= Z:
            bev_label, _ = voxel_label.max(axis=2, keepdim=True)
            valid_mask = (bev_label != -1).to(torch.int8)
            bev_label = (1 - bev_label) * valid_mask - 1 * (1 - valid_mask)
            return bev_label

        low = voxel_label[:, :, :freespace_coord_h]
        high = voxel_label[:, :, freespace_coord_h:]
        bev_high, _ = high.max(axis=2, keepdim=True)  # 上半截格子上有没有障碍物
        bev_low, _ = low.max(axis=2, keepdim=True)

        valid_mask = (bev_low != -1).to(torch.int8)  # 下半截有点用下半截label，下半截没点用上半截label.
        bev_label = valid_mask * bev_low + (1 - valid_mask) * bev_high
        valid_mask = (torch.maximum(bev_high, bev_low) != -1).to(torch.int8)
        bev_label = (1 - bev_label) * valid_mask - 1 * (1 - valid_mask)
        return bev_label


class FreespaceBevMultiClassAssigner(FreepaceBevAssigner):
    def __init__(self, data_config, model_config, freespace_labels=["driveable_surface"]):
        super().__init__(data_config, model_config, freespace_labels)
        self.infinite = (
            model_config.target_assigner_infinite if hasattr(model_config, "target_assigner_infinite") else -1
        )

    def assign_targets(self, label_meta):
        """
        main
        Args:
            label_meta(Tuple): (voxel_labels, voxel_coords, voxel_num_points)
        Return
            target_dict:
                "bev_labels":  torch.tensor,  shape = [bs,1, H_bev, W_bev]
        """
        voxel_labels, voxel_coords, voxel_num_points = label_meta
        voxel_labels, voxel_coords, voxel_num_points = self.split_meta(
            voxel_labels=voxel_labels, voxel_coords=voxel_coords, voxel_num_points=voxel_num_points
        )
        bev_labels = []
        for label, coord, num_point in zip(voxel_labels, voxel_coords, voxel_num_points):  # per frame in batch
            bev_label = self._gt_to_bev_labels_multi_class(label, coord, num_point)
            bev_labels.append(bev_label)

        bev_labels = torch.stack(bev_labels, dim=0)

        target_dict = {"bev_labels": bev_labels.permute(0, 3, 1, 2)}
        return target_dict

    def _gt_to_bev_labels_multi_class(self, point_label, voxel_coords, voxel_num_points):
        """
        将3D label压缩为2D。voxel内label冲突时，优先级为：static > dynamic > freespace > background > -1
        Args:
            point_label(tensor): point-wise segmentation label, NxMx1 # TODO shape
            voxel_coords(tensor): point coords of voxel, Nx3
            voxel_num_points(tensor): corresponding number of points in voxel, Nx1
        Return:
            bev_label(tensor): 每个pillar的freespace label
        """
        H_bev, W_bev = (
            self.data_config.grid_size[1],
            self.data_config.grid_size[0],
        )
        Z = self.data_config.grid_size[2]
        freespace_coord_h = self.data_config.freespace_grid

        voxel_label = torch.ones((H_bev, W_bev, Z, 1), dtype=torch.int8).cuda() * -1
        z_idx, h_idx, w_idx = voxel_coords[:, 1].long(), voxel_coords[:, 2].long(), voxel_coords[:, 3].long()
        point_label = point_label.max(1).values  # label assign
        voxel_label[h_idx, w_idx, z_idx] = point_label.to(torch.int8).view(-1, 1)

        if freespace_coord_h >= Z:
            bev_low, _ = voxel_label.max(axis=2, keepdim=True)
            valid_mask = (bev_low != -1).to(torch.int64)
            bev_label = bev_low * valid_mask + self.infinite * (1 - valid_mask)
            return bev_label.squeeze(-1)

        low = voxel_label[:, :, :freespace_coord_h]
        high = voxel_label[:, :, freespace_coord_h:]

        bev_high, _ = high.max(axis=2, keepdim=True)  # 上半截格子类型
        bev_low, _ = low.max(axis=2, keepdim=True)  # 下半截格子类型
        valid_low_mask = (bev_low != -1).to(torch.int8)
        bev_label = valid_low_mask * bev_low + (1 - valid_low_mask) * bev_high  # 下半截有点用下半截label，下半截没点用上半截label.

        valid_mask = (torch.maximum(bev_high, bev_low) != -1).to(
            torch.int64
        )  # 格子内有没有点, int64是为了保证下一行self.infinite * (1 - valid_mask)没有溢出
        bev_label = bev_label * valid_mask + self.infinite * (1 - valid_mask)

        bev_label = bev_label.squeeze(-1)
        return bev_label


class FreepaceBevAssigner_voxelize(FreespaceBevMultiClassAssigner):
    """
    用于input与label需要不同的voxel size时
    """

    def __init__(
        self, data_config, model_config, data_config_label, freespace_labels=["driveable_surface"], multi_class=False
    ):
        super().__init__(data_config, model_config, freespace_labels)
        self.data_config = data_config_label
        self.voxelizer = self._build_voxelizer(self.data_config)

        if multi_class:
            self.label_fn = self._gt_to_bev_labels_multi_class
        else:
            self.label_fn = self._gt_to_bev_labels_freespace

    def _build_voxelizer(self, data_config):
        # 将标注转为bev label
        voxelizer = Voxelization(
            voxel_size=data_config.voxel_size,
            point_cloud_range=data_config.point_cloud_range,
            max_num_points=data_config.max_num_points,
            max_voxels=data_config.max_voxels,
            num_point_features=data_config.src_num_point_features + 1,
            device=torch.device("cuda"),
        )
        return voxelizer

    def assign_targets(self, label_with_points):
        """
        main
        :params: point_label, point-wise segmantation label
        :return: target_dict:
            "bev_labels":  torch.tensor,  shape = [bs,1, H_bev, W_bev]
        """
        """
        main
        Args:
            label_with_point(tensor): Nx4 in which last dim is label
        Return
            target_dict:
                "bev_labels":  torch.tensor,  shape = [bs,1, H_bev, W_bev]
        """
        bev_labels = []
        for label_with_point in label_with_points:
            if label_with_point[:, -1].max() == -1:  # no valid point-label: all bev_label are assgin to -1
                bev_label = (
                    torch.ones(
                        (self.data_config.grid_size[1], self.data_config.grid_size[0], 1), dtype=torch.int8
                    ).cuda()
                    * -1
                )
            else:
                voxels, voxel_coords, voxel_num_points = self.voxelizer(label_with_point)
                voxel_label = voxels[:, :, -1]
                bev_label = self.label_fn(voxel_label, voxel_coords, voxel_num_points)
            bev_labels.append(bev_label)

        bev_labels = torch.stack(bev_labels, dim=0)

        target_dict = {"bev_labels": bev_labels.permute(0, 3, 1, 2)}
        return target_dict
