import torch
import torch.nn as nn
from torch.nn import functional as F


class GridSegHead(nn.Module):
    """
    :params: in_channel: 输入feature的 feature dim
    :params: out_channel: 中间层输出的 feature dim
    :params: num_classes: 分割时的分类数目, 应该设置为车道线分类数目+1(背景类), 目前暂时先只区分是否为车道线, 默认num_classes=2
    :params: grid_size: BEV feature map的尺寸, 与data config中的grid_size一致

    """

    def __init__(
        self,
        target_assigner,
        in_channel=384,
        out_channel=1024,
        num_classes=2,
        model_cfg=None,
        data_cfg=None,
        output_shape=(512, 512),
    ):
        super(GridSegHead, self).__init__()

        self.data_config = data_cfg
        self.model_config = model_cfg
        self.grid_size = data_cfg.grid_size
        self.in_channel = in_channel
        self.out_channel = out_channel
        self.output_shape = tuple(output_shape)
        self.regression_dim = model_cfg.seg_head_regression_dim
        self.num_classes = num_classes

        self.cls_branch = model_cfg.seg_head_cls_branch
        self.regress_branch = model_cfg.seg_head_regress_branch
        self.conf_threshold = model_cfg.seg_head_confidence_threshold
        self.loss_cls_weight = model_cfg.loss_cls_weight
        self.loss_regr_weight = model_cfg.loss_regr_weight

        self.init_head()
        self.target_assigner = target_assigner

    def init_head(self):
        self.upsample_layer = nn.ConvTranspose2d(
            in_channels=self.in_channel,
            out_channels=self.in_channel,
            stride=2,
            kernel_size=3,
            padding=1,
            output_padding=1,
            dilation=1,
            padding_mode="zeros",
            bias=False,
        )

        # ----------------- binary segementation -----------------
        if self.cls_branch:
            self.class_predictor = nn.Sequential(
                nn.Conv2d(self.in_channel, self.out_channel, 1),
                nn.BatchNorm2d(self.out_channel),
                nn.ReLU(),
                nn.Conv2d(self.out_channel, self.num_classes, 1),
                nn.BatchNorm2d(self.num_classes),
            )

        # ----------------- regression -----------------
        if self.regress_branch:
            self.regression_head = nn.Sequential(
                nn.Conv2d(self.in_channel, self.out_channel, 1),
                nn.BatchNorm2d(self.out_channel),
                nn.ReLU(),
                nn.Conv2d(self.out_channel, self.regression_dim, 1),
            )

    def assign_targets(self, gt):
        targets_dict = self.target_assigner.assign_targets(gt)
        return targets_dict

    def upsample(self, x):
        """
        an upsample layer, then interpolate
        reference https://github.com/mit-han-lab/bevfusion/blob/main/mmdet3d/models/heads/segm/vanilla.py

        """
        x = self.upsample_layer(x)  # bs, in_channel, H_bev, W_bev
        if x.shape[2:][0] != self.output_shape[0] or x.shape[2:][1] != self.output_shape[1]:
            x = F.interpolate(
                x,
                size=self.output_shape,
                mode="bilinear",
                align_corners=False,
            )
        return x

    def forward(self, x, gt=None):
        x = self.upsample(x)

        forward_ret_dict = {}
        if self.regress_branch:
            regr_output = self.regression_head(x)  # bs, regr_dim, H_bev, W_bev
            forward_ret_dict["pred_coords"] = regr_output

        if self.cls_branch:
            class_output = self.class_predictor(x)  # bs, cls_nums, H_bev, W_bev
            forward_ret_dict["pred_seg"] = class_output

        if gt:
            targets_dict = self.assign_targets(gt)
            forward_ret_dict.update(targets_dict)  # key: {"bev_labels", "gt_coords"}
        return forward_ret_dict

    def label_formatting(self, bev_label, pred):
        """
        BEV label二值化
        :params: bev_label,  shape = [bs, 1, H, W]
        :returns: seg_label, shape = [bs, 1, H, w]
        """
        seg_label = torch.clone(bev_label)  # bev label后面还要用
        seg_label = torch.where(seg_label == 0, 0, 1)
        return seg_label

    def get_loss(self, forward_ret_dict):
        bev_labels = forward_ret_dict["bev_labels"]  # shape = [bs, 1, H_bev, W_bev]

        loss = 0.0
        ret = {}
        if self.cls_branch:
            y_pred_cls = forward_ret_dict["pred_seg"]
            lanes_label = self.label_formatting(bev_labels, y_pred_cls)
            y_label_cls = lanes_label.long().cuda()

            # cls_loss
            cls_loss = self.crit(y_pred_cls, y_label_cls, weights=torch.ones_like(y_label_cls))
            cls_loss = cls_loss.mean()
            cls_loss = self.loss_cls_weight * cls_loss
            # observe metric
            pos_mask = y_label_cls.eq(1).long()
            neg_mask = 1 - pos_mask
            y_pred_cls = torch.sigmoid(y_pred_cls)
            pos_pred = (y_pred_cls * pos_mask).sum() / (pos_mask.sum() + 1e-8)
            neg_pred = (y_pred_cls * neg_mask).sum() / neg_mask.sum()

            loss += cls_loss
            ret["cls_loss"] = cls_loss
            ret["pos_pred"] = pos_pred
            ret["neg_pred"] = neg_pred

        if self.regress_branch:
            # Regression Loss
            pred_coords = forward_ret_dict["pred_coords"]  # [bs, 3, H, W]
            gt_coords = forward_ret_dict["gt_coords"]  # [bs, 3, H, W]
            x_loss = self.crit_reg(pred_coords[:, 0], gt_coords[:, 0])
            y_loss = self.crit_reg(pred_coords[:, 1], gt_coords[:, 1])
            z_loss = self.crit_reg(pred_coords[:, 2], gt_coords[:, 2])
            regr_loss = self.loss_regr_weight * (x_loss + y_loss + z_loss)
            loss += regr_loss

            ret.update(
                {
                    "regr_loss": regr_loss,
                    "x_loss": x_loss,
                    "y_loss": y_loss,
                    "z_loss": z_loss,
                }
            )

        ret["loss"] = loss
        return ret


class GridSegFreespaceHead(GridSegHead):
    def label_formatting(self, bev_label, smooth_lambda=None):
        """
        BEV label二值化, 带label smooth
        Args:
            bev_label:  shape = [bs, 1, H, W] , 每个grid的label
            smooth_lambda: shape = [bs], 每个instance的label smooth 参数
        Returns:
            seg_label: shape = [bs, 1, H, w]
            mask: shape = [bs, 1, H, w]
        """
        ndim = len(bev_label.shape)
        seg_label = torch.clone(bev_label)  # bev label后面还要用
        seg_label = torch.where(seg_label == 1, 1, 0)
        if smooth_lambda is not None:
            smooth_lambda = smooth_lambda.view([-1] + [1] * (ndim - 1))
            seg_label = seg_label * smooth_lambda + (1 - seg_label) * (1 - smooth_lambda)
        mask = bev_label != -1
        return seg_label, mask

    def get_loss(self, forward_ret_dict):
        bev_labels = forward_ret_dict["bev_labels"]  # shape = [bs, 1, H_bev, W_bev]
        if "label_smooth_lambda" in forward_ret_dict:
            label_smooth_lambda = forward_ret_dict["label_smooth_lambda"]
        else:
            label_smooth_lambda = torch.ones((len(bev_labels))).to(bev_labels)

        loss = 0.0
        ret = {}
        if self.cls_branch:
            y_pred_cls = forward_ret_dict["pred_seg"]
            lanes_label, mask = self.label_formatting(bev_labels, smooth_lambda=label_smooth_lambda)
            y_label_cls = lanes_label

            # cls_loss ###
            cls_loss = self.crit(y_pred_cls, y_label_cls, weights=mask)
            cls_loss = cls_loss.sum() / (mask.sum() + 1e-8)
            cls_loss = self.loss_cls_weight * cls_loss
            # observe metric
            pos_mask = y_label_cls.eq(1).long()
            neg_mask = mask * (1 - pos_mask)
            y_pred_cls = torch.sigmoid(y_pred_cls)
            pos_pred = (y_pred_cls * pos_mask).sum() / (pos_mask.sum() + 1e-8)
            neg_pred = (y_pred_cls * neg_mask).sum() / (neg_mask.sum() + 1e-8)

            loss += cls_loss
            ret["cls_loss"] = cls_loss
            ret["pos_pred"] = pos_pred
            ret["neg_pred"] = neg_pred

        ret["loss"] = loss
        return ret

    def init_head(self):
        self.upsample_layer = nn.ConvTranspose2d(
            in_channels=self.in_channel,
            out_channels=self.out_channel,  # 由于cls_predictor只有一层，这里借用了out_channel这个参数
            stride=2,
            kernel_size=2,
            bias=False,
        )

        # ----------------- binary segementation -----------------
        if self.cls_branch:
            self.class_predictor = nn.Sequential(
                nn.Conv2d(self.out_channel, self.num_classes, 1),
            )

    def upsample(self, x):
        """
        an upsample layer, then deconv
        """
        x = self.upsample_layer(x)  # bs, in_channel, H_bev, W_bev

        assert (
            x.shape[2:][0] == self.output_shape[0] and x.shape[2:][1] == self.output_shape[1]
        ), "predict shape should align with target"
        return x


class GridSegFreespaceHeadMultiClass(GridSegFreespaceHead):
    # 多分类分割
    def __init__(
        self,
        target_assigner,
        in_channel=384,
        out_channel=1024,
        num_classes=2,
        model_cfg=None,
        data_cfg=None,
        output_shape=(512, 512),
        ignore_labels=[0, -1],
    ):
        self.binary_seg = model_cfg.seg_head_binary_seg
        super().__init__(
            target_assigner=target_assigner,
            in_channel=in_channel,
            out_channel=out_channel,
            num_classes=num_classes,
            model_cfg=model_cfg,
            data_cfg=data_cfg,
            output_shape=output_shape,
        )
        assert self.num_classes > 1

    def init_head(self):
        self.upsample_layer = nn.ConvTranspose2d(
            in_channels=self.in_channel,
            out_channels=self.out_channel,  # 由于cls_predictor只有一层，这里借用了out_channel这个参数
            stride=2,
            kernel_size=2,
            bias=False,
        )

        if self.cls_branch:
            self.class_predictor = nn.Sequential(
                nn.Conv2d(self.out_channel, self.num_classes, 1),
            )
        # ----------------- binary segementation -----------------
        if self.binary_seg:
            self.class_predictor_binary = nn.Sequential(
                nn.Conv2d(self.out_channel, 1, 1),
            )

    def forward(self, x, gt=None):
        x = self.upsample(x)
        forward_ret_dict = {}

        class_output = self.class_predictor(x)  # bs, cls_nums, H_bev, W_bev
        class_output = class_output.permute(0, 2, 3, 1)  # bs, H_bev, W_bev, cls_nums
        class_output = torch.softmax(class_output, -1)
        forward_ret_dict["pred_seg"] = class_output

        if self.binary_seg:
            class_output_binary = self.class_predictor_binary(x)  # bs, cls_nums, H_bev, W_bev
            forward_ret_dict["pred_seg_binary"] = class_output_binary

        if gt is not None:
            targets_dict = self.assign_targets(gt)
            forward_ret_dict.update(targets_dict)
        return forward_ret_dict

    def get_loss(self, forward_ret_dict):
        loss = 0.0
        ret = {}

        # cls_loss
        bev_labels = forward_ret_dict["bev_labels"]  # shape = [bs, 1, H_bev, W_bev]
        y_label_cls = bev_labels.squeeze(1).long()
        y_pred_cls = forward_ret_dict["pred_seg"]  # shape = [bs, H_bev, W_bev, cls_nums]

        cls_loss = self.crit(y_pred_cls, y_label_cls)
        cls_loss = self.loss_cls_weight * cls_loss
        loss += cls_loss
        ret["cls_loss"] = cls_loss

        mask = y_label_cls != -1
        hit = (y_pred_cls.argmax(-1) == y_label_cls)[mask]
        misclassify = 1.0 - hit.sum() / (mask.sum() + 1e-8)
        ret["cls_misclassify"] = misclassify

        # binary cls loss
        if self.binary_seg:
            if "label_smooth_lambda" in forward_ret_dict:
                label_smooth_lambda = forward_ret_dict["label_smooth_lambda"]
            else:
                label_smooth_lambda = torch.ones((len(bev_labels))).to(bev_labels)
            y_pred_cls = forward_ret_dict["pred_seg_binary"]
            lanes_label, mask = self.label_formatting(bev_labels, smooth_lambda=label_smooth_lambda)
            y_label_cls = lanes_label.long().cuda()

            cls_loss = self.crit_binary(y_pred_cls, y_label_cls, weights=mask)
            cls_loss = cls_loss.sum() / (mask.sum() + 1e-8)
            cls_loss = self.model_config.loss_cls_binary_weight * cls_loss
            # observe metric
            pos_mask = y_label_cls.eq(1).long()
            neg_mask = mask * (1 - pos_mask)
            y_pred_cls = torch.sigmoid(y_pred_cls)
            pos_pred = (y_pred_cls * pos_mask).sum() / (pos_mask.sum() + 1e-8)
            neg_pred = (y_pred_cls * neg_mask).sum() / (neg_mask.sum() + 1e-8)

            loss += cls_loss
            ret["cls_binary_loss"] = cls_loss
            ret["pos_pred"] = pos_pred
            ret["neg_pred"] = neg_pred

        ret["loss"] = loss
        return ret
