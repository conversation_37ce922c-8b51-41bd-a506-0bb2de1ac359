import torch.nn as nn
import torch.nn.functional as F


class FFN(nn.Module):
    """ Very simple multi-layer perceptron (also called FFN)"""

    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2, basic_type="linear"):
        super().__init__()
        self.basic_type = basic_type if output_dim > 0 else "identity"
        self.num_layers = num_layers
        h = [hidden_dim] * (num_layers - 1)
        self.ffn_layers = nn.ModuleList(self.basic_layer(n, k) for n, k in zip([input_dim] + h, h + [output_dim]))

    def forward(self, x):
        for i, layer in enumerate(self.ffn_layers):
            x = F.relu(layer(x)) if i < self.num_layers - 1 else layer(x)
        return x

    def basic_layer(self, n, k):
        if self.basic_type == "linear":
            return nn.Linear(n, k)
        elif self.basic_type == "conv":
            return nn.Conv2d(n, k, kernel_size=1, stride=1)
        elif self.basic_type == "identity":
            return nn.Identity()
        else:
            raise NotImplementedError


class ADMapOutputHead(nn.Module):
    def __init__(self, input_dim=256, hidden_dim=512, output_dim=503, num_layers=2):
        super(ADMapOutputHead, self).__init__()
        self.head = FFN(input_dim, hidden_dim, output_dim, num_layers)

    def forward(self, seq_feature):
        out = self.head.forward(seq_feature)  # B,N,S,6
        return out
