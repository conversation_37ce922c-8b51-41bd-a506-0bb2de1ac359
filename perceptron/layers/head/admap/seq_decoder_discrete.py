import torch
import torch.nn as nn
from perceptron.layers.head.admap.base_transformer import (
    TransformerEncoderLayer,
    TransformerEncoder,
    TransformerDecoderLayer,
    TransformerDecoder,
)


class TransDiscreteSeqDecoder(nn.Module):
    def __init__(
        self,
        in_channels,
        d_model=512,
        nhead=8,
        num_encoder_layers=6,
        num_decoder_layers=6,
        dim_feedforward=2048,
        dropout=0.1,
        activation="relu",
        normalize_before=False,
        return_intermediate_dec=False,
        use_checkpoint=True,
        flag_mf=True,
    ):
        super().__init__()
        self.d_model = d_model
        self.embedding_dim = d_model
        self.flag_mf = flag_mf
        ret_inter = return_intermediate_dec
        # input
        self.input_proj = nn.Conv2d(in_channels, d_model, kernel_size=1)

        # encoder
        encoder_layer = TransformerEncoderLayer(d_model, nhead, dim_feedforward, dropout, activation, normalize_before)
        encoder_norm = nn.LayerNorm(d_model) if normalize_before else None
        self.encoder = TransformerEncoder(encoder_layer, num_encoder_layers, encoder_norm, use_checkpoint)
        # decoder
        decoder_layer = TransformerDecoderLayer(d_model, nhead, dim_feedforward, dropout, activation, normalize_before)
        decoder_norm = nn.LayerNorm(d_model)
        self.decoder = TransformerDecoder(decoder_layer, num_decoder_layers, decoder_norm, ret_inter, use_checkpoint)

        # padding pe
        self.learnable_pad_pe = nn.Embedding(3, self.embedding_dim)

        self._init_embedding()
        self._reset_parameters()

    def _init_embedding(self):
        self.pos_embeddings = nn.Embedding(200, self.embedding_dim)
        self.vertex_embed = nn.Embedding(503 * 3, self.embedding_dim)
        self.start_embeddings = nn.Embedding(1, self.embedding_dim)
        if self.flag_mf:
            self.pre_pos_embeddings = nn.Embedding(200, self.embedding_dim)
            self.pre_vertex_embed = nn.Embedding(503 * 3, self.embedding_dim)
            self.pre_start_embeddings = nn.Embedding(1, self.embedding_dim)

    def _embed_inputs_new(self, pre_seqs, seqs):  # seqs: S,B
        seq_len, B = seqs.shape[:2]
        # Position embeddings
        # pos_embeddings = self.pos_embeddings(
        #     (torch.arange(seq_len, device=seqs.device) / self.coord_dim).floor().long())  #S,C
        pos_embeddings = self.pos_embeddings(torch.arange(seq_len, device=seqs.device).long())  # S,C
        # Coord indicators (x, y, z(optional))
        # coord_embeddings = self.coord_embed(
        #     torch.arange(seq_len, device=seqs.device) % self.coord_dim)  #S,C
        # Discrete vertex value embeddings
        seqs_tmp = seqs.clone()
        seqs_tmp[1::3, :] += 503
        seqs_tmp[2::3, :] += 503 * 2
        vert_embeddings = self.vertex_embed(seqs_tmp)  # S,B,C
        # Aggregate embeddings
        embeddings = vert_embeddings + (pos_embeddings)[:, None]  # S,B,C

        if pre_seqs is not None:
            pre_seqs_tmp = pre_seqs.to(seqs_tmp.device)
            pre_seq_len, B = pre_seqs.shape[:2]
            pre_pos_embeddings = self.pre_pos_embeddings(torch.arange(pre_seq_len, device=pre_seqs_tmp.device).long())
            # pre_seqs_tmp = pre_seqs.clone()
            pre_seqs_tmp[1::3, :] += 503
            pre_seqs_tmp[2::3, :] += 503 * 2
            pre_vert_embeddings = self.pre_vertex_embed(pre_seqs_tmp)  # S,B,C
            pre_embeddings = pre_vert_embeddings + (pre_pos_embeddings)[:, None]  # S,B,C
            # Add start embeddings
            embeddings = torch.cat(
                [
                    pre_embeddings,
                    self.pre_start_embeddings(torch.tensor(0).to(embeddings.device)).repeat(1, B, 1),
                    embeddings,
                ],
                dim=0,
            )  # M+1+S,B,C
        else:
            # Add start embeddings
            embeddings = torch.cat(
                [self.start_embeddings(torch.tensor(0).to(embeddings.device)).repeat(1, B, 1), embeddings], dim=0
            )  # S+1,B,C

        return embeddings

    def embed_inputs_parallel(self, seqs):
        # seqs: [N, S, B]
        N, S, B = seqs.shape
        pos_index = torch.arange(S, device=seqs.device).long()[None, :, None].repeat(N, 1, B)  # [N, S, B]
        pos_embeddings = self.pos_embeddings(pos_index)  # [N, S, B, C]

        seqs_tmp = seqs.clone()
        seqs_tmp[:, 1::3, :] += 503
        seqs_tmp[:, 2::3, :] += 503 * 2
        vert_embeddings = self.vertex_embed(seqs_tmp)  # [N, S, B, C]

        # Aggregate embeddings
        embeddings = vert_embeddings + pos_embeddings  # [N, S, B, C]

        # Add start embeddings
        embeddings = torch.cat(
            [self.start_embeddings(torch.tensor(0, device=embeddings.device)).repeat(N, 1, B, 1), embeddings], dim=1
        )  # [N, S+1,B,C]

        return embeddings  # # [N, S+1,B,C]

    # @force_fp32(apply_to=("src", "src_pe", "tgt", "tgt_pe", "memory"))
    def forward(
        self, src, src_pe, tgt, tgt_pe=None, pre_seq=None, cur2pre_idx=None, pre_seq_pe=None, pre_npt=None
    ):  # src: B,C,H,W  src_pe:B,C,H,W  tgt: B,N,S
        # check shape
        assert src.shape[0] == src_pe.shape[0] == tgt.shape[0] == tgt_pe.shape[0], "bs should be the same"
        # src-init
        src_feat = self.input_proj(src)  # (B, C, H, W)
        src_feat = src_feat.flatten(2).unsqueeze(0).permute(0, 3, 1, 2).squeeze(0)  # (H* W, B, C)
        # src-pe
        src_pos_embed = src_pe
        src_pos_embed = src_pos_embed.flatten(2).unsqueeze(0).permute(0, 3, 1, 2).squeeze(0)  # (H* W, B, C)
        # forward
        memory = self.encoder.forward(src_feat, None, None, src_pos_embed)  # (H* W, B, C)

        output = []
        # tgt-init
        tgt_feat = tgt.permute(1, 2, 0)  # (N, S, B)
        N, S, B = tgt_feat.shape[0], tgt_feat.shape[1], tgt_feat.shape[2]

        # -------------pre_seq--------------------
        all_tgt_tmp_list = []
        all_new_query_pos_list = []
        pre_seq_pe_list = []
        if pre_seq:
            pre_seq = torch.tensor(pre_seq).permute(1, 2, 0)  # (M, S, B)
            for i in range(N):
                tgt_tmp = tgt_feat[i]  # S, B
                pre_seq_tmp = None
                if pre_seq is not None:
                    tmp_new_query_pos = tgt_pe  # [B, N, S, C]
                    if len(cur2pre_idx) > i and cur2pre_idx[i] != -1:
                        pre_seq_tmp = pre_seq[cur2pre_idx[i]]
                        all_tgt_tmp_i = self._embed_inputs_new(pre_seq_tmp, tgt_tmp)  # S+1, B,C
                        all_tgt_tmp_list.append(all_tgt_tmp_i)  # M, S+1, B,C
                        all_new_query_pos_list.append(tmp_new_query_pos[:, cur2pre_idx[i], :, :])  # [1, S, C]
                        pre_seq_pe_list.append(pre_seq_pe[:, cur2pre_idx[i], :, :])

        if all_tgt_tmp_list:
            all_tgt_tmp = torch.stack(all_tgt_tmp_list).squeeze(2).transpose(0, 1)  # S+1+npt, M, C
            # all_tgt_pos_embed = None
            all_memory = memory.repeat(1, all_tgt_tmp.shape[1], 1)  # H*W, M, C
            all_S = all_tgt_tmp.shape[0]
            all_update_src_pe = src_pos_embed.repeat(1, all_tgt_tmp.shape[1], 1)  # (H* W, B*M, C)
            causal_mask = torch.triu(torch.ones(all_S, all_S), diagonal=1).bool().to(src_feat.device)  # S,S

            all_pad_pe = self.learnable_pad_pe.weight[None, None].expand(
                B, all_tgt_tmp.shape[1], 3, self.embedding_dim
            )  # [1, 1, 3, C]

            all_query_pos = torch.stack(all_new_query_pos_list).transpose(0, 1)  # [B, M, S, C]
            pre_seq_pos = torch.stack(pre_seq_pe_list).transpose(0, 1)
            all_query_pos = torch.cat(
                [pre_seq_pos, all_pad_pe, all_query_pos], dim=2
            )  # [B, M, npt+3+S, C]   pre_seq_pe: [B, M, npt, C]

            length = all_tgt_tmp.shape[0]
            all_new_query_pos = all_query_pos[:, :, :length, :]  # [B, N, S+npt, C]
            all_new_query_pos = all_new_query_pos.flatten(0, 1).permute(1, 0, 2)  # [S+npt, B*M, C]

            hs = self.decoder.forward(
                all_tgt_tmp, all_memory, causal_mask, None, None, None, all_update_src_pe, all_new_query_pos
            )  # M,S,B,C
            hs = hs[-1]  # S+1+npt,N,C
            if all_S > S + 1:
                output = hs[pre_npt * 3 :, :, :].transpose(0, 1).unsqueeze(0)
            else:
                output = hs.transpose(0, 1).unsqueeze(0)  # B,N,S+1,C

        # ------------cur_tgt--------------------
        embed_tgt = self.embed_inputs_parallel(tgt_feat)  # [N, S+1, B, C]

        tgt_dec = embed_tgt.permute(1, 0, 2, 3).flatten(1, 2)  # [N, S+1, B, C] -> [S+1, N, B, C] -> [S+1, N*B, C]
        update_memory = memory.repeat(1, N, 1)  # [h*w, N*B, C]
        update_src_pe = src_pos_embed.repeat(1, N, 1)  # (H* W, B*N, C)
        causal_mask = torch.triu(torch.ones(S + 1, S + 1), diagonal=1).bool().to(src_feat.device)  # S, S

        query_pos = tgt_pe
        pad_pe = self.learnable_pad_pe.weight[None, None].expand(B, N, 3, self.embedding_dim)  # [1, 1, 3, C]

        query_pos = torch.cat([pad_pe, query_pos], dim=2)  # [1, 1, 3+S, C]
        length = tgt_dec.shape[0]
        new_query_pos = query_pos[:, :, :length, :]  # [B, N, S, C]
        new_query_pos = new_query_pos.transpose(0, 1).flatten(0, 1).permute(1, 0, 2)

        # new_query_pos = new_query_pos.flatten(0, 1).permute(1, 0, 2)  # [B, N, S, C] --> [B*N, S, C] - > [S, B*N, C]

        hs = self.decoder.forward(tgt_dec, update_memory, causal_mask, None, None, None, update_src_pe, new_query_pos)[
            -1
        ]  # [S+1, N*B, C]  TODO: 这步操作会有 fp16 溢出
        C = hs.shape[-1]
        hs = hs.reshape(S + 1, N, B, C).permute(2, 1, 0, 3)  # [S+1, N*B, C] -> [S+1, N, B, C] -> B, N, S+1, C

        if all_tgt_tmp_list:
            return output, hs
        else:
            return [], hs

    def _reset_parameters(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
