import torch
import torch.nn as nn
import numpy as np
from torch.nn import functional as F
from perceptron.layers.blocks_3d.mmdet3d.bevdepth_lss_fpn import ASPP
from perceptron.layers.blocks_3d.seg3d.unet import UNet
from perceptron.layers.losses.seg2d import MultiClassFocalLoss, DiceMultiClassLoss
from perceptron.layers.losses.det3d import SigmoidFocalClassificationLoss
from perceptron.layers.blocks_3d.prediction3d.modules import SinePositionalEncoding3D
from perceptron.utils.e2e_utils.utils import pos2posemb3d
from perceptron.models.multisensor_fusion.base import ForceFp32
from perceptron.layers.blocks_2d.det3d.bev_fpn import BottleRep, QARepVGGBlock
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet.models.utils import build_transformer
from mmcv.cnn.bricks.transformer import build_positional_encoding
from mmcv.cnn import ConvModule


class DecoderBlock3D(torch.nn.Module):
    def __init__(
        self, in_channels=256, out_channels=128, skip_dim=256, residual=True, factor=2, scale_size=(128, 400, 8)
    ):
        super().__init__()

        mid_channels = out_channels // factor

        self.conv = nn.Sequential(
            nn.Upsample(size=scale_size, mode="trilinear", align_corners=True),
            nn.Conv3d(in_channels, mid_channels, 3, padding=1, bias=False),
            nn.BatchNorm3d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv3d(mid_channels, out_channels, 1, padding=0, bias=False),
            nn.BatchNorm3d(out_channels),
            nn.ReLU(inplace=True),
        )

        if residual:
            self.shortcut = nn.Sequential(
                nn.Upsample(size=scale_size, mode="trilinear", align_corners=True),
                nn.Conv3d(skip_dim, out_channels, 1),
                nn.BatchNorm3d(out_channels),
                nn.ReLU(inplace=True),
            )
            self.relu = nn.ReLU(inplace=True)

    def forward(self, x, skip):
        x = self.conv(x)
        if self.shortcut is not None:
            shortcut = self.shortcut(skip)
            x = x + shortcut
            x = self.relu(x)
        return x


class Decoder3D(nn.Module):
    def __init__(self, dim=256, blocks=[], residual=True, factor=2, scale_size=[]):
        super().__init__()

        decoder_layers = list()

        in_channels = dim
        for i in range(len(blocks)):
            out_channels = blocks[i]
            decoder_layers.append(DecoderBlock3D(in_channels, out_channels, dim, residual, factor, scale_size[i]))
            in_channels = out_channels

        self.decoder_layers = nn.Sequential(*decoder_layers)

        self.merge_layer = nn.Conv3d(
            in_channels=out_channels, out_channels=out_channels, kernel_size=(1, 1, scale_size[-1][-1])
        )

    def forward(self, x):
        y = x
        for decoder_layer in self.decoder_layers:
            y = decoder_layer(y, x)
        return self.merge_layer(y)


def bilinear_grid_sample(im, grid, align_corners=False):
    n, c, h, w = im.shape
    gn, gh, gw, _ = grid.shape
    assert n == gn
    x = grid[:, :, :, 0]
    y = grid[:, :, :, 1]
    if align_corners:
        x = ((x + 1) / 2) * (w - 1)
        y = ((y + 1) / 2) * (h - 1)
    else:
        x = ((x + 1) * w - 1) / 2
        y = ((y + 1) * h - 1) / 2
    x = x.view(n, -1)
    y = y.view(n, -1)
    x0 = torch.floor(x).long()
    y0 = torch.floor(y).long()
    x1 = x0 + 1
    y1 = y0 + 1
    wa = ((x1 - x) * (y1 - y)).unsqueeze(1)
    wb = ((x1 - x) * (y - y0)).unsqueeze(1)
    wc = ((x - x0) * (y1 - y)).unsqueeze(1)
    wd = ((x - x0) * (y - y0)).unsqueeze(1)
    # Apply default for grid_sample function zero padding
    # inputs = torch.ones(1, 128, 3, 3)
    filters = torch.Tensor([[[[1]]]]).expand(im.shape[1], 1, 1, 1).cuda()  # torch.randn(8, 4, 3, 3)
    im_padded = F.conv2d(im, filters, padding=1, groups=im.shape[1])
    # im_padded = F.pad(im, pad=[1, 1, 1, 1], mode='constant', value=0)
    padded_h = h + 2
    padded_w = w + 2
    # save points positions after padding
    x0, x1, y0, y1 = x0 + 1, x1 + 1, y0 + 1, y1 + 1
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    x0 = torch.where(x0 < 0, torch.tensor(0).to(device), x0)
    x0 = torch.where(x0 > padded_w - 1, torch.tensor(padded_w - 1).to(device), x0)
    x1 = torch.where(x1 < 0, torch.tensor(0).to(device), x1)
    x1 = torch.where(x1 > padded_w - 1, torch.tensor(padded_w - 1).to(device), x1)
    y0 = torch.where(y0 < 0, torch.tensor(0).to(device), y0)
    y0 = torch.where(y0 > padded_h - 1, torch.tensor(padded_h - 1).to(device), y0)
    y1 = torch.where(y1 < 0, torch.tensor(0).to(device), y1)
    y1 = torch.where(y1 > padded_h - 1, torch.tensor(padded_h - 1).to(device), y1)
    im_padded = im_padded.view(n, c, -1)
    x0_y0 = (x0 + y0 * padded_w).unsqueeze(1).expand(-1, c, -1)
    x0_y1 = (x0 + y1 * padded_w).unsqueeze(1).expand(-1, c, -1)
    x1_y0 = (x1 + y0 * padded_w).unsqueeze(1).expand(-1, c, -1)
    x1_y1 = (x1 + y1 * padded_w).unsqueeze(1).expand(-1, c, -1)
    Ia = torch.gather(im_padded, 2, x0_y0)
    Ib = torch.gather(im_padded, 2, x0_y1)
    Ic = torch.gather(im_padded, 2, x1_y0)
    Id = torch.gather(im_padded, 2, x1_y1)
    return (Ia * wa + Ib * wb + Ic * wc + Id * wd).view(n, c, gh, gw)


class FusionEncoder(nn.Module):
    def __init__(
        self,
        use_elementwise: bool = True,
        input_channel: int = 512,
        output_channel: int = 256,
        reduction: int = 2,
        voxel_size: float = 0.2,
        pc_range: list = [-15.2, 0.0, -5.0, 15.2, 81.6, 3.0],
        H: int = 408,
        W: int = 152,
        use_timestamp=False,
        *args,
        **kwargs,
    ):

        super().__init__()
        self.use_elementwise = use_elementwise
        self.use_timestamp = use_timestamp
        if not self.use_elementwise:
            self.att = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(output_channel, output_channel, kernel_size=1, stride=1),
                nn.Sigmoid(),
            )
            self.reduce_conv = nn.Sequential(
                nn.Conv2d(output_channel, output_channel, 3, padding=1, bias=False),
                nn.BatchNorm2d(output_channel, eps=1e-3),
                nn.ReLU(True),
            )
            input_channel = input_channel if not self.use_timestamp else input_channel + 1
            self.fusion_conv = nn.Sequential(
                BottleRep(input_channel, output_channel // 2, basic_block=QARepVGGBlock),
                BottleRep(output_channel // 2, output_channel, basic_block=QARepVGGBlock),
            )
        for m in self.modules():  # ?
            if isinstance(m, nn.BatchNorm2d):
                m.eps = 1e-3
        self.H = H
        self.W = W
        self.ref_xy = self.init_ref_xy(H, W)
        self.voxel_size_ = torch.Tensor(voxel_size).cuda()
        self.voxel_coord_ = torch.Tensor([pc_range[i] + voxel_size[i] for i in range(2)]).cuda()

    def init_ref_xy(self, H, W, device="cuda", dtype=torch.float):
        """the 2d reference points.
        Args:
            H, W: spatial shape of bev.
            evice (obj:`device`): The device where
                reference_points should be.
        Returns:
            Tensor: reference points.
        """

        ref_y, ref_x = torch.meshgrid(
            torch.range(0, H - 1, dtype=dtype, device=device), torch.range(0, W - 1, dtype=dtype, device=device)
        )
        ref_xy = torch.stack((ref_x, ref_y), -1)

        return ref_xy

    def bev_transform(self, bev_feat, cur_ego2global, last_ego2global, bda_mat=None):
        bs, c, h, w = bev_feat.shape

        ref_xy = self.ref_xy.clone().unsqueeze(0).repeat(bs, 1, 1, 1)  # 1 x 128 x 128 x 2
        ref_xy = ref_xy * self.voxel_size_[:2] + self.voxel_coord_[:2]
        ref_4d = torch.cat(
            [
                ref_xy,
                ref_xy.new_tensor(np.ones(ref_xy.shape[:-1] + (1,)), device=ref_xy.device),
                ref_xy.new_tensor(np.ones(ref_xy.shape[:-1] + (1,)), device=ref_xy.device),
            ],
            -1,
        )  # 1 x 128 x 128 x 4
        if bda_mat is not None:
            curego2lastego = bda_mat.inverse()
        else:
            # last_ego2global = torch.mean(last_ego2global, 1)
            # cur_ego2global = torch.mean(cur_ego2global, 1)
            curego2lastego = last_ego2global.inverse() @ cur_ego2global
        curego2lastego = curego2lastego.unsqueeze(1).unsqueeze(1)
        pix_coords = curego2lastego.to(ref_4d.dtype) @ ref_4d.unsqueeze(-1)
        pix_coords_xy = pix_coords.squeeze(-1)[..., :2]
        pix_coords_xy = (pix_coords_xy - self.voxel_coord_[:2]) / self.voxel_size_[:2]

        pix_coords_xy[..., 0] /= w - 1
        pix_coords_xy[..., 1] /= h - 1
        pix_coords_xy = (pix_coords_xy - 0.5) * 2
        # pix_coords_xy = pix_coords_xy.clamp(-1,1)

        return bilinear_grid_sample(
            bev_feat,
            pix_coords_xy,
        )
        return F.grid_sample(
            bev_feat,
            pix_coords_xy,
        )

    def forward(self, prev_bev_feats, cur_bev_feats, prev_l2g, cur_12g, timestamp=None):
        # assert x1.shape == x2.shape, f"shape: {x1.shape} != {x2.shape}"
        # if self.use_elementwise:
        #     return x1 + x2
        # else:

        prev_transferd_bevfeats = self.bev_transform(prev_bev_feats, prev_l2g, cur_12g)
        x = torch.cat([prev_transferd_bevfeats, cur_bev_feats], dim=1)
        if self.use_timestamp:
            timestamp = timestamp[:, None, None, None].expand(len(timestamp), 1, self.H, self.W)
            x = torch.cat([x, timestamp], dim=1)
        x = self.fusion_conv(x)
        return self.reduce_conv(x * self.att(x))


class BEVFreespaceSegHead(nn.Module):
    """
    多分类Freespace Head
    Args:
        in_channel: 输入feature的 feature dim
        num_classes: 分割时的分类数目, 应该设置为车道线分类数目+1(背景类), 目前暂时先只区分是否为车道线, 默认num_classes=2
        target_assigner: only when using lidarseg gt need to asign label
        loss_weight: loss weight , type=dict
        binary_seg: whether using binary classifier head, default = False

    """

    def __init__(
        self,
        in_channel=384,
        num_classes=3,
        target_assigner=None,
        loss_weight=dict(loss_cls=10, loss_binary=10, loss_dice=10, loss_vismask=10),
        binary_seg=False,
        aspp_cfg=None,
        unet_cfg=None,
        upsample_cfg=None,
        modal=["Camera", "Radar", "Lidar"],
        use_mask=True,
        mask_loss_type=0,
        use_weighted_mask=False,
        dis_weight=False,
        transformer=None,
        decoder3d=None,
        decoder2d=None,
        ignore_label=255,
        focus_area_loss=False,
        pc_range=[-15.2, 0.0, -5.0, 15.2, 81.6, 3.0],
        query_shape=[19, 51, 2],
        multiframe_bev_fuse_cfg=None,
        img_encoder_type="PETR",
        as_two_stage=False,
        positional_encoding=None,
        fusion_dim=256,
        lidar_init=False,
        lidar_feat_crop=None,
        sub_img_inds=None,
        surround=False,
        norm_cfg=dict(type="BN2d"),
        height_layer_num=1,  # occ分层预测的层数
        use_visible_head=False,  # 可见性预测
    ):
        super(BEVFreespaceSegHead, self).__init__()
        self.in_channel = in_channel
        self.num_classes = num_classes
        assert self.num_classes > 1
        self.loss_weight = loss_weight
        self.binary_seg = binary_seg
        self.aspp_cfg = aspp_cfg
        self.unet_cfg = unet_cfg
        self.upsample_cfg = upsample_cfg
        self.modal = modal
        self.use_mask = use_mask
        self.use_weighted_mask = use_weighted_mask
        self.dis_weight = dis_weight
        self.ignore_label = ignore_label
        self.mask_loss_type = mask_loss_type
        print(
            f"use_mask: {use_mask}, use_weighted_mask: {use_weighted_mask}, norm_cfg:{norm_cfg}, sub_img_inds:{sub_img_inds}, height_layer_num:{height_layer_num}"
        )
        self.focus_area_loss = focus_area_loss
        self.use_visible_head = use_visible_head
        print(f"use vis head: {use_visible_head}")
        self.query_shape = query_shape
        self.img_encoder_type = img_encoder_type
        self.as_two_stage = as_two_stage
        self.lidar_init = lidar_init
        self.lidar_feat_crop = lidar_feat_crop
        self.sub_img_inds = sub_img_inds
        self.surround = surround

        self.target_assigner = target_assigner
        if height_layer_num > 1:
            self.crit = MultiClassFocalLoss(
                num_classes=self.num_classes,
                ignore_labels=[ignore_label],
                alpha=1,
                gamma=2,
                unsqueeze_index=-1,
            )
        else:
            # unsqueeze_index = 3
            self.crit = MultiClassFocalLoss(
                num_classes=self.num_classes,
                ignore_labels=[ignore_label],
                alpha=1,
                gamma=2,
            )
        self.crit_dice = DiceMultiClassLoss(self.num_classes, ignore_labels=[0, ignore_label], use_softmax=False)
        if use_visible_head:
            self.crit_vismask = MultiClassFocalLoss(num_classes=2, alpha=1, gamma=2)
        self.norm_cfg = norm_cfg
        # Yunqi Confi
        self.pc_range = pc_range

        # 08.15 新增：occ分两层预测
        self.height_layer_num = height_layer_num
        self.use_visible_head = use_visible_head

        if "Camera" in modal:
            if self.img_encoder_type == "PETR":
                self.init_petr_img_head(transformer, decoder3d, decoder2d)
            elif self.img_encoder_type == "Deformable":
                self.init_deform_img_head(transformer, positional_encoding, decoder2d)
            else:
                raise NotImplementedError
            if self.lidar_init:

                self.lidar_init_conv = ConvModule(
                    384,
                    self.embed_dims,
                    kernel_size=3,
                    stride=2,  # SIZE
                    padding=1,
                    conv_cfg=dict(type="Conv2d"),
                    norm_cfg=self.norm_cfg,
                )

        if "Lidar" in modal or "Lidar_Cam_init":
            if self.aspp_cfg is not None:
                self.aspp = ASPP(**self.aspp_cfg)

            if self.unet_cfg is not None:
                self.unet = UNet(**self.unet_cfg)

            if self.upsample_cfg is not None:  # SIZE mode 2 lidar feat 不需要上采样
                self.upsample_layer = nn.Upsample(**self.upsample_cfg)
        if "Lidar" in modal or "Camera" in modal:
            self.fusion_dim = fusion_dim
            bevfusion_conv = [
                ConvModule(
                    self.fusion_dim,
                    128,
                    kernel_size=3,
                    padding=1,
                    conv_cfg=dict(type="Conv2d"),
                    norm_cfg=self.norm_cfg,
                ),
                ConvModule(
                    128,
                    64,
                    kernel_size=3,
                    padding=1,
                    conv_cfg=dict(type="Conv2d"),
                    norm_cfg=self.norm_cfg,
                ),
                ConvModule(
                    64,
                    self.in_channel,  # 分类器channel
                    kernel_size=1,
                    padding=0,
                    conv_cfg=dict(type="Conv2d"),
                    norm_cfg=self.norm_cfg,
                ),
            ]
            self.bevfusion_conv = nn.Sequential(*bevfusion_conv)
        # Classifier
        self.class_predictor = nn.Sequential(
            nn.Conv2d(self.in_channel, self.num_classes * self.height_layer_num, 1),
        )
        if self.use_visible_head:
            self.vismask_head = nn.Sequential(
                nn.Conv2d(self.in_channel, 2, 1),
            )
        else:
            self.vismask_head = None
        if self.binary_seg:
            self.class_predictor_binary = nn.Sequential(
                nn.Conv2d(self.in_channel, 1, 1),
            )
            self.crit_binary = SigmoidFocalClassificationLoss()
            assert "loss_binary" in self.loss_weight

        # 多帧融合
        self.multiframe_bev_fuse_cfg = multiframe_bev_fuse_cfg
        if self.multiframe_bev_fuse_cfg:
            self.fusion_module = FusionEncoder(**self.multiframe_bev_fuse_cfg)

    # 定义petr head 提取img 表征
    def init_petr_img_head(self, transformer, decoder3d, decoder2d):
        self.depth_num = 64
        self.depth_start = 1.0
        self.depth_end = self.pc_range[4]
        self.hidden_dim = 256
        self.rv_embedding = nn.Sequential(
            nn.Linear(self.depth_num * 3, self.hidden_dim * 4),
            nn.ReLU(inplace=True),
            nn.Linear(self.hidden_dim * 4, self.hidden_dim),
        )
        self.with_multiview = False
        if self.with_multiview:
            self.adapt_pos3d = nn.Sequential(
                nn.Conv2d(self.hidden_dim * 3 // 2, self.hidden_dim * 4, kernel_size=1, stride=1, padding=0),
                nn.ReLU(inplace=True),
                nn.Conv2d(self.hidden_dim * 4, self.hidden_dim, kernel_size=1, stride=1, padding=0),
            )
            self.positional_encoding = SinePositionalEncoding3D(num_feats=self.hidden_dim // 2, normalize=True)

        x = (torch.arange(self.query_shape[0]) + 0.5) / self.query_shape[0]
        y = (torch.arange(self.query_shape[1]) + 0.5) / self.query_shape[1]
        z = (torch.arange(self.query_shape[2]) + 0.5) / self.query_shape[2]
        grid = torch.meshgrid(x, y, z)
        self.reference_points = torch.cat(
            [grid[0].reshape(-1)[..., None], grid[1].reshape(-1)[..., None], grid[2].reshape(-1)[..., None]], dim=-1
        ).cuda()
        self.query_embedding = nn.Sequential(
            nn.Linear(384, self.hidden_dim),  # for vit
            nn.ReLU(inplace=True),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )

        self.transformer = build_transformer(transformer)
        self.transformer.init_weights()

        if decoder3d is not None:
            assert decoder2d is None
            self.decoder3d = Decoder3D(**decoder3d)
        else:
            assert decoder2d is not None
            self.decoder2d = None

    def init_deform_img_head(self, transformer, positional_encoding, decoder2d):
        self.bev_h = self.query_shape[1]
        self.bev_w = self.query_shape[0]
        self.real_w = self.pc_range[3] - self.pc_range[0]
        self.real_h = self.pc_range[4] - self.pc_range[1]

        self.positional_encoding = build_positional_encoding(positional_encoding)
        if self.as_two_stage:
            transformer["as_two_stage"] = self.as_two_stage
        self.transformer = build_transformer(transformer)
        self.transformer.init_weights()
        self.embed_dims = self.transformer.embed_dims
        # self.view_num = self.transformer.view_num
        if not self.as_two_stage:
            self.bev_embedding = nn.Embedding(self.bev_h * self.bev_w, self.embed_dims)
        if decoder2d is not None:
            decoder_list = []
            if decoder2d["scale_factor"] > 1:
                decoder_list.append(
                    nn.Upsample(scale_factor=decoder2d["scale_factor"], mode="bilinear", align_corners=True)
                )
            hidden_dims = decoder2d["hidden_dims"]
            out_dims = decoder2d["out_dims"]
            num_layers = decoder2d["num_layers"]
            assert num_layers > 2
            decoder_list.append(
                ConvModule(
                    self.embed_dims,
                    hidden_dims,
                    kernel_size=3,
                    padding=1,
                    conv_cfg=dict(type="Conv2d"),
                    norm_cfg=self.norm_cfg,
                )
            )
            for _ in range(num_layers - 2):
                decoder_list.append(
                    ConvModule(
                        hidden_dims,
                        hidden_dims,
                        kernel_size=3,
                        padding=1,
                        conv_cfg=dict(type="Conv2d"),
                        norm_cfg=self.norm_cfg,
                    )
                )

            decoder_list.append(
                ConvModule(
                    hidden_dims,
                    out_dims,
                    kernel_size=1,
                    padding=0,
                    conv_cfg=dict(type="Conv2d"),
                    norm_cfg=self.norm_cfg,
                )
            )

            self.decoder_2d = nn.Sequential(*decoder_list)
        else:
            self.decoder_2d = None

    def assign_targets(self, gt):
        if self.target_assigner is None:
            targets_dict = {"bev_labels": gt}  # 不用在线label asign
        else:
            targets_dict = self.target_assigner.assign_targets(gt)
        return targets_dict

    @ForceFp32(apply_to=("img_metas"))
    def _rv_pe(self, img_feats, img_metas):
        BN, C, H, W = img_feats.shape
        pad_h, pad_w = img_metas["pad_shape"]
        # generate grid
        coords_h = torch.arange(H, device=img_feats[0].device).float() * pad_h / H
        coords_w = torch.arange(W, device=img_feats[0].device).float() * pad_w / W
        coords_d = (
            self.depth_start
            + torch.arange(self.depth_num, device=img_feats[0].device).float() * (self.depth_end - 1) / self.depth_num
        )
        coords_h, coords_w, coords_d = torch.meshgrid([coords_h, coords_w, coords_d])
        coords = torch.stack([coords_w, coords_h, coords_d, coords_h.new_ones(coords_h.shape)], dim=-1)
        H, W, D, C = coords.shape
        # inverse ida
        batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        coords = (
            img_metas["ida_mats"]
            .view(batch_size, sweep * num_cams, 1, 1, 1, 4, 4)
            .inverse()
            .matmul(coords.unsqueeze(-1))
        )
        # cam_to_ego
        # coords shape [B, N, H, W, D, 4, 1]
        coords = torch.cat((coords[..., :2, :] * coords[..., 2:3, :], coords[..., 2:, :]), 5)
        coords = torch.inverse(img_metas["lidar2imgs"]).view(batch_size, num_cams, 1, 1, 1, 4, 4).matmul(coords)
        # inverse bda
        if "bda_mat" in img_metas:
            coords = (
                img_metas["bda_mat"].unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 1, 4, 4)
                @ coords
            ).squeeze(-1)
        else:
            coords = coords.squeeze(-1)
        coords_3d = coords.view(batch_size * num_cams, H, W, D, C)
        # normalization
        coords_3d = (coords_3d[..., :3] - coords_3d.new_tensor(self.pc_range[:3])) / (
            coords_3d.new_tensor(self.pc_range[3:]) - coords_3d.new_tensor(self.pc_range[:3])
        )
        if self.with_multiview:
            coords_3d = inverse_sigmoid(coords_3d)
        # rv_pos_embeds: torch.Size([BN, H, W, C])
        rv_pos_embeds = self.rv_embedding(coords_3d.reshape(*coords_3d.shape[:-2], -1))
        if self.with_multiview:
            batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
            BN, C, H, W = img_feats.shape
            masks = img_feats.new_zeros((batch_size, num_cams, H, W))
            sin_embed = self.positional_encoding(masks)
            sin_embed = self.adapt_pos3d(sin_embed.flatten(0, 1))  # (BN, C, H, W )
            sin_embed = sin_embed.permute((0, 2, 3, 1))
            rv_pos_embeds = rv_pos_embeds + sin_embed
        return rv_pos_embeds

    @ForceFp32(apply_to=("img_metas"))
    def _rv_query_embed(self, ref_points, img_metas):
        pad_h, pad_w = img_metas["pad_shape"]
        ref_points = ref_points * (
            ref_points.new_tensor(self.pc_range[3:]) - ref_points.new_tensor(self.pc_range[:3])
        ) + ref_points.new_tensor(self.pc_range[:3])

        points = torch.cat([ref_points, ref_points.new_ones(*ref_points.shape[:-1], 1)], dim=-1)
        # map 3d ref points into 2d img
        sweep = 1

        if len(img_metas["lidar2imgs"].shape) == 4:
            batch_size, num_cams, _, _ = img_metas["lidar2imgs"].shape
        else:
            batch_size, sweep, num_cams, _, _ = img_metas["lidar2imgs"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        if "bda_mat" in img_metas:
            points = img_metas["bda_mat"].inverse().view(batch_size, 1, 4, 4).matmul(points.unsqueeze(-1))
        else:
            points = points.unsqueeze(-1)
        points = points.unsqueeze(1)
        points = img_metas["lidar2imgs"].view(batch_size, num_cams, 1, 4, 4).matmul(points)
        proj_points_clone = points.clone()

        # select valid ref point on img
        z_mask = proj_points_clone[..., 2:3, :].detach() > 0
        proj_points_clone[..., :3, :] = points[..., :3, :] / (
            points[..., 2:3, :].detach() + z_mask * 1e-6 - (~z_mask) * 1e-6
        )
        proj_points_clone = img_metas["ida_mats"].view(batch_size, num_cams, 1, 4, 4).matmul(proj_points_clone)
        proj_points_clone = proj_points_clone.squeeze(-1)
        mask = (
            (proj_points_clone[..., 0] < pad_w)
            & (proj_points_clone[..., 0] >= 0)
            & (proj_points_clone[..., 1] < pad_h)
            & (proj_points_clone[..., 1] >= 0)
        )
        mask &= z_mask.view(*mask.shape)

        # map 2d ref points back to 3d with multi depth
        coords_d = (
            1 + torch.arange(self.depth_num, device=ref_points.device).float() * (self.pc_range[4] - 1) / self.depth_num
        )
        batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        projback_points = (
            img_metas["ida_mats"]
            .view(batch_size, num_cams, 1, 4, 4)
            .inverse()
            .matmul(proj_points_clone.unsqueeze(-1))
            .squeeze(-1)
        )
        projback_points = torch.einsum("bvnc, d -> bvndc", projback_points, coords_d)
        projback_points = torch.cat(
            [projback_points[..., :3], projback_points.new_ones(*projback_points.shape[:-1], 1)], dim=-1
        ).unsqueeze(-1)

        projback_points = (
            torch.inverse(img_metas["lidar2imgs"]).view(batch_size, num_cams, 1, 1, 4, 4).matmul(projback_points)
        )
        if "bda_mat" in img_metas:
            projback_points = (
                img_metas["bda_mat"].unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 4, 4)
                @ projback_points
            ).squeeze(-1)
        else:
            projback_points = projback_points.squeeze(-1)
        projback_points = (projback_points[..., :3] - projback_points.new_tensor(self.pc_range[:3])) / (
            projback_points.new_tensor(self.pc_range[3:]) - projback_points.new_tensor(self.pc_range[:3])
        )
        rv_embeds = self.rv_embedding(projback_points.reshape(*projback_points.shape[:-2], -1))
        rv_embeds = (rv_embeds * mask.unsqueeze(-1)).sum(dim=1)
        return rv_embeds

    def forward(
        self,
        data_shape,
        img_feats,
        radar_points,
        radar_output,
        lidar_feats,
        freespace,
        freespace_mask,
        bev_embedding,
        img_metas,
        l2g,
        timestamp,
        prev_bev=None,
        only_bev=False,
        test=False,
        **kwargs,
    ):
        batch_size, num_frame, N, C, H, W = data_shape

        if lidar_feats is not None:
            if self.lidar_feat_crop is not None:
                crop_h = self.lidar_feat_crop["crop_h"]
                crop_w = self.lidar_feat_crop["crop_w"]
                lidar_feats = lidar_feats[:, :, :, crop_h[0] : crop_h[1], crop_w[0] : crop_w[1]]
            x = self.forward_lidar_bev_encoder(lidar_feats)
        else:
            x = None
        if img_feats is not None:
            if self.sub_img_inds is not None:
                _, imgC, imgH, imgW = img_feats[0].size()
                img_feats = img_feats[0].view(batch_size, N, imgC, imgH, imgW)
                img_feats = img_feats[:, self.sub_img_inds].view(-1, imgC, imgH, imgW)
                img_feats = [img_feats]
                ida_mats = img_metas[0]["ida_mats"][:, :, self.sub_img_inds]
                lidar2imgs = img_metas[0]["lidar2imgs"][:, :, self.sub_img_inds]
                img_metas[0]["ida_mats"] = ida_mats
                img_metas[0]["lidar2imgs"] = lidar2imgs
                N = len(self.sub_img_inds)
                data_shape = (batch_size, num_frame, N, C, H, W)

            x_img = self.forward_img_bev_encoder(
                data_shape, img_feats, img_metas, prev_bev=prev_bev, only_bev=only_bev, test=test, lidar_features=x
            )

        if "Lidar" in self.modal or "Camera" in self.modal:
            if "Lidar" in self.modal and "Camera" in self.modal:
                x = torch.cat((x, x_img), dim=1)
            elif "Lidar" not in self.modal and "Camera" in self.modal:
                x = x_img
            x = self.bevfusion_conv(x)
        # multiframe fuse
        if self.multiframe_bev_fuse_cfg:
            nf = x.shape[0] // batch_size
            assert nf <= 2
            if nf == 1:
                raise
                cur_x = x.reshape(batch_size, nf, *x.shape[1:])[:, 0]
                prev_x = cur_x
                prev_l2g = torch.stack([l2g[bid][0] for bid in range(batch_size)])
                cur_12g = torch.stack([l2g[bid][0] for bid in range(batch_size)])
            else:
                prev_x, cur_x = x.reshape(batch_size, nf, *x.shape[1:]).unbind(1)
                prev_x = prev_x.detach()
                prev_l2g = torch.stack([l2g[bid][0] for bid in range(batch_size)])
                cur_12g = torch.stack([l2g[bid][1] for bid in range(batch_size)])
            if self.multiframe_bev_fuse_cfg.get("use_timestamp", False):

                timestamp = torch.tensor(timestamp, dtype=torch.float64).cuda()
                timestamp = timestamp.reshape(batch_size, nf)

                timestamp = timestamp[:, 1] - timestamp[:, 0]
                timestamp = (timestamp / 1e6).to(cur_x.dtype)

            else:
                timestamp = None

            x = self.fusion_module(prev_x, cur_x, prev_l2g, cur_12g, timestamp)

        # Predictor
        forward_ret_dict = {}

        class_output = self.class_predictor(x)  # bs, cls_nums, H_bev, W_bev
        class_output = class_output.permute(0, 2, 3, 1)  # bs, H_bev, W_bev, cls_nums
        if self.height_layer_num > 1:
            class_output = class_output.view(*class_output.shape[:-1], self.height_layer_num, self.num_classes)
        class_output = torch.softmax(class_output, -1)
        forward_ret_dict["pred_seg"] = class_output
        # ========== vismask head =============#
        if self.vismask_head is not None:
            vismask_output = self.vismask_head(x)
            vismask_output = vismask_output.permute(0, 2, 3, 1)
            vismask_output = torch.softmax(vismask_output, -1)
            forward_ret_dict.update({"vismask_pred": vismask_output})
        if self.binary_seg:
            class_output_binary = self.class_predictor_binary(x)  # bs, cls_nums, H_bev, W_bev
            forward_ret_dict["pred_seg_binary"] = class_output_binary

        if self.training and freespace is not None:
            if self.multiframe_bev_fuse_cfg:

                forward_ret_dict, loss_dict = self.get_loss(forward_ret_dict, freespace[:, -1:], freespace_mask[:, -1:])
                if self.multiframe_bev_fuse_cfg["sup_each_frame"]:
                    # Predictor
                    each_forward_ret_dict = {}

                    class_output = self.class_predictor(
                        torch.cat([prev_x[:, None], cur_x[:, None]], 1).flatten(0, 1)
                    )  # bs, cls_nums, H_bev, W_bev
                    class_output = class_output.permute(0, 2, 3, 1)  # bs, H_bev, W_bev, cls_nums
                    class_output = torch.softmax(class_output, -1)
                    each_forward_ret_dict["pred_seg"] = class_output
                    forward_ret_dict_, loss_dict_ = self.get_loss(
                        forward_ret_dict, freespace[:, -1:], freespace_mask[:, -1:]
                    )
                    for k, v in loss_dict_.items():
                        if "loss" in k:
                            loss_dict["each_" + k] = 0.2 * v

            else:
                forward_ret_dict, loss_dict = self.get_loss(forward_ret_dict, freespace, freespace_mask)
            return forward_ret_dict, loss_dict
        else:
            return forward_ret_dict, {}

    def forward_img_bev_encoder(
        self, data_shape, img_feats, img_metas, prev_bev=None, only_bev=False, test=False, lidar_features=None
    ):
        batch_size, num_frame, N, C, H, W = data_shape
        if self.img_encoder_type == "PETR":
            if isinstance(img_feats, list):
                assert len(img_feats) == len(img_metas)
                rv_pos_embeds = list()
                for img_feats_per, img_meta_per in zip(img_feats, img_metas):
                    rv_pos_embed = self._rv_pe(img_feats_per, img_meta_per)
                    rv_pos_embeds.append(torch.nan_to_num(rv_pos_embed))
            else:
                rv_pos_embeds = self._rv_pe(img_feats, img_metas)
            # Reference Points Query Embedding
            reference_points = self.reference_points
            if reference_points.ndim == 2:
                reference_points = reference_points.unsqueeze(0).repeat(batch_size, 1, 1)
            elif reference_points.ndim != 3:
                raise ValueError(f"unexcepted dim for reference points: {reference_points.shape}")
            rv_query_embeds = self.query_embedding(pos2posemb3d(reference_points))
            # Multi_camera Feature Fusion
            # start_time = time.time()

            outs_img, _ = self.transformer(
                x=None,
                x_img=img_feats[0],
                query_embed=rv_query_embeds,
                bev_pos_embed=None,
                rv_pos_embed=rv_pos_embeds[0],
                attn_masks=None,
            )

            outs_img = torch.nan_to_num(outs_img)[-1]

            # Camera Feature Algin to Lidar
            dim_x, dim_y, dim_z = self.query_shape
            outs_img = outs_img.view(batch_size, dim_x, dim_y, dim_z, -1).permute(0, 4, 1, 2, 3).contiguous()
            x_img = self.decoder3d(outs_img)
            x_img = x_img.squeeze(-1).permute(0, 1, 3, 2)
        elif self.img_encoder_type == "Deformable":
            num_cam = N
            bs, feat_dim, feat_h, feat_w = img_feats[0].shape
            bs = bs // num_cam
            # 摘出batch
            img_feats[0] = img_feats[0].view(bs, num_cam, feat_dim, feat_h, feat_w)

            dtype = img_feats[0].dtype
            object_query_embeds = None
            bev_queries = self.bev_embedding.weight.to(dtype)
            if self.lidar_init:
                lidar_init_features = self.lidar_init_conv(lidar_features).permute(0, 2, 3, 1).flatten(0, 2)
                lidar_init_features = lidar_init_features.reshape(bs, -1, self.embed_dims) + bev_queries[None, :, :]
                bev_queries = lidar_init_features

            bev_mask = torch.zeros((bs, self.bev_h, self.bev_w), device=bev_queries.device).to(dtype)
            bev_pos = self.positional_encoding(bev_mask).to(dtype)

            if only_bev:  # only use encoder to obtain BEV features, TODO: refine the workaround
                bev_embed = (
                    self.transformer.get_bev_features(
                        img_feats,
                        bev_queries,
                        self.bev_h,
                        self.bev_w,
                        grid_length=(self.real_h / self.bev_h, self.real_w / self.bev_w),
                        bev_pos=bev_pos,
                        img_metas=img_metas,
                        prev_bev=prev_bev,
                    )
                    .view(bs, 1, self.bev_h, self.bev_w, feat_dim)
                    .squeeze(1)
                    .permute(0, 3, 1, 2)
                )  # 1, 256, h ,w

            else:
                bev_embed, _ = self.transformer(
                    img_feats,
                    bev_queries,
                    object_query_embeds,
                    self.bev_h,
                    self.bev_w,
                    grid_length=(self.real_h / self.bev_h, self.real_w / self.bev_w),
                    bev_pos=bev_pos,
                    reg_branches=None,  # noqa:E501
                    cls_branches=None,
                    img_metas=img_metas,
                    prev_bev=prev_bev,
                )

            if self.decoder_2d is not None:
                x_img = self.decoder_2d(bev_embed)
            else:
                x_img = bev_embed

        return x_img

    def forward_lidar_bev_encoder(self, lidar_feats):
        b, n, c, h, w = lidar_feats.shape
        lidar_feats = lidar_feats.reshape(b * n, c, h, w)
        if hasattr(self, "upsample_layer"):
            x = self.upsample_layer(lidar_feats)  # # [bs,64,bev_h,bev_w] -> [bs,64,bev_h_up,bev_w_up]
            bev_h = 2 * self.bev_h
        else:
            x = lidar_feats  # [1, 384, 408, 152]

        pad = (
            0,
            0,  # w 维度（最后一个维度）不扩充
            bev_h - x.shape[2],
            0,  # h 维度 左边扩充，右边不扩充
            0,
            0,  # c 维度，不扩充
            0,
            0,  # b*n 维度，不扩充
        )

        x = F.pad(x, pad)
        assert x.shape[2] == bev_h

        return x

    def get_loss(self, forward_ret_dict, freespace, freespace_mask):
        if self.height_layer_num > 1:
            forward_ret_dict["semantic"] = (
                freespace.permute(0, 1, 4, 3, 2).flatten(0, 1).long()
            )  # torch.Size([2, 1, 2, 304, 1088]) torch.Size([2, 1088, 304, 2])
            forward_ret_dict["semantic_mask"] = freespace_mask.permute(0, 1, 4, 3, 2).flatten(
                0, 1
            )  # torch.Size([2, 1, 2, 304, 1088]) torch.Size([2, 1088, 304, 2])
        else:
            forward_ret_dict["semantic"] = freespace.permute(0, 1, 3, 2).flatten(0, 1).long()
            forward_ret_dict["semantic_mask"] = freespace_mask.permute(0, 1, 3, 2).flatten(0, 1)
        if self.use_mask:
            forward_ret_dict["semantic"][
                ~forward_ret_dict["semantic_mask"]
            ] = self.mask_loss_type  # 0 # self.ignore_label
        elif self.use_weighted_mask:
            # Compute weight map: 1.0 for mask regions, 0.5 for others
            semantic_mask_weight = torch.ones_like(
                forward_ret_dict["semantic_mask"], dtype=torch.float32
            )  # torch.Size([2, 1088, 304, 2])
            if self.dis_weight:
                if self.height_layer_num > 1:
                    B, H, W, layers = semantic_mask_weight.shape
                else:
                    B, H, W = semantic_mask_weight.shape

                # 构造基于距离的权重 [H]
                distance_weights = torch.ones((H,), dtype=torch.float32, device=semantic_mask_weight.device)
                distance_weights[476:680] = 0.7
                distance_weights[680:884] = 0.4
                distance_weights[884:1088] = 0.1

                # broadcast 成 [B, H, W]
                if self.height_layer_num > 1:
                    distance_weights = distance_weights.view(1, H, 1, 1).expand(B, H, W, layers)
                else:
                    distance_weights = distance_weights.view(1, H, 1).expand(B, H, W)

                # 非 mask 区域使用 distance 权重，mask 区域使用 1.0
                semantic_mask_weight = (
                    forward_ret_dict["semantic_mask"].float() * 1.0
                    + (~forward_ret_dict["semantic_mask"]).float() * distance_weights
                )
            else:
                semantic_mask_weight[~forward_ret_dict["semantic_mask"]] = 0.5

            if not self.surround:
                forward_ret_dict["semantic_mask_weight"] = semantic_mask_weight[
                    :, (semantic_mask_weight.shape[1] - forward_ret_dict["pred_seg"].shape[1]) :
                ]

        if not self.surround:
            forward_ret_dict["semantic"] = forward_ret_dict["semantic"][
                :, (forward_ret_dict["semantic"].shape[1] - forward_ret_dict["pred_seg"].shape[1]) :
            ]
        loss_cls, focus_loss_cls = self.get_cls_loss(forward_ret_dict)
        loss_dict = {"occ_loss_cls": loss_cls}
        if focus_loss_cls is not None:
            loss_dict.update({"occ_focus_loss_cls": focus_loss_cls})
        if "loss_dice" in self.loss_weight:
            loss_dice = self.get_dice_loss(forward_ret_dict)["dice_loss"]
            loss_dict.update({"occ_loss_dice": loss_dice})
        if self.use_visible_head:
            # vismask
            forward_ret_dict["vismask_gt"] = torch.zeros_like(
                forward_ret_dict["semantic_mask"]
            ).long()  # torch.Size([2, 1088, 304, 2])  torch.Size([2, 1088, 304])
            forward_ret_dict["vismask_gt"][forward_ret_dict["semantic_mask"]] = 1
            if not self.surround:
                forward_ret_dict["vismask_gt"] = forward_ret_dict["vismask_gt"][
                    :, (forward_ret_dict["vismask_gt"].shape[1] - forward_ret_dict["vismask_pred"].shape[1]) :
                ]
            if forward_ret_dict["vismask_gt"].ndim == 4:
                forward_ret_dict["vismask_gt"] = forward_ret_dict["vismask_gt"][:, :, :, 0]

            loss_vismask_head = self.get_vismask_loss(forward_ret_dict)
            loss_dict.update({"occ_loss_vismask": loss_vismask_head})
        if self.binary_seg:
            loss_binary = self.get_binary_loss(forward_ret_dict)["cls_binary_loss"]
            loss_dict.update({"occ_loss_binary": loss_binary})
        return forward_ret_dict, loss_dict

    def label_formatting(self, bev_label, smooth_lambda=None):
        """
        BEV label二值化, 带label smooth
        Args:
            bev_label:  shape = [bs, 1, H, W] , 每个grid的label
            smooth_lambda: shape = [bs], 每个instance的label smooth 参数
        Returns:
            seg_label: shape = [bs, 1, H, w]
            mask: shape = [bs, 1, H, w]
        """
        ndim = len(bev_label.shape)
        seg_label = torch.clone(bev_label)  # bev label后面还要用
        seg_label = torch.where(seg_label == 0, 0, 1)
        if smooth_lambda is not None:
            smooth_lambda = smooth_lambda.view([-1] + [1] * (ndim - 1))
            seg_label = seg_label * smooth_lambda + (1 - seg_label) * (1 - smooth_lambda)
        mask = bev_label != self.ignore_label
        return seg_label, mask

    def get_cls_loss(self, forward_ret_dict):
        y_label_cls = forward_ret_dict[
            "semantic"
        ]  # shape = [bs, H_bev, W_bev] # torch.Size([2, 816, 304, 2]) 数值 torch.Size([2, 816, 304])
        y_pred_cls = forward_ret_dict[
            "pred_seg"
        ]  # shape = [bs, H_bev, W_bev, cls_nums] # torch.Size([2, 816, 304, 2, 9]) 概率 torch.Size([2, 816, 304, 9])
        # self.draw_result(
        #                  y_pred_cls,
        #                  forward_ret_dict['semantic_mask'][:,(y_label_cls.shape[1]-y_pred_cls.shape[1]):],
        #                  y_label_cls[:,(y_label_cls.shape[1]-y_pred_cls.shape[1]):],)

        if "semantic_mask_weight" in forward_ret_dict:
            cls_loss = self.crit(y_pred_cls, y_label_cls, weights=forward_ret_dict["semantic_mask_weight"])
        else:
            cls_loss = self.crit(y_pred_cls, y_label_cls)
        cls_loss = self.loss_weight["loss_cls"] * cls_loss
        if self.focus_area_loss:
            # TODO delete hard code
            _, H, W = y_label_cls.shape
            focus_h_index = int(H * 0.2)
            focus_w_index = [int(W * 0.35), int(W * 0.65)]
            focus_cls_loss = self.crit(
                y_pred_cls[:, :focus_h_index, focus_w_index[0] : focus_w_index[1]],
                y_label_cls[:, :focus_h_index, focus_w_index[0] : focus_w_index[1]],
            )
            focus_cls_loss = self.loss_weight["loss_cls"] * focus_cls_loss
        else:
            focus_cls_loss = None
        # mask = y_label_cls != -1
        # hit = (y_pred_cls.argmax(-1) == y_label_cls)[mask]
        # misclassify = 1.0 - hit.sum() / (mask.sum() + 1e-8)
        # ret["cls_misclassify"] = misclassify
        return cls_loss, focus_cls_loss

    def draw_result(self, y_pred_cls, mask, y_label_cls):
        import numpy as np
        import matplotlib.pyplot as plt
        import matplotlib.colors as mcolors

        y_pred_cls = np.array(y_pred_cls.argmax(-1).detach().cpu())
        y_label_cls[y_label_cls == 255] = 5
        y_label_cls = np.array(y_label_cls.detach().cpu())
        mask = np.array(mask.detach().cpu())

        cmap = mcolors.ListedColormap(
            [
                (0.0, 0.0, 0.0),  # 类别0，黑色
                (1.0, 0.0, 0.0),  # 类别1，红色
                (0.0, 1.0, 0.0),  # 类别2，绿色
                (0.0, 0.0, 1.0),  # 类别3，蓝色
                (1.0, 1.0, 0.0),  # 类别4，黄色
                (1.0, 0.0, 1.0),  # 类别5，紫色
                (1.0, 1.0, 1.0),  # 类别6，白色
            ]
        )
        for b in range(len(y_label_cls)):
            freespace_pred = y_pred_cls[b, ::-1]
            freespace_gt = y_label_cls[b, ::-1]
            mask_i = mask[b, ::-1]

            plt.figure(dpi=800)
            # 可视化分割图
            non_mask_freespace = np.concatenate([freespace_gt, freespace_pred], axis=1)
            plt.imshow(non_mask_freespace, cmap=cmap)
            # Show the plot
            plt.savefig("/data/Envs/megvii/freespace.jpg")

            plt.figure(dpi=800)
            # 可视化分割图
            mask_freespace = np.concatenate([freespace_gt * mask_i, freespace_pred * mask_i], axis=1)
            plt.imshow(mask_freespace, cmap=cmap)

            # Show the plot
            plt.savefig("/data/Envs/megvii/freespace_mask.jpg")

    def get_binary_loss(self, forward_ret_dict):
        ret = {}
        bev_labels = forward_ret_dict["semantic"]
        if "label_smooth_lambda" in forward_ret_dict:
            label_smooth_lambda = forward_ret_dict["label_smooth_lambda"]
        else:
            label_smooth_lambda = torch.ones((len(bev_labels))).to(bev_labels)
        y_pred_cls = forward_ret_dict["pred_seg_binary"].squeeze(1)
        lanes_label, mask = self.label_formatting(bev_labels, smooth_lambda=label_smooth_lambda)
        y_label_cls = lanes_label.long().cuda()

        cls_loss = self.crit_binary(y_pred_cls, y_label_cls, weights=mask)
        cls_loss = cls_loss.sum() / (mask.sum() + 1e-4)
        cls_loss = self.loss_weight["loss_binary"] * cls_loss
        # observe metric
        pos_mask = y_label_cls.eq(1).long()
        neg_mask = mask * (1 - pos_mask)
        y_pred_cls = torch.sigmoid(y_pred_cls)
        pos_pred = (y_pred_cls * pos_mask).sum() / (pos_mask.sum() + 1e-4)
        neg_pred = (y_pred_cls * neg_mask).sum() / (neg_mask.sum() + 1e-4)

        ret["cls_binary_loss"] = cls_loss
        ret["pos_pred"] = pos_pred
        ret["neg_pred"] = neg_pred
        return ret

    def get_dice_loss(self, forward_ret_dict):
        ret = {}

        pred_seg = forward_ret_dict["pred_seg"]
        bev_label = forward_ret_dict["semantic"]
        if self.height_layer_num > 1:
            y_pred_cls = pred_seg.permute(0, 4, 1, 2, 3)  # shape = [bs, cls, H_bev, W_bev, 2_layers]
            y_label_cls = bev_label.long()  # [bs,H,W, 2_layers]
        else:
            y_pred_cls = pred_seg.permute(0, 3, 1, 2)  # shape = [bs, 4, H_bev, W_bev]
            y_label_cls = bev_label.squeeze(1).long()  # [bs,1,H,W] -> [bs,H,W]
        bs = y_pred_cls.size(0)
        pred = y_pred_cls.contiguous().view(bs, self.num_classes, -1)
        target = y_label_cls.contiguous().view(bs, -1)
        dice_loss, observe_dict = self.crit_dice(pred, target)
        self.loss_dice_weight = self.loss_weight.get("loss_dice", 0)
        ret["dice_loss"] = self.loss_dice_weight * dice_loss
        ret.update(observe_dict)
        return ret

    def get_vismask_loss(self, forward_ret_dict):
        vismask_loss = self.crit_vismask(forward_ret_dict["vismask_pred"], forward_ret_dict["vismask_gt"])
        vismask_loss = self.loss_weight.get("loss_vismask", 5.0) * vismask_loss

        return vismask_loss
