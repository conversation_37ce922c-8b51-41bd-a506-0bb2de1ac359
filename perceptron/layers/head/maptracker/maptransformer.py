# Copyright (c) OpenMMLab. All rights reserved.
import warnings
import copy

import torch
from mmcv.cnn.bricks.registry import TRANSFORMER_LAYER, TRANSFORMER_LAYER_SEQUENCE
from mmcv.cnn.bricks.transformer import BaseTransformerLayer, build_transformer_layer
from mmcv.runner.base_module import BaseModule, ModuleList
from mmdet.models.utils.transformer import inverse_sigmoid


@TRANSFORMER_LAYER_SEQUENCE.register_module()
class MapTransformerDecoder_new(BaseModule):
    """Implements the decoder in DETR transformer.
    Args:
        return_intermediate (bool): Whether to return intermediate outputs.
        coder_norm_cfg (dict): Config of last normalization layer. Default:
            `LN`.
    """

    def __init__(self, transformerlayers=None, num_layers=None, return_intermediate=True, init_cfg=None, coord_dim=3):
        super().__init__(init_cfg)
        if isinstance(transformerlayers, dict):
            transformerlayers = [copy.deepcopy(transformerlayers) for _ in range(num_layers)]
        else:
            assert isinstance(transformerlayers, list) and len(transformerlayers) == num_layers
        self.num_layers = num_layers
        self.layers = ModuleList()
        for i in range(num_layers):
            self.layers.append(build_transformer_layer(transformerlayers[i]))
        self.embed_dims = self.layers[0].embed_dims
        self.pre_norm = self.layers[0].pre_norm
        self.return_intermediate = return_intermediate
        self.coord_dim = coord_dim

    def forward(
        self,
        query,
        key,
        value,
        query_pos,
        key_pos,
        key_padding_mask,
        query_key_padding_mask,
        reference_points,
        reg_branches,
        predict_refine=None,
        memory_bank=None,
        query_split=None,
        **kwargs,
    ):
        """Forward function for `TransformerDecoder`.
        Args:
            query (Tensor): Input query with shape
                `(num_query, bs, embed_dims)`.
            reference_points (Tensor): The reference
                points of offset. has shape (bs, num_query, num_points, 2).
            valid_ratios (Tensor): The radios of valid
                points on the feature map, has shape
                (bs, num_levels, 2)
            reg_branch: (obj:`nn.ModuleList`): Used for
                refining the regression results. Only would
                be passed when with_box_refine is True,
                otherwise would be passed a `None`.
        Returns:
            Tensor: Results with shape [1, num_query, bs, embed_dims] when
                return_intermediate is `False`, otherwise it has shape
                [num_layers, num_query, bs, embed_dims].
        """
        num_queries, bs, embed_dims = query.shape
        output = query
        intermediate = []
        if isinstance(reg_branches, list):
            intermediate_reference_points = dict()
            for i, _ in enumerate(reg_branches):
                intermediate_reference_points[i] = []
        else:
            intermediate_reference_points = []

        for lid, layer in enumerate(self.layers):
            output = layer(
                output,
                key,
                value,
                query_pos=query_pos,
                key_pos=key_pos,
                key_padding_mask=key_padding_mask,
                query_key_padding_mask=query_key_padding_mask,
                memory_bank=memory_bank,
                **kwargs,
            )

            if isinstance(reg_branches, list):
                for i, reg_branch_one in enumerate(reg_branches):
                    reg_points = reg_branch_one[lid](
                        output.permute(1, 0, 2)[:, query_split[i] : query_split[i + 1], ...]
                    )  # (bs, num_q, coord_dim*num_points)
                    bs, num_queries, num_points2 = reg_points.shape
                    reg_points = reg_points.view(
                        bs, num_queries, num_points2 // self.coord_dim, self.coord_dim
                    )  # range (0, 1)

                    if predict_refine:  # TODO: 这个模式下的逻辑需要修正，应当是每层都更新 refpts, 然后再在每层更新的 refpts 基础上预测真值偏差
                        new_reference_points = reg_points + inverse_sigmoid(reference_points)
                        new_reference_points = new_reference_points.sigmoid()
                    else:
                        new_reference_points = reg_points.sigmoid()  # (bs, num_q, num_points, 2)

                    if self.return_intermediate:
                        intermediate_reference_points[i].append(new_reference_points)  # (bs, num_q, num_points, 2)

                if self.return_intermediate:
                    intermediate.append(output.permute(1, 0, 2))  # [(bs, num_q, embed_dims)]
            else:
                reg_points = reg_branches[lid](output.permute(1, 0, 2))  # (bs, num_q, coord_dim*num_points)
                bs, num_queries, num_points2 = reg_points.shape
                reg_points = reg_points.view(
                    bs, num_queries, num_points2 // self.coord_dim, self.coord_dim
                )  # range (0, 1)

                if predict_refine:  # TODO: 这个模式下的逻辑需要修正，应当是每层都更新 refpts, 然后再在每层更新的 refpts 基础上预测真值偏差
                    new_reference_points = reg_points + inverse_sigmoid(reference_points)
                    new_reference_points = new_reference_points.sigmoid()
                else:
                    new_reference_points = reg_points.sigmoid()  # (bs, num_q, num_points, 2)

                if self.return_intermediate:
                    intermediate.append(output.permute(1, 0, 2))  # [(bs, num_q, embed_dims)]
                    intermediate_reference_points.append(new_reference_points)  # (bs, num_q, num_points, 2)

        if self.return_intermediate:
            return intermediate, intermediate_reference_points

        return output, reference_points  # 没有用到，用到时需要修改这行


@TRANSFORMER_LAYER.register_module()
class MapTransformerLayer(BaseTransformerLayer):
    """Base `TransformerLayer` for vision transformer.

    It can be built from `mmcv.ConfigDict` and support more flexible
    customization, for example, using any number of `FFN or LN ` and
    use different kinds of `attention` by specifying a list of `ConfigDict`
    named `attn_cfgs`. It is worth mentioning that it supports `prenorm`
    when you specifying `norm` as the first element of `operation_order`.
    More details about the `prenorm`: `On Layer Normalization in the
    Transformer Architecture <https://arxiv.org/abs/2002.04745>`_ .

    Args:
        attn_cfgs (list[`mmcv.ConfigDict`] | obj:`mmcv.ConfigDict` | None )):
            Configs for `self_attention` or `cross_attention` modules,
            The order of the configs in the list should be consistent with
            corresponding attentions in operation_order.
            If it is a dict, all of the attention modules in operation_order
            will be built with this config. Default: None.
        ffn_cfgs (list[`mmcv.ConfigDict`] | obj:`mmcv.ConfigDict` | None )):
            Configs for FFN, The order of the configs in the list should be
            consistent with corresponding ffn in operation_order.
            If it is a dict, all of the attention modules in operation_order
            will be built with this config.
        operation_order (tuple[str]): The execution order of operation
            in transformer. Such as ('self_attn', 'norm', 'ffn', 'norm').
            Support `prenorm` when you specifying first element as `norm`.
            Default：None.
        norm_cfg (dict): Config dict for normalization layer.
            Default: dict(type='LN').
        init_cfg (obj:`mmcv.ConfigDict`): The Config for initialization.
            Default: None.
        batch_first (bool): Key, Query and Value are shape
            of (batch, n, embed_dim)
            or (n, batch, embed_dim). Default to False.
    """

    def __init__(
        self,
        attn_cfgs=None,
        ffn_cfgs=dict(
            type="FFN",
            embed_dims=256,
            feedforward_channels=1024,
            num_fcs=2,
            ffn_drop=0.0,
            act_cfg=dict(type="ReLU", inplace=True),
        ),
        operation_order=None,
        norm_cfg=dict(type="LN"),
        init_cfg=None,
        batch_first=False,
        **kwargs,
    ):

        super().__init__(
            attn_cfgs=attn_cfgs,
            ffn_cfgs=ffn_cfgs,
            operation_order=operation_order,
            norm_cfg=norm_cfg,
            init_cfg=init_cfg,
            batch_first=batch_first,
            **kwargs,
        )

    def forward(
        self,
        query,
        key=None,
        value=None,
        memory_query=None,
        query_pos=None,
        key_pos=None,
        attn_masks=None,
        query_key_padding_mask=None,
        key_padding_mask=None,
        memory_bank=None,
        **kwargs,
    ):
        """Forward function for `TransformerDecoderLayer`.

        **kwargs contains some specific arguments of attentions.

        Args:
            query (Tensor): The input query with shape
                [num_queries, bs, embed_dims] if
                self.batch_first is False, else
                [bs, num_queries embed_dims].
            key (Tensor): The key tensor with shape [num_keys, bs,
                embed_dims] if self.batch_first is False, else
                [bs, num_keys, embed_dims] .
            value (Tensor): The value tensor with same shape as `key`.
            query_pos (Tensor): The positional encoding for `query`.
                Default: None.
            key_pos (Tensor): The positional encoding for `key`.
                Default: None.
            attn_masks (List[Tensor] | None): 2D Tensor used in
                calculation of corresponding attention. The length of
                it should equal to the number of `attention` in
                `operation_order`. Default: None.
            query_key_padding_mask (Tensor): ByteTensor for `query`, with
                shape [bs, num_queries]. Only used in `self_attn` layer.
                Defaults to None.
            key_padding_mask (Tensor): ByteTensor for `query`, with
                shape [bs, num_keys]. Default: None.

        Returns:
            Tensor: forwarded results with shape [num_queries, bs, embed_dims].
        """

        if memory_bank is not None:
            bs = query.shape[1]
            all_valid_track_idx = []
            for b_i in range(bs):
                all_valid_track_idx.append(memory_bank.valid_track_idx[b_i])

        norm_index = 0
        attn_index = 0
        ffn_index = 0
        identity = query
        if attn_masks is None:
            attn_masks = [None for _ in range(self.num_attn)]
        elif isinstance(attn_masks, torch.Tensor):
            attn_masks = [copy.deepcopy(attn_masks) for _ in range(self.num_attn)]
            warnings.warn("Use same attn_mask in all attentions in " f"{self.__class__.__name__} ")
        else:
            assert len(attn_masks) == self.num_attn, (
                "The length of "
                f"attn_masks {len(attn_masks)} must be equal "
                "to the number of attention in "
                f"operation_order {self.num_attn}"
            )

        cross_att_num = self.operation_order.count("cross_attn")
        for layer in self.operation_order:
            if layer == "self_attn":
                if memory_query is None:
                    temp_key = temp_value = query
                else:
                    temp_key = temp_value = torch.cat([memory_query, query], dim=0)

                query = self.attentions[attn_index](
                    query,
                    temp_key,
                    temp_value,
                    identity if self.pre_norm else None,
                    query_pos=query_pos,
                    key_pos=query_pos,
                    attn_mask=attn_masks[attn_index],
                    key_padding_mask=query_key_padding_mask,
                    **kwargs,
                )
                attn_index += 1
                identity = query

            elif layer == "norm":
                query = self.norms[norm_index](query)
                norm_index += 1

            elif layer == "cross_attn":
                if attn_index == 1:
                    query_bev = self.attentions[attn_index](
                        query,
                        key,
                        value,
                        identity if self.pre_norm else None,
                        query_pos=query_pos,
                        key_pos=key_pos,
                        attn_mask=attn_masks[attn_index],
                        key_padding_mask=key_padding_mask,
                        **kwargs,
                    )
                    attn_index += 1
                    if (
                        cross_att_num == 1
                    ):  # 兼容单帧多帧，maptracker 是 query_bev + query_mem, 后面可以考虑串行，用query_bev 之后更新的 query 和 mem 交互
                        query = query_bev

                else:
                    # Memory cross attention
                    assert attn_index == 2
                    if memory_bank is not None:
                        bs = query.shape[1]
                        query_i_list = []
                        for b_i in range(bs):
                            valid_track_idx = all_valid_track_idx[b_i]
                            query_i = query[:, b_i].clone()
                            query_i = query_i[None, :]
                            if len(valid_track_idx) != 0:
                                mem_embeds = memory_bank.batch_mem_embeds_dict[b_i][:, valid_track_idx, :]
                                mem_key_padding_mask = memory_bank.batch_key_padding_dict[b_i][valid_track_idx]
                                mem_key_pos = memory_bank.batch_mem_relative_pe_dict[b_i][:, valid_track_idx]

                                query_i[:, valid_track_idx] = self.attentions[attn_index](
                                    query_i[:, valid_track_idx],
                                    mem_embeds,
                                    mem_embeds,
                                    identity=None,
                                    query_pos=None,
                                    key_pos=mem_key_pos,
                                    attn_mask=None,
                                    key_padding_mask=mem_key_padding_mask,
                                    **kwargs,
                                )

                            query_i_list.append(query_i[0])
                        query_memory = torch.stack(query_i_list).permute(1, 0, 2)
                    else:
                        query_memory = torch.zeros_like(query_bev)

                    query = query_memory + query
                    identity = query
                    attn_index += 1

            elif layer == "ffn":
                query = self.ffns[ffn_index](query, identity if self.pre_norm else None)
                ffn_index += 1

        return query
