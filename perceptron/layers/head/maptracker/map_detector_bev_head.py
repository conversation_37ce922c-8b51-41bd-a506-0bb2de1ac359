import copy
import torch
import torch.nn as nn
from mmcv.cnn import ConvModule, Linear, bias_init_with_prob, xavier_init
from mmcv.runner import force_fp32
from mmcv.cnn.bricks.transformer import build_positional_encoding, build_transformer_layer_sequence
from mmdet.models import build_loss

from mmdet.core import multi_apply, reduce_mean, build_assigner, build_sampler
from mmdet.models import HEADS
from mmdet.models.utils.transformer import inverse_sigmoid
from einops import rearrange


def pos2posemb3d(pos, num_pos_feats=128, temperature=10000):
    import math

    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    pos_z = pos[..., 2, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_z = torch.stack((pos_z[..., 0::2].sin(), pos_z[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x, pos_z), dim=-1)
    return posemb


@HEADS.register_module(force=True)
class MapDetectorBevHead(nn.Module):
    def __init__(
        self,
        num_queries,
        query_split=[0, 200, 250, 300, 400],
        query_group=1,
        num_classes=3,
        num_lane_dash_attr=2,
        num_lane_color_attr=2,
        num_lane_shape_attr=2,
        num_curb_attr=2,
        num_arrow_attr=20,
        num_drection_attr=4,
        attr_nums=6,
        in_channels=128,
        embed_dims=256,
        depth_num=64,
        line_num_points=20,
        stop_num_points=4,
        entrance_num_points=4,
        box_num_points=4,
        coord_dim=2,
        pc_range=[-15.2, 0.0, -5.0, 15.2, 100, 3.0],
        different_heads=True,
        predict_refine=False,
        bev_pos=None,
        sync_cls_avg_factor=True,
        bg_cls_weight=0.0,
        trans_loss_weight=0.0,
        transformer=dict(),
        loss_cls=dict(),
        loss_line_reg=dict(),
        loss_stop_reg=dict(),
        loss_box_reg=dict(),
        loss_entrance_reg=dict(),
        loss_lane_color_attr=dict(),
        loss_lane_dash_attr=dict(),
        loss_curb_attr=dict(),
        loss_arrow_attr=dict(),
        loss_direction_attr=dict(),
        assigner=dict(),
        modal=["camera"],
        lidar_embed_dims=384,
    ):
        super().__init__()
        self.num_classes = num_classes
        self.num_lane_color_attr = num_lane_color_attr
        self.num_lane_dash_attr = num_lane_dash_attr
        self.num_lane_shape_attr = num_lane_shape_attr
        self.num_curb_attr = num_curb_attr
        self.num_arrow_attr = num_arrow_attr
        self.num_drection_attr = num_drection_attr
        self.attr_nums = attr_nums
        self.in_channels = in_channels
        self.embed_dims = embed_dims
        self.different_heads = different_heads
        self.predict_refine = predict_refine
        self.line_num_points = line_num_points
        self.stop_num_points = stop_num_points
        self.entrance_num_points = entrance_num_points
        self.box_num_points = box_num_points
        self.coord_dim = coord_dim

        self.sync_cls_avg_factor = sync_cls_avg_factor
        self.bg_cls_weight = bg_cls_weight

        self.trans_loss_weight = trans_loss_weight
        self.pc_range = pc_range

        sampler_cfg = dict(type="PseudoSampler")
        self.sampler = build_sampler(sampler_cfg, context=self)

        self.transformer = build_transformer_layer_sequence(transformer)

        self.loss_cls = build_loss(loss_cls)
        self.loss_line_reg = build_loss(loss_line_reg)
        self.loss_stop_reg = build_loss(loss_stop_reg)
        self.loss_box_reg = build_loss(loss_box_reg)
        self.loss_entrance_reg = build_loss(loss_entrance_reg)
        self.loss_lane_color_attr = build_loss(loss_lane_color_attr)
        self.loss_lane_dash_attr = build_loss(loss_lane_dash_attr)
        self.loss_curb_attr = build_loss(loss_curb_attr)
        self.loss_arrow_attr = build_loss(loss_arrow_attr)
        self.loss_direction_attr = build_loss(loss_direction_attr)
        self.assigner = build_assigner(assigner)

        if self.loss_cls.use_sigmoid:
            self.cls_out_channels = num_classes
        else:
            self.cls_out_channels = num_classes + 1

        # _init_embedding
        self.num_queries_ins = num_queries
        self.query_split_ins = query_split
        self.query_group = query_group
        self.num_queries_with_group = 0
        self.query_split_with_group = [0]
        for i in range(len(self.query_split_ins[1:])):
            if i == 0:
                self.num_queries_with_group += self.query_split_ins[i + 1] * self.query_group
                self.query_split_with_group.append(self.query_split_ins[i + 1] * self.query_group)
            else:
                add_query = self.query_split_ins[i + 1] - self.query_split_ins[i]
                self.num_queries_with_group += add_query
                self.query_split_with_group.append(self.query_split_with_group[i] + add_query)

        self.line_query_num_ins = self.query_split_ins[1] - self.query_split_ins[0]
        self.line_query_index_ins = [self.query_split_ins[0], self.query_split_ins[1]]
        self.stop_query_num_ins = self.query_split_ins[2] - self.query_split_ins[1]
        self.stop_query_index_ins = [self.query_split_ins[1], self.query_split_ins[2]]
        self.box_query_num_ins = self.query_split_ins[3] - self.query_split_ins[2]
        self.box_query_index_ins = [self.query_split_ins[2], self.query_split_ins[3]]
        self.entrance_query_num_ins = self.query_split_ins[4] - self.query_split_ins[3]
        self.entrance_query_index_ins = [self.query_split_ins[3], self.query_split_ins[4]]

        self.line_query_num_with_group = self.query_split_with_group[1] - self.query_split_with_group[0]
        self.line_query_index_with_group = [self.query_split_with_group[0], self.query_split_with_group[1]]
        self.stop_query_num_with_group = self.query_split_with_group[2] - self.query_split_with_group[1]
        self.stop_query_index_with_group = [self.query_split_with_group[1], self.query_split_with_group[2]]
        self.box_query_num_with_group = self.query_split_with_group[3] - self.query_split_with_group[2]
        self.box_query_index_with_group = [self.query_split_with_group[2], self.query_split_with_group[3]]
        self.entrance_query_num_with_group = self.query_split_with_group[4] - self.query_split_with_group[3]
        self.entrance_query_index_with_group = [self.query_split_with_group[3], self.query_split_with_group[4]]
        self.depth_num = depth_num

        if "lidar" in modal:  # 有 lidar 时, 直接 mlp 生成 pe, 不再做反投影操作
            self.query_pe_mlp = nn.Sequential(
                nn.Linear(self.embed_dims // 2 * 3, self.embed_dims * 4),
                nn.ReLU(inplace=True),
                nn.Linear(self.embed_dims * 4, self.embed_dims),
            )
            self.grid_height = 0.5
            self.shared_conv = ConvModule(  # 用对应 channel 维度
                lidar_embed_dims,
                self.embed_dims,
                kernel_size=3,
                stride=1,  # 下采样x2
                padding=1,
                conv_cfg=dict(type="Conv2d"),
                norm_cfg=dict(type="BN2d"),
            )
        # query_pos_embed & query_embed
        self.reference_points = nn.Embedding(self.num_queries_with_group, 3)  # 一个 query 代表一个 instance, 用于计算 pe
        self.query_embedding = nn.Embedding(self.num_queries_with_group, self.embed_dims)

        positional_encoding = dict(type="SinePositionalEncoding", num_feats=self.embed_dims // 2, normalize=True)
        self.bev_pos_embed = build_positional_encoding(positional_encoding)

        self._init_branch()
        self.init_weights()

    def forward(self, *args, return_loss=True, **kwargs):
        if return_loss:
            return self.forward_train(*args, **kwargs)
        else:
            return self.forward_infer_single(*args, **kwargs)

    def init_weights(self):
        """Initialize weights of the head and embeddings."""

        xavier_init(self.reference_points, distribution="uniform", bias=0.0)
        # xavier_init(self.rv_embedding, distribution='uniform', bias=0.)
        xavier_init(self.query_embedding, distribution="uniform", bias=0.0)

        for p in self.transformer.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

        # init prediction branch
        for m in self.line_reg_branchs:
            for param in m.parameters():
                if param.dim() > 1:
                    nn.init.xavier_uniform_(param)

        for m in self.stop_reg_branchs:
            for param in m.parameters():
                if param.dim() > 1:
                    nn.init.xavier_uniform_(param)

        for m in self.box_reg_branchs:
            for param in m.parameters():
                if param.dim() > 1:
                    nn.init.xavier_uniform_(param)

        for m in self.entrance_cls_branches:
            for param in m.parameters():
                if param.dim() > 1:
                    nn.init.xavier_uniform_(param)

        # focal loss init
        if self.loss_cls.use_sigmoid:
            bias_init = bias_init_with_prob(0.01)
            if isinstance(self.line_cls_branches, nn.ModuleList):
                for m in self.line_cls_branches:
                    if hasattr(m, "bias"):
                        nn.init.constant_(m.bias, bias_init)
            else:
                m = self.line_cls_branches
                nn.init.constant_(m.bias, bias_init)

            if isinstance(self.stop_cls_branches, nn.ModuleList):
                for m in self.stop_cls_branches:
                    if hasattr(m, "bias"):
                        nn.init.constant_(m.bias, bias_init)
            else:
                m = self.stop_cls_branches
                nn.init.constant_(m.bias, bias_init)

            if isinstance(self.box_cls_branches, nn.ModuleList):
                for m in self.box_cls_branches:
                    if hasattr(m, "bias"):
                        nn.init.constant_(m.bias, bias_init)
            else:
                m = self.box_cls_branches
                nn.init.constant_(m.bias, bias_init)

            if isinstance(self.entrance_cls_branches, nn.ModuleList):
                for m in self.entrance_cls_branches:
                    if hasattr(m, "bias"):
                        nn.init.constant_(m.bias, bias_init)
            else:
                m = self.entrance_cls_branches
                nn.init.constant_(m.bias, bias_init)

    def _init_reg_branch_one(self, num_points):
        reg_branch = [
            Linear(self.embed_dims, 2 * self.embed_dims),
            nn.LayerNorm(2 * self.embed_dims),
            nn.ReLU(),
            Linear(2 * self.embed_dims, 2 * self.embed_dims),
            nn.LayerNorm(2 * self.embed_dims),
            nn.ReLU(),
            Linear(2 * self.embed_dims, num_points * self.coord_dim),
        ]

        return reg_branch

    def _init_branch(self):
        """Initialize classification branch and regression branch of head."""

        line_cls_branch = Linear(self.embed_dims * self.query_group, self.cls_out_channels)  # with group
        stop_cls_branch = Linear(self.embed_dims, self.cls_out_channels)
        box_cls_branch = Linear(self.embed_dims, self.cls_out_channels)
        entrance_cls_branch = Linear(self.embed_dims, self.cls_out_channels)

        line_reg_branch = nn.Sequential(*self._init_reg_branch_one(self.line_num_points // self.query_group))
        stop_reg_branch = nn.Sequential(*self._init_reg_branch_one(self.stop_num_points))
        box_reg_branch = nn.Sequential(*self._init_reg_branch_one(self.box_num_points))
        entrance_reg_branch = nn.Sequential(*self._init_reg_branch_one(self.entrance_num_points))

        lane_color_attr_branch = Linear(
            self.embed_dims, (self.line_num_points // self.query_group) * self.num_lane_color_attr
        )
        lane_dash_attr_branch = Linear(
            self.embed_dims, (self.line_num_points // self.query_group) * self.num_lane_dash_attr
        )
        curb_attr_branch = Linear(self.embed_dims, (self.line_num_points // self.query_group) * self.num_curb_attr)
        arrow_attr_branch = Linear(self.embed_dims, self.box_num_points * self.num_arrow_attr)

        direction_attr_branch = Linear(self.embed_dims, self.box_num_points * self.num_drection_attr)

        num_layers = self.transformer.num_layers
        if self.different_heads:
            line_cls_branches = nn.ModuleList([copy.deepcopy(line_cls_branch) for _ in range(num_layers)])
            stop_cls_branches = nn.ModuleList([copy.deepcopy(stop_cls_branch) for _ in range(num_layers)])
            box_cls_branches = nn.ModuleList([copy.deepcopy(box_cls_branch) for _ in range(num_layers)])
            entrance_cls_branches = nn.ModuleList([copy.deepcopy(entrance_cls_branch) for _ in range(num_layers)])
            line_reg_branchs = nn.ModuleList([copy.deepcopy(line_reg_branch) for _ in range(num_layers)])
            stop_reg_branchs = nn.ModuleList([copy.deepcopy(stop_reg_branch) for _ in range(num_layers)])
            box_reg_branchs = nn.ModuleList([copy.deepcopy(box_reg_branch) for _ in range(num_layers)])
            entrance_reg_branchs = nn.ModuleList([copy.deepcopy(entrance_reg_branch) for _ in range(num_layers)])
            lane_color_attr_branches = nn.ModuleList([copy.deepcopy(lane_color_attr_branch) for _ in range(num_layers)])
            lane_dash_attr_branches = nn.ModuleList([copy.deepcopy(lane_dash_attr_branch) for _ in range(num_layers)])
            curb_attr_branches = nn.ModuleList([copy.deepcopy(curb_attr_branch) for _ in range(num_layers)])
            arrow_attr_branches = nn.ModuleList([copy.deepcopy(arrow_attr_branch) for _ in range(num_layers)])
            direction_attr_branches = nn.ModuleList([copy.deepcopy(direction_attr_branch) for _ in range(num_layers)])
        else:
            line_cls_branches = nn.ModuleList([line_cls_branch for _ in range(num_layers)])
            stop_cls_branches = nn.ModuleList([stop_cls_branch for _ in range(num_layers)])
            box_cls_branches = nn.ModuleList([box_cls_branch for _ in range(num_layers)])
            entrance_cls_branches = nn.ModuleList([entrance_cls_branch for _ in range(num_layers)])
            line_reg_branchs = nn.ModuleList([line_reg_branch for _ in range(num_layers)])
            stop_reg_branchs = nn.ModuleList([stop_reg_branch for _ in range(num_layers)])
            box_reg_branchs = nn.ModuleList([box_reg_branch for _ in range(num_layers)])
            entrance_reg_branchs = nn.ModuleList([entrance_reg_branch for _ in range(num_layers)])
            lane_color_attr_branches = nn.ModuleList([lane_color_attr_branch for _ in range(num_layers)])
            lane_dash_attr_branches = nn.ModuleList([lane_dash_attr_branch for _ in range(num_layers)])
            curb_attr_branches = nn.ModuleList([curb_attr_branch for _ in range(num_layers)])
            arrow_attr_branches = nn.ModuleList([arrow_attr_branch for _ in range(num_layers)])
            direction_attr_branches = nn.ModuleList([direction_attr_branch for _ in range(num_layers)])

        self.line_reg_branchs = line_reg_branchs
        self.stop_reg_branchs = stop_reg_branchs
        self.box_reg_branchs = box_reg_branchs
        self.entrance_reg_branchs = entrance_reg_branchs

        self.line_cls_branches = line_cls_branches
        self.stop_cls_branches = stop_cls_branches
        self.box_cls_branches = box_cls_branches
        self.entrance_cls_branches = entrance_cls_branches

        self.lane_color_attr_branches = lane_color_attr_branches
        self.lane_dash_attr_branches = lane_dash_attr_branches
        self.curb_attr_branches = curb_attr_branches
        self.arrow_attr_branches = arrow_attr_branches
        self.direction_attr_branches = direction_attr_branches

    def _bev_coords(self, grid_h, grid_w, grid_height, device="cuda"):
        """
        生成BEV特征的0-1坐标编码。

        参数:
        - grid_h: 特征图的高度
        - grid_w: 特征图的宽度
        - height: 自定义的高度 默认为0.5

        返回:
        -  pe: 坐标编码的torch.Tensor，形状为(grid_h, grid_w, 3)
        """
        # 生成归一化的x和y坐标
        x_coords = (torch.linspace(0, grid_h - 1, grid_h, device=device) + 0.5) / grid_h  # shape: (grid_h,)
        y_coords = (torch.linspace(0, grid_w - 1, grid_w, device=device) + 0.5) / grid_w  # shape: (grid_w,)

        # 右下角为 [0, 0], 左上角为 [1, 1]
        x_coords = 1 - x_coords
        y_coords = 1 - y_coords

        # 将坐标扩展到二维网格
        x_grid, y_grid = torch.meshgrid(x_coords, y_coords)

        # 创建一个与x_grid和y_grid形状相同的高度值张量
        r_grid = torch.full_like(x_grid, fill_value=grid_height)  # shape: (grid_h, grid_w)

        # 将归一化坐标和高度值合并为一个数组
        xyz_coords = torch.stack([x_grid, y_grid, r_grid], dim=-1)  # shape: (grid_h, grid_w, 3)

        return xyz_coords

    def forward_train(
        self,
        bev_features,
        lidar_features,
        img_metas,
        gts,
        track_query_info=None,
        memory_bank=None,
        return_matching=False,
        scene_weight=None,
    ):
        """
        Args:
            img_feature (List[Tensor]): shape [B, N_cam, C, H, W]
                single frame feature in image view
        Outs:
            preds_dict (list[dict]):
                loss_dict:
                outputs:
                det_match_idxs:
                det_match_gt_idxs:
                matched_reg_cost:
                gt_info_list:
        """
        # bs, N_cam, C, H, W = img_features.shape
        # bs = bev_features.shape[0] if bev_features is not None else lidar_features.shape[0]
        bs, C, H, W = bev_features.shape
        # img_masks = bev_features.new_zeros((bs, H, W))
        # pos_embed = None

        query_embedding = self.query_embedding.weight[None, ...].repeat(bs, 1, 1)  # [B, num_q, embed_dims]
        ref_points = inverse_sigmoid(self.reference_points.weight.clone()).sigmoid()  # [num_q, 3]

        assert list(query_embedding.shape) == [bs, self.num_queries_with_group, self.embed_dims]

        # Prepare the propagated track queries, concat with the original dummy queries
        if track_query_info is not None and "track_query_hs_embeds" in track_query_info[0]:
            new_query_embeds = []
            new_init_ref_pts = []
            for b_i in range(bs):
                new_queries = torch.cat(
                    [
                        track_query_info[b_i]["track_query_hs_embeds"],
                        query_embedding[b_i],
                        track_query_info[b_i]["pad_hs_embeds"],
                    ],
                    dim=0,
                )
                new_query_embeds.append(new_queries)
                new_ref = torch.cat(
                    [
                        track_query_info[b_i]["trans_track_query_boxes"],
                        ref_points,
                        track_query_info[b_i]["pad_query_boxes"],
                    ],
                    dim=0,
                )  # [num_q, 3]
                new_init_ref_pts.append(new_ref)
                # print('length of track queries', track_query_info[b_i]['track_query_hs_embeds'].shape[0])

            # concat to get the track+dummy queries
            query_embedding = torch.stack(new_query_embeds, dim=0)  # [bs, num_q, C]
            ref_points = torch.stack(new_init_ref_pts, dim=0)  # [bs, num_q, 3]
            query_kp_mask = torch.stack([t["query_padding_mask"] for t in track_query_info], dim=0)
        else:
            ref_points = ref_points.unsqueeze(0).repeat(bs, 1, 1)
            query_kp_mask = self.query_embedding.weight.new_zeros((bs, self.num_queries_with_group), dtype=torch.bool)

        query_embedding = query_embedding.permute(1, 0, 2)  # [num_q, B, embed_dims]

        key_pos, feat_flatten, mask_flatten = None, None, None

        if bev_features is not None:
            bs, C, H, W = bev_features.shape
            # key_pos = self._rv_pe(bev_features, img_metas)     # rv pos embed [bs*num_cam, h, w, c]
            # key_pos = key_pos.reshape(bs, *key_pos.shape[1:])  # [bs, num_cam, h, w, c]
            # key_pos = key_pos.permute(1, 2, 3, 0, 4).flatten(0, 2)   # [n_cam, h, w, bs, c] -> [n_cam*h*w, bs, c]

            # reshape multi-view imgs
            # [bs, N_cam, C, H, W] -> [N_cam, H, W, bs, C] -> [N_cam*H*W, bs, C]
            # [bs, C, H, W] -> [H, W, bs, C] -> [H*W, bs, C]
            feat_flatten = bev_features.permute(2, 3, 0, 1).flatten(0, 1)
            mask_flatten = bev_features.new_zeros(bs, H * W)

        if lidar_features is not None:  # 尝试一下简单点的编码方式，和最新障碍物的 e2ehead 对应

            # 1. get query pos based on 3d coords
            num_pos_feats = self.embed_dims // 2
            query_pos = self.query_pe_mlp(pos2posemb3d(ref_points, num_pos_feats))  # [bs, n_query, C]
            query_pos = query_pos.permute(1, 0, 2)

            # 2. get lidar pos based on 3d coords
            grid_h, grid_w = lidar_features.shape[-2:]
            lidar_coords = self._bev_coords(
                grid_h, grid_w, self.grid_height, device=query_pos.device
            )  # [grid_h, grid_w, 3]
            lidar_coords = lidar_coords.flatten(0, 1).unsqueeze(0).repeat(bs, 1, 1)  # [bs, grid_h*grid_w, 3]
            # query pe 和 lidar pe 使用完全相同且共享的编码方式
            lidar_pos = self.query_pe_mlp(pos2posemb3d(lidar_coords, num_pos_feats))  # [bs, grid_h*grid_w, C]
            lidar_pos = lidar_pos.permute(1, 0, 2)
            # 拼起来新的 feat_flatten / key_pos / mask_flatten
            # feat_flatten.shape: torch.Size([7680, 2, 256]), grid_h*grid_w: 19152
            # 3. cat feats
            lidar_features = self.shared_conv(lidar_features)
            lidar_feat_flatten = lidar_features.flatten(2, 3).permute(2, 0, 1)
            feat_flatten = (
                torch.cat([feat_flatten, lidar_feat_flatten], dim=0) if feat_flatten is not None else lidar_feat_flatten
            )
            key_pos = torch.cat([key_pos, lidar_pos], dim=0) if key_pos is not None else lidar_pos
            lidar_mask_flatten = lidar_features.new_zeros(bs, grid_h * grid_w)
            mask_flatten = (
                torch.cat([mask_flatten, lidar_mask_flatten], dim=1) if mask_flatten is not None else lidar_mask_flatten
            )

        else:  # 按照反投影来编码
            # query_pos = self._rv_query_embed(ref_points, img_metas)   # query pos embed
            # query_pos = query_pos.permute(1, 0, 2)
            pass

        # import numpy as np
        # query_pos_np = query_pos.detach().cpu().numpy()
        # key_pos_np = key_pos.detach().cpu().numpy()
        # np.savez('query_pos_400.npz', data=query_pos_np)
        # np.savez('key_pos_400.npz', data=key_pos_np)
        # self. transformer 输入格式要求
        # query: [num_query, bs, C]
        # key: [N_cam*H*W, bs, C]
        # value: [N_cam*H*W, bs, C]
        # query_pos: [num_query, bs, C]
        # key_pos: [N_cam*H*W, bs, C]
        # key_padding_mask: [bs, N_cam*H*W]
        # memory_bank: 透传

        inter_queries, inter_references = self.transformer(
            query=query_embedding,
            key=feat_flatten,
            value=feat_flatten,
            query_pos=None,
            key_pos=key_pos,
            key_padding_mask=mask_flatten,
            query_key_padding_mask=query_kp_mask,
            reference_points=ref_points,
            reg_branches=[
                self.line_reg_branchs,
                self.stop_reg_branchs,
                self.box_reg_branchs,
                self.entrance_reg_branchs,
            ],
            predict_refine=self.predict_refine,
            memory_bank=memory_bank,
            query_split=self.query_split_with_group,
        )

        outputs = []
        for i, (queries) in enumerate(inter_queries):  # 每一层的输出
            line_scores = self.line_cls_branches[i](
                queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...].reshape(
                    bs, self.line_query_num_ins, -1
                )
            )  # (bs, num_q, num_classes)
            stop_scores = self.stop_cls_branches[i](
                queries[:, self.stop_query_index_with_group[0] : self.stop_query_index_with_group[1], ...].reshape(
                    bs, self.stop_query_num_ins, -1
                )
            )
            box_scores = self.box_cls_branches[i](
                queries[:, self.box_query_index_with_group[0] : self.box_query_index_with_group[1], ...].reshape(
                    bs, self.box_query_num_ins, -1
                )
            )
            entrance_scores = self.entrance_cls_branches[i](
                queries[
                    :, self.entrance_query_index_with_group[0] : self.entrance_query_index_with_group[1], ...
                ].reshape(bs, self.entrance_query_num_ins, -1)
            )

            bs = inter_references[0][i].shape[0]
            line_reg_points = inter_references[0][i].reshape(
                bs, self.line_query_num_ins, -1
            )  # (bs, num_q, 3*num_points)
            stop_reg_points = inter_references[1][i].reshape(
                bs, self.stop_query_num_ins, -1
            )  # (bs, num_q, 3*num_points)
            box_reg_points = inter_references[2][i].reshape(bs, self.box_query_num_ins, -1)  # (bs, num_q, 3*num_points)
            entrance_reg_points = inter_references[3][i].reshape(
                bs, self.entrance_query_num_ins, -1
            )  # (bs, num_q, 3*num_points)

            lane_color_scores = self.lane_color_attr_branches[i](
                queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...]
            ).reshape(bs, self.line_query_num_ins, -1)
            lane_dash_scores = self.lane_dash_attr_branches[i](
                queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...]
            ).reshape(bs, self.line_query_num_ins, -1)
            curb_scores = self.curb_attr_branches[i](
                queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...]
            ).reshape(bs, self.line_query_num_ins, -1)
            arrow_scores = self.arrow_attr_branches[i](
                queries[:, self.box_query_index_with_group[0] : self.box_query_index_with_group[1], ...]
            ).reshape(bs, self.box_query_num_ins, -1)
            direction_scores = self.direction_attr_branches[i](
                queries[:, self.box_query_index_with_group[0] : self.box_query_index_with_group[1], ...]
            ).reshape(bs, self.box_query_num_ins, -1)

            scores = torch.cat((line_scores, stop_scores, box_scores, entrance_scores), axis=1)
            scores_list, line_scores_list, stop_scores_list, box_scores_list, entrance_scores_list = [], [], [], [], []
            line_reg_points_list, stop_reg_points_list, box_reg_points_list, entrance_reg_points_list = [], [], [], []
            (
                lane_color_scores_list,
                lane_dash_scores_list,
                curb_scores_list,
                arrow_scores_list,
                direction_scores_list,
            ) = ([], [], [], [], [])

            for j in range(len(scores)):
                # padding queries should not be output
                line_reg_points_list.append(line_reg_points[j])
                stop_reg_points_list.append(stop_reg_points[j])
                box_reg_points_list.append(box_reg_points[j])
                entrance_reg_points_list.append(entrance_reg_points[j])

                scores_list.append(scores[j])
                line_scores_list.append(line_scores[j])
                stop_scores_list.append(stop_scores[j])
                box_scores_list.append(box_scores[j])
                entrance_scores_list.append(entrance_scores[j])

                lane_color_scores_list.append(lane_color_scores[j])
                lane_dash_scores_list.append(lane_dash_scores[j])
                curb_scores_list.append(curb_scores[j])
                arrow_scores_list.append(arrow_scores[j])
                direction_scores_list.append(direction_scores[j])

            pred_dict = {
                "scores": scores_list,
                "line_scores": line_scores_list,
                "stop_scores": stop_scores_list,
                "box_scores": box_scores_list,
                "entrance_scores": entrance_scores_list,
                "lines": line_reg_points_list,
                "stops": stop_reg_points_list,
                "boxes": box_reg_points_list,
                "entrances": entrance_reg_points_list,
                "lane_color_scores": lane_color_scores_list,
                "lane_dash_scores": lane_dash_scores_list,
                "curb_scores": curb_scores_list,
                "arrow_scores": arrow_scores_list,
                "direction_scores": direction_scores_list,
            }
            if return_matching:
                pred_dict["hs_embeds"] = queries
            outputs.append(pred_dict)

        # Pass in the track query information to massage the cost matrix
        loss_dict, det_match_idxs, det_match_gt_idxs, gt_info_list, matched_reg_cost = self.loss(
            gts=gts, preds=outputs, track_info=track_query_info, scene_weight=scene_weight
        )

        # output
        # [bs, n_query, n_pts, n_attr]
        lane_color_scores = lane_color_scores.view(
            bs, self.line_query_num_ins, self.line_num_points, self.num_lane_color_attr
        )
        # [bs, n_query, n_pts, n_attr]
        lane_dash_scores = lane_dash_scores.view(
            bs, self.line_query_num_ins, self.line_num_points, self.num_lane_dash_attr
        )
        # [bs, n_query, n_pts, n_attr]
        curb_scores = curb_scores.view(bs, self.line_query_num_ins, self.line_num_points, self.num_curb_attr)
        # [bs, n_query, n_pts, n_attr]
        arrow_scores = arrow_scores.view(bs, self.box_query_num_ins, self.box_num_points, self.num_arrow_attr)
        # [bs, n_query, n_pts, n_attr]
        direction_scores = direction_scores.view(
            bs, self.box_query_num_ins, self.box_num_points, self.num_drection_attr
        )

        if self.loss_cls.use_sigmoid:  # 20241023 版为 True
            scores = scores.sigmoid()

        line_reg_points = line_reg_points.view(bs, self.line_query_num_ins, self.line_num_points, 3)
        stop_reg_points = stop_reg_points.view(bs, self.stop_query_num_ins, self.stop_num_points, 3)
        box_reg_points = box_reg_points.view(bs, self.box_query_num_ins, self.box_num_points, 3)
        entrance_reg_points = entrance_reg_points.view(bs, self.entrance_query_num_ins, self.entrance_num_points, 3)

        all_pts = (line_reg_points, stop_reg_points, box_reg_points, entrance_reg_points)
        scores_all = (scores, lane_color_scores, lane_dash_scores, curb_scores, arrow_scores, direction_scores)
        if return_matching:
            return (
                loss_dict,
                outputs[-1],
                det_match_idxs[-1],
                det_match_gt_idxs[-1],
                matched_reg_cost[-1],
                gt_info_list[-1],
            )
        else:
            return outputs, loss_dict, det_match_idxs, det_match_gt_idxs, gt_info_list, all_pts, scores_all

    def forward_test(self, img_features, img_metas, track_query_info=None, memory_bank=None):
        """
        Args:
            bev_feature (List[Tensor]): shape [B, C, H, W]
                feature in bev view
        Outs:
            preds_dict (list[dict]):
                lines (Tensor): Classification score of all
                    decoder layers, has shape
                    [bs, num_query, 2*num_points]
                scores (Tensor):
                    [bs, num_query,]
        """

        bs, N_cam, C, H, W = img_features.shape

        query_embedding = self.query_embedding.weight[None, ...].repeat(bs, 1, 1)  # [B, num_q, embed_dims]
        ref_points = inverse_sigmoid(self.reference_points.weight.clone()).sigmoid()  # [num_q, 3]

        assert list(query_embedding.shape) == [bs, self.num_queries, self.embed_dims]

        # Prepare the propagated track queries, concat with the original dummy queries
        if track_query_info is not None and "track_query_hs_embeds" in track_query_info[0]:
            prev_hs_embed = torch.stack([t["track_query_hs_embeds"] for t in track_query_info])
            prev_boxes = torch.stack([t["trans_track_query_boxes"] for t in track_query_info])
            prev_boxes = rearrange(prev_boxes, "b n (k c) -> b n k c", c=2)

            # concat to get the track+dummy queries
            query_embedding = torch.cat([prev_hs_embed, query_embedding], dim=1)
            # init_reference_points = torch.cat([prev_boxes, init_reference_points], dim=1)
        else:
            ref_points = ref_points.unsqueeze(0).repeat(bs, 1, 1)

        query_kp_mask = query_embedding.new_zeros((bs, query_embedding.shape[1]), dtype=torch.bool)

        query_embedding = query_embedding.permute(1, 0, 2)  # [num_q, B, embed_dims]

        key_pos = self._rv_pe(img_features, img_metas)  # rv pos embed [bs*num_cam, h, w, c]
        key_pos = key_pos.reshape(bs, N_cam, *key_pos.shape[1:])  # [bs, num_cam, h, w, c]
        key_pos = key_pos.permute(1, 2, 3, 0, 4).flatten(0, 2)  # [n_cam, h, w, bs, c] -> [n_cam*h*w, bs, c]

        query_pos = self._rv_query_embed(ref_points, img_metas)  # query pos embed
        query_pos = query_pos.permute(1, 0, 2)

        # reshape multi-view imgs
        # [bs, N_cam, C, H, W] -> [N_cam, H, W, bs, C] -> [N_cam*H*W, bs, C]
        feat_flatten = img_features.permute(1, 3, 4, 0, 2).flatten(0, 2)
        mask_flatten = img_features.new_zeros(bs, N_cam * H * W)

        # self. transformer 输入格式要求
        # query: [num_query, bs, C]
        # key: [N_cam*H*W, bs, C]
        # value: [N_cam*H*W, bs, C]
        # query_pos: [num_query, bs, C]
        # key_pos: [N_cam*H*W, bs, C]
        # key_padding_mask: [bs, N_cam*H*W]
        # memory_bank: 透传
        inter_queries, inter_references = self.transformer(
            query=query_embedding,
            key=feat_flatten,
            value=feat_flatten,
            query_pos=query_pos,
            key_pos=key_pos,
            key_padding_mask=mask_flatten,
            query_key_padding_mask=query_kp_mask,
            reference_points=ref_points,
            reg_branches=[self.line_reg_branchs, self.stop_reg_branchs, self.box_reg_branchs],
            predict_refine=self.predict_refine,
            memory_bank=memory_bank,
        )

        outputs = []
        for i, (queries) in enumerate(inter_queries):  # 每一层的输出
            scores = self.cls_branches[i](queries)  # (bs, num_q, num_classes)

            line_reg_points = inter_references[0][i]  # (bs, num_q, num_points, 3)
            line_reg_points = line_reg_points.flatten(2, 3)  # (bs, num_q, 3*num_points)
            stop_reg_points = inter_references[1][i]
            stop_reg_points = stop_reg_points.flatten(2, 3)  # (bs, num_q, 3*num_points)
            box_reg_points = inter_references[2][i]
            box_reg_points = box_reg_points.flatten(2, 3)  # (bs, num_q, 3*num_points)

            lane_color_scores = self.lane_color_attr_branches[i](queries)
            lane_dash_scores = self.lane_dash_attr_branches[i](queries)
            curb_scores = self.curb_attr_branches[i](queries)
            arrow_scores = self.arrow_attr_branches[i](queries)

            scores_list = []
            line_reg_points_list, stop_reg_points_list, box_reg_points_list = [], [], []
            lane_color_scores_list, lane_dash_scores_list, curb_scores_list, arrow_scores_list = [], [], [], []
            for j in range(len(scores)):
                # padding queries should not be output
                line_reg_points_list.append(line_reg_points[j])
                stop_reg_points_list.append(stop_reg_points[j])
                box_reg_points_list.append(box_reg_points[j])
                scores_list.append(scores[j])
                lane_color_scores_list.append(lane_color_scores[j])
                lane_dash_scores_list.append(lane_dash_scores[j])
                curb_scores_list.append(curb_scores[j])
                arrow_scores_list.append(arrow_scores[j])

            pred_dict = {
                "scores": scores_list,
                "lines": line_reg_points_list,
                "stops": stop_reg_points_list,
                "boxes": box_reg_points_list,
                "lane_color_scores": lane_color_scores_list,
                "lane_dash_scores": lane_dash_scores_list,
                "curb_scores": curb_scores_list,
                "arrow_scores": arrow_scores_list,
            }

            outputs.append(pred_dict)

        return outputs

    def forward_infer_single(self, bev_features, img_metas):
        """只在部署 infer 时用到
        Args:
            bev_feature (List[Tensor]): shape [B, C, H, W]
                feature in bev view
        Outs:
            preds_dict (list[dict]):
                lines (Tensor): Classification score of all
                    decoder layers, has shape
                    [bs, num_query, 2*num_points]
                scores (Tensor):
                    [bs, num_query,]
        """
        # bs, N_cam, C, H, W = img_features.shape
        bs, C, H, W = bev_features.shape
        # img_masks = bev_features.new_zeros((bs, H, W))
        # pos_embed = None

        query_embedding = self.query_embedding.weight[None, ...].repeat(bs, 1, 1)  # [B, num_q, embed_dims]
        ref_points = inverse_sigmoid(self.reference_points.weight.clone()).sigmoid()  # [num_q, 3]

        assert list(query_embedding.shape) == [bs, self.num_queries_with_group, self.embed_dims]

        ref_points = ref_points.unsqueeze(0).repeat(bs, 1, 1)

        query_kp_mask = query_embedding.new_zeros((bs, query_embedding.shape[1]), dtype=torch.bool)

        query_embedding = query_embedding.permute(1, 0, 2)  # [num_q, B, embed_dims]

        query_pos, key_pos = None, None

        # reshape multi-view imgs
        # [bs, N_cam, C, H, W] -> [N_cam, H, W, bs, C] -> [N_cam*H*W, bs, C]

        feat_flatten = bev_features.permute(2, 3, 0, 1).flatten(0, 1)
        mask_flatten = bev_features.new_zeros(bs, H * W)

        # import numpy as np
        # device = key_pos.device
        # query_pos_np = query_pos.detach().cpu().numpy()
        # key_pos_np = key_pos.detach().cpu().numpy()
        # np.savez(f'bmk_query_pos_400_{device}.npz', data=query_pos_np)
        # np.savez(f'bmk_key_pos_400_{device}.npz', data=key_pos_np)
        """
        import numpy as np
        hf9_query_pos = np.load(f'bmk_query_pos_400_cuda:0.npz', allow_pickle=False)
        hf9_key_pos = np.load(f'bmk_key_pos_400_cuda:0.npz', allow_pickle=False)
        device = key_pos.device
        hf9_query_pos_tensor = torch.tensor(hf9_query_pos['data']).to(device)
        hf9_key_pos_tensor = torch.tensor(hf9_key_pos['data']).to(device)
        """

        # origin pos
        # hf9_query_pos_tensor = query_pos
        # hf9_key_pos_tensor = key_pos

        # self. transformer 输入格式要求
        # query: [num_query, bs, C]
        # key: [N_cam*H*W, bs, C]
        # value: [N_cam*H*W, bs, C]
        # query_pos: [num_query, bs, C]
        # key_pos: [N_cam*H*W, bs, C]
        # key_padding_mask: [bs, N_cam*H*W]
        # memory_bank: 透传
        inter_queries, inter_references = self.transformer(
            query=query_embedding,
            key=feat_flatten,
            value=feat_flatten,
            query_pos=query_pos,  # hf9_query_pos_tensor,
            key_pos=key_pos,  # hf9_key_pos_tensor,
            key_padding_mask=mask_flatten,
            query_key_padding_mask=query_kp_mask,
            reference_points=ref_points,
            reg_branches=[
                self.line_reg_branchs,
                self.stop_reg_branchs,
                self.box_reg_branchs,
                self.entrance_reg_branchs,
            ],
            predict_refine=self.predict_refine,
            memory_bank=None,
            query_split=self.query_split_with_group,
        )

        i = -1
        queries = inter_queries[-1]  # 最后一层输出

        line_scores = self.line_cls_branches[i](
            queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...].reshape(
                bs, self.line_query_num_ins, -1
            )
        )  # (bs, num_q, num_classes)
        stop_scores = self.stop_cls_branches[i](
            queries[:, self.stop_query_index_with_group[0] : self.stop_query_index_with_group[1], ...].reshape(
                bs, self.stop_query_num_ins, -1
            )
        )
        box_scores = self.box_cls_branches[i](
            queries[:, self.box_query_index_with_group[0] : self.box_query_index_with_group[1], ...].reshape(
                bs, self.box_query_num_ins, -1
            )
        )
        entrance_scores = self.entrance_cls_branches[i](
            queries[:, self.entrance_query_index_with_group[0] : self.entrance_query_index_with_group[1], ...].reshape(
                bs, self.entrance_query_num_ins, -1
            )
        )

        bs = inter_references[0][i].shape[0]
        line_reg_points = inter_references[0][i].reshape(bs, self.line_query_num_ins, -1)  # (bs, num_q, 3*num_points)
        stop_reg_points = inter_references[1][i].reshape(bs, self.stop_query_num_ins, -1)  # (bs, num_q, 3*num_points)
        box_reg_points = inter_references[2][i].reshape(bs, self.box_query_num_ins, -1)  # (bs, num_q, 3*num_points)
        entrance_reg_points = inter_references[3][i].reshape(
            bs, self.entrance_query_num_ins, -1
        )  # (bs, num_q, 3*num_points)

        lane_color_scores = self.lane_color_attr_branches[i](
            queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...]
        ).reshape(bs, self.line_query_num_ins, -1)
        lane_dash_scores = self.lane_dash_attr_branches[i](
            queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...]
        ).reshape(bs, self.line_query_num_ins, -1)
        curb_scores = self.curb_attr_branches[i](
            queries[:, self.line_query_index_with_group[0] : self.line_query_index_with_group[1], ...]
        ).reshape(bs, self.line_query_num_ins, -1)
        arrow_scores = self.arrow_attr_branches[i](
            queries[:, self.box_query_index_with_group[0] : self.box_query_index_with_group[1], ...]
        ).reshape(bs, self.box_query_num_ins, -1)
        direction_scores = self.direction_attr_branches[i](
            queries[:, self.box_query_index_with_group[0] : self.box_query_index_with_group[1], ...]
        ).reshape(bs, self.box_query_num_ins, -1)

        scores = torch.cat((line_scores, stop_scores, box_scores, entrance_scores), axis=1)

        # output
        # [bs, n_query, n_pts, n_attr]
        lane_color_scores = lane_color_scores.view(
            bs, self.line_query_num_ins, self.line_num_points, self.num_lane_color_attr
        )
        # [bs, n_query, n_pts, n_attr]
        lane_dash_scores = lane_dash_scores.view(
            bs, self.line_query_num_ins, self.line_num_points, self.num_lane_dash_attr
        )
        # [bs, n_query, n_pts, n_attr]
        curb_scores = curb_scores.view(bs, self.line_query_num_ins, self.line_num_points, self.num_curb_attr)
        # [bs, n_query, n_pts, n_attr]
        arrow_scores = arrow_scores.view(bs, self.box_query_num_ins, self.box_num_points, self.num_arrow_attr)
        # [bs, n_query, n_pts, n_attr]
        direction_scores = direction_scores.view(
            bs, self.box_query_num_ins, self.box_num_points, self.num_drection_attr
        )

        if self.loss_cls.use_sigmoid:  # 20241023 版为 True
            scores = scores.sigmoid()

        line_reg_points = line_reg_points.view(bs, self.line_query_num_ins, self.line_num_points, 3)
        stop_reg_points = stop_reg_points.view(bs, self.stop_query_num_ins, self.stop_num_points, 3)
        box_reg_points = box_reg_points.view(bs, self.box_query_num_ins, self.box_num_points, 3)
        entrance_reg_points = entrance_reg_points.view(bs, self.entrance_query_num_ins, self.entrance_num_points, 3)

        all_pts = (line_reg_points, stop_reg_points, box_reg_points, entrance_reg_points)
        scores_all = (scores, lane_color_scores, lane_dash_scores, curb_scores, arrow_scores, direction_scores)
        return (all_pts, scores_all)

    @force_fp32(apply_to=("score_pred", "lines_pred", "gt_lines"))
    def _get_target_single(
        self,
        type_name,
        score_pred_ori,
        lines_pred_ori,
        gt_labels,
        gt_lines,
        gt_attrs,
        track_info=None,
        scene_weight=None,
        point_num=None,
        gt_valid_region=None,
        gt_bboxes_ignore=None,
    ):
        """
        Compute regression and classification targets for one image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            score_pred (Tensor): Box score logits from a single decoder layer
                for one image. Shape [num_query, cls_out_channels].
            lines_pred (Tensor):
                shape [num_query, 2*num_points]
            gt_labels (torch.LongTensor)
                shape [num_gt, ]
            gt_lines (Tensor):
                shape [num_gt, 2*num_points].

        Returns:
            tuple[Tensor]: a tuple containing the following for one sample.
                - labels (LongTensor): Labels of each image.
                    shape [num_query, 1]
                - label_weights (Tensor]): Label weights of each image.
                    shape [num_query, 1]
                - lines_target (Tensor): Lines targets of each image.
                    shape [num_query, num_points, 2]
                - lines_weights (Tensor): Lines weights of each image.
                    shape [num_query, num_points, 2]
                - pos_inds (Tensor): Sampled positive indices for each image.
                - neg_inds (Tensor): Sampled negative indices for each image.
        """
        # 排除 lines_pred gt_valid_region
        lines_pred_reshaped = lines_pred_ori.view(lines_pred_ori.shape[0], -1, 3)

        x_coords = torch.mean(lines_pred_reshaped[:, :, 0], dim=1)
        y_coords = torch.mean(lines_pred_reshaped[:, :, 1], dim=1)

        minx, miny, _, maxx, maxy, _ = gt_valid_region
        valid_mask = (x_coords >= minx) & (x_coords <= maxx) & (y_coords >= miny) & (y_coords <= maxy)

        # 如果仅有少量query处于有效区域，应如何处理？->按照 y 区域分配定量query + 在有效区域的点
        query_s_idx, query_e_idx = int(len(lines_pred_ori) * miny), int(len(lines_pred_ori) * maxy)
        valid_mask[query_s_idx:query_e_idx] = True
        if valid_mask.sum() == 0:
            valid_mask[:1] = True

        score_pred = score_pred_ori[valid_mask]
        lines_pred = lines_pred_ori[valid_mask]
        # assigner and sampler
        # We massage the matching cost here using the track info, following
        # the 3-type supervision of TrackFormer/MOTR
        num_pred_lines = len(lines_pred)
        num_gt = gt_lines.numel()
        if num_gt > 0 and num_pred_lines > 0:  # stopline、arrow 可能为空，跳过匹配
            if type_name in ["stop", "entrance"]:
                assign_pred = torch.cat((lines_pred[:, 0:3], lines_pred[:, 6:9]), axis=1)
                assert_gt = torch.cat((gt_lines[:, :, 0:3], gt_lines[:, :, 6:9]), axis=2)
            else:
                assign_pred = lines_pred
                assert_gt = gt_lines
            assign_result, gt_permute_idx, matched_reg_cost = self.assigner.assign(
                preds=dict(
                    lines=assign_pred,
                    scores=score_pred,
                ),
                gts=dict(
                    lines=assert_gt,
                    labels=gt_labels,
                ),
                track_info=track_info,
                gt_bboxes_ignore=gt_bboxes_ignore,
            )
            sampling_result = self.sampler.sample(assign_result, lines_pred, gt_lines)

            pos_inds = sampling_result.pos_inds
            neg_inds = sampling_result.neg_inds
            pos_gt_inds = sampling_result.pos_assigned_gt_inds

            labels = gt_lines.new_full((num_pred_lines,), self.num_classes, dtype=torch.long)  # (num_q, )
            labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds]
        else:
            gt_permute_idx = None
            pos_inds = torch.tensor(0)
            neg_inds = torch.tensor(score_pred.shape[0])
            pos_gt_inds, matched_reg_cost = [], 0  # TODO 这样的赋值是否合适？
            labels = gt_lines.new_full((num_pred_lines,), self.num_classes, dtype=torch.long)  # (num_q, )

        label_weights = gt_lines.new_ones(num_pred_lines)  # (num_q, )

        lines_target = gt_lines.new_full(lines_pred.shape, 0, dtype=torch.float)
        lines_weights = gt_lines.new_full(lines_pred.shape, 0, dtype=torch.long)

        attr_num = point_num * self.attr_nums  # 虚实/ 颜色 / 形状 / 是否栏杆 / 箭头属性 # TODO： attr_num 需要外部传入
        attr_target = gt_lines.new_full((lines_pred.shape[0], attr_num), 0, dtype=torch.long)  # (num_q, attr*num_pts)
        attr_weights = gt_lines.new_full((lines_pred.shape[0], attr_num), 0, dtype=torch.long)  # (num_q, attr**num_pts)

        if num_gt > 0 and num_pred_lines > 0:
            if gt_permute_idx is not None:  # using permute invariant label
                # gt_permute_idx: (num_q, num_gt)
                # pos_inds: which query is positive
                # pos_gt_inds: which gt each pos pred is assigned
                # single_matched_gt_permute_idx: which permute order is matched
                single_matched_gt_permute_idx = gt_permute_idx[pos_inds, pos_gt_inds]
                lines_target[pos_inds] = gt_lines[pos_gt_inds, single_matched_gt_permute_idx].type(
                    lines_target.dtype
                )  # (num_q, 3*num_pts)
                attr_target[pos_inds] = gt_attrs[pos_gt_inds, single_matched_gt_permute_idx].type(
                    attr_target.dtype
                )  # (num_q, attr*num_pts)
            else:
                lines_target[pos_inds] = sampling_result.pos_gt_bboxes.type(lines_target.dtype)  # (num_q, 3*num_pts)
                attr_target[pos_inds] = sampling_result.pos_gt_bboxes.type(attr_target.dtype)  # (num_q, attr*num_pts)

            label_weights[pos_inds] = scene_weight[0]
            lines_weights[pos_inds] = scene_weight[0]  # (num_q, 3*num_pts)
            attr_weights[pos_inds] = scene_weight[0]  # (num_q, attr*num_pts)

        # normalization
        # n = lines_weights.sum(-1, keepdim=True) # (num_q, 1)
        # lines_weights = lines_weights / n.masked_fill(n == 0, 1) # (num_q, 2*num_pts)
        # [0, ..., 0] for neg ind and [1/npts, ..., 1/npts] for pos ind

        return (
            valid_mask,
            labels,
            label_weights,
            lines_target,
            lines_weights,
            attr_target,
            attr_weights,
            pos_inds,
            neg_inds,
            pos_gt_inds,
            matched_reg_cost,
        )

    def merge_label_target(self, line_target_info, stop_target_info, box_target_info, entrance_target_info):
        all_scores_list, all_merge_label_list, all_merge_label_weights_list = [], [], []
        for i in range(len(line_target_info["labels_list"])):
            merge_score_list = torch.cat(
                (
                    line_target_info["scores_pred"][i],
                    stop_target_info["scores_pred"][i],
                    box_target_info["scores_pred"][i],
                    entrance_target_info["scores_pred"][i],
                ),
                dim=0,
            )
            merge_label_list = torch.cat(
                (
                    line_target_info["labels_list"][i],
                    stop_target_info["labels_list"][i],
                    box_target_info["labels_list"][i],
                    entrance_target_info["labels_list"][i],
                ),
                dim=0,
            )
            merge_label_weights_list = torch.cat(
                (
                    line_target_info["label_weights_list"][i],
                    stop_target_info["label_weights_list"][i],
                    box_target_info["label_weights_list"][i],
                    entrance_target_info["label_weights_list"][i],
                ),
                dim=0,
            )
            all_scores_list.append(merge_score_list)
            all_merge_label_list.append(merge_label_list)
            all_merge_label_weights_list.append(merge_label_weights_list)

        return all_scores_list, all_merge_label_list, all_merge_label_weights_list

    def get_target_onetype(
        self,
        type_name,
        scores_pred,
        lines_pred,
        attrs_pred,
        gt_labels,
        gt_lines,
        gt_attrs,
        point_num,
        track_info,
        scene_weight,
        gt_bboxes_ignore_list,
        gt_valid_region,
    ):
        point_num = [point_num for _ in scores_pred]
        type_name = [type_name for _ in scores_pred]
        (
            valid_mask_list,
            labels_list,
            label_weights_list,
            lines_targets_list,
            lines_weights_list,
            attr_target_list,
            attr_weights_list,
            pos_inds_list,
            neg_inds_list,
            pos_gt_inds_list,
            matched_reg_cost,
        ) = multi_apply(
            self._get_target_single,
            type_name,
            scores_pred,
            lines_pred,
            gt_labels,
            gt_lines,
            gt_attrs,
            track_info,
            scene_weight,
            point_num,
            gt_valid_region,
            gt_bboxes_ignore=gt_bboxes_ignore_list,
        )

        num_total_pos = sum((inds.numel() for inds in pos_inds_list))
        num_total_neg = sum((inds.numel() for inds in neg_inds_list))

        if track_info[0] is not None:
            # remove the padding elements from the neg counting
            padding_mask = torch.cat([t["query_padding_mask"] for t in track_info], dim=0)
            num_padding = padding_mask.sum()
            num_total_neg -= num_padding
        for i in range(len(valid_mask_list)):
            scores_pred[i] = scores_pred[i][valid_mask_list[i]]
            lines_pred[i] = lines_pred[i][valid_mask_list[i]]
            for k in attrs_pred.keys():
                attrs_pred[k][i] = attrs_pred[k][i][valid_mask_list[i]]
        return {
            "scores_pred": scores_pred,
            "lines_pred": lines_pred,
            "attrs_pred": attrs_pred,
            "labels_list": labels_list,
            "label_weights_list": label_weights_list,
            "lines_targets_list": lines_targets_list,
            "lines_weights_list": lines_weights_list,
            "attr_target_list": attr_target_list,
            "attr_weights_list": attr_weights_list,
            "pos_inds_list": pos_inds_list,
            "neg_inds_list": neg_inds_list,
            "pos_gt_inds_list": pos_gt_inds_list,
            "matched_reg_cost": matched_reg_cost,
            "num_total_pos": num_total_pos,
            "num_total_neg": num_total_neg,
        }

    # @force_fp32(apply_to=('preds', 'gts'))
    def get_targets(self, preds, gts, track_info=None, gt_bboxes_ignore_list=None, scene_weight=None):
        """
            Compute regression and classification targets for a batch image.
            Outputs from a single decoder layer of a single feature level are used.
            Args:
                preds (dict):
                    - lines (Tensor): shape (bs, num_queries, 2*num_points)
                    - scores (Tensor): shape (bs, num_queries, num_class_channels)
                gts (dict):
                    - class_label (list[Tensor]): tensor shape (num_gts, )
                    - lines (list[Tensor]): tensor shape (num_gts, 2*num_points)
                gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                    boxes which can be ignored for each image. Default None.
            Returns:
                tuple: a tuple containing the following targets.
                    - labels_list (list[Tensor]): Labels for all images.
                    - label_weights_list (list[Tensor]): Label weights for all \
                        images.
                    - lines_targets_list (list[Tensor]): Lines targets for all \
                        images.
                    - lines_weight_list (list[Tensor]): Lines weights for all \
                        images.
                    - num_total_pos (int): Number of positive samples in all \
                        images.
                    - num_total_neg (int): Number of negative samples in all \
                        images.
        """
        assert gt_bboxes_ignore_list is None, "Only supports for gt_bboxes_ignore setting to None."

        # format the inputs
        gt_l_labels = gts["lines_labels"]
        gt_s_labels = gts["stops_labels"]
        gt_b_labels = gts["boxes_labels"]
        gt_e_labels = gts["entrances_labels"]

        gt_lines = gts["lines"]
        gt_stops = gts["stops"]
        gt_boxes = gts["boxes"]
        gt_entrances = gts["entrances"]

        gt_lineattrs = gts["lineattrs"]
        gt_stopattrs = gts["stopattrs"]
        gt_boxattrs = gts["boxattrs"]
        gt_entranceattrs = gts["entranceattrs"]
        gt_valid_region = gts["valid_region"]

        line_scores_pred = preds["line_scores"]
        stop_scores_pred = preds["stop_scores"]
        box_scores_pred = preds["box_scores"]
        entrance_scores_pred = preds["entrance_scores"]

        lines_pred = preds["lines"]
        stops_pred = preds["stops"]
        boxes_pred = preds["boxes"]
        entrances_pred = preds["entrances"]

        lines_attrs_pred = {
            "lane_color_scores": preds["lane_color_scores"],
            "lane_dash_scores": preds["lane_dash_scores"],
            "curb_scores": preds["curb_scores"],
        }
        boxes_attrs_pred = {
            "arrow_scores": preds["arrow_scores"],
            "direction_scores": preds["direction_scores"],
        }
        if track_info is None:
            track_info = [track_info for _ in range(len(gt_l_labels + gt_s_labels + gt_b_labels))]

        line_target_info = self.get_target_onetype(
            "line",
            line_scores_pred,
            lines_pred,
            lines_attrs_pred,
            gt_l_labels,
            gt_lines,
            gt_lineattrs,
            self.line_num_points,
            track_info,
            scene_weight,
            gt_bboxes_ignore_list,
            gt_valid_region,
        )
        num_total_pos = line_target_info["num_total_pos"]
        num_total_neg = line_target_info["num_total_neg"]

        stop_target_info = self.get_target_onetype(
            "stop",
            stop_scores_pred,
            stops_pred,
            {},
            gt_s_labels,
            gt_stops,
            gt_stopattrs,
            self.stop_num_points,
            track_info,
            scene_weight,
            gt_bboxes_ignore_list,
            gt_valid_region,
        )
        num_total_pos += stop_target_info["num_total_pos"]
        num_total_neg += stop_target_info["num_total_neg"]

        box_target_info = self.get_target_onetype(
            "box",
            box_scores_pred,
            boxes_pred,
            boxes_attrs_pred,
            gt_b_labels,
            gt_boxes,
            gt_boxattrs,
            self.box_num_points,
            track_info,
            scene_weight,
            gt_bboxes_ignore_list,
            gt_valid_region,
        )
        num_total_pos += box_target_info["num_total_pos"]
        num_total_neg += box_target_info["num_total_neg"]

        entrance_target_info = self.get_target_onetype(
            "entrance",
            entrance_scores_pred,
            entrances_pred,
            {},
            gt_e_labels,
            gt_entrances,
            gt_entranceattrs,
            self.entrance_num_points,
            track_info,
            scene_weight,
            gt_bboxes_ignore_list,
            gt_valid_region,
        )
        num_total_pos += entrance_target_info["num_total_pos"]
        num_total_neg += entrance_target_info["num_total_neg"]

        scores_list, labels_list, label_weights_list = self.merge_label_target(
            line_target_info, stop_target_info, box_target_info, entrance_target_info
        )

        pos_inds_list = None  # TODO merge line、stop、box info
        pos_gt_inds_list = None  # TODO merge line、stop、box info
        matched_reg_cost = None  # TODO merge line、stop、box info
        new_gts = dict(
            labels=labels_list,  # list[Tensor(num_q, )], length=bs
            label_weights=label_weights_list,  # list[Tensor(num_q, )], length=bs, all ones
            lines=line_target_info["lines_targets_list"],  # list[Tensor(num_q, 2*num_pts)], length=bs
            lines_weights=line_target_info["lines_weights_list"],  # list[Tensor(num_q, 2*num_pts)], length=bs
            lineattrs=line_target_info["attr_target_list"],
            lineattrs_weight=line_target_info["attr_weights_list"],
            stops=stop_target_info["lines_targets_list"]
            if stop_target_info
            else None,  # list[Tensor(num_q, 2*num_pts)], length=bs
            stops_weights=stop_target_info["lines_weights_list"]
            if stop_target_info
            else None,  # list[Tensor(num_q, 2*num_pts)], length=bs
            stopattrs=stop_target_info["attr_target_list"] if stop_target_info else None,
            stopattrs_weight=stop_target_info["attr_weights_list"] if stop_target_info else None,
            boxes=box_target_info["lines_targets_list"]
            if box_target_info
            else None,  # list[Tensor(num_q, 2*num_pts)], length=bs
            boxes_weights=box_target_info["lines_weights_list"]
            if box_target_info
            else None,  # list[Tensor(num_q, 2*num_pts)], length=bs
            boxattrs=box_target_info["attr_target_list"] if box_target_info else None,
            boxattrs_weight=box_target_info["attr_weights_list"] if box_target_info else None,
            entrances=entrance_target_info["lines_targets_list"]
            if entrance_target_info
            else None,  # list[Tensor(num_q, 2*num_pts)], length=bs
            entrances_weights=entrance_target_info["lines_weights_list"]
            if entrance_target_info
            else None,  # list[Tensor(num_q, 2*num_pts)], length=bs
            entranceattrs=entrance_target_info["attr_target_list"] if entrance_target_info else None,
            entranceattrs_weight=entrance_target_info["attr_weights_list"] if entrance_target_info else None,
        )

        new_pred_dict = {
            "scores": scores_list,
            "line_scores": line_target_info["scores_pred"],
            "stop_scores": stop_target_info["scores_pred"],
            "box_scores": box_target_info["scores_pred"],
            "entrance_scores": entrance_target_info["scores_pred"],
            "lines": line_target_info["lines_pred"],
            "stops": stop_target_info["lines_pred"],
            "boxes": box_target_info["lines_pred"],
            "entrances": entrance_target_info["lines_pred"],
            "lane_color_scores": line_target_info["attrs_pred"]["lane_color_scores"],
            "lane_dash_scores": line_target_info["attrs_pred"]["lane_dash_scores"],
            "curb_scores": line_target_info["attrs_pred"]["curb_scores"],
            "arrow_scores": box_target_info["attrs_pred"]["arrow_scores"],
            "direction_scores": box_target_info["attrs_pred"]["direction_scores"],
        }

        return new_pred_dict, new_gts, num_total_pos, num_total_neg, pos_inds_list, pos_gt_inds_list, matched_reg_cost

    def loss_attr(self, preds, new_gts, cls_avg_factor):
        pred_dash_scores = torch.cat(preds["lane_dash_scores"], dim=0).reshape(
            -1, self.num_lane_dash_attr
        )  # (bs*num_q, 2)
        pred_color_scores = torch.cat(preds["lane_color_scores"], dim=0).reshape(
            -1, self.num_lane_color_attr
        )  # (bs*num_q, 2)
        pred_curb_scores = torch.cat(preds["curb_scores"], dim=0).reshape(
            -1, self.num_curb_attr
        )  # (bs*num_q, cls_out_channles)
        pred_arrow_scores = torch.cat(preds["arrow_scores"], dim=0).reshape(
            -1, self.num_arrow_attr
        )  # (bs*num_q, cls_out_channles)
        pred_direction_scores = torch.cat(preds["direction_scores"], dim=0).reshape(
            -1, self.num_drection_attr
        )  # (bs*num_q, cls_out_channles)

        lineattrs_weight = torch.cat(new_gts["lineattrs_weight"], dim=0).reshape(
            -1, self.attr_nums
        )  # self.attr_nums=5 refer 虚实 / 颜色 / 形状 / 是否栏杆 / 箭头属性
        gt_lineattrs = torch.cat(new_gts["lineattrs"], dim=0).reshape(
            -1, self.attr_nums
        )  # self.attr_nums=5 refer 虚实 / 颜色 / 形状 / 是否栏杆 / 箭头属性
        # gt_lineattrs = gt_lineattrs-1 # unknow 0 不加入模型训练
        dash_loss = self.loss_lane_dash_attr(
            pred_dash_scores, gt_lineattrs[..., 0], lineattrs_weight[..., 0], avg_factor=cls_avg_factor
        )
        color_loss = self.loss_lane_color_attr(
            pred_color_scores, gt_lineattrs[..., 1], lineattrs_weight[..., 1], avg_factor=cls_avg_factor
        )
        curb_loss = self.loss_curb_attr(
            pred_curb_scores, gt_lineattrs[..., 3], lineattrs_weight[..., 3], avg_factor=cls_avg_factor
        )
        line_attr = dash_loss + color_loss + curb_loss

        # if new_gts['boxattrs_weight'] is not None:
        if pred_arrow_scores.shape[0] > 0 and pred_direction_scores.shape[0] > 0:
            boxattrs_weight = torch.cat(new_gts["boxattrs_weight"], dim=0).reshape(
                -1, self.attr_nums
            )  # self.attr_nums=5 refer 虚实 / 颜色 / 形状 / 是否栏杆 / 箭头属性
            gt_boxattrs = torch.cat(new_gts["boxattrs"], dim=0).reshape(
                -1, self.attr_nums
            )  # self.attr_nums=5 refer 虚实 / 颜色 / 形状 / 是否栏杆 / 箭头属性
            arrow_attr = self.loss_arrow_attr(
                pred_arrow_scores, gt_boxattrs[..., 4], boxattrs_weight[..., 4], avg_factor=cls_avg_factor
            )
            direct_attr = self.loss_direction_attr(
                pred_direction_scores, gt_boxattrs[..., 5], boxattrs_weight[..., 5], avg_factor=cls_avg_factor
            )
        else:
            arrow_attr, direct_attr = torch.tensor(0), torch.tensor(0)
        return line_attr, arrow_attr, direct_attr

    def loss_reg_onetype(self, pred_lines_list, gt_lines_list, lines_weights_list, num_total_pos, loss_reg):
        pred_lines = torch.cat(pred_lines_list, dim=0)
        gt_lines = torch.cat(gt_lines_list, dim=0)
        line_weights = torch.cat(lines_weights_list, dim=0)
        # if track_info is not None:
        #     line_weights = line_weights * padding_loss_mask[:, None].float()

        assert len(pred_lines) == len(gt_lines)
        assert len(gt_lines) == len(line_weights)

        loss_reg = loss_reg(pred_lines, gt_lines, line_weights, avg_factor=num_total_pos)

        return loss_reg

    # @force_fp32(apply_to=('preds', 'gts'))
    def loss_single(
        self, preds_ori, gts, track_info=None, gt_bboxes_ignore_list=None, scene_weight=None, reduction="none"
    ):
        """
        Loss function for outputs from a single decoder layer of a single
        feature level.
        Args:
            preds (dict):
                - lines (Tensor): shape (bs, num_queries, 2*num_points)
                - scores (Tensor): shape (bs, num_queries, num_class_channels)
            gts (dict):
                - class_label (list[Tensor]): tensor shape (num_gts, )
                - lines (list[Tensor]): tensor shape (num_gts, 2*num_points)
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """

        # Get target for each sample
        (
            preds,
            new_gts,
            num_total_pos,
            num_total_neg,
            pos_inds_list,
            pos_gt_inds_list,
            matched_reg_cost,
        ) = self.get_targets(preds_ori, gts, track_info, gt_bboxes_ignore_list, scene_weight)

        # Batched all data
        # for k, v in new_gts.items():
        #     new_gts[k] = torch.stack(v, dim=0) # tensor (bs, num_q, ...)

        # construct weighted avg_factor to match with the official DETR repo
        cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.bg_cls_weight

        if self.sync_cls_avg_factor:
            cls_avg_factor = reduce_mean(preds["scores"][0].new_tensor([cls_avg_factor]))
        cls_avg_factor = max(cls_avg_factor, 1)

        if track_info is not None:
            cat_padding_mask = torch.cat([t["query_padding_mask"] for t in track_info], dim=0)
            padding_loss_mask = ~cat_padding_mask

        # Classification loss
        # since the inputs needs the second dim is the class dim, we permute the prediction.
        pred_scores = torch.cat(preds["scores"], dim=0)  # (bs*num_q, cls_out_channles)
        cls_scores = pred_scores.reshape(-1, self.cls_out_channels)  # (bs*num_q, cls_out_channels)
        cls_labels = torch.cat(new_gts["labels"], dim=0).reshape(-1)  # (bs*num_q, )
        cls_weights = torch.cat(new_gts["label_weights"], dim=0).reshape(-1)  # (bs*num_q, )
        if track_info is not None:
            cls_weights = cls_weights * padding_loss_mask.float()

        if cls_scores.shape[0] != 0:
            loss_cls = self.loss_cls(cls_scores, cls_labels, cls_weights, avg_factor=cls_avg_factor)

            # Compute the average number of gt boxes across all gpus, for
            # normalization purposes
            num_total_pos = loss_cls.new_tensor([num_total_pos])
            num_total_pos = torch.clamp(reduce_mean(num_total_pos), min=1).item()
            loss_reg_lines = self.loss_reg_onetype(
                preds["lines"], new_gts["lines"], new_gts["lines_weights"], num_total_pos, self.loss_line_reg
            )
            loss_reg_stops = self.loss_reg_onetype(
                preds["stops"], new_gts["stops"], new_gts["stops_weights"], num_total_pos, self.loss_stop_reg
            )
            loss_reg_boxes = self.loss_reg_onetype(
                preds["boxes"], new_gts["boxes"], new_gts["boxes_weights"], num_total_pos, self.loss_box_reg
            )
            loss_reg_entrances = self.loss_reg_onetype(
                preds["entrances"],
                new_gts["entrances"],
                new_gts["entrances_weights"],
                num_total_pos,
                self.loss_entrance_reg,
            )
            # compute attr loss
            line_attr, arrow_attr, direct_attr = self.loss_attr(preds, new_gts, cls_avg_factor)
        else:
            loss_cls = torch.tensor(0)
            loss_reg_lines = torch.tensor(0)
            loss_reg_stops = torch.tensor(0)
            loss_reg_boxes = torch.tensor(0)
            loss_reg_entrances = torch.tensor(0)
            line_attr = torch.tensor(0)
            arrow_attr = torch.tensor(0)
            direct_attr = torch.tensor(0)

        loss_dict = dict(
            cls=loss_cls,
            reg_lines=loss_reg_lines,
            reg_stops=loss_reg_stops,
            reg_boxes=loss_reg_boxes,
            reg_entrances=loss_reg_entrances,
            line_attr=line_attr,
            arrow_attr=arrow_attr,
            direct_attr=direct_attr,
        )

        new_gts_info = {
            "labels": new_gts["labels"],
            "lines": new_gts["lines"],
        }

        return loss_dict, pos_inds_list, pos_gt_inds_list, matched_reg_cost, new_gts_info

    @force_fp32(apply_to=("gt_lines_list", "preds_dicts"))
    def loss(
        self,
        gts,
        preds,
        gt_bboxes_ignore=None,
        track_info=None,
        reduction="mean",
        scene_weight=None,
    ):
        """
        Loss Function.
        Args:
            gts (list[dict]): list length: num_layers
                dict {
                    'label': list[tensor(num_gts, )], list length: batchsize,
                    'line': list[tensor(num_gts, 2*num_points)], list length: batchsize,
                    ...
                }
            preds (list[dict]): list length: num_layers
                dict {
                    'lines': tensor(bs, num_queries, 2*num_points),
                    'scores': tensor(bs, num_queries, class_out_channels),
                }

            gt_bboxes_ignore (list[Tensor], optional): Bounding boxes
                which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """
        assert gt_bboxes_ignore is None, (
            f"{self.__class__.__name__} only supports " f"for gt_bboxes_ignore setting to None."
        )

        track_info = [track_info for _ in range(len(gts))]
        # Since there might have multi layer
        losses, pos_inds_lists, pos_gt_inds_lists, matched_reg_costs, gt_info_list = multi_apply(
            self.loss_single, preds, gts, track_info, scene_weight=scene_weight, reduction=reduction
        )

        # Format the losses
        loss_dict = dict()
        # loss from the last decoder layer
        for k, v in losses[-1].items():
            loss_dict[k] = v

        # Loss from other decoder layers
        num_dec_layer = 0
        for loss in losses[:-1]:
            for k, v in loss.items():
                loss_dict[f"d{num_dec_layer}.{k}"] = v
            num_dec_layer += 1

        return loss_dict, pos_inds_lists, pos_gt_inds_lists, gt_info_list, matched_reg_costs

    def post_process(self, preds_dict, tokens, track_dict=None, thr=0.0):
        lines = preds_dict["lines"]  # List[Tensor(num_queries, 2*num_points)]
        bs = len(lines)
        scores = preds_dict["scores"]  # (bs, num_queries, 3)
        lane_color_scores = preds_dict["lane_color_scores"]
        lane_dash_scores = preds_dict["lane_dash_scores"]
        curb_scores = preds_dict["curb_scores"]
        arrow_scores = preds_dict["arrow_scores"]
        results = []
        for i in range(bs):
            tmp_vectors = lines[i]
            tmp_lane_color_scores = lane_color_scores[i]
            tmp_lane_dash_scores = lane_dash_scores[i]
            tmp_curb_scores = curb_scores[i]
            tmp_arrow_scores = arrow_scores[i]
            # set up the prop_flags
            tmp_prop_flags = torch.zeros(tmp_vectors.shape[0]).bool()
            tmp_prop_flags[-100:] = 0
            tmp_prop_flags[:-100] = 1
            num_preds, num_points2 = tmp_vectors.shape
            tmp_vectors = tmp_vectors.view(num_preds, num_points2 // self.coord_dim, self.coord_dim)
            tmp_lane_color_scores = tmp_lane_color_scores.view(
                num_preds, num_points2 // self.coord_dim, self.num_lane_color_attr
            )  # [n_query, n_pts, n_attr]
            tmp_lane_dash_scores = tmp_lane_dash_scores.view(
                num_preds, num_points2 // self.coord_dim, self.num_lane_dash_attr
            )  # [n_query, n_pts, n_attr]
            tmp_curb_scores = tmp_curb_scores.view(
                num_preds, num_points2 // self.coord_dim, self.num_curb_attr
            )  # [n_query, n_pts, n_attr]
            tmp_arrow_scores = tmp_arrow_scores.view(
                num_preds, num_points2 // self.coord_dim, self.num_arrow_attr
            )  # [n_query, n_pts, n_attr]
            if self.loss_cls.use_sigmoid:
                tmp_scores, tmp_labels = scores[i].max(-1)
                tmp_scores = tmp_scores.sigmoid()
                pos = tmp_scores > thr
            else:
                assert self.num_classes + 1 == self.cls_out_channels
                tmp_scores, tmp_labels = scores[i].max(-1)
                bg_cls = self.cls_out_channels
                pos = tmp_labels != bg_cls
            tmp_lane_color_scores = tmp_lane_color_scores.argmax(-1)
            tmp_lane_dash_scores = tmp_lane_dash_scores.argmax(-1)
            tmp_curb_scores = tmp_curb_scores.argmax(-1)
            tmp_arrow_scores = tmp_arrow_scores.argmax(-1)

            tmp_vectors = tmp_vectors[pos]
            tmp_scores = tmp_scores[pos]
            tmp_labels = tmp_labels[pos]
            tmp_prop_flags = tmp_prop_flags[pos.to(tmp_prop_flags.device)]
            tmp_lane_color_scores = tmp_lane_color_scores[pos]
            tmp_lane_dash_scores = tmp_lane_dash_scores[pos]
            tmp_curb_scores = tmp_curb_scores[pos]
            tmp_arrow_scores = tmp_arrow_scores[pos]
            attrs_all = torch.zeros(
                tmp_vectors.shape[0], tmp_vectors.shape[1], self.attr_nums, dtype=torch.int32
            )  # 虚实/ 颜色 / 形状 / 是否栏杆 / 箭头属性
            attrs_all[:, :, 0] = tmp_lane_dash_scores + 1  # 根据属性接口定义，所有属性0表示unknown
            attrs_all[:, :, 1] = tmp_lane_color_scores + 1
            attrs_all[:, :, 3] = tmp_curb_scores + 1
            attrs_all[:, :, 4] = tmp_arrow_scores + 1

            if len(tmp_scores) == 0:
                single_result = {
                    "vectors": [],
                    "scores": [],
                    "labels": [],
                    "props": [],
                    "token": tokens[i],
                    "attrs_all": [],
                }
            else:
                single_result = {
                    "vectors": tmp_vectors.detach().cpu().numpy(),
                    "scores": tmp_scores.detach().cpu().numpy(),
                    "labels": tmp_labels.detach().cpu().numpy(),
                    "props": tmp_prop_flags.detach().cpu().numpy(),
                    "token": tokens[i],
                    "attrs_all": attrs_all.detach().cpu().numpy(),
                }

            # also save the tracking information for analyzing
            if track_dict is not None and len(track_dict["lines"]) > 0:
                tmp_track_scores = track_dict["scores"][i]
                tmp_track_vectors = track_dict["lines"][i]
                tmp_track_scores, tmp_track_labels = tmp_track_scores.max(-1)
                tmp_track_scores = tmp_track_scores.sigmoid()
                single_result["track_scores"] = tmp_track_scores.detach().cpu().numpy()
                single_result["track_vectors"] = tmp_track_vectors.detach().cpu().numpy()
                single_result["track_labels"] = tmp_track_labels.detach().cpu().numpy()
            else:
                single_result["track_scores"] = []
                single_result["track_vectors"] = []
                single_result["track_labels"] = []

            results.append(single_result)

        return results

    def prepare_temporal_propagation(
        self, preds_dict, scene_name, local_idx, memory_bank=None, thr_track=0.1, thr_det=0.5
    ):
        lines = preds_dict["lines"]  # List[Tensor(num_queries, 2*num_points)]
        queries = preds_dict["hs_embeds"]
        bs = len(lines)
        assert bs == 1, "now only support bs=1 for temporal-evolving inference"
        scores = preds_dict["scores"]  # (bs, num_queries, 3)

        first_frame = local_idx == 0

        tmp_vectors = lines[0]
        tmp_queries = queries[0]

        # focal loss
        if self.loss_cls.use_sigmoid:
            tmp_scores, tmp_labels = scores[0].max(-1)
            tmp_scores = tmp_scores.sigmoid()
            pos_track = tmp_scores[:-100] > thr_track
            pos_det = tmp_scores[-100:] > thr_det
            pos = torch.cat([pos_track, pos_det], dim=0)
        else:
            raise RuntimeError("The experiment uses sigmoid for cls outputs")

        pos_vectors = tmp_vectors[pos]
        pos_labels = tmp_labels[pos]
        pos_queries = tmp_queries[pos]
        pos_scores = tmp_scores[pos]

        if first_frame:
            global_ids = torch.arange(len(pos_vectors))
            num_instance = len(pos_vectors)
        else:
            prop_ids = self.prop_info["global_ids"]
            prop_num_instance = self.prop_info["num_instance"]
            global_ids_track = prop_ids[pos_track.to(prop_ids[0].device)]
            num_newborn = int(pos_det.sum())
            global_ids_newborn = torch.arange(num_newborn) + prop_num_instance
            global_ids = torch.cat([global_ids_track, global_ids_newborn])
            num_instance = prop_num_instance + num_newborn

        self.prop_info = {
            "vectors": pos_vectors,
            "queries": pos_queries,
            "scores": pos_scores,
            "labels": pos_labels,
            "scene_name": scene_name,
            "local_idx": local_idx,
            "global_ids": global_ids,
            "num_instance": num_instance,
        }

        if memory_bank is not None:
            if first_frame:
                num_tracks = 0
            else:
                num_tracks = self.prop_active_tracks
            pos_out_inds = torch.where(pos)[0]
            prev_out = {
                "hs_embeds": queries,
                "scores": scores,
            }
            memory_bank.update_memory(0, first_frame, pos_out_inds, prev_out, num_tracks, local_idx, memory_bank.curr_t)
            self.prop_active_tracks = len(pos_out_inds)

        save_pos_results = {
            "vectors": pos_vectors.cpu().numpy(),
            "scores": pos_scores.cpu().numpy(),
            "labels": pos_labels.cpu().numpy(),
            "global_ids": global_ids.cpu().numpy(),
            "scene_name": scene_name,
            "local_idx": local_idx,
            "num_instance": num_instance,
        }

        return save_pos_results

    def get_track_info(self, scene_name, local_idx):
        prop_info = self.prop_info
        assert prop_info["scene_name"] == scene_name and (
            prop_info["local_idx"] + 1 == local_idx or prop_info["local_idx"] == local_idx
        )

        vectors = prop_info["vectors"]
        queries = prop_info["queries"]
        # device = queries.device

        target = {}
        target["track_query_hs_embeds"] = queries
        target["track_query_boxes"] = vectors
        track_info = [
            target,
        ]

        return track_info

    def clear_temporal_cache(self):
        self.prop_info = None

    def train(self, *args, **kwargs):
        super().train(*args, **kwargs)

    def eval(self):
        super().eval()

    def _rv_pe(self, img_feats, img_metas):
        B, N, C, H, W = img_feats.shape
        pad_h, pad_w = img_metas["pad_shape"]
        # generate grid
        coords_h = torch.arange(H, device=img_feats[0].device).float() * pad_h / H
        coords_w = torch.arange(W, device=img_feats[0].device).float() * pad_w / W
        coords_d = (
            1
            + torch.arange(self.depth_num, device=img_feats[0].device).float() * (self.pc_range[4] - 1) / self.depth_num
        )
        coords_h, coords_w, coords_d = torch.meshgrid([coords_h, coords_w, coords_d])
        coords = torch.stack([coords_w, coords_h, coords_d, coords_h.new_ones(coords_h.shape)], dim=-1)
        H, W, D, C = coords.shape
        # inverse ida
        batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        coords = (
            img_metas["ida_mats"]
            .view(batch_size, sweep * num_cams, 1, 1, 1, 4, 4)
            .inverse()
            .matmul(coords.unsqueeze(-1))
        )
        # cam_to_ego
        # coords shape [B, N, H, W, D, 4, 1]
        coords = torch.cat((coords[..., :2, :] * coords[..., 2:3, :], coords[..., 2:, :]), 5)
        coords = torch.inverse(img_metas["lidar2imgs"]).view(batch_size, num_cams, 1, 1, 1, 4, 4).matmul(coords)
        # inverse bda
        if "bda_mat" in img_metas:
            coords = (
                img_metas["bda_mat"].unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 1, 4, 4)
                @ coords
            ).squeeze(-1)
        else:
            coords = coords.squeeze(-1)
        coords_3d = coords.view(batch_size * num_cams, H, W, D, C)
        # normalization
        coords_3d = (coords_3d[..., :3] - coords_3d.new_tensor(self.pc_range[:3])) / (
            coords_3d.new_tensor(self.pc_range[3:]) - coords_3d.new_tensor(self.pc_range[:3])
        )
        # rv_pos_embeds: torch.Size([BN, H, W, C])
        rv_pos_embeds = self.rv_embedding(coords_3d.reshape(*coords_3d.shape[:-2], -1))
        return rv_pos_embeds

    def _rv_query_embed(self, ref_points, img_metas):

        pad_h, pad_w = img_metas["pad_shape"]
        ref_points = ref_points * (
            ref_points.new_tensor(self.pc_range[3:]) - ref_points.new_tensor(self.pc_range[:3])
        ) + ref_points.new_tensor(self.pc_range[:3])

        points = torch.cat([ref_points, ref_points.new_ones(*ref_points.shape[:-1], 1)], dim=-1)
        # map 3d ref points into 2d img
        sweep = 1
        batch_size, _, num_cams, _, _ = img_metas["lidar2imgs"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        if "bda_mat" in img_metas:
            points = img_metas["bda_mat"].inverse().view(batch_size, 1, 4, 4).matmul(points.unsqueeze(-1))
        else:
            points = points.unsqueeze(-1)
        points = points.unsqueeze(1)
        points = img_metas["lidar2imgs"].view(batch_size, num_cams, 1, 4, 4).matmul(points)
        proj_points_clone = points.clone()

        # select valid ref point on img
        z_mask = proj_points_clone[..., 2:3, :].detach() > 0
        proj_points_clone[..., :3, :] = points[..., :3, :] / (
            points[..., 2:3, :].detach() + z_mask * 1e-6 - (~z_mask) * 1e-6
        )
        proj_points_clone = img_metas["ida_mats"].view(batch_size, num_cams, 1, 4, 4).matmul(proj_points_clone)
        proj_points_clone = proj_points_clone.squeeze(-1)
        mask = (
            (proj_points_clone[..., 0] < pad_w)
            & (proj_points_clone[..., 0] >= 0)
            & (proj_points_clone[..., 1] < pad_h)
            & (proj_points_clone[..., 1] >= 0)
        )
        mask &= z_mask.view(*mask.shape)

        # map 2d ref points back to 3d with multi depth
        coords_d = (
            1 + torch.arange(self.depth_num, device=ref_points.device).float() * (self.pc_range[4] - 1) / self.depth_num
        )
        batch_size, sweep, num_cams, _, _ = img_metas["ida_mats"].shape
        assert sweep == 1, "only support sweep = 1 yet."
        projback_points = (
            img_metas["ida_mats"]
            .view(batch_size, num_cams, 1, 4, 4)
            .inverse()
            .matmul(proj_points_clone.unsqueeze(-1))
            .squeeze(-1)
        )
        projback_points = torch.einsum("bvnc, d -> bvndc", projback_points, coords_d)
        projback_points = torch.cat(
            [projback_points[..., :3], projback_points.new_ones(*projback_points.shape[:-1], 1)], dim=-1
        ).unsqueeze(-1)

        projback_points = (
            torch.inverse(img_metas["lidar2imgs"]).view(batch_size, num_cams, 1, 1, 4, 4).matmul(projback_points)
        )
        if "bda_mat" in img_metas:
            projback_points = (
                img_metas["bda_mat"].unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 4, 4)
                @ projback_points
            ).squeeze(-1)
        else:
            projback_points = projback_points.squeeze(-1)
        projback_points = (projback_points[..., :3] - projback_points.new_tensor(self.pc_range[:3])) / (
            projback_points.new_tensor(self.pc_range[3:]) - projback_points.new_tensor(self.pc_range[:3])
        )
        rv_embeds = self.rv_embedding(projback_points.reshape(*projback_points.shape[:-2], -1))
        rv_embeds = (rv_embeds * mask.unsqueeze(-1)).sum(dim=1)
        return rv_embeds
