import copy
import torch
import torch.nn as nn
import torch.nn.functional as F
from mmdet.models import HEADS, build_loss
from mmdet.models.dense_heads import DETRHead
from mmdet3d.core.bbox.coders import build_bbox_coder
from mmcv.runner import force_fp32
from mmcv.cnn import Linear, bias_init_with_prob
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet.core.bbox.transforms import bbox_xyxy_to_cxcywh, bbox_cxcywh_to_xyxy
from mmdet.core import multi_apply, reduce_mean
from mmcv.utils import TORCH_VERSION, digit_version
import numpy as np
from copy import deepcopy
from mmdet.core import build_assigner
from perceptron.layers.head.maptracker.assigner import <PERSON><PERSON><PERSON><PERSON>signer, MapQueriesCost
from perceptron.layers.losses.detr_loss import Lines<PERSON>1<PERSON>oss


def denormalize_3d_pts(pts, pc_range):
    new_pts = pts.clone()
    new_pts[..., 0:1] = pts[..., 0:1] * (pc_range[3] - pc_range[0]) + pc_range[0]
    new_pts[..., 1:2] = pts[..., 1:2] * (pc_range[4] - pc_range[1]) + pc_range[1]
    new_pts[..., 2:3] = pts[..., 2:3] * (pc_range[5] - pc_range[2]) + pc_range[2]
    return new_pts


def normalize_3d_pts(pts, pc_range):
    patch_h = pc_range[4] - pc_range[1]
    patch_w = pc_range[3] - pc_range[0]
    patch_z = pc_range[5] - pc_range[2]
    new_pts = pts.clone()
    new_pts[..., 0:1] = pts[..., 0:1] - pc_range[0]
    new_pts[..., 1:2] = pts[..., 1:2] - pc_range[1]
    new_pts[..., 2:3] = pts[..., 2:3] - pc_range[2]
    factor = pts.new_tensor([patch_w, patch_h, patch_z])
    normalized_pts = new_pts / factor
    return normalized_pts


def normalize_2d_bbox(bboxes, pc_range):

    patch_h = pc_range[4] - pc_range[1]
    patch_w = pc_range[3] - pc_range[0]
    cxcywh_bboxes = bbox_xyxy_to_cxcywh(bboxes)
    cxcywh_bboxes[..., 0:1] = cxcywh_bboxes[..., 0:1] - pc_range[0]
    cxcywh_bboxes[..., 1:2] = cxcywh_bboxes[..., 1:2] - pc_range[1]
    factor = bboxes.new_tensor([patch_w, patch_h, patch_w, patch_h])

    normalized_bboxes = cxcywh_bboxes / factor
    return normalized_bboxes


def normalize_2d_pts(pts, pc_range):
    patch_h = pc_range[4] - pc_range[1]
    patch_w = pc_range[3] - pc_range[0]
    new_pts = pts.clone()
    new_pts[..., 0:1] = pts[..., 0:1] - pc_range[0]
    new_pts[..., 1:2] = pts[..., 1:2] - pc_range[1]
    factor = pts.new_tensor([patch_w, patch_h])
    normalized_pts = new_pts / factor
    return normalized_pts


def denormalize_2d_bbox(bboxes, pc_range):

    bboxes = bbox_cxcywh_to_xyxy(bboxes)
    bboxes[..., 0::2] = bboxes[..., 0::2] * (pc_range[3] - pc_range[0]) + pc_range[0]
    bboxes[..., 1::2] = bboxes[..., 1::2] * (pc_range[4] - pc_range[1]) + pc_range[1]

    return bboxes


def denormalize_2d_pts(pts, pc_range):
    new_pts = pts.clone()
    new_pts[..., 0:1] = pts[..., 0:1] * (pc_range[3] - pc_range[0]) + pc_range[0]
    new_pts[..., 1:2] = pts[..., 1:2] * (pc_range[4] - pc_range[1]) + pc_range[1]
    return new_pts


@HEADS.register_module(force=True)
class MapDetectorMapTRv2Head(DETRHead):
    """Head of Detr3D.
    Args:
        with_box_refine (bool): Whether to refine the reference points
            in the decoder. Defaults to False.
        as_two_stage (bool) : Whether to generate the proposal from
            the outputs of encoder.
        transformer (obj:`ConfigDict`): ConfigDict is used for building
            the Encoder and Decoder.
        bev_h, bev_w (int): spatial shape of BEV queries.
    """

    def __init__(
        self,
        *args,
        with_box_refine=False,
        as_two_stage=False,
        transformer=None,
        bbox_coder=None,
        num_cls_fcs=2,
        code_weights=None,
        bev_h=30,
        bev_w=30,
        num_vec_one2one=50,
        num_vec_one2many=0,
        k_one2many=6,
        line_num_points=20,
        num_classes=3,
        attr_nums=3,
        num_lane_color_attr=3,
        num_lane_dash_attr=7,
        num_curb_attr=3,
        lambda_one2many=1,
        num_pts_per_vec=2,
        num_pts_per_gt_vec=2,
        query_embed_type="all_pts",
        transform_method="minmax",
        gt_shift_pts_pattern="v0",
        dir_interval=1,
        aux_seg=dict(
            use_aux_seg=False,
            bev_seg=False,
            pv_seg=False,
            seg_classes=1,
            feat_down_sample=32,
        ),
        z_cfg=dict(
            pred_z_flag=False,
            gt_z_flag=False,
        ),
        loss_pts=dict(type="ChamferDistance", loss_src_weight=1.0, loss_dst_weight=1.0),
        loss_seg=dict(type="SimpleLoss", pos_weight=2.13, loss_weight=1.0),
        loss_pv_seg=dict(type="SimpleLoss", pos_weight=2.13, loss_weight=1.0),
        loss_dir=dict(type="PtsDirCosLoss", loss_weight=2.0),
        loss_line_reg=dict(),
        loss_lane_color_attr=dict(),
        loss_lane_dash_attr=dict(),
        loss_curb_attr=dict(),
        assigner=dict(),
        **kwargs,
    ):

        self.bev_h = bev_h
        self.bev_w = bev_w
        self.fp16_enabled = False

        self.with_box_refine = with_box_refine
        self.as_two_stage = as_two_stage
        self.bev_encoder_type = transformer.encoder.type
        if self.as_two_stage:
            transformer["as_two_stage"] = self.as_two_stage
        if "code_size" in kwargs:
            self.code_size = 2 if not z_cfg["pred_z_flag"] else 3
        else:
            self.code_size = 2
        if code_weights is not None:
            self.code_weights = code_weights
        else:
            self.code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2]

        self.bbox_coder = build_bbox_coder(bbox_coder)
        self.pc_range = self.bbox_coder.pc_range
        self.real_w = self.pc_range[3] - self.pc_range[0]
        self.real_h = self.pc_range[4] - self.pc_range[1]
        self.num_cls_fcs = num_cls_fcs - 1

        self.query_embed_type = query_embed_type
        self.transform_method = transform_method
        self.gt_shift_pts_pattern = gt_shift_pts_pattern

        self.num_classes = num_classes
        num_vec = num_vec_one2one + num_vec_one2many
        num_query = num_vec * num_pts_per_vec
        self.num_query = num_query
        self.num_vec = num_vec
        self.num_pts_per_vec = num_pts_per_vec
        self.num_pts_per_gt_vec = num_pts_per_gt_vec
        self.dir_interval = dir_interval
        self.aux_seg = aux_seg
        self.z_cfg = z_cfg

        self.num_vec_one2one = num_vec_one2one
        self.num_vec_one2many = num_vec_one2many
        self.k_one2many = k_one2many
        self.lambda_one2many = lambda_one2many
        self.line_query_num_ins = num_vec_one2one

        self.num_lane_dash_attr = num_lane_dash_attr
        self.num_lane_color_attr = num_lane_color_attr
        self.num_curb_attr = num_curb_attr
        self.line_num_points = line_num_points
        self.attr_nums = attr_nums

        super(MapDetectorMapTRv2Head, self).__init__(*args, transformer=transformer, num_classes=num_classes, **kwargs)
        self.code_weights = nn.Parameter(torch.tensor(self.code_weights, requires_grad=False), requires_grad=False)

        self.num_decoder_layers = self.transformer.decoder.num_layers

        self.private_assigner = build_assigner(assigner)

        self.loss_pts = build_loss(loss_pts)
        self.loss_dir = build_loss(loss_dir)
        self.loss_seg = build_loss(loss_seg)
        self.loss_pv_seg = build_loss(loss_pv_seg)
        self.loss_line_reg = build_loss(loss_line_reg)
        self.loss_lane_color_attr = build_loss(loss_lane_color_attr)
        self.loss_lane_dash_attr = build_loss(loss_lane_dash_attr)
        self.loss_curb_attr = build_loss(loss_curb_attr)

        self._init_layers()

    def _init_layers(self):
        """Initialize classification branch and regression branch of head."""
        cls_branch = []
        for _ in range(self.num_reg_fcs):
            cls_branch.append(Linear(self.embed_dims, self.embed_dims))
            cls_branch.append(nn.LayerNorm(self.embed_dims))
            cls_branch.append(nn.ReLU(inplace=True))
        cls_branch.append(Linear(self.embed_dims, self.cls_out_channels))
        fc_cls = nn.Sequential(*cls_branch)

        # add attr
        lane_color_branch = []
        for _ in range(self.num_reg_fcs):
            lane_color_branch.append(Linear(self.embed_dims, self.embed_dims))
            lane_color_branch.append(nn.LayerNorm(self.embed_dims))
            lane_color_branch.append(nn.ReLU(inplace=True))
        lane_color_branch.append(Linear(self.embed_dims, self.line_num_points * self.num_lane_color_attr))
        lane_color_cls = nn.Sequential(*lane_color_branch)

        lane_dash_branch = []
        for _ in range(self.num_reg_fcs):
            lane_dash_branch.append(Linear(self.embed_dims, self.embed_dims))
            lane_dash_branch.append(nn.LayerNorm(self.embed_dims))
            lane_dash_branch.append(nn.ReLU(inplace=True))
        lane_dash_branch.append(Linear(self.embed_dims, self.line_num_points * self.num_lane_dash_attr))
        lane_dash_cls = nn.Sequential(*lane_dash_branch)

        curb_attr_branch = []
        for _ in range(self.num_reg_fcs):
            curb_attr_branch.append(Linear(self.embed_dims, self.embed_dims))
            curb_attr_branch.append(nn.LayerNorm(self.embed_dims))
            curb_attr_branch.append(nn.ReLU(inplace=True))
        curb_attr_branch.append(Linear(self.embed_dims, self.line_num_points * self.num_curb_attr))
        curb_attr_cls = nn.Sequential(*curb_attr_branch)

        reg_branch = []
        for _ in range(self.num_reg_fcs):
            reg_branch.append(Linear(self.embed_dims, self.embed_dims))
            reg_branch.append(nn.ReLU())
        reg_branch.append(Linear(self.embed_dims, self.code_size))
        reg_branch = nn.Sequential(*reg_branch)

        def _get_clones(module, N):
            return nn.ModuleList([copy.deepcopy(module) for i in range(N)])

        # last reg_branch is used to generate proposal from
        # encode feature map when as_two_stage is True.
        num_pred = (
            (self.transformer.decoder.num_layers + 1) if self.as_two_stage else self.transformer.decoder.num_layers
        )

        if self.with_box_refine:
            self.cls_branches = _get_clones(fc_cls, num_pred)
            self.reg_branches = _get_clones(reg_branch, num_pred)
            # attr
            self.lane_color_branches = _get_clones(lane_color_cls, num_pred)
            self.lane_dash_branches = _get_clones(lane_dash_cls, num_pred)
            self.curb_attr_branches = _get_clones(curb_attr_cls, num_pred)
        else:
            self.cls_branches = nn.ModuleList([fc_cls for _ in range(num_pred)])
            self.reg_branches = nn.ModuleList([reg_branch for _ in range(num_pred)])
            self.lane_color_branches = nn.ModuleList([lane_color_branch for _ in range(num_pred)])
            self.lane_dash_branches = nn.ModuleList([lane_dash_branch for _ in range(num_pred)])
            self.curb_attr_branches = nn.ModuleList([curb_attr_branch for _ in range(num_pred)])

        if self.aux_seg["use_aux_seg"]:
            if not (self.aux_seg["bev_seg"] or self.aux_seg["pv_seg"]):
                raise ValueError("aux_seg must have bev_seg or pv_seg")
            if self.aux_seg["bev_seg"]:
                self.seg_head = nn.Sequential(
                    nn.Conv2d(self.embed_dims, self.embed_dims, kernel_size=3, padding=1, bias=False),
                    # nn.BatchNorm2d(128),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(self.embed_dims, self.aux_seg["seg_classes"], kernel_size=1, padding=0),
                )
            if self.aux_seg["pv_seg"]:
                self.pv_seg_head = nn.Sequential(
                    nn.Conv2d(self.embed_dims, self.embed_dims, kernel_size=3, padding=1, bias=False),
                    # nn.BatchNorm2d(128),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(self.embed_dims, self.aux_seg["seg_classes"], kernel_size=1, padding=0),
                )

        if not self.as_two_stage:
            if "BEVFormerEncoderMap" in self.bev_encoder_type:
                self.bev_embedding = nn.Embedding(self.bev_h * self.bev_w, self.embed_dims)
            else:
                self.bev_embedding = None
            if self.query_embed_type == "all_pts":
                self.query_embedding = nn.Embedding(self.num_query, self.embed_dims * 2)
            elif self.query_embed_type == "instance_pts":
                self.query_embedding = None
                self.instance_embedding = nn.Embedding(self.num_vec, self.embed_dims * 2)
                self.pts_embedding = nn.Embedding(self.num_pts_per_vec, self.embed_dims * 2)

    def init_weights(self):
        """Initialize weights of the DeformDETR head."""
        self.transformer.init_weights()
        if self.loss_cls.use_sigmoid:
            bias_init = bias_init_with_prob(0.01)
            for m in self.cls_branches:
                nn.init.constant_(m[-1].bias, bias_init)

    # @auto_fp16(apply_to=('mlvl_feats'))
    @force_fp32(apply_to=("mlvl_feats", "prev_bev"))
    def forward(self, mlvl_feats, lidar_feat, img_metas, prev_bev=None, only_bev=False):
        """Forward function.
        Args:
            mlvl_feats (tuple[Tensor]): Features from the upstream
                network, each is a 5D-tensor with shape
                (B, N, C, H, W).
            prev_bev: previous bev featues
            only_bev: only compute BEV features with encoder.
        Returns:
            all_cls_scores (Tensor): Outputs from the classification head, \
                shape [nb_dec, bs, num_query, cls_out_channels]. Note \
                cls_out_channels should includes background.
            all_bbox_preds (Tensor): Sigmoid outputs from the regression \
                head with normalized coordinate format (cx, cy, w, l, cz, h, theta, vx, vy). \
                Shape [nb_dec, bs, num_query, 9].
        """
        if self.training:
            num_vec = self.num_vec
        else:
            # num_vec = self.num_vec_one2one
            num_vec = self.num_vec  # debug

        if mlvl_feats[0] is not None:
            bs, num_cam, _, _, _ = mlvl_feats[0].shape
            dtype = mlvl_feats[0].dtype
        else:
            bs, _, _, _ = lidar_feat.shape
            dtype = lidar_feat.dtype

        if self.query_embed_type == "all_pts":
            object_query_embeds = self.query_embedding.weight.to(dtype)
        elif self.query_embed_type == "instance_pts":
            pts_embeds = self.pts_embedding.weight.unsqueeze(0)
            instance_embeds = self.instance_embedding.weight[0:num_vec].unsqueeze(1)
            object_query_embeds = (pts_embeds + instance_embeds).flatten(0, 1).to(dtype)
        if self.bev_embedding is not None:
            bev_queries = self.bev_embedding.weight.to(dtype)

            bev_mask = torch.zeros((bs, self.bev_h, self.bev_w), device=bev_queries.device).to(dtype)
            bev_pos = self.positional_encoding(bev_mask).to(dtype)
        else:
            bev_queries = None
            bev_mask = None
            bev_pos = None

        # make attn mask
        """ attention mask to prevent information leakage
        """
        self_attn_mask = (
            torch.zeros(
                [
                    num_vec,
                    num_vec,
                ]
            )
            .bool()
            .to(mlvl_feats[0].device if mlvl_feats[0] is not None else lidar_feat.device)
        )
        self_attn_mask[
            self.num_vec_one2one :,
            0 : self.num_vec_one2one,
        ] = True
        self_attn_mask[
            0 : self.num_vec_one2one,
            self.num_vec_one2one :,
        ] = True

        if only_bev:  # only use encoder to obtain BEV features, TODO: refine the workaround
            return self.transformer.get_bev_features(
                mlvl_feats,
                lidar_feat,
                bev_queries,
                self.bev_h,
                self.bev_w,
                grid_length=(self.real_h / self.bev_h, self.real_w / self.bev_w),
                bev_pos=bev_pos,
                img_metas=img_metas,
                prev_bev=prev_bev,
            )["bev"]
        else:
            outputs = self.transformer(
                mlvl_feats,  # [(bs, n_cam, 256, 32, 60)]
                lidar_feat,  # None
                bev_queries,  # (6000=hxw, 256)
                object_query_embeds,  # (7000, 512)
                self.bev_h,  # 100
                self.bev_w,  # 60
                grid_length=(self.real_h / self.bev_h, self.real_w / self.bev_w),
                bev_pos=bev_pos,  # (bs, 256, h, w)
                reg_branches=self.reg_branches if self.with_box_refine else None,  # noqa:E501
                cls_branches=self.cls_branches if self.as_two_stage else None,
                img_metas=img_metas,
                prev_bev=prev_bev,  # None
                self_attn_mask=self_attn_mask,  # (350, 350) - numvec, numvec
                num_vec=num_vec,  # 350
                num_pts_per_vec=self.num_pts_per_vec,  # 20
            )

        bev_embed, depth, hs, init_reference, inter_references = outputs
        hs = hs.permute(0, 2, 1, 3)
        outputs_classes_one2one = []
        outputs_coords_one2one = []
        outputs_pts_coords_one2one = []
        outputs_lane_color_one2one = []
        outputs_lane_dash_one2one = []
        outputs_curb_one2one = []

        outputs_classes_one2many = []
        outputs_coords_one2many = []
        outputs_pts_coords_one2many = []
        outputs_lane_color_one2many = []
        outputs_lane_dash_one2many = []
        outputs_curb_one2many = []
        for lvl in range(hs.shape[0]):
            if lvl == 0:
                reference = init_reference[..., 0:2] if not self.z_cfg["gt_z_flag"] else init_reference[..., 0:3]
            else:
                reference = (
                    inter_references[lvl - 1][..., 0:2]
                    if not self.z_cfg["gt_z_flag"]
                    else inter_references[lvl - 1][..., 0:3]
                )
            reference = inverse_sigmoid(reference)
            outputs_class = self.cls_branches[lvl](hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2))
            outputs_lane_color = self.lane_color_branches[lvl](
                hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2)
            )  # (bs, num_vector, num_points * num_attr_color)
            outputs_lane_dash = self.lane_dash_branches[lvl](
                hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2)
            )  # (bs, num_vector, num_points * num_attr_dash)
            outputs_curb_attr = self.curb_attr_branches[lvl](
                hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2)
            )  # (bs, num_vector, num_points * num_attr_curb)

            tmp = self.reg_branches[lvl](hs[lvl])
            tmp = tmp[..., 0:2] if not self.z_cfg["gt_z_flag"] else tmp[..., 0:3]
            tmp += reference
            tmp = tmp.sigmoid()  # cx,cy,w,h
            outputs_coord, outputs_pts_coord = self.transform_box(tmp, num_vec=num_vec)

            outputs_classes_one2one.append(outputs_class[:, 0 : self.num_vec_one2one])
            outputs_coords_one2one.append(outputs_coord[:, 0 : self.num_vec_one2one])
            outputs_pts_coords_one2one.append(outputs_pts_coord[:, 0 : self.num_vec_one2one])
            outputs_lane_color_one2one.append(outputs_lane_color[:, 0 : self.num_vec_one2one])
            outputs_lane_dash_one2one.append(outputs_lane_dash[:, 0 : self.num_vec_one2one])
            outputs_curb_one2one.append(outputs_curb_attr[:, 0 : self.num_vec_one2one])

            outputs_classes_one2many.append(outputs_class[:, self.num_vec_one2one :])
            outputs_coords_one2many.append(outputs_coord[:, self.num_vec_one2one :])
            outputs_pts_coords_one2many.append(outputs_pts_coord[:, self.num_vec_one2one :])
            outputs_lane_color_one2many.append(outputs_lane_color[:, self.num_vec_one2one :])
            outputs_lane_dash_one2many.append(outputs_lane_dash[:, self.num_vec_one2one :])
            outputs_curb_one2many.append(outputs_curb_attr[:, self.num_vec_one2one :])

        outputs_classes_one2one = torch.stack(outputs_classes_one2one)
        outputs_coords_one2one = torch.stack(outputs_coords_one2one)
        outputs_pts_coords_one2one = torch.stack(outputs_pts_coords_one2one)
        outputs_lane_color_one2one = torch.stack(outputs_lane_color_one2one)
        outputs_lane_dash_one2one = torch.stack(outputs_lane_dash_one2one)
        outputs_curb_one2one = torch.stack(outputs_curb_one2one)

        outputs_classes_one2many = torch.stack(outputs_classes_one2many)
        outputs_coords_one2many = torch.stack(outputs_coords_one2many)
        outputs_pts_coords_one2many = torch.stack(outputs_pts_coords_one2many)
        outputs_lane_color_one2many = torch.stack(outputs_lane_color_one2many)
        outputs_lane_dash_one2many = torch.stack(outputs_lane_dash_one2many)
        outputs_curb_one2many = torch.stack(outputs_curb_one2many)

        outputs_seg = None
        outputs_pv_seg = None
        if self.aux_seg["use_aux_seg"]:
            seg_bev_embed = (
                bev_embed.permute(1, 0, 2).view(bs, self.bev_h, self.bev_w, -1).permute(0, 3, 1, 2).contiguous()
            )
            if self.aux_seg["bev_seg"]:
                outputs_seg = self.seg_head(seg_bev_embed)
            if mlvl_feats[0] is not None:
                bs, num_cam, embed_dims, feat_h, feat_w = mlvl_feats[-1].shape
                if self.aux_seg["pv_seg"]:
                    outputs_pv_seg = self.pv_seg_head(mlvl_feats[-1].flatten(0, 1))
                    outputs_pv_seg = outputs_pv_seg.view(bs, num_cam, -1, feat_h, feat_w)

        outs = {
            "bev_embed": bev_embed,
            "all_cls_scores": outputs_classes_one2one,
            "all_bbox_preds": outputs_coords_one2one,
            "all_pts_preds": outputs_pts_coords_one2one,
            "all_lane_colors": outputs_lane_color_one2one,
            "all_lane_dashs": outputs_lane_dash_one2one,
            "all_lane_curbs": outputs_curb_one2one,
            "enc_cls_scores": None,
            "enc_bbox_preds": None,
            "enc_pts_preds": None,
            "depth": depth,
            "seg": outputs_seg,
            "pv_seg": outputs_pv_seg,
            "one2many_outs": dict(
                all_cls_scores=outputs_classes_one2many,
                all_bbox_preds=outputs_coords_one2many,
                all_pts_preds=outputs_pts_coords_one2many,
                all_lane_colors=outputs_lane_color_one2many,
                all_lane_dashs=outputs_lane_dash_one2many,
                all_lane_curbs=outputs_curb_one2many,
                enc_cls_scores=None,
                enc_bbox_preds=None,
                enc_pts_preds=None,
                seg=None,
                pv_seg=None,
            ),
        }

        return outs

    @force_fp32(apply_to=("mlvl_feats", "prev_bev"))
    def forward_deploy(self, mlvl_feats, lidar_feat=None, img_metas=None, prev_bev=None, only_bev=False, **kwargs):
        """Forward function.
        Args:
            mlvl_feats (tuple[Tensor]): Features from the upstream
                network, each is a 5D-tensor with shape
                (B, N, C, H, W).
            prev_bev: previous bev featues
            only_bev: only compute BEV features with encoder.
        Returns:
            all_cls_scores (Tensor): Outputs from the classification head, \
                shape [nb_dec, bs, num_query, cls_out_channels]. Note \
                cls_out_channels should includes background.
            all_bbox_preds (Tensor): Sigmoid outputs from the regression \
                head with normalized coordinate format (cx, cy, w, l, cz, h, theta, vx, vy). \
                Shape [nb_dec, bs, num_query, 9].
        """
        num_vec = self.num_vec_one2one

        bs, num_cam, _, _, _ = mlvl_feats[0].shape
        dtype = mlvl_feats[0].dtype
        if self.query_embed_type == "all_pts":
            object_query_embeds = self.query_embedding.weight.to(dtype)
        elif self.query_embed_type == "instance_pts":
            pts_embeds = self.pts_embedding.weight.unsqueeze(0)
            instance_embeds = self.instance_embedding.weight[0:num_vec].unsqueeze(1)
            object_query_embeds = (pts_embeds + instance_embeds).flatten(0, 1).to(dtype)
        if self.bev_embedding is not None:
            bev_queries = self.bev_embedding.weight.to(dtype)

            bev_mask = torch.zeros((bs, self.bev_h, self.bev_w), device=bev_queries.device).to(dtype)
            bev_pos = self.positional_encoding(bev_mask).to(dtype)
        else:
            bev_queries = None
            bev_mask = None
            bev_pos = None

        # make attn mask
        """ attention mask to prevent information leakage
        """

        self_attn_mask = (
            torch.zeros(
                [
                    num_vec,
                    num_vec,
                ]
            )
            .bool()
            .to(mlvl_feats[0].device)
        )

        if only_bev:  # only use encoder to obtain BEV features, TODO: refine the workaround
            return self.transformer.get_bev_features(
                mlvl_feats,
                lidar_feat,
                bev_queries,
                self.bev_h,
                self.bev_w,
                grid_length=(self.real_h / self.bev_h, self.real_w / self.bev_w),
                bev_pos=bev_pos,
                img_metas=img_metas,
                prev_bev=prev_bev,
            )["bev"]
        else:
            outputs = self.transformer(
                mlvl_feats,  # [(bs, n_cam, 256, 32, 60)]
                lidar_feat,  # None
                bev_queries,  # (6000=hxw, 256)
                object_query_embeds,  # (7000, 512)
                self.bev_h,  # 100
                self.bev_w,  # 60
                grid_length=(self.real_h / self.bev_h, self.real_w / self.bev_w),
                bev_pos=bev_pos,  # (bs, 256, h, w)
                reg_branches=self.reg_branches if self.with_box_refine else None,  # noqa:E501
                cls_branches=self.cls_branches if self.as_two_stage else None,
                img_metas=img_metas,
                prev_bev=prev_bev,  # None
                self_attn_mask=self_attn_mask,  # (350, 350) - numvec, numvec
                num_vec=num_vec,  # 350
                num_pts_per_vec=self.num_pts_per_vec,  # 20
                **kwargs,
            )

        bev_embed, depth, hs, init_reference, inter_references = outputs
        hs = hs.permute(0, 2, 1, 3)
        outputs_classes_one2one = []
        outputs_coords_one2one = []
        outputs_pts_coords_one2one = []
        outputs_lane_color_one2one = []  # add color
        outputs_lane_dash_one2one = []  # add dash
        outputs_lane_curb_one2one = []  # add curb

        lvl = hs.shape[0] - 1  # last index
        reference = (
            inter_references[lvl - 1][..., 0:2] if not self.z_cfg["gt_z_flag"] else inter_references[lvl - 1][..., 0:3]
        )
        reference = inverse_sigmoid(reference)
        # vec_embedding = hs[lvl].reshape(bs, self.num_vec, -1)
        outputs_class = self.cls_branches[lvl](hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2))
        outputs_lane_color = self.lane_color_branches[lvl](
            hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2)
        )  # (bs, num_vector, num_points * num_attr_color)
        outputs_lane_dash = self.lane_dash_branches[lvl](
            hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2)
        )  # (bs, num_vector, num_points * num_attr_dash)
        outputs_curb_attr = self.curb_attr_branches[lvl](
            hs[lvl].view(bs, num_vec, self.num_pts_per_vec, -1).mean(2)
        )  # (bs, num_vector, num_points * num_attr_curb)

        tmp = self.reg_branches[lvl](hs[lvl])
        tmp = tmp[..., 0:2] if not self.z_cfg["gt_z_flag"] else tmp[..., 0:3]
        tmp += reference
        tmp = tmp.sigmoid()  # cx,cy,w,h
        outputs_coord, outputs_pts_coord = self.transform_box(tmp, num_vec=num_vec)

        outputs_classes_one2one = outputs_class[:, 0 : self.num_vec_one2one]
        outputs_coords_one2one = outputs_coord[:, 0 : self.num_vec_one2one]
        outputs_pts_coords_one2one = outputs_pts_coord[:, 0 : self.num_vec_one2one]
        outputs_lane_color_one2one = outputs_lane_color[:, 0 : self.num_vec_one2one]  # add color
        outputs_lane_dash_one2one = outputs_lane_dash[:, 0 : self.num_vec_one2one]  # add dash
        outputs_lane_curb_one2one = outputs_curb_attr[:, 0 : self.num_vec_one2one]  # add curb

        outs = {
            "all_cls_scores": outputs_classes_one2one,
            "all_bbox_preds": outputs_coords_one2one,
            "all_pts_preds": outputs_pts_coords_one2one,
            "all_lane_colors": outputs_lane_color_one2one,  # add color
            "all_lane_dashs": outputs_lane_dash_one2one,  # add dash
            "all_lane_curbs": outputs_lane_curb_one2one,  # add curb
        }

        return outs

    def transform_box(self, pts, num_vec=50, y_first=False):
        """
        Converting the points set into bounding box.

        Args:
            pts: the input points sets (fields), each points
                set (fields) is represented as 2n scalar.
            y_first: if y_fisrt=True, the point set is represented as
                [y1, x1, y2, x2 ... yn, xn], otherwise the point set is
                represented as [x1, y1, x2, y2 ... xn, yn].
        Returns:
            The bbox [cx, cy, w, h] transformed from points.
        """
        if self.z_cfg["gt_z_flag"]:
            pts_reshape = pts.view(pts.shape[0], num_vec, self.num_pts_per_vec, 3)
        else:
            pts_reshape = pts.view(pts.shape[0], num_vec, self.num_pts_per_vec, 2)
        pts_y = pts_reshape[:, :, :, 0] if y_first else pts_reshape[:, :, :, 1]
        pts_x = pts_reshape[:, :, :, 1] if y_first else pts_reshape[:, :, :, 0]
        if self.transform_method == "minmax":
            xmin = pts_x.min(dim=2, keepdim=True)[0]
            xmax = pts_x.max(dim=2, keepdim=True)[0]
            ymin = pts_y.min(dim=2, keepdim=True)[0]
            ymax = pts_y.max(dim=2, keepdim=True)[0]
            bbox = torch.cat([xmin, ymin, xmax, ymax], dim=2)
            bbox = bbox_xyxy_to_cxcywh(bbox)
        else:
            raise NotImplementedError
        return bbox, pts_reshape

    def _get_target_single(
        self, cls_score, bbox_pred, pts_pred, gt_labels, gt_bboxes, gt_shifts_pts, gt_bboxes_ignore=None
    ):
        """ "Compute regression and classification targets for one image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            cls_score (Tensor): Box score logits from a single decoder layer
                for one image. Shape [num_query, cls_out_channels].
            bbox_pred (Tensor): Sigmoid outputs from a single decoder layer
                for one image, with normalized coordinate (cx, cy, w, h) and
                shape [num_query, 4].
            gt_bboxes (Tensor): Ground truth bboxes for one image with
                shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels (Tensor): Ground truth class indices for one image
                with shape (num_gts, ).
            gt_bboxes_ignore (Tensor, optional): Bounding boxes
                which can be ignored. Default None.
        Returns:
            tuple[Tensor]: a tuple containing the following for one image.
                - labels (Tensor): Labels of each image.
                - label_weights (Tensor]): Label weights of each image.
                - bbox_targets (Tensor): BBox targets of each image.
                - bbox_weights (Tensor): BBox weights of each image.
                - pos_inds (Tensor): Sampled positive indices for each image.
                - neg_inds (Tensor): Sampled negative indices for each image.
        """
        num_bboxes = bbox_pred.size(0)
        # assigner and sampler
        gt_c = gt_bboxes.shape[-1]
        assign_result, order_index = self.assigner.assign(
            bbox_pred, cls_score, pts_pred, gt_bboxes, gt_labels, gt_shifts_pts, gt_bboxes_ignore
        )

        sampling_result = self.sampler.sample(assign_result, bbox_pred, gt_bboxes)
        # pts_sampling_result = self.sampler.sample(assign_result, pts_pred,
        #                                       gt_pts)

        pos_inds = sampling_result.pos_inds
        neg_inds = sampling_result.neg_inds

        # label targets
        labels = gt_bboxes.new_full((num_bboxes,), self.num_classes, dtype=torch.long)
        labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds]
        label_weights = gt_bboxes.new_ones(num_bboxes)

        # bbox targets
        bbox_targets = torch.zeros_like(bbox_pred)[..., :gt_c]
        bbox_weights = torch.zeros_like(bbox_pred)
        bbox_weights[pos_inds] = 1.0

        # pts targets
        # num_query, num_order, num_points, num_coords
        if order_index is None:
            assigned_shift = gt_labels[sampling_result.pos_assigned_gt_inds]
        else:
            assigned_shift = order_index[sampling_result.pos_inds, sampling_result.pos_assigned_gt_inds]
        pts_targets = pts_pred.new_zeros((pts_pred.size(0), pts_pred.size(1), pts_pred.size(2)))
        pts_weights = torch.zeros_like(pts_targets)
        pts_weights[pos_inds] = 1.0

        # DETR
        bbox_targets[pos_inds] = sampling_result.pos_gt_bboxes
        pts_targets[pos_inds] = gt_shifts_pts[sampling_result.pos_assigned_gt_inds, assigned_shift, :, :]
        return (labels, label_weights, bbox_targets, bbox_weights, pts_targets, pts_weights, pos_inds, neg_inds)

    def get_targets(
        self,
        cls_scores_list,
        bbox_preds_list,
        pts_preds_list,
        gt_bboxes_list,
        gt_labels_list,
        gt_shifts_pts_list,
        gt_bboxes_ignore_list=None,
    ):
        """"Compute regression and classification targets for a batch image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            cls_scores_list (list[Tensor]): Box score logits from a single
                decoder layer for each image with shape [num_query,
                cls_out_channels].
            bbox_preds_list (list[Tensor]): Sigmoid outputs from a single
                decoder layer for each image, with normalized coordinate
                (cx, cy, w, h) and shape [num_query, 4].
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indices for each
                image with shape (num_gts, ).
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            tuple: a tuple containing the following targets.
                - labels_list (list[Tensor]): Labels for all images.
                - label_weights_list (list[Tensor]): Label weights for all \
                    images.
                - bbox_targets_list (list[Tensor]): BBox targets for all \
                    images.
                - bbox_weights_list (list[Tensor]): BBox weights for all \
                    images.
                - num_total_pos (int): Number of positive samples in all \
                    images.
                - num_total_neg (int): Number of negative samples in all \
                    images.
        """
        assert gt_bboxes_ignore_list is None, "Only supports for gt_bboxes_ignore setting to None."
        num_imgs = len(cls_scores_list)
        gt_bboxes_ignore_list = [gt_bboxes_ignore_list for _ in range(num_imgs)]

        (
            labels_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            pts_targets_list,
            pts_weights_list,
            pos_inds_list,
            neg_inds_list,
        ) = multi_apply(
            self._get_target_single,
            cls_scores_list,
            bbox_preds_list,
            pts_preds_list,
            gt_labels_list,
            gt_bboxes_list,
            gt_shifts_pts_list,
            gt_bboxes_ignore_list,
        )
        num_total_pos = sum((inds.numel() for inds in pos_inds_list))
        num_total_neg = sum((inds.numel() for inds in neg_inds_list))
        return (
            labels_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            pts_targets_list,
            pts_weights_list,
            num_total_pos,
            num_total_neg,
        )

    def loss_single(
        self,
        cls_scores,
        bbox_preds,
        pts_preds,
        gt_bboxes_list,
        gt_labels_list,
        gt_shifts_pts_list,
        gt_bboxes_ignore_list=None,
    ):
        """ "Loss function for outputs from a single decoder layer of a single
        feature level.
        Args:
            cls_scores (Tensor): Box score logits from a single decoder layer
                for all images. Shape [bs, num_query, cls_out_channels].
            bbox_preds (Tensor): Sigmoid outputs from a single decoder layer
                for all images, with normalized coordinate (cx, cy, w, h) and
                shape [bs, num_query, 4].
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indices for each
                image with shape (num_gts, ).
            gt_pts_list (list[Tensor]): Ground truth pts for each image
                with shape (num_gts, fixed_num, 2) in [x,y] format.
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """
        num_imgs = cls_scores.size(0)
        cls_scores_list = [cls_scores[i] for i in range(num_imgs)]
        bbox_preds_list = [bbox_preds[i] for i in range(num_imgs)]
        pts_preds_list = [pts_preds[i] for i in range(num_imgs)]

        cls_reg_targets = self.get_targets(
            cls_scores_list,
            bbox_preds_list,
            pts_preds_list,
            gt_bboxes_list,
            gt_labels_list,
            gt_shifts_pts_list,
            gt_bboxes_ignore_list,
        )
        (
            labels_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            pts_targets_list,
            pts_weights_list,
            num_total_pos,
            num_total_neg,
        ) = cls_reg_targets

        labels = torch.cat(labels_list, 0)
        label_weights = torch.cat(label_weights_list, 0)
        bbox_targets = torch.cat(bbox_targets_list, 0)
        bbox_weights = torch.cat(bbox_weights_list, 0)
        pts_targets = torch.cat(pts_targets_list, 0)
        pts_weights = torch.cat(pts_weights_list, 0)

        # classification loss
        cls_scores = cls_scores.reshape(-1, self.cls_out_channels)
        # construct weighted avg_factor to match with the official DETR repo
        cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.bg_cls_weight
        if self.sync_cls_avg_factor:
            cls_avg_factor = reduce_mean(cls_scores.new_tensor([cls_avg_factor]))

        cls_avg_factor = max(cls_avg_factor, 1)
        loss_cls = self.loss_cls(cls_scores, labels, label_weights, avg_factor=cls_avg_factor)

        # Compute the average number of gt boxes accross all gpus, for
        # normalization purposes
        num_total_pos = loss_cls.new_tensor([num_total_pos])
        num_total_pos = torch.clamp(reduce_mean(num_total_pos), min=1).item()

        # regression L1 loss
        bbox_preds = bbox_preds.reshape(-1, bbox_preds.size(-1))
        normalized_bbox_targets = normalize_2d_bbox(bbox_targets, self.pc_range)
        # normalized_bbox_targets = bbox_targets
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        bbox_weights = bbox_weights * self.code_weights

        loss_bbox = self.loss_bbox(
            bbox_preds[isnotnan, :4],
            normalized_bbox_targets[isnotnan, :4],
            bbox_weights[isnotnan, :4],
            avg_factor=num_total_pos,
        )

        # regression pts CD loss
        # pts_preds = pts_preds

        # num_samples, num_order, num_pts, num_coords
        normalized_pts_targets = (
            normalize_2d_pts(pts_targets, self.pc_range)
            if not self.z_cfg["gt_z_flag"]
            else normalize_3d_pts(pts_targets, self.pc_range)
        )

        # num_samples, num_pts, num_coords
        pts_preds = pts_preds.reshape(-1, pts_preds.size(-2), pts_preds.size(-1))
        if self.num_pts_per_vec != self.num_pts_per_gt_vec:
            pts_preds = pts_preds.permute(0, 2, 1)
            pts_preds = F.interpolate(pts_preds, size=(self.num_pts_per_gt_vec), mode="linear", align_corners=True)
            pts_preds = pts_preds.permute(0, 2, 1).contiguous()

        loss_pts = self.loss_pts(
            pts_preds[isnotnan, :, :],
            normalized_pts_targets[isnotnan, :, :],
            pts_weights[isnotnan, :, :],
            avg_factor=num_total_pos,
        )
        dir_weights = pts_weights[:, : -self.dir_interval, 0]
        denormed_pts_preds = (
            denormalize_2d_pts(pts_preds, self.pc_range)
            if not self.z_cfg["gt_z_flag"]
            else denormalize_3d_pts(pts_preds, self.pc_range)
        )
        denormed_pts_preds_dir = (
            denormed_pts_preds[:, self.dir_interval :, :] - denormed_pts_preds[:, : -self.dir_interval, :]
        )
        pts_targets_dir = pts_targets[:, self.dir_interval :, :] - pts_targets[:, : -self.dir_interval, :]
        # dir_weights = pts_weights[:, indice,:-1,0]

        loss_dir = self.loss_dir(
            denormed_pts_preds_dir[isnotnan, :, :],
            pts_targets_dir[isnotnan, :, :],
            dir_weights[isnotnan, :],
            avg_factor=num_total_pos,
        )

        bboxes = denormalize_2d_bbox(bbox_preds, self.pc_range)
        # regression IoU loss, defaultly GIoU loss
        loss_iou = self.loss_iou(
            bboxes[isnotnan, :4], bbox_targets[isnotnan, :4], bbox_weights[isnotnan, :4], avg_factor=num_total_pos
        )

        if digit_version(TORCH_VERSION) >= digit_version("1.8"):
            loss_cls = torch.nan_to_num(loss_cls)
            loss_bbox = torch.nan_to_num(loss_bbox)
            loss_iou = torch.nan_to_num(loss_iou)
            loss_pts = torch.nan_to_num(loss_pts)
            loss_dir = torch.nan_to_num(loss_dir)
        return loss_cls, loss_bbox, loss_iou, loss_pts, loss_dir

    @force_fp32(apply_to=("preds_dicts"))
    def loss(
        self,
        gt_bboxes_list,
        gt_labels_list,
        gt_seg_mask,
        gt_pv_seg_mask,
        preds_dicts,
        gt_bboxes_ignore=None,
        img_metas=None,
    ):
        """ "Loss function.
        Args:

            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indices for each
                image with shape (num_gts, ).
            preds_dicts:
                all_cls_scores (Tensor): Classification score of all
                    decoder layers, has shape
                    [nb_dec, bs, num_query, cls_out_channels].
                all_bbox_preds (Tensor): Sigmoid regression
                    outputs of all decode layers. Each is a 4D-tensor with
                    normalized coordinate format (cx, cy, w, h) and shape
                    [nb_dec, bs, num_query, 4].
                enc_cls_scores (Tensor): Classification scores of
                    points on encode feature map , has shape
                    (N, h*w, num_classes). Only be passed when as_two_stage is
                    True, otherwise is None.
                enc_bbox_preds (Tensor): Regression results of each points
                    on the encode feature map, has shape (N, h*w, 4). Only be
                    passed when as_two_stage is True, otherwise is None.
            gt_bboxes_ignore (list[Tensor], optional): Bounding boxes
                which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """
        assert gt_bboxes_ignore is None, (
            f"{self.__class__.__name__} only supports " "for gt_bboxes_ignore setting to None."
        )
        gt_vecs_list = copy.deepcopy(gt_bboxes_list)

        all_cls_scores = preds_dicts["all_cls_scores"]
        all_bbox_preds = preds_dicts["all_bbox_preds"]
        all_pts_preds = preds_dicts["all_pts_preds"]
        enc_cls_scores = preds_dicts["enc_cls_scores"]
        enc_bbox_preds = preds_dicts["enc_bbox_preds"]
        enc_pts_preds = preds_dicts["enc_pts_preds"]

        num_dec_layers = len(all_cls_scores)
        device = gt_labels_list[0].device

        gt_bboxes_list = [gt_bboxes.bbox.to(device) for gt_bboxes in gt_vecs_list]
        gt_pts_list = [gt_bboxes.fixed_num_sampled_points.to(device) for gt_bboxes in gt_vecs_list]
        if self.gt_shift_pts_pattern == "v0":
            gt_shifts_pts_list = [gt_bboxes.shift_fixed_num_sampled_points.to(device) for gt_bboxes in gt_vecs_list]
        elif self.gt_shift_pts_pattern == "v1":
            gt_shifts_pts_list = [gt_bboxes.shift_fixed_num_sampled_points_v1.to(device) for gt_bboxes in gt_vecs_list]
        elif self.gt_shift_pts_pattern == "v2":
            gt_shifts_pts_list = [gt_bboxes.shift_fixed_num_sampled_points_v2.to(device) for gt_bboxes in gt_vecs_list]
        elif self.gt_shift_pts_pattern == "v3":
            gt_shifts_pts_list = [gt_bboxes.shift_fixed_num_sampled_points_v3.to(device) for gt_bboxes in gt_vecs_list]
        elif self.gt_shift_pts_pattern == "v4":
            gt_shifts_pts_list = [gt_bboxes.shift_fixed_num_sampled_points_v4.to(device) for gt_bboxes in gt_vecs_list]
        else:
            raise NotImplementedError
        all_gt_bboxes_list = [gt_bboxes_list for _ in range(num_dec_layers)]
        all_gt_labels_list = [gt_labels_list for _ in range(num_dec_layers)]
        [gt_pts_list for _ in range(num_dec_layers)]
        all_gt_shifts_pts_list = [gt_shifts_pts_list for _ in range(num_dec_layers)]
        all_gt_bboxes_ignore_list = [gt_bboxes_ignore for _ in range(num_dec_layers)]

        losses_cls, losses_bbox, losses_iou, losses_pts, losses_dir = multi_apply(
            self.loss_single,
            all_cls_scores,
            all_bbox_preds,
            all_pts_preds,
            all_gt_bboxes_list,
            all_gt_labels_list,
            all_gt_shifts_pts_list,
            all_gt_bboxes_ignore_list,
        )

        loss_dict = dict()
        if self.aux_seg["use_aux_seg"]:
            if self.aux_seg["bev_seg"]:
                if preds_dicts["seg"] is not None:
                    seg_output = preds_dicts["seg"]
                    num_imgs = seg_output.size(0)
                    seg_gt = torch.stack([gt_seg_mask[i] for i in range(num_imgs)], dim=0)
                    loss_seg = self.loss_seg(seg_output, seg_gt.float())
                    loss_dict["loss_seg"] = loss_seg
            if self.aux_seg["pv_seg"]:
                if preds_dicts["pv_seg"] is not None:
                    pv_seg_output = preds_dicts["pv_seg"]
                    num_imgs = pv_seg_output.size(0)
                    pv_seg_gt = torch.stack([gt_pv_seg_mask[i] for i in range(num_imgs)], dim=0)
                    loss_pv_seg = self.loss_pv_seg(pv_seg_output, pv_seg_gt.float())
                    loss_dict["loss_pv_seg"] = loss_pv_seg
        # loss of proposal generated from encode feature map.
        if enc_cls_scores is not None:
            binary_labels_list = [torch.zeros_like(gt_labels_list[i]) for i in range(len(all_gt_labels_list))]
            # TODO bug here
            enc_loss_cls, enc_losses_bbox, enc_losses_iou, enc_losses_pts, enc_losses_dir = self.loss_single(
                enc_cls_scores,
                enc_bbox_preds,
                enc_pts_preds,
                gt_bboxes_list,
                binary_labels_list,
                gt_pts_list,
                gt_bboxes_ignore,
            )
            loss_dict["enc_loss_cls"] = enc_loss_cls
            loss_dict["enc_loss_bbox"] = enc_losses_bbox
            loss_dict["enc_losses_iou"] = enc_losses_iou
            loss_dict["enc_losses_pts"] = enc_losses_pts
            loss_dict["enc_losses_dir"] = enc_losses_dir

        # loss from the last decoder layer
        loss_dict["loss_cls"] = losses_cls[-1]
        loss_dict["loss_bbox"] = losses_bbox[-1]
        loss_dict["loss_iou"] = losses_iou[-1]
        loss_dict["loss_pts"] = losses_pts[-1]
        loss_dict["loss_dir"] = losses_dir[-1]
        # loss from other decoder layers
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i, loss_iou_i, loss_pts_i, loss_dir_i in zip(
            losses_cls[:-1], losses_bbox[:-1], losses_iou[:-1], losses_pts[:-1], losses_dir[:-1]
        ):
            loss_dict[f"d{num_dec_layer}.loss_cls"] = loss_cls_i
            loss_dict[f"d{num_dec_layer}.loss_bbox"] = loss_bbox_i
            loss_dict[f"d{num_dec_layer}.loss_iou"] = loss_iou_i
            loss_dict[f"d{num_dec_layer}.loss_pts"] = loss_pts_i
            loss_dict[f"d{num_dec_layer}.loss_dir"] = loss_dir_i
            num_dec_layer += 1
        return loss_dict

    @force_fp32(apply_to=("preds_dicts"))
    def get_bboxes(self, preds_dicts, img_metas, rescale=False):
        """Generate bboxes from bbox head predictions.
        Args:
            preds_dicts (tuple[list[dict]]): Prediction results.
            img_metas (list[dict]): Point cloud and image's meta info.
        Returns:
            list[dict]: Decoded bbox, scores and labels after nms.
        """
        # bboxes: xmin, ymin, xmax, ymax
        preds_dicts = self.bbox_coder.decode(preds_dicts)

        num_samples = len(preds_dicts)
        ret_list = []
        for i in range(num_samples):
            preds = preds_dicts[i]
            bboxes = preds["bboxes"]
            # bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 5] * 0.5

            # code_size = bboxes.shape[-1]
            # bboxes = img_metas[i]['box_type_3d'](bboxes, code_size)
            scores = preds["scores"]
            labels = preds["labels"]
            pts = preds["pts"]

            ret_list.append([bboxes, scores, labels, pts])

        return ret_list

    @force_fp32(apply_to=("preds_dicts"))
    def loss_all(self, gts, track_query_info, labels, preds_dicts, img_metas):
        """
        public pred format + private gt format
        """

        bev_embed = preds_dicts["bev_embed"]  # (hxw, bs, c)
        all_cls_scores = preds_dicts["all_cls_scores"]  # (num_dec_layer, bs, num_query, num_classes)
        preds_dicts["all_bbox_preds"]
        all_pts_preds = preds_dicts["all_pts_preds"]  # (num_dec_layer, bs, num_query, num_pts, 3)
        preds_dicts["enc_cls_scores"]
        preds_dicts["enc_bbox_preds"]
        preds_dicts["enc_pts_preds"]
        seg = preds_dicts["seg"]  # (bs, 1, bev_h, bev_w)
        one2many_outs = preds_dicts[
            "one2many_outs"
        ]  # query_num*K  dict_keys(['all_cls_scores', 'all_bbox_preds', 'all_pts_preds', 'enc_cls_scores', 'enc_bbox_preds', 'enc_pts_preds', 'seg', 'pv_seg'])

        # support lane attr
        all_lane_colors = preds_dicts["all_lane_colors"]  # (attr_nums, bs, num_query, num_pts * num_lane_color_attr)
        all_lane_dashs = preds_dicts["all_lane_dashs"]  # (attr_nums, bs, num_query, num_pts * num_lane_dash_attr)
        all_lane_curbs = preds_dicts["all_lane_curbs"]  # (attr_nums, bs, num_query, num_pts * num_lane_curb_attr)

        # 1. prepare labels and images
        all_vectors = labels["vectors"]
        all_attrs = labels["attrs"]  # [[frame{cls: tensor}]], tensor [N_ins, N_attr]

        all_scene_weight = labels["map_scene_weight"]
        all_valid_region = labels["valid_region"]
        all_bev_manual_mask = labels["bev_manual_mask"]
        cur_vectors = [batch_vec[-1] for batch_vec in all_vectors]
        cur_attrs = [batch_vec[-1] for batch_vec in all_attrs]

        gts = self.batch_data(cur_vectors, cur_attrs, all_valid_region, bev_embed.device, all_bev_manual_mask)

        outputs = []
        num_dec_layer = all_cls_scores.size(0)  # attr_nums
        bs = all_cls_scores.size(1)
        for i in range(num_dec_layer):
            line_scores = all_cls_scores[i].reshape(bs, self.line_query_num_ins, -1)  # (bs, num_query, num_classes)
            line_reg_points = all_pts_preds[i].reshape(bs, self.line_query_num_ins, -1)  # (bs, num_query, 3*num_points)

            # add attr
            tmp_lane_colors = all_lane_colors[i].reshape(
                bs, self.line_query_num_ins, -1
            )  # (bs, num_query, num_pts * num_classes)
            tmp_lane_dashs = all_lane_dashs[i].reshape(
                bs, self.line_query_num_ins, -1
            )  # (bs, num_query, num_pts * num_classes)
            tmp_lane_curbs = all_lane_curbs[i].reshape(
                bs, self.line_query_num_ins, -1
            )  # (bs, num_query, num_pts * num_classes)

            scores = line_scores
            scores_list, line_scores_list = [], []
            line_reg_points_list = []

            lane_color_list = []
            lane_dash_list = []
            lane_curb_list = []

            for j in range(len(scores)):
                # padding queries should not be output
                line_reg_points_list.append(line_reg_points[j])
                scores_list.append(scores[j])
                line_scores_list.append(line_scores[j])

                # add attr
                lane_color_list.append(tmp_lane_colors[j])
                lane_dash_list.append(tmp_lane_dashs[j])
                lane_curb_list.append(tmp_lane_curbs[j])

            pred_dict = {
                "scores": scores_list,
                "line_scores": line_scores_list,
                "lines": line_reg_points_list,
                "lane_colors": lane_color_list,
                "lane_dashs": lane_dash_list,
                "lane_curbs": lane_curb_list,
            }

            outputs.append(pred_dict)

        # Pass in the track query information to massage the cost matrix
        loss_dict, det_match_idxs, det_match_gt_idxs, gt_info_list, matched_reg_cost = self.loss_private(
            gts=gts, preds=outputs, track_info=track_query_info, scene_weight=all_scene_weight
        )

        # one-to-many
        # reorg one-to-many pred
        many_outputs = []
        num_dec_layer = one2many_outs["all_cls_scores"].size(0)
        bs = one2many_outs["all_cls_scores"].size(1)
        for i in range(num_dec_layer):
            many_line_scores = one2many_outs["all_cls_scores"][i].reshape(
                bs, self.line_query_num_ins * self.k_one2many, -1
            )  # (bs, num_query, num_classes)
            many_line_reg_points = one2many_outs["all_pts_preds"][i].reshape(
                bs, self.line_query_num_ins * self.k_one2many, -1
            )  # (bs, num_query, num_pts * 3)

            # add attr
            many_tmp_lane_colors = one2many_outs["all_lane_colors"][i].reshape(
                bs, self.line_query_num_ins * self.k_one2many, -1
            )  # (bs, num_query, num_pts * num_classes)
            many_tmp_lane_dashs = one2many_outs["all_lane_dashs"][i].reshape(
                bs, self.line_query_num_ins * self.k_one2many, -1
            )  # (bs, num_query, num_pts * num_classes)
            many_tmp_lane_curbs = one2many_outs["all_lane_curbs"][i].reshape(
                bs, self.line_query_num_ins * self.k_one2many, -1
            )  # (bs, num_query, num_pts * num_classes)

            many_scores = many_line_scores  # specific last score..
            many_scores_list, many_line_scores_list = [], []
            many_line_reg_points_list = []

            many_lane_color_list = []
            many_lane_dash_list = []
            many_lane_curb_list = []

            for j in range(len(many_scores)):
                # padding queries should not be output
                many_line_reg_points_list.append(many_line_reg_points[j])
                many_scores_list.append(many_scores[j])
                many_line_scores_list.append(many_line_scores[j])

                many_lane_color_list.append(many_tmp_lane_colors[j])
                many_lane_dash_list.append(many_tmp_lane_dashs[j])
                many_lane_curb_list.append(many_tmp_lane_curbs[j])

            pred_dict = {
                "scores": many_scores_list,
                "line_scores": many_line_scores_list,
                "lines": many_line_reg_points_list,
                "lane_colors": many_lane_color_list,
                "lane_dashs": many_lane_dash_list,
                "lane_curbs": many_lane_curb_list,
            }

            many_outputs.append(pred_dict)

        # add one-to-many loss, simply copy K times and padding.
        many_gts = copy.deepcopy(gts)
        for i in range(num_dec_layer):
            for bs_id in range(bs):
                many_gts[i]["lines_labels"][bs_id] = torch.cat([gts[i]["lines_labels"][bs_id]] * self.k_one2many, dim=0)
                many_gts[i]["lines"][bs_id] = torch.cat([gts[i]["lines"][bs_id]] * self.k_one2many, dim=0)
                many_gts[i]["lineattrs"][bs_id] = torch.cat([gts[i]["lineattrs"][bs_id]] * self.k_one2many, dim=0)

        # Pass in the track query information to massage the cost matrix
        (
            many_loss_dict,
            many_det_match_idxs,
            many_det_match_gt_idxs,
            many_gt_info_list,
            many_matched_reg_cost,
        ) = self.loss_private(
            gts=many_gts, preds=many_outputs, track_info=track_query_info, scene_weight=all_scene_weight
        )

        # loss rename
        new_many_loss_dict = dict()
        for k, v in many_loss_dict.items():
            new_many_loss_dict[f"many_{k}"] = v
        loss_dict.update(new_many_loss_dict)

        # add seg
        seg_gt = labels["lane_bev_segment_map"].float()
        extra_seg_loss = self.loss_seg(seg, seg_gt)
        loss_dict["seg"] = extra_seg_loss

        # one-to-many ###

        if self.loss_cls.use_sigmoid:  # 20241023 版为 True
            scores = scores.sigmoid()
            many_scores = many_scores.sigmoid()  # add one-to-many last score..

        line_reg_points = line_reg_points.view(bs, self.line_query_num_ins, self.line_num_points, 3)
        many_line_reg_points = many_line_reg_points.view(
            bs, self.line_query_num_ins * self.k_one2many, self.line_num_points, 3
        )

        lane_color_scores = all_lane_colors[-1].reshape(bs, self.line_query_num_ins, -1)
        lane_dash_scores = all_lane_dashs[-1].reshape(bs, self.line_query_num_ins, -1)
        curb_scores = all_lane_curbs[-1].reshape(bs, self.line_query_num_ins, -1)

        lane_color_scores = lane_color_scores.view(bs, self.line_query_num_ins, self.line_num_points, -1)
        lane_dash_scores = lane_dash_scores.view(bs, self.line_query_num_ins, self.line_num_points, -1)
        curb_scores = curb_scores.view(bs, self.line_query_num_ins, self.line_num_points, -1)

        all_pts = (line_reg_points,)
        scores_all = (scores, lane_color_scores, lane_dash_scores, curb_scores)
        return outputs, loss_dict, det_match_idxs, det_match_gt_idxs, gt_info_list, all_pts, scores_all

    @force_fp32(apply_to=("gt_lines_list", "preds_dicts"))
    def loss_private(
        self,
        gts,
        preds,
        gt_bboxes_ignore=None,
        track_info=None,
        reduction="mean",
        scene_weight=None,
    ):
        """
        Loss Function.
        Args:
            gts (list[dict]): list length: num_layers
                dict {
                    'label': list[tensor(num_gts, )], list length: batchsize,
                    'line': list[tensor(num_gts, 2*num_points)], list length: batchsize,
                    ...
                }
            preds (list[dict]): list length: num_layers
                dict {
                    'lines': tensor(bs, num_queries, 2*num_points),
                    'scores': tensor(bs, num_queries, class_out_channels),
                }

            gt_bboxes_ignore (list[Tensor], optional): Bounding boxes
                which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """
        assert gt_bboxes_ignore is None, (
            f"{self.__class__.__name__} only supports " "for gt_bboxes_ignore setting to None."
        )

        track_info = [track_info for _ in range(len(gts))]
        # Since there might have multi layer
        losses, pos_inds_lists, pos_gt_inds_lists, matched_reg_costs, gt_info_list = multi_apply(
            self.loss_single_private, preds, gts, track_info, scene_weight=scene_weight, reduction=reduction
        )

        # Format the losses
        loss_dict = dict()
        # loss from the last decoder layer
        for k, v in losses[-1].items():
            loss_dict[k] = v

        # Loss from other decoder layers
        num_dec_layer = 0
        for loss in losses[:-1]:
            for k, v in loss.items():
                loss_dict[f"d{num_dec_layer}.{k}"] = v
            num_dec_layer += 1

        return loss_dict, pos_inds_lists, pos_gt_inds_lists, gt_info_list, matched_reg_costs

    # @force_fp32(apply_to=('preds', 'gts'))
    def loss_single_private(
        self, preds_ori, gts, track_info=None, gt_bboxes_ignore_list=None, scene_weight=None, reduction="none"
    ):
        """
        Loss function for outputs from a single decoder layer of a single
        feature level.
        Args:
            preds (dict):
                - lines (Tensor): shape (bs, num_queries, 2*num_points)
                - scores (Tensor): shape (bs, num_queries, num_class_channels)
            gts (dict):
                - class_label (list[Tensor]): tensor shape (num_gts, )
                - lines (list[Tensor]): tensor shape (num_gts, 2*num_points)
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """

        # Get target for each sample
        (
            preds,
            new_gts,
            num_total_pos,
            num_total_neg,
            pos_inds_list,
            pos_gt_inds_list,
            matched_reg_cost,
        ) = self.get_targets_private(preds_ori, gts, track_info, gt_bboxes_ignore_list, scene_weight)
        # Batched all data
        # for k, v in new_gts.items():
        #     new_gts[k] = torch.stack(v, dim=0) # tensor (bs, num_q, ...)

        # construct weighted avg_factor to match with the official DETR repo
        cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.bg_cls_weight

        if self.sync_cls_avg_factor:
            cls_avg_factor = reduce_mean(preds["scores"][0].new_tensor([cls_avg_factor]))
        cls_avg_factor = max(cls_avg_factor, 1)

        # Classification loss
        # since the inputs needs the second dim is the class dim, we permute the prediction.
        pred_scores = torch.cat(preds["scores"], dim=0)  # (bs*num_q, cls_out_channles)
        cls_scores = pred_scores.reshape(-1, self.cls_out_channels)  # (bs*num_q, cls_out_channels)
        cls_labels = torch.cat(new_gts["labels"], dim=0).reshape(-1)  # (bs*num_q, )
        cls_weights = torch.cat(new_gts["label_weights"], dim=0).reshape(-1)  # (bs*num_q, )
        # if track_info is not None:
        #     cls_weights = cls_weights * padding_loss_mask.float()

        if cls_scores.shape[0] != 0:
            loss_cls = self.loss_cls(cls_scores, cls_labels, cls_weights, avg_factor=cls_avg_factor)

            # Compute the average number of gt boxes across all gpus, for
            # normalization purposes
            num_total_pos = loss_cls.new_tensor([num_total_pos])
            num_total_pos = torch.clamp(reduce_mean(num_total_pos), min=1).item()
            loss_reg_lines = self.loss_reg_onetype(
                preds["lines"], new_gts["lines"], new_gts["lines_weights"], num_total_pos, self.loss_line_reg
            )

            # add edge-dir loss
            pts_preds = torch.cat(preds["lines"], dim=0)
            pts_preds = pts_preds.reshape(len(pts_preds), -1, self.code_size)
            pts_targets = torch.cat(new_gts["lines"], dim=0)  # cat [bs, num_query, 20, 3]
            pts_targets = pts_targets.reshape(len(pts_targets), -1, self.code_size)
            denormed_pts_preds = (
                denormalize_2d_pts(pts_preds, self.pc_range)
                if not self.z_cfg["gt_z_flag"]
                else denormalize_3d_pts(pts_preds, self.pc_range)
            )
            denormed_pts_preds_dir = (
                denormed_pts_preds[:, self.dir_interval :, :] - denormed_pts_preds[:, : -self.dir_interval, :]
            )
            denormed_pts_targets = (
                denormalize_2d_pts(pts_targets, self.pc_range)
                if not self.z_cfg["gt_z_flag"]
                else denormalize_3d_pts(pts_targets, self.pc_range)
            )
            denormed_pts_targets_dir = (
                denormed_pts_targets[:, self.dir_interval :, :] - pts_targets[:, : -self.dir_interval, :]
            )

            dir_weight_list = []
            for bs_id in range(len(new_gts["lines_weights"])):
                dir_weight_list.append(
                    new_gts["lines_weights"][bs_id].reshape(len(new_gts["lines_weights"][bs_id]), -1, self.code_size)
                )
            cat_dir_weights = torch.cat(dir_weight_list, dim=0)
            dir_weights = cat_dir_weights  # share weight

            # note: delete isnotnan..., reuse line weight
            loss_dir = self.loss_dir(
                denormed_pts_preds_dir,
                denormed_pts_targets_dir,
                dir_weights[:, self.dir_interval :, 0],
                avg_factor=num_total_pos,
            )

            # compute attr loss
            line_attr = self.loss_attr(preds, new_gts, cls_avg_factor)
        else:
            loss_cls = torch.tensor(0)
            loss_reg_lines = torch.tensor(0)
            loss_dir = torch.tensor(0)
            torch.tensor(0)

        loss_dict = dict(
            cls=loss_cls,
            reg_lines=loss_reg_lines,
            dir=loss_dir,  # add edeg-direction sup
            line_attr=line_attr,
        )

        new_gts_info = {
            "labels": new_gts["labels"],
            "lines": new_gts["lines"],
        }

        return loss_dict, pos_inds_list, pos_gt_inds_list, matched_reg_cost, new_gts_info

    def loss_attr(self, preds, new_gts, cls_avg_factor):
        pred_dash_scores = torch.cat(preds["lane_dash_scores"], dim=0).reshape(
            -1, self.num_lane_dash_attr
        )  # (bs * num_query, 2)
        pred_color_scores = torch.cat(preds["lane_color_scores"], dim=0).reshape(
            -1, self.num_lane_color_attr
        )  # (bs * num_query, 2)
        pred_curb_scores = torch.cat(preds["curb_scores"], dim=0).reshape(
            -1, self.num_curb_attr
        )  # (bs * num_query, cls_out_channles)

        lineattrs_weight = torch.cat(new_gts["lineattrs_weight"], dim=0).reshape(
            -1, self.attr_nums
        )  # self.attr_nums=3 refer 虚实 / 颜色 / 是否栏杆
        gt_lineattrs = torch.cat(new_gts["lineattrs"], dim=0).reshape(
            -1, self.attr_nums
        )  # self.attr_nums=3 refer 虚实 / 颜色 / 是否栏杆

        dash_loss = self.loss_lane_dash_attr(
            pred_dash_scores, gt_lineattrs[..., 0], lineattrs_weight[..., 0], avg_factor=cls_avg_factor
        )
        color_loss = self.loss_lane_color_attr(
            pred_color_scores, gt_lineattrs[..., 1], lineattrs_weight[..., 1], avg_factor=cls_avg_factor
        )
        curb_loss = self.loss_curb_attr(
            pred_curb_scores, gt_lineattrs[..., 2], lineattrs_weight[..., 2], avg_factor=cls_avg_factor
        )  # fix 6->3
        line_attr = dash_loss + color_loss + curb_loss
        return line_attr

    # @force_fp32(apply_to=('preds', 'gts'))
    def get_targets_private(self, preds, gts, track_info=None, gt_bboxes_ignore_list=None, scene_weight=None):
        """
            Compute regression and classification targets for a batch image.
            Outputs from a single decoder layer of a single feature level are used.
            Args:
                preds (dict):
                    - lines (Tensor): shape (bs, num_queries, 2*num_points)
                    - scores (Tensor): shape (bs, num_queries, num_class_channels)
                gts (dict):
                    - class_label (list[Tensor]): tensor shape (num_gts, )
                    - lines (list[Tensor]): tensor shape (num_gts, 2*num_points)
                gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                    boxes which can be ignored for each image. Default None.
            Returns:
                tuple: a tuple containing the following targets.
                    - labels_list (list[Tensor]): Labels for all images.
                    - label_weights_list (list[Tensor]): Label weights for all \
                        images.
                    - lines_targets_list (list[Tensor]): Lines targets for all \
                        images.
                    - lines_weight_list (list[Tensor]): Lines weights for all \
                        images.
                    - num_total_pos (int): Number of positive samples in all \
                        images.
                    - num_total_neg (int): Number of negative samples in all \
                        images.
        """
        assert gt_bboxes_ignore_list is None, "Only supports for gt_bboxes_ignore setting to None."

        # format the inputs
        gt_l_labels = gts["lines_labels"]
        gt_lines = gts["lines"]
        gt_lineattrs = gts["lineattrs"]
        gt_valid_region = gts["valid_region"]
        gt_bev_manual_mask = gts["bev_manual_mask"]

        line_scores_pred = preds["line_scores"]

        lines_pred = preds["lines"]

        lines_attrs_pred = {
            "lane_color_scores": preds["lane_colors"],
            "lane_dash_scores": preds["lane_dashs"],
            "curb_scores": preds["lane_curbs"],
        }

        line_target_info = self.get_target_onetype(
            "line",
            line_scores_pred,
            lines_pred,
            lines_attrs_pred,
            gt_l_labels,
            gt_lines,
            gt_lineattrs,
            self.line_num_points,
            track_info,
            scene_weight,
            gt_bboxes_ignore_list,
            gt_valid_region,
            gt_bev_manual_mask,
        )
        num_total_pos = line_target_info["num_total_pos"]
        num_total_neg = line_target_info["num_total_neg"]

        scores_list, labels_list, label_weights_list = self.merge_label_target(line_target_info)

        pos_inds_list = None  # TODO merge line、stop、box info
        pos_gt_inds_list = None  # TODO merge line、stop、box info
        matched_reg_cost = None  # TODO merge line、stop、box info
        new_gts = dict(
            labels=labels_list,  # list[Tensor(num_query, )], length=bs
            label_weights=label_weights_list,  # list[Tensor(num_query, )], length=bs, all ones
            lines=line_target_info["lines_targets_list"],  # list[Tensor(num_query, 2*num_pts)], length=bs
            lines_weights=line_target_info["lines_weights_list"],  # list[Tensor(num_query, 2*num_pts)], length=bs
            lineattrs=line_target_info["attr_target_list"],
            lineattrs_weight=line_target_info["attr_weights_list"],
        )

        new_pred_dict = {
            "scores": scores_list,
            "line_scores": line_target_info["scores_pred"],
            "lines": line_target_info["lines_pred"],
            "lane_color_scores": line_target_info["attrs_pred"]["lane_color_scores"],
            "lane_dash_scores": line_target_info["attrs_pred"]["lane_dash_scores"],
            "curb_scores": line_target_info["attrs_pred"]["curb_scores"],
        }

        return new_pred_dict, new_gts, num_total_pos, num_total_neg, pos_inds_list, pos_gt_inds_list, matched_reg_cost

    def get_target_onetype(
        self,
        type_name,
        scores_pred,
        lines_pred,
        attrs_pred,
        gt_labels,
        gt_lines,
        gt_attrs,
        point_num,
        track_info,
        scene_weight,
        gt_bboxes_ignore_list,
        gt_valid_region,
        gt_bev_manual_mask,
    ):
        point_num = [point_num for _ in scores_pred]
        type_name = [type_name for _ in scores_pred]

        track_info = [None for _ in range(len(scores_pred))]
        ignore_info = [None for _ in range(len(scores_pred))]

        (
            valid_mask_list,
            labels_list,
            label_weights_list,
            lines_targets_list,
            lines_weights_list,
            attr_target_list,
            attr_weights_list,
            pos_inds_list,
            neg_inds_list,
            pos_gt_inds_list,
            matched_reg_cost,
        ) = multi_apply(
            self._get_target_single_private,
            type_name,
            scores_pred,
            lines_pred,
            gt_labels,
            gt_lines,
            gt_attrs,
            track_info,
            scene_weight,
            point_num,
            gt_valid_region,
            ignore_info,
            gt_bev_manual_mask,
        )

        num_total_pos = sum((inds.numel() for inds in pos_inds_list))
        num_total_neg = sum((inds.numel() for inds in neg_inds_list))

        if track_info[0] is not None:
            # remove the padding elements from the neg counting
            padding_mask = torch.cat([t["query_padding_mask"] for t in track_info], dim=0)
            num_padding = padding_mask.sum()
            num_total_neg -= num_padding
        for i in range(len(valid_mask_list)):
            scores_pred[i] = scores_pred[i][valid_mask_list[i]]
            lines_pred[i] = lines_pred[i][valid_mask_list[i]]
            for k in attrs_pred.keys():
                attrs_pred[k][i] = attrs_pred[k][i][valid_mask_list[i]]
        return {
            "scores_pred": scores_pred,
            "lines_pred": lines_pred,
            "attrs_pred": attrs_pred,
            "labels_list": labels_list,
            "label_weights_list": label_weights_list,
            "lines_targets_list": lines_targets_list,
            "lines_weights_list": lines_weights_list,
            "attr_target_list": attr_target_list,
            "attr_weights_list": attr_weights_list,
            "pos_inds_list": pos_inds_list,
            "neg_inds_list": neg_inds_list,
            "pos_gt_inds_list": pos_gt_inds_list,
            "matched_reg_cost": matched_reg_cost,
            "num_total_pos": num_total_pos,
            "num_total_neg": num_total_neg,
        }

    def batch_data(self, vectors, gt_attrs, valid_regions, device, bev_manual_mask):
        bs = len(vectors)
        # filter none vector's case
        num_gts = []
        for idx in range(bs):
            num_gts.append(sum([len(v) for k, v in vectors[idx].items()]))
        [i for i in range(bs) if num_gts[i] > 0]
        # assert len(valid_idx) == bs # make sure every sample has gts

        all_line_labels_list, all_stop_labels_list, all_box_labels_list, all_entrance_labels_list = [], [], [], []

        all_gt2local = []
        all_local2gt = []

        all_lines_list, all_stops_list, all_boxes_list, all_entrances_list = [], [], [], []
        all_lineattrs_list, all_stopattrs_list, all_boxattrs_list, all_entranceattrs_list = [], [], [], []

        all_region_list = []
        for idx in range(bs):
            l_labels, s_labels, b_labels, e_labels = [], [], [], []
            lines, stops, boxes, entrances = [], [], [], []
            lineattrs, stopattrs, boxattrs, entranceattrs = [], [], [], []
            gt2local = []
            local2gt = {}
            for label, _lines in vectors[idx].items():
                assert len(_lines) == len(gt_attrs[idx][label])
                for _ins_id, _line in enumerate(_lines):
                    gt2local.append([label, _ins_id])
                    local2gt[(label, _ins_id)] = len(lines)
                    _line = torch.tensor(_line)
                    _attr = torch.tensor(gt_attrs[idx][label][_ins_id])
                    if len(_line.shape) == 3:  # permutation
                        num_permute, num_points, coords_dim = _line.shape
                        if label in [0, 1]:  # TODO  0, 1 代表 laneline、curb， 临时验证使用
                            l_labels.append(label)
                            lines.append(_line.clone().reshape(num_permute, -1))  # (38, 40)
                            lineattrs.append(_attr.clone().reshape(num_permute, -1))  # (38, 40)
                        elif label in [2]:  # TODO  2 代表 stopline，6 代表 entrance， 建模方式均统一为point-vector-width
                            s_labels.append(label)
                            stops.append(_line.clone().reshape(num_permute, -1))  # (2, 40)
                            stopattrs.append(_attr.clone().reshape(num_permute, -1))  # (2, 40)
                        elif label in [6]:  # TODO  2 代表 stopline，6 代表 entrance， 建模方式均统一为point-vector-width
                            e_labels.append(label)
                            entrances.append(_line.clone().reshape(num_permute, -1))  # (2, 40)
                            entranceattrs.append(_attr.clone().reshape(num_permute, -1))  # (2, 40)
                        elif label >= 3:
                            b_labels.append(label)
                            boxes.append(_line.clone().reshape(num_permute, -1))  # (8, 40)
                            boxattrs.append(_attr.clone().reshape(num_permute, -1))  # (8, 40)
                    elif len(_line.shape) == 2:
                        if label in [0, 1]:  # TODO  0, 1 代表 laneline、curb， 临时验证使用
                            l_labels.append(label)
                            lines.append(_line.clone().reshape(-1))
                            lineattrs.append(_attr.clone().reshape(-1))
                        elif label in [2]:  # TODO  2 代表 stopline，6 代表 entrance， 建模方式均统一为point-vector-width
                            s_labels.append(label)
                            stops.append(_line.clone().reshape(-1))
                            stopattrs.append(_attr.clone().reshape(-1))
                        elif label in [6]:  # TODO  2 代表 stopline，6 代表 entrance， 建模方式均统一为point-vector-width
                            e_labels.append(label)
                            entrances.append(_line.clone().reshape(-1))
                            entranceattrs.append(_attr.clone().reshape(-1))
                        elif label >= 3:
                            b_labels.append(label)
                            boxes.append(_line.clone().reshape(-1))
                            boxattrs.append(_attr.clone().reshape(-1))
                    else:
                        assert False
            if len(lines) > 0:
                all_line_labels_list.append(torch.tensor(l_labels, dtype=torch.long).to(device))
                all_lines_list.append(torch.stack(lines).float().to(device))
                all_lineattrs_list.append(torch.stack(lineattrs).float().to(device))  # N_lane + N_curb
            else:
                all_line_labels_list.append(torch.tensor(l_labels, dtype=torch.long).to(device))
                all_lines_list.append(torch.tensor(lines).float().to(device))
                all_lineattrs_list.append(torch.tensor(lineattrs).float().to(device))  # N_lane + N_curb
            if len(stops) > 0:
                all_stop_labels_list.append(torch.tensor(s_labels, dtype=torch.long).to(device))
                all_stops_list.append(torch.stack(stops).float().to(device))
                all_stopattrs_list.append(torch.stack(stopattrs).float().to(device))  # unuse
            else:
                all_stop_labels_list.append(torch.tensor(s_labels, dtype=torch.long).to(device))
                all_stops_list.append(torch.tensor(stops).float().to(device))
                all_stopattrs_list.append(torch.tensor(stopattrs).float().to(device))  # unuse
            if len(boxes) > 0:
                all_box_labels_list.append(torch.tensor(b_labels, dtype=torch.long).to(device))
                all_boxes_list.append(torch.stack(boxes).float().to(device))
                all_boxattrs_list.append(torch.stack(boxattrs).float().to(device))  # N_arrow
            else:
                all_box_labels_list.append(torch.tensor(b_labels, dtype=torch.long).to(device))
                all_boxes_list.append(torch.tensor(boxes).float().to(device))
                all_boxattrs_list.append(torch.tensor(boxattrs).float().to(device))  # N_arrow
            if len(entrances) > 0:
                all_entrance_labels_list.append(torch.tensor(e_labels, dtype=torch.long).to(device))
                all_entrances_list.append(torch.stack(entrances).float().to(device))
                all_entranceattrs_list.append(torch.stack(entranceattrs).float().to(device))  # N_arrow
            else:
                all_entrance_labels_list.append(torch.tensor(e_labels, dtype=torch.long).to(device))
                all_entrances_list.append(torch.tensor(entrances).float().to(device))
                all_entranceattrs_list.append(torch.tensor(entranceattrs).float().to(device))  # N_arrow
            region_tensor = torch.from_numpy(np.array(valid_regions[idx][0])).float().to(device).flatten(0, 1)
            all_region_list.append(region_tensor)
            all_gt2local.append(gt2local)
            all_local2gt.append(local2gt)

        gt = {
            "lines_labels": all_line_labels_list,
            "stops_labels": all_stop_labels_list,
            "boxes_labels": all_box_labels_list,
            "entrances_labels": all_entrance_labels_list,
            "lines": all_lines_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_pts*coords_num]
            "stops": all_stops_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "boxes": all_boxes_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "entrances": all_entrances_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "lineattrs": all_lineattrs_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "stopattrs": all_stopattrs_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "boxattrs": all_boxattrs_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "entranceattrs": all_entranceattrs_list,  # batch List[Tensor, ], tensor shape: [N_ins, num_permute, num_attrs*coords_num]
            "gt2local": all_gt2local,  # batch List[List, ], eg. [[1, 0], [1, 1], ...]
            "local2gt": all_local2gt,  # batch List[dict, ], dict 的 key 为tuple, value 为对一个 index
            "valid_region": all_region_list,
        }

        # add bev mask
        all_bev_manual_mask_list = []
        for idx in range(bs):
            all_bev_manual_mask_list.append(bev_manual_mask[idx : idx + 1, ...])
        gt["bev_manual_mask"] = all_bev_manual_mask_list  # batch List[1, 1, 100, 60]

        gts = [deepcopy(gt) for _ in range(self.num_decoder_layers)]

        return gts

    @force_fp32(apply_to=("score_pred", "lines_pred", "gt_lines"))
    def _get_target_single_private(
        self,
        type_name,
        score_pred_ori,
        lines_pred_ori,
        gt_labels,
        gt_lines,
        gt_attrs,
        track_info=None,
        scene_weight=None,
        point_num=None,
        gt_valid_region=None,
        gt_bboxes_ignore=None,
        gt_bev_manual_mask=None,
    ):
        """
        Compute regression and classification targets for one image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            score_pred (Tensor): Box score logits from a single decoder layer
                for one image. Shape [num_query, cls_out_channels].
            lines_pred (Tensor):
                shape [num_query, 2*num_points]
            gt_labels (torch.LongTensor)
                shape [num_gt, ]
            gt_lines (Tensor):
                shape [num_gt, 2*num_points].

        Returns:
            tuple[Tensor]: a tuple containing the following for one sample.
                - labels (LongTensor): Labels of each image.
                    shape [num_query, 1]
                - label_weights (Tensor]): Label weights of each image.
                    shape [num_query, 1]
                - lines_target (Tensor): Lines targets of each image.
                    shape [num_query, num_points, 2]
                - lines_weights (Tensor): Lines weights of each image.
                    shape [num_query, num_points, 2]
                - pos_inds (Tensor): Sampled positive indices for each image.
                - neg_inds (Tensor): Sampled negative indices for each image.
        """
        # 排除 lines_pred gt_valid_region
        lines_pred_reshaped = lines_pred_ori.view(lines_pred_ori.shape[0], -1, 3)

        # mask相关的核心模块
        gt_bev_manual_mask = gt_bev_manual_mask.squeeze()
        ignore_sum = torch.sum(torch.eq(gt_bev_manual_mask, -1)).item()
        if ignore_sum > 0:
            # 历史数据
            x_coords = torch.mean(lines_pred_reshaped[:, :, 0], dim=1)
            y_coords = torch.mean(lines_pred_reshaped[:, :, 1], dim=1)

            minx, miny, _, maxx, maxy, _ = gt_valid_region
            valid_mask = (x_coords >= minx) & (x_coords <= maxx) & (y_coords >= miny) & (y_coords <= maxy)

            # 如果仅有少量query处于有效区域，应如何处理？->按照 y 区域分配定量query + 在有效区域的点
            query_s_idx, query_e_idx = int(len(lines_pred_ori) * miny), int(len(lines_pred_ori) * maxy)
            valid_mask[query_s_idx:query_e_idx] = True
            if valid_mask.sum() == 0:
                valid_mask[:1] = True
        else:
            with torch.no_grad():
                lines_pred_reshaped_clone = lines_pred_reshaped.clone().detach()  # (50, 20, 3)
                valid_mask = torch.ones(len(lines_pred_reshaped_clone)).bool()
                for query_id in range(len(lines_pred_reshaped_clone)):
                    # 0~49
                    tmp_lane_pred = lines_pred_reshaped_clone[query_id]  # (20, 3)
                    inner_count = 0
                    for i in range(len(tmp_lane_pred)):
                        x_int = torch.clamp((tmp_lane_pred[i][0] * 60).int(), 0, 59)  # lidar-x
                        y_int = torch.clamp((tmp_lane_pred[i][1] * 100).int(), 0, 99)  # lidar-y
                        if gt_bev_manual_mask[y_int, x_int] == 1:
                            inner_count += 1
                    inner_ratio = inner_count / len(lines_pred_reshaped_clone)  # 在mask内的比例
                    if inner_ratio > 0.75:
                        # 不监督
                        valid_mask[query_id] = False
                # 避免全为0
                if valid_mask.sum() == 0:
                    valid_mask[:1] = True  # 强行赋1

        score_pred = score_pred_ori[valid_mask]
        lines_pred = lines_pred_ori[valid_mask]
        # assigner and sampler
        # We massage the matching cost here using the track info, following
        # the 3-type supervision of TrackFormer/MOTR
        num_pred_lines = len(lines_pred)
        num_gt = gt_lines.numel()
        if num_gt > 0 and num_pred_lines > 0:  # stopline、arrow 可能为空，跳过匹配
            if type_name in ["stop", "entrance"]:
                assign_pred = torch.cat((lines_pred[:, 0:3], lines_pred[:, 6:9]), axis=1)
                assert_gt = torch.cat((gt_lines[:, :, 0:3], gt_lines[:, :, 6:9]), axis=2)
            else:
                assign_pred = lines_pred
                assert_gt = gt_lines
            assign_result, gt_permute_idx, matched_reg_cost = self.private_assigner.assign(
                preds=dict(
                    lines=assign_pred,
                    scores=score_pred,
                ),
                gts=dict(
                    lines=assert_gt,
                    labels=gt_labels,
                ),
                track_info=track_info,
                gt_bboxes_ignore=None,
            )  # hardcode
            sampling_result = self.sampler.sample(assign_result, lines_pred, gt_lines)

            pos_inds = sampling_result.pos_inds
            neg_inds = sampling_result.neg_inds
            pos_gt_inds = sampling_result.pos_assigned_gt_inds

            labels = gt_lines.new_full((num_pred_lines,), self.num_classes, dtype=torch.long)  # (num_q, )
            labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds]
        else:
            gt_permute_idx = None
            pos_inds = torch.tensor(0)
            neg_inds = torch.tensor(score_pred.shape[0])
            pos_gt_inds, matched_reg_cost = [], 0  # TODO 这样的赋值是否合适？
            labels = gt_lines.new_full((num_pred_lines,), self.num_classes, dtype=torch.long)  # (num_q, )

        label_weights = gt_lines.new_ones(num_pred_lines)  # (num_q, )

        lines_target = gt_lines.new_full(lines_pred.shape, 0, dtype=torch.float)
        lines_weights = gt_lines.new_full(lines_pred.shape, 0, dtype=torch.long)

        attr_num = point_num * self.attr_nums  # 虚实/ 颜色 / 是否栏杆
        attr_target = gt_lines.new_full((lines_pred.shape[0], attr_num), 0, dtype=torch.long)  # (num_q, attr*num_pts)
        attr_weights = gt_lines.new_full((lines_pred.shape[0], attr_num), 0, dtype=torch.long)  # (num_q, attr**num_pts)
        if num_gt > 0 and num_pred_lines > 0:
            if gt_permute_idx is not None:  # using permute invariant label
                # gt_permute_idx: (num_q, num_gt)
                # pos_inds: which query is positive
                # pos_gt_inds: which gt each pos pred is assigned
                # single_matched_gt_permute_idx: which permute order is matched
                single_matched_gt_permute_idx = gt_permute_idx[pos_inds, pos_gt_inds]
                lines_target[pos_inds] = gt_lines[pos_gt_inds, single_matched_gt_permute_idx].type(
                    lines_target.dtype
                )  # (num_q, 3*num_pts)
                attr_target[pos_inds] = gt_attrs[pos_gt_inds, single_matched_gt_permute_idx].type(
                    attr_target.dtype
                )  # (num_q, attr*num_pts)
            else:
                lines_target[pos_inds] = sampling_result.pos_gt_bboxes.type(lines_target.dtype)  # (num_q, 3*num_pts)
                attr_target[pos_inds] = sampling_result.pos_gt_bboxes.type(attr_target.dtype)  # (num_q, attr*num_pts)

            label_weights[pos_inds] = scene_weight[0]
            lines_weights[pos_inds] = scene_weight[0]  # (num_q, 3*num_pts)
            attr_weights[pos_inds] = scene_weight[0]  # (num_q, attr*num_pts)

        # normalization
        # n = lines_weights.sum(-1, keepdim=True) # (num_q, 1)
        # lines_weights = lines_weights / n.masked_fill(n == 0, 1) # (num_q, 2*num_pts)
        # [0, ..., 0] for neg ind and [1/npts, ..., 1/npts] for pos ind
        return (
            valid_mask,
            labels,
            label_weights,
            lines_target,
            lines_weights,
            attr_target,
            attr_weights,
            pos_inds,
            neg_inds,
            pos_gt_inds,
            matched_reg_cost,
        )

    def merge_label_target(self, line_target_info):
        all_scores_list, all_merge_label_list, all_merge_label_weights_list = [], [], []
        for i in range(len(line_target_info["labels_list"])):
            merge_score_list = torch.cat((line_target_info["scores_pred"][i],), dim=0)
            merge_label_list = torch.cat((line_target_info["labels_list"][i],), dim=0)
            merge_label_weights_list = torch.cat((line_target_info["label_weights_list"][i],), dim=0)
            all_scores_list.append(merge_score_list)
            all_merge_label_list.append(merge_label_list)
            all_merge_label_weights_list.append(merge_label_weights_list)

        return all_scores_list, all_merge_label_list, all_merge_label_weights_list

    def loss_reg_onetype(self, pred_lines_list, gt_lines_list, lines_weights_list, num_total_pos, loss_reg):
        pred_lines = torch.cat(pred_lines_list, dim=0)
        gt_lines = torch.cat(gt_lines_list, dim=0)
        line_weights = torch.cat(lines_weights_list, dim=0)
        # if track_info is not None:
        #     line_weights = line_weights * padding_loss_mask[:, None].float()

        assert len(pred_lines) == len(gt_lines)
        assert len(gt_lines) == len(line_weights)

        loss_reg = loss_reg(pred_lines, gt_lines, line_weights, avg_factor=num_total_pos)

        return loss_reg


if __name__ == "__main__":
    assigner = HungarianLinesAssigner()
    cost = MapQueriesCost()
    mm = LinesL1Loss()
    print(assigner, cost, mm)
