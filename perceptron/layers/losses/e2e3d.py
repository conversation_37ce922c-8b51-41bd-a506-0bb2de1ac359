# ------------------------------------------------------------------------
# Copyright (c) Toyota Research Institute
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------

import copy
import torch
from torch import nn
from mmcv.runner import force_fp32
from mmdet.models import LOSSES
from mmdet.models import build_loss
from loguru import logger

from mmdet.core import build_assigner, reduce_mean, multi_apply, build_sampler
from perceptron.layers.head.det3d.bbox.util import normalize_bbox
from perceptron.utils.e2e_utils.utils import normalize, denormalize
from perceptron_ops.iou3d_nms.iou3d_nms_utils import boxes_iou_bev


class TrackingLossBase(nn.Module):
    """Naive multi-frame loss"""

    def __init__(
        self,
        num_classes,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        sync_cls_avg_factor=False,
        sync_pos_avg_factor=True,
        interm_loss=True,
        apply_rfs=False,
        hist_traj_loss=False,
        loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=2.0),
        loss_bbox=dict(type="L1Loss", loss_weight=0.25),
        loss_iou=dict(type="GIoULoss", loss_weight=0.0),
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=[-51.2, -51.2, -5.0, 51.2, 51.2, 3.0],
        ),
        rfs_level=-1,
    ):
        super().__init__()
        self.num_classes = num_classes
        self.interm_loss = interm_loss  # if compute separate losses for all the decoders
        self.apply_rfs = apply_rfs
        self.rfs_level = rfs_level
        self.hist_traj_loss = hist_traj_loss
        self.assigner = build_assigner(assigner)
        self.loss_cls = build_loss(loss_cls)
        self.loss_bbox = build_loss(loss_bbox)
        self.loss_iou = build_loss(loss_iou)
        sampler_cfg = dict(type="PseudoSampler")
        self.sampler = build_sampler(sampler_cfg, context=self)

        self.pc_range = self.assigner.pc_range

        if self.loss_cls.use_sigmoid:
            self.cls_out_channels = num_classes
        else:
            self.cls_out_channels = num_classes + 1

        if code_weights is not None:
            self.code_weights = code_weights
        else:
            self.code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2]

        self.code_weights = nn.Parameter(torch.tensor(self.code_weights, requires_grad=False), requires_grad=False)

        self.bg_cls_weight = 0
        self.sync_cls_avg_factor = sync_cls_avg_factor
        self.sync_pos_avg_factor = sync_pos_avg_factor
        class_weight = loss_cls.get("class_weight", None)
        if class_weight is not None:
            assert isinstance(class_weight, float), (
                "Expected " "class_weight to have type float. Found " f"{type(class_weight)}."
            )
            # NOTE following the official DETR rep0, bg_cls_weight means
            # relative classification weight of the no-object class.
            bg_cls_weight = loss_cls.get("bg_cls_weight", class_weight)
            assert isinstance(bg_cls_weight, float), (
                "Expected " "bg_cls_weight to have type float. Found " f"{type(bg_cls_weight)}."
            )
            class_weight = torch.ones(num_classes + 1) * class_weight
            # set background class as the last indice
            class_weight[num_classes] = bg_cls_weight
            loss_cls.update({"class_weight": class_weight})
            if "bg_cls_weight" in loss_cls:
                loss_cls.pop("bg_cls_weight")
            self.bg_cls_weight = bg_cls_weight

    def _get_target_single(
        self,
        cls_score,
        bbox_pred,
        gt_labels,
        gt_bboxes,
        instance_ids,
        gt_bboxes_ignore=None,
        roi_mask=None,
        query_points=None,
    ):
        """ "Compute regression and classification targets for one image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            cls_score (Tensor): Box score logits from a single decoder layer
                for one image. Shape [num_query, cls_out_channels].
            bbox_pred (Tensor): Sigmoid outputs from a single decoder layer
                for one image, with normalized coordinate (cx, cy, w, h) and
                shape [num_query, 4].
            gt_bboxes (Tensor): Ground truth bboxes for one image with
                shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels (Tensor): Ground truth class indices for one image
                with shape (num_gts, ).
            gt_bboxes_ignore (Tensor, optional): Bounding boxes
                which can be ignored. Default None.
        Returns:
            tuple[Tensor]: a tuple containing the following for one image.
                - labels (Tensor): Labels of each image.
                - label_weights (Tensor]): Label weights of each image.
                - bbox_targets (Tensor): BBox targets of each image.
                - bbox_weights (Tensor): BBox weights of each image.
                - pos_inds (Tensor): Sampled positive indices for each image.
                - neg_inds (Tensor): Sampled negative indices for each image.
        """

        num_bboxes = bbox_pred.size(0)
        # assigner and sampler
        if roi_mask is not None:
            assert query_points is not None
            assign_result = self.assigner.assign(
                bbox_pred,
                cls_score,
                gt_bboxes,
                gt_labels,
                gt_bboxes_ignore,
                query_bboxes=query_points,
                roi_mask=roi_mask,
            )
        else:
            assign_result = self.assigner.assign(bbox_pred, cls_score, gt_bboxes, gt_labels, gt_bboxes_ignore)
        sampling_result = self.sampler.sample(assign_result, bbox_pred, gt_bboxes)
        pos_inds = sampling_result.pos_inds
        neg_inds = sampling_result.neg_inds

        # label targets
        labels = gt_bboxes.new_full((num_bboxes,), self.num_classes, dtype=torch.long)
        label_instance_ids = gt_bboxes.new_full((num_bboxes,), -1, dtype=torch.long)
        gt_match_idxes = gt_bboxes.new_full((num_bboxes,), -1, dtype=torch.long)
        labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds].long()
        label_instance_ids[pos_inds] = instance_ids[sampling_result.pos_assigned_gt_inds]
        gt_match_idxes[pos_inds] = sampling_result.pos_assigned_gt_inds.clone()
        label_weights = gt_bboxes.new_ones(num_bboxes)

        # bbox targets
        code_size = gt_bboxes.size(1)
        bbox_targets = torch.zeros_like(bbox_pred)[..., :code_size]
        bbox_weights = torch.zeros_like(bbox_pred)
        bbox_weights[pos_inds] = 1.0

        # hack for empty
        if pos_inds.numel() == 0:
            sampling_result.pos_gt_bboxes = gt_bboxes.new_empty((0, code_size))
        bbox_targets[pos_inds] = sampling_result.pos_gt_bboxes
        return (
            labels,
            label_instance_ids,
            label_weights,
            bbox_targets,
            bbox_weights,
            pos_inds,
            neg_inds,
            gt_match_idxes,
        )

    def get_targets(
        self,
        cls_scores_list,
        bbox_preds_list,
        gt_bboxes_list,
        gt_labels_list,
        instance_ids_list,
        gt_bboxes_ignore_list=None,
        roi_mask_list=None,
        query_points_list=None,
    ):
        """"Compute regression and classification targets for a batch image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            cls_scores_list (list[Tensor]): Box score logits from a single
                decoder layer for each image with shape [num_query,
                cls_out_channels].
            bbox_preds_list (list[Tensor]): Sigmoid outputs from a single
                decoder layer for each image, with normalized coordinate
                (cx, cy, w, h) and shape [num_query, 4].
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indices for each
                image with shape (num_gts, ).
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            tuple: a tuple containing the following targets.
                - labels_list (list[Tensor]): Labels for all images.
                - label_weights_list (list[Tensor]): Label weights for all \
                    images.
                - bbox_targets_list (list[Tensor]): BBox targets for all \
                    images.
                - bbox_weights_list (list[Tensor]): BBox weights for all \
                    images.
                - num_total_pos (int): Number of positive samples in all \
                    images.
                - num_total_neg (int): Number of negative samples in all \
                    images.
        """
        assert gt_bboxes_ignore_list is None, "Only supports for gt_bboxes_ignore setting to None."
        num_imgs = len(cls_scores_list)
        gt_bboxes_ignore_list = [gt_bboxes_ignore_list for _ in range(num_imgs)]

        if roi_mask_list is None:
            assert query_points_list is None
            roi_mask_list = [roi_mask_list for _ in range(num_imgs)]
            query_points_list = [query_points_list for _ in range(num_imgs)]

        (
            labels_list,
            label_instance_ids_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            pos_inds_list,
            neg_inds_list,
            gt_match_idxes_list,
        ) = multi_apply(
            self._get_target_single,
            cls_scores_list,
            bbox_preds_list,
            gt_labels_list,
            gt_bboxes_list,
            instance_ids_list,
            gt_bboxes_ignore_list,
            roi_mask_list,
            query_points_list,
        )
        num_total_pos = sum((inds.numel() for inds in pos_inds_list))
        num_total_neg = sum((inds.numel() for inds in neg_inds_list))
        return (
            labels_list,
            label_instance_ids_list,
            label_weights_list,
            bbox_targets_list,
            bbox_weights_list,
            num_total_pos,
            num_total_neg,
            gt_match_idxes_list,
        )

    def loss_single_decoder(
        self,
        frame_idx,
        cls_scores,
        bbox_preds,
        gt_bboxes_list,
        gt_labels_list,
        instance_ids_list,
        gt_bboxes_ignore_list=None,
        gt_matching=None,
        aux_infos=None,
    ):
        """ "Loss function for outputs from a single decoder layer of a single
        feature level. The sub-function of frame-level loss.
        Args:
            cls_scores (Tensor): Box score logits from a single decoder layer
                for all images. Shape [bs, num_query, cls_out_channels].
            bbox_preds (Tensor): Sigmoid outputs from a single decoder layer
                for all images, with normalized coordinate (cx, cy, w, h) and
                shape [bs, num_query, 4].
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indices for each
                image with shape (num_gts, ).
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """
        num_imgs = cls_scores.size(0)
        cls_scores_list = [cls_scores[i] for i in range(num_imgs)]
        bbox_preds_list = [bbox_preds[i] for i in range(num_imgs)]
        if gt_matching is None:
            cls_reg_targets = self.get_targets(
                cls_scores_list,
                bbox_preds_list,
                gt_bboxes_list,
                gt_labels_list,
                instance_ids_list,
                gt_bboxes_ignore_list,
            )
            (
                labels_list,
                _,
                label_weights_list,
                bbox_targets_list,
                bbox_weights_list,
                num_total_pos,
                num_total_neg,
                gt_match_idxes_list,
            ) = cls_reg_targets
        else:
            (
                labels_list,
                label_weights_list,
                bbox_targets_list,
                bbox_weights_list,
                num_total_pos,
                num_total_neg,
                gt_match_idxes_list,
            ) = gt_matching
        labels = torch.cat(labels_list, 0)
        label_weights = torch.cat(label_weights_list, 0)
        bbox_targets = torch.cat(bbox_targets_list, 0)
        bbox_weights = torch.cat(bbox_weights_list, 0)

        # classification loss
        cls_scores = cls_scores.reshape(-1, self.cls_out_channels)
        # construct weighted avg_factor to match with the official DETR repo
        cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.bg_cls_weight
        if self.sync_cls_avg_factor:
            cls_avg_factor = reduce_mean(cls_scores.new_tensor([cls_avg_factor]))

        cls_avg_factor = max(cls_avg_factor, 1)
        loss_cls = self.loss_cls(cls_scores, labels, label_weights, avg_factor=cls_avg_factor)

        # Compute the average number of gt boxes accross all gpus, for
        # normalization purposes
        num_total_pos = loss_cls.new_tensor([num_total_pos])
        if self.sync_pos_avg_factor:
            num_total_pos = torch.clamp(reduce_mean(num_total_pos), min=1).item()
        else:
            num_total_pos = torch.clamp(num_total_pos, min=1).item()

        # regression L1 loss
        bbox_preds = bbox_preds.reshape(-1, bbox_preds.size(-1))
        normalized_bbox_targets = normalize_bbox(bbox_targets, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        # bbox_weights = bbox_weights * torch.tensor(self.code_weights).to(bbox_preds.device)
        bbox_weights = bbox_weights * self.code_weights.clone().detach().to(bbox_preds.device)

        loss_bbox = self.loss_bbox(
            bbox_preds[isnotnan, :10],
            normalized_bbox_targets[isnotnan, :10],
            bbox_weights[isnotnan, :10],
            avg_factor=num_total_pos,
        )

        try:
            loss_cls = torch.nan_to_num(loss_cls)
            loss_bbox = torch.nan_to_num(loss_bbox)
        except Exception:
            loss_cls = nan_to_num(loss_cls)
            loss_bbox = nan_to_num(loss_bbox)

        return loss_cls, loss_bbox

    def loss_single_frame(self, frame_idx, gt_bboxes_list, gt_labels_list, instance_ids, preds_dicts, gt_bboxes_ignore):
        """Loss function on a single frame for classification and localization.
        Args:
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indices for each
                image with shape (num_gts, ).
            preds_dicts:
                all_cls_scores (Tensor): Classification score of all
                    decoder layers, has shape
                    [nb_dec, bs, num_query, cls_out_channels].
                all_bbox_preds (Tensor): Sigmoid regression
                    outputs of all decode layers. Each is a 4D-tensor with
                    normalized coordinate format (cx, cy, w, h) and shape
                    [nb_dec, bs, num_query, 4].
                enc_cls_scores (Tensor): Classification scores of
                    points on encode feature map , has shape
                    (N, h*w, num_classes). Only be passed when as_two_stage is
                    True, otherwise is None.
                enc_bbox_preds (Tensor): Regression results of each points
                    on the encode feature map, has shape (N, h*w, 4). Only be
                    passed when as_two_stage is True, otherwise is None.
            gt_bboxes_ignore (list[Tensor], optional): Bounding boxes
                which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """
        assert gt_bboxes_ignore is None, (
            f"{self.__class__.__name__} only supports " f"for gt_bboxes_ignore setting to None."
        )
        all_cls_scores = preds_dicts["all_cls_scores"]
        all_bbox_preds = preds_dicts["all_bbox_preds"]
        # enc_cls_scores = preds_dicts["enc_cls_scores"]
        # enc_bbox_preds = preds_dicts["enc_bbox_preds"]

        num_dec_layers = len(all_cls_scores)
        device = gt_labels_list[0].device
        gt_bboxes_list = [
            torch.cat((gt_bboxes.gravity_center, gt_bboxes.tensor[:, 3:]), dim=1).to(device)
            for gt_bboxes in gt_bboxes_list
        ]

        all_gt_bboxes_list = [gt_bboxes_list for _ in range(num_dec_layers)]
        all_gt_labels_list = [gt_labels_list for _ in range(num_dec_layers)]
        all_instance_ids_list = [instance_ids for _ in range(num_dec_layers)]
        all_gt_bboxes_ignore_list = [gt_bboxes_ignore for _ in range(num_dec_layers)]

        if self.interm_loss:
            losses_cls, losses_bbox = multi_apply(
                self.loss_single_decoder,
                [frame_idx for _ in range(num_dec_layers)],
                all_cls_scores,
                all_bbox_preds,
                all_gt_bboxes_list,
                all_gt_labels_list,
                all_instance_ids_list,
                all_gt_bboxes_ignore_list,
            )
        else:
            losses_cls, losses_bbox = self.loss_single_decoder(
                num_dec_layers - 1,
                all_cls_scores[-1],
                all_bbox_preds[-1],
                all_gt_bboxes_list[-1],
                all_gt_labels_list[-1],
                all_instance_ids_list[-1],
                all_gt_bboxes_ignore_list[-1],
            )
            losses_cls, losses_bbox = [losses_cls], [losses_bbox]

        loss_dict = dict()

        # loss from the last decoder layer
        loss_dict[f"f{frame_idx}.loss_cls"] = losses_cls[-1]
        loss_dict[f"f{frame_idx}.loss_bbox"] = losses_bbox[-1]

        # loss from other decoder layers
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(losses_cls[:-1], losses_bbox[:-1]):
            loss_dict[f"f{frame_idx}.d{num_dec_layer}.loss_cls"] = loss_cls_i
            loss_dict[f"f{frame_idx}.d{num_dec_layer}.loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        return loss_dict

    @force_fp32(apply_to=("preds_dicts"))
    def forward(self, preds_dicts):
        """Loss function for multi-frame tracking"""
        # frame_num = len(preds_dicts)
        losses_dicts = [p.pop("loss_dict") for p in preds_dicts]
        loss_dict = dict()
        for key in losses_dicts[-1].keys():
            # example loss_dict["d2.loss_cls"] = losses_dicts[-1]["f0.d2.loss_cls"]
            loss_dict[key[3:]] = losses_dicts[-1][key]

        for frame_loss in losses_dicts[:-1]:
            loss_dict.update(frame_loss)

        return loss_dict


class TrackingLoss(TrackingLossBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def loss_single_frame(
        self,
        frame_idx,
        gt_bboxes_list,
        gt_labels_list,
        instance_ids,
        preds_dicts,
        gt_bboxes_ignore,
        roi_mask=None,
        occ_save_age=None,
    ):
        """Match according to both tracking and detection information
        Generate the single frame loss function, modify the ids of track instances
        """
        assert gt_bboxes_ignore is None, (
            f"{self.__class__.__name__} only supports " f"for gt_bboxes_ignore setting to None."
        )

        all_cls_scores = preds_dicts["all_cls_scores"]  # [num_dec, bs, num_query, class_num]
        all_bbox_preds = preds_dicts["all_bbox_preds"]  # [num_dec, bs, num_query, 10 ]
        all_query_points = preds_dicts["reference"]
        # enc_cls_scores = preds_dicts["enc_cls_scores"]
        # enc_bbox_preds = preds_dicts["enc_bbox_preds"]
        track_instances = preds_dicts["track_instances"]

        num_dec_layers = len(all_cls_scores)
        device = gt_labels_list[0].device
        # after this operation, [x, y, z-h/2] becomes [x, y, z]
        gt_bboxes_list = [
            torch.cat((gt_bboxes.gravity_center, gt_bboxes.tensor[:, 3:]), dim=1).to(device)
            for gt_bboxes in gt_bboxes_list
        ]
        # obj_idxes: track_id, gt_idx: enumerate index
        obj_idxes_list = instance_ids[0].detach().cpu().numpy().tolist()
        obj_idx_to_gt_idx = {obj_idx: gt_idx for gt_idx, obj_idx in enumerate(obj_idxes_list)}

        num_disappear_track = 0
        # step 1. Inherit and Update the previous tracks
        matched_gt_idxes = track_instances.matched_gt_idxes.cpu()
        disappear_time = track_instances.disappear_time.cpu()
        for trk_idx, obj_id in enumerate(track_instances.obj_idxes.cpu().tolist()):
            if obj_id >= 0:
                if obj_id in obj_idx_to_gt_idx:
                    matched_gt_idxes[trk_idx] = obj_idx_to_gt_idx[obj_id]
                    if gt_labels_list[0][obj_idx_to_gt_idx[obj_id]] != -2:
                        disappear_time[trk_idx] = 0
                    else:
                        disappear_time[trk_idx] += 1

                    # FIXME：强绑定的track query，如果loss过大，丢掉，重新参与匹配
                    if (
                        getattr(self, "kill_bad_track", None)
                        and boxes_iou_bev(
                            all_bbox_preds[-1][0][trk_idx][:7].unsqueeze(0),
                            gt_bboxes_list[0][matched_gt_idxes[trk_idx]][:7].unsqueeze(0),
                        )
                        < 0.01
                    ):
                        track_instances.obj_idxes[trk_idx] = -1
                        disappear_time[trk_idx] += 1
                        matched_gt_idxes[trk_idx] = -2

                else:
                    num_disappear_track += 1
                    matched_gt_idxes[trk_idx] = -2
                    disappear_time[trk_idx] += 1
            else:
                matched_gt_idxes[trk_idx] = -1
        track_instances.matched_gt_idxes.copy_(matched_gt_idxes)
        track_instances.disappear_time.copy_(disappear_time)

        full_track_idxes = torch.arange(len(track_instances), dtype=torch.long, device=all_cls_scores.device)
        # previsouly tracked, which is matched by rule
        all_matched_track_idxes = full_track_idxes[track_instances.obj_idxes >= 0]
        matched_track_idxes = full_track_idxes[track_instances.matched_gt_idxes >= 0]
        pre_matched_obj_idxes = track_instances.obj_idxes[all_matched_track_idxes]
        pre_matched_gt_idxes = track_instances.matched_gt_idxes[matched_track_idxes]

        # step2. select the unmatched slots.
        # note that the FP tracks whose obj_idxes are -2 will not be selected here.
        unmatched_track_idxes = full_track_idxes[track_instances.obj_idxes == -1]

        # step3. select the untracked gt instances (new tracks).
        tgt_indexes = track_instances.matched_gt_idxes.clone()
        tgt_indexes = tgt_indexes[tgt_indexes >= 0]
        tgt_state = torch.zeros(len(gt_bboxes_list[0]), device=all_cls_scores.device)
        tgt_state[tgt_indexes] = 1
        # new tgt indexes
        untracked_tgt_indexes = torch.arange(len(gt_bboxes_list[0]), device=all_cls_scores.device)[tgt_state == 0]

        rfs_level = self.rfs_level if self.rfs_level != -1 else num_dec_layers - 1
        if self.apply_rfs:
            all_unmatched_gt_bboxes_list = [[gt_bboxes_list[0]] for _ in range(rfs_level)]
            all_unmatched_gt_bboxes_list.extend(
                [[gt_bboxes_list[0][untracked_tgt_indexes]] for _ in range(rfs_level, num_dec_layers)]
            )
            all_unmatched_gt_labels_list = [[gt_labels_list[0]] for _ in range(rfs_level)]
            all_unmatched_gt_labels_list.extend(
                [[gt_labels_list[0][untracked_tgt_indexes]] for _ in range(rfs_level, num_dec_layers)]
            )
            all_unmatched_gt_ids_list = [[instance_ids[0]] for _ in range(rfs_level)]
            all_unmatched_gt_ids_list.extend(
                [[instance_ids[0][untracked_tgt_indexes]] for _ in range(rfs_level, num_dec_layers)]
            )
        else:
            all_unmatched_gt_bboxes_list = [[gt_bboxes_list[0][untracked_tgt_indexes]] for _ in range(num_dec_layers)]
            all_unmatched_gt_labels_list = [[gt_labels_list[0][untracked_tgt_indexes]] for _ in range(num_dec_layers)]
            all_unmatched_gt_ids_list = [[instance_ids[0][untracked_tgt_indexes]] for _ in range(num_dec_layers)]
            if roi_mask is not None:
                all_roi_mask_list = [roi_mask for _ in range(num_dec_layers)]
        all_unmatched_ignore_list = [None for _ in range(num_dec_layers)]

        unmatched_cls_scores = all_cls_scores.clone()
        unmatched_bbox_preds = all_bbox_preds.clone()
        unmatched_query_points = all_query_points.clone()

        # step4. do matching between the unmatched slots and GTs.
        all_matching_list = list()
        matched_track_idxes = full_track_idxes[matched_track_idxes]
        unmatched_track_idxes = full_track_idxes[unmatched_track_idxes]
        for dec_layer_idx in range(num_dec_layers):
            if self.apply_rfs and dec_layer_idx < rfs_level:
                unmatched_track_dec_matching_result = self.get_targets(
                    unmatched_cls_scores[dec_layer_idx],
                    unmatched_bbox_preds[dec_layer_idx],
                    all_unmatched_gt_bboxes_list[dec_layer_idx],
                    all_unmatched_gt_labels_list[dec_layer_idx],
                    all_unmatched_gt_ids_list[dec_layer_idx],
                    all_unmatched_ignore_list[dec_layer_idx],
                )

                (
                    labels_list,
                    label_instance_ids_list,
                    label_weights_list,
                    bbox_targets_list,
                    bbox_weights_list,
                    num_total_pos,
                    num_total_neg,
                    gt_match_idxes_list,
                ) = unmatched_track_dec_matching_result

                track_instances.obj_idxes = label_instance_ids_list[0]
                track_instances.matched_gt_idxes = gt_match_idxes_list[0]

                matched_labels = gt_labels_list[0].long()
                # matched_label_weights = gt_labels_list[0].new_ones(len(matched_labels)).float()
                matched_bbox_targets = gt_bboxes_list[0]
                # matched_bbox_weights = torch.ones_like(track_instances.bboxes)

                (
                    dec_labels,
                    _,
                    dec_label_weights,
                    dec_bbox_targets,
                    dec_bbox_weights,
                    dec_num_total_pos,
                    dec_num_total_neg,
                    _,
                ) = unmatched_track_dec_matching_result

                labels_list = torch.ones_like(track_instances.obj_idxes).long() * self.num_classes
                labels_list = dec_labels[0]
                labels_list = [labels_list]

                label_weights_list = torch.ones_like(track_instances.obj_idxes).float()
                if roi_mask is not None:
                    non_used = (labels_list[0] == -1) | (labels_list[0] == -2)  # 考虑遮挡 短track

                    track_instances.matched_gt_idxes[labels_list[0] == -1] = -2
                    track_instances.matched_gt_idxes[labels_list[0] == -2] = -3  # -3(遮挡) 可以继续传递

                    label_weights_list[non_used] = 0.0  # 同时处理遮挡 and 蒙版
                    label_weights_list = [label_weights_list]
                    if occ_save_age:
                        assert occ_save_age > 0
                        label_weights_list[0][labels_list[0] == -2] = (
                            track_instances.disappear_time[labels_list[0] == -2] + 1
                        ) / occ_save_age
                        labels_list[0][labels_list[0] == -2] = self.num_classes
                else:
                    label_weights_list = [label_weights_list]

                bbox_targets_list = torch.zeros_like(track_instances.bboxes)[:, : dec_bbox_targets[0].size(1)]
                bbox_targets_list = dec_bbox_targets[0]
                bbox_targets_list = [bbox_targets_list]

                bbox_weights_list = torch.zeros_like(track_instances.bboxes)
                bbox_weights_list = dec_bbox_weights[0]
                bbox_weights_list = [bbox_weights_list]

                total_pos = dec_num_total_pos
                total_neg = dec_num_total_neg

                matched_gt_idxes_list = track_instances.obj_idxes.new_full(
                    (len(track_instances),), -1, dtype=torch.long
                )
                matched_gt_idxes_list = track_instances.matched_gt_idxes
            else:
                if roi_mask is not None:
                    unmatched_track_dec_matching_result = self.get_targets(
                        unmatched_cls_scores[dec_layer_idx, :, unmatched_track_idxes, :],
                        unmatched_bbox_preds[dec_layer_idx, :, unmatched_track_idxes, :],
                        all_unmatched_gt_bboxes_list[dec_layer_idx],
                        all_unmatched_gt_labels_list[dec_layer_idx],
                        all_unmatched_gt_ids_list[dec_layer_idx],
                        all_unmatched_ignore_list[dec_layer_idx],
                        all_roi_mask_list[dec_layer_idx],
                        unmatched_query_points[dec_layer_idx, :, unmatched_track_idxes, :],
                    )
                else:
                    unmatched_track_dec_matching_result = self.get_targets(
                        unmatched_cls_scores[dec_layer_idx, :, unmatched_track_idxes, :],
                        unmatched_bbox_preds[dec_layer_idx, :, unmatched_track_idxes, :],
                        all_unmatched_gt_bboxes_list[dec_layer_idx],
                        all_unmatched_gt_labels_list[dec_layer_idx],
                        all_unmatched_gt_ids_list[dec_layer_idx],
                        all_unmatched_ignore_list[dec_layer_idx],
                    )

                (
                    labels_list,
                    label_instance_ids_list,
                    label_weights_list,
                    bbox_targets_list,
                    bbox_weights_list,
                    num_total_pos,
                    num_total_neg,
                    gt_match_idxes_list,
                ) = unmatched_track_dec_matching_result

                # step5. update the obj_idxes according to the matching result with the last decoder layer
                track_instances.obj_idxes[all_matched_track_idxes] = pre_matched_obj_idxes
                track_instances.obj_idxes[unmatched_track_idxes] = label_instance_ids_list[0]
                track_instances.matched_gt_idxes[matched_track_idxes] = pre_matched_gt_idxes
                track_instances.matched_gt_idxes[unmatched_track_idxes] = gt_match_idxes_list[0]

                # step6. merge the matching results of tracking/query instances
                matched_labels = gt_labels_list[0][tgt_indexes].long()
                # matched_label_weights = gt_labels_list[0].new_ones(len(tgt_indexes)).float()
                matched_bbox_targets = gt_bboxes_list[0][tgt_indexes]
                # matched_bbox_weights = torch.ones_like(track_instances.bboxes)[: len(tgt_indexes)]

                (
                    dec_labels,
                    _,
                    dec_label_weights,
                    dec_bbox_targets,
                    dec_bbox_weights,
                    dec_num_total_pos,
                    dec_num_total_neg,
                    _,
                ) = unmatched_track_dec_matching_result

                labels_list = torch.ones_like(track_instances.obj_idxes).long() * self.num_classes
                labels_list[matched_track_idxes] = matched_labels
                labels_list[unmatched_track_idxes] = dec_labels[0]
                labels_list = [labels_list]

                label_weights_list = torch.ones_like(track_instances.obj_idxes).float()

                # FIXME: det query匹配的正样本权重增加！
                if getattr(self, "det_query_weight", None):
                    det_label_weigths = torch.ones_like(dec_labels[0]).float()
                    det_pos_index = dec_labels[0] < self.num_classes  # positive det query
                    det_label_weigths[det_pos_index] = getattr(self, "det_query_weight", None)
                    label_weights_list[unmatched_track_idxes] = det_label_weigths

                # FIXME: 应该被kill掉的case，weight增加
                if getattr(self, "killed_track_weight", None):
                    label_weights_list[track_instances.matched_gt_idxes == -2] = getattr(self, "killed_track_weight", 1)

                if roi_mask is not None:
                    non_used = (labels_list[0] == -1) | (labels_list[0] == -2)  # 考虑遮挡 短track

                    track_instances.matched_gt_idxes[labels_list[0] == -1] = -2
                    track_instances.matched_gt_idxes[labels_list[0] == -2] = -3  # -3(遮挡) 可以继续传递

                    label_weights_list[non_used] = 0.0  # 同时处理遮挡 and 蒙版
                    label_weights_list = [label_weights_list]
                    if occ_save_age:
                        assert occ_save_age > 0
                        label_weights_list[0][labels_list[0] == -2] = (
                            track_instances.disappear_time[labels_list[0] == -2] + 1
                        ) / occ_save_age
                        labels_list[0][labels_list[0] == -2] = self.num_classes
                else:
                    label_weights_list = [label_weights_list]

                bbox_targets_list = torch.zeros_like(track_instances.bboxes)[:, : dec_bbox_targets[0].size(1)]
                bbox_targets_list[matched_track_idxes] = matched_bbox_targets
                bbox_targets_list[unmatched_track_idxes] = dec_bbox_targets[0]
                bbox_targets_list = [bbox_targets_list]

                bbox_weights_list = torch.zeros_like(track_instances.bboxes)
                bbox_weights_list[matched_track_idxes] = 1.0
                bbox_weights_list[unmatched_track_idxes] = dec_bbox_weights[0]

                if roi_mask is not None:
                    bbox_weights_list[non_used] = 0.0  # 同时处理遮挡 and 蒙版
                    if occ_save_age:
                        assert occ_save_age > 0
                        bbox_weights_list[labels_list[0] == -2] = 1
                bbox_weights_list = [bbox_weights_list]

                total_pos = dec_num_total_pos + len(matched_track_idxes)
                total_neg = dec_num_total_neg + num_disappear_track

                matched_gt_idxes_list = track_instances.obj_idxes.new_full(
                    (len(track_instances),), -1, dtype=torch.long
                )
                matched_gt_idxes_list[matched_track_idxes] = track_instances.matched_gt_idxes[matched_track_idxes]
                matched_gt_idxes_list[unmatched_track_idxes] = track_instances.matched_gt_idxes[unmatched_track_idxes]

            dec_matching_results = (
                labels_list,
                label_weights_list,
                bbox_targets_list,
                bbox_weights_list,
                total_pos,
                total_neg,
                matched_gt_idxes_list,
            )
            all_matching_list.append(dec_matching_results)

        # step 7. compute the single frame losses
        # after getting the matching result, we no longer need contents for gt_bboxes_list etc.
        if self.interm_loss:
            losses_cls, losses_bbox = multi_apply(
                self.loss_single_decoder,
                [frame_idx for _ in range(num_dec_layers)],
                all_cls_scores,
                all_bbox_preds,
                [None for _ in range(num_dec_layers)],
                [None for _ in range(num_dec_layers)],
                [None for _ in range(num_dec_layers)],
                [None for _ in range(num_dec_layers)],
                all_matching_list,
            )
        else:
            losses_cls, losses_bbox = self.loss_single_decoder(
                frame_idx, all_cls_scores[-1], all_bbox_preds[-1], None, None, None, None, all_matching_list[-1]
            )
            losses_cls, losses_bbox = [losses_cls], [losses_bbox]

        loss_dict = dict()

        # loss from the last decoder layer
        loss_dict[f"f{frame_idx}.loss_cls"] = losses_cls[-1]
        loss_dict[f"f{frame_idx}.loss_bbox"] = losses_bbox[-1]

        # loss from other decoder layers
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(losses_cls[:-1], losses_bbox[:-1]):
            loss_dict[f"f{frame_idx}.d{num_dec_layer}.loss_cls"] = loss_cls_i
            loss_dict[f"f{frame_idx}.d{num_dec_layer}.loss_bbox"] = loss_bbox_i
            num_dec_layer += 1

        if roi_mask is not None:
            return loss_dict, non_used
        else:
            return loss_dict


@LOSSES.register_module()
class TrackingLossCombo(TrackingLoss):
    """Tracking loss with reference point supervision"""

    def __init__(
        self,
        num_classes,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        sync_cls_avg_factor=False,
        sync_pos_avg_factor=True,
        interm_loss=True,
        apply_rfs=False,
        hist_traj_loss=False,
        loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=2.0),
        loss_bbox=dict(type="L1Loss", loss_weight=0.25),
        loss_iou=dict(type="GIoULoss", loss_weight=0.0),
        loss_prediction=dict(type="L1Loss", loss_weight=1.0),
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=[-51.2, -51.2, -5.0, 51.2, 51.2, 3.0],
        ),
        rfs_level=-1,
        loss_prediction_type="all",
        hard_k=4,
        contrastive_loss_weight=1.0,
        contrastive_mask_fix=False,
        loss_occlusion=False,
        loss_self_angle=False,
        killed_track_weight=1.0,
        kill_bad_track=False,
        det_query_weight=1,
    ):
        super(TrackingLoss, self).__init__(
            num_classes,
            code_weights,
            sync_cls_avg_factor,
            sync_pos_avg_factor,
            interm_loss,
            apply_rfs,
            hist_traj_loss,
            loss_cls,
            loss_bbox,
            loss_iou,
            assigner,
            rfs_level,
        )
        self.loss_traj = build_loss(loss_prediction)
        self.loss_mem_cls = build_loss(loss_cls)
        # self.loc_refine_code_weights = [1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        self.loc_refine_code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2]
        self.loss_type = loss_prediction_type
        self.temperature = 0.07
        self.hardmin = True
        self.hard_k = hard_k
        self.contrastive_loss_weight = contrastive_loss_weight
        self.contrastive_mask_fix = contrastive_mask_fix
        self.loss_occlusion = loss_occlusion
        self.loss_self_angle = loss_self_angle
        self.killed_track_weight = killed_track_weight
        self.kill_bad_track = kill_bad_track
        self.det_query_weight = det_query_weight

    def loss_prediction(
        self,
        frame_idx,
        loss_dict,
        active_track_instances,
        gt_trajs,
        gt_traj_masks,
        instance_inds,
        fut_len,
        loss_key="for",
        gt_vel=None,
        label_weight=None,
    ):
        obj_idx_to_gt_idx = {
            obj_idx: gt_idx for gt_idx, obj_idx in enumerate(instance_inds[0].detach().cpu().numpy().tolist())
        }

        if loss_key == "fut":
            active_gt_trajs = torch.ones_like(active_track_instances.fut_predictions)
        elif loss_key == "traj":
            active_gt_trajs = torch.ones_like(active_track_instances.traj_predictions)
        else:
            AssertionError, "loss key error!"
        active_gt_trajs[..., -1] = 0.0
        if gt_vel is not None:
            active_gt_vel = torch.ones_like(active_gt_trajs)
        active_gt_traj_masks = torch.zeros_like(active_gt_trajs)[..., 0]
        range_1 = torch.arange(1, fut_len + 1, device=gt_trajs.device)
        range_2 = torch.arange(0, fut_len, device=gt_trajs.device)
        for track_idx, cpu_id in enumerate(active_track_instances.obj_idxes.cpu().tolist()):
            if cpu_id not in obj_idx_to_gt_idx.keys():
                continue
            if (
                label_weight is not None
                and label_weight[track_idx]
                and (active_track_instances.matched_gt_idxes[track_idx] == -2)
            ):
                continue
            index = obj_idx_to_gt_idx[cpu_id]
            traj = gt_trajs[index : index + 1, : fut_len + 1, :]
            gt_motion = traj[:, range_1] - traj[:, range_2]
            active_gt_trajs[track_idx : track_idx + 1] = gt_motion
            active_gt_traj_masks[track_idx : track_idx + 1] = (
                gt_traj_masks[index : index + 1, 1 : fut_len + 1] * gt_traj_masks[index : index + 1, :fut_len]
            )
            if gt_vel is not None:
                active_gt_vel[track_idx : track_idx + 1] = gt_vel[index : index + 1, :fut_len, :]

        gt_trajs, gt_masks = active_gt_trajs[..., :2], active_gt_traj_masks
        if gt_vel is not None:
            gt_vel = active_gt_vel[..., :2]

        if loss_key == "fut":
            pred_trajs = active_track_instances.cache_fut_predictions[..., :2]
            if gt_vel is not None:
                pred_vol = active_track_instances.cache_fut_velocity[..., :2]
        elif loss_key == "traj":
            pred_trajs = active_track_instances.traj_predictions[..., :2]
        if self.loss_type == "valid":
            diff = torch.abs(
                gt_trajs[..., :2] * gt_masks.unsqueeze(-1) - pred_trajs[..., :2] * gt_masks.unsqueeze(-1)
            )  # [n, fut_len ,2]
            loss_prediction = diff.sum((1, 2)) / gt_masks.sum(1).clip(1.0)
            loss_prediction = loss_prediction.mean() * self.loss_traj.loss_weight

            if gt_vel is not None:
                diff = torch.abs(
                    gt_vel[..., :2] * gt_masks.unsqueeze(-1) - pred_vol[..., :2] * gt_masks.unsqueeze(-1)
                )  # [n, fut_len ,2]
                loss_velocity = diff.sum((1, 2)) / gt_masks.sum(1).clip(1.0)
                loss_velocity = loss_velocity.mean() * self.loss_traj.loss_weight * 0.1
        else:
            loss_prediction = self.loss_traj(
                pred_trajs[..., :2] * gt_masks.unsqueeze(-1),
                gt_trajs[..., :2] * gt_masks.unsqueeze(-1),  # former one is preds
            )
            if gt_vel is not None:
                loss_velocity = self.loss_traj(
                    pred_vol[..., :2] * gt_masks.unsqueeze(-1), gt_vel[..., :2] * gt_masks.unsqueeze(-1)
                )
        loss_dict[f"f{frame_idx}.loss_{loss_key}"] = loss_prediction

        if self.hist_traj_loss:
            hist_loss = self.loss_pred_hist(active_track_instances)
            loss_dict[f"f{frame_idx}.loss_hist"] = hist_loss

        if gt_vel is not None:
            loss_dict[f"f{frame_idx}.loss_velocity"] = loss_velocity

        return loss_dict

    def loss_pred_hist(self, track_instances):
        hist_xyz = track_instances.hist_xyz
        pred_hist_xyz = track_instances.hist_xyz_dec
        cache_bbox = track_instances.cache_bboxes.clone()
        # current_xyz = track_instances.cache_reference_points.clone()
        current_xyz = normalize(cache_bbox[..., [0, 1, 4]], self.pc_range)
        temp_xyz = torch.cat([hist_xyz[:, 1:, :], current_xyz.unsqueeze(1)], dim=1)

        padding_mask = track_instances.hist_padding_masks == 0
        gt_hist_traj = denormalize(temp_xyz - hist_xyz, self.pc_range)

        loss_0 = torch.linalg.norm(pred_hist_xyz - gt_hist_traj, axis=-1, ord=1) * padding_mask
        loss_1 = loss_0.sum(dim=-1) / padding_mask.sum(dim=-1)
        loss = loss_1.mean()

        return loss

    def loss_mem_bank(
        self,
        frame_idx,
        loss_dict,
        gt_bboxes_list,
        gt_labels_list,
        instance_ids,
        track_instances,
        mask=None,
        occ_save_age=0,
    ):
        obj_idxes_list = instance_ids[0].detach().cpu().numpy().tolist()
        obj_idx_to_gt_idx = {obj_idx: gt_idx for gt_idx, obj_idx in enumerate(obj_idxes_list)}
        device = track_instances.query_feats.device

        # classification loss
        matched_labels = torch.ones((len(track_instances),), dtype=torch.long, device=device) * self.num_classes
        matched_label_weights = torch.ones((len(track_instances),), dtype=torch.float32, device=device)
        num_pos, num_neg = 0, 0
        for track_idx, cpu_id in enumerate(track_instances.obj_idxes.cpu().tolist()):
            if cpu_id not in obj_idx_to_gt_idx.keys():
                num_neg += 1
                continue
            index = obj_idx_to_gt_idx[cpu_id]
            matched_labels[track_idx] = gt_labels_list[0][index].long()
            num_pos += 1

        labels_list = copy.deepcopy(matched_labels)
        label_weights_list = matched_label_weights
        if mask is not None:
            # kill disappear instance
            if getattr(self, "killed_track_weight", None):
                label_weights_list[track_instances.matched_gt_idxes == -2] = getattr(self, "killed_track_weight", 1)

            label_weights_list[mask] = 0
            if occ_save_age > 0:
                label_weights_list[labels_list == -2] = (
                    track_instances.disappear_time[labels_list == -2] + 1
                ) / occ_save_age
                labels_list[labels_list == -2] = self.num_classes

        cls_scores = track_instances.cache_logits

        cls_avg_factor = num_pos * 1.0 + num_neg * self.bg_cls_weight
        if self.sync_cls_avg_factor:
            cls_avg_factor = reduce_mean(cls_scores.new_tensor([cls_avg_factor]))

        cls_avg_factor = max(cls_avg_factor, 1)
        loss_cls = self.loss_mem_cls(cls_scores, labels_list, label_weights_list, avg_factor=cls_avg_factor)
        loss_cls = torch.nan_to_num(loss_cls)

        loss_dict[f"f{frame_idx}.loss_mem_cls"] = loss_cls

        # location refinement loss
        gt_bboxes_list = [
            torch.cat((gt_bboxes.gravity_center, gt_bboxes.tensor[:, 3:]), dim=1).to(device)
            for gt_bboxes in gt_bboxes_list
        ]

        pos_bbox_num = 0
        matched_bbox_targets = torch.zeros(
            (len(track_instances), gt_bboxes_list[0].shape[1]), dtype=torch.float32, device=device
        )
        matched_bbox_weights = torch.zeros(
            (len(track_instances), len(self.loc_refine_code_weights)), dtype=torch.float32, device=device
        )
        for track_idx, cpu_id in enumerate(track_instances.obj_idxes.cpu().tolist()):
            if cpu_id not in obj_idx_to_gt_idx.keys():
                matched_bbox_weights[track_idx] = 0.0
                continue
            index = obj_idx_to_gt_idx[cpu_id]
            matched_bbox_targets[track_idx] = gt_bboxes_list[0][index].float()
            matched_bbox_weights[track_idx] = 1.0
            pos_bbox_num += 1

        normalized_bbox_targets = normalize_bbox(matched_bbox_targets, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        isnotnan &= torch.isfinite(track_instances.cache_bboxes).all(dim=-1)

        bbox_weights = matched_bbox_weights * torch.tensor(self.loc_refine_code_weights, device=device)
        if mask is not None:
            bbox_weights[mask] = 0
            if occ_save_age > 0 and (labels_list == -2).shape[0] > 0:
                bbox_weights[labels_list == -2] = 1

        loss_bbox = self.loss_bbox(
            track_instances.cache_bboxes[isnotnan, :10],
            normalized_bbox_targets[isnotnan, :10],
            bbox_weights[isnotnan, :10],
            avg_factor=pos_bbox_num,
        )
        loss_dict[f"f{frame_idx}.loss_mem_bbox"] = loss_bbox

        if self.loss_occlusion:
            occlusion_gt = torch.zeros_like(track_instances.disappear_time)
            occlusion_gt[matched_labels == -2] = 1  # 遮挡目标gt设为1
            occlusion_score = track_instances.occlusion
            loss_occ_cls = self.loss_mem_cls(
                occlusion_score, occlusion_gt, label_weights_list, avg_factor=cls_avg_factor
            )
            loss_dict[f"f{frame_idx}.loss_mem_occlusion"] = loss_occ_cls

        if self.loss_self_angle:
            # max_logit = torch.argmax(
            #     track_instances.logits[
            #         :,
            #     ],
            #     dim=-1,
            # )
            # valid = (track_instances.hist_padding_masks[:, -2] == 0) &  (max_logit < 4) &  (max_logit > 0)
            valid = track_instances.hist_padding_masks[:, -2] == 0
            loss_bbox = self.loss_bbox(
                track_instances.cache_bboxes[valid, 6:8],
                track_instances.bboxes[valid, 6:8],
                bbox_weights[valid, 6:8],
                avg_factor=pos_bbox_num,
            )
            loss_dict[f"f{frame_idx}.loss_mem_self_angle"] = loss_bbox

        return loss_dict

    @force_fp32(apply_to=("preds_dicts"))
    def forward(self, preds_dicts):
        """Loss function for multi-frame tracking"""
        # frame_num = len(preds_dicts)
        losses_dicts = [p.pop("loss_dict") for p in preds_dicts if "loss_dict" in list(p.keys())]
        loss_dict = dict()

        for key in losses_dicts[-1].keys():
            # example loss_dict["d2.loss_cls"] = losses_dicts[-1]["f0.d2.loss_cls"]
            loss_dict[key[3:]] = losses_dicts[-1][key]

        for frame_loss in losses_dicts[:-1]:
            loss_dict.update(frame_loss)

        for key, loss in loss_dict.items():
            if torch.isnan(loss):
                logger.warning(f"{key} is nan ,set to zero here !!!")
                # loss_dict[key] = torch.zeros_like(loss)
                loss_dict[key] = torch.nan_to_num(loss_dict[key])  # * 0

        return loss_dict

    def loss_contrastive_loss(
        self,
        frame_idx,
        loss_dict,
        x,
        labels,
        pos_feat,
        pos_labels,
        neg_feat,
        neg_labels,
        hist_padding_masks,
        loss_weight=1.0,
    ):
        """
        Args:
            x: feature matrix with shape (batch_size, feat_dim).
            labels: ground truth labels with shape (batch_size).
        """

        n = x.size(0)

        x = torch.nn.functional.normalize(x, p=2, dim=-1)
        pos_feat = torch.nn.functional.normalize(pos_feat, p=2, dim=-1)
        neg_feat = torch.nn.functional.normalize(neg_feat, p=2, dim=-1)

        kv_feat = torch.cat([pos_feat, neg_feat], dim=0)
        kv_labels = torch.cat([pos_labels, neg_labels], dim=-1)
        m = kv_feat.size(0)

        mask = labels.unsqueeze(1) == kv_labels.unsqueeze(0)
        # similarity = torch.matmul(x_1, x_2.t()) - (0.2 * mask.float())
        similarity = torch.matmul(x, kv_feat.t())
        anchor_dot_contrast = torch.div(similarity, self.temperature)

        # for numerical stability
        logits_max, _ = torch.max(anchor_dot_contrast, dim=1, keepdim=True)
        logits = anchor_dot_contrast - logits_max.detach()

        # compute log_prob
        # print(logits.shape, mask.shape)
        exp_logits = torch.exp(logits) * (1 - mask.float())
        if self.hardmin:
            # k hardest sample
            sort_logits, indices1 = torch.sort(exp_logits, descending=True)
            sort_indices, indices2 = torch.sort(indices1)
            exp_logits_k = torch.Tensor(n, self.hard_k).to(exp_logits.device)
            for i in range(n):
                exp_logits_k[i] = exp_logits[i][indices2[i] < self.hard_k]
            ################################
            # log_prob = logits - torch.log(exp_logits_k.sum(1).unsqueeze(1).expand(n, n) - ((mask.float() - torch.eye(n).cuda()) * exp_logits).sum(1).unsqueeze(1).expand(n, n))
            # log_prob = logits - torch.log(exp_logits_k.sum(1).unsqueeze(1).expand(n, n) + exp_logits - (mask.float() * exp_logits).sum(1).unsqueeze(1).expand(n, n)).detach()
            # log_prob = logits - torch.log(exp_logits_k.sum(1).unsqueeze(1).expand(n, n) - (mask.float() * exp_logits).sum(1).unsqueeze(1).expand(n, n))
            log_prob = logits - torch.log(exp_logits_k.sum(1).unsqueeze(1).expand(n, m))
        else:
            # log_prob = logits - torch.log(exp_logits.sum(1).unsqueeze(1).expand(n, n) + exp_logits - (mask.float() * exp_logits).sum(1).unsqueeze(1).expand(n, n)).detach()
            # log_prob = logits - torch.log(exp_logits.sum(1).unsqueeze(1).expand(n, n) - (mask.float() * exp_logits).sum(1).unsqueeze(1).expand(n, n))
            log_prob = logits - torch.log(exp_logits.sum(1).unsqueeze(1).expand(n, m))

        # log_prob = - (mask.float() * log_prob).sum(1) / mask.sum(1)
        # loss1 = log_prob.view(n).mean()
        log_prob, _ = (mask.float() * log_prob).min(1)
        if not self.contrastive_mask_fix:
            loss = (-log_prob.view(n) * hist_padding_masks).mean()
        else:
            loss = (-log_prob.view(n) * ~hist_padding_masks).mean()

        loss_dict[f"f{frame_idx}.loss_content_contrast"] = loss * self.contrastive_loss_weight
        # if torch.isnan(loss).any():

        return loss_dict


def nan_to_num(x, nan=0.0, posinf=None, neginf=None):
    x[torch.isnan(x)] = nan
    if posinf is not None:
        x[torch.isposinf(x)] = posinf
    if neginf is not None:
        x[torch.isneginf(x)] = posinf
    return x
