import torch.nn as nn


class ConvBNReLU(nn.Module):
    def __init__(
        self, input_channel, output_channel, kernel_size=3, stride=1, padding=1, activation="ReLU", *args, **kwargs
    ):
        super(ConvBNReLU, self).__init__()
        self.conv = nn.Conv2d(
            input_channel,
            output_channel,
            kernel_size=kernel_size,
            stride=stride,
            padding=padding,
            bias=False,
        )
        self.bn = nn.BatchNorm2d(output_channel)
        self.activation = activation
        self.relu = nn.ReLU(inplace=True)
        self.init_weight()

    def forward(self, x):
        x = self.conv(x)
        x = self.bn(x)
        if self.activation == "ReLU":
            x = self.relu(x)
        return x

    def init_weight(self):
        for ly in self.children():
            if isinstance(ly, nn.Conv2d):
                nn.init.kaiming_normal_(ly.weight, a=1)
                if ly.bias is not None:
                    nn.init.constant_(ly.bias, 0)
