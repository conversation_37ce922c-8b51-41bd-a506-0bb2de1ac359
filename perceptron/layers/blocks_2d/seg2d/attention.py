import torch
import torch.nn as nn
import torch.nn.functional as F
from .conv_bn_relu import ConvBNReLU


class AttentionRefinementModule(nn.Module):
    def __init__(self, input_channel, output_channel, *args, **kwargs):
        super(AttentionRefinementModule, self).__init__()
        self.conv = ConvBNReLU(input_channel, output_channel, kernel_size=3, stride=1, padding=1)
        self.conv_atten = nn.Conv2d(output_channel, output_channel, kernel_size=1, bias=False)
        self.bn_atten = nn.BatchNorm2d(output_channel)
        self.sigmoid_atten = nn.Sigmoid()
        self.init_weight()

    def forward(self, x):
        feat = self.conv(x)
        atten = F.avg_pool2d(feat, feat.size()[2:])
        atten = self.conv_atten(atten)
        atten = self.bn_atten(atten)
        atten = self.sigmoid_atten(atten)
        out = torch.mul(feat, atten)
        return out

    def init_weight(self):
        for ly in self.children():
            if isinstance(ly, nn.Conv2d):
                nn.init.kaiming_normal_(ly.weight, a=1)
                if ly.bias is not None:
                    nn.init.constant_(ly.bias, 0)
