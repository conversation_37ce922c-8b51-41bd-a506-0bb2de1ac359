import math
import torch
import torch.nn as nn
from .conv_bn_relu import ConvBNReLU


class AddBottleneck(nn.Module):
    def __init__(self, input_channel, output_channel, block_num=3, stride=1):
        super(Add<PERSON><PERSON><PERSON><PERSON>, self).__init__()
        assert block_num > 1, print("block number should be larger than 1.")
        assert output_channel % 2 ** (block_num - 1) == 0, print(
            "output_channel should be some times the size of 2**(block_num - 1)"
        )
        self.conv_list = nn.ModuleList()
        self.stride = stride
        self.skip = nn.Sequential(
            nn.Conv2d(
                input_channel,
                input_channel,
                kernel_size=3,
                stride=stride,
                padding=1,
                groups=input_channel,
                bias=False,
            ),
            nn.BatchNorm2d(input_channel),
            nn.Conv2d(input_channel, output_channel, kernel_size=1, stride=1, padding=0, bias=False),
            nn.BatchNorm2d(output_channel),
        )
        self.downsample = nn.Sequential(
            nn.Conv2d(
                output_channel // 2,
                output_channel // 2,
                kernel_size=3,
                stride=2,
                padding=1,
                groups=output_channel // 2,
                bias=False,
            ),
            nn.BatchNorm2d(output_channel // 2),
        )
        for idx in range(block_num):
            if idx == 0:
                self.conv_list.append(ConvBNReLU(input_channel, output_channel // 2))
            elif idx == 1 and block_num == 2:
                self.conv_list.append(ConvBNReLU(output_channel // 2, output_channel // 2))
            elif idx == 1 and block_num > 2:
                self.conv_list.append(ConvBNReLU(output_channel // 2, output_channel // 4))
            elif idx < block_num - 1:
                self.conv_list.append(
                    ConvBNReLU(
                        output_channel // int(math.pow(2, idx)),
                        output_channel // int(math.pow(2, idx + 1)),
                    )
                )
            else:
                self.conv_list.append(
                    ConvBNReLU(
                        output_channel // int(math.pow(2, idx)),
                        output_channel // int(math.pow(2, idx)),
                    )
                )

    def forward(self, x):
        out_list = []
        out = x
        for idx, conv in enumerate(self.conv_list):
            if idx == 0 and self.stride == 2:
                out = self.downsample(conv(out))
            else:
                out = conv(out)
            out_list.append(out)
        x = self.skip(x)
        return torch.cat(out_list, dim=1) + x


class CatBottleneck(nn.Module):
    def __init__(self, input_channel, output_channel, block_num=3, stride=1):
        super(CatBottleneck, self).__init__()
        assert block_num > 1, print("block number should be larger than 1.")
        assert output_channel % 2 ** block_num == 0, print(
            "output_channel should be some times the size of 2**block_num"
        )
        self.conv_list = nn.ModuleList()
        self.stride = stride
        self.skip = nn.Sequential(
            nn.Conv2d(
                input_channel,
                input_channel,
                kernel_size=3,
                stride=stride,
                padding=1,
                groups=input_channel,
                bias=False,
            ),
            nn.BatchNorm2d(input_channel),
            nn.Conv2d(input_channel, output_channel // 2, kernel_size=1, stride=1, padding=0, bias=False),
            nn.BatchNorm2d(output_channel // 2),
        )
        self.downsample = nn.Sequential(
            nn.Conv2d(
                output_channel // 4,
                output_channel // 4,
                kernel_size=3,
                stride=2,
                padding=1,
                groups=output_channel // 4,
                bias=False,
            ),
            nn.BatchNorm2d(output_channel // 4),
        )
        for idx in range(block_num):
            if idx == 0:
                self.conv_list.append(ConvBNReLU(input_channel, output_channel // 4))
            elif idx == 1 and block_num == 2:
                self.conv_list.append(ConvBNReLU(output_channel // 4, output_channel // 4))
            elif idx == 1 and block_num > 2:
                self.conv_list.append(ConvBNReLU(output_channel // 4, output_channel // 8))
            elif idx < block_num - 1:
                self.conv_list.append(
                    ConvBNReLU(
                        output_channel // int(math.pow(2, idx + 1)),
                        output_channel // int(math.pow(2, idx + 2)),
                    )
                )
            else:
                self.conv_list.append(
                    ConvBNReLU(
                        output_channel // int(math.pow(2, idx + 1)),
                        output_channel // int(math.pow(2, idx + 1)),
                    )
                )

    def forward(self, x):
        out_list = []
        out = x
        for idx, conv in enumerate(self.conv_list):
            if idx == 0 and self.stride == 2:
                out = self.downsample(conv(out))
            else:
                out = conv(out)
            out_list.append(out)
        x = self.skip(x)
        out_list.append(x)
        return torch.cat(out_list, dim=1)
