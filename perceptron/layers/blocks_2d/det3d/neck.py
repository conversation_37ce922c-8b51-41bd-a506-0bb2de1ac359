import functools
import torch
import torch.nn as nn
from torch.nn import functional as F
import torch.utils.checkpoint as cp
from .sc_conv import SCBottleneck


class BEVFPN(nn.Module):
    def __init__(
        self,
        layer_nums=[5, 5, 5],
        layer_strides=[1, 2, 2],
        num_filters=[128, 256, 512],
        upsample_strides=[2, 2],
        num_upsample_filters=[256, 256],
        input_channels=256,
        use_scconv=True,
        upsample_output=True,
        upsample_num_filters=512,
        witch_cp=False,
    ):
        super().__init__()
        assert len(layer_nums) > 0 and len(num_upsample_filters) > 0

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]
        self.blocks = nn.ModuleList()
        self.deblocks = nn.ModuleList()
        self.cross = nn.ModuleList()
        self.with_cp = witch_cp

        for idx in range(num_levels):
            cur_layers = [
                nn.ZeroPad2d(1),
                nn.Conv2d(
                    c_in_list[idx],
                    num_filters[idx],
                    kernel_size=3,
                    stride=layer_strides[idx],
                    padding=0,
                    bias=False,
                ),
                nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            ]
            for k in range(layer_nums[idx]):
                if not use_scconv:
                    cur_layers.extend(
                        [
                            nn.Conv2d(
                                num_filters[idx],
                                num_filters[idx],
                                kernel_size=3,
                                padding=1,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        ]
                    )
                else:
                    cur_layers.extend(
                        [
                            SCBottleneck(
                                num_filters[idx],
                                num_filters[idx],
                                norm_layer=functools.partial(nn.BatchNorm2d, eps=1e-3, momentum=0.01),
                            )
                        ]
                    )

            self.blocks.append(nn.Sequential(*cur_layers))

            self.cross.append(
                nn.Sequential(
                    nn.ConvTranspose2d(
                        num_filters[idx],
                        num_upsample_filters[idx],
                        kernel_size=1,
                        stride=1,
                        bias=False,
                    ),
                    nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                    nn.ReLU(inplace=True),
                )
            )

            if idx < num_levels - 1:
                self.deblocks.append(
                    nn.Sequential(
                        nn.ConvTranspose2d(
                            num_upsample_filters[idx],
                            num_upsample_filters[idx],
                            upsample_strides[idx],
                            stride=upsample_strides[idx],
                            bias=False,
                        ),
                        nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                        nn.ReLU(inplace=True),
                    )
                )
        self.upsample_featuremap = upsample_output
        if self.upsample_featuremap:
            self.upsample_conv = nn.Sequential(
                nn.ConvTranspose2d(num_upsample_filters[0], upsample_num_filters, 2, stride=2, bias=False),
                nn.BatchNorm2d(upsample_num_filters, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )

    def forward(self, spatial_features):
        """
        Args:
            data_dict:
                spatial_features
        Returns:
        """
        outputs = []
        pyramids = []
        x = spatial_features
        for i in range(len(self.blocks)):
            x = self.blocks[i](x)
            pyramids.append(x)

        for i in range(len(self.deblocks), -1, -1):
            cross_x = self.cross[i](pyramids[i])

            if i < len(self.deblocks):
                x = self.deblocks[i](x)
                x = x + cross_x
            else:
                x = cross_x
            outputs.append(x)

        if self.upsample_featuremap:
            x = self.upsample_conv(x)
            outputs.append(x)

        return x, outputs, pyramids


class ASPP(nn.Module):
    def __init__(self, in_channels, out_channels):  # 512, 256
        super(ASPP, self).__init__()

        self.conv_1x1_1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=1)
        self.bn_conv_1x1_1 = nn.BatchNorm2d(in_channels // 2)

        self.conv_3x3_1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, stride=1, padding=6, dilation=6)
        self.bn_conv_3x3_1 = nn.BatchNorm2d(in_channels // 2)

        self.conv_3x3_2 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, stride=1, padding=12, dilation=12)
        self.bn_conv_3x3_2 = nn.BatchNorm2d(in_channels // 2)

        self.conv_3x3_3 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, stride=1, padding=18, dilation=18)
        self.bn_conv_3x3_3 = nn.BatchNorm2d(in_channels // 2)

        # self.avg_pool = nn.AdaptiveAvgPool2d(1)

        # self.conv_1x1_2 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=1)
        # self.bn_conv_1x1_2 = nn.BatchNorm2d(in_channels // 2)

        self.conv_1x1_3 = nn.Conv2d(in_channels // 2 * 4, out_channels, kernel_size=1)  # (1280 = 4*256)
        self.bn_conv_1x1_3 = nn.BatchNorm2d(out_channels)

    def forward(self, feature_map):
        # (feature_map has shape (batch_size, 512, h/16, w/16)) (assuming self.resnet is ResNet18_OS16 or ResNet34_OS16. If self.resnet instead is ResNet18_OS8 or ResNet34_OS8, it will be (batch_size, 512, h/8, w/8))
        # feature_map_h = feature_map.size()[2] # (== h/16)
        # feature_map_w = feature_map.size()[3] # (== w/16)

        out_1x1 = F.relu(self.bn_conv_1x1_1(self.conv_1x1_1(feature_map)))  # (shape: (batch_size, 256, h/16, w/16))
        out_3x3_1 = F.relu(self.bn_conv_3x3_1(self.conv_3x3_1(feature_map)))  # (shape: (batch_size, 256, h/16, w/16))
        out_3x3_2 = F.relu(self.bn_conv_3x3_2(self.conv_3x3_2(feature_map)))  # (shape: (batch_size, 256, h/16, w/16))
        out_3x3_3 = F.relu(self.bn_conv_3x3_3(self.conv_3x3_3(feature_map)))  # (shape: (batch_size, 256, h/16, w/16))

        # out_img = self.avg_pool(feature_map) # (shape: (batch_size, 512, 1, 1))
        # out_img = F.relu(self.bn_conv_1x1_2(self.conv_1x1_2(out_img))) # (shape: (batch_size, 256, 1, 1))
        # out_img = F.upsample(out_img, size=(feature_map_h, feature_map_w), mode="bilinear") # (shape: (batch_size, 256, h/16, w/16))

        out = torch.cat([out_1x1, out_3x3_1, out_3x3_2, out_3x3_3], 1)  # (shape: (batch_size, 1280, h/16, w/16))
        out = F.relu(self.bn_conv_1x1_3(self.conv_1x1_3(out)))  # (shape: (batch_size, 256, h/16, w/16))

        return out


class ASFF(nn.Module):
    def __init__(self, input_dims, level, rfb=False, output_dims=512):
        super(ASFF, self).__init__()
        self.level = level
        self.dim = input_dims
        self.inter_dim = self.dim[self.level]
        assert level in [0, 1, 2]
        if level == 0:
            self.stride_level_1 = nn.Sequential(
                nn.Conv2d(self.dim[1], self.inter_dim, 3, stride=2, bias=False, padding=1),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )
            self.stride_level_2 = nn.Sequential(
                nn.Conv2d(self.dim[2], self.inter_dim, 3, stride=2, bias=False, padding=1),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )
        elif level == 1:
            self.compress_level_0 = nn.Sequential(
                nn.Conv2d(self.dim[0], self.inter_dim, 1, stride=1, bias=False, padding=0),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )
            self.stride_level_2 = nn.Sequential(
                nn.Conv2d(self.dim[2], self.inter_dim, 3, stride=2, bias=False, padding=1),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )
        elif level == 2:
            self.compress_level_0 = nn.Sequential(
                nn.Conv2d(self.dim[0], self.inter_dim, 1, stride=1, bias=False, padding=0),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )
            self.compress_level_1 = nn.Sequential(
                nn.Conv2d(self.dim[1], self.inter_dim, 1, stride=1, bias=False, padding=0),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )
        self.expand = nn.Sequential(
            nn.Conv2d(self.inter_dim, output_dims, 3, stride=1, bias=False, padding=1),
            nn.BatchNorm2d(output_dims, eps=1e-3, momentum=0.01),
            nn.ReLU(),
        )

        compress_c = 8 if rfb else 16  # when adding rfb, we use half number of channels to save memory

        self.weight_level_0 = nn.Sequential(
            nn.Conv2d(self.inter_dim, compress_c, 1, stride=1, bias=False, padding=0),
            nn.BatchNorm2d(compress_c),
            nn.ReLU(),
        )
        self.weight_level_1 = nn.Sequential(
            nn.Conv2d(self.inter_dim, compress_c, 1, stride=1, bias=False, padding=0),
            nn.BatchNorm2d(compress_c),
            nn.ReLU(),
        )
        self.weight_level_2 = nn.Sequential(
            nn.Conv2d(self.inter_dim, compress_c, 1, stride=1, bias=False, padding=0),
            nn.BatchNorm2d(compress_c),
            nn.ReLU(),
        )

        self.weight_levels = nn.Conv2d(compress_c * 3, 3, kernel_size=1, stride=1, padding=0)

    def forward(self, x_level_0, x_level_1, x_level_2):
        if self.level == 0:
            level_0_resized = x_level_0
            level_1_resized = self.stride_level_1(x_level_1)

            level_2_downsampled_inter = F.max_pool2d(x_level_2, 3, stride=2, padding=1)
            level_2_resized = self.stride_level_2(level_2_downsampled_inter)

        elif self.level == 1:
            level_0_compressed = self.compress_level_0(x_level_0)
            level_0_resized = F.interpolate(level_0_compressed, scale_factor=2, mode="nearest")
            level_1_resized = x_level_1
            level_2_resized = self.stride_level_2(x_level_2)
        elif self.level == 2:
            level_0_compressed = self.compress_level_0(x_level_0)
            level_0_resized = F.interpolate(level_0_compressed, scale_factor=4, mode="nearest")
            level_1_compressed = self.compress_level_1(x_level_1)
            level_1_resized = F.interpolate(level_1_compressed, scale_factor=2, mode="nearest")
            level_2_resized = x_level_2

        level_0_weight_v = self.weight_level_0(level_0_resized)
        level_1_weight_v = self.weight_level_1(level_1_resized)
        level_2_weight_v = self.weight_level_2(level_2_resized)
        # print(self.level, level_0_weight_v.shape, level_1_weight_v.shape, level_2_weight_v.shape)

        levels_weight_v = torch.cat((level_0_weight_v, level_1_weight_v, level_2_weight_v), 1)
        levels_weight = self.weight_levels(levels_weight_v)
        levels_weight = F.softmax(levels_weight, dim=1)

        fused_out_reduced = (
            level_0_resized * levels_weight[:, 0:1, :, :]
            + level_1_resized * levels_weight[:, 1:2, :, :]
            + level_2_resized * levels_weight[:, 2:, :, :]
        )

        out = self.expand(fused_out_reduced)
        return out


class BEVFPNASFF(BEVFPN):
    def __init__(
        self,
        layer_nums=[5, 5, 5],
        layer_strides=[1, 2, 2],
        num_filters=[128, 256, 512],
        upsample_strides=[2, 2],
        num_upsample_filters=[256, 256],
        input_channels=256,
        use_scconv=True,
        upsample_output=True,
        upsample_num_filters=512,
        rfb=False,
        witch_cp=False,
        output_dims=512,
    ):
        super().__init__(
            layer_nums,
            layer_strides,
            num_filters,
            upsample_strides,
            num_upsample_filters,
            input_channels,
            use_scconv,
            upsample_output,
            upsample_num_filters,
            witch_cp,
        )
        assert upsample_output is True or len(layer_nums) == len(upsample_strides) >= 3
        input_dims = num_upsample_filters + [upsample_num_filters] if upsample_output else num_upsample_filters
        # self.level_0_fusion = ASFF(input_dims=input_dims, level=0, rfb=rfb)
        self.level_1_fusion = ASFF(input_dims=input_dims, level=1, rfb=rfb, output_dims=output_dims)
        self.level_2_fusion = ASFF(input_dims=input_dims, level=2, rfb=rfb, output_dims=output_dims)
        self.aspp = ASPP(num_filters[-1], num_filters[-1])

    def forward(self, spatial_features):
        """
        Args:
            data_dict:
                spatial_features
        Returns:
        """
        outputs = []
        pyramids = []
        x = spatial_features
        for i in range(len(self.blocks)):
            x = self.blocks[i](x)
            if i == len(self.blocks) - 1:
                x = self.aspp(x)
            pyramids.append(x)

        for i in range(len(self.deblocks), -1, -1):
            cross_x = self.cross[i](pyramids[i])

            if i < len(self.deblocks):
                x = self.deblocks[i](x)
                x = x + cross_x
            else:
                x = cross_x
            outputs.append(x)

        if self.upsample_featuremap:
            x = self.upsample_conv(x)
            outputs.append(x)

        asff_outputs = list()
        for i in range(3):
            fusion = getattr(self, "level_{}_fusion".format(i))
            fused = fusion(outputs[0], outputs[1], outputs[2])
            asff_outputs.append(fused)

        return x, asff_outputs, pyramids


class BEVFPNTwoLayer(BEVFPNASFF):  # 融合aspp + asff + 稀疏featuremap
    def __init__(
        self,
        layer_nums=[5, 5, 5],
        layer_strides=[1, 2, 2],
        num_filters=[128, 256, 512],
        upsample_strides=[2, 2],
        num_upsample_filters=[256, 256],
        input_channels=256,
        witch_cp=False,
        use_scconv=True,
        upsample_output=True,
        upsample_num_filters=256,
        rfb=False,
        use_sparse3d_feature=False,
        bev_3d_input_channels=64 * 11,
        output_dims=512,
    ):
        super().__init__(
            layer_nums,
            layer_strides,
            num_filters,
            upsample_strides,
            num_upsample_filters,
            input_channels,
            use_scconv,
            upsample_output,
            upsample_num_filters,
            rfb,
            witch_cp,
            output_dims,
        )
        self.use_sparse3d_feature = use_sparse3d_feature
        if self.use_sparse3d_feature:
            self.cross_bev_3d = nn.Sequential(
                nn.Conv2d(
                    bev_3d_input_channels,
                    64,
                    kernel_size=1,
                    stride=1,
                    padding=0,
                    bias=False,
                ),
                nn.BatchNorm2d(64, eps=1e-3, momentum=0.01),
                nn.ReLU(),
                nn.Conv2d(
                    64,
                    upsample_num_filters,
                    kernel_size=3,
                    stride=1,
                    padding=1,
                    bias=False,
                ),
                nn.BatchNorm2d(upsample_num_filters, eps=1e-3, momentum=0.01),
                nn.ReLU(),
            )

    def forward(self, spatial_features, bev_3d_features=None):
        """
        Args:
            data_dict:
                spatial_features
        Returns:
        """
        outputs = []
        pyramids = []
        x = spatial_features
        for i in range(len(self.blocks)):
            if self.with_cp and self.training:
                x = cp.checkpoint(self.blocks[i], x)
            else:
                x = self.blocks[i](x)
            if i == len(self.blocks) - 1:
                x = self.aspp(x)
            pyramids.append(x)

        for i in range(len(self.deblocks), -1, -1):
            cross_x = self.cross[i](pyramids[i])

            if i < len(self.deblocks):
                x = self.deblocks[i](x)
                x = x + cross_x
            else:
                x = cross_x
            outputs.append(x)

        if self.upsample_featuremap:
            x = self.upsample_conv(x)
            outputs.append(x)

        asff_outputs = list()
        for i in range(1, 3):
            fusion = getattr(self, "level_{}_fusion".format(i))

            if i == 2 and self.use_sparse3d_feature:
                fused = fusion(outputs[1], outputs[2], outputs[3] + self.cross_bev_3d(bev_3d_features))
            else:
                fused = fusion(outputs[1], outputs[2], outputs[3])

            asff_outputs.append(fused)

        return asff_outputs


class BEVFPNv4Pillar(nn.Module):  # 用于voxel_size=0.1的pointpillar模型
    def __init__(
        self,
        layer_nums=[5, 5, 5],
        layer_strides=[1, 2, 2],
        num_filters=[128, 256, 512],
        upsample_strides=[2, 2],
        input_channels=256,
        use_scconv=True,
        upsample_num_filters=256,
        rfb=False,
    ):
        super().__init__()
        if not isinstance(use_scconv, list):
            use_scconv = [use_scconv] * len(layer_nums)
        if layer_nums is not None:
            assert len(layer_nums) == len(layer_strides) == len(num_filters)
        else:
            layer_nums = layer_strides = num_filters = []

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]
        offset = num_levels - len(upsample_strides)
        self.blocks = nn.ModuleList()
        self.deblocks = nn.ModuleList()
        self.cross = nn.ModuleList()
        for idx in range(num_levels):
            cur_layers = [
                nn.ZeroPad2d(1),
                nn.Conv2d(
                    c_in_list[idx],
                    num_filters[idx],
                    kernel_size=3,
                    stride=layer_strides[idx],
                    padding=0,
                    bias=False,
                ),
                nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                nn.ReLU(),
            ]
            for k in range(layer_nums[idx]):
                if not use_scconv[idx]:
                    cur_layers.extend(
                        [
                            nn.Conv2d(
                                num_filters[idx],
                                num_filters[idx],
                                kernel_size=3,
                                padding=1,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(),
                        ]
                    )
                else:
                    cur_layers.extend(
                        [
                            SCBottleneck(
                                num_filters[idx],
                                num_filters[idx],
                                norm_layer=functools.partial(nn.BatchNorm2d, eps=1e-3, momentum=0.01),
                            )
                        ]
                    )
            self.blocks.append(nn.Sequential(*cur_layers))
            if idx - offset + 1 >= 0:
                self.cross.append(
                    nn.Sequential(
                        nn.Conv2d(
                            num_filters[idx],
                            upsample_num_filters,
                            kernel_size=1,
                            stride=1,
                            bias=False,
                        ),
                        nn.BatchNorm2d(upsample_num_filters, eps=1e-3, momentum=0.01),
                        nn.ReLU(),
                    )
                )

        for idx in range(len(upsample_strides)):
            stride = upsample_strides[idx]
            if stride >= 1:
                self.deblocks.append(
                    nn.Sequential(
                        nn.ConvTranspose2d(
                            upsample_num_filters,
                            upsample_num_filters,
                            upsample_strides[idx],
                            stride=upsample_strides[idx],
                            bias=False,
                        ),
                        nn.BatchNorm2d(upsample_num_filters, eps=1e-3, momentum=0.01),
                        nn.ReLU(),
                    )
                )
        input_dims = [upsample_num_filters] * 3
        self.level_1_fusion = ASFF(input_dims=input_dims, level=1, compress=rfb)
        self.level_2_fusion = ASFF(input_dims=input_dims, level=2, compress=rfb)
        self.aspp = ASPP(num_filters[-1], num_filters[-1])

        self.num_bev_features = sum(input_dims)

    def forward(self, spatial_features):
        outputs = []
        pyramids = []
        x = spatial_features

        for i in range(len(self.blocks)):
            x = self.blocks[i](x)
            if i == len(self.blocks) - 1:
                x = self.aspp(x)
            pyramids.append(x)
        offset = len(self.blocks) - len(self.deblocks)
        for i in range(len(self.deblocks), -1, -1):
            cross_x = self.cross[i](pyramids[i + offset - 1])
            if i < 2:
                x = self.deblocks[i](x)
                x = x + cross_x
            else:
                x = cross_x
            outputs.append(x)

        asff_outputs = list()
        for i in range(1, 3):
            fusion = getattr(self, "level_{}_fusion".format(i))
            fused = fusion(outputs[0], outputs[1], outputs[2])
            asff_outputs.append(fused)

        return x, asff_outputs, pyramids
