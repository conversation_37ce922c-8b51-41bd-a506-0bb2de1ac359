import functools
import torch
import torch.nn as nn
import numpy as np
from torch.nn import functional as F
from .sc_conv import SCBottleneck


class ASPP(nn.Module):
    def __init__(self, in_channels, out_channels):  # 512, 256
        super(ASPP, self).__init__()

        self.conv_1x1_1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=1)
        self.bn_conv_1x1_1 = nn.BatchNorm2d(in_channels // 2)

        self.conv_3x3_1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, stride=1, padding=6, dilation=6)
        self.bn_conv_3x3_1 = nn.BatchNorm2d(in_channels // 2)

        self.conv_3x3_2 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, stride=1, padding=12, dilation=12)
        self.bn_conv_3x3_2 = nn.BatchNorm2d(in_channels // 2)

        self.conv_3x3_3 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, stride=1, padding=18, dilation=18)
        self.bn_conv_3x3_3 = nn.BatchNorm2d(in_channels // 2)

        # self.avg_pool = nn.AdaptiveAvgPool2d(1)

        # self.conv_1x1_2 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=1)
        # self.bn_conv_1x1_2 = nn.BatchNorm2d(in_channels // 2)

        self.conv_1x1_3 = nn.Conv2d(in_channels // 2 * 4, out_channels, kernel_size=1)  # (1280 = 4*256)
        self.bn_conv_1x1_3 = nn.BatchNorm2d(out_channels)

    def forward(self, feature_map):
        # (feature_map has shape (batch_size, 512, h/16, w/16)) (assuming self.resnet is ResNet18_OS16 or ResNet34_OS16. If self.resnet instead is ResNet18_OS8 or ResNet34_OS8, it will be (batch_size, 512, h/8, w/8))
        # feature_map_h = feature_map.size()[2] # (== h/16)
        # feature_map_w = feature_map.size()[3] # (== w/16)

        out_1x1 = F.relu(
            self.bn_conv_1x1_1(self.conv_1x1_1(feature_map)), inplace=True
        )  # (shape: (batch_size, 256, h/16, w/16))
        out_3x3_1 = F.relu(
            self.bn_conv_3x3_1(self.conv_3x3_1(feature_map)), inplace=True
        )  # (shape: (batch_size, 256, h/16, w/16))
        out_3x3_2 = F.relu(
            self.bn_conv_3x3_2(self.conv_3x3_2(feature_map)), inplace=True
        )  # (shape: (batch_size, 256, h/16, w/16))
        out_3x3_3 = F.relu(
            self.bn_conv_3x3_3(self.conv_3x3_3(feature_map)), inplace=True
        )  # (shape: (batch_size, 256, h/16, w/16))

        # out_img = self.avg_pool(feature_map) # (shape: (batch_size, 512, 1, 1))
        # out_img = F.relu(self.bn_conv_1x1_2(self.conv_1x1_2(out_img))) # (shape: (batch_size, 256, 1, 1))
        # out_img = F.upsample(out_img, size=(feature_map_h, feature_map_w), mode="bilinear") # (shape: (batch_size, 256, h/16, w/16))

        out = torch.cat([out_1x1, out_3x3_1, out_3x3_2, out_3x3_3], 1)  # (shape: (batch_size, 1280, h/16, w/16))
        out = F.relu(self.bn_conv_1x1_3(self.conv_1x1_3(out)), inplace=True)  # (shape: (batch_size, 256, h/16, w/16))

        return out


class ASFF(nn.Module):
    def __init__(self, input_dims, level, rfb=False):
        super(ASFF, self).__init__()
        self.level = level
        self.dim = input_dims
        self.inter_dim = self.dim[self.level]
        assert level in [0, 1, 2]
        if level == 0:
            self.stride_level_1 = nn.Sequential(
                nn.Conv2d(self.dim[1], self.inter_dim, 3, stride=2, bias=False, padding=1),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
            self.stride_level_2 = nn.Sequential(
                nn.Conv2d(self.dim[2], self.inter_dim, 3, stride=2, bias=False, padding=1),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
        elif level == 1:
            self.compress_level_0 = nn.Sequential(
                nn.Conv2d(self.dim[0], self.inter_dim, 1, stride=1, bias=False, padding=0),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
            self.stride_level_2 = nn.Sequential(
                nn.Conv2d(self.dim[2], self.inter_dim, 3, stride=2, bias=False, padding=1),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
        elif level == 2:
            self.compress_level_0 = nn.Sequential(
                nn.Conv2d(self.dim[0], self.inter_dim, 1, stride=1, bias=False, padding=0),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
            self.compress_level_1 = nn.Sequential(
                nn.Conv2d(self.dim[1], self.inter_dim, 1, stride=1, bias=False, padding=0),
                nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            )
        self.expand = nn.Sequential(
            nn.Conv2d(self.inter_dim, self.inter_dim, 3, stride=1, bias=False, padding=1),
            nn.BatchNorm2d(self.inter_dim, eps=1e-3, momentum=0.01),
            nn.ReLU(inplace=True),
        )

        compress_c = 8 if rfb else 16  # when adding rfb, we use half number of channels to save memory

        self.weight_level_0 = nn.Sequential(
            nn.Conv2d(self.inter_dim, compress_c, 1, stride=1, bias=False, padding=0),
            nn.BatchNorm2d(compress_c),
            nn.ReLU(inplace=True),
        )
        self.weight_level_1 = nn.Sequential(
            nn.Conv2d(self.inter_dim, compress_c, 1, stride=1, bias=False, padding=0),
            nn.BatchNorm2d(compress_c),
            nn.ReLU(inplace=True),
        )
        self.weight_level_2 = nn.Sequential(
            nn.Conv2d(self.inter_dim, compress_c, 1, stride=1, bias=False, padding=0),
            nn.BatchNorm2d(compress_c),
            nn.ReLU(inplace=True),
        )

        self.weight_levels = nn.Conv2d(compress_c * 3, 3, kernel_size=1, stride=1, padding=0)

    def forward(self, x_level_0, x_level_1, x_level_2):
        if self.level == 0:
            level_0_resized = x_level_0
            level_1_resized = self.stride_level_1(x_level_1)

            level_2_downsampled_inter = F.max_pool2d(x_level_2, 3, stride=2, padding=1)
            level_2_resized = self.stride_level_2(level_2_downsampled_inter)

        elif self.level == 1:
            level_0_compressed = self.compress_level_0(x_level_0)
            level_0_resized = F.interpolate(level_0_compressed, scale_factor=2, mode="nearest")
            level_1_resized = x_level_1
            level_2_resized = self.stride_level_2(x_level_2)
        elif self.level == 2:
            level_0_compressed = self.compress_level_0(x_level_0)
            level_0_resized = F.interpolate(level_0_compressed, scale_factor=4, mode="nearest")
            level_1_compressed = self.compress_level_1(x_level_1)
            level_1_resized = F.interpolate(level_1_compressed, scale_factor=2, mode="nearest")
            level_2_resized = x_level_2

        level_0_weight_v = self.weight_level_0(level_0_resized)
        level_1_weight_v = self.weight_level_1(level_1_resized)
        level_2_weight_v = self.weight_level_2(level_2_resized)
        # print(self.level, level_0_weight_v.shape, level_1_weight_v.shape, level_2_weight_v.shape)

        levels_weight_v = torch.cat((level_0_weight_v, level_1_weight_v, level_2_weight_v), 1)
        levels_weight = self.weight_levels(levels_weight_v)
        levels_weight = F.softmax(levels_weight, dim=1)

        fused_out_reduced = (
            level_0_resized * levels_weight[:, 0:1, :, :]
            + level_1_resized * levels_weight[:, 1:2, :, :]
            + level_2_resized * levels_weight[:, 2:, :, :]
        )

        out = self.expand(fused_out_reduced)
        return out


class BEVFPNv4Pillar(nn.Module):  # 用于voxel_size=0.1的pointpillar模型
    def __init__(
        self,
        layer_nums=[5, 5, 5],
        layer_strides=[1, 2, 2],
        num_filters=[128, 256, 512],
        upsample_strides=[2, 2],
        input_channels=256,
        use_scconv=True,
        num_upsample_filters=256,
        rfb=False,
        with_cp=False,
        upsample_output=False,
    ):
        super().__init__()
        if not isinstance(use_scconv, list):
            use_scconv = [use_scconv] * len(layer_nums)
        if layer_nums is not None:
            assert len(layer_nums) == len(layer_strides) == len(num_filters)
        else:
            layer_nums = layer_strides = num_filters = []

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]

        num_levels = len(layer_nums)
        c_in_list = [input_channels, *num_filters[:-1]]
        offset = num_levels - len(upsample_strides)
        self.blocks = nn.ModuleList()
        self.deblocks = nn.ModuleList()
        self.cross = nn.ModuleList()
        for idx in range(num_levels):
            cur_layers = [
                nn.ZeroPad2d(1),
                nn.Conv2d(
                    c_in_list[idx],
                    num_filters[idx],
                    kernel_size=3,
                    stride=layer_strides[idx],
                    padding=0,
                    bias=False,
                ),
                nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                nn.ReLU(inplace=True),
            ]
            for k in range(layer_nums[idx]):
                if not use_scconv[idx]:
                    cur_layers.extend(
                        [
                            nn.Conv2d(
                                num_filters[idx],
                                num_filters[idx],
                                kernel_size=3,
                                padding=1,
                                bias=False,
                            ),
                            nn.BatchNorm2d(num_filters[idx], eps=1e-3, momentum=0.01),
                            nn.ReLU(inplace=True),
                        ]
                    )
                else:
                    cur_layers.extend(
                        [
                            SCBottleneck(
                                num_filters[idx],
                                num_filters[idx],
                                norm_layer=functools.partial(nn.BatchNorm2d, eps=1e-3, momentum=0.01),
                            )
                        ]
                    )
            self.blocks.append(nn.Sequential(*cur_layers))
            if idx - offset + 1 >= 0:
                self.cross.append(
                    nn.Sequential(
                        nn.Conv2d(
                            num_filters[idx],
                            num_upsample_filters,
                            kernel_size=1,
                            stride=1,
                            bias=False,
                        ),
                        nn.BatchNorm2d(num_upsample_filters, eps=1e-3, momentum=0.01),
                        nn.ReLU(inplace=True),
                    )
                )

        for idx in range(len(upsample_strides)):
            stride = upsample_strides[idx]
            if stride >= 1:
                self.deblocks.append(
                    nn.Sequential(
                        nn.ConvTranspose2d(
                            num_upsample_filters,
                            num_upsample_filters,
                            upsample_strides[idx],
                            stride=upsample_strides[idx],
                            bias=False,
                        ),
                        nn.BatchNorm2d(num_upsample_filters, eps=1e-3, momentum=0.01),
                        nn.ReLU(inplace=True),
                    )
                )
        input_dims = [num_upsample_filters] * 3
        # self.level_0_fusion = ASFF(input_dims=input_dims, level=0, rfb=rfb)
        self.level_1_fusion = ASFF(input_dims=input_dims, level=1, rfb=rfb)
        self.level_2_fusion = ASFF(input_dims=input_dims, level=2, rfb=rfb)
        self.aspp = ASPP(num_filters[-1], num_filters[-1])

        self.num_bev_features = sum(input_dims)

    def forward(self, spatial_features):
        outputs = []
        pyramids = []
        x = spatial_features

        for i in range(len(self.blocks)):
            x = self.blocks[i](x)
            if i == len(self.blocks) - 1:
                x = self.aspp(x)
            pyramids.append(x)
        offset = len(self.blocks) - len(self.deblocks)
        for i in range(len(self.deblocks), -1, -1):
            cross_x = self.cross[i](pyramids[i + offset - 1])
            if i < 2:
                x = self.deblocks[i](x)
                x = x + cross_x
            else:
                x = cross_x
            outputs.append(x)

        asff_outputs = list()
        for i in range(1, 3):
            fusion = getattr(self, "level_{}_fusion".format(i))
            fused = fusion(outputs[0], outputs[1], outputs[2])
            asff_outputs.append(fused)

        return x, asff_outputs, pyramids


def conv_bn(in_channels, out_channels, kernel_size, stride, padding, groups=1):
    """Basic cell for rep-style block, including conv and bn"""
    result = nn.Sequential()
    result.add_module(
        "conv",
        nn.Conv2d(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=kernel_size,
            stride=stride,
            padding=padding,
            groups=groups,
            bias=False,
        ),
    )
    result.add_module("bn", nn.BatchNorm2d(num_features=out_channels))
    return result


class RepVGGBlock_v6(nn.Module):
    """RepVGGBlock is a basic rep-style block, including training and deploy status
    This code is based on https://github.com/DingXiaoH/RepVGG/blob/main/repvgg.py
    """

    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size=3,
        stride=1,
        padding=1,
        dilation=1,
        groups=1,
        padding_mode="zeros",
        deploy=False,
        use_se=False,
    ):
        super(RepVGGBlock_v6, self).__init__()
        """ Initialization of the class.
        Args:
            in_channels (int): Number of channels in the input image
            out_channels (int): Number of channels produced by the convolution
            kernel_size (int or tuple): Size of the convolving kernel
            stride (int or tuple, optional): Stride of the convolution. Default: 1
            padding (int or tuple, optional): Zero-padding added to both sides of
                the input. Default: 1
            dilation (int or tuple, optional): Spacing between kernel elements. Default: 1
            groups (int, optional): Number of blocked connections from input
                channels to output channels. Default: 1
            padding_mode (string, optional): Default: 'zeros'
            deploy: Whether to be deploy status or training status. Default: False
            use_se: Whether to use se. Default: False
        """
        self.deploy = deploy
        self.groups = groups
        self.in_channels = in_channels
        self.out_channels = out_channels

        assert kernel_size == 3
        assert padding == 1

        padding_11 = padding - kernel_size // 2

        self.nonlinearity = nn.ReLU(True)

        if use_se:
            raise NotImplementedError("se block not supported yet")
        else:
            self.se = nn.Identity()

        if deploy:
            self.rbr_reparam = nn.Conv2d(
                in_channels=in_channels,
                out_channels=out_channels,
                kernel_size=kernel_size,
                stride=stride,
                padding=padding,
                dilation=dilation,
                groups=groups,
                bias=True,
                padding_mode=padding_mode,
            )

        else:
            self.rbr_identity = (
                nn.BatchNorm2d(num_features=in_channels) if out_channels == in_channels and stride == 1 else None
            )
            self.rbr_dense = conv_bn(
                in_channels=in_channels,
                out_channels=out_channels,
                kernel_size=kernel_size,
                stride=stride,
                padding=padding,
                groups=groups,
            )
            self.rbr_1x1 = conv_bn(
                in_channels=in_channels,
                out_channels=out_channels,
                kernel_size=1,
                stride=stride,
                padding=padding_11,
                groups=groups,
            )

    def forward(self, inputs):
        """Forward process"""
        if hasattr(self, "rbr_reparam"):
            return self.nonlinearity(self.se(self.rbr_reparam(inputs)))

        if self.rbr_identity is None:
            id_out = 0
        else:
            id_out = self.rbr_identity(inputs)

        return self.nonlinearity(self.se(self.rbr_dense(inputs) + self.rbr_1x1(inputs) + id_out))

    def get_equivalent_kernel_bias(self):
        kernel3x3, bias3x3 = self._fuse_bn_tensor(self.rbr_dense)
        kernel1x1, bias1x1 = self._fuse_bn_tensor(self.rbr_1x1)
        kernelid, biasid = self._fuse_bn_tensor(self.rbr_identity)
        return kernel3x3 + self._pad_1x1_to_3x3_tensor(kernel1x1) + kernelid, bias3x3 + bias1x1 + biasid

    def _pad_1x1_to_3x3_tensor(self, kernel1x1):
        if kernel1x1 is None:
            return 0
        else:
            return torch.nn.functional.pad(kernel1x1, [1, 1, 1, 1])

    def _fuse_bn_tensor(self, branch):
        if branch is None:
            return 0, 0
        if isinstance(branch, nn.Sequential):
            kernel = branch.conv.weight
            running_mean = branch.bn.running_mean
            running_var = branch.bn.running_var
            gamma = branch.bn.weight
            beta = branch.bn.bias
            eps = branch.bn.eps
        else:
            assert isinstance(branch, nn.BatchNorm2d)
            if not hasattr(self, "id_tensor"):
                input_dim = self.in_channels // self.groups
                kernel_value = np.zeros((self.in_channels, input_dim, 3, 3), dtype=np.float32)
                for i in range(self.in_channels):
                    kernel_value[i, i % input_dim, 1, 1] = 1
                self.id_tensor = torch.from_numpy(kernel_value).to(branch.weight.device)
            kernel = self.id_tensor
            running_mean = branch.running_mean
            running_var = branch.running_var
            gamma = branch.weight
            beta = branch.bias
            eps = branch.eps
        std = (running_var + eps).sqrt()
        t = (gamma / std).reshape(-1, 1, 1, 1)
        return kernel * t, beta - running_mean * gamma / std

    def switch_to_deploy(self):
        if hasattr(self, "rbr_reparam"):
            return
        kernel, bias = self.get_equivalent_kernel_bias()
        self.rbr_reparam = nn.Conv2d(
            in_channels=self.rbr_dense.conv.in_channels,
            out_channels=self.rbr_dense.conv.out_channels,
            kernel_size=self.rbr_dense.conv.kernel_size,
            stride=self.rbr_dense.conv.stride,
            padding=self.rbr_dense.conv.padding,
            dilation=self.rbr_dense.conv.dilation,
            groups=self.rbr_dense.conv.groups,
            bias=True,
        )
        self.rbr_reparam.weight.data = kernel
        self.rbr_reparam.bias.data = bias
        for para in self.parameters():
            para.detach_()
        self.__delattr__("rbr_dense")
        self.__delattr__("rbr_1x1")
        if hasattr(self, "rbr_identity"):
            self.__delattr__("rbr_identity")
        if hasattr(self, "id_tensor"):
            self.__delattr__("id_tensor")
        self.deploy = True


class QARepVGGBlock(RepVGGBlock_v6):
    """
    RepVGGBlock is a basic rep-style block, including training and deploy status
    This code is based on https://arxiv.org/abs/2212.01593
    """

    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size=3,
        stride=1,
        padding=1,
        dilation=1,
        groups=1,
        padding_mode="zeros",
        deploy=False,
        use_se=False,
    ):
        super(QARepVGGBlock, self).__init__(
            in_channels, out_channels, kernel_size, stride, padding, dilation, groups, padding_mode, deploy, use_se
        )
        if not deploy:
            self.bn = nn.BatchNorm2d(out_channels)
            self.rbr_1x1 = nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, groups=groups, bias=False)
            self.rbr_identity = nn.Identity() if out_channels == in_channels and stride == 1 else None
        self._id_tensor = None

    def forward(self, inputs):
        if hasattr(self, "rbr_reparam"):
            return self.nonlinearity(self.bn(self.se(self.rbr_reparam(inputs))))

        if self.rbr_identity is None:
            id_out = 0
        else:
            id_out = self.rbr_identity(inputs)

        return self.nonlinearity(self.bn(self.se(self.rbr_dense(inputs) + self.rbr_1x1(inputs) + id_out)))

    def get_equivalent_kernel_bias(self):
        kernel3x3, bias3x3 = self._fuse_bn_tensor(self.rbr_dense)
        kernel = kernel3x3 + self._pad_1x1_to_3x3_tensor(self.rbr_1x1.weight)
        bias = bias3x3

        if self.rbr_identity is not None:
            input_dim = self.in_channels // self.groups
            kernel_value = np.zeros((self.in_channels, input_dim, 3, 3), dtype=np.float32)
            for i in range(self.in_channels):
                kernel_value[i, i % input_dim, 1, 1] = 1
            id_tensor = torch.from_numpy(kernel_value).to(self.rbr_1x1.weight.device)
            kernel = kernel + id_tensor
        return kernel, bias

    def _fuse_extra_bn_tensor(self, kernel, bias, branch):
        assert isinstance(branch, nn.BatchNorm2d)
        running_mean = branch.running_mean - bias  # remove bias
        running_var = branch.running_var
        gamma = branch.weight
        beta = branch.bias
        eps = branch.eps
        std = (running_var + eps).sqrt()
        t = (gamma / std).reshape(-1, 1, 1, 1)
        return kernel * t, beta - running_mean * gamma / std

    def switch_to_deploy(self):
        if hasattr(self, "rbr_reparam"):
            return
        kernel, bias = self.get_equivalent_kernel_bias()
        self.rbr_reparam = nn.Conv2d(
            in_channels=self.rbr_dense.conv.in_channels,
            out_channels=self.rbr_dense.conv.out_channels,
            kernel_size=self.rbr_dense.conv.kernel_size,
            stride=self.rbr_dense.conv.stride,
            padding=self.rbr_dense.conv.padding,
            dilation=self.rbr_dense.conv.dilation,
            groups=self.rbr_dense.conv.groups,
            bias=True,
        )
        self.rbr_reparam.weight.data = kernel
        self.rbr_reparam.bias.data = bias
        for para in self.parameters():
            para.detach_()
        self.__delattr__("rbr_dense")
        self.__delattr__("rbr_1x1")
        if hasattr(self, "rbr_identity"):
            self.__delattr__("rbr_identity")
        if hasattr(self, "id_tensor"):
            self.__delattr__("id_tensor")
        # keep post bn for QAT
        # if hasattr(self, 'bn'):
        #     self.__delattr__('bn')
        self.deploy = True


class Transpose(nn.Module):
    """Normal Transpose, default for upsampling"""

    def __init__(self, in_channels, out_channels, kernel_size=2, stride=2):
        super().__init__()
        self.upsample_transpose = torch.nn.ConvTranspose2d(
            in_channels=in_channels, out_channels=out_channels, kernel_size=kernel_size, stride=stride, bias=True
        )

    def forward(self, x):
        return self.upsample_transpose(x)


class BottleRep(nn.Module):
    def __init__(self, in_channels, out_channels, basic_block=RepVGGBlock_v6, weight=False):
        super().__init__()
        self.conv1 = basic_block(in_channels, out_channels)
        self.conv2 = basic_block(out_channels, out_channels)
        if in_channels != out_channels:
            self.shortcut = False
        else:
            self.shortcut = True
        if weight:
            self.alpha = nn.Parameter(torch.ones(1))
        else:
            self.alpha = 1.0

    def forward(self, x):
        outputs = self.conv1(x)
        outputs = self.conv2(outputs)
        return outputs + self.alpha * x if self.shortcut else outputs
