import torch
from torch.nn import ModuleList
from mmcv.cnn.bricks.transformer import build_transformer_layer_sequence
from mmdet.models.utils.builder import TRANSFORMER
from mmcv.cnn import xavier_init, constant_init
from mmcv.runner.base_module import BaseModule
import warnings
import copy
import torch.nn as nn
from mmcv.cnn.bricks.transformer import TransformerLayerSequence, build_attention, build_feedforward_network
from mmcv.cnn import build_norm_layer
from mmcv.cnn.bricks.registry import ATTENTION, TRANSFORMER_LAYER, TRANSFORMER_LAYER_SEQUENCE
from mmcv.utils import ConfigDict
import torch.utils.checkpoint as cp
from mmdet.models.utils.transformer import inverse_sigmoid
from mmcv.ops.multi_scale_deform_attn import MultiScaleDeformableAttnFunction
from perceptron.utils.e2e_utils.instance import get_query_embeds

# from mmcv.runner import auto_fp16

try:
    pass
except BaseException:  # noqa3
    warnings.warn("FlashAttention and related components are not included!!!")

# Disable warnings
warnings.filterwarnings("ignore")


def get_global_pos(points, pc_range):
    points = points * (pc_range[3:6] - pc_range[0:3]) + pc_range[0:3]
    return points


@TRANSFORMER.register_module()
class MOTDeformableTransformer(BaseModule):
    """Implements the deformable transformer referring to DETR3D/Sparse4D.
    Args:
        as_two_stage (bool): Generate query from encoder features.
            Default: False.
        num_feature_levels (int): Number of feature maps from FPN:
            Default: 4.
        two_stage_num_proposals (int): Number of proposals when set
            `as_two_stage` as True. Default: 300.
    """

    def __init__(self, decoder=None, **kwargs):
        super(MOTDeformableTransformer, self).__init__(**kwargs)
        self.decoder = build_transformer_layer_sequence(decoder)

    def init_weights(self):
        # follow the official DETR to init parameters
        for m in self.modules():
            if hasattr(m, "weight") and m.weight.dim() > 1:
                xavier_init(m, distribution="uniform")
        self._is_init = True

    def forward(
        self,
        query,
        query_pos,
        feat_flatten,
        img_spatial_flatten,
        lidar_feat_flatten,
        lidar_spatial_flatten,
        level_start_index,
        lidar_level_start_index,
        attn_masks,
        reference_points,
        pc_range,
        img_metas,
        reg_branches=None,
        query_embedding=None,
    ):
        """Forward function for `Detr3DTransformer`."""
        lidar2img = img_metas["lidar2imgs"]
        if "ida_mats" in img_metas:
            try:
                lidar2img = img_metas["ida_mats"].matmul(lidar2img)
            except Exception as e:
                print(e)
                lidar2img = lidar2img
        query = query.transpose(0, 1).contiguous()  # default attention batch_first  is True
        assert query.shape == query_pos.shape

        inter_states = self.decoder(
            query=query,
            query_pos=query_pos,
            mlvl_feats=feat_flatten,
            reference_points=reference_points,
            spatial_flatten=img_spatial_flatten,
            lidar_feats=lidar_feat_flatten,
            lidar_spatial_flatten=lidar_spatial_flatten,
            level_start_index=level_start_index,
            lidar_level_start_index=lidar_level_start_index,
            pc_range=pc_range,
            lidar2img=lidar2img,
            img_metas=img_metas,
            attn_masks=attn_masks,
            reg_branches=reg_branches,
            query_embedding=query_embedding,
        )

        return inter_states


@TRANSFORMER_LAYER_SEQUENCE.register_module()
class DeformableTransformerDecoder(TransformerLayerSequence):
    """Implements the decoder in DETR3D transformer.
    Args:
        return_intermediate (bool): Whether to return intermediate outputs.
        coder_norm_cfg (dict): Config of last normalization layer. Default：
            `LN`.
    """

    def __init__(self, *args, post_norm_cfg=dict(type="LN"), **kwargs):
        super(DeformableTransformerDecoder, self).__init__(*args, **kwargs)
        if post_norm_cfg is not None:
            self.post_norm = build_norm_layer(post_norm_cfg, self.embed_dims)[1]
        else:
            self.post_norm = None

    def forward(
        self,
        query,
        query_pos,
        mlvl_feats,
        reference_points,
        spatial_flatten,
        lidar_feats,
        lidar_spatial_flatten,
        level_start_index,
        lidar_level_start_index,
        pc_range,
        lidar2img,
        img_metas,
        attn_masks,
        reg_branches=None,
        query_embedding=None,
    ):
        """Forward function for `Detr3DTransformerDecoder`.
        Args:
            query (Tensor): Input query with shape
                `(num_query, bs, embed_dims)`.
            reference_points (Tensor): The reference
                points of offset. has shape
                (bs, num_query, 4) when as_two_stage,
                otherwise has shape ((bs, num_query, 2).
            reg_branch: (obj:`nn.ModuleList`): Used for
                refining the regression results. Only would
                be passed when with_box_refine is True,
                otherwise would be passed a `None`.
        Returns:
            Tensor: Results with shape [1, num_query, bs, embed_dims] when
                return_intermediate is `False`, otherwise it has shape
                [num_layers, num_query, bs, embed_dims].
        """

        intermediate = []
        intermediate_reference_points = []

        for lid, layer in enumerate(self.layers):

            query = layer(
                query,
                query_pos,
                mlvl_feats,
                reference_points,
                spatial_flatten,
                lidar_feats,
                lidar_spatial_flatten,
                level_start_index,
                lidar_level_start_index,
                pc_range,
                lidar2img,
                img_metas,
                attn_masks,
            )
            if reg_branches is not None:
                tmp = reg_branches(query)
                new_reference_points = inverse_sigmoid(reference_points) + tmp
                new_reference_points = new_reference_points.sigmoid()
                reference_points = new_reference_points
                query_pos = get_query_embeds(
                    reference_points, radar_points=None, query_embedding=query_embedding, bev_query_embed=None
                )

            if self.post_norm is not None:
                intermediate.append(self.post_norm(query))
                intermediate_reference_points.append(reference_points)
            else:
                intermediate.append(query)
                intermediate_reference_points.append(reference_points)

        return torch.stack(intermediate), torch.stack(intermediate_reference_points)


@TRANSFORMER_LAYER.register_module()
class DeformableTransformerDecoderLayer(BaseModule):
    """Base `TransformerLayer` for vision transformer.

    It can be built from `mmcv.ConfigDict` and support more flexible
    customization, for example, using any number of `FFN or LN ` and
    use different kinds of `attention` by specifying a list of `ConfigDict`
    named `attn_cfgs`. It is worth mentioning that it supports `prenorm`
    when you specifying `norm` as the first element of `operation_order`.
    More details about the `prenorm`: `On Layer Normalization in the
    Transformer Architecture <https://arxiv.org/abs/2002.04745>`_ .

    Args:
        attn_cfgs (list[`mmcv.ConfigDict`] | obj:`mmcv.ConfigDict` | None )):
            Configs for `self_attention` or `cross_attention` modules,
            The order of the configs in the list should be consistent with
            corresponding attentions in operation_order.
            If it is a dict, all of the attention modules in operation_order
            will be built with this config. Default: None.
        ffn_cfgs (list[`mmcv.ConfigDict`] | obj:`mmcv.ConfigDict` | None )):
            Configs for FFN, The order of the configs in the list should be
            consistent with corresponding ffn in operation_order.
            If it is a dict, all of the attention modules in operation_order
            will be built with this config.
        operation_order (tuple[str]): The execution order of operation
            in transformer. Such as ('self_attn', 'norm', 'ffn', 'norm').
            Support `prenorm` when you specifying first element as `norm`.
            Default：None.
        norm_cfg (dict): Config dict for normalization layer.
            Default: dict(type='LN').
        init_cfg (obj:`mmcv.ConfigDict`): The Config for initialization.
            Default: None.
        batch_first (bool): Key, Query and Value are shape
            of (batch, n, embed_dim)
            or (n, batch, embed_dim). Default to False.
    """

    def __init__(
        self,
        attn_cfgs=None,
        ffn_cfgs=dict(
            type="FFN",
            embed_dims=256,
            feedforward_channels=1024,
            num_fcs=2,
            ffn_drop=0.0,
            act_cfg=dict(type="ReLU", inplace=True),
        ),
        operation_order=None,
        norm_cfg=dict(type="LN"),
        init_cfg=None,
        batch_first=False,
        with_cp=True,
        **kwargs,
    ):
        super().__init__(init_cfg)

        self.batch_first = batch_first

        assert set(operation_order) & {"self_attn", "norm", "ffn", "cross_attn"} == set(operation_order), (
            f"The operation_order of"
            f" {self.__class__.__name__} should "
            f"contains all four operation type "
            f"{['self_attn', 'norm', 'ffn', 'cross_attn']}"
        )

        num_attn = operation_order.count("self_attn") + operation_order.count("cross_attn")
        if isinstance(attn_cfgs, dict):
            attn_cfgs = [copy.deepcopy(attn_cfgs) for _ in range(num_attn)]
        else:
            assert num_attn == len(attn_cfgs), (
                f"The length "
                f"of attn_cfg {num_attn} is "
                f"not consistent with the number of attention"
                f"in operation_order {operation_order}."
            )

        self.num_attn = num_attn
        self.operation_order = operation_order
        self.norm_cfg = norm_cfg
        self.pre_norm = operation_order[0] == "norm"
        self.attentions = ModuleList()

        index = 0
        for operation_name in operation_order:
            if operation_name in ["self_attn", "cross_attn"]:
                if "batch_first" in attn_cfgs[index]:
                    assert self.batch_first == attn_cfgs[index]["batch_first"]
                else:
                    attn_cfgs[index]["batch_first"] = self.batch_first
                attention = build_attention(attn_cfgs[index])
                # Some custom attentions used as `self_attn`
                # or `cross_attn` can have different behavior.
                attention.operation_name = operation_name
                self.attentions.append(attention)
                index += 1

        self.embed_dims = self.attentions[0].embed_dims

        self.ffns = ModuleList()
        num_ffns = operation_order.count("ffn")
        if isinstance(ffn_cfgs, dict):
            ffn_cfgs = ConfigDict(ffn_cfgs)
        if isinstance(ffn_cfgs, dict):
            ffn_cfgs = [copy.deepcopy(ffn_cfgs) for _ in range(num_ffns)]
        assert len(ffn_cfgs) == num_ffns
        for ffn_index in range(num_ffns):
            if "embed_dims" not in ffn_cfgs[ffn_index]:
                ffn_cfgs[ffn_index]["embed_dims"] = self.embed_dims
            else:
                assert ffn_cfgs[ffn_index]["embed_dims"] == self.embed_dims
            self.ffns.append(build_feedforward_network(ffn_cfgs[ffn_index], dict(type="FFN")))

        self.norms = ModuleList()
        num_norms = operation_order.count("norm")
        for _ in range(num_norms):
            self.norms.append(build_norm_layer(norm_cfg, self.embed_dims)[1])

        self.use_checkpoint = with_cp

    def _forward(
        self,
        query,
        query_pos,
        mlvl_feats,
        reference_points,
        spatial_flatten,
        lidar_feats,
        lidar_spatial_flatten,
        level_start_index,
        lidar_level_start_index,
        pc_range,
        lidar2img,
        img_metas,
        attn_masks=None,
        query_key_padding_mask=None,
        key_padding_mask=None,
        **kwargs,
    ):
        """Forward function for `TransformerDecoderLayer`.

        **kwargs contains some specific arguments of attentions.

        Args:
            query (Tensor): The input query with shape
                [num_queries, bs, embed_dims] if
                self.batch_first is False, else
                [bs, num_queries embed_dims].
            key (Tensor): The key tensor with shape [num_keys, bs,
                embed_dims] if self.batch_first is False, else
                [bs, num_keys, embed_dims] .
            value (Tensor): The value tensor with same shape as `key`.
            query_pos (Tensor): The positional encoding for `query`.
                Default: None.
            key_pos (Tensor): The positional encoding for `key`.
                Default: None.
            attn_masks (List[Tensor] | None): 2D Tensor used in
                calculation of corresponding attention. The length of
                it should equal to the number of `attention` in
                `operation_order`. Default: None.
            query_key_padding_mask (Tensor): ByteTensor for `query`, with
                shape [bs, num_queries]. Only used in `self_attn` layer.
                Defaults to None.
            key_padding_mask (Tensor): ByteTensor for `query`, with
                shape [bs, num_keys]. Default: None.

        Returns:
            Tensor: forwarded results with shape [num_queries, bs, embed_dims].
        """

        norm_index = 0
        attn_index = 0
        ffn_index = 0
        identity = query
        if attn_masks is None:
            attn_masks = [None for _ in range(self.num_attn)]
        elif isinstance(attn_masks, torch.Tensor):
            attn_masks = [copy.deepcopy(attn_masks) for _ in range(self.num_attn)]
            warnings.warn(f"Use same attn_mask in all attentions in " f"{self.__class__.__name__} ")
        else:
            assert len(attn_masks) == self.num_attn, (
                f"The length of "
                f"attn_masks {len(attn_masks)} must be equal "
                f"to the number of attention in "
                f"operation_order {self.num_attn}"
            )

        for layer in self.operation_order:
            if layer == "self_attn":
                temp_key = temp_value = query  # FIXME if query zero init
                # temp_value = query_pos
                query = self.attentions[attn_index](
                    query,
                    temp_key,
                    temp_value,
                    identity if self.pre_norm else None,
                    query_pos=query_pos,
                    key_pos=query_pos,
                    attn_mask=attn_masks[attn_index],
                    key_padding_mask=query_key_padding_mask,
                    **kwargs,
                )
                attn_index += 1
                identity = query

            elif layer == "norm":
                query = self.norms[norm_index](query)
                norm_index += 1

            elif layer == "cross_attn":
                query = self.attentions[attn_index](
                    query,
                    query_pos,
                    mlvl_feats,
                    reference_points,
                    spatial_flatten,
                    lidar_feats,
                    lidar_spatial_flatten,
                    level_start_index,
                    lidar_level_start_index,
                    pc_range,
                    lidar2img,
                    img_metas,
                    **kwargs,
                )
                attn_index += 1
                identity = query

            elif layer == "ffn":
                query = self.ffns[ffn_index](query, identity if self.pre_norm else None)
                ffn_index += 1

        return query

    def forward(
        self,
        query,
        query_pos,
        mlvl_feats,
        reference_points,
        spatial_flatten,
        lidar_feats,
        lidar_spatial_flatten,
        level_start_index,
        lidar_level_start_index,
        pc_range,
        lidar2img,
        img_metas,
        attn_masks=None,
        query_key_padding_mask=None,
        key_padding_mask=None,
    ):
        """Forward function for `TransformerCoder`.
        Returns:
            Tensor: forwarded results with shape [num_query, bs, embed_dims].
        """

        if self.use_checkpoint and self.training:
            x = cp.checkpoint(
                self._forward,
                query,
                query_pos,
                mlvl_feats,
                reference_points,
                spatial_flatten,
                lidar_feats,
                lidar_spatial_flatten,
                level_start_index,
                lidar_level_start_index,
                pc_range,
                lidar2img,
                img_metas,
                attn_masks,
                query_key_padding_mask,
                key_padding_mask,
            )
        else:
            x = self._forward(
                query,
                query_pos,
                mlvl_feats,
                reference_points,
                spatial_flatten,
                lidar_feats,
                lidar_spatial_flatten,
                level_start_index,
                lidar_level_start_index,
                pc_range,
                lidar2img,
                img_metas,
                attn_masks,
                query_key_padding_mask,
                key_padding_mask,
            )
        return x


@ATTENTION.register_module()
class DeformableFeatureAggregationCuda(BaseModule):
    def __init__(
        self,
        embed_dims=256,
        num_groups=8,
        num_levels=4,
        num_cams=6,
        dropout=0.1,
        num_pts=13,
        im2col_step=64,
        batch_first=True,
        bias=1.0,
        with_lidar=False,
        with_img=True,
        lidar_pc_range=[-32.0, -0.0, -5.0, 32.0, 120, 3.0],
    ):
        super(DeformableFeatureAggregationCuda, self).__init__()
        self.embed_dims = embed_dims
        self.num_groups = num_groups
        self.group_dims = self.embed_dims // self.num_groups
        self.num_levels = num_levels
        self.num_cams = num_cams
        self.with_img = with_img
        self.with_lidar = with_lidar
        if self.with_img:
            self.weights_fc = nn.Linear(self.embed_dims, self.num_groups * self.num_levels * num_pts)
            self.cam_embed = nn.Sequential(
                nn.Linear(12, self.embed_dims // 2),
                nn.ReLU(inplace=True),
                nn.Linear(self.embed_dims // 2, self.embed_dims),
                nn.ReLU(inplace=True),
                nn.LayerNorm(self.embed_dims),
            )
        if self.with_lidar:
            self.weights_lidar_fc = nn.Linear(self.embed_dims, self.num_groups * self.num_levels * num_pts)
            self.lidar_pc_range = lidar_pc_range

        self.output_proj = nn.Linear(self.embed_dims, self.embed_dims)
        self.learnable_fc = nn.Linear(self.embed_dims, num_pts * 3)

        self.drop = nn.Dropout(dropout)
        self.im2col_step = im2col_step
        self.bias = bias

    def init_weight(self):
        constant_init(self.weights_fc, val=0.0, bias=0.0)
        xavier_init(self.output_proj, distribution="uniform", bias=0.0)
        nn.init.uniform_(self.learnable_fc.bias.data, -self.bias, self.bias)

    def forward(
        self,
        instance_feature,
        query_pos,
        feat_flatten,
        reference_points,
        spatial_flatten,
        lidar_feats,
        lidar_spatial_flatten,
        level_start_index,
        lidar_level_start_index,
        pc_range,
        lidar2img_mat,
        img_metas,
    ):
        lidar2img_mat = lidar2img_mat.squeeze(1)  # drop ts axis
        bs, num_anchor = reference_points.shape[:2]
        reference_points = get_global_pos(reference_points, pc_range)
        key_points = reference_points.unsqueeze(-2) + self.learnable_fc(instance_feature).reshape(bs, num_anchor, -1, 3)

        if self.with_img:
            weights = self._get_weights(instance_feature, query_pos, lidar2img_mat)
            features = self.feature_sampling(
                feat_flatten, spatial_flatten, level_start_index, key_points, weights, lidar2img_mat, img_metas
            )

        # output = self.output_proj(features)

        if self.with_lidar:
            lidar_weights = self._get_lidar_weights(instance_feature, query_pos)
            lidar_features = self.feature_sampling_lidar(
                lidar_feats, lidar_spatial_flatten, lidar_level_start_index, key_points, lidar_weights, pc_range
            )
            # output = self.output_proj(features + lidar_features)

        if self.with_img and self.with_lidar:
            output = self.output_proj(features + lidar_features)
        elif self.with_img:
            output = self.output_proj(features)
        else:
            output = self.output_proj(lidar_features)

        output = self.drop(output) + instance_feature
        return output

    def _get_weights(self, instance_feature, anchor_embed, lidar2img_mat):
        bs, num_anchor = instance_feature.shape[:2]

        lidar2img = lidar2img_mat[..., :3, :].flatten(-2)
        cam_embed = self.cam_embed(lidar2img)  # B, N, C
        feat_pos = (instance_feature + anchor_embed).unsqueeze(2) + cam_embed.unsqueeze(1)
        weights = self.weights_fc(feat_pos).reshape(bs, num_anchor, -1, self.num_groups).softmax(dim=-2)
        weights = (
            weights.reshape(bs, num_anchor, self.num_cams, -1, self.num_groups).permute(0, 2, 1, 4, 3).contiguous()
        )
        return weights.flatten(end_dim=1)

    def _get_lidar_weights(self, instance_feature, anchor_embed):
        bs, num_anchor = instance_feature.shape[:2]

        feat_pos = (instance_feature + anchor_embed).unsqueeze(2)
        weights = self.weights_lidar_fc(feat_pos).reshape(bs, num_anchor, -1, self.num_groups).softmax(dim=-2)
        weights = weights.reshape(bs, num_anchor, 1, -1, self.num_groups).permute(0, 2, 1, 4, 3).contiguous()
        return weights.flatten(end_dim=1)

    def feature_sampling(
        self, feat_flatten, spatial_flatten, level_start_index, key_points, weights, lidar2img_mat, img_metas
    ):
        bs, num_anchor, _ = key_points.shape[:3]

        pts_extand = torch.cat([key_points, torch.ones_like(key_points[..., :1])], dim=-1)
        points_2d = torch.matmul(lidar2img_mat[:, :, None, None], pts_extand[:, None, ..., None]).squeeze(-1)

        points_2d = points_2d[..., :2] / torch.clamp(points_2d[..., 2:3], min=1e-5)
        points_2d[..., 0:1] = points_2d[..., 0:1] / img_metas["pad_shape"][1]
        points_2d[..., 1:2] = points_2d[..., 1:2] / img_metas["pad_shape"][0]

        points_2d = points_2d.flatten(end_dim=1)  # [b*6, 900, 13, 2]
        points_2d = points_2d[:, :, None, None, :, :].repeat(1, 1, self.num_groups, self.num_levels, 1, 1)

        bn, num_value, _ = feat_flatten.size()
        feat_flatten = feat_flatten.reshape(bn, num_value, self.num_groups, -1).contiguous()
        # attention_weights = weights * mask
        output = MultiScaleDeformableAttnFunction.apply(
            feat_flatten, spatial_flatten, level_start_index, points_2d, weights, self.im2col_step
        )

        output = output.reshape(bs, self.num_cams, num_anchor, -1)

        return output.sum(1)

    def feature_sampling_lidar(self, feat_flatten, spatial_flatten, level_start_index, key_points, weights, pc_range):
        bs, num_anchor, _ = key_points.shape[:3]  # 原始3D 位置

        pts_extand = key_points[..., :2]  # BEV
        points_2d = pts_extand
        points_2d[..., 0:1] = (pts_extand[..., 0:1] - self.lidar_pc_range[0]) / (
            self.lidar_pc_range[3] - self.lidar_pc_range[0]
        )
        points_2d[..., 1:2] = (pts_extand[..., 1:2] - self.lidar_pc_range[1]) / (
            self.lidar_pc_range[4] - self.lidar_pc_range[1]
        )

        points_2d = points_2d.clamp(min=0.0, max=1.0)
        points_2d = points_2d[:, :, None, None, :, :].repeat(1, 1, self.num_groups, self.num_levels, 1, 1)

        bn, num_value, _ = feat_flatten.size()
        feat_flatten = feat_flatten.reshape(bn, num_value, self.num_groups, -1).contiguous()
        # attention_weights = weights * mask
        output = MultiScaleDeformableAttnFunction.apply(
            feat_flatten, spatial_flatten, level_start_index, points_2d, weights, self.im2col_step
        )

        output = output.reshape(bs, 1, num_anchor, -1)

        return output.sum(1)
