import torch
import torch.nn as nn


def get_paddings_indicator(actual_num, max_num, axis=0):
    """Create boolean mask by actually number of a padded tensor.
    actual_num tensor
    max_num scalar
    """

    actual_num = torch.unsqueeze(actual_num, axis + 1)
    # tiled_actual_num: [N, M, 1]
    max_num_shape = [1] * len(actual_num.shape)
    max_num_shape[axis + 1] = -1
    max_num = torch.arange(max_num, dtype=torch.int, device=actual_num.device).view(max_num_shape)
    # tiled_actual_num: [[3,3,3,3,3], [4,4,4,4,4], [2,2,2,2,2]]
    # tiled_max_num: [[0,1,2,3,4], [0,1,2,3,4], [0,1,2,3,4]]
    paddings_indicator = actual_num.int() > max_num
    # paddings_indicator shape: [batch_size, max_num]
    return paddings_indicator


class PFNLayer(nn.Module):
    def __init__(
        self,
    ):
        super().__init__()
        self.name = "PFNLayer"

    def forward(self, inputs):
        x_max = torch.max(inputs, dim=1, keepdim=True)[0]
        return x_max


class PillarFeatureNet(nn.Module):
    def __init__(
        self,
        voxel_size,
        point_cloud_range,
    ):

        super().__init__()
        self.name = "PillarFeatureNet"
        self.pfn_layer = PFNLayer()

        # Need pillar (voxel) size and x/y offset in order to calculate pillar offset
        self.vx = voxel_size[0]
        self.vy = voxel_size[1]
        self.x_offset = self.vx / 2 + point_cloud_range[0]
        self.y_offset = self.vy / 2 + point_cloud_range[1]

    def forward(self, features, num_voxels, coors):
        """
        features shape=(num_non_empty_voxel, max_num_points_per_voxel, 4) 每个voxel中最多有max_num_points_per_voxel个point, 若不足则后面的points为(0,0,0,0)
        num_voxels shape=(num_non_empty_voxel) 每个voxel中point的数量
        coors shape=(num_non_empty_voxel, [batch_size, z, y, x])

        """
        # Find distance of x, y, z and r from cluster center
        points_mean = features.sum(dim=1, keepdim=True) / num_voxels.type_as(features).view(-1, 1, 1)
        features -= points_mean
        # 把不满的voxel里padding的点的设置为0
        voxel_count = features.shape[1]
        mask = get_paddings_indicator(num_voxels, voxel_count, axis=0)
        mask = torch.unsqueeze(mask, -1).type_as(features)
        features *= mask

        # 去掉试试看
        features = self.pfn_layer(features)

        features = torch.cat(
            [
                features.squeeze(),
                points_mean.squeeze(),
                coors[:, 3].float().unsqueeze(1) * self.vx + self.x_offset,
                coors[:, 2].float().unsqueeze(1) * self.vy + self.y_offset,
            ],
            dim=-1,
        )
        return features
