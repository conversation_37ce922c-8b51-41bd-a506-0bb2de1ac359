import torch
import math
import pickle
import numpy as np

from torch import nn
from torch.nn.parameter import Parameter
from torch.nn.init import kaiming_uniform_
from os.path import exists, join
from os import makedirs


def spherical_Lloyd(
    radius,
    num_cells,
    dimension=3,
    fixed="center",
    approximation="monte-carlo",
    approx_n=5000,
    max_iter=500,
    momentum=0.9,
):
    """Creation of kernel point via Lloyd algorithm. We use an approximation of the algorithm, and compute the Voronoi
    cell centers with discretization  of space. The exact formula is not trivial with part of the sphere as sides.

    Args:
        radius (float): Radius of the kernels
        num_cells (int): Number of cell (kernel points) in the Voronoi diagram.
        dimension (int, optional): dimension of the space. Defaults to 3.
        fixed (str, optional): fix position of certain kernel points ('none', 'center' or 'verticals'). Defaults to "center".
        approximation (str, optional): Approximation method for <PERSON>'s algorithm ('discretization', 'monte-carlo'). Defaults to "monte-carlo".
        approx_n (int, optional): Number of point used for approximation. Defaults to 5000.
        max_iter (int, optional): Maximum nu;ber of iteration for the algorithm. Defaults to 500.
        momentum (float, optional): Momentum of the low pass filter smoothing kernel point positions. Defaults to 0.9.
    Returns:
        points (float): [num_kernels, num_points, dimension]
    """
    radius0 = 1.0
    kernel_points = np.zeros((0, dimension))
    while kernel_points.shape[0] < num_cells:
        new_points = np.random.rand(num_cells, dimension) * 2 * radius0 - radius0
        kernel_points = np.vstack((kernel_points, new_points))
        d2 = np.sum(np.power(kernel_points, 2), axis=1)
        kernel_points = kernel_points[np.logical_and(d2 < radius0 ** 2, (0.9 * radius0) ** 2 < d2), :]
    kernel_points = kernel_points[:num_cells, :].reshape((num_cells, -1))

    if fixed == "center":
        kernel_points[0, :] *= 0
    if fixed == "verticals":
        kernel_points[:3, :] *= 0
        kernel_points[1, -1] += 2 * radius0 / 3
        kernel_points[2, -1] -= 2 * radius0 / 3

    if approximation == "discretization":
        side_n = int(np.floor(approx_n ** (1.0 / dimension)))
        dl = 2 * radius0 / side_n
        coords = np.arange(-radius0 + dl / 2, radius0, dl)
        if dimension == 2:
            x, y = np.meshgrid(coords, coords)
            X = np.vstack((np.ravel(x), np.ravel(y))).T
        elif dimension == 3:
            x, y, z = np.meshgrid(coords, coords, coords)
            X = np.vstack((np.ravel(x), np.ravel(y), np.ravel(z))).T
        elif dimension == 4:
            x, y, z, t = np.meshgrid(coords, coords, coords, coords)
            X = np.vstack((np.ravel(x), np.ravel(y), np.ravel(z), np.ravel(t))).T
        else:
            raise ValueError("Unsupported dimension (max is 4)")
    elif approximation == "monte-carlo":
        X = np.zeros((0, dimension))
    else:
        raise ValueError('Wrong approximation method chosen: "{:s}"'.format(approximation))

    d2 = np.sum(np.power(X, 2), axis=1)
    X = X[d2 < radius0 * radius0, :]
    max_moves = np.zeros((0,))

    for iter in range(max_iter):
        if approximation == "monte-carlo":
            X = np.random.rand(approx_n, dimension) * 2 * radius0 - radius0
            d2 = np.sum(np.power(X, 2), axis=1)
            X = X[d2 < radius0 * radius0, :]
        differences = np.expand_dims(X, 1) - kernel_points
        sq_distances = np.sum(np.square(differences), axis=2)
        cell_inds = np.argmin(sq_distances, axis=1)
        centers = []
        for c in range(num_cells):
            bool_c = cell_inds == c
            num_c = np.sum(bool_c.astype(np.int32))
            if num_c > 0:
                centers.append(np.sum(X[bool_c, :], axis=0) / num_c)
            else:
                centers.append(kernel_points[c])
        centers = np.vstack(centers)
        moves = (1 - momentum) * (centers - kernel_points)
        kernel_points += moves
        max_moves = np.append(max_moves, np.max(np.linalg.norm(moves, axis=1)))

        if fixed == "center":
            kernel_points[0, :] *= 0
        if fixed == "verticals":
            kernel_points[0, :] *= 0
            kernel_points[:3, :-1] *= 0
    return kernel_points * radius


def kernel_point_optimization(
    radius,
    num_points,
    num_kernels=1,
    dimension=3,
    fixed="center",
    ratio=0.66,
):
    """Creation of kernel point via optimization of potentials.

    Args:
        radius (float): Radius of the kernels
        num_points (int): points composing kernels
        num_kernels (int, optional): number of wanted kernels. Defaults to 1.
        dimension (int, optional):dimension of the space. Defaults to 3.
        fixed (str, optional): fix position of certain kernel points ('none', 'center' or 'verticals'). Defaults to "center".
        ratio (float, optional): ratio of the radius where you want the kernels points to be placed. Defaults to 0.66.

    Returns:
        (points,saved_gradient_norms) (tuple): points with shape [num_kernels, num_points, dimension]
    """
    radius0 = 1
    diameter0 = 2
    moving_factor = 1e-2
    continuous_moving_decay = 0.9995
    thresh = 1e-5
    clip = 0.05 * radius0
    kernel_points = np.random.rand(num_kernels * num_points - 1, dimension) * diameter0 - radius0
    while kernel_points.shape[0] < num_kernels * num_points:
        new_points = np.random.rand(num_kernels * num_points - 1, dimension) * diameter0 - radius0
        kernel_points = np.vstack((kernel_points, new_points))
        d2 = np.sum(np.power(kernel_points, 2), axis=1)
        kernel_points = kernel_points[d2 < 0.5 * radius0 * radius0, :]
    kernel_points = kernel_points[: num_kernels * num_points, :].reshape((num_kernels, num_points, -1))

    if fixed == "center":
        kernel_points[:, 0, :] *= 0
    if fixed == "verticals":
        kernel_points[:, :3, :] *= 0
        kernel_points[:, 1, -1] += 2 * radius0 / 3
        kernel_points[:, 2, -1] -= 2 * radius0 / 3

    saved_gradient_norms = np.zeros((10000, num_kernels))
    old_gradient_norms = np.zeros((num_kernels, num_points))
    for iter in range(10000):
        A = np.expand_dims(kernel_points, axis=2)
        B = np.expand_dims(kernel_points, axis=1)
        interd2 = np.sum(np.power(A - B, 2), axis=-1)
        inter_grads = (A - B) / (np.power(np.expand_dims(interd2, -1), 3 / 2) + 1e-6)
        inter_grads = np.sum(inter_grads, axis=1)
        circle_grads = 10 * kernel_points
        gradients = inter_grads + circle_grads
        if fixed == "verticals":
            gradients[:, 1:3, :-1] = 0
        gradients_norms = np.sqrt(np.sum(np.power(gradients, 2), axis=-1))
        saved_gradient_norms[iter, :] = np.max(gradients_norms, axis=1)
        if fixed == "center" and np.max(np.abs(old_gradient_norms[:, 1:] - gradients_norms[:, 1:])) < thresh:
            break
        elif fixed == "verticals" and np.max(np.abs(old_gradient_norms[:, 3:] - gradients_norms[:, 3:])) < thresh:
            break
        elif np.max(np.abs(old_gradient_norms - gradients_norms)) < thresh:
            break
        old_gradient_norms = gradients_norms
        moving_dists = np.minimum(moving_factor * gradients_norms, clip)
        if fixed == "center":
            moving_dists[:, 0] = 0
        if fixed == "verticals":
            moving_dists[:, 0] = 0
        kernel_points -= np.expand_dims(moving_dists, -1) * gradients / np.expand_dims(gradients_norms + 1e-6, -1)
        moving_factor *= continuous_moving_decay
    r = np.sqrt(np.sum(np.power(kernel_points, 2), axis=-1))
    kernel_points *= ratio / np.mean(r[:, 1:])
    return kernel_points * radius, saved_gradient_norms


def create_3D_rotations(axis, angle):
    """Create rotation matrices from a list of axes and angles. Code from wikipedia on quaternions
    Args:
        axis (float32): [N, 3]
        angle (float32): [N,]

    Returns:
        (float32): [N, 3, 3]
    """
    t1 = np.cos(angle)
    t2 = 1 - t1
    t3 = axis[:, 0] * axis[:, 0]
    t6 = t2 * axis[:, 0]
    t7 = t6 * axis[:, 1]
    t8 = np.sin(angle)
    t9 = t8 * axis[:, 2]
    t11 = t6 * axis[:, 2]
    t12 = t8 * axis[:, 1]
    t15 = axis[:, 1] * axis[:, 1]
    t19 = t2 * axis[:, 1] * axis[:, 2]
    t20 = t8 * axis[:, 0]
    t24 = axis[:, 2] * axis[:, 2]
    R = np.stack(
        [
            t1 + t2 * t3,
            t7 - t9,
            t11 + t12,
            t7 + t9,
            t1 + t2 * t15,
            t19 - t20,
            t11 - t12,
            t19 + t20,
            t1 + t2 * t24,
        ],
        axis=1,
    )
    return np.reshape(R, (-1, 3, 3))


def load_kernels(radius, num_kpoints, dimension, fixed, lloyd=False):
    kernel_dir = "kernels/dispositions"
    if not exists(kernel_dir):
        makedirs(kernel_dir)
    if num_kpoints > 30:
        lloyd = True
    kernel_file = join(kernel_dir, "k_{:03d}_{:s}_{:d}D.ply".format(num_kpoints, fixed, dimension))
    if not exists(kernel_file):
        if lloyd:
            kernel_points = spherical_Lloyd(1.0, num_kpoints, dimension=dimension, fixed=fixed)
        else:
            kernel_points, grad_norms = kernel_point_optimization(
                1.0,
                num_kpoints,
                num_kernels=100,
                dimension=dimension,
                fixed=fixed,
            )
            best_k = np.argmin(grad_norms[-1, :])
            kernel_points = kernel_points[best_k, :, :]
        with open(kernel_file, "wb") as f:
            pickle.dump(kernel_points, f)
    else:
        with open(kernel_file, "rb") as f:
            kernel_points = pickle.load(f)
    R = np.eye(dimension)
    theta = np.random.rand() * 2 * np.pi
    if dimension == 2:
        if fixed != "vertical":
            c, s = np.cos(theta), np.sin(theta)
            R = np.array([[c, -s], [s, c]], dtype=np.float32)
    elif dimension == 3:
        if fixed != "vertical":
            c, s = np.cos(theta), np.sin(theta)
            R = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=np.float32)
        else:
            phi = (np.random.rand() - 0.5) * np.pi
            u = np.array([np.cos(theta) * np.cos(phi), np.sin(theta) * np.cos(phi), np.sin(phi)])
            alpha = np.random.rand() * 2 * np.pi
            R = create_3D_rotations(np.reshape(u, (1, -1)), np.reshape(alpha, (1, -1)))[0]
            R = R.astype(np.float32)

    kernel_points = kernel_points + np.random.normal(scale=0.01, size=kernel_points.shape)
    kernel_points = radius * kernel_points
    kernel_points = np.matmul(kernel_points, R)
    return kernel_points.astype(np.float32)


def gather(x, idx, method=2):
    """Implementation of a custom gather operation for faster backwards.
    Args:
        x (tensor): input with shape [N, D_1, ... D_d]
        idx (int): indexing with shape [n_1, ..., n_m]
        method (int, optional): Defaults to 2.
    Returns:
        x[idx]: [n_1, ..., n_m, D_1, ... D_d]
    """
    if method == 0:
        return x[idx]
    elif method == 1:
        x = x.unsqueeze(1)
        x = x.expand((-1, idx.shape[-1], -1))
        idx = idx.unsqueeze(2)
        idx = idx.expand((-1, -1, x.shape[-1]))
        return x.gather(0, idx)
    elif method == 2:
        for i, ni in enumerate(idx.size()[1:]):
            x = x.unsqueeze(i + 1)
            new_s = list(x.size())
            new_s[i + 1] = ni
            x = x.expand(new_s)
        n = len(idx.size())
        for i, di in enumerate(x.size()[n:]):
            idx = idx.unsqueeze(i + n)
            new_s = list(idx.size())
            new_s[i + n] = di
            idx = idx.expand(new_s)
        return x.gather(0, idx)
    else:
        raise ValueError("Unkown method")


def radius_gaussian(sq_r, sig, eps=1e-9):
    """Compute a radius gaussian

    Args:
        sq_r (tensor): input radiuses [dn, ..., d1, d0]
        sig (float): extents of gaussians [d1, d0] or [d0] or float
        eps (float, optional): Defaults to 1e-9.

    Returns:
        (tensor): gaussian of sq_r [dn, ..., d1, d0]
    """
    return torch.exp(-sq_r / (2 * sig ** 2 + eps))


class KPConv(nn.Module):
    def __init__(
        self,
        kernel_size,
        p_dim,
        in_channels,
        out_channels,
        KP_extent,
        radius,
        fixed_kernel_points="center",
        KP_influence="linear",
        aggregation_mode="sum",
        deformable=False,
        modulated=False,
    ):
        """Initialize parameters for KPConvDeformable.

        Args:
            kernel_size (int): Number of kernel points.
            p_dim (int): dimension of the point space.
            in_channels (int): dimension of input features.
            out_channels (int): dimension of output features.
            KP_extent (float): influence radius of each kernel point.
            radius (float): radius used for kernel point init.
            fixed_kernel_points (str, optional): fix position of certain kernel points ('none', 'center' or 'verticals').
            KP_influence (str, optional): influence function of the kernel points ('constant', 'linear', 'gaussian').
            aggregation_mode (str, optional): choose to sum influences, or only keep the closest ('closest', 'sum').
            deformable (bool, optional): choose deformable or not.
            modulated (bool, optional): choose if kernel weights are modulated in addition to deformed.
        """
        super(KPConv, self).__init__()

        self.K = kernel_size
        self.p_dim = p_dim
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.KP_extent = KP_extent
        self.radius = radius
        self.fixed_kernel_points = fixed_kernel_points
        self.KP_influence = KP_influence
        self.aggregation_mode = aggregation_mode
        self.deformable = deformable
        self.modulated = modulated

        self.min_d2 = None
        self.deformed_KP = None
        self.offset_features = None
        self.weights = Parameter(
            torch.zeros((self.K, in_channels, out_channels), dtype=torch.float32),
            requires_grad=True,
        )

        if deformable:
            if modulated:
                self.offset_dim = (self.p_dim + 1) * self.K
            else:
                self.offset_dim = self.p_dim * self.K
            self.offset_conv = KPConv(
                self.K,
                self.p_dim,
                self.in_channels,
                self.offset_dim,
                KP_extent,
                radius,
                fixed_kernel_points=fixed_kernel_points,
                KP_influence=KP_influence,
                aggregation_mode=aggregation_mode,
            )
            self.offset_bias = Parameter(torch.zeros(self.offset_dim, dtype=torch.float32), requires_grad=True)

        else:
            self.offset_dim = None
            self.offset_conv = None
            self.offset_bias = None

        self.reset_parameters()
        self.kernel_points = self.init_KP()

    def reset_parameters(self):
        kaiming_uniform_(self.weights, a=math.sqrt(5))
        if self.deformable:
            nn.init.zeros_(self.offset_bias)

    def init_KP(self):
        """Initialize the kernel point positions in a sphere

        Returns:
            tensor: the tensor of kernel points
        """
        K_points_numpy = load_kernels(self.radius, self.K, dimension=self.p_dim, fixed=self.fixed_kernel_points)
        return Parameter(torch.tensor(K_points_numpy, dtype=torch.float32), requires_grad=False)

    def forward(self, q_pts, s_pts, neighb_inds, x):
        if self.deformable:
            self.offset_features = self.offset_conv(q_pts, s_pts, neighb_inds, x) + self.offset_bias
            if self.modulated:
                unscaled_offsets = self.offset_features[:, : self.p_dim * self.K]
                unscaled_offsets = unscaled_offsets.view(-1, self.K, self.p_dim)
                modulations = 2 * torch.sigmoid(self.offset_features[:, self.p_dim * self.K :])
            else:
                unscaled_offsets = self.offset_features.view(-1, self.K, self.p_dim)
                modulations = None
            offsets = unscaled_offsets * self.KP_extent
        else:
            offsets = None
            modulations = None
        s_pts = torch.cat((s_pts, torch.zeros_like(s_pts[:1, :]) + 1e6), 0)
        neighbors = s_pts[neighb_inds, :]
        neighbors = neighbors - q_pts.unsqueeze(1)
        if self.deformable:
            self.deformed_KP = offsets + self.kernel_points
            deformed_K_points = self.deformed_KP.unsqueeze(1)
        else:
            deformed_K_points = self.kernel_points
        neighbors.unsqueeze_(2)
        differences = neighbors - deformed_K_points
        sq_distances = torch.sum(differences ** 2, dim=3)
        if self.deformable:
            self.min_d2, _ = torch.min(sq_distances, dim=1)
            in_range = torch.any(sq_distances < self.KP_extent ** 2, dim=2).type(torch.int32)
            new_max_neighb = torch.max(torch.sum(in_range, dim=1))
            neighb_row_bool, neighb_row_inds = torch.topk(in_range, new_max_neighb.item(), dim=1)
            new_neighb_inds = neighb_inds.gather(1, neighb_row_inds, sparse_grad=False)
            neighb_row_inds.unsqueeze_(2)
            neighb_row_inds = neighb_row_inds.expand(-1, -1, self.K)
            sq_distances = sq_distances.gather(1, neighb_row_inds, sparse_grad=False)
            new_neighb_inds *= neighb_row_bool
            new_neighb_inds -= (neighb_row_bool.type(torch.int64) - 1) * int(s_pts.shape[0] - 1)
        else:
            new_neighb_inds = neighb_inds

        if self.KP_influence == "constant":
            all_weights = torch.ones_like(sq_distances)
            all_weights = torch.transpose(all_weights, 1, 2)
        elif self.KP_influence == "linear":
            all_weights = torch.clamp(1 - torch.sqrt(sq_distances) / self.KP_extent, min=0.0)
            all_weights = torch.transpose(all_weights, 1, 2)
        elif self.KP_influence == "gaussian":
            # Influence in gaussian of the distance.
            sigma = self.KP_extent * 0.3
            all_weights = radius_gaussian(sq_distances, sigma)
            all_weights = torch.transpose(all_weights, 1, 2)
        else:
            raise ValueError("Unknown influence function type (config.KP_influence)")

        if self.aggregation_mode == "closest":
            neighbors_1nn = torch.argmin(sq_distances, dim=2)
            all_weights *= torch.transpose(nn.functional.one_hot(neighbors_1nn, self.K), 1, 2)
        elif self.aggregation_mode != "sum":
            raise ValueError("Unknown convolution mode. Should be 'closest' or 'sum'")
        x = torch.cat((x, torch.zeros_like(x[:1, :])), 0)
        neighb_x = gather(x, new_neighb_inds)
        weighted_features = torch.matmul(all_weights, neighb_x)
        if self.deformable and self.modulated:
            weighted_features *= modulations.unsqueeze(2)
        weighted_features = weighted_features.permute((1, 0, 2))
        kernel_outputs = torch.matmul(weighted_features.to(torch.float32), self.weights.to(torch.float32))
        return torch.sum(kernel_outputs, dim=0)

    def __repr__(self):
        return "KPConv(radius: {:.2f}, in_feat: {:d}, out_feat: {:d})".format(
            self.radius, self.in_channels, self.out_channels
        )


class KPConvBackbone(nn.Module):
    def __init__(self, in_channels=256, out_channels=256, deformable=False):
        super(KPConvBackbone, self).__init__()
        self.kpconv = KPConv(
            kernel_size=15,
            p_dim=3,
            in_channels=in_channels,
            out_channels=out_channels,
            KP_extent=1.2,
            radius=0.60,
            deformable=deformable,
        )
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()

    def forward(self, x, pxyz, pknn, pmask):
        res = []
        for i in range(x.shape[0]):
            points = pxyz[i, ...]
            feats = x[i, ...].squeeze()
            feats = self.kpconv(points, points, pknn[i, ...], feats)
            mask = pmask[i, ...]
            feats[mask == 0, :] = 0
            res.append(feats.unsqueeze(2).transpose(0, 2).unsqueeze(2))
        res = torch.cat(res, axis=0)
        res = self.relu(self.bn(res))

        return res.squeeze(2).transpose(1, 2)
