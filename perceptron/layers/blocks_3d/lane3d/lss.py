"""
Copyright (C) 2020 NVIDIA Corporation.  All rights reserved.
Licensed under the NVIDIA Source Code License. See LICENSE at https://github.com/nv-tlabs/lift-splat-shoot.
Authors: <AUTHORS>
"""

import torch
from torch import nn
from perceptron.layers.blocks_3d.lane3d.utils import ResNet, gen_dx_bx, cumsum_trick, QuickCumsum
from perceptron_ops.voxel_pooling import voxel_pooling


class LSSTransform(nn.Module):
    def __init__(
        self,
        grid_conf,
        input_shape=(896, 512),  # (x, y)
        fuse_channel=768 + 384,
        d_model=512,
        camC=64,  # Cam feat dimension
        downsample=16,  # Downsample rate from input shape
    ):
        """
        Parameter Reference

        grid conf -- LSS Depth configuration
        """

        super(LSSTransform, self).__init__()
        self.grid_conf = grid_conf

        dx, bx, nx = gen_dx_bx(self.grid_conf["xbound"], self.grid_conf["ybound"], self.grid_conf["zbound"])

        self.dx = nn.Parameter(dx, requires_grad=False)
        self.bx = nn.Parameter(bx, requires_grad=False)
        self.nx = nn.Parameter(nx, requires_grad=False)

        self.downsample = downsample
        self.camC = camC
        self.input_shape = input_shape
        self.frustum = self.create_frustum()

        # D x H/downsample x D/downsample x 3
        self.D, _, _, _ = self.frustum.shape

        # toggle using QuickCumsum vs. autograd
        self.use_quickcumsum = True

        # self.fuse = Up(fuse_channel, d_model)
        self.depthnet = nn.Conv2d(d_model, self.D + self.camC, kernel_size=1, padding=0)

        self.use_cuda_voxel_pooling = True

    def create_frustum(self):
        """
        Generate FoV Frustum
        """
        # make grid in image plane
        # TODO: Fill Final Dimension
        ogfW, ogfH = self.input_shape
        fH, fW = ogfH // self.downsample, ogfW // self.downsample
        ds = torch.arange(*self.grid_conf["dbound"], dtype=torch.float).view(-1, 1, 1).expand(-1, fH, fW)
        D, _, _ = ds.shape
        xs = torch.linspace(0, ogfW - 1, fW, dtype=torch.float).view(1, 1, fW).expand(D, fH, fW)
        ys = torch.linspace(0, ogfH - 1, fH, dtype=torch.float).view(1, fH, 1).expand(D, fH, fW)
        paddings = torch.ones_like(ds)

        # D x H x W x 3
        frustum = torch.stack((xs, ys, ds, paddings), -1)  # 构建三维点 (D, H, W, 3) -- 3 对应 (x, y z)
        return nn.Parameter(frustum, requires_grad=False)

    def get_geometry(self, intrins, ida_mats, sensor2ego_trans, sensor2ego_rots, post_rot_bda):
        """
        Determine the (x,y,z) locations (in the ego frame) of the points in the point cloud.

        Returns B x N x D x H/downsample x W/downsample x 3
        """
        (
            B,
            N,
            _,
        ) = sensor2ego_trans.shape

        # *undo* post-transformation
        # B x N x D x H x W x 3
        # 恢复到原有分辨率下
        points = self.frustum
        ida_mats = ida_mats.view(B, N, 1, 1, 1, 4, 4)
        points = ida_mats.inverse().matmul(points.unsqueeze(-1))

        # cam_to_ego
        points = torch.cat(
            (points[:, :, :, :, :, :2] * points[:, :, :, :, :, 2:3], points[:, :, :, :, :, 2:3]), 5
        )  # Z * (u,v,1)
        combine = sensor2ego_rots.matmul(torch.inverse(intrins)).float()
        points = combine.view(B, N, 1, 1, 1, 3, 3).matmul(points).squeeze(-1)
        points += sensor2ego_trans.view(B, N, 1, 1, 1, 3)

        if post_rot_bda is not None:
            post_rot_bda = post_rot_bda.unsqueeze(1).repeat(1, N, 1, 1).view(B, N, 1, 1, 1, 3, 3).float()
            points = (post_rot_bda @ points.unsqueeze(-1)).squeeze(-1)

        return points

    def voxel_pooling(self, geom_feats, x):
        B, N, D, H, W, C = x.shape
        Nprime = B * N * D * H * W

        # flatten x
        # x = x.reshape(Nprime, C)

        # flatten indices
        # B x N x D x H/downsample x W/downsample x 3
        geom_feats = ((geom_feats - (self.bx - self.dx / 2.0)) / self.dx).long()  # 对应的 Index

        if self.use_cuda_voxel_pooling:
            geom_feats[..., :2] = self.nx[:2] - geom_feats[..., :2] - 1
            x = voxel_pooling(geom_feats[..., [1, 0, 2]].int(), x.contiguous(), self.nx[[1, 0, 2]]).contiguous()
            return x

        # flatten x
        x = x.reshape(Nprime, C)

        geom_feats = geom_feats.view(Nprime, 3)
        batch_ix = torch.cat([torch.full([Nprime // B, 1], ix, device=x.device, dtype=torch.long) for ix in range(B)])
        geom_feats = torch.cat((geom_feats, batch_ix), 1)  # x, y, z, b

        # filter out points that are outside box
        kept = (
            (geom_feats[:, 0] >= 0)
            & (geom_feats[:, 0] < self.nx[0])
            & (geom_feats[:, 1] >= 0)
            & (geom_feats[:, 1] < self.nx[1])
            & (geom_feats[:, 2] >= 0)
            & (geom_feats[:, 2] < self.nx[2])
        )
        x = x[kept]
        geom_feats = geom_feats[kept]

        # get tensors from the same voxel next to each other
        ranks = (
            geom_feats[:, 0] * (self.nx[1] * self.nx[2] * B)
            + geom_feats[:, 1] * (self.nx[2] * B)
            + geom_feats[:, 2] * B
            + geom_feats[:, 3]
        )
        sorts = ranks.argsort()
        x, geom_feats, ranks = x[sorts], geom_feats[sorts], ranks[sorts]

        # cumsum trick
        if not self.use_quickcumsum:
            x, geom_feats = cumsum_trick(x, geom_feats, ranks)
        else:
            x, geom_feats = QuickCumsum.apply(x, geom_feats, ranks)

        # griddify (B x C x Z x X x Y)
        final = torch.zeros((B, C, self.nx[2], self.nx[1], self.nx[0]), device=x.device)
        final[geom_feats[:, 3], :, geom_feats[:, 2], geom_feats[:, 1], geom_feats[:, 0]] = x
        # print('check: ', final.shape)

        # collapse Z
        final = torch.cat(final.unbind(dim=2), 1)
        # Flip to current coordinate
        final = torch.flip(torch.rot90(final, k=1, dims=(2, 3)), dims=(3,))

        return final

    def forward(self, inputs):
        sensor2ego_mat = inputs["trans_cam2ego"]
        cam_feat = inputs["im_bkb_features"]
        fuse_feat = cam_feat[0]  # self.fuse(cam_feat[1], cam_feat[0])  # (BN, C, H, W)
        feat = self.depthnet(fuse_feat)
        rv_feat = feat[:, self.D : (self.D + self.camC)]  # (BN, C, H, W)
        depth = feat[:, : self.D].softmax(dim=1).type(feat.dtype)  # (BN, D, H, W)
        new_x = depth.unsqueeze(1) * feat[:, self.D : (self.D + self.camC)].unsqueeze(2)  # (BN, C, D, H, W)
        new_x = new_x.reshape(*inputs["images"].shape[:2], *new_x.shape[-4:])
        new_x = new_x.permute(0, 1, 3, 4, 5, 2)  # (B, N, D, H, W, C)
        intrins = inputs["trans_cam2img"]

        # B x N x D x H/downsample x W/downsample x 3: (x,y,z) locations (in the ego frame)
        ida_mats = inputs["ida_mats"]
        post_rot_bda = inputs["post_rot_bda"]
        sensor2ego_trans = sensor2ego_mat[:, :, :3, 3]  # 所有 Batch 相同
        sensor2ego_rots = sensor2ego_mat[:, :, :3, :3]

        geom = self.get_geometry(intrins, ida_mats, sensor2ego_trans, sensor2ego_rots, post_rot_bda)

        do_flip = inputs["extra_infos"].get("do_flip", None)
        if do_flip is not None:
            bs = len(do_flip)
            for i in range(bs):
                flip = do_flip[i].item()
                if flip:
                    # Flip Y Axis
                    geom[i, :, :, :, :, 1] = -1 * geom[i, :, :, :, :, 1]
                    # Change Image Order
                    geom[i] = geom[i, [0], ...]
                    # Flip Image X Coord
                    geom[i] = torch.flip(geom[i], dims=(3,))

        bev_feat = self.voxel_pooling(geom, new_x)
        return bev_feat, rv_feat, depth


class BEVDetEncoder(nn.Module):
    def __init__(self, lss_conf, res_conf=None):
        super(BEVDetEncoder, self).__init__()
        self.lss = LSSTransform(**lss_conf)
        self.resnet = None
        if res_conf is not None:
            self.resnet = ResNet(**res_conf)

    def forward(self, inputs):
        x, rv_feat, depth = self.lss(inputs)
        if self.resnet is not None:
            x = self.resnet(x)
        return [x], rv_feat, depth
