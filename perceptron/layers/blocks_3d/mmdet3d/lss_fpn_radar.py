import torch
from torch import nn
import numpy as np

from perceptron.layers.blocks_3d.det3d.kpconv_backbone import KPConvBackbone
from scipy.spatial.ckdtree import cKDTree as kdtree
from perceptron_ops.voxel_pooling import voxel_pooling


class LSSFPNRadar(nn.Module):
    def __init__(
        self,
        x_bound,
        y_bound,
        z_bound,
        use_dims,
        radar_backbone_conf,
    ):
        """Modified from `https://github.com/nv-tlabs/lift-splat-shoot`.

        Args:
            x_bound (list): Boundaries for x.
            y_bound (list): Boundaries for y.
            z_bound (list): Boundaries for z.
            use_dims(list): chosen dims.
            radar_backbone_conf (dict): Config for image backbone.
        """

        super(LSSFPNRadar, self).__init__()
        self.radar_backbone_conf = radar_backbone_conf

        self.register_buffer("voxel_size", torch.Tensor([row[2] for row in [x_bound, y_bound, z_bound]]))
        self.register_buffer(
            "voxel_coord", torch.Tensor([row[0] + row[2] / 2.0 for row in [x_bound, y_bound, z_bound]])
        )
        self.register_buffer(
            "voxel_num", torch.LongTensor([round((row[1] - row[0]) / row[2]) for row in [x_bound, y_bound, z_bound]])
        )
        self.use_dims = use_dims
        self.radar_backbone = self.build_backbone()
        self.x_bound = x_bound
        self.y_bound = y_bound

    def extract_knn_feat(self, radar_points):
        radar_points_xyz = radar_points[..., :3].cpu().numpy()
        B, _, _ = radar_points_xyz.shape
        radar_points_batch = list()
        for i in range(B):
            tree = kdtree(radar_points_xyz[i, ...])
            _, knns = tree.query(radar_points_xyz[i], k=self.radar_backbone_conf.get("knn", 7))
            radar_points_cpu = radar_points[i].cpu().numpy()
            radar_points_batch.append(np.concatenate((radar_points_cpu, knns), axis=1))
        radar_points_batch = torch.tensor(radar_points_batch).to(radar_points.device)
        return radar_points_batch

    def build_backbone(self):
        if self.radar_backbone_conf["type"] == "MLP":
            return nn.Sequential(
                nn.Linear(
                    in_features=self.radar_backbone_conf["input_channels"],
                    out_features=self.radar_backbone_conf["output_channels"],
                ),
                nn.BatchNorm1d(self.radar_backbone_conf["output_channels"]),
                nn.ReLU(),
            )
        if self.radar_backbone_conf["type"] == "KPConv":
            return KPConvBackbone(
                in_channels=self.radar_backbone_conf["input_channels"],
                out_channels=self.radar_backbone_conf["output_channels"],
                deformable=self.radar_backbone_conf["deformable"],
            )

    def forward(self, radar_points):
        sweep_radar_points = radar_points[:, 0, ...]
        valid_mask = (
            (sweep_radar_points[..., 1] < self.y_bound[1])
            & (sweep_radar_points[..., 0] < self.x_bound[1])
            & (sweep_radar_points[..., 1] > self.y_bound[0])
            & (sweep_radar_points[..., 0] > self.x_bound[0])
        )
        sweep_radar_points = sweep_radar_points * valid_mask.unsqueeze(-1)
        geom_xyz_radar = sweep_radar_points[..., :3]
        geom_xyz_radar = (
            (geom_xyz_radar - (self.voxel_coord - self.voxel_size / 2.0).to(geom_xyz_radar.device))
            / self.voxel_size.to(geom_xyz_radar.device)
        ).int()
        if self.radar_backbone_conf["type"] == "MLP":
            radar_feat = sweep_radar_points[..., self.use_dims]
            radar_feat = radar_feat[..., 0 : self.radar_backbone_conf["input_channels"]]
            B, N, _ = radar_feat.shape
            radar_points_feature = self.radar_backbone(radar_feat.view(-1, self.radar_backbone_conf["input_channels"]))
            radar_points_feature = radar_points_feature.reshape(B, N, self.radar_backbone_conf["output_channels"])
        if self.radar_backbone_conf["type"] == "KPConv":
            sweep_radar_points_mask = sweep_radar_points[..., -1]
            radar_used = sweep_radar_points[..., self.use_dims]
            knn_feat = self.extract_knn_feat(radar_used)
            radar_points_input = torch.cat([knn_feat, sweep_radar_points_mask.unsqueeze(-1)], dim=-1)
            B, N, _ = radar_points_input.shape
            radar_feat, radar_pxyz, radar_pknn, radar_mask = (
                radar_points_input[..., 0:13],
                radar_points_input[..., 0:3],
                radar_points_input[..., 13 : 13 + self.radar_backbone_conf.get("knn", 7)],
                radar_points_input[..., -1],
            )
            radar_points_feature = self.radar_backbone(radar_feat, radar_pxyz, radar_pknn.long(), radar_mask.long())
            radar_points_feature = radar_points_feature.reshape(B, N, self.radar_backbone_conf["output_channels"])
        radar_feature_map = voxel_pooling(geom_xyz_radar, radar_points_feature.contiguous(), self.voxel_num.cuda())
        return radar_feature_map.contiguous()
