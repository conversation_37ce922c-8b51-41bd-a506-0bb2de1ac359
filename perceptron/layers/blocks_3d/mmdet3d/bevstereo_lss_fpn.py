import torch
import math
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
from scipy.special import erf
from scipy.stats import norm

from mmdet.models.backbones.resnet import BasicBlock
from mmcv.cnn import build_conv_layer

from perceptron.layers.blocks_3d.mmdet3d.bevdepth_lss_fpn import BEVDepthLSSFPN as BaseLSSFPN
from perceptron.layers.blocks_3d.mmdet3d.bevdepth_lss_fpn import DepthNet as BaseDepthNet, ASPP, Mlp, SELayer
from perceptron_ops.voxel_pooling import voxel_pooling


class ConvBnReLU3D(nn.Module):
    """Implements of 3d convolution + batch normalization + ReLU."""

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int = 3,
        stride: int = 1,
        pad: int = 1,
        dilation: int = 1,
    ) -> None:
        """initialization method for convolution3D + batch normalization + relu module
        Args:
            in_channels: input channel number of convolution layer
            out_channels: output channel number of convolution layer
            kernel_size: kernel size of convolution layer
            stride: stride of convolution layer
            pad: pad of convolution layer
            dilation: dilation of convolution layer
        """
        super(ConvBnReLU3D, self).__init__()
        self.conv = nn.Conv3d(
            in_channels, out_channels, kernel_size, stride=stride, padding=pad, dilation=dilation, bias=False
        )
        self.bn = nn.BatchNorm3d(out_channels)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """forward method"""
        return F.relu(self.bn(self.conv(x)), inplace=True)


class GNET(nn.Module):
    def __init__(self, ch_in, ch_out=2):
        super(GNET, self).__init__()
        h_dim = 128
        self.gnet = nn.Sequential(
            nn.Conv2d(ch_in, h_dim, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(h_dim, h_dim, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(h_dim, h_dim, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(h_dim, ch_out, 1),
        )

    def forward(self, cost_volume, sigma_0):
        sigma_1 = self.gnet(cost_volume)
        sigma_new = (F.elu(sigma_1) + 1.0 + 1e-10) * sigma_0  # B, 3 x N_c, H, W
        return sigma_new


class DepthNet(BaseDepthNet):
    def __init__(self, in_channels, mid_channels, context_channels, depth_channels, d_bound, num_ranges=4):
        super(DepthNet, self).__init__(in_channels, mid_channels, context_channels, depth_channels)
        self.d_bound = d_bound
        self.context_conv = nn.Sequential(nn.Conv2d(mid_channels, context_channels, kernel_size=1, stride=1, padding=0))
        self.depth_feat_conv = nn.Sequential(
            BasicBlock(mid_channels, mid_channels),
            BasicBlock(mid_channels, mid_channels),
            BasicBlock(mid_channels, mid_channels),
            ASPP(mid_channels, mid_channels),
            build_conv_layer(
                cfg=dict(
                    type="DCN",
                    in_channels=mid_channels,
                    out_channels=mid_channels,
                    kernel_size=3,
                    padding=1,
                    groups=4,
                    im2col_step=128,
                )
            ),
        )
        self.num_ranges = num_ranges
        self.mu_sigma_range_net = nn.Sequential(
            BasicBlock(mid_channels, mid_channels),
            nn.ConvTranspose2d(mid_channels, mid_channels, 3, stride=2, padding=1, output_padding=1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(mid_channels, mid_channels, 3, stride=2, padding=1, output_padding=1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, num_ranges * 3, kernel_size=1, stride=1, padding=0),
        )
        self.mono_depth_net = nn.Sequential(
            BasicBlock(mid_channels, mid_channels),
            nn.Conv2d(mid_channels, depth_channels, kernel_size=1, stride=1, padding=0),
        )
        self.bn = nn.BatchNorm1d(27)
        self.depth_mlp = Mlp(27, mid_channels, mid_channels)
        self.depth_se = SELayer(mid_channels)  # NOTE: add camera-aware
        self.context_mlp = Mlp(27, mid_channels, mid_channels)
        self.context_se = SELayer(mid_channels)
        del self.depth_conv
        del self.mlp
        del self.se
        del self.aspp
        del self.depth_pred

    # @autocast(False)
    def forward(self, x, mats_dict, scale_depth_factor=1000.0):
        B, _, H, W = x.shape
        intrins = mats_dict["intrin_mats"][:, 0:1, ..., :3, :3]
        batch_size = intrins.shape[0]
        num_cams = intrins.shape[2]
        ida = mats_dict["ida_mats"][:, 0:1, ...]
        sensor2ego = mats_dict["sensor2ego_mats"][:, 0:1, ..., :3, :]
        bda = mats_dict["bda_mat"].view(batch_size, 1, 1, 4, 4).repeat(1, 1, num_cams, 1, 1)
        mlp_input = torch.cat(
            [
                torch.stack(
                    [
                        intrins[:, 0:1, ..., 0, 0],
                        intrins[:, 0:1, ..., 1, 1],
                        intrins[:, 0:1, ..., 0, 2],
                        intrins[:, 0:1, ..., 1, 2],
                        ida[:, 0:1, ..., 0, 0],
                        ida[:, 0:1, ..., 0, 1],
                        ida[:, 0:1, ..., 0, 3],
                        ida[:, 0:1, ..., 1, 0],
                        ida[:, 0:1, ..., 1, 1],
                        ida[:, 0:1, ..., 1, 3],
                        bda[:, 0:1, ..., 0, 0],
                        bda[:, 0:1, ..., 0, 1],
                        bda[:, 0:1, ..., 1, 0],
                        bda[:, 0:1, ..., 1, 1],
                        bda[:, 0:1, ..., 2, 2],
                    ],
                    dim=-1,
                ),
                sensor2ego.view(batch_size, 1, num_cams, -1),
            ],
            -1,
        )
        mlp_input = self.bn(mlp_input.reshape(-1, mlp_input.shape[-1]))
        x = self.reduce_conv(x)
        context_se = self.context_mlp(mlp_input)[..., None, None]
        context = self.context_se(x, context_se)
        context = self.context_conv(context)
        depth_se = self.depth_mlp(mlp_input)[..., None, None]
        depth_feat = self.depth_se(x, depth_se)
        depth_feat = self.depth_feat_conv(depth_feat)
        mono_depth = self.mono_depth_net(depth_feat)
        mu_sigma_score = self.mu_sigma_range_net(depth_feat)
        d_coords = torch.arange(*self.d_bound, dtype=torch.float).reshape(1, -1, 1, 1).cuda()
        d_coords = d_coords.repeat(B, 1, H, W)
        mu = mu_sigma_score[:, 0 : self.num_ranges, ...]
        sigma = mu_sigma_score[:, self.num_ranges : 2 * self.num_ranges, ...]
        range_score = mu_sigma_score[:, 2 * self.num_ranges : 3 * self.num_ranges, ...]
        sigma = F.elu(sigma) + 1.0 + 1e-10
        return x, context, mu, sigma, range_score, mono_depth


class BEVStereoLSSFPN(BaseLSSFPN):
    def __init__(
        self,
        sampling_range=3,
        num_samples=3,
        mag_downsample_factor=4,
        mag_iteration=3,
        min_sigma=1,
        num_groups=8,
        num_ranges=4,
        range_list=None,
        use_mask=True,
        k_list=None,
        **kwargs
    ):
        """Backbone of BEVStereo

        Args:
            sampling_range (int, optional): Range for sampling points. Defaults to 3.
            num_samples (int, optional): Number of sampling points. Defaults to 3.
            mag_downsample_factor (int, optional): Downsample factor of mag_feature. Defaults to 4.
            mag_iteration (int, optional): Iteration number. Defaults to 3.
            min_sigma (int, optional): Minimal value of sigma. Defaults to 1.
            num_groups (int, optional): Number of groups. Defaults to 8.
            num_ranges (int, optional): Number of ranges. Defaults to 4.
            range_list (_type_, optional): List of range. Defaults to None.
            use_mask (bool, optional): Whether to use mask. Defaults to True.
            k_list (_type_, optional): List of key. Defaults to None.
        """
        self.num_ranges = num_ranges
        super().__init__(**kwargs)
        self.sampling_range = sampling_range
        self.num_samples = num_samples
        if k_list is None:
            self.register_buffer("k_list", torch.Tensor(self.depth_sampling()))
        else:
            self.register_buffer("k_list", torch.Tensor(k_list))
        self.mag_downsample_factor = mag_downsample_factor
        self.mag_iteration = mag_iteration
        self.register_buffer(
            "depth_values", torch.arange((self.d_bound[1] - self.d_bound[0]) / self.d_bound[2], dtype=torch.float)
        )
        self.num_groups = num_groups
        self.similarity_net = nn.Sequential(
            ConvBnReLU3D(in_channels=num_groups, out_channels=16, kernel_size=1, stride=1, pad=0),
            ConvBnReLU3D(in_channels=16, out_channels=8, kernel_size=1, stride=1, pad=0),
            nn.Conv3d(in_channels=8, out_channels=1, kernel_size=1, stride=1, padding=0),
        )
        if range_list is None:
            range_length = (kwargs["d_bound"][1] - kwargs["d_bound"][0]) / num_ranges
            self.range_list = [
                [kwargs["d_bound"][0] + range_length * i, kwargs["d_bound"][0] + range_length * (i + 1)]
                for i in range(num_ranges)
            ]
        else:
            assert len(range_list) == num_ranges
            self.range_list = range_list

        self.min_sigma = min_sigma
        self.depth_downsample_net = nn.Sequential(
            nn.Conv2d(self.depth_channels, 256, 3, 2, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(),
            nn.Conv2d(256, 256, 3, 2, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(),
            nn.Conv2d(256, self.depth_channels, 1, 1, 0),
        )
        self.context_downsample_net = nn.Identity()
        self.use_mask = use_mask
        if self.use_mask:
            mask_in_channels = 2 * int((kwargs["d_bound"][1] - kwargs["d_bound"][0]) // kwargs["d_bound"][2])
            self.mask_net = nn.Sequential(
                nn.Conv2d(mask_in_channels, 64, 3, 1, 1),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                BasicBlock(64, 64),
                BasicBlock(64, 64),
                nn.Conv2d(64, 1, 1, 1, 0),
                nn.Sigmoid(),
            )

    def homo_warping(
        self,
        mag_feat,
        key_intrin_mats,
        sweep_intrin_mats,
        sensor2sensor_mats,
        key_ida_mats,
        sweep_ida_mats,
        depth_sample,
        frustum,
    ):
        """Used for mvs method to transfer sweep image feature to key image feature.

        Args:
            src_fea(Tensor): image features.
            key_intrin_mats(Tensor): Intrin matrix for key sensor.
            sweep_intrin_mats(Tensor): Intrin matrix for sweep sensor.
            sensor2sensor_mats(Tensor): Transformation matrix from key sensor to sweep sensor.
            key_ida_mats(Tensor): Ida matrix for key frame.
            sweep_ida_mats(Tensor): Ida matrix for sweep frame.
        """
        batch_size_with_num_cams, channels = mag_feat.shape[0], mag_feat.shape[1]
        height, width = mag_feat.shape[2], mag_feat.shape[3]
        with torch.no_grad():
            points = frustum
            points = points.reshape(points.shape[0], -1, points.shape[-1])
            points[..., 2] = 1
            # Undo ida for key frame.
            points = key_ida_mats.reshape(batch_size_with_num_cams, *key_ida_mats.shape[2:]).inverse().unsqueeze(
                1
            ) @ points.unsqueeze(-1)
            # Convert points from pixel coord to key camera coord.
            points[..., :3, :] *= depth_sample.reshape(batch_size_with_num_cams, -1, 1, 1)
            num_depth = frustum.shape[1]

            points = (
                sweep_intrin_mats.reshape(batch_size_with_num_cams, *sweep_intrin_mats.shape[2:]).unsqueeze(1)
                @ sensor2sensor_mats.reshape(batch_size_with_num_cams, *sensor2sensor_mats.shape[2:]).unsqueeze(1)
                @ key_intrin_mats.reshape(batch_size_with_num_cams, *key_intrin_mats.shape[2:]).inverse().unsqueeze(1)
            ) @ points
            # points in sweep pixel coord.
            points[..., :2, :] = points[..., :2, :] / points[..., 2:3, :]  # [B, 2, Ndepth, H*W]

            points = (
                sweep_ida_mats.reshape(batch_size_with_num_cams, *sweep_ida_mats.shape[2:]).unsqueeze(1) @ points
            ).squeeze(-1)
            # warped_depth_sample = points[..., 2].reshape(batch_size_with_num_cams, num_samples, height, width)
            neg_mask = points[..., 2] < 1e-3
            points[..., 0][neg_mask] = width * self.mag_downsample_factor
            points[..., 1][neg_mask] = height * self.mag_downsample_factor
            points[..., 2][neg_mask] = 1
            proj_x_normalized = points[..., 0] / ((width * self.mag_downsample_factor - 1) / 2) - 1
            proj_y_normalized = points[..., 1] / ((height * self.mag_downsample_factor - 1) / 2) - 1
            grid = torch.stack([proj_x_normalized, proj_y_normalized], dim=2)  # [B, Ndepth, H*W, 2]

        warped_mag_fea = F.grid_sample(
            mag_feat,
            grid.view(batch_size_with_num_cams, num_depth * height, width, 2),
            mode="bilinear",
            padding_mode="zeros",
        )
        warped_mag_fea = warped_mag_fea.view(batch_size_with_num_cams, channels, num_depth, height, width)

        return warped_mag_fea

    def create_depth_sample_frustum(self, depth_sample, downsample_factor=16):
        """Generate frustum"""
        # make grid in image plane
        ogfH, ogfW = self.final_dim
        fH, fW = ogfH // downsample_factor, ogfW // downsample_factor
        batch_size, num_depth, _, _ = depth_sample.shape
        x_coords = (
            torch.linspace(0, ogfW - 1, fW, dtype=torch.float, device=depth_sample.device)
            .view(1, 1, 1, fW)
            .expand(batch_size, num_depth, fH, fW)
        )
        y_coords = (
            torch.linspace(0, ogfH - 1, fH, dtype=torch.float, device=depth_sample.device)
            .view(1, 1, fH, 1)
            .expand(batch_size, num_depth, fH, fW)
        )
        paddings = torch.ones_like(depth_sample)

        # D x H x W x 3
        frustum = torch.stack((x_coords, y_coords, depth_sample, paddings), -1)
        return frustum

    def _configure_depth_net(self, depth_net_conf):
        return nn.Sequential(
            DepthNet(
                depth_net_conf["in_channels"],
                depth_net_conf["mid_channels"],
                self.output_channels,
                self.depth_channels,
                self.d_bound,
                self.num_ranges,
            )
        )

    def get_cam_feats(self, imgs):
        """Get feature maps from images."""
        batch_size, num_sweeps, num_cams, num_channels, imH, imW = imgs.shape

        imgs = imgs.flatten().view(batch_size * num_sweeps * num_cams, num_channels, imH, imW)
        backbone_feats = self.img_backbone(imgs)
        img_feats = self.img_neck(backbone_feats)[0]
        img_feats_reshape = img_feats.reshape(
            batch_size, num_sweeps, num_cams, img_feats.shape[1], img_feats.shape[2], img_feats.shape[3]
        )
        return img_feats_reshape, backbone_feats[0].detach()

    def depth_sampling(self):
        P_total = erf(self.sampling_range / np.sqrt(2))  # Probability covered by the sampling range
        idx_list = np.arange(0, self.num_samples + 1)
        p_list = (1 - P_total) / 2 + ((idx_list / self.num_samples) * P_total)
        k_list = norm.ppf(p_list)
        k_list = (k_list[1:] + k_list[:-1]) / 2
        return list(k_list)

    def _forward_single_sweep(self, sweep_index, context, mats_dict, depth_score):
        batch_size, num_cams = context.shape[0], context.shape[1]
        context = context.reshape(batch_size * num_cams, *context.shape[2:])
        depth = depth_score
        img_feat_with_depth = depth.unsqueeze(1) * context.unsqueeze(2)

        img_feat_with_depth = self._forward_voxel_net(img_feat_with_depth)

        img_feat_with_depth = img_feat_with_depth.reshape(
            batch_size,
            num_cams,
            img_feat_with_depth.shape[1],
            img_feat_with_depth.shape[2],
            img_feat_with_depth.shape[3],
            img_feat_with_depth.shape[4],
        )
        geom_xyz = self.get_geometry(
            mats_dict["sensor2ego_mats"][:, sweep_index, ...],
            mats_dict["intrin_mats"][:, sweep_index, ...],
            mats_dict["ida_mats"][:, sweep_index, ...],
            mats_dict.get("bda_mat", None),
        )
        img_feat_with_depth = img_feat_with_depth.permute(0, 1, 3, 4, 5, 2)
        geom_xyz = ((geom_xyz - (self.voxel_coord - self.voxel_size / 2.0)) / self.voxel_size).int()
        feature_map = voxel_pooling(geom_xyz, img_feat_with_depth.contiguous().float(), self.voxel_num.cuda())
        if self.is_return_depth and sweep_index == 0:
            return feature_map.contiguous(), depth
        return feature_map.contiguous()

    def forward(self, sweep_imgs, mats_dict, timestamps=None):
        """Forward function.

        Args:
            sweep_imgs(Tensor): Input images with shape of (B, num_sweeps, num_cameras, 3, H, W).
            mats_dict(dict):
                sensor2ego_mats(Tensor): Transformation matrix from camera to ego with shape of (B, num_sweeps, num_cameras, 4, 4).
                intrin_mats(Tensor): Intrinsic matrix with shape of (B, num_sweeps, num_cameras, 4, 4).
                ida_mats(Tensor): Transformation matrix for ida with shape of (B, num_sweeps, num_cameras, 4, 4).
                sensor2sensor_mats(Tensor): Transformation matrix from key frame camera to sweep frame camera with shape of (B, num_sweeps, num_cameras, 4, 4).
                bda_mat(Tensor): Rotation matrix for bda with shape of (B, 4, 4).
            timestamps(Tensor): Timestamp for all images with the shape of(B, num_sweeps, num_cameras).

        Return:
            Tensor: bev feature map.
        """
        batch_size, num_sweeps, num_cams, num_channels, img_height, img_width = sweep_imgs.shape
        context_all_sweeps = list()
        depth_feat_all_sweeps = list()
        img_feats_all_sweeps = list()
        mag_feats_all_sweeps = list()
        mu_all_sweeps = list()
        sigma_all_sweeps = list()
        mono_depth_all_sweeps = list()
        range_score_all_sweeps = list()
        for sweep_index in range(0, num_sweeps):
            if sweep_index > 0:
                with torch.no_grad():
                    img_feats, mag_feats = self.get_cam_feats(sweep_imgs[:, sweep_index : sweep_index + 1, ...])
                    img_feats_all_sweeps.append(img_feats.view(batch_size * num_cams, *img_feats.shape[3:]))
                    mag_feats_all_sweeps.append(mag_feats)
                    depth_feat, context, mu, sigma, range_score, mono_depth = self.depth_net[0](
                        img_feats.view(batch_size * num_cams, *img_feats.shape[3:]), mats_dict
                    )
                    context_all_sweeps.append(
                        self.context_downsample_net(context.reshape(batch_size * num_cams, *context.shape[1:]))
                    )
                    depth_feat_all_sweeps.append(depth_feat)
            else:
                img_feats, mag_feats = self.get_cam_feats(sweep_imgs[:, sweep_index : sweep_index + 1, ...])
                img_feats_all_sweeps.append(img_feats.view(batch_size * num_cams, *img_feats.shape[3:]))
                mag_feats_all_sweeps.append(mag_feats)
                depth_feat, context, mu, sigma, range_score, mono_depth = self.depth_net[0](
                    img_feats.view(batch_size * num_cams, *img_feats.shape[3:]), mats_dict
                )
                depth_feat_all_sweeps.append(depth_feat)
                context_all_sweeps.append(
                    self.context_downsample_net(context.reshape(batch_size * num_cams, *context.shape[1:]))
                )
            mu_all_sweeps.append(mu)
            sigma_all_sweeps.append(sigma)
            mono_depth_all_sweeps.append(mono_depth)
            range_score_all_sweeps.append(range_score)
        depth_score_all_sweeps = list()

        for ref_idx in range(num_sweeps):
            sensor2sensor_mats = list()
            for src_idx in range(num_sweeps):
                ref2keysensor_mats = mats_dict["sensor2sensor_mats"][:, ref_idx, ...].inverse()
                key2srcsensor_mats = mats_dict["sensor2sensor_mats"][:, src_idx, ...]
                ref2srcsensor_mats = key2srcsensor_mats @ ref2keysensor_mats
                sensor2sensor_mats.append(ref2srcsensor_mats)
            if ref_idx == 0:
                # last iteration on stage 1 does not have propagation (photometric consistency filtering)
                if self.use_mask:
                    mag_depth, mask = self._forward_mag(
                        ref_idx,
                        mag_feats_all_sweeps,
                        mono_depth_all_sweeps,
                        mats_dict,
                        sensor2sensor_mats,
                        mu_all_sweeps,
                        sigma_all_sweeps,
                        range_score_all_sweeps,
                        depth_feat_all_sweeps,
                    )
                else:
                    mag_depth = self._forward_mag(
                        ref_idx,
                        mag_feats_all_sweeps,
                        mono_depth_all_sweeps,
                        mats_dict,
                        sensor2sensor_mats,
                        mu_all_sweeps,
                        sigma_all_sweeps,
                        range_score_all_sweeps,
                        depth_feat_all_sweeps,
                    )
            else:
                with torch.no_grad():
                    # last iteration on stage 1 does not have propagation (photometric consistency filtering)
                    if self.use_mask:
                        mag_depth, mask = self._forward_mag(
                            ref_idx,
                            mag_feats_all_sweeps,
                            mono_depth_all_sweeps,
                            mats_dict,
                            sensor2sensor_mats,
                            mu_all_sweeps,
                            sigma_all_sweeps,
                            range_score_all_sweeps,
                            depth_feat_all_sweeps,
                        )
                    else:
                        mag_depth = self._forward_mag(
                            ref_idx,
                            mag_feats_all_sweeps,
                            mono_depth_all_sweeps,
                            mats_dict,
                            sensor2sensor_mats,
                            mu_all_sweeps,
                            sigma_all_sweeps,
                            range_score_all_sweeps,
                            depth_feat_all_sweeps,
                        )
            if self.use_mask:
                depth_score = (mono_depth_all_sweeps[ref_idx] + self.depth_downsample_net(mag_depth) * mask).softmax(1)
            else:
                depth_score = (mono_depth_all_sweeps[ref_idx] + self.depth_downsample_net(mag_depth)).softmax(1)
            depth_score_all_sweeps.append(depth_score)

        key_frame_res = self._forward_single_sweep(
            0,
            context_all_sweeps[0].reshape(batch_size, num_cams, *context_all_sweeps[0].shape[1:]),
            mats_dict,
            depth_score_all_sweeps[0],
        )
        if num_sweeps == 1:
            return key_frame_res

        key_frame_feature = key_frame_res[0] if self.is_return_depth else key_frame_res

        ret_feature_list = [key_frame_feature]
        for sweep_index in range(1, num_sweeps):
            with torch.no_grad():
                feature_map = self._forward_single_sweep(
                    sweep_index,
                    context_all_sweeps[sweep_index].reshape(
                        batch_size, num_cams, *context_all_sweeps[sweep_index].shape[1:]
                    ),
                    mats_dict,
                    depth_score_all_sweeps[sweep_index],
                )
                ret_feature_list.append(feature_map)

        if self.is_return_depth:
            return torch.cat(ret_feature_list, 1), depth_score_all_sweeps[0]
        else:
            return torch.cat(ret_feature_list, 1)

    def _forward_mask(
        self,
        sweep_index,
        mono_depth_all_sweeps,
        mats_dict,
        depth_sample,
        depth_sample_frustum,
        sensor2sensor_mats,
    ):
        num_sweeps = len(mono_depth_all_sweeps)
        mask_all_sweeps = list()
        for idx in range(num_sweeps):
            if idx == sweep_index:
                continue
            warped_mono_depth = self.homo_warping(
                mono_depth_all_sweeps[idx],
                mats_dict["intrin_mats"][:, sweep_index, ...],
                mats_dict["intrin_mats"][:, idx, ...],
                sensor2sensor_mats[idx],
                mats_dict["ida_mats"][:, sweep_index, ...],
                mats_dict["ida_mats"][:, idx, ...],
                depth_sample,
                depth_sample_frustum.type_as(mono_depth_all_sweeps[idx]),
            )
            mask = self.mask_net(
                torch.cat([mono_depth_all_sweeps[sweep_index].detach(), warped_mono_depth.mean(2).detach()], 1)
            )
            mask_all_sweeps.append(mask)
        return torch.stack(mask_all_sweeps).mean(0)

    def _forward_mag(
        self,
        sweep_index,
        mag_feats_all_sweeps,
        mono_depth_all_sweeps,
        mats_dict,
        sensor2sensor_mats,
        mu_all_sweeps,
        sigma_all_sweeps,
        range_score_all_sweeps,
        depth_feat_all_sweeps,
    ):
        batch_size_with_cams, _, feat_height, feat_width = mag_feats_all_sweeps[0].shape
        device = mag_feats_all_sweeps[0].device
        d_coords = torch.arange(*self.d_bound, dtype=torch.float, device=device).reshape(1, -1, 1, 1)
        d_coords = d_coords.repeat(batch_size_with_cams, 1, feat_height, feat_width)
        depth_score = mag_feats_all_sweeps[0].new_zeros(
            batch_size_with_cams, self.depth_channels, feat_height, feat_width
        )
        mask_score = mag_feats_all_sweeps[0].new_zeros(
            batch_size_with_cams,
            self.depth_channels,
            feat_height * self.mag_downsample_factor // self.downsample_factor,
            feat_width * self.mag_downsample_factor // self.downsample_factor,
        )
        score_all_ranges = list()
        range_score = range_score_all_sweeps[sweep_index].softmax(1)
        for range_idx in range(self.num_ranges):
            # Map mu to the corresponding interval.
            range_start = self.range_list[range_idx][0]
            mu_all_sweeps_single_range = [
                mu[:, range_idx : range_idx + 1, ...].sigmoid()
                * (self.range_list[range_idx][1] - self.range_list[range_idx][0])
                + range_start
                for mu in mu_all_sweeps
            ]
            sigma_all_sweeps_single_range = [sigma[:, range_idx : range_idx + 1, ...] for sigma in sigma_all_sweeps]
            batch_size_with_cams, _, feat_height, feat_width = mag_feats_all_sweeps[0].shape
            mu = mu_all_sweeps_single_range[sweep_index]
            sigma = sigma_all_sweeps_single_range[sweep_index]
            for _ in range(self.mag_iteration):
                depth_sample = torch.cat([mu + sigma * k for k in self.k_list], 1)
                depth_sample_frustum = self.create_depth_sample_frustum(depth_sample, self.mag_downsample_factor)
                mu_score = self._generate_cost_volume(
                    sweep_index,
                    mag_feats_all_sweeps,
                    mats_dict,
                    depth_sample,
                    depth_sample_frustum,
                    sensor2sensor_mats,
                )
                mu_score = mu_score.softmax(1)
                scale_factor = torch.clamp(
                    0.5 / (1e-4 + mu_score[:, self.num_samples // 2 : self.num_samples // 2 + 1, ...]), min=0.1, max=10
                )

                sigma = sigma * scale_factor
                mu = (depth_sample * mu_score).sum(1, keepdim=True)
                del depth_sample
                del depth_sample_frustum
            range_length = int((self.range_list[range_idx][1] - self.range_list[range_idx][0]) // self.d_bound[2])
            if self.use_mask:
                depth_sample = F.avg_pool2d(
                    mu,
                    self.downsample_factor // self.mag_downsample_factor,
                    self.downsample_factor // self.mag_downsample_factor,
                )
                depth_sample_frustum = self.create_depth_sample_frustum(depth_sample, self.downsample_factor)
                mask = self._forward_mask(
                    sweep_index,
                    mono_depth_all_sweeps,
                    mats_dict,
                    depth_sample,
                    depth_sample_frustum,
                    sensor2sensor_mats,
                )
                mask_score[
                    :,
                    int((range_start - self.d_bound[0]) // self.d_bound[2]) : range_length
                    + int((range_start - self.d_bound[0]) // self.d_bound[2]),
                    ...,
                ] += mask
                del depth_sample
                del depth_sample_frustum
            sigma = torch.clamp(sigma, self.min_sigma)
            mu_repeated = mu.repeat(1, range_length, 1, 1)
            eps = 1e-6
            depth_score_single_range = (
                -1
                / 2
                * (
                    (
                        d_coords[
                            :,
                            int((range_start - self.d_bound[0]) // self.d_bound[2]) : range_length
                            + int((range_start - self.d_bound[0]) // self.d_bound[2]),
                            ...,
                        ]
                        - mu_repeated
                    )
                    / torch.sqrt(sigma)
                )
                ** 2
            )
            depth_score_single_range = depth_score_single_range.exp()
            score_all_ranges.append(mu_score.sum(1).unsqueeze(1))
            depth_score_single_range = depth_score_single_range / (sigma * math.sqrt(2 * math.pi) + eps)
            depth_score[
                :,
                int((range_start - self.d_bound[0]) // self.d_bound[2]) : range_length
                + int((range_start - self.d_bound[0]) // self.d_bound[2]),
                ...,
            ] = (
                depth_score_single_range * range_score[:, range_idx : range_idx + 1, ...]
            )
            del depth_score_single_range
            del mu_repeated
        if self.use_mask:
            return depth_score, mask_score
        else:
            return depth_score

    def _generate_cost_volume(
        self,
        sweep_index,
        mag_feats_all_sweeps,
        mats_dict,
        depth_sample,
        depth_sample_frustum,
        sensor2sensor_mats,
    ):
        batch_size, num_channels, height, width = mag_feats_all_sweeps[0].shape
        num_sweeps = len(mag_feats_all_sweeps)
        depth_score_all_sweeps = list()
        for idx in range(num_sweeps):
            if idx == sweep_index:
                continue
            warped_mag_fea = self.homo_warping(
                mag_feats_all_sweeps[idx],
                mats_dict["intrin_mats"][:, sweep_index, ...],
                mats_dict["intrin_mats"][:, idx, ...],
                sensor2sensor_mats[idx],
                mats_dict["ida_mats"][:, sweep_index, ...],
                mats_dict["ida_mats"][:, idx, ...],
                depth_sample,
                depth_sample_frustum.type_as(mag_feats_all_sweeps[idx]),
            )
            warped_mag_fea_reshaped = warped_mag_fea.reshape(
                batch_size, self.num_groups, num_channels // self.num_groups, self.num_samples, height, width
            )
            ref_mag_feat = mag_feats_all_sweeps[sweep_index].reshape(
                batch_size, self.num_groups, num_channels // self.num_groups, height, width
            )
            feat_cost = torch.mean((ref_mag_feat.unsqueeze(3) * warped_mag_fea_reshaped), axis=2)
            depth_score = self.similarity_net(feat_cost).squeeze(1)
            depth_score_all_sweeps.append(depth_score)
        return torch.stack(depth_score_all_sweeps).mean(0)
