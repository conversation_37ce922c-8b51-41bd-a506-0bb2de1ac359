from torch import nn
from perceptron.layers.head.mmdet3d.bevdet_head import BevDetHead, BevDetHeadPrivate
from perceptron.layers.blocks_3d.mmdet3d.base_lss_fpn import BaseLSSFPN, LSSFPNPrivate

__all__ = ["BevDet", "BevDetPrivate"]


class BevDet(nn.Module):
    """Reimplementation of
    `BEVDet: High-performance Multi-camera 3D Object Detection in Bird-Eye-View`, `https://arxiv.org/abs/2112.11790`.
    """

    # TODO: Reduce grid_conf and data_aug_conf
    def __init__(self, backbone_conf, head_conf, is_train_depth=False):
        super(BevDet, self).__init__()
        self.backbone = BaseLSSFPN(**backbone_conf)
        self.head = BevDetHead(**head_conf)
        self.is_train_depth = is_train_depth

    # TODO: Merge to one input
    def forward(
        self,
        x,
        mats,
        timestamps=None,
    ):
        if self.is_train_depth and self.training:
            x, depth_pred = self.backbone(x, mats, timestamps)
            preds = self.head(x)
            return preds, depth_pred
        else:
            x = self.backbone(x, mats, timestamps)
            preds = self.head(x)
            return preds

    def get_targets(self, gt_boxes, gt_labels):
        return self.head.get_targets(gt_boxes, gt_labels)

    def loss(self, targets, preds_dicts):
        return self.head.loss(targets, preds_dicts)

    def get_bboxes(self, preds_dicts, img_metas=None, img=None, rescale=False):
        return self.head.get_bboxes(preds_dicts, img_metas, img, rescale)


class BevDetPrivate(BevDet):
    def __init__(self, backbone_conf, head_conf, is_train_depth=False):
        super(BevDet, self).__init__()
        self.backbone = LSSFPNPrivate(**backbone_conf)
        self.head = BevDetHeadPrivate(**head_conf)
        self.is_train_depth = is_train_depth
