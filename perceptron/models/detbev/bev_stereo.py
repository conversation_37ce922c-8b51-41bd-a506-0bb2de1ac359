from perceptron.layers.head.mmdet3d.bevdet_head import BevDetHead
from perceptron.layers.blocks_3d.mmdet3d.bevstereo_lss_fpn import BEVStereoLSSFPN
from perceptron.models.detbev.bev_depth import BevDepth as BaseBEVDet

__all__ = ["BevStereo"]


class BevStereo(BaseBEVDet):
    """Implementation of
    `BEVStereo: Enhancing Depth Estimation in Multi-view 3D Object Detection with Dynamic Temporal Stereo`, `https://arxiv.org/abs/2209.10248`.
    """

    # TODO: Reduce grid_conf and data_aug_conf
    def __init__(self, backbone_conf, head_conf, is_train_depth=True):
        super(BaseBEVDet, self).__init__()
        backbone_conf["is_return_depth"] = is_train_depth
        self.backbone = BEVStereoLSSFPN(**backbone_conf)
        self.head = BevDetHead(**head_conf)
        self.is_train_depth = is_train_depth
