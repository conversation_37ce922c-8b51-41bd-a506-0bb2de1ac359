import torch.nn as nn
import torch.nn.functional as F
from perceptron.layers.blocks_2d.seg2d import FeatureFusionModule, BiSeNetOutput
from .contextpath import ContextPath


class BiSeNet(nn.Module):
    def __init__(
        self,
        backbone,
        n_classes,
        pretrain_model="",
        use_boundary_2=False,
        use_boundary_4=False,
        use_boundary_8=True,
        use_boundary_16=False,
        use_conv_last=False,
        *args,
        **kwargs
    ):
        super(BiSeNet, self).__init__()
        self.backbone = backbone
        self.n_classes = n_classes
        self.use_boundary_2 = use_boundary_2
        self.use_boundary_4 = use_boundary_4
        self.use_boundary_8 = use_boundary_8
        self.use_boundary_16 = use_boundary_16
        self.use_conv_last = use_conv_last
        self.cp = ContextPath(self.backbone, pretrain_model, use_conv_last=self.use_conv_last)

        if self.backbone == "STDCNet1446":
            conv_out_inplanes = 128
            sp2_inplanes = 32
            sp4_inplanes = 64
            sp8_inplanes = 256
            sp16_inplanes = 512
            inplane = sp8_inplanes + conv_out_inplanes

        elif self.backbone == "STDCNet813":
            conv_out_inplanes = 128
            sp2_inplanes = 32
            sp4_inplanes = 64
            sp8_inplanes = 256
            sp16_inplanes = 512
            inplane = sp8_inplanes + conv_out_inplanes

        else:
            print("backbone is not in backbone lists")
            exit(0)
        self.ffm = FeatureFusionModule(inplane, 256)
        self.conv_out = BiSeNetOutput(256, 256, self.n_classes)
        self.conv_out16 = BiSeNetOutput(conv_out_inplanes, 64, self.n_classes)
        self.conv_out32 = BiSeNetOutput(conv_out_inplanes, 64, self.n_classes)
        self.conv_out_sp16 = BiSeNetOutput(sp16_inplanes, 64, 1)
        self.conv_out_sp8 = BiSeNetOutput(sp8_inplanes, 64, 1)
        self.conv_out_sp4 = BiSeNetOutput(sp4_inplanes, 64, 1)
        self.conv_out_sp2 = BiSeNetOutput(sp2_inplanes, 64, 1)
        self.init_weight()

    def forward(self, data):
        _, _, H, W = data.size()

        feat_res2, feat_res4, feat_res8, feat_res16, feat_cp8, feat_cp16 = self.cp(data)

        feat_out_sp2 = self.conv_out_sp2(feat_res2)

        feat_out_sp4 = self.conv_out_sp4(feat_res4)

        feat_out_sp8 = self.conv_out_sp8(feat_res8)

        feat_fuse = self.ffm([feat_res8, feat_cp8])

        feat_out = self.conv_out(feat_fuse)
        feat_out16 = self.conv_out16(feat_cp8)
        feat_out32 = self.conv_out32(feat_cp16)

        feat_out = F.interpolate(feat_out, (H, W), mode="bilinear", align_corners=True)
        feat_out16 = F.interpolate(feat_out16, (H, W), mode="bilinear", align_corners=True)
        feat_out32 = F.interpolate(feat_out32, (H, W), mode="bilinear", align_corners=True)

        if self.use_boundary_2 and self.use_boundary_4 and self.use_boundary_8:
            self.logits = [feat_out, feat_out16, feat_out32, feat_out_sp2, feat_out_sp4, feat_out_sp8]
        if not self.use_boundary_2 and self.use_boundary_4 and self.use_boundary_8:
            self.logits = [feat_out, feat_out16, feat_out32, feat_out_sp4, feat_out_sp8]
        if not self.use_boundary_2 and not self.use_boundary_4 and self.use_boundary_8:
            self.logits = [feat_out, feat_out16, feat_out32, feat_out_sp8]
        if not self.use_boundary_2 and not self.use_boundary_4 and not self.use_boundary_8:
            self.logits = [feat_out, feat_out16, feat_out32]

        return self.logits

    def init_weight(self):
        for ly in self.children():
            if isinstance(ly, nn.Conv2d):
                nn.init.kaiming_normal_(ly.weight, a=1)
                if ly.bias is not None:
                    nn.init.constant_(ly.bias, 0)

    def get_params(self):
        wd_params, nowd_params, lr_mul_wd_params, lr_mul_nowd_params = [], [], [], []
        for name, child in self.named_children():
            child_wd_params, child_nowd_params = child.get_params()
            if isinstance(child, (FeatureFusionModule, BiSeNetOutput)):
                lr_mul_wd_params += child_wd_params
                lr_mul_nowd_params += child_nowd_params
            else:
                wd_params += child_wd_params
                nowd_params += child_nowd_params
        return wd_params, nowd_params, lr_mul_wd_params, lr_mul_nowd_params
