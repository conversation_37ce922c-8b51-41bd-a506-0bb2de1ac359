import torch.nn as nn
import torch.nn.functional as F
from perceptron.layers.blocks_2d.seg2d import AttentionRefinementModule, ConvBNReLU
from .stdcnet import STDCNet813, STDCNet1446


class ContextPath(nn.Module):
    def __init__(self, backbone="STDCNet813", pretrain_model="", use_conv_last=False, *args, **kwargs):
        super(ContextPath, self).__init__()
        self.backbone_name = backbone
        if backbone == "STDCNet1446":
            self.backbone = STDCNet1446(pretrain_model=pretrain_model, use_conv_last=use_conv_last)
            self.arm16 = AttentionRefinementModule(512, 128)
            inplanes = 1024
            if use_conv_last:
                inplanes = 1024
            self.arm32 = AttentionRefinementModule(inplanes, 128)
            self.conv_head32 = ConvBNReLU(128, 128, kernel_size=3, stride=1, padding=1)
            self.conv_head16 = ConvBNReLU(128, 128, kernel_size=3, stride=1, padding=1)
            self.conv_avg = ConvBNReLU(inplanes, 128, kernel_size=1, stride=1, padding=0)
        elif backbone == "STDCNet813":
            self.backbone = STDCNet813(pretrain_model=pretrain_model, use_conv_last=use_conv_last)
            self.arm16 = AttentionRefinementModule(512, 128)
            inplanes = 1024
            if use_conv_last:
                inplanes = 1024
            self.arm32 = AttentionRefinementModule(inplanes, 128)
            self.conv_head32 = ConvBNReLU(128, 128, kernel_size=3, stride=1, padding=1)
            self.conv_head16 = ConvBNReLU(128, 128, kernel_size=3, stride=1, padding=1)
            self.conv_avg = ConvBNReLU(inplanes, 128, kernel_size=1, stride=1, padding=0)
        else:
            print("backbone is not in backbone lists")
            exit(0)
        self.init_weight()

    def forward(self, x):
        feat2, feat4, feat8, feat16, feat32 = self.backbone(x)

        H8, W8 = feat8.size()[2:]
        H16, W16 = feat16.size()[2:]
        H32, W32 = feat32.size()[2:]

        avg = F.avg_pool2d(feat32, feat32.size()[2:])

        avg = self.conv_avg(avg)
        avg_up = F.interpolate(avg, (H32, W32), mode="nearest")

        feat32_arm = self.arm32(feat32)
        feat32_sum = feat32_arm + avg_up
        feat32_up = F.interpolate(feat32_sum, (H16, W16), mode="nearest")
        feat32_up = self.conv_head32(feat32_up)

        feat16_arm = self.arm16(feat16)
        feat16_sum = feat16_arm + feat32_up
        feat16_up = F.interpolate(feat16_sum, (H8, W8), mode="nearest")
        feat16_up = self.conv_head16(feat16_up)

        return feat2, feat4, feat8, feat16, feat16_up, feat32_up  # x8, x16

    def init_weight(self):
        for ly in self.children():
            if isinstance(ly, nn.Conv2d):
                nn.init.kaiming_normal_(ly.weight, a=1)
                if ly.bias is not None:
                    nn.init.constant_(ly.bias, 0)

    def get_params(self):
        wd_params, nowd_params = [], []
        for name, module in self.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv2d)):
                wd_params.append(module.weight)
                if module.bias is not None:
                    nowd_params.append(module.bias)
            elif isinstance(module, nn.BatchNorm2d):
                nowd_params += list(module.parameters())
        return wd_params, nowd_params
