import mmcv
import torch
import numpy as np
from typing import Any, List
from perceptron.models.multisensor_fusion import BaseEncoder, ForceFp32
from perceptron.models.multisensor_fusion.cmt.base import CMT
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.layers.blocks_3d.det3d import MeanVFE, VoxelResBackBone8x
from perceptron.layers.blocks_2d.det3d import BaseBEVBackbone, HeightCompression
from perceptron.models.multisensor_fusion.cmt.base import DetHead
from perceptron.layers.head.det3d.cmt_head import CenterCMTFusionHead


class LidarPrivateEncoder(BaseEncoder):
    def __init__(self, lidar_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super().__init__()
        self.cfg = lidar_encoder_cfg
        self.voxelizer = self.build_voxelizer()
        self.vfe = self.build_vfe()
        self.backbone_3d = self.build_backbone_3d()
        self.map_to_bev = self.build_map_to_bev()
        self.backbone_2d = BaseBEVBackbone(**lidar_encoder_cfg.backbone_2d)

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
            max_num_points=self.cfg.max_num_points,
            max_voxels=self.cfg.max_voxels,
            num_point_features=self.cfg.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = MeanVFE(
            num_point_features=self.cfg.use_num_point_features,
        )
        return vfe

    def build_backbone_3d(self):
        return VoxelResBackBone8x(
            input_channels=self.vfe.get_output_feature_dim(),
            grid_size=np.array(self.cfg.grid_size),
            last_pad=0,
        )

    def build_map_to_bev(self):
        return HeightCompression(num_bev_features=self.cfg.map_to_bev_num_features)

    @ForceFp32(apply_to=("lidar_points"))
    def forward(self, lidar_points: List[torch.tensor]) -> torch.tensor:
        voxels, voxel_coords, voxel_num_points = self.voxelizer(lidar_points)
        voxel_features = self.vfe(voxels, voxel_num_points)
        encoded_spconv_tensor, encoded_spconv_tensor_stride, _ = self.backbone_3d(
            voxel_features, voxel_coords, len(lidar_points)
        )

        spatial_features, encoded_spconv_tensor_stride = self.map_to_bev(
            encoded_spconv_tensor, encoded_spconv_tensor_stride
        )
        features_2d, pyramid = self.backbone_2d(spatial_features)
        return [features_2d]


class CMTPrivate(CMT):
    def __init__(self, model_cfg):
        super(CMTPrivate, self).__init__(model_cfg)

    def _configure_lidar_encoder(self):
        return LidarPrivateEncoder(self.cfg.lidar_encoder)


class CenterDetHead(DetHead):
    def __init__(self, *args, **kwargs):
        super(CenterDetHead, self).__init__(*args, **kwargs)

    def build_dense_head(self):
        dense_head_module = CenterCMTFusionHead(
            in_channels=self.det_head_cfg.in_channels,
            num_lidar_init_query=self.det_head_cfg.num_lidar_init_query,
            nms_kernel_size=self.det_head_cfg.nms_kernel_size,
            modal=self.det_head_cfg.modal,
            depth_num=self.det_head_cfg.depth_num,
            num_query=self.det_head_cfg.num_query,
            hidden_dim=self.det_head_cfg.hidden_dim,
            grid_size=self.det_head_cfg.grid_size,
            norm_bbox=True,
            downsample_scale=self.det_head_cfg.downsample_scale,
            scalar=10,
            noise_scale=1.0,
            noise_trans=0.0,
            dn_weight=1.0,
            split=0.75,
            train_cfg=self.train_cfg,
            test_cfg=self.test_cfg,
            common_heads=self.det_head_cfg.common_heads,
            tasks=self.det_head_cfg.tasks,
            transformer=self.det_head_cfg.transformer,
            bbox_coder=self.det_head_cfg.bbox_coder,
            loss_cls=self.det_head_cfg.loss_cls,
            loss_bbox=self.det_head_cfg.loss_bbox,
            loss_heatmap=self.det_head_cfg.loss_heatmap,
            separate_head=self.det_head_cfg.separate_head,
        )

        return dense_head_module


class CenterCMTPrivate(CMTPrivate):
    def __init__(self, model_cfg):
        super(CenterCMTPrivate, self).__init__(model_cfg)

    def _configure_det_head(self):
        return CenterDetHead(self.cfg.det_head, self.cfg.train_cfg["pts"], self.cfg.test_cfg["pts"])
