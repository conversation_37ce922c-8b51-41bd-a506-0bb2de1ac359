import mmcv
import torch
import torch.nn as nn

from contextlib import nullcontext
from typing import Any, Dict, List
from perceptron.models.multisensor_fusion import BaseMultiSensorFusion, BaseEncoder, ForceFp32
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.det3d.modules.utils.grid_mask import GridMask
from perceptron.data.det3d.modules.pipelines.transformation import ModalMask3D
from perceptron.layers.head.det3d import CMTFusionHead
from perceptron.layers.blocks_2d.mmdet2d.cpfpn import CPFPN
from mmdet.models.backbones.resnet import ResNet
from mmdet3d.models.backbones.second import SECOND
from mmdet3d.models.necks.second_fpn import SECONDFPN
from mmdet3d.models.voxel_encoders.voxel_encoder import HardSimpleVFE
from mmdet3d.models.middle_encoders.sparse_encoder import SparseEncoder
from perceptron.layers.blocks_2d.mmdet2d.vovnetcp import VoVNetCP


class LidarEncoder(BaseEncoder):
    """
    This is usually used lidar encoder for public datasets, such as NuScenes. For private datasets, it achieves sub-optimal performance.

    @ todo: reformat this class, some function names are really confusing.
    """

    def __init__(self, lidar_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super().__init__()
        self.cfg = lidar_encoder_cfg
        self.voxelizer = self.build_voxelizer(self.cfg["pts_voxel_layer"])
        self.vfe = self.build_vfe(self.cfg["pts_voxel_encoder"])
        self.backbone_3d = self.build_backbone_3d(self.cfg["pts_backbone"])
        self.neck_3d = self.build_neck_3d(self.cfg["pts_neck"])
        self.pts_middle_encoder = self.build_pts_middle_encoder(self.cfg["pts_middle_encoder"])

    def build_voxelizer(self, cfg):
        return Voxelization(
            voxel_size=cfg.voxel_size,
            point_cloud_range=cfg.point_cloud_range,
            max_num_points=cfg.max_num_points,
            max_voxels=cfg.max_voxels,
            num_point_features=cfg.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self, cfg):
        return HardSimpleVFE(cfg.num_features)

    def build_backbone_3d(self, cfg):
        return SECOND(
            in_channels=cfg.in_channels,
            out_channels=cfg.out_channels,
            layer_nums=cfg.layer_nums,
            layer_strides=cfg.layer_strides,
            norm_cfg=cfg.norm_cfg,
            conv_cfg=cfg.conv_cfg,
        )

    def build_neck_3d(self, cfg):
        return SECONDFPN(
            in_channels=cfg.in_channels,
            out_channels=cfg.out_channels,
            upsample_strides=cfg.upsample_strides,
            norm_cfg=cfg.norm_cfg,
            upsample_cfg=cfg.upsample_cfg,
            use_conv_for_no_stride=cfg.use_conv_for_no_stride,
        )

    def build_pts_middle_encoder(self, cfg):
        return SparseEncoder(
            in_channels=cfg.in_channels,
            sparse_shape=cfg.sparse_shape,
            output_channels=cfg.output_channels,
            order=cfg.order,
            encoder_channels=cfg.encoder_channels,
            encoder_paddings=cfg.encoder_paddings,
            block_type=cfg.block_type,
        )

    def forward(self, lidar_points):

        voxels, coors, num_points = self.voxelizer(lidar_points)
        voxel_features = self.vfe(voxels, num_points, coors)
        batch_size = coors[-1, 0] + 1
        x = self.pts_middle_encoder(voxel_features, coors, batch_size)
        x = self.backbone_3d(x)
        x = self.neck_3d(x)
        return x


class CameraEncoder(BaseEncoder):
    def __init__(self, camera_encoder_cfg: mmcv.Config, **kwargs):
        super().__init__()
        img_backbone = camera_encoder_cfg["img_backbone"]
        if "type" not in img_backbone or img_backbone["type"] == "ResNet":
            img_backbone.pop("type", None)
            self.img_backbone = ResNet(**img_backbone)
        elif img_backbone["type"] == "VoVNetCP":
            img_backbone.pop("type")
            self.img_backbone = VoVNetCP(**img_backbone)
        else:
            raise NotImplementedError(f"unsupported img_backbone {img_backbone['type']}")
        if "type" in camera_encoder_cfg["img_neck"]:
            if camera_encoder_cfg["img_neck"]["type"] not in ["CPFPN", "SECONDFPN"]:
                raise NotImplementedError(f"unsupported img_neck {camera_encoder_cfg['img_neck']['type']}")
            type = camera_encoder_cfg["img_neck"].pop("type")
            if type == "CPFPN":
                self.img_neck = CPFPN(**camera_encoder_cfg["img_neck"])
            elif type == "SECONDFPN":
                self.img_neck = SECONDFPN(**camera_encoder_cfg["img_neck"])

        if not hasattr(self, "img_neck"):
            self.img_neck = CPFPN(**camera_encoder_cfg["img_neck"])
            self.map_neck = CPFPN(**camera_encoder_cfg["img_neck"])

    # @ForceFp32(apply_to=("imgs"))
    def forward(
        self,
        imgs: torch.tensor,
        train_backbone: bool = True,
    ):
        context = nullcontext()
        if not train_backbone:
            context = torch.no_grad()

        batch_size, sweep, view_num, channel, height, width = imgs.shape
        imgs = imgs.view(batch_size * sweep * view_num, channel, height, width)
        with context:
            img_feats = self.img_backbone(imgs)
            map_feats = (img_feats[0].clone(), img_feats[1].clone())
        img_feats = self.img_neck(img_feats)
        map_feats = self.map_neck(map_feats)

        return img_feats, map_feats


class RadarEncoder(BaseEncoder):
    def __init__(self, radar_encoder_cfg: mmcv.Config, **kwargs):
        super().__init__()
        self.radar_backbone = self.build_radar_backbone(radar_encoder_cfg["out_channels"])

    def build_radar_backbone(self, out_channels):
        mlp_layers = []

        for i in range(0, len(out_channels) - 1):
            mlp_layers.append(
                nn.Linear(
                    in_features=out_channels[i],
                    out_features=out_channels[i + 1],
                )
            )
            mlp_layers.append(nn.BatchNorm1d(out_channels[i + 1]))
            mlp_layers.append(nn.ReLU(inplace=True))

        return nn.Sequential(*mlp_layers)

    def forward(
        self,
        radar_points: torch.tensor,
    ):
        radar_feats = self.radar_backbone(radar_points)
        return radar_feats


class DetHead(nn.Module):
    def __init__(
        self, det_head_cfg: mmcv.Config, train_cfg: mmcv.Config = None, test_cfg: mmcv.Config = None, **kwargs
    ):
        super().__init__()
        self.det_head_cfg = det_head_cfg
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg
        self.dense_head = self.build_dense_head()

    def build_dense_head(self):
        dense_head_module = CMTFusionHead(
            **self.det_head_cfg,
            train_cfg=self.train_cfg,
            test_cfg=self.test_cfg,
        )
        return dense_head_module

    @ForceFp32(
        apply_to=("lidar_feature", "img_feature", "radar_feature", "radar_points", "img_metas", "gt_boxes", "roi_mask")
    )
    def forward(
        self,
        lidar_feature: torch.tensor,
        img_feature: torch.tensor,
        img_metas: dict,
        gt_boxes: torch.tensor = None,
        radar_feature: torch.tensor = None,
        radar_points: torch.tensor = None,
        roi_mask: torch.tensor = None,
    ) -> Any:  # todo: 趁重构，可以趁机将参数也重排一下。
        if img_feature is None:
            img_feature = [None]
        else:
            img_feature = list(img_feature)
        forward_ret_dict = self.dense_head(lidar_feature, img_feature, img_metas, gt_boxes, radar_feature, radar_points)
        losses = None
        if gt_boxes is not None:
            with torch.cuda.amp.autocast(enabled=False):
                losses = self.dense_head.loss(gt_boxes, forward_ret_dict, roi_mask)
                return forward_ret_dict, losses
        else:
            # return self.dense_head.bbox_coder.decode(forward_ret_dict), losses # htt: 为什么master code要重新包装一下forward_ret_dict
            return forward_ret_dict, losses  #


class CMT(BaseMultiSensorFusion):
    r"""
    `BEVFusion`: Multi-Task Multi-Sensor Fusion with Unified Bird's-Eye View Representation.

    `Reference`: https://arxiv.org/abs/2205.13542
    """

    def __init__(self, model_cfg) -> Any:
        super(CMT, self).__init__()
        self.num_class = len(model_cfg.class_names)
        self.class_names = model_cfg.class_names
        self.cfg = model_cfg

        if self.cfg.get("lidar_encoder", None):
            self.lidar_encoder = self._configure_lidar_encoder()
        else:
            self.lidar_encoder = None

        if self.cfg.get("camera_encoder", None):
            self.camera_encoder = self._configure_camera_encoder()
        else:
            self.camera_encoder = None

        if self.cfg.get("radar_encoder", None):
            self.radar_encoder = self._configure_radar_encoder()
        else:
            self.radar_encoder = None

        self.det_head = self._configure_det_head()

        if self.cfg.get("grid_mask", None):
            self.grid_mask = GridMask(
                True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=self.cfg.get("grid_mask_prob", 0.7)
            )
        else:
            self.grid_mask = None

        if self.cfg.get("modal_mask", None):
            self.modal_mask = ModalMask3D(mode="train", mask_modal=None)
        else:
            self.modal_mask = None

    def forward(
        self,
        lidar_points: List[torch.tensor] = None,
        cameras_imgs: torch.tensor = None,
        metas: Dict[str, torch.tensor] = None,
        gt_boxes: torch.tensor = None,
        radar_points: List[torch.tensor] = None,
        roi_mask: torch.tensor = None,
        **kwargs,
    ) -> Any:

        if self.modal_mask is not None:
            lidar_points, cameras_imgs = self.modal_mask(lidar_points, cameras_imgs)
        if self.with_lidar_encoder:
            lidar_output = self.lidar_encoder(lidar_points)
        else:
            lidar_output = [None]

        if self.with_camera_encoder:
            cameras_imgs = cameras_imgs.unsqueeze(1)  # fake sweeps, no "sweep" dim in imgs from current dataset.
            if self.grid_mask is not None:
                B, S, N, C, H, W = cameras_imgs.shape
                cameras_imgs = cameras_imgs.view(B * S * N, C, H, W)
                cameras_imgs = self.grid_mask(cameras_imgs)
                cameras_imgs = cameras_imgs.view(B, S, N, C, H, W)
            camera_output = self.camera_encoder(cameras_imgs)
            height, weight = cameras_imgs.shape[-2], cameras_imgs.shape[-1]
            metas["pad_shape"] = [height, weight]
        else:
            camera_output = None
        if self.with_radar_encoder:
            radar_points_view = radar_points.float().view(-1, 11)
            B, N = radar_points.shape[:2]
            radar_output_feat = self.radar_encoder(radar_points_view.clone())
            C = radar_output_feat.shape[-1]
            radar_output = radar_output_feat.reshape(B, N, C)
            radar_points = radar_points[..., :3]
        else:
            radar_output = None
            radar_points = None

        # check
        forward_ret_dict, loss_dict = self.det_head(
            lidar_output, camera_output, metas, gt_boxes, radar_output, radar_points, roi_mask
        )
        if self.training:
            return forward_ret_dict, loss_dict, {}
        else:
            return forward_ret_dict

    def train(
        self,
        mode: bool = True,
    ):
        super(CMT, self).train(mode)
        if self.modal_mask:
            if mode:
                self.modal_mask.mode = "train"
            else:
                self.modal_mask.mode = "test"

    def _configure_lidar_encoder(self):
        return LidarEncoder(self.cfg.lidar_encoder)

    def _configure_camera_encoder(self):
        return CameraEncoder(self.cfg.camera_encoder)

    def _configure_radar_encoder(self):
        return RadarEncoder(self.cfg.radar_encoder)

    def _configure_det_head(self):
        return DetHead(self.cfg.det_head, self.cfg.train_cfg["pts"], self.cfg.test_cfg["pts"])
