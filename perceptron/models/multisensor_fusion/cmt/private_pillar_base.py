from perceptron.models.multisensor_fusion.cmt.private_base import CMTPrivate
import mmcv
import torch
from typing import Any, List

from perceptron.models.multisensor_fusion import BaseEncoder, ForceFp32
from perceptron.layers.blocks_2d.det3d import PointPillarScatter
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.layers.blocks_3d.det3d import PillarVFE
from perceptron.layers.blocks_2d.det3d import BaseBEVBackbone


class LidarEncoder(BaseEncoder):
    def __init__(self, lidar_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super().__init__()
        self.cfg = lidar_encoder_cfg
        self.voxelizer = self.build_voxelizer()
        self.vfe = self.build_vfe()
        self.map_to_bev = self.build_map_to_bev()
        self.backbone_2d = self.build_backbone_2d()

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
            max_num_points=self.cfg.max_num_points,
            max_voxels=self.cfg.max_voxels,
            num_point_features=self.cfg.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = PillarVFE(
            use_norm=True,
            with_distance=False,
            use_absolute_xyz=True,
            num_filters=self.cfg.vfe_num_filters,
            num_point_features=self.cfg.use_num_point_features,
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
        )
        return vfe

    def build_map_to_bev(self):
        return PointPillarScatter(
            num_bev_features=self.cfg.map_to_bev_num_features,
            grid_size=self.cfg.grid_size,
        )

    def build_backbone_2d(self):
        assert (
            self.map_to_bev.num_bev_features == self.cfg.backbone_2d.input_channels
        ), f"Unmatched num_bev_features: {self.map_to_bev.num_bev_features} vs {self.cfg.backbone_2d.input_channels} !!!"
        backbone_2d_module = BaseBEVBackbone(**self.cfg.backbone_2d)
        return backbone_2d_module

    @ForceFp32(apply_to=("lidar_points"))  # voxelizer只能接受fp32类型的输入
    def forward(self, lidar_points: List[torch.tensor]) -> torch.tensor:
        voxels, voxel_coords, voxel_num_points = self.voxelizer(lidar_points)
        # 要测half model的话，额外调用 voxels = voxels.half()
        pillar_features = self.vfe(voxels, voxel_coords, voxel_num_points)
        spatial_features = self.map_to_bev(pillar_features, voxel_coords)
        spatial_features_2d, pyramid = self.backbone_2d(spatial_features)
        return [spatial_features_2d]


class CMTPillar(CMTPrivate):
    def __init__(self, model_cfg):
        super(CMTPillar, self).__init__(model_cfg)

    def _configure_lidar_encoder(self):
        return LidarEncoder(self.cfg.lidar_encoder)
