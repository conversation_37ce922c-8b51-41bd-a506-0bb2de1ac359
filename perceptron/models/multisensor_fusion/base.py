import torch
import functools
import numpy as np
import torch.nn as nn
from collections import abc
from pathlib import Path

from inspect import getfullargspec
from torch.cuda.amp import autocast
from mmcv.runner.fp16_utils import digit_version
from typing import Any, Optional, Callable, Tuple
from abc import <PERSON><PERSON>eta, abstractmethod
from torch.nn.modules.batchnorm import _BatchNorm

try:
    from pyinstrument import Profiler
except ImportError:
    raise ImportError("Please install Profiler!")

_TENSOR = torch.Tensor
_TORCH_NN_MODULE = nn.Module
TORCH_VERSION = torch.__version__


class BaseMultiSensorFusion(nn.Module):

    """Base class for multi-sensor fusion perception."""

    def __init__(self) -> Any:
        super(BaseMultiSensorFusion, self).__init__()

    @property
    def with_lidar_encoder(self) -> Optional[_TORCH_NN_MODULE]:
        """bool: whether has a lidar encoder"""
        return hasattr(self, "lidar_encoder") and self.lidar_encoder

    @property
    def with_camera_encoder(self) -> Optional[_TORCH_NN_MODULE]:
        """bool: whether has a cameras encoder"""
        return hasattr(self, "camera_encoder") and self.camera_encoder

    @property
    def with_side_camera_encoder(self) -> Optional[_TORCH_NN_MODULE]:
        """bool: whether has a cameras encoder"""
        return hasattr(self, "side_camera_encoder") and self.side_camera_encoder

    @property
    def with_radar_encoder(self) -> Optional[_TORCH_NN_MODULE]:
        """bool: whether has a radar encoder"""
        return hasattr(self, "radar_encoder") and self.radar_encoder

    @property
    def with_fusion_encoder(self):
        """bool: whether has a features fusion module."""
        return hasattr(self, "fusion_encoder") and self.fusion_encoder

    @property
    def with_prediction_module(self):
        """bool: whether has a motion module."""
        return hasattr(self, "prediction_module") and self.prediction_module

    @property
    def with_map_module(self):
        """bool: whether has a map module."""
        return hasattr(self, "map_module") and self.map_module

    @abstractmethod
    def extract_feat(self, **kwargs):
        """Extract features from all sensors."""
        raise NotImplementedError("Must be implemented by yourself!")

    def onnx_export(self, **kwargs) -> Any:
        raise NotImplementedError(f"{self.__class__.__name__} does " f"not support ONNX EXPORT")


class BaseEncoder(nn.Module, metaclass=ABCMeta):
    def __init__(self, frozen_encoder: bool = False, frozen_bn: bool = False):
        super(BaseEncoder, self).__init__()
        self.frozen_encoder = frozen_encoder
        self.frozen_bn = frozen_bn

    def _freeze_stages(self):
        if self.frozen_encoder:
            for param in self.parameters():
                param.requires_grad = False

    def train(self, mode=True):
        super(BaseEncoder, self).train(mode)
        self._freeze_stages()
        if mode and self.frozen_bn:
            for m in self.modules():
                if isinstance(m, _BatchNorm):
                    m.eval()


class ForceFp32:
    """Decorator to convert input arguments to `fp32 in force`.
    #### Args:
        `apply_to` (Iterable, optional): The argument names to be converted.
            `None` indicates all arguments.
        `out_fp16` (bool): Whether to convert the output back to `fp16`.
    #### Examples:
    --------

    ```
    class MyModule(torch.nn.Module):
        def __init__(self):
            pass

        @ForceFp32(apply_to=("x"))
        def foward(self, x: Any, ...):
            pass
    ```

    """

    def __init__(self, apply_to: Optional[Tuple[str]] = None, out_fp16: bool = False) -> None:
        self.apply_to = apply_to
        self.out_fp16 = out_fp16

    def __call__(self, func: Callable) -> None:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any):
            # check if the module has set the attribute `fp16_enabled`, if not,
            # just fallback to the original method.
            if not isinstance(args[0], torch.nn.Module):
                raise TypeError("@force_fp32 can only be used to decorate the " "method of nn.Module")
            # get the arg spec of the decorated method
            args_info = getfullargspec(func)
            args_to_cast = args_info.args if self.apply_to is None else self.apply_to
            # convert the args that need to be processed
            new_args = []
            if args:
                arg_names = args_info.args[: len(args)]
                for i, arg_name in enumerate(arg_names):
                    if arg_name in args_to_cast:
                        new_args.append(self.cast_tensor_type(args[i], torch.half, torch.float))
                    else:
                        new_args.append(args[i])
            # convert the kwargs that need to be processed
            new_kwargs = dict()
            if kwargs:
                for arg_name, arg_value in kwargs.items():
                    if arg_name in args_to_cast:
                        new_kwargs[arg_name] = self.cast_tensor_type(arg_value, torch.half, torch.float)
                    else:
                        new_kwargs[arg_name] = arg_value

            # apply converted arguments to the decorated method
            if TORCH_VERSION != "parrots" and digit_version(TORCH_VERSION) >= digit_version("1.6.0"):
                with autocast(enabled=False):
                    output = func(*new_args, **new_kwargs)
            else:
                output = func(*new_args, **new_kwargs)
            # cast the results back to fp32 if necessary
            if self.out_fp16:
                output = self.cast_tensor_type(output, torch.float, torch.half)
            return output

        return wrapper

    @staticmethod
    def cast_tensor_type(inputs: Any, src_type: str, dst_type: str) -> Any:
        """Recursively convert Tensor in inputs from src_type to dst_type.

        Args:
            inputs: Inputs that to be casted.
            src_type (torch.dtype): Source type..
            dst_type (torch.dtype): Destination type.

        Returns:
            The same type with inputs, but all contained Tensors have been cast.
        """
        if isinstance(inputs, nn.Module):
            return inputs
        elif isinstance(inputs, torch.Tensor):
            return inputs.to(dst_type)
        elif isinstance(inputs, str):
            return inputs
        elif isinstance(inputs, np.ndarray):
            return inputs
        elif isinstance(inputs, abc.Mapping):
            return type(inputs)({k: ForceFp32.cast_tensor_type(v, src_type, dst_type) for k, v in inputs.items()})
        elif isinstance(inputs, abc.Iterable):
            return type(inputs)(ForceFp32.cast_tensor_type(item, src_type, dst_type) for item in inputs)
        else:
            return inputs


class CustomerProfiler:
    """Decorator of pyinstrument."""

    def __init__(self, out_dir: str = None, **kwargs) -> None:
        self.profiler = Profiler(**kwargs)
        self.out_dir = Path.cwd() if out_dir is None else out_dir

    def __call__(self, func: Callable[[Any], Any]) -> Any:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any):
            self.profiler.start()
            output = func(*args, **kwargs)
            self.profiler.stop()

            results_file = f"{self.out_dir}/profiling.html"
            with open(results_file, "w", encoding="utf-8") as f_html:
                f_html.write(self.profiler.output_html())
            print(self.profiler.output_text(unicode=True, color=True))
            return output

        return wrapper


class AutoFp16:
    """Decorator to enable fp16 training automatically.

    Args:
        apply_to (Iterable, optional): The argument names to be converted.
            `None` indicates all arguments.
        out_fp32 (bool): Whether to convert the output back to fp32.

    ```
    class MyModule(torch.nn.Module):
        def __init__(self):
            pass

        @AutoFp16(apply_to=("x"))
        def foward(self, x: Any, ...):
            pass
    ```

    """

    def __init__(self, apply_to: Optional[Tuple[str]] = None, out_fp32: bool = False) -> None:
        self.apply_to = apply_to
        self.out_fp32 = out_fp32

    def __call__(self, func: Callable) -> None:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any):
            if not isinstance(args[0], torch.nn.Module):
                raise TypeError("@auto_fp16 can only be used to decorate the " "method of nn.Module")

            # get the arg spec of the decorated method
            args_info = getfullargspec(func)
            # get the argument names to be casted
            args_to_cast = args_info.args if self.apply_to is None else self.apply_to
            # convert the args that need to be processed
            new_args = []
            # NOTE: default args are not taken into consideration
            if args:
                arg_names = args_info.args[: len(args)]
                for i, arg_name in enumerate(arg_names):
                    if arg_name in args_to_cast:
                        new_args.append(self.cast_tensor_type(args[i], torch.float, torch.half))
                    else:
                        new_args.append(args[i])
            # convert the kwargs that need to be processed
            new_kwargs = {}
            if kwargs:
                for arg_name, arg_value in kwargs.items():
                    if arg_name in args_to_cast:
                        new_kwargs[arg_name] = self.cast_tensor_type(arg_value, torch.float, torch.half)
                    else:
                        new_kwargs[arg_name] = arg_value
            # apply converted arguments to the decorated method
            if TORCH_VERSION != "parrots" and digit_version(TORCH_VERSION) >= digit_version("1.6.0"):
                with autocast(enabled=True):
                    output = func(*new_args, **new_kwargs)
            else:
                output = func(*new_args, **new_kwargs)
            # cast the results back to fp32 if necessary
            if self.out_fp32:
                output = self.cast_tensor_type(output, torch.half, torch.float)
            return output

        return wrapper

    @staticmethod
    def cast_tensor_type(inputs: Any, src_type: str, dst_type: str) -> Any:
        """Recursively convert Tensor in inputs from src_type to dst_type.

        Args:
            inputs: Inputs that to be casted.
            src_type (torch.dtype): Source type..
            dst_type (torch.dtype): Destination type.

        Returns:
            The same type with inputs, but all contained Tensors have been cast.
        """
        if isinstance(inputs, nn.Module):
            return inputs
        elif isinstance(inputs, torch.Tensor):
            return inputs.to(dst_type)
        elif isinstance(inputs, str):
            return inputs
        elif isinstance(inputs, np.ndarray):
            return inputs
        elif isinstance(inputs, abc.Mapping):
            return type(inputs)({k: AutoFp16.cast_tensor_type(v, src_type, dst_type) for k, v in inputs.items()})
        elif isinstance(inputs, abc.Iterable):
            return type(inputs)(AutoFp16.cast_tensor_type(item, src_type, dst_type) for item in inputs)
        else:
            return inputs
