import math

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from mmcv.cnn import ConvModule
from mmdet3d.models import ResNet, build_backbone, build_neck
from mmdet3d.models.builder import BACKBONES
from mmdet.models.backbones.resnet import BasicBlock
from mmdet.models.utils import ResLayer
from perceptron_ops.voxel_pooling import voxel_pooling
from scipy.optimize import linear_sum_assignment

from perceptron.utils import torch_dist as dist


@BACKBONES.register_module()
class ResNetNoPool(ResNet):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        del self.maxpool

    # https://github.com/open-mmlab/mmdetection/blob/master/mmdet/models/backbones/resnet.py#L631
    def forward(self, x):
        if self.deep_stem:
            x = self.stem(x)
        else:
            x = self.conv1(x)
            x = self.norm1(x)
            x = self.relu(x)
        # x = self.maxpool(x) NOTE: del maxpool
        outs = []
        for i, layer_name in enumerate(self.res_layers):
            res_layer = getattr(self, layer_name)
            x = res_layer(x)
            if i in self.out_indices:
                outs.append(x)
        return tuple(outs)


class BasicLayers(nn.Module):
    def __init__(self, cfg):
        super().__init__()
        self.res_layers = nn.ModuleList()
        for i in range(len(cfg.channels)):
            layer = ResLayer(
                block=BasicBlock,
                inplanes=cfg.channels[i - 1] if i else cfg.in_channels,
                planes=cfg.channels[i],
                num_blocks=cfg.num_blocks[i],
                stride=cfg.strides[i],
            )
            self.res_layers.append(layer)

    def forward(self, x):
        out = []
        for layer in self.res_layers:
            x = layer(x)
            out.append(x)
        return out


class LSSViewTransformer(nn.Module):
    def __init__(self, cfg):
        super().__init__()
        self.cfg = cfg
        self.voxel_lb = nn.Parameter(torch.Tensor(cfg.voxel_range[::2]), requires_grad=False)
        self.voxel_size = nn.Parameter(torch.Tensor(cfg.voxel_size), requires_grad=False)
        self.num_voxel = nn.Parameter(
            torch.LongTensor(
                [(cfg.voxel_range[2 * i + 1] - cfg.voxel_range[2 * i]) // cfg.voxel_size[i] for i in range(3)]
            ),
            requires_grad=False,
        )
        self.depth_channels = math.ceil((cfg.depth_range[1] - cfg.depth_range[0]) / cfg.depth_step)
        self.out_channels = cfg.out_channels
        self.lift_conv = nn.Conv2d(cfg.in_channels, self.depth_channels + self.out_channels, kernel_size=1, padding=0)

    def lift(self, x):
        B, _, H, W = x.shape
        x = self.lift_conv(x)
        depth, context = torch.split(x, [self.depth_channels, self.out_channels], dim=1)
        depth = depth.softmax(dim=1).type(depth.dtype)
        x = depth.unsqueeze(1) * context.unsqueeze(2)
        x = x.view(B, self.out_channels, self.depth_channels, H, W)
        x = x.permute(0, 2, 3, 4, 1)  # [B, D, H, W, C]
        return x

    def splat(self, x, frustum_coord):
        frustum_coord = frustum_coord.permute(0, 3, 1, 2, 4)  # [B, D, H, W, 3]
        voxel_index = ((frustum_coord - self.voxel_lb) / self.voxel_size).int().contiguous()
        x_bev = voxel_pooling(voxel_index, x.contiguous(), self.num_voxel).contiguous()
        return x_bev

    def forward(self, x, frustum_coord):
        x = self.lift(x)
        x = self.splat(x, frustum_coord)
        return x


class RowAnchorHead(nn.Module):
    def __init__(self, cfg):
        super().__init__()
        self.cfg = cfg
        self.share_conv = ConvModule(
            cfg.in_channels,
            cfg.share_conv_channel,
            kernel_size=3,
            padding=1,
            norm_cfg=dict(type="BN2d"),
        )
        self.branch_head = nn.ModuleDict()
        self.branch_head.add_module(
            "obj",
            nn.Sequential(
                ConvModule(cfg.share_conv_channel, cfg.head_conv, kernel_size=1, norm_cfg=dict(type="BN2d")),
                nn.AdaptiveMaxPool2d((1, 1)),
                nn.Conv2d(cfg.head_conv, self.cfg.n_instances, kernel_size=1),
            ),
        )
        self.branch_head.add_module(
            "cls",
            nn.Sequential(
                ConvModule(cfg.share_conv_channel, cfg.head_conv, kernel_size=1, norm_cfg=dict(type="BN2d")),
                nn.AdaptiveMaxPool2d((1, 1)),
                nn.Conv2d(cfg.head_conv, self.cfg.n_instances * cfg.n_classes, kernel_size=1),
            ),
        )
        self.branch_head.add_module(
            "vis",
            nn.Sequential(
                ConvModule(cfg.share_conv_channel, cfg.head_conv, kernel_size=1, norm_cfg=dict(type="BN2d")),
                nn.AdaptiveMaxPool2d((None, 1)),
                nn.Conv2d(cfg.head_conv, self.cfg.n_instances, kernel_size=1),
            ),
        )
        for k in ["row", "reg_x", "reg_z"]:
            self.branch_head.add_module(
                k,
                nn.Sequential(
                    ConvModule(cfg.share_conv_channel, cfg.head_conv, kernel_size=1, norm_cfg=dict(type="BN2d")),
                    nn.Conv2d(cfg.share_conv_channel, self.cfg.n_instances, kernel_size=1),
                ),
            )

    @torch.no_grad()
    def match(self, output, targets):
        indices = []
        for b, tgt in enumerate(targets):
            n_tgt = len(tgt["cls"])

            if n_tgt == 0:
                indices.append([[], []])
                continue

            cost = 0

            obj = output["obj"][b].flatten(1)
            tgt_obj = torch.ones_like(obj)
            cost = cost + F.binary_cross_entropy_with_logits(obj, tgt_obj, reduction="none")

            cls = output["cls"][b].reshape(-1, self.cfg.n_classes, 1).expand([-1, -1, n_tgt])
            tgt_cls = tgt["cls"][None, ...].expand([self.cfg.n_instances, -1])
            cost = cost + F.cross_entropy(cls, tgt_cls, reduction="none")

            vis = output["vis"][b][:, None, :, 0].expand([-1, n_tgt, -1])
            tgt_vis = tgt["vis"][None, ...].expand([self.cfg.n_instances, -1, -1]).float()
            cost = cost + F.binary_cross_entropy_with_logits(vis, tgt_vis, reduction="none").mean(-1)

            row = output["row"][b][:, None, ...].expand([-1, n_tgt, -1, -1])
            tgt_row = tgt["row"][None, ...].expand([self.cfg.n_instances, -1, -1])
            cost_row_all = F.cross_entropy(row.flatten(0, 2), tgt_row.flatten(), reduction="none").reshape(
                self.cfg.n_instances, n_tgt, -1
            )
            cost = cost + (cost_row_all * tgt_vis).sum(-1) / tgt_vis.sum(-1)

            for reg_key in ["reg_x", "reg_z"]:
                reg = (
                    output[reg_key][b][:, None, ...].expand([-1, n_tgt, -1, -1]).gather(-1, tgt_row[..., None])[..., 0]
                )
                tgt_reg = targets[b][reg_key][None, ...].expand([self.cfg.n_instances, -1, -1])
                cost = cost + (F.l1_loss(reg, tgt_reg) * tgt_vis).sum(-1) / tgt_vis.sum(-1)

            indices.append(linear_sum_assignment(cost.detach().cpu().numpy()))
        return [(torch.as_tensor(i, dtype=torch.int64), torch.as_tensor(j, dtype=torch.int64)) for i, j in indices]

    def loss(self, output, targets):
        n_lane = 0
        indices = self.match(output, targets)
        loss_dict = {k: 0.0 for k in ["obj", "cls", "vis", "row", "reg_x", "reg_z"]}
        for b, (i, j) in enumerate(indices):
            obj = output["obj"][b].flatten()
            tgt_obj = torch.zeros_like(obj)
            tgt_obj[i] = 1.0
            loss_dict["obj"] += F.binary_cross_entropy_with_logits(obj, tgt_obj, reduction="sum")

            cls = output["cls"][b].reshape(-1, self.cfg.n_classes)[i]
            tgt_cls = targets[b]["cls"][j]
            loss_dict["cls"] += F.cross_entropy(cls, tgt_cls, reduction="sum")

            vis = output["vis"][b, i].flatten(1)
            tgt_vis = targets[b]["vis"][j]
            loss_vis = F.binary_cross_entropy_with_logits(vis, tgt_vis.float(), reduction="none")
            loss_dict["vis"] += loss_vis.mean(-1).sum()

            row = output["row"][b, i]
            tgt_row = targets[b]["row"][j]
            N, H, W = row.shape
            loss_row = F.cross_entropy(row.reshape(-1, W), tgt_row.flatten(), reduction="none").reshape([N, H])
            loss_dict["row"] += ((loss_row * tgt_vis).sum(-1) / tgt_vis.sum(-1)).sum()

            for reg_key in ["reg_x", "reg_z"]:
                reg = output[reg_key][b, i].gather(-1, tgt_row[..., None])[..., 0]
                tgt_reg = targets[b][reg_key][j]
                loss_reg = F.l1_loss(reg, tgt_reg, reduction="none")
                loss_dict[reg_key] += ((loss_reg * tgt_vis).sum(-1) / tgt_vis.sum(-1)).sum()

            n_lane += len(j)

        n_lane = dist.reduce_mean(torch.Tensor([n_lane]).cuda()).item()

        for k in loss_dict:
            loss_dict[k] /= max(n_lane, 1)

        return loss_dict

    def post_process(self, output):
        score = output["obj"].reshape(-1, self.cfg.n_instances).sigmoid()
        category = output["cls"].reshape(-1, self.cfg.n_instances, self.cfg.n_classes).argmax(-1)
        vis = output["vis"].flatten(-2) > 0
        batch_pred = []
        for i in range(len(score)):
            pred = dict(
                lanes=[],
                scores=[],
                categories=[],
            )
            for j in range(self.cfg.n_instances):
                if hasattr(self.cfg.post_process, "score_thresh") and score[i, j] < self.cfg.post_process.score_thresh:
                    continue
                v = vis[i, j].cpu().detach().numpy()
                if getattr(self.cfg.post_process, "pad_end", False):
                    v_idx = np.where(v)[0]
                    if len(v_idx) > 0:
                        v[v_idx[0] :] = True
                rid = output["row"][i, j][v].argmax(-1).detach().cpu().numpy()
                cid = np.arange(len(rid))
                x = self.cfg.x_steps[rid]
                x = x + output["reg_x"][i, j][v].detach().cpu().numpy()[cid, rid] * self.cfg.grid_size[0]
                y = self.cfg.y_steps[v]
                z = output["reg_z"][i, j][v].detach().cpu().numpy()[cid, rid]
                z = z * (self.cfg.z_range[1] - self.cfg.z_range[0]) + self.cfg.z_range[0]
                pred["lanes"].append(np.stack([x, y, z]).T)
                pred["scores"].append(score[i, j].item())
                pred["categories"].append(category[i, j].item())
            batch_pred.append(pred)
        return batch_pred

    def forward(self, x):
        x = self.share_conv(x)
        output = {k: m(x) for k, m in self.branch_head.items()}
        return output


class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.ReLU, drop=0.1):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.drop1 = nn.Dropout(drop)
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop2 = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop1(x)
        x = self.fc2(x)
        x = self.drop2(x)
        return x


class BEVLaneFCN(nn.Module):
    def __init__(self, cfg):
        super().__init__()
        self.cfg = cfg
        self.img_backbone = self._configure_img_backbone()
        self.img_neck = self._configure_img_neck()
        self.view_transformer = self._configure_view_transformer()
        if hasattr(cfg, "bev_pe"):
            self.bev_pe = self._configure_bev_pe()
        self.bev_backbone = self._configure_bev_backbone()
        self.bev_neck = self._configure_bev_neck()
        self.lanedet_head = self._configure_lanedet_head()

    def _configure_img_backbone(self):
        m = build_backbone(self.cfg.img_backbone)
        m.init_weights()
        return m

    def _configure_img_neck(self):
        m = build_neck(self.cfg.img_neck)
        m.init_weights()
        return m

    def _configure_view_transformer(self):
        if self.cfg.view_transformer.type == "LSS":
            return LSSViewTransformer(self.cfg.view_transformer)
        else:
            raise NotImplementedError

    def _configure_bev_pe(self):
        if self.cfg.bev_pe.type == "learnable":
            return Mlp(2, out_features=self.cfg.lanedet_head.in_channels)
        else:
            raise NotImplementedError

    def _configure_bev_backbone(self):
        if self.cfg.bev_backbone.type == "BasicLayers":
            return BasicLayers(self.cfg.bev_backbone)
        m = build_backbone(self.cfg.bev_backbone)
        m.init_weights()
        return m

    def _configure_bev_neck(self):
        m = build_neck(self.cfg.bev_neck)
        m.init_weights()
        return m

    def _configure_lanedet_head(self):
        if self.cfg.lanedet_head.type == "RowAnchorHead":
            return RowAnchorHead(self.cfg.lanedet_head)
        else:
            raise NotImplementedError

    def forward(self, batch):
        x = batch["img"]
        x = self.img_backbone(x)
        x = self.img_neck(x)[0]
        x = self.view_transformer(x, batch["frustum_coord"])
        x = [x, *self.bev_backbone(x)]
        x = self.bev_neck(x)[0]
        if hasattr(self, "bev_pe"):
            x = x + self.bev_pe(batch["bev_coord"]).permute(0, 3, 1, 2)
        output = self.lanedet_head(x)
        if self.training:
            return self.lanedet_head.loss(output, batch["targets"])
        else:
            return self.lanedet_head.post_process(output)
