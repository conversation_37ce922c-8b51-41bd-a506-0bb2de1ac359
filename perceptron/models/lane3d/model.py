import torch.nn as nn
from mmdet.models import build_backbone
from mmdet3d.models import build_neck
from perceptron.layers.head.lane3d import CondLane3DHead, RVSegmentationHead
from perceptron.layers.blocks_3d.lane3d import B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BEVCondDecoder


class BEVCond3DLane(nn.Module):
    def __init__(self, model_config, *args, **kwargs):
        super(BEVCond3DLane, self).__init__()
        self.im_backbone = build_backbone(model_config["img_backbone"])
        self.im_backbone.init_weights()
        self.im_bkb_neck = build_neck(model_config["img_neck"])
        self.bev_encoder = BEVDetEncoder(**model_config["bev_encoder"])
        self.bev_decoder = BEVCondDecoder(**model_config["bev_decoder"])
        self.output_head = CondLane3DHead(**model_config["output_head"])
        self.rv_seg_head = RVSegmentationHead(**model_config["rvseg_head"])

    def forward(self, inputs):
        outputs = {
            k: inputs[k]
            for k in ["images", "targets", "trans_cam2ego", "trans_cam2img", "extra_infos", "ida_mats", "post_rot_bda"]
        }
        images = inputs["images"]
        images = images.view(-1, *images.shape[-3:])
        outputs["im_bkb_features"] = self.im_bkb_neck(self.im_backbone(images))
        outputs["im_bev_features"], outputs["im_rv_features"], outputs["im_depth"] = self.bev_encoder(outputs)
        outputs["rv_seg_res"] = self.rv_seg_head(outputs)
        outputs["instance_features"] = self.bev_decoder(outputs)
        outputs.update(self.output_head(outputs))
        return outputs
