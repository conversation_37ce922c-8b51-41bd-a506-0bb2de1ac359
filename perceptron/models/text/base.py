import torch.nn as nn
from transformers import AutoTokenizer, Siglip2TextModel
import itertools


class Siglip2Text(nn.Module):
    def __init__(self, model_name_or_path, frozen_modules, text_encoder_cfg, **kwargs):
        super().__init__()
        self.text_encoder = Siglip2TextModel.from_pretrained(model_name_or_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)
        self.frozen_modules = frozen_modules
        self.text_encoder_cfg = self.get_text_encoder_cfg(text_encoder_cfg)
        self._freeze_modules()

    def get_text_encoder_cfg(self, text_encoder_cfg):
        text_encoder_cfg = {
            "padding": text_encoder_cfg.get("padding", "max_length"),
            "truncation": text_encoder_cfg.get("truncation", True),
            "max_length": text_encoder_cfg.get("max_length", 64),
            "stride": text_encoder_cfg.get("stride", 0),
            "pad_to_multiple_of": text_encoder_cfg.get("pad_to_multiple_of", None),
            "padding_side": text_encoder_cfg.get("padding_side", "right"),
            "return_tensors": text_encoder_cfg.get("return_tensors", "pt"),
        }
        return text_encoder_cfg

    def forward(self, text, **kwargs):  # : List[List[str]]

        num_per_batch = [len(t) for t in text]
        assert max(num_per_batch) == min(num_per_batch), "number of sequences not equal in batch"
        text = list(itertools.chain(*text))
        text = self.tokenizer(text, **self.text_encoder_cfg)
        text = text.to(device=self.text_encoder.device)
        txt_outputs = self.text_encoder(**text)
        txt_feats = txt_outputs.pooler_output
        txt_feats = txt_feats.reshape(-1, num_per_batch[0], txt_feats.shape[-1])

        return txt_feats

    def _freeze_modules(
        self,
    ):
        if self.frozen_modules == -1:
            # not freeze
            return
        if self.frozen_modules == -2:
            self.text_encoder.eval()
            for _, module in self.text_encoder.named_modules():
                module.eval()
                for param in module.parameters():
                    param.requires_grad = False
            return
        for name, module in self.text_encoder.named_modules():
            for frozen_name in self.frozen_modules:
                if name.startswith(frozen_name):
                    module.eval()
                    for param in module.parameters():
                        param.requires_grad = False
                break


class TextEncoder(nn.Module):
    def __init__(self, text_encoder_cfg, **kwargs):  #: mmcv.Config
        super().__init__()
        model_name_or_path = text_encoder_cfg["model_name_or_path"]
        if "siglip2" in model_name_or_path:
            self.text_encoder = Siglip2Text(**text_encoder_cfg)
        else:
            raise NotImplementedError(f"unsupported text_encoder {model_name_or_path}")

    def forward(self, text):  #: List[List[str]]
        return self.text_encoder(text)
