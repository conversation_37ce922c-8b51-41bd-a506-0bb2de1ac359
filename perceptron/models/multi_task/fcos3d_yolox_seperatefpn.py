import torch
import torch.nn as nn
import refile

from mmdet3d.models.builder import build_backbone, build_head, build_neck
from mmcv.runner.base_module import BaseModule
from mmdet3d.models.detectors import FCOSMono3D
from mmdet3d.core import bbox3d2result
from mmdet.core import bbox2result

from yolox.models import YOLOXHead
from yolox.models.network_blocks import BaseConv, CSPLayer, DWConv


class PAFPN(nn.Module):
    """
    YOLOv3 PAFPN.
    """

    def __init__(
        self,
        depth=1.0,
        width=1.0,
        in_features=("dark3", "dark4", "dark5"),
        in_channels=[256, 512, 1024],
        depthwise=False,
        act="silu",
    ):
        super().__init__()
        self.in_features = in_features
        self.in_channels = in_channels
        Conv = DWConv if depthwise else BaseConv

        self.down_conv0 = BaseConv(int(in_channels[2] * 2 * width), int(in_channels[2] * width), 1, 1, act=act)
        self.down_conv1 = BaseConv(int(in_channels[1] * 2 * width), int(in_channels[1] * width), 1, 1, act=act)
        self.down_conv2 = BaseConv(int(in_channels[0] * 2 * width), int(in_channels[0] * width), 1, 1, act=act)

        self.upsample = nn.Upsample(scale_factor=2, mode="nearest")
        self.lateral_conv0 = BaseConv(int(in_channels[2] * width), int(in_channels[1] * width), 1, 1, act=act)
        self.C3_p4 = CSPLayer(
            int(2 * in_channels[1] * width),
            int(in_channels[1] * width),
            round(3 * depth),
            False,
            depthwise=depthwise,
            act=act,
        )  # cat

        self.reduce_conv1 = BaseConv(int(in_channels[1] * width), int(in_channels[0] * width), 1, 1, act=act)
        self.C3_p3 = CSPLayer(
            int(2 * in_channels[0] * width),
            int(in_channels[0] * width),
            round(3 * depth),
            False,
            depthwise=depthwise,
            act=act,
        )

        # bottom-up conv
        self.bu_conv2 = Conv(int(in_channels[0] * width), int(in_channels[0] * width), 3, 2, act=act)
        self.C3_n3 = CSPLayer(
            int(2 * in_channels[0] * width),
            int(in_channels[1] * width),
            round(3 * depth),
            False,
            depthwise=depthwise,
            act=act,
        )

        # bottom-up conv
        self.bu_conv1 = Conv(int(in_channels[1] * width), int(in_channels[1] * width), 3, 2, act=act)
        self.C3_n4 = CSPLayer(
            int(2 * in_channels[1] * width),
            int(in_channels[2] * width),
            round(3 * depth),
            False,
            depthwise=depthwise,
            act=act,
        )

    def forward(self, features):
        """
        Args:
            inputs: input features.

        Returns:
            Tuple[Tensor]: FPN feature.
        """

        [x2, x1, x0] = features

        x0 = self.down_conv0(x0)  # 2048->1024
        x1 = self.down_conv1(x1)  # 1024->512
        x2 = self.down_conv2(x2)  # 512->256

        fpn_out0 = self.lateral_conv0(x0)  # 1024->512/32
        f_out0 = self.upsample(fpn_out0)  # 512/16
        f_out0 = torch.cat([f_out0, x1], 1)  # 512->1024/16
        f_out0 = self.C3_p4(f_out0)  # 1024->512/16

        fpn_out1 = self.reduce_conv1(f_out0)  # 512->256/16
        f_out1 = self.upsample(fpn_out1)  # 256/8
        f_out1 = torch.cat([f_out1, x2], 1)  # 256->512/8
        pan_out2 = self.C3_p3(f_out1)  # 512->256/8

        p_out1 = self.bu_conv2(pan_out2)  # 256->256/16
        p_out1 = torch.cat([p_out1, fpn_out1], 1)  # 256->512/16
        pan_out1 = self.C3_n3(p_out1)  # 512->512/16

        p_out0 = self.bu_conv1(pan_out1)  # 512->512/32
        p_out0 = torch.cat([p_out0, fpn_out0], 1)  # 512->1024/32
        pan_out0 = self.C3_n4(p_out0)  # 1024->1024/32

        outputs = (pan_out2, pan_out1, pan_out0)
        return outputs


class Fcos3dYoloxModel(BaseModule):
    def __init__(self, fcos3d_config=None, yolox_config=None):
        super().__init__()

        self.fcos3d_config = fcos3d_config
        self.yolox_config = yolox_config
        if self.fcos3d_config.pretrained is not None:
            self.fcos3d_config.backbone.pretrained = self.fcos3d_config.pretrained
        self.backbone = build_backbone(self.fcos3d_config.backbone)

        # fcos3d module
        if self.fcos3d_config.neck is not None:
            self.fcos3d_neck = build_neck(self.fcos3d_config.neck)

        self.fcos3d_config.bbox_head.update(train_cfg=self.fcos3d_config.train_cfg)
        self.fcos3d_config.bbox_head.update(test_cfg=self.fcos3d_config.test_cfg)
        self.fcos3d_bbox_head = build_head(self.fcos3d_config.bbox_head)

        # yolox
        self.yolox_fpn = PAFPN(in_channels=yolox_config.in_channels)
        self.yolox_head = YOLOXHead(
            yolox_config.num_classes,
            yolox_config.width,
            in_channels=yolox_config.in_channels,
            act=yolox_config.act,
        )

        self.register_buffer("global_step", torch.LongTensor(1).zero_())

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    def is_parallel(self, model):
        """check if model is in parallel mode."""
        parallel_type = (
            nn.parallel.DataParallel,
            nn.parallel.DistributedDataParallel,
        )
        return isinstance(model, parallel_type)

    def custom_deep_copy(self, model):
        copyed_model = type(model)(fcos3d_config=model.fcos3d_config, yolox_config=model.yolox_config)
        state_dict = model.module.state_dict() if self.is_parallel(model) else model.state_dict()
        copyed_model.load_state_dict(state_dict)
        copyed_model.cuda()
        return copyed_model

    def load_params_from_file(self, filename, logger, to_cpu=False):
        if not refile.smart_isfile(filename):
            raise FileNotFoundError

        logger.info("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)
        model_state_disk = checkpoint["model_state"]

        if "version" in checkpoint:
            logger.info("==> Checkpoint trained from version: %s" % checkpoint["version"])

        update_model_state = {}
        for key, val in model_state_disk.items():
            if key in self.state_dict() and self.state_dict()[key].shape == model_state_disk[key].shape:
                update_model_state[key] = val

        state_dict = self.state_dict()
        state_dict.update(update_model_state)
        self.load_state_dict(state_dict)

        for key in state_dict:
            if key not in update_model_state:
                logger.info("Not updated weight %s: %s" % (key, str(state_dict[key].shape)))

        logger.info("==> Done (loaded %d/%d)" % (len(update_model_state), len(self.state_dict())))

    def load_yolox_pretrained_params(self, filename, to_cpu=False):
        if not refile.smart_isfile(filename):
            raise FileNotFoundError
        print("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)

        model_state_disk = checkpoint["model_state"]
        if "version" in checkpoint:
            print("==> Checkpoint trained from version: %s" % checkpoint["version"])

        update_model_state = {}
        for key, val in model_state_disk.items():
            if key[:5] == "head.":
                replace_key = "yolox_" + key
            elif key[:9] == "backbone.":
                replace_key = key.replace("backbone", "yolox_fpn")
            if replace_key in self.state_dict() and self.state_dict()[replace_key].shape == model_state_disk[key].shape:
                update_model_state[replace_key] = val

        state_dict = self.state_dict()
        state_dict.update(update_model_state)
        self.load_state_dict(state_dict)

        for key in state_dict:
            if key not in update_model_state:
                print("Not updated weight %s: %s" % (key, str(state_dict[key].shape)))
        print("==> Done (loaded %d/%d)" % (len(update_model_state), len(self.state_dict())))

    def load_params_with_optimizer(self, filename, to_cpu=False, optimizer=None, logger=None):
        if not refile.smart_isfile(filename):
            raise FileNotFoundError

        logger.info("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)
        epoch = checkpoint.get("epoch", -1)
        it = checkpoint.get("it", 0.0)

        self.load_state_dict(checkpoint["model_state"])

        if optimizer is not None:
            if "optimizer_state" in checkpoint and checkpoint["optimizer_state"] is not None:
                logger.info(
                    "==> Loading optimizer parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU")
                )
                optimizer.load_state_dict(checkpoint["optimizer_state"])
            else:
                assert filename[-4] == ".", filename
                src_file, ext = filename[:-4], filename[-3:]
                optimizer_filename = "%s_optim.%s" % (src_file, ext)
                if refile.smart_exists(optimizer_filename):
                    optimizer_ckpt = torch.load(optimizer_filename, map_location=loc_type)
                    optimizer.load_state_dict(optimizer_ckpt["optimizer_state"])

        if "version" in checkpoint:
            print("==> Checkpoint trained from version: %s" % checkpoint["version"])
        logger.info("==> Done")

        return it, epoch

    def onnx_export(self, img, img_metas):
        """Test function without test time augmentation.

        Args:
            img (torch.Tensor): input images.
            img_metas (list[dict]): List of image information.

        Returns:
            tuple[Tensor, Tensor]: dets of shape [N, num_det, 5]
                and class labels of shape [N, num_det].
        """
        # backbone
        x = self.backbone(img)

        # fcos3d
        x_fcos3d = self.fcos3d_neck(x)
        pred_fcos3d = self.fcos3d_forward_test(x_fcos3d, img_metas)

        # yolox
        fpn_outs = x_fcos3d[:3]
        pred_yolox = self.yolox_forward_test(fpn_outs)

        return pred_fcos3d, pred_yolox

    def fcos3d_forward_train(
        self,
        feat,
        img_metas,
        gt_bboxes,
        gt_labels,
        gt_bboxes_3d,
        gt_labels_3d,
        centers2d,
        depths,
        img=None,
        attr_labels=None,
        gt_bboxes_ignore=None,
    ):
        losses = self.fcos3d_bbox_head.forward_train(
            feat,
            img_metas,
            gt_bboxes,
            gt_labels,
            gt_bboxes_3d,
            gt_labels_3d,
            centers2d,
            depths,
            attr_labels,
            gt_bboxes_ignore,
        )
        return losses

    def yolox_forward_train(self, feat, labels=None, img=None):
        loss, iou_loss, conf_loss, cls_loss, l1_loss, num_fg = self.yolox_head(feat, labels, img)
        outputs = {
            "total_loss": loss,
            "iou_loss": iou_loss,
            "l1_loss": l1_loss,
            "conf_loss": conf_loss,
            "cls_loss": cls_loss,
            "num_fg": num_fg,
        }
        return outputs

    def yolox_forward_test(self, feat):
        outputs = self.yolox_head(feat)
        return outputs

    def fcos3d_forward_test(self, feat, img_metas, rescale=False):
        """Test function without test time augmentation.

        Args:
            imgs (list[torch.Tensor]): List of multiple images
            img_metas (list[dict]): List of image information.
            rescale (bool, optional): Whether to rescale the results.
                Defaults to False.

        Returns:
            list[list[np.ndarray]]: BBox results of each image and classes.
                The outer list corresponds to each image. The inner list
                corresponds to each class.
        """
        x = feat
        outs = self.fcos3d_bbox_head(x)
        bbox_outputs = self.fcos3d_bbox_head.get_bboxes(*outs, img_metas, rescale=rescale)

        if self.fcos3d_bbox_head.pred_bbox2d:
            bbox2d_img = [
                bbox2result(bboxes2d, labels, self.fcos3d_bbox_head.num_classes)
                for bboxes, scores, labels, attrs, bboxes2d in bbox_outputs
            ]
            bbox_outputs = [bbox_outputs[0][:-1]]

        bbox_img = [bbox3d2result(bboxes, scores, labels, attrs) for bboxes, scores, labels, attrs in bbox_outputs]

        bbox_list = [dict() for i in range(len(img_metas))]
        for result_dict, img_bbox in zip(bbox_list, bbox_img):
            result_dict["img_bbox"] = img_bbox
        if self.fcos3d_bbox_head.pred_bbox2d:
            for result_dict, img_bbox2d in zip(bbox_list, bbox2d_img):
                result_dict["img_bbox2d"] = img_bbox2d

        return bbox_list

    def forward(self, img, targets=None, tasks=None, loss_ratios=None):

        if self.training:
            x = self.backbone(img)
            assert targets is not None
            assert tasks is not None
            fcos3d_loss_scaled = 0
            yolox_loss_scaled = 0
            ext_dict = {}

            if "fcos3d" in tasks:
                fcos3d_fpn_outs = self.fcos3d_neck(x)
                fcos3d_losses = self.fcos3d_forward_train(fcos3d_fpn_outs, **targets)
                fcos3d_loss, tb_dict = FCOSMono3D._parse_losses(self, fcos3d_losses)
                fcos3d_loss = fcos3d_loss.mean()
                fcos3d_loss_scaled = fcos3d_loss * loss_ratios["fcos3d"]
                ext_dict["fcos3d_loss_scaled"] = fcos3d_loss_scaled
                ext_dict["fcos3d_loss_ori"] = fcos3d_loss

            if "yolox" in tasks:
                yolox_fpn_outs = self.yolox_fpn(x[1:])
                yolox_losses = self.yolox_forward_train(yolox_fpn_outs, targets["labels"], img)
                yolox_loss = yolox_losses["total_loss"]
                yolox_loss_scaled = yolox_loss * loss_ratios["yolox"]
                ext_dict["yolox_loss_scaled"] = yolox_loss_scaled
                ext_dict["yolox_loss_ori"] = yolox_loss

            loss = fcos3d_loss_scaled + yolox_loss_scaled
            return loss, ext_dict
        else:
            # backbone
            x = self.backbone(img)

            # fcos3d
            fcos3d_fpn_outs = self.fcos3d_neck(x)
            img_metas = targets["img_metas"]

            pred_fcos3d = self.fcos3d_forward_test(fcos3d_fpn_outs, img_metas)

            # yolox
            yolox_fpn_outs = self.yolox_fpn(x[1:])
            pred_yolox = self.yolox_forward_test(yolox_fpn_outs)

            return pred_fcos3d, pred_yolox
