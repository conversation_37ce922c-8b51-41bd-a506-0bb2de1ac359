import torch.nn as nn

from typing import Any

from perceptron.Perceptron.perceptron.models.end2end.perceptron_2neck import VisionEncoder


class Driver(nn.module):
    r"""
    For Driver model
    """

    def __init__(self, model_cfg) -> Any:
        super(Driver, self).__init__()

        # base setting
        self.obstacle = model_cfg.get("det_head", False)
        self.map = model_cfg.get("map_head", False)
        assert self.obstacle and self.map, "Object and map is needed!!!"
        self.train_perceptron = model_cfg.get("train_perceptron", False)
        self.freeze_bn = model_cfg.get("freeze_bn", False)
        self.cfg = model_cfg

        # multi-modal setting
        self.perceptron_head = self._configure_perceptron_head()

        self.init_params_and_layers()

    def init_params_and_layers(self):
        # freeze backbone
        if not self.train_perceptron:
            for param in self.perceptron_head.parameters():
                param.requires_grad = False

    def freeze_bn_func(self):
        # freeze backbone bn
        if not self.train_perceptron:
            if self.freeze_bn:
                self.perceptron_head.eval()

    def forward(
        self,
        **kwargs,
    ) -> Any:
        self.freeze_bn_func()

        outs, losses_obstacle, _ = self.perceptron_head(**kwargs)
        (outs_obstacle, outs_map) = outs

    def _configure_perceptron_head(self):
        return VisionEncoder(self.cfg)
