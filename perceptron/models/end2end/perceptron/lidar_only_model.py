from typing import Any, List, <PERSON><PERSON>

import mmcv
import numpy as np
import torch
from torch import nn

from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar.BEVFusion_private_lidar_pointpillar_mh_dr_base_exp import (
    DetHead as BaseDetHead,
)

# from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar.p177.eval_config.config_p177 import

from perceptron.layers.blocks_2d.det3d.map_to_bev.pointpillar_scatter import PointPillarScatter
from perceptron.layers.blocks_3d.det3d.vfe.pillar_feature_net import PillarFeatureNet
from perceptron.layers.head.det3d import CenterHeadA1000, FCOSAssignerMaskRotBin, IouAwareGenProposalsMergedRotbin
from perceptron.layers.losses.det3d import (
    CenterNetRegLoss,
    CenterNetRotclsLoss,
    CenterNetRotregLoss,
    WeightedFocalLoss,
    WeightFocalLossWHDR,
)
from perceptron.models.multisensor_fusion import BaseEncoder


def _load_data_to_gpu(data_dict):
    for k, v in data_dict.items():
        if isinstance(v, torch.Tensor):
            data_dict[k] = v.to(torch.float32).cuda()
        elif isinstance(v, dict):
            _load_data_to_gpu(data_dict[k])
        else:
            data_dict[k] = v


class LidarEncoder(BaseEncoder):
    def __init__(self, lidar_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super(LidarEncoder, self).__init__()

        self.voxelizer = Voxelization(
            voxel_size=lidar_encoder_cfg.voxel_size,
            point_cloud_range=lidar_encoder_cfg.point_cloud_range,
            max_num_points=lidar_encoder_cfg.max_num_points,
            max_voxels=lidar_encoder_cfg.max_voxels,
            num_point_features=lidar_encoder_cfg.src_num_point_features,
            device=torch.device("cuda"),
        )
        self.vfe = PillarFeatureNet(
            voxel_size=lidar_encoder_cfg.voxel_size,
            point_cloud_range=lidar_encoder_cfg.point_cloud_range,
        )
        self.map_to_bev = PointPillarScatter(
            num_bev_features=lidar_encoder_cfg.map_to_bev_num_features,
            grid_size=lidar_encoder_cfg.grid_size,
        )

    # @ForceFp32(apply_to=("lidar_points"))
    def forward(self, lidar_points: List[torch.tensor]) -> torch.tensor:
        voxels, voxel_coords, voxel_num_points = self.voxelizer(lidar_points)
        pillar_features = self.vfe(voxels, voxel_num_points, voxel_coords)
        spatial_features = self.map_to_bev(pillar_features, voxel_coords)

        rescale = spatial_features.new_tensor(
            1.0 / np.array([0.1, 0.1, 4, 128, 24.8, 120, 8, 256, 24.8, 120]), dtype=torch.float16
        ).reshape(1, -1, 1, 1)
        spatial_features = spatial_features * rescale
        return spatial_features


class BevEncoderResBlock(nn.Module):
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, dilation=1, downsample=None):
        super(BevEncoderResBlock, self).__init__()

        self.downsample = downsample
        self.stride = stride
        self.dilation = dilation

        self.conv1 = nn.Conv2d(
            inplanes, planes, kernel_size=3, stride=stride, padding=dilation, dilation=dilation, bias=False
        )
        self.norm1 = nn.BatchNorm2d(planes, eps=1e-3, momentum=0.01)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, padding=1, bias=False)
        self.norm2 = nn.BatchNorm2d(planes, eps=1e-3, momentum=0.01)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        identity = x

        out = self.relu(self.norm1(self.conv1(x)))
        out = self.relu(self.norm2(self.conv2(out)))

        if self.downsample is not None:
            identity = self.relu(self.downsample(x))
        out = out + identity

        return out


class BevEncoderResLayer(nn.Module):
    def __init__(self, blocks, in_planes, planes, stride=1, dilate=False):
        super(BevEncoderResLayer, self).__init__()
        self.blocks = blocks
        self.in_planes = in_planes
        self.planes = planes
        self.stride = stride
        self.dilate = dilate

        self.layers = nn.ModuleList()
        self.layers.append(
            BevEncoderResBlock(
                inplanes=in_planes,
                planes=planes,
                stride=stride,
                downsample=nn.Sequential(
                    nn.Conv2d(in_planes, planes, kernel_size=1, stride=stride),
                    nn.BatchNorm2d(planes, eps=1e-3, momentum=0.01),
                ),
            )
        )
        self.layers.extend([BevEncoderResBlock(inplanes=planes, planes=planes) for _ in range(1, blocks)])

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return x


class BevEncoderResNet(nn.Module):
    def __init__(self, layer_nums, layer_strides, num_filters, upsample_strides, num_upsample_filters, input_channels):
        super(BevEncoderResNet, self).__init__()
        layer_nums, layer_strides, num_filters, upsample_strides, num_upsample_filters = [
            [] if param is None else param
            for param in [layer_nums, layer_strides, num_filters, upsample_strides, num_upsample_filters]
        ]
        assert len(layer_nums) == len(layer_strides) == len(num_filters)
        assert len(upsample_strides) == len(num_upsample_filters)

        c_in_list = [input_channels, num_filters[0] // 2, *num_filters[:-1]]
        self.blocks = nn.ModuleList()
        self.deblocks = nn.ModuleList()

        self.conv = nn.Sequential(
            nn.Conv2d(c_in_list[0], c_in_list[1], kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(c_in_list[1]),
        )

        for idx in range(len(layer_nums)):
            self.blocks.append(
                BevEncoderResLayer(
                    blocks=layer_nums[idx], in_planes=c_in_list[idx + 1], planes=num_filters[idx], stride=2
                )
            )

            if (len(upsample_strides) > 0) and (upsample_strides[idx] >= 1):
                self.deblocks.append(
                    nn.Sequential(
                        nn.ConvTranspose2d(
                            num_filters[idx],
                            num_upsample_filters[idx],
                            upsample_strides[idx],
                            stride=upsample_strides[idx],
                            bias=False,
                        ),
                        nn.BatchNorm2d(num_upsample_filters[idx], eps=1e-3, momentum=0.01),
                        nn.ReLU(inplace=True),
                    )
                )

    def forward(self, spatial_features):
        ups = []
        pyramid = {}
        x = spatial_features  # bs,10,448,128
        x = self.conv(x)  # bs,32,448,128

        for i in range(len(self.blocks)):
            x = self.blocks[i](x)

            stride = int(spatial_features.shape[2] / x.shape[2])
            pyramid["spatial_features_%dx" % stride] = x

            if len(self.deblocks) > 0:
                ups.append(self.deblocks[i](x))
            else:
                ups.append(x)

        if len(ups) > 1:
            x = torch.cat(ups, dim=1)
        elif len(ups) == 1:
            x = ups[0]

        if len(self.deblocks) > len(self.blocks):
            x = self.deblocks[-1](x)

        return x, pyramid


class BevEncoder(nn.Module):
    def __init__(self, bev_encoder_cfg: mmcv.Config, **kwargs):
        super(BevEncoder, self).__init__()

        self.backbone_2d = BevEncoderResNet(
            layer_nums=bev_encoder_cfg.backbone2d_layer_nums,
            layer_strides=bev_encoder_cfg.backbone2d_layer_strides,
            num_filters=bev_encoder_cfg.backbone2d_num_filters,
            upsample_strides=bev_encoder_cfg.backbone2d_upsample_strides,
            num_upsample_filters=bev_encoder_cfg.backbone2d_num_upsample_filters,
            input_channels=bev_encoder_cfg.num_bev_features,
        )

    # @ForceFp32(apply_to=("x"))
    def forward(self, x: torch.tensor) -> Tuple[torch.tensor, List[torch.tensor]]:
        spatial_features_2d, pyramid = self.backbone_2d(x)
        return spatial_features_2d, pyramid


class DetHead(BaseDetHead):
    def __init__(self, det_head_cfg: mmcv.Config, **kwargs):
        super(DetHead, self).__init__(det_head_cfg)

    def build_dense_head(self):
        target_cfg = self.det_head_cfg.target_assigner
        target_assigner = FCOSAssignerMaskRotBin(
            out_size_factor=target_cfg.densehead_out_size_factor,
            tasks=target_cfg.densehead_tasks,
            dense_reg=target_cfg.target_assigner_dense_reg,
            gaussian_overlap=target_cfg.target_assigner_gaussian_overlap,
            max_objs=target_cfg.target_assigner_max_objs,
            min_radius=target_cfg.target_assigner_min_radius,
            mapping=target_cfg.target_assigner_mapping,
            grid_size=target_cfg.grid_size,
            pc_range=target_cfg.pc_range,
            voxel_size=target_cfg.voxel_size,
            assign_topk=target_cfg.target_assigner_topk,
            no_log=target_cfg.target_assigner_no_log,
            with_velocity=target_cfg.with_velocity,
        )

        proposal_cfg = self.det_head_cfg.proposal_layer
        proposal_layer = IouAwareGenProposalsMergedRotbin(
            dataset_name=proposal_cfg.densehead_dataset_name,
            class_names=[t["class_names"] for t in proposal_cfg.densehead_tasks],
            post_center_limit_range=proposal_cfg.proposal_post_center_limit_range,
            score_threshold=proposal_cfg.proposal_score_threshold,
            pc_range=proposal_cfg.proposal_pc_range,
            out_size_factor=proposal_cfg.densehead_out_size_factor,
            voxel_size=proposal_cfg.proposal_voxel_size,
            no_log=proposal_cfg.no_log,
            iou_aware_list=proposal_cfg.proposal_iou_aware_list,
            nms_iou_threshold_train=proposal_cfg.nms_iou_threshold_train,
            nms_pre_max_size_train=proposal_cfg.nms_pre_max_size_train,
            nms_post_max_size_train=proposal_cfg.nms_post_max_size_train,
            nms_iou_threshold_test=proposal_cfg.nms_iou_threshold_test,
            nms_pre_max_size_test=proposal_cfg.nms_pre_max_size_test,
            nms_post_max_size_test=proposal_cfg.nms_post_max_size_test,
        )

        head_cfg = self.det_head_cfg.dense_head
        dense_head_module = CenterHeadA1000(
            dataset_name=head_cfg.densehead_dataset_name,
            tasks=head_cfg.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            out_size_factor=head_cfg.densehead_out_size_factor,
            input_channels=head_cfg.input_channels,
            grid_size=head_cfg.grid_size,
            point_cloud_range=head_cfg.point_cloud_range,
            code_weights=head_cfg.densehead_loss_code_weights,
            loc_weight=head_cfg.densehead_loss_loc_weight,
            iou_weight=head_cfg.densehead_loss_iou_weight,
            share_conv_channel=head_cfg.densehead_share_conv_channel,
            common_heads=head_cfg.densehead_common_heads,
            upsample_for_pedestrian=head_cfg.upsample_for_pedestrian,
            mode=head_cfg.densehead_mode,
            init_bias=head_cfg.densehead_init_bias,
            predict_boxes_when_training=False,
        )
        dense_head_module.shared_conv = nn.Sequential(
            nn.Conv2d(
                head_cfg.input_channels, head_cfg.densehead_share_conv_channel, kernel_size=3, padding=1, bias=True
            ),
            nn.BatchNorm2d(head_cfg.densehead_share_conv_channel),
            nn.ReLU(inplace=True),
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                WeightedFocalLoss(self.det_head_cfg.target_assigner_alpha, self.det_head_cfg.target_assigner_gamma),
            )
            m.add_module("crit_dr", WeightFocalLossWHDR())
            m.add_module("crit_iou_aware", CenterNetRegLoss())
            m.add_module("crit_reg", CenterNetRegLoss())
            m.add_module("rot_cls_crit", CenterNetRotclsLoss())
            m.add_module("rot_offset_crit", CenterNetRotregLoss())

        _build_losses(dense_head_module)

        return dense_head_module

    # @ForceFp32(apply_to=("x", "gt_boxes"))
    def forward(self, x: torch.tensor, gt_boxes: torch.tensor) -> Any:
        forward_ret_dict = self.dense_head(x, gt_boxes)

        if self.training:
            for _, encoding in forward_ret_dict["box_encoding"].items():
                encoding[torch.isinf(encoding)] = 0
        return forward_ret_dict
