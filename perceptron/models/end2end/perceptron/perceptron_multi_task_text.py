import torch

from typing import Any, List
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder
from perceptron.layers.head.multimodal_align.img_text_align import TextAlignHead

# from perceptron.models.text.base import TextEncoder
from perceptron.utils import torch_dist
from torch import distributed as dist
import numpy as np


def get_task_mask(task_list, task_names=["box", "occ", "map"]):
    bs = len(task_list)
    mask_list = {k: [] for k in task_names}
    for i in range(bs):
        for task_name in task_names:
            if task_name in task_list[i]:
                mask_list[task_name].append(i)
    return [torch.tensor(mask_list[i]) for i in task_names]


def get_mask_kwargs(kwargs, mask, total_bs, task=None):
    if len(mask) == total_bs:
        return kwargs
    new_kwargs = {}
    for k, v in kwargs.items():
        if len(v) != total_bs:
            if task is not None:
                if task == "box" and k in ["semantic", "semantic_mask"]:
                    continue
            assert len(v) == len(mask), "特殊的k, 在多个task中都存在, 需要单独处理"
            new_kwargs[k] = v
        else:
            if isinstance(v, list):
                new_kwargs[k] = [v[i.item()] for i in mask.cpu()]
            elif isinstance(v, torch.Tensor):
                new_kwargs[k] = v[mask.to(v.device)]
            else:
                raise NotImplementedError(f"暂未支持的type: k: {k}, v: {type(v)}")
    return new_kwargs


def get_mask_input(mask, total_bs, *args):
    if len(mask) == total_bs:
        return args
    new_args = []
    for v in args:
        if v is not None:
            if isinstance(v, list):
                v = [v[i.item()] for i in mask.cpu()]
            elif isinstance(v, torch.Tensor):
                v = v[mask.to(v.device)]
            else:
                raise NotImplementedError(f"暂未支持的type: v: {type(v)}")
        new_args.append(v)
    return new_args


def get_mask_feat_input(mask, total_bs, img_feat):
    if len(mask) == total_bs or img_feat is None:
        return img_feat
    img_feat = img_feat[0]
    num_img, C, H, W = img_feat.size()
    img_feat = img_feat.reshape(total_bs, -1, C, H, W)
    img_feat = img_feat[mask.to(img_feat.device)]
    img_feat = img_feat.reshape(-1, C, H, W)
    return [img_feat]


def get_task_rank_list(cur_gpu_task_mask_list):
    task_num = len(cur_gpu_task_mask_list)
    cur_gpu_task_valid_mask_list = [len(i) > 0 for i in cur_gpu_task_mask_list]
    task_valid_mask_list = torch_dist.all_gather_object(cur_gpu_task_valid_mask_list)
    task_valid_mask_list = np.array(task_valid_mask_list)
    valid_group_list = []
    world_size = torch_dist.get_world_size()
    for task_id in range(task_num):
        valid_group_rank = np.where(task_valid_mask_list[:, task_id])[0].tolist()
        if len(valid_group_rank) == world_size or len(valid_group_rank) == 0:
            valid_group_list.append(None)
        else:
            valid_group_list.append(dist.new_group(ranks=valid_group_rank))
    return valid_group_list


class VisionEncoder_MultiTask_Text(VisionEncoder):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)
        self.text_head = self._configure_text_head()

    def _configure_text_head(
        self,
    ):
        # text_cfg = self.cfg["text_encoder"]
        self.cfg["text_head"].pop("type")
        return TextAlignHead(**self.cfg["text_head"])

    def forward(
        self,
        points=None,
        img_metas=None,
        gt_bboxes_3d=None,
        gt_labels_3d=None,
        instance_inds=None,
        imgs=None,
        radar_points: List[torch.tensor] = None,
        img_semantic_seg=None,
        sequence_pe=None,
        sequence_data=None,
        sequence_data_noise=None,
        valid_mask_seq=None,
        seq_attribute=None,
        index_in_dataset=None,
        tran_mats_dict=None,
        roi_mask=None,
        **kwargs,
    ) -> Any:
        self.freeze_bn_func()
        # 0. Prepare groud_truth and img_metas
        if self.camera_encoder:
            batch_size, num_frame, N, C, H, W = imgs.shape
        else:
            batch_size, num_frame = len(points), len(points[0])
            N, C, H, W = 0, 3, 0, 0

        task_names = ["box", "occ", "map"]
        if "task_list" in kwargs:
            task_list = kwargs.pop("task_list")
            assert (
                len(task_list) == batch_size
            ), f"bs and len(task_list) not match, bs: {batch_size}, task_list: {task_list}"
            od_mask, occ_mask, map_mask = get_task_mask(task_list, task_names=task_names)
            od_group, occ_group, map_group = get_task_rank_list([od_mask, occ_mask, map_mask])
        else:
            od_mask, occ_mask, map_mask = [torch.arange(0, batch_size) for _ in range(3)]
            od_group, occ_group, map_group = [None for _ in range(3)]
        od_bs_size, occ_bs_size, map_bs_size = len(od_mask), len(occ_mask), len(map_mask)  # noqa
        labels = (gt_bboxes_3d, gt_labels_3d, instance_inds)
        labels_bias = 1  # TODO: zhangyi check
        # 如果加上track 就是0
        if hasattr(self, "obstacle_head") and self.obstacle_head.tracking:
            labels_bias = 0
        metas_labels_task_dict = {}
        # from perceptron.utils import torch_dist
        # rank = torch_dist.get_rank()
        device = imgs.device if self.camera_encoder else points[0][0].device
        for task, task_mask in zip(["box", "occ", "map"], [od_mask, occ_mask, map_mask]):
            img_metas_new, labels_new = [], []
            if len(task_mask) > 0:
                task_img_metas = get_mask_input(task_mask, batch_size, img_metas)[0]
                for frame_idx in range(num_frame):
                    # 需要把map label 和det label 拆分开
                    # FIXME: 不知道在干嘛
                    img_metas_single_frame, labels_single_frame = self.format_infos(
                        [len(task_mask)] + list(imgs.shape)[1:]
                        if self.camera_encoder
                        else (len(task_mask), num_frame, 0, 3, 0, 0),
                        frame_idx,
                        task_img_metas,
                        device,
                        labels=labels,
                        labels_bias=labels_bias,
                        type="train" if self.training else "infer",
                        process_box=True if task == "box" else False,
                    )
                    img_metas_new.append(img_metas_single_frame)
                    labels_new.append(labels_single_frame)
            metas_labels_task_dict[task] = (img_metas_new, labels_new)

        # 1. Extract base feature
        img_feats, radar_output, radar_points, lidar_feats = self.base_encoder(points, imgs, radar_points)
        # 2. Obstacle perceptron
        if self.obstacle and od_bs_size > 0:
            od_radar_points, od_radar_output, od_lidar_feats, od_roi_mask = get_mask_input(
                od_mask, batch_size, radar_points, radar_output, lidar_feats, roi_mask
            )
            od_img_feats = get_mask_feat_input(od_mask, batch_size, img_feats)
            od_kwargs = get_mask_kwargs(kwargs, od_mask, batch_size, task="box")
            img_metas_od_new, labels_od_new = metas_labels_task_dict["box"]
            # print("od_kwargs 1", od_kwargs.keys())
            # print(
            #     "labels_new ",
            #     type(labels_od_new),
            #     labels_od_new.keys() if isinstance(labels_od_new, dict) else len(labels_od_new),
            # )
            outs = self.obstacle_head.forward(
                data_shape=(od_bs_size, num_frame, N, C, H, W),
                img_feats=od_img_feats,
                radar_points=od_radar_points,
                radar_output=od_radar_output,
                lidar_feats=od_lidar_feats,
                labels=labels_od_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_od_new,
                roi_mask=od_roi_mask,
                **od_kwargs,
                group=od_group,
            )
            if self.training:
                outs_obstacle, losses_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    if self.obstacle_head.tracking:
                        bbox_list = self.obstacle_head.det_head.dense_head.get_e2e_bboxes(outs, img_metas_od_new[0])
                        self.obstacle_head.runtime_tracker_list[0].update_active_tracks(
                            outs["track_instances"], outs["active_mask"]
                        )
                        if not self.cfg.tracking_module.get("with_velocity", False):
                            bbox_results = [
                                self.track_bbox3d2result(bboxes, scores, labels, obj_idxes, track_scores, forecasting)
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting in bbox_list
                            ]
                        else:
                            bbox_results = [
                                self.track_bbox3d2result(
                                    bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity
                                )
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity in bbox_list
                            ]

                        bbox_results[0]["track_ids"] = bbox_results[0]["track_ids"].long().cpu().numpy()
                        outs_obstacle = bbox_results
                    else:
                        outs_obstacle = self.obstacle_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_obstacle = outs
        else:
            outs_obstacle, losses_obstacle = None, {}

        # 2. Map perceptron
        if self.map:
            # FIXME: 需要处理map对应的feat以及labels
            img_metas_new, labels_new = metas_labels_task_dict["map"]
            if self.training:
                outs_map, losses_map = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=img_feats[0],
                    img_metas=img_metas_new,
                    img_semantic_seg=img_semantic_seg,
                    sequence_pe=sequence_pe,
                    sequence_data=sequence_data,
                    sequence_data_noise=sequence_data_noise,
                    valid_mask_seq=valid_mask_seq,
                    seq_attribute=seq_attribute,
                    # timestamp=timestamp,
                    **kwargs,
                )
            else:
                outs_map, outs_map_logits = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=img_feats[0],
                    img_metas=img_metas_new,
                    imgs=imgs,
                    sequence_data=sequence_data,
                    valid_mask_seq=valid_mask_seq,
                    seq_attribute=seq_attribute,
                    index_in_dataset=index_in_dataset,
                    tran_mats_dict=tran_mats_dict,
                    **kwargs,
                )
        else:
            outs_map, losses_map = None, {}
        # freespace
        if self.freespace and occ_bs_size > 0:
            occ_radar_points, occ_radar_output, occ_lidar_feats = get_mask_input(
                occ_mask, batch_size, radar_points, radar_output, lidar_feats
            )
            occ_img_feats = get_mask_feat_input(occ_mask, batch_size, img_feats)
            occ_kwargs = get_mask_kwargs(kwargs, occ_mask, batch_size, task="occ")
            img_metas_new, labels_new = metas_labels_task_dict["occ"]
            outs_freespace, loss_freespace = self.freespace_head.forward(
                data_shape=(batch_size, num_frame, N, C, H, W),
                img_feats=occ_img_feats,
                radar_points=occ_radar_points,
                radar_output=occ_radar_output,
                lidar_feats=occ_lidar_feats,
                freespace=occ_kwargs["semantic"] if self.training else None,
                freespace_mask=occ_kwargs["semantic_mask"] if self.training else None,
                bev_embedding=self.bev_embedding if self.bev_embedding else None,
                img_metas=img_metas_new,
                l2g=occ_kwargs["l2g"],
                test=(not self.training),
                timestamp=[t % 1e10 for t in occ_kwargs["timestamp"]],
            )
        else:
            outs_freespace, loss_freespace = None, {}
        # if self.text_encoder:
        # print("od_kwargs numnum", type(od_kwargs), type(img_metas_od_new), len(img_metas_od_new), len(od_img_feats))
        outs_texts, loss_texts = self.text_head(
            data_shape=(od_bs_size, num_frame, N, C, H, W),
            img_feats=od_img_feats[0],  # torch.Size([16, 256, 32, 60])
            radar_points=od_radar_points,  # None
            radar_output=od_radar_output,  # None
            lidar_feats=od_lidar_feats,  # torch.Size([2, 1, 384, 300, 160])
            labels=labels_od_new,  # list len1
            bev_embedding=self.bev_embedding,
            img_metas=img_metas_od_new[
                0
            ],  # dict_keys(['ida_mats', 'lidar2imgs', 'box_type_3d', 'lidar2ego', 'ff_gt_bboxes_list', 'ff_gt_labels_list', 'pad_shape'])
            **od_kwargs,
        )
        if self.training:
            losses_obstacle.update(losses_map)
            losses_obstacle.update(loss_freespace)
            losses_obstacle.update(loss_texts)
            return (outs_obstacle, outs_map), losses_obstacle, {}
        else:
            if self.obstacle and self.freespace:
                return (outs_obstacle, outs_map, outs_freespace)
            if self.freespace:
                return outs_freespace
            return (outs_obstacle, outs_map)
