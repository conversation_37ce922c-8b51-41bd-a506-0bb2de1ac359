import torch

from typing import Any, List
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder
from perceptron.utils import torch_dist
from torch import distributed as dist
import numpy as np
from perceptron.models.multisensor_fusion.cmt.base import CameraEncoder, CameraEncoderMultiNeck
import copy
from perceptron.layers.losses.det3d import AutomaticWeightedLoss


def get_task_mask(task_list, task_names=["box", "occ", "map"], return_dict=False):
    bs = len(task_list)
    mask_list = {k: [] for k in task_names}
    for i in range(bs):
        for task_name in task_names:
            if task_name in task_list[i]:
                mask_list[task_name].append(i)
    if return_dict:
        return {task_name: torch.tensor(mask_list[task_name]) for task_name in task_names}
    else:
        return [torch.tensor(mask_list[i]) for i in task_names]


def get_mask_kwargs(kwargs, mask, total_bs, task=None):
    if len(mask) == total_bs:
        return kwargs
    new_kwargs = {}
    for k, v in kwargs.items():
        if len(v) != total_bs:
            if task is not None:
                if task in ["box", "god", "lidar_only"] and k in ["semantic", "semantic_mask"]:
                    continue
            assert len(v) == len(mask), "特殊的k, 在多个task中都存在, 需要单独处理"
            new_kwargs[k] = v
        else:
            if isinstance(v, list):
                new_kwargs[k] = [v[i.item()] for i in mask.cpu()]
            elif isinstance(v, torch.Tensor):
                new_kwargs[k] = v[mask.to(v.device)]
            else:
                raise NotImplementedError(f"暂未支持的type: k: {k}, v: {type(v)}")
    return new_kwargs


def get_cam_masked_img_metas(img_metas, cam_mask_or_idx=None, key_list=["ida_mats", "lidar2imgs"]):
    if cam_mask_or_idx is None:
        return img_metas
    for i in range(len(img_metas)):
        for k in key_list:
            # lidar2imgs: torch.Size([1, 1, 8, 4, 4])
            # ida_mats: (1, 1, 8)
            # 针对occ dataset中可能只有4v的输入, 不需要处理8v的输入
            if len(cam_mask_or_idx) == img_metas[i][k].shape[2]:
                continue
            cam_mask_or_idx.to(img_metas[i][k].device)
            img_metas[i][k] = img_metas[i][k][:, :, cam_mask_or_idx]
    return img_metas


def get_mask_input(mask, total_bs, *args):
    if len(mask) == total_bs:
        return args
    new_args = []
    for v in args:
        if v is not None:
            if isinstance(v, list):
                v = [v[i.item()] for i in mask.cpu()]
            elif isinstance(v, torch.Tensor):
                v = v[mask.to(v.device)]
            else:
                raise NotImplementedError(f"暂未支持的type: v: {type(v)}")
        new_args.append(v)
    return new_args


def get_mask_feat_input(mask, total_bs, img_feat, cam_mask_or_idx=None):
    if img_feat is None:
        return img_feat
    if len(mask) == total_bs and (cam_mask_or_idx is None or len(cam_mask_or_idx) * len(mask) == img_feat[0].shape[0]):
        return img_feat
    img_feat = img_feat[0]
    num_img, C, H, W = img_feat.size()
    img_feat = img_feat.reshape(total_bs, -1, C, H, W)
    mask = mask.to(img_feat.device)
    if cam_mask_or_idx is not None:
        cam_mask_or_idx = cam_mask_or_idx.to(img_feat.device)
        if cam_mask_or_idx.dtype in [torch.int64, torch.int32, torch.long]:
            bs_idx_grid, n_idx_grid = torch.meshgrid(mask, cam_mask_or_idx, indexing="ij")
        else:
            raise ValueError(f"Unsupported cam_mask_or_idx type: {cam_mask_or_idx.dtype}")
        img_feat = img_feat[bs_idx_grid, n_idx_grid]
    else:
        img_feat = img_feat[mask]
    img_feat = img_feat.reshape(-1, C, H, W)
    return [img_feat]


def get_task_rank_list(cur_gpu_task_mask_list):
    task_num = len(cur_gpu_task_mask_list)
    is_dict = isinstance(cur_gpu_task_mask_list, dict)
    if is_dict:
        task_key_list = list(cur_gpu_task_mask_list.keys())
        cur_gpu_task_mask_list = [cur_gpu_task_mask_list[k] for k in task_key_list]
    cur_gpu_task_valid_mask_list = [len(i) > 0 for i in cur_gpu_task_mask_list]
    task_valid_mask_list = torch_dist.all_gather_object(cur_gpu_task_valid_mask_list)
    task_valid_mask_list = np.array(task_valid_mask_list)
    valid_group_list = []
    world_size = torch_dist.get_world_size()
    for task_id in range(task_num):
        valid_group_rank = np.where(task_valid_mask_list[:, task_id])[0].tolist()
        if len(valid_group_rank) == world_size or len(valid_group_rank) == 0:
            valid_group_list.append(None)
        else:
            valid_group_list.append(dist.new_group(ranks=valid_group_rank))
    if is_dict:
        valid_group_list = {task_key_list[i]: valid_group_list[i] for i in range(task_num)}
    return valid_group_list


class VisionEncoder_MultiTask(VisionEncoder):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)
        self.occ_cam_idx = model_cfg.get("occ_cam_idx_or_mask", None)
        max_cam = 8  # TODO fix hard code
        if self.occ_cam_idx is not None and hasattr(self.cfg, "freespace_head") and self.cfg.freespace_head is not None:
            if isinstance(self.occ_cam_idx, list):
                self.occ_cam_idx = torch.tensor(self.occ_cam_idx)
            if self.occ_cam_idx.dtype == torch.bool:
                self.occ_cam_idx = torch.where(self.occ_cam_idx)[0]
        if self.occ_cam_idx is None:
            self.occ_cam_num = max_cam
        else:
            self.occ_cam_num = len(self.occ_cam_idx)
        self.lidar_only_loss_weight = model_cfg.get("lidar_only_loss_weight", 1.0)
        self.use_auto_loss = model_cfg.get("auto_loss", False)
        if self.use_auto_loss:
            self.auto_loss = AutomaticWeightedLoss(num=3)

    def _configure_camera_encoder(self):
        if "type" in self.cfg.camera_encoder:
            encoder_type = self.cfg.camera_encoder.pop("type")
            if encoder_type == "CameraEncoder":
                return CameraEncoder(self.cfg.camera_encoder)
            elif encoder_type == "CameraEncoderMultiNeck":
                return CameraEncoderMultiNeck(self.cfg.camera_encoder)
            else:
                raise ValueError(f"Unsupported camera encoder type: {encoder_type}")
        else:
            return CameraEncoder(self.cfg.camera_encoder)

    def forward(
        self,
        points=None,
        img_metas=None,
        gt_bboxes_3d=None,
        gt_labels_3d=None,
        instance_inds=None,
        imgs=None,
        radar_points: List[torch.tensor] = None,
        img_semantic_seg=None,  # 从这往下都是 map
        map_gt=None,
        map_local2global_info=None,
        scene_frame_idx=None,
        map_attr=None,
        map_scene_weight=None,
        valid_region=None,
        lane_bev_segment_map=None,
        bev_manual_mask=None,
        roi_mask=None,
        **kwargs,
    ) -> Any:
        self.freeze_bn_func()
        # 0. Prepare groud_truth and img_metas
        if self.camera_encoder:
            batch_size, num_frame, N, C, H, W = imgs.shape
        else:
            batch_size, num_frame = len(points), len(points[0])
            N, C, H, W = 0, 3, 0, 0

        task_names = ["box", "occ", "map", "god", "lidar_only"]
        od_mask, occ_mask, map_mask, god_mask, lidar_only_mask = [
            torch.arange(0, batch_size) for _ in range(len(task_names))
        ]
        od_group, occ_group, map_group, god_group, lidar_only_group = [None for _ in range(len(task_names))]
        if "task_list" in kwargs:
            task_list = kwargs.pop("task_list")
            assert (
                len(task_list) == batch_size
            ), f"bs and len(task_list) not match, bs: {batch_size}, task_list: {task_list}"
            if self.training:
                # FIXME: 如果不使用self.training过滤，那么e2e推理过程因为存在不同clip的差异，不同gpu最后几个iter可能不对齐，导致推理hang。
                # 如果加上self.training，会存在评测occ任务数据集只读取4v时od head存在问题；并且评测会推理所有任务。
                assert not (
                    "box" in [t for sub in task_list for t in sub]
                    and (
                        "lidar_only" in [t for sub in task_list for t in sub]
                        or "god" in [t for sub in task_list for t in sub]
                    )
                ), "Invalid task_list: 'box' cannot coexist with 'lidar_only' or 'god'"
                od_mask, occ_mask, map_mask, god_mask, lidar_only_mask = get_task_mask(task_list, task_names=task_names)
                od_group, occ_group, map_group, god_group, lidar_only_group = get_task_rank_list(
                    [od_mask, occ_mask, map_mask, god_mask, lidar_only_mask]
                )

        od_bs_size, occ_bs_size, map_bs_size, god_bs_size, lidar_only_bs_size = (  # noqa
            len(od_mask),
            len(occ_mask),
            len(map_mask),
            len(god_mask),
            len(lidar_only_mask),
        )  # noqa
        labels = (gt_bboxes_3d, gt_labels_3d, instance_inds)
        labels_bias = 1  # TODO: zhangyi check
        # 如果加上track 就是0
        if hasattr(self, "obstacle_head") and self.obstacle_head.tracking:
            labels_bias = 0
        metas_labels_task_dict = {}
        # from perceptron.utils import torch_dist
        # rank = torch_dist.get_rank()
        device = imgs.device if self.camera_encoder else points[0][0].device
        for task, task_mask in zip(task_names, [od_mask, occ_mask, map_mask, god_mask, lidar_only_mask]):
            img_metas_new, labels_new = [], []
            if len(task_mask) > 0:
                task_img_metas = get_mask_input(task_mask, batch_size, img_metas)[0]
                for frame_idx in range(num_frame):
                    # 需要把map label 和det label 拆分开
                    # FIXME: 不知道在干嘛
                    img_metas_single_frame, labels_single_frame = self.format_infos(
                        [len(task_mask)] + list(imgs.shape)[1:]
                        if self.camera_encoder
                        else (len(task_mask), num_frame, 0, 3, 0, 0),
                        frame_idx,
                        task_img_metas,
                        device,
                        labels=labels,
                        labels_bias=labels_bias,
                        type="train" if self.training else "infer",
                        process_box=True if task in ["box", "god", "lidar_only"] else False,
                        task_name=task,
                    )
                    img_metas_new.append(img_metas_single_frame)
                    labels_new.append(labels_single_frame)
            metas_labels_task_dict[task] = (img_metas_new, labels_new)

        # 1. Extract base feature
        img_feats, radar_output, radar_points, lidar_feats = self.base_encoder(points, imgs, radar_points)
        # 2. Obstacle perceptron
        outs_dict = {}
        losses_dict = {}

        outs_obstacle, losses_obstacle = None, {}
        if self.obstacle and od_bs_size > 0:
            od_radar_points, od_radar_output, od_lidar_feats, od_roi_mask = get_mask_input(
                od_mask, batch_size, radar_points, radar_output, lidar_feats, roi_mask
            )
            if isinstance(img_feats, dict):
                od_img_feats = img_feats["img_neck"]
            else:
                od_img_feats = img_feats
            od_img_feats = get_mask_feat_input(od_mask, batch_size, od_img_feats)
            od_kwargs = get_mask_kwargs(kwargs, od_mask, batch_size, task="box")
            img_metas_new, labels_new = metas_labels_task_dict["box"]
            outs = self.obstacle_head.forward(
                data_shape=(od_bs_size, num_frame, N, C, H, W),
                img_feats=od_img_feats,
                radar_points=od_radar_points,
                radar_output=od_radar_output,
                lidar_feats=od_lidar_feats,
                labels=labels_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_new,
                roi_mask=od_roi_mask,
                **od_kwargs,
                group=od_group,
            )
            if self.training:
                outs_obstacle, losses_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    if self.obstacle_head.tracking:
                        bbox_list = self.obstacle_head.det_head.dense_head.get_e2e_bboxes(outs, img_metas_new[0])
                        self.obstacle_head.runtime_tracker_list[0].update_active_tracks(
                            outs["track_instances"], outs["active_mask"]
                        )
                        if not self.cfg.tracking_module.get("with_velocity", False):
                            bbox_results = [
                                self.track_bbox3d2result(bboxes, scores, labels, obj_idxes, track_scores, forecasting)
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting in bbox_list
                            ]
                        else:
                            bbox_results = [
                                self.track_bbox3d2result(
                                    bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity
                                )
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity in bbox_list
                            ]

                        bbox_results[0]["track_ids"] = bbox_results[0]["track_ids"].long().cpu().numpy()
                        outs_obstacle = bbox_results
                    else:
                        outs_obstacle = self.obstacle_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_obstacle = outs
        outs_dict["obstacle"] = outs_obstacle
        losses_dict.update(losses_obstacle)

        outs_god_obstacle, losses_god_obstacle = None, {}
        if self.god and god_bs_size > 0:  # TODO
            god_radar_points, god_radar_output, god_lidar_feats, god_roi_mask = get_mask_input(
                god_mask, batch_size, radar_points, radar_output, lidar_feats, roi_mask
            )
            if isinstance(img_feats, dict):
                if "img_neck_god" in img_feats:
                    god_img_feats = img_feats["img_neck_god"]
                else:
                    god_img_feats = img_feats["img_neck"]  # 和od共用一个neck
            else:
                god_img_feats = img_feats
            god_img_feats = get_mask_feat_input(god_mask, batch_size, god_img_feats)
            god_kwargs = get_mask_kwargs(kwargs, god_mask, batch_size, task="god")
            img_metas_new, labels_new = metas_labels_task_dict["god"]
            outs = self.god_head.forward(
                data_shape=(god_bs_size, num_frame, N, C, H, W),
                img_feats=god_img_feats,
                radar_points=god_radar_points,
                radar_output=god_radar_output,
                lidar_feats=god_lidar_feats,
                labels=labels_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_new,
                roi_mask=god_roi_mask,
                **god_kwargs,
            )
            if self.training:
                outs_god_obstacle, losses_god_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    outs_god_obstacle = self.god_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_god_obstacle = outs
        outs_dict["god"] = outs_god_obstacle

        new_loss_god = {}
        for k, v in losses_god_obstacle.items():
            new_loss_god[f"{k}_god"] = v
        losses_dict.update(new_loss_god)

        # 2. Map perceptron
        outs_map, losses_map = None, {}
        if self.map and map_bs_size > 0:
            # map_kwargs = get_mask_kwargs(kwargs, map_mask, batch_size, task="map")
            img_metas_new, labels_new = metas_labels_task_dict["map"]
            if isinstance(img_feats, dict):
                map_img_feats = img_feats["img_neck_map"]
            else:
                map_img_feats = img_feats
            map_img_feats = get_mask_feat_input(map_mask, batch_size, map_img_feats)
            if self.training:
                outs_map, losses_map = self.map_head(
                    data_shape=(map_bs_size, num_frame, N, C, H, W),  # TODO：map默认 N = 4，num_camera
                    img_feats=map_img_feats,  # frame list[img_feats[bs*cam, C, h=32, w=60]]
                    img_metas=img_metas_new,
                    lidar_feats=None,
                    labels=dict(
                        vectors=map_gt,
                        semantic_mask=img_semantic_seg,
                        map_local2global_info=map_local2global_info,
                        attrs=map_attr,
                        map_scene_weight=map_scene_weight,
                        valid_region=valid_region,
                        lane_bev_segment_map=lane_bev_segment_map,  # add seg
                        bev_manual_mask=bev_manual_mask,  # add manual mask
                    ),
                    scene_frame_idx=scene_frame_idx,
                    **kwargs,
                )
            else:
                outs_map = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=map_img_feats,
                    img_metas=img_metas_new,
                    lidar_feats=None,
                    labels=dict(
                        vectors=map_gt,
                        semantic_mask=img_semantic_seg,
                        map_local2global_info=map_local2global_info,
                        attrs=map_attr,
                        map_scene_weight=map_scene_weight,
                        valid_region=valid_region,
                        lane_bev_segment_map=lane_bev_segment_map,  # add seg
                        bev_manual_mask=bev_manual_mask,  # add manual mask
                    ),
                    scene_frame_idx=scene_frame_idx,  # for analysis
                    return_loss=False,
                    **kwargs,
                )

        outs_dict["map"] = outs_map
        losses_dict.update(losses_map)

        # freespace
        outs_freespace, loss_freespace = None, {}
        if self.freespace and occ_bs_size > 0:
            occ_radar_points, occ_radar_output, occ_lidar_feats = get_mask_input(
                occ_mask, batch_size, radar_points, radar_output, lidar_feats
            )
            assert num_frame == 1, "occ task only support single frame now"
            if isinstance(img_feats, dict):
                occ_img_feats = img_feats["img_neck_occ"]
            else:
                occ_img_feats = img_feats

            occ_img_feats = get_mask_feat_input(occ_mask, batch_size, occ_img_feats, self.occ_cam_idx)
            occ_kwargs = get_mask_kwargs(kwargs, occ_mask, batch_size, task="occ")
            img_metas_new, labels_new = metas_labels_task_dict["occ"]
            img_metas_new = get_cam_masked_img_metas(
                img_metas_new, self.occ_cam_idx, key_list=["ida_mats", "lidar2imgs"]
            )
            outs_freespace, loss_freespace = self.freespace_head.forward(
                data_shape=(occ_bs_size, num_frame, self.occ_cam_num, C, H, W),
                img_feats=occ_img_feats,
                radar_points=occ_radar_points,
                radar_output=occ_radar_output,
                lidar_feats=occ_lidar_feats,
                freespace=occ_kwargs["semantic"] if self.training else None,  # 1, 1, 304, 1088
                freespace_mask=occ_kwargs["semantic_mask"] if self.training else None,
                bev_embedding=self.bev_embedding if self.bev_embedding else None,
                img_metas=img_metas_new,
                l2g=occ_kwargs["l2g"],
                test=(not self.training),
                timestamp=[t % 1e10 for t in occ_kwargs["timestamp"]],
            )
        outs_dict["freespace"] = outs_freespace
        losses_dict.update(loss_freespace)

        if self.training:
            return outs_dict, losses_dict, {}
        else:
            return outs_dict


class VisionEncoder_MultiTaskV2(VisionEncoder_MultiTask):
    def format_infos(
        self, img_shape, frame_idx, img_metas, device, labels=None, labels_bias=0, type="infer", process_box=True
    ):
        batch_size, num_frame, N, C, H, W = img_shape
        img_metas_single_frame = list()
        for batch_idx in range(batch_size):
            img_metas_single_sample = {
                key: img_metas[batch_idx][key][frame_idx]
                for key in img_metas[0].keys()
                if key not in ("imgs_nori_id", "map_tgt")
            }
            img_metas_single_frame.append(img_metas_single_sample)
        # 需要区分 det labels 和 map labels
        if type == "train":
            if (
                hasattr(self, "obstacle_head") or hasattr(self, "god_head") or hasattr(self, "lidar_only_head")
            ) and process_box:
                gt_bboxes_3d, gt_labels_3d, instance_inds = labels
                ff_gt_bboxes = [gt_bboxes_3d[j][frame_idx] for j in range(batch_size)]
                ff_gt_labels = [gt_labels_3d[j][frame_idx] + labels_bias for j in range(batch_size)]
                ff_instance_ids = (
                    [instance_inds[j][frame_idx] for j in range(batch_size)] if instance_inds is not None else None
                )

                labels_single_frame = (ff_gt_bboxes, ff_gt_labels, ff_instance_ids)
                input_info = {
                    "ff_gt_bboxes_list": copy.deepcopy(
                        # [bbox.tensor.to(img_feats.device) for bbox in ff_gt_bboxes_list[frame_idx]]
                        [
                            torch.cat((bbox.gravity_center, bbox.tensor[:, 3:]), dim=1).to(device)
                            for bbox in ff_gt_bboxes
                        ]
                    ),
                    "ff_gt_labels_list": copy.deepcopy(ff_gt_labels),
                    "pad_shape": [H, W],
                }
            else:
                input_info = {"pad_shape": [H, W]}
                labels_single_frame = ([], [], [])
        elif type == "infer":
            labels_single_frame = None
            input_info = {
                "ff_gt_bboxes_list": None,
                "ff_gt_labels_list": None,
                "pad_shape": [H, W],
            }
        img_metas_single_frame = self.format_img_meta(img_metas_single_frame, device)
        img_metas_single_frame.update(input_info)  # for prepare_for_dn function

        return img_metas_single_frame, labels_single_frame

    def forward(
        self,
        points=None,
        img_metas=None,
        imgs=None,
        radar_points: List[torch.tensor] = None,
        img_semantic_seg=None,  # 从这往下都是 map
        sequence_pe=None,
        sequence_data=None,
        sequence_data_noise=None,
        valid_mask_seq=None,
        seq_attribute=None,
        index_in_dataset=None,
        tran_mats_dict=None,
        roi_mask=None,
        multitask_labels=None,
        **kwargs,
    ) -> Any:
        # gt_bboxes_3d=None,
        # gt_labels_3d=None,
        # instance_inds=None,
        self.freeze_bn_func()
        # 0. Prepare groud_truth and img_metas
        if self.camera_encoder:
            batch_size, num_frame, N, C, H, W = imgs.shape
        else:
            batch_size, num_frame = len(points), len(points[0])
            N, C, H, W = 0, 3, 0, 0

        task_names = ["box", "occ", "map", "god", "lidar_only"]
        task_mask_dict = {task_name: torch.arange(0, batch_size) for task_name in task_names}
        task_group_dict = {task_name: None for task_name in task_names}
        if "task_list" in kwargs:
            task_list = kwargs.pop("task_list")
            if self.training:
                assert (
                    len(task_list) == batch_size
                ), f"bs and len(task_list) not match, bs: {batch_size}, task_list: {task_list}"
                # FIXME: 如果不使用self.training过滤，那么e2e推理过程因为存在不同clip的差异，不同gpu最后几个iter可能不对齐，导致推理hang。
                # 如果加上self.training，会存在评测occ任务数据集只读取4v时od head存在问题；并且评测会推理所有任务。
                task_mask_dict = get_task_mask(task_list, task_names=task_names, return_dict=True)
                task_group_dict = get_task_rank_list(task_mask_dict)

        task_bs_size = {task_name: len(task_mask_dict[task_name]) for task_name in task_names}

        metas_labels_task_dict = {}
        # from perceptron.utils import torch_dist
        # rank = torch_dist.get_rank()
        device = imgs.device if self.camera_encoder else points[0][0].device
        for task, task_mask in task_mask_dict.items():
            if self.training and task in ["box", "god", "lidar_only"]:
                gt_bboxes_3d, gt_labels_3d, instance_inds = [
                    [multitask_labels[i][task][k] for i in task_mask]
                    for k in ["gt_bboxes_3d", "gt_labels_3d", "instance_inds"]
                ]  # bs, 1, 1, 11 # right bs, frame, num_obj
                labels = (gt_bboxes_3d, gt_labels_3d, instance_inds)
            else:
                labels = (None, None, None)
            labels_bias = 1
            # 如果加上track 就是0
            if hasattr(self, "obstacle_head") and self.obstacle_head.tracking:
                # TODO: 应该只需要对tracking部分设置
                if task in ["box"]:
                    labels_bias = 0

            img_metas_new, labels_new = [], []

            if len(task_mask) > 0:
                task_img_metas = get_mask_input(task_mask, batch_size, img_metas)[0]
                for frame_idx in range(num_frame):
                    # 需要把map label 和det label 拆分开
                    # FIXME: 不知道在干嘛
                    img_metas_single_frame, labels_single_frame = self.format_infos(
                        [len(task_mask)] + list(imgs.shape)[1:]
                        if self.camera_encoder
                        else (len(task_mask), num_frame, 0, 3, 0, 0),
                        frame_idx,
                        task_img_metas,
                        device,
                        labels=labels,
                        labels_bias=labels_bias,
                        type="train" if self.training else "infer",
                        process_box=True if task in ["box", "god", "lidar_only"] else False,
                    )
                    img_metas_new.append(img_metas_single_frame)
                    labels_new.append(labels_single_frame)

            if task == "occ":
                labels_new = (None, None)
                if self.training:
                    semantic = [multitask_labels[i][task]["semantic"] for i in task_mask]  # bs, 1, 1, 256
                    semantic_mask = [multitask_labels[i][task]["semantic_mask"] for i in task_mask]  # bs, 1, 1, 256
                    # 在dataset中没有concat，需要在这里重新cat
                    if len(semantic) > 0:
                        # 2.5d 多包了一层
                        if isinstance(semantic[0][0], List):
                            semantic = torch.cat(
                                [
                                    torch.stack(semantic[i][0], dim=0).unsqueeze(0).unsqueeze(1)
                                    for i in range(len(semantic))
                                ],
                                dim=0,
                            )
                            semantic_mask = torch.cat(
                                [
                                    torch.stack(semantic_mask[i][0], dim=0).unsqueeze(0).unsqueeze(1)
                                    for i in range(len(semantic))
                                ],
                                dim=0,
                            )
                        else:
                            semantic = torch.stack(
                                [torch.stack(semantic[i], dim=0) for i in range(len(semantic))], dim=0
                            )  # bs, 1, 304, 1088
                            semantic_mask = torch.stack(
                                [torch.stack(semantic_mask[i], dim=0) for i in range(len(semantic_mask))], dim=0
                            )
                        labels_new = (semantic, semantic_mask)
            metas_labels_task_dict[task] = (img_metas_new, labels_new)

        # 1. Extract base feature
        img_feats, radar_output, radar_points, lidar_feats = self.base_encoder(points, imgs, radar_points)
        # 2. Obstacle perceptron
        outs_dict = {}
        losses_dict = {}
        outs_obstacle, losses_obstacle = None, {}
        if self.obstacle and task_bs_size["box"] > 0:
            od_mask = task_mask_dict["box"]
            od_bs_size = task_bs_size["box"]
            od_group = task_group_dict["box"]
            od_radar_points, od_radar_output, od_lidar_feats, od_roi_mask = get_mask_input(
                od_mask, batch_size, radar_points, radar_output, lidar_feats, roi_mask
            )
            if isinstance(img_feats, dict):
                od_img_feats = img_feats["img_neck"]
            else:
                od_img_feats = img_feats
            od_img_feats = get_mask_feat_input(od_mask, batch_size, od_img_feats)
            od_kwargs = get_mask_kwargs(kwargs, od_mask, batch_size, task="box")
            img_metas_new, labels_new = metas_labels_task_dict["box"]
            outs = self.obstacle_head.forward(
                data_shape=(od_bs_size, num_frame, N, C, H, W),
                img_feats=od_img_feats,
                radar_points=od_radar_points,
                radar_output=od_radar_output,
                lidar_feats=od_lidar_feats,
                labels=labels_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_new,
                roi_mask=od_roi_mask,
                **od_kwargs,
                group=od_group,
            )
            if self.training:
                outs_obstacle, losses_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    if self.obstacle_head.tracking:
                        bbox_list = self.obstacle_head.det_head.dense_head.get_e2e_bboxes(outs, img_metas_new[0])
                        self.obstacle_head.runtime_tracker_list[0].update_active_tracks(
                            outs["track_instances"], outs["active_mask"]
                        )
                        if not self.cfg.tracking_module.get("with_velocity", False):
                            bbox_results = [
                                self.track_bbox3d2result(bboxes, scores, labels, obj_idxes, track_scores, forecasting)
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting in bbox_list
                            ]
                        else:
                            bbox_results = [
                                self.track_bbox3d2result(
                                    bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity
                                )
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity in bbox_list
                            ]

                        bbox_results[0]["track_ids"] = bbox_results[0]["track_ids"].long().cpu().numpy()
                        outs_obstacle = bbox_results
                    else:
                        outs_obstacle = self.obstacle_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_obstacle = outs
        outs_dict["obstacle"] = outs_obstacle
        losses_dict.update(losses_obstacle)

        outs_god_obstacle, losses_god_obstacle = None, {}
        if self.god and task_bs_size["god"] > 0:  # TODO
            god_mask = task_mask_dict["god"]
            god_bs_size = task_bs_size["god"]
            god_group = task_group_dict["god"]
            god_radar_points, god_radar_output, god_lidar_feats, god_roi_mask = get_mask_input(
                god_mask, batch_size, radar_points, radar_output, lidar_feats, roi_mask
            )
            if isinstance(img_feats, dict):
                if "img_neck_god" in img_feats:
                    god_img_feats = img_feats["img_neck_god"]
                else:
                    god_img_feats = img_feats["img_neck"]  # 和od共用一个neck
            else:
                god_img_feats = img_feats
            god_img_feats = get_mask_feat_input(god_mask, batch_size, god_img_feats)
            god_kwargs = get_mask_kwargs(kwargs, god_mask, batch_size, task="god")
            img_metas_new, labels_new = metas_labels_task_dict["god"]
            # import pickle # loss_cls=0.2521 loss_bbox=1.2733
            # loss_cls_god=0.2534 loss_bbox_god=1.2869
            # loss_cls_god=0.2570 loss_bbox_god=1.1733
            # feats_all = pickle.load(open("god_good_feats_all.pkl", "rb"))
            # god_img_feats = feats_all["img_feats"]
            # god_radar_points = feats_all["radar_points"]
            # god_radar_output = feats_all["radar_output"]
            # god_lidar_feats = feats_all["lidar_feats"]
            # god_roi_mask = feats_all["roi_mask"]
            # god_kwargs = feats_all["kwargs"]

            outs = self.god_head.forward(
                data_shape=(god_bs_size, num_frame, N, C, H, W),
                img_feats=god_img_feats,
                radar_points=god_radar_points,
                radar_output=god_radar_output,
                lidar_feats=god_lidar_feats,
                labels=labels_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_new,
                roi_mask=god_roi_mask,
                group=god_group,
                **god_kwargs,
            )
            if self.training:
                outs_god_obstacle, losses_god_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    outs_god_obstacle = self.god_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_god_obstacle = outs
        outs_dict["god"] = outs_god_obstacle

        new_loss_god = {}
        for k, v in losses_god_obstacle.items():
            new_loss_god[f"{k}_god"] = v
        losses_dict.update(new_loss_god)

        # 2. Map perceptron
        outs_map, losses_map = None, {}
        if self.map and task_bs_size["map"] > 0:
            map_bs_size = task_bs_size["map"]
            # map_mask = task_mask_dict["map"]
            # FIXME: 需要处理map对应的feat以及labels
            img_metas_new, labels_new = metas_labels_task_dict["map"]
            if self.training:
                outs_map, losses_map = self.map_head(
                    data_shape=(map_bs_size, num_frame, N, C, H, W),
                    img_feats=img_feats[0],
                    img_metas=img_metas_new,
                    img_semantic_seg=img_semantic_seg,
                    sequence_pe=sequence_pe,
                    sequence_data=sequence_data,
                    sequence_data_noise=sequence_data_noise,
                    valid_mask_seq=valid_mask_seq,
                    seq_attribute=seq_attribute,
                    # timestamp=timestamp,
                    **kwargs,
                )
            else:
                outs_map, outs_map_logits = self.map_head(
                    data_shape=(map_bs_size, num_frame, N, C, H, W),
                    img_feats=img_feats[0],
                    img_metas=img_metas_new,
                    imgs=imgs,
                    sequence_data=sequence_data,
                    valid_mask_seq=valid_mask_seq,
                    seq_attribute=seq_attribute,
                    index_in_dataset=index_in_dataset,
                    tran_mats_dict=tran_mats_dict,
                    **kwargs,
                )

        outs_dict["map"] = outs_map
        losses_dict.update(losses_map)

        # freespace
        outs_freespace, loss_freespace = None, {}
        occ_bs_size = task_bs_size["occ"]
        if self.freespace and occ_bs_size > 0:
            occ_mask = task_mask_dict["occ"]
            occ_radar_points, occ_radar_output, occ_lidar_feats = get_mask_input(
                occ_mask, batch_size, radar_points, radar_output, lidar_feats
            )
            assert num_frame == 1, "occ task only support single frame now"
            if isinstance(img_feats, dict):
                occ_img_feats = img_feats["img_neck_occ"]
            else:
                occ_img_feats = img_feats

            occ_img_feats = get_mask_feat_input(occ_mask, batch_size, occ_img_feats, self.occ_cam_idx)
            occ_kwargs = get_mask_kwargs(kwargs, occ_mask, batch_size, task="occ")
            img_metas_new, labels_new = metas_labels_task_dict["occ"]
            semantic, semantic_mask = labels_new
            img_metas_new = get_cam_masked_img_metas(
                img_metas_new, self.occ_cam_idx, key_list=["ida_mats", "lidar2imgs"]
            )
            outs_freespace, loss_freespace = self.freespace_head.forward(
                data_shape=(occ_bs_size, num_frame, self.occ_cam_num, C, H, W),
                img_feats=occ_img_feats,
                radar_points=occ_radar_points,
                radar_output=occ_radar_output,
                lidar_feats=occ_lidar_feats,
                freespace=semantic if self.training else None,
                freespace_mask=semantic_mask if self.training else None,
                bev_embedding=self.bev_embedding if self.bev_embedding else None,
                img_metas=img_metas_new,
                l2g=occ_kwargs["l2g"],
                test=(not self.training),
                timestamp=[t % 1e10 for t in occ_kwargs["timestamp"]],
            )
        outs_dict["freespace"] = outs_freespace
        losses_dict.update(loss_freespace)

        # lidar_only
        self.forward_lidar_only_head(
            task_bs_size,
            task_mask_dict,
            task_group_dict,
            lidar_feats,
            roi_mask,
            kwargs,
            metas_labels_task_dict,
            batch_size,
            outs_dict,
            losses_dict,
        )
        if self.training:
            if self.use_auto_loss:
                new_loss_dict = dict()
                for key, value in losses_dict.items():
                    if key.startswith("occ_") and isinstance(value, torch.Tensor):
                        if "loss_occ" not in new_loss_dict:
                            new_loss_dict["loss_occ"] = value
                        else:
                            new_loss_dict["loss_occ"] += value
                    elif key == "loss_rpn_lidar_only" and isinstance(value, torch.Tensor):
                        if "loss_lidar_only" not in new_loss_dict:
                            new_loss_dict["loss_lidar_only"] = value
                        else:
                            new_loss_dict["loss_lidar_only"] += value
                    elif "loss" in key and isinstance(value, torch.Tensor):
                        if "loss_obstacle" not in new_loss_dict:
                            new_loss_dict["loss_obstacle"] = value
                        else:
                            new_loss_dict["loss_obstacle"] += value
                new_loss_dict["loss_sum"] = self.auto_loss(
                    new_loss_dict["loss_obstacle"], new_loss_dict["loss_occ"], new_loss_dict["loss_lidar_only"]
                )
                new_loss_dict.update({"loss_obstacle": new_loss_dict["loss_obstacle"].item()})
                new_loss_dict.update({"loss_occ": new_loss_dict["loss_occ"].item()})
                new_loss_dict.update({"loss_lidar_only": new_loss_dict["loss_lidar_only"].item()})
                new_loss_dict.update({"auto_loss_params_od": self.auto_loss.params[0].item()})
                new_loss_dict.update({"auto_loss_params_occ": self.auto_loss.params[1].item()})
                new_loss_dict.update({"auto_loss_params_1l": self.auto_loss.params[2].item()})
                return outs_dict, new_loss_dict, {}
            else:
                return outs_dict, losses_dict, {}
        else:
            if "lidar_only" not in outs_dict:
                outs_dict["lidar_only"] = None
            return outs_dict

    def forward_lidar_only_head(
        self,
        task_bs_size,
        task_mask_dict,
        task_group_dict,
        lidar_feats,
        roi_mask,
        kwargs,
        metas_labels_task_dict,
        batch_size,
        outs_dict,
        losses_dict,
    ):

        if self.lidar_only and task_bs_size["lidar_only"] > 0:
            lidar_only_mask = task_mask_dict["lidar_only"]
            # lidar_only_bs_size = task_bs_size["lidar_only"]
            # lidar_only_group = task_group_dict["lidar_only"]

            lidar_only_lidar_feats, lidar_only_roi_mask = get_mask_input(
                lidar_only_mask, batch_size, lidar_feats, roi_mask
            )

            lidar_only_lidar_feats_shape = lidar_only_lidar_feats.shape
            H, W = lidar_only_lidar_feats_shape[3], lidar_only_lidar_feats_shape[4]
            grid_size = self.lidar_only_head.dense_head.grid_size
            H_2, W_2 = grid_size[1] // 2, grid_size[0] // 2
            lidar_only_lidar_feats = lidar_only_lidar_feats[
                :, 0, :, (H - H_2) // 2 : (H + H_2) // 2, (W - W_2) // 2 : (W + W_2) // 2
            ]  # (bs, 384, 300, 124)
            # lidar_only_kwargs = get_mask_kwargs(kwargs, lidar_only_mask, batch_size, task="lidar_only")
            if self.training:
                img_metas_new, labels_new = metas_labels_task_dict["lidar_only"]
                labels_old = format_lidar_only_labels(labels_new[0])  # (bs, n, 8)
            else:
                labels_old = None
            forward_ret_dict = self.lidar_only_head.forward(
                lidar_only_lidar_feats,  # torch.Size([bs, 384, 300, 124])
                labels_old,
            )
            if self.training:
                with torch.cuda.amp.autocast(enabled=False):
                    for task_id, encoding in forward_ret_dict["box_encoding"].items():
                        encoding[torch.isinf(encoding)] = 0
                    loss_rpn, tb_dict = self.lidar_only_head.dense_head.get_loss(forward_ret_dict)
                losses_lidar_only = {"loss_rpn_lidar_only": loss_rpn * self.lidar_only_loss_weight}
                losses_lidar_only.update(
                    {"lidar_only_" + k: v * self.lidar_only_loss_weight for k, v in tb_dict.items()}
                )
                losses_dict.update(losses_lidar_only)
            outs_dict["lidar_only"] = forward_ret_dict
        return outs_dict, losses_dict


def fill_batch_tensor(batch_data: list):
    if max([len(x) for x in batch_data]) == min([len(x) for x in batch_data]):
        return torch.stack(batch_data, dim=0)
    else:
        batch_size = len(batch_data)
        batch_length = max([len(x) for x in batch_data])
        for data in batch_data:
            if data.size != 0:
                data_shape = data.shape
                break
        batch_data_shape = (batch_size, batch_length, *data_shape[1:])
        batch_tensor = torch.zeros(batch_data_shape, dtype=batch_data[0].dtype, device=batch_data[0].device)
        for i, data in enumerate(batch_data):
            if data.size != 0:
                batch_tensor[i, : len(data)] = data
        return batch_tensor


def format_lidar_only_labels(labels_new):
    """
    Format the labels for lidar only task.
    Args:
        labels_new: [LiDARInstance3DBoxes, gt_labels_3d, instance_inds]
            gt_labels_3d: List[torch.Tensor], each tensor is of shape (num_obj, )
    Returns:
        labels_old: (bs, n, 8) Formatted labels for lidar only task.
    """
    boxes3d_list = labels_new[0]  # List[LiDARInstance3DBoxes]
    gt_labels_3d = labels_new[1]  # List[torch.Tensor]
    # instance_inds = labels_new[2]  # List[torch.Tensor]

    boxes3d_list = [torch.cat([boxes3d.gravity_center, boxes3d.tensor[:, 3:]], dim=-1) for boxes3d in boxes3d_list]
    boxes3d_tensor = fill_batch_tensor(boxes3d_list)  # (bs, n, 7)
    gt_labels_3d = fill_batch_tensor(gt_labels_3d)  # (bs, n)
    labels_old = torch.cat([boxes3d_tensor[:, :, :7], gt_labels_3d.unsqueeze(-1).float()], dim=-1)  # (bs, n, 8)
    return labels_old
