import mmcv
import torch
import torch.nn as nn
import numpy as np
import random

import copy
from typing import Any, List
from perceptron.data.det3d.modules.utils.grid_mask import GridMask, GridJuncMask
from perceptron.models.multisensor_fusion.cmt.base import <PERSON><PERSON><PERSON><PERSON>, CameraEncoderTwoNeck, LidarEncoder
from perceptron.models.multisensor_fusion import BaseEncoder, BaseMultiSensorFusion
from perceptron.models.end2end.perceptron.obj_perceptor import Obstacle
from perceptron.models.end2end.perceptron.map_perceptor import MapHead
from perceptron.layers.head.seg3d.bev_freespace_head import BEVFreespaceSegHead
from perceptron.models.end2end.perceptron.lidar_only_model import DetHead as LidarOnlyHead


class RadarEncoder(BaseEncoder):
    def __init__(self, radar_encoder_cfg: mmcv.Config, **kwargs):
        super().__init__()
        self.radar_backbone = self.build_radar_backbone(radar_encoder_cfg["out_channels"])

    def build_radar_backbone(self, out_channels):
        mlp_layers = []

        for i in range(0, len(out_channels) - 1):
            mlp_layers.append(
                nn.Linear(
                    in_features=out_channels[i],
                    out_features=out_channels[i + 1],
                )
            )
            mlp_layers.append(nn.BatchNorm1d(out_channels[i + 1]))
            mlp_layers.append(nn.ReLU(inplace=True))

        return nn.Sequential(*mlp_layers)

    def forward(
        self,
        radar_points: torch.tensor,
    ):
        radar_feats = self.radar_backbone(radar_points)
        return radar_feats


class VisionEncoder(BaseMultiSensorFusion):
    r"""
    `BEVFusion`: Multi-Task Multi-Sensor Fusion with Unified Bird's-Eye View Representation.

    `Reference`: https://arxiv.org/abs/2205.13542
    """

    def __init__(self, model_cfg) -> Any:
        super(VisionEncoder, self).__init__()

        # base setting
        self.cfg = model_cfg
        self.num_query = model_cfg.num_query
        self.class_names = model_cfg.class_names
        self.num_classes = model_cfg.num_classes
        self.embed_dims = model_cfg.get("embed_dims", 256)
        self.train_backbone = model_cfg.get("train_backbone", True)
        self.drop_lidar = model_cfg.get("drop_lidar", 1.0)
        self.freeze_bn = model_cfg.get("freeze_bn", False)
        self.freeze_modules = model_cfg.get("freeze_module", None)
        self.result_wo_decoder = model_cfg.get(
            "result_decoder", False
        )  # used when model defined by extra E2E prediction module

        # multi-modal setting
        if self.cfg.get("camera_encoder", None):
            self.camera_encoder = self._configure_camera_encoder()

            if self.cfg.get("grid_mask", None):
                self.grid_mask = GridMask(
                    True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=self.cfg.get("grid_mask_prob", 0.7)
                )
            else:
                self.grid_mask = None

            if self.cfg.get("grid_junc_mask", None):
                self.grid_junc_mask = GridJuncMask(
                    rotate=1, offset=False, ratio=0.25, mode=0, prob=self.cfg.get("grid_mask_prob", 0.5)
                )
            else:
                self.grid_junc_mask = None

        else:
            self.camera_encoder = None

        if self.cfg.get("radar_encoder", None):
            self.radar_encoder = self._configure_radar_encoder()
            self.use_radar_init = model_cfg.get("use_radar_init", False)
            if self.use_radar_init:
                self.bev_embedding = nn.Sequential(
                    nn.Linear(384, self.det_head.dense_head.hidden_dim),
                    nn.ReLU(inplace=True),
                    nn.Linear(self.det_head.dense_head.hidden_dim, self.det_head.dense_head.hidden_dim),
                )
            else:
                self.bev_embedding = None
        else:
            self.radar_encoder = None
            self.bev_embedding = None

        if self.cfg.get("lidar_encoder", None):
            self.lidar_encoder = self._configure_lidar_encoder()
        else:
            self.lidar_encoder = None

        # multi-task setting
        self.obstacle = model_cfg.get("det_head", False)
        self.map = model_cfg.get("map_head", False)
        self.freespace = model_cfg.get("freespace_head", False)
        self.god = model_cfg.get("god_head", False)
        self.lidar_only = model_cfg.get("lidar_only_head", False)

        if self.obstacle:
            self.obstacle_head = self._configure_obstacle_head()

        if self.map:
            self.map_head = self._configure_map_head()

        if self.freespace:
            self.freespace_head = self._configure_freespace_head()

        if self.god:
            self.god_head = self._configure_god_head()

        if self.lidar_only:
            self.lidar_only_head = self._configure_lidar_only_head()

        self.init_params_and_layers()

    def init_params_and_layers(self):
        """Generate the instances for tracking, especially the object queries"""
        # query initialization for detection
        # reference points, mapping fourier encoding to embed_dims

        # freeze backbone
        if not self.train_backbone:
            if self.with_camera_encoder:
                for param in self.camera_encoder.img_backbone.parameters():
                    param.requires_grad = False
            if self.with_radar_encoder:
                for param in self.radar_encoder.radar_backbone.parameters():
                    param.requires_grad = False
            if self.with_lidar_encoder:
                for param in self.lidar_encoder.parameters():
                    param.requires_grad = False
        if self.freeze_modules is not None:
            print("self.freeze_modules ", self.freeze_modules)
            for module in self.freeze_modules:
                if "camera_encoder" in module:
                    for param in self.camera_encoder.parameters():
                        param.requires_grad = False
                if "lidar_encoder" in module:
                    for param in self.lidar_encoder.parameters():
                        param.requires_grad = False

    def freeze_bn_func(self):
        # freeze backbone
        if not self.train_backbone:
            if self.freeze_bn and self.with_camera_encoder:
                self.camera_encoder.img_backbone.eval()
            if self.freeze_bn and self.with_radar_encoder:
                self.radar_encoder.radar_backbone.eval()
            if self.freeze_bn and self.with_lidar_encoder:
                self.lidar_encoder.eval()

    def format_img_meta(self, img_metas, device, task_name):
        img_metas_single_frame = {}
        for key, item in img_metas[0].items():
            if (
                key == "lidar2imgs"
                or key == "ida_mats"
                or key == "lidar2ego"
                or key == "ego2global"
                or key == "scene_name"
                or key == "post_rot_bda"
            ):
                # 对 post_rot_bda 做单独检查
                if key == "post_rot_bda":
                    # 如果任何 batch 的 post_rot_bda 是空字典，就跳过
                    if any(
                        isinstance(img_metas[batch_idx].get("post_rot_bda"), dict)
                        and len(img_metas[batch_idx]["post_rot_bda"]) == 0
                        for batch_idx in range(len(img_metas))
                    ):
                        continue

                img_metas_single_frame[key] = (
                    torch.tensor(np.stack([img_metas[batch_idx][key] for batch_idx in range(len(img_metas))]))
                    .unsqueeze(1)
                    .to(device)
                )
                if task_name == "map" and key != "scene_name":
                    img_metas_single_frame[key] = img_metas_single_frame[key][:, :, :4, ...]
            # elif key == "post_rot_bda":
            #     img_metas_single_frame[key] = (
            #         torch.tensor(np.stack([img_metas[batch_idx][key] for batch_idx in range(len(img_metas))]))
            #         .unsqueeze(1)
            #         .to(device)
            #     )
            elif key == "bda_mat":
                img_metas_single_frame[key] = torch.tensor(
                    np.stack([img_metas[batch_idx][key] for batch_idx in range(len(img_metas))])
                ).to(device)
            elif key == "box_type_3d":
                img_metas_single_frame[key] = [img_metas[batch_idx][key] for batch_idx in range(len(img_metas))]
            else:
                pass
        return img_metas_single_frame

    def format_infos(
        self,
        img_shape,
        frame_idx,
        img_metas,
        device,
        labels=None,
        labels_bias=0,
        type="infer",
        process_box=True,
        task_name="box",
    ):
        batch_size, num_frame, N, C, H, W = img_shape
        img_metas_single_frame = list()
        for batch_idx in range(batch_size):
            img_metas_single_sample = {
                key: img_metas[batch_idx][key][frame_idx]
                for key in img_metas[0].keys()
                if key not in ("imgs_nori_id", "map_tgt")
            }
            img_metas_single_frame.append(img_metas_single_sample)
        # 需要区分 det labels 和 map labels
        if type == "train":
            if hasattr(self, "obstacle_head") and process_box:
                gt_bboxes_3d, gt_labels_3d, instance_inds = labels
                ff_gt_bboxes = [gt_bboxes_3d[j][frame_idx] for j in range(batch_size)]
                ff_gt_labels = [gt_labels_3d[j][frame_idx] + labels_bias for j in range(batch_size)]
                ff_instance_ids = (
                    [instance_inds[j][frame_idx] for j in range(batch_size)] if instance_inds is not None else None
                )

                labels_single_frame = (ff_gt_bboxes, ff_gt_labels, ff_instance_ids)
                input_info = {
                    "ff_gt_bboxes_list": copy.deepcopy(
                        # [bbox.tensor.to(img_feats.device) for bbox in ff_gt_bboxes_list[frame_idx]]
                        [
                            torch.cat((bbox.gravity_center, bbox.tensor[:, 3:]), dim=1).to(device)
                            for bbox in ff_gt_bboxes
                        ]
                    ),
                    "ff_gt_labels_list": copy.deepcopy(ff_gt_labels),
                    "pad_shape": [H, W],
                }
            else:
                input_info = {"pad_shape": [H, W]}
                labels_single_frame = ([], [], [])
        elif type == "infer":
            labels_single_frame = None
            input_info = {
                "ff_gt_bboxes_list": None,
                "ff_gt_labels_list": None,
                "pad_shape": [H, W],
            }
        img_metas_single_frame = self.format_img_meta(img_metas_single_frame, device, task_name)
        img_metas_single_frame.update(input_info)  # for prepare_for_dn function

        return img_metas_single_frame, labels_single_frame

    def extract_img_feat(self, img):
        # Extract img_feats
        if self.with_camera_encoder:
            if self.grid_mask is not None:
                batch_size, num_frame, N, C, H, W = img.shape
                img = img.view(batch_size * num_frame * N, C, H, W)
                img = self.grid_mask(img)
                if self.cfg.get("grid_junc_mask", None):
                    img = self.grid_junc_mask(img)
                img = img.view(batch_size, num_frame, N, C, H, W)
            img_feats = self.camera_encoder(img, self.train_backbone)
        else:
            img_feats = None

        return img_feats

    def extract_radar_feat(self, radar_points):
        if self.with_radar_encoder:
            radar_points_view = radar_points.float().view(-1, 11)
            B, N_frames, N_num = radar_points.shape[:3]
            radar_output_feat = self.radar_encoder(radar_points_view.clone())
            C = radar_output_feat.shape[-1]
            radar_output = radar_output_feat.reshape(B, N_frames, N_num, C)
            radar_points = radar_points[..., :3]
        else:
            radar_output = None
            radar_points = None
        return radar_output, radar_points

    def extract_lidar_feat(self, points):
        pass

    def base_encoder(
        self,
        points=None,
        img=None,
        radar_points: List[torch.tensor] = None,
    ):
        if self.camera_encoder is not None and img is not None:
            batch_size, num_frame, N, C, H, W = img.shape
            # img = img.flip(3)  # TODO: 为了和 map 对点，最终应当移除
            img_feats = self.extract_img_feat(img.contiguous())
            # embed()
        else:
            img_feats = None

        if self.radar_encoder and radar_points is not None:
            radar_output, radar_points = self.extract_radar_feat(radar_points)
            if self.use_radar_init:
                radar_points = self.det_head.dense_head.get_radar_pos_init(radar_points)
        else:
            radar_output, radar_points = None, None

        if self.lidar_encoder and points is not None:
            lidar_feats = self.lidar_encoder(points)
        else:
            lidar_feats = None

        return img_feats, radar_output, radar_points, lidar_feats

    def forward(
        self,
        points=None,
        img_metas=None,
        gt_bboxes_3d=None,
        gt_labels_3d=None,
        instance_inds=None,
        imgs=None,
        radar_points: List[torch.tensor] = None,
        img_semantic_seg=None,  # 从这往下都是 map
        map_gt=None,
        map_local2global_info=None,
        scene_frame_idx=None,
        map_attr=None,
        map_scene_weight=None,
        valid_region=None,
        lane_bev_segment_map=None,  # add seg
        bev_manual_mask=None,  # add manual_mask
        roi_mask=None,
        **kwargs,
    ) -> Any:
        self.freeze_bn_func()
        # 0. Prepare groud_truth and img_metas
        if self.camera_encoder:
            batch_size, num_frame, N, C, H, W = imgs.shape
        else:
            batch_size, num_frame = len(points), len(points[0])
            N, C, H, W = 0, 3, 0, 0
        img_metas_new, labels_new = [], []
        labels = (gt_bboxes_3d, gt_labels_3d, instance_inds)
        labels_bias = 1  # TODO: zhangyi check
        # 如果加上track 就是0
        if hasattr(self, "obstacle_head") and self.obstacle_head.tracking:
            labels_bias = 0
        for frame_idx in range(num_frame):
            # 需要把map label 和det label 拆分开
            img_metas_single_frame, labels_single_frame = self.format_infos(
                imgs.shape if self.camera_encoder else (batch_size, num_frame, 0, 3, 0, 0),
                frame_idx,
                img_metas,
                imgs.device if self.camera_encoder else points[0][0].device,
                labels=labels,
                labels_bias=labels_bias,
                type="train" if self.training else "infer",
            )
            img_metas_new.append(img_metas_single_frame)
            labels_new.append(labels_single_frame)

        # 1. Extract base feature
        img_feats, radar_output, radar_points, lidar_feats = self.base_encoder(points, imgs, radar_points)

        if random.random() > self.drop_lidar and self.training:
            lidar_feats *= 0

        # 2. Obstacle perceptron
        if self.obstacle:
            outs = self.obstacle_head.forward(
                data_shape=(batch_size, num_frame, N, C, H, W),
                img_feats=img_feats,
                radar_points=radar_points,
                radar_output=radar_output,
                lidar_feats=lidar_feats,
                labels=labels_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_new,
                roi_mask=roi_mask,
                **kwargs,
            )
            if self.training:
                outs_obstacle, losses_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    if self.obstacle_head.tracking:
                        bbox_list = self.obstacle_head.det_head.dense_head.get_e2e_bboxes(outs, img_metas_new[0])
                        self.obstacle_head.runtime_tracker_list[0].update_active_tracks(
                            outs["track_instances"], outs["active_mask"]
                        )
                        if not self.cfg.tracking_module.get("with_velocity", False):
                            bbox_results = [
                                self.track_bbox3d2result(bboxes, scores, labels, obj_idxes, track_scores, forecasting)
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting in bbox_list
                            ]
                        else:
                            bbox_results = [
                                self.track_bbox3d2result(
                                    bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity
                                )
                                for bboxes, scores, labels, obj_idxes, track_scores, forecasting, velocity in bbox_list
                            ]

                        bbox_results[0]["track_ids"] = bbox_results[0]["track_ids"].long().cpu().numpy()
                        outs_obstacle = bbox_results
                    else:
                        outs_obstacle = self.obstacle_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_obstacle = outs
        else:
            outs_obstacle, losses_obstacle = None, {}

        # 2. Map perceptron
        if self.map:
            if self.training:
                outs_map, losses_map = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=img_feats,  # frame list[img_feats[bs*cam, C, h=32, w=60]]
                    img_metas=img_metas_new,
                    lidar_feats=None,  # lidar_feats,
                    labels=dict(
                        vectors=map_gt,
                        semantic_mask=img_semantic_seg,
                        map_local2global_info=map_local2global_info,
                        attrs=map_attr,
                        map_scene_weight=map_scene_weight,
                        valid_region=valid_region,
                        lane_bev_segment_map=lane_bev_segment_map,  # add seg
                        bev_manual_mask=bev_manual_mask,  # add manual mask
                    ),
                    scene_frame_idx=scene_frame_idx,
                    **kwargs,
                )
            else:
                outs_map = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=img_feats,
                    img_metas=img_metas_new,
                    lidar_feats=None,  # lidar_feats,
                    labels=dict(
                        vectors=map_gt,
                        semantic_mask=img_semantic_seg,
                        map_local2global_info=map_local2global_info,
                        attrs=map_attr,
                        map_scene_weight=map_scene_weight,
                        valid_region=valid_region,
                        lane_bev_segment_map=lane_bev_segment_map,  # add seg
                        bev_manual_mask=bev_manual_mask,  # add manual mask
                    ),
                    scene_frame_idx=scene_frame_idx,  # for analysis
                    return_loss=False,
                    **kwargs,
                )
        else:
            outs_map, losses_map = None, {}

        # freespace
        if self.freespace:
            outs_freespace, loss_freespace = self.freespace_head.forward(
                data_shape=(batch_size, num_frame, N, C, H, W),
                img_feats=img_feats,
                radar_points=radar_points,
                radar_output=radar_output,
                lidar_feats=lidar_feats,
                freespace=kwargs["semantic"] if self.training else None,
                freespace_mask=kwargs["semantic_mask"] if self.training else None,
                bev_embedding=self.bev_embedding if self.bev_embedding else None,
                img_metas=img_metas_new,
                l2g=kwargs["l2g"],
                test=(not self.training),
                timestamp=[t % 1e10 for t in kwargs["timestamp"]],
            )
        else:
            outs_freespace, loss_freespace = None, {}
        if self.training:
            losses_obstacle.update(losses_map)
            losses_obstacle.update(loss_freespace)
            return (outs_obstacle, outs_map), losses_obstacle, {}
        else:
            if self.obstacle and self.freespace and self.map:
                return (outs_obstacle, outs_map, outs_freespace)
            if self.freespace:
                return outs_freespace
            if self.map:
                return outs_map
            return outs_obstacle

    def _configure_camera_encoder(self, for_map=False):
        return CameraEncoder(self.cfg.camera_encoder)

    def _configure_radar_encoder(self):
        return RadarEncoder(self.cfg.radar_encoder)

    def _configure_lidar_encoder(self):
        return LidarEncoder(self.cfg.lidar_encoder)

    def _configure_obstacle_head(self):
        return Obstacle(self.cfg)

    def _configure_map_head(self):
        return MapHead(**self.cfg.map_head)

    def _configure_freespace_head(self):
        self.cfg.freespace_head.pop("type", None)
        return BEVFreespaceSegHead(**self.cfg.freespace_head)

    def _configure_god_head(self):
        return Obstacle(self.cfg.god_head)

    def _configure_lidar_only_head(self):
        return LidarOnlyHead(self.cfg.lidar_only_head)

    def track_bbox3d2result(
        self, bboxes, scores, labels, obj_idxes, track_scores, forecasting=None, velocity=None, attrs=None
    ):
        """Convert detection results to a list of numpy arrays.
        Args:
            bboxes (torch.Tensor): Bounding boxes with shape of (n, 5).
            labels (torch.Tensor): Labels with shape of (n, ).
            scores (torch.Tensor): Scores with shape of (n, ).
            forecasting (torch.Tensor): Motion forecasting with shape of (n, T, 2)
            attrs (torch.Tensor, optional): Attributes with shape of (n, ). \
                Defaults to None.
        Returns:
            dict[str, torch.Tensor]: Bounding box results in cpu mode.
                - boxes_3d (torch.Tensor): 3D boxes.
                - scores (torch.Tensor): Prediction scores.
                - labels_3d (torch.Tensor): Box labels.
                - attrs_3d (torch.Tensor, optional): Box attributes.
                - forecasting (torh.Tensor, optional): Motion forecasting
        """
        result_dict = dict(
            boxes_3d=bboxes.to("cpu"),
            scores_3d=scores.cpu(),
            labels_3d=labels.cpu(),
            track_scores=track_scores.cpu(),
            track_ids=obj_idxes.cpu(),
        )

        if attrs is not None:
            result_dict["attrs_3d"] = attrs.cpu()

        if forecasting is not None:
            result_dict["forecasting"] = forecasting.cpu()[..., :2]

        if velocity is not None:
            result_dict["velocity"] = velocity.cpu()[..., :2]

        return result_dict


class VisionEncoder2Necks(VisionEncoder):
    def __init__(self, model_cfg) -> Any:
        super().__init__(model_cfg)

    def _configure_camera_encoder(self, for_map=False):
        return CameraEncoderTwoNeck(self.cfg.camera_encoder)

    def forward(
        self,
        points=None,
        img_metas=None,
        gt_bboxes_3d=None,
        gt_labels_3d=None,
        instance_inds=None,
        imgs=None,
        radar_points: List[torch.tensor] = None,
        img_semantic_seg=None,  # 从这往下都是 map
        sequence_pe=None,
        sequence_data=None,
        sequence_data_noise=None,
        valid_mask_seq=None,
        seq_attribute=None,
        index_in_dataset=None,
        tran_mats_dict=None,
        **kwargs,
    ) -> Any:
        self.freeze_bn_func()
        # 0. Prepare groud_truth and img_metas
        batch_size, num_frame, N, C, H, W = imgs.shape
        img_metas_new, labels_new = [], []
        labels = (gt_bboxes_3d, gt_labels_3d, instance_inds)
        labels_bias = 1  # TODO: zhangyi check
        # 如果加上track 就是0
        if hasattr(self, "obstacle_head") and self.obstacle_head.tracking:
            labels_bias = 0
        for frame_idx in range(num_frame):
            # 需要把map label 和det label 拆分开
            img_metas_single_frame, labels_single_frame = self.format_infos(
                imgs.shape if self.camera_encoder else (batch_size, num_frame, 0, 3, 0, 0),
                frame_idx,
                img_metas,
                imgs.device if self.camera_encoder else points[0][0].device,
                labels=labels,
                labels_bias=labels_bias,
                type="train" if self.training else "infer",
            )
            img_metas_new.append(img_metas_single_frame)
            labels_new.append(labels_single_frame)

        # 1. Extract base feature
        img_feats, img_feats_map, radar_output, radar_points, lidar_feats = self.base_encoder(
            points, imgs, radar_points
        )

        # 2. Obstacle perceptron
        if self.obstacle:
            outs = self.obstacle_head.forward(
                data_shape=(batch_size, num_frame, N, C, H, W),
                img_feats=img_feats,
                radar_points=radar_points,
                radar_output=radar_output,
                lidar_feats=lidar_feats,
                labels=labels_new,
                bev_embedding=self.bev_embedding,
                img_metas=img_metas_new,
                **kwargs,
            )
            if self.training:
                outs_obstacle, losses_obstacle, _ = outs
            else:
                if not self.result_wo_decoder:
                    if self.obstacle_head.tracking:
                        bbox_list = self.obstacle_head.det_head.dense_head.get_e2e_bboxes(outs, img_metas_new[0])
                        self.obstacle_head.runtime_tracker.update_active_tracks(
                            outs["track_instances"], outs["active_mask"]
                        )
                        bbox_results = [
                            self.track_bbox3d2result(bboxes, scores, labels, obj_idxes, track_scores, forecasting)
                            for bboxes, scores, labels, obj_idxes, track_scores, forecasting in bbox_list
                        ]
                        bbox_results[0]["track_ids"] = bbox_results[0]["track_ids"].long().cpu().numpy()
                        outs_obstacle = bbox_results
                    else:
                        outs_obstacle = self.obstacle_head.det_head.dense_head.bbox_coder.decode([outs])
                else:
                    outs_obstacle = outs
        else:
            outs_obstacle, losses_obstacle = None, {}

        # 2. Map perceptron
        if self.map:
            if self.training:
                outs_map, losses_map = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=img_feats_map[0],
                    img_metas=img_metas_new,
                    img_semantic_seg=img_semantic_seg,
                    sequence_pe=sequence_pe,
                    sequence_data=sequence_data,
                    sequence_data_noise=sequence_data_noise,
                    valid_mask_seq=valid_mask_seq,
                    seq_attribute=seq_attribute,
                    # timestamp=timestamp,
                    **kwargs,
                )
            else:
                outs_map, outs_map_logits = self.map_head(
                    data_shape=(batch_size, num_frame, N, C, H, W),
                    img_feats=img_feats_map[0],
                    img_metas=img_metas_new,
                    imgs=imgs,
                    sequence_data=sequence_data,
                    valid_mask_seq=valid_mask_seq,
                    seq_attribute=seq_attribute,
                    index_in_dataset=index_in_dataset,
                    tran_mats_dict=tran_mats_dict,
                    **kwargs,
                )
        else:
            outs_map, losses_map = None, {}

        if self.training:
            losses_obstacle.update(losses_map)
            return (outs_obstacle, outs_map), losses_obstacle, {}
        else:
            return (outs_obstacle, outs_map)

    def base_encoder(
        self,
        points=None,
        img=None,
        radar_points: List[torch.tensor] = None,
    ):
        if self.camera_encoder is not None and img is not None:
            batch_size, num_frame, N, C, H, W = img.shape
            # img = img.flip(3)  # TODO: 为了和 map 对点，最终应当移除
            img_feats, img_feats_map = self.extract_img_feat(img.contiguous())
            # embed()
        else:
            img_feats, img_feats_map = None

        if self.radar_encoder and radar_points is not None:
            radar_output, radar_points = self.extract_radar_feat(radar_points)
            if self.use_radar_init:
                radar_points = self.det_head.dense_head.get_radar_pos_init(radar_points)
        else:
            radar_output, radar_points = None, None

        if self.lidar_encoder and points is not None:
            lidar_feats = self.lidar_encoder(points)
        else:
            lidar_feats = None

        return img_feats, img_feats_map, radar_output, radar_points, lidar_feats

    def extract_img_feat(self, img):
        # Extract img_feats
        if self.with_camera_encoder:
            if self.grid_mask is not None:
                batch_size, num_frame, N, C, H, W = img.shape
                img = img.view(batch_size * num_frame * N, C, H, W)
                img = self.grid_mask(img)
                if self.cfg.get("grid_junc_mask", None):
                    img = self.grid_junc_mask(img)
                img = img.view(batch_size, num_frame, N, C, H, W)
            img_feats, img_feats_map = self.camera_encoder(img, self.train_backbone)
        else:
            img_feats, img_feats_map = None

        return img_feats, img_feats_map
