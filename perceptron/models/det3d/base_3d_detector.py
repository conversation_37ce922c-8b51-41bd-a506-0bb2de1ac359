import torch
import torch.nn as nn
import refile


class Base3DDetector(nn.Module):
    def __init__(self, num_class, class_names):
        super().__init__()
        self.num_class = num_class
        self.class_names = class_names
        self.register_buffer("global_step", torch.LongTensor(1).zero_())

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    def build_networks(self):
        raise NotImplementedError

    def build_vfe(self):
        raise NotImplementedError

    def build_backbone_3d(self):
        raise NotImplementedError

    def build_map_to_bev(self):
        raise NotImplementedError

    def build_backbone_2d(self):
        raise NotImplementedError

    def build_pfe(self):
        raise NotImplementedError

    def build_dense_head(self):
        raise NotImplementedError

    def build_point_head(self):
        raise NotImplementedError

    def build_roi_head(self):
        raise NotImplementedError

    def forward(self, **kwargs):
        raise NotImplementedError

    def post_processing(self):
        raise NotImplementedError

    def load_params_from_file(self, filename, logger, to_cpu=False):
        if not refile.smart_isfile(filename):
            raise FileNotFoundError

        logger.info("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)
        model_state_disk = checkpoint["model_state"]

        if "version" in checkpoint:
            logger.info("==> Checkpoint trained from version: %s" % checkpoint["version"])

        update_model_state = {}
        for key, val in model_state_disk.items():
            if key in self.state_dict() and self.state_dict()[key].shape == model_state_disk[key].shape:
                update_model_state[key] = val
                # logger.info('Update weight %s: %s' % (key, str(val.shape)))

        state_dict = self.state_dict()
        state_dict.update(update_model_state)
        self.load_state_dict(state_dict)

        for key in state_dict:
            if key not in update_model_state:
                logger.info("Not updated weight %s: %s" % (key, str(state_dict[key].shape)))

        logger.info("==> Done (loaded %d/%d)" % (len(update_model_state), len(self.state_dict())))

    def load_params_with_optimizer(self, filename, to_cpu=False, optimizer=None, logger=None):
        if not refile.smart_isfile(filename):
            raise FileNotFoundError

        logger.info("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)
        epoch = checkpoint.get("epoch", -1)
        it = checkpoint.get("it", 0.0)

        self.load_state_dict(checkpoint["model_state"])

        if optimizer is not None:
            if "optimizer_state" in checkpoint and checkpoint["optimizer_state"] is not None:
                logger.info(
                    "==> Loading optimizer parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU")
                )
                optimizer.load_state_dict(checkpoint["optimizer_state"])
            else:
                assert filename[-4] == ".", filename
                src_file, ext = filename[:-4], filename[-3:]
                optimizer_filename = "%s_optim.%s" % (src_file, ext)
                if refile.smart_exists(optimizer_filename):
                    optimizer_ckpt = torch.load(optimizer_filename, map_location=loc_type)
                    optimizer.load_state_dict(optimizer_ckpt["optimizer_state"])

        if "version" in checkpoint:
            print("==> Checkpoint trained from version: %s" % checkpoint["version"])
        logger.info("==> Done")

        return it, epoch
