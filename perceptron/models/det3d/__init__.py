from collections import namedtuple

import numpy as np
import torch


def load_data_to_gpu(batch_dict):
    for key, val in batch_dict.items():
        if key in ["frame_id", "metadata", "calib", "image_shape"]:
            continue
        if key in ["points"]:
            if isinstance(val, list):
                batch_dict[key] = [torch.from_numpy(v).float().cuda() for v in val]
            else:
                batch_dict[key] = [torch.from_numpy(val).float().cuda()]
        if isinstance(val, np.ndarray):
            batch_dict[key] = torch.from_numpy(val).float().cuda()
        elif isinstance(val, torch.Tensor):
            batch_dict[key] = val.float().cuda()
        elif isinstance(val, list) and isinstance(val[0], np.ndarray):
            batch_dict[key] = [torch.from_numpy(v).float().cuda() for v in val]
        else:
            continue


def model_fn_decorator():
    ModelReturn = namedtuple("ModelReturn", ["loss", "tb_dict", "disp_dict"])

    def model_func(model, batch_dict):
        load_data_to_gpu(batch_dict)
        ret_dict, tb_dict, disp_dict = model(batch_dict)

        loss = ret_dict["loss"].mean()
        if hasattr(model, "update_global_step"):
            model.update_global_step()
        else:
            model.module.update_global_step()

        return ModelReturn(loss, tb_dict, disp_dict)

    return model_func
