from torch.nn.utils import clip_grad_norm_
from loguru import logger

from .base_callback import Callback

__all__ = ["ClipGrad"]


class ClipGrad(Callback):
    def __init__(self, max_norm: float):
        self.max_norm = max_norm

    def before_optimize(self, trainer):
        grad_norm = clip_grad_norm_(trainer.model.parameters(), self.max_norm)
        if trainer.global_step % trainer.exp.print_interval == 0:
            logger.info({"grad_norm": grad_norm})
