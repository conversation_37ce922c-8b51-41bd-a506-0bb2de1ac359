import glob
import os
import pickle
import shutil
import subprocess
import time
import numpy as np
import re

import refile
import refile.smart
import torch

from loguru import logger
from threading import Thread
from refile import smart_path_join

from perceptron.engine.executors import Trainer
from .base_callback import Callback, MasterOnlyCallback
from perceptron.utils import torch_dist

try:
    from cvpack2.checkpoint.c2_model_loading import align_and_update_state_dicts
except ImportError:
    # cvpack2 is an optional dependency at the moment
    pass


__all__ = ["CheckPointSaver", "CheckPointLoader", "CheckPointC2Loader", "MultiplePretrainLoader"]


class CheckPointSaver(MasterOnlyCallback):
    def __init__(
        self,
        local_path,
        filename=r"checkpoint_epoch_{epoch}.pth",
        step_filename=r"checkpoint_epoch_{epoch}_step_{step}.pth",
        remote_path=None,
        save_interval: int = 5,
        save_step_interval: int = -1,
        num_keep_latest=None,
        save_setp_interval=1,
        num_step_keep_latest=None,
    ):
        self.local_path = local_path
        self.filename = filename
        self.step_filename = step_filename
        self.remote_path = remote_path
        self.save_interval = save_interval
        self.save_step_interval = save_step_interval
        self.num_step_keep_latest = num_step_keep_latest
        self.num_keep_latest = num_keep_latest
        os.makedirs(local_path, exist_ok=True)

    def _copy_to_oss_background(self, source, dest, update_best_ckpt=False):
        """
        sync local checkpoint to oss in background
        """

        def worker(source, dest, update_best_ckpt=False, cur_best_ckpt_pth=""):
            for retry in range(8):
                try:
                    refile.s3.s3_upload(source, dest)
                    # make a text file contains the oss path
                    with open(source + ".tmp", "w") as f:
                        f.write(dest)
                    shutil.move(source + ".tmp", source)
                    logger.info("finish sync {} -> {}".format(source, dest))
                    if update_best_ckpt:
                        refile.s3.s3_copy(dest, cur_best_ckpt_pth)
                        logger.info("finish sync {} -> {}".format(dest, cur_best_ckpt_pth))
                    return
                except Exception as e:
                    logger.error(e)
                    time.sleep(int(2 ** retry))
                    continue
            logger.error("error copy {} -> {}, after {} retries".format(source, dest, retry))

        cur_best_ckpt_pth = ""
        if update_best_ckpt:
            if refile.smart_exists(self.remote_path):
                all_remote_ckpt_path = refile.s3.s3_listdir(self.remote_path)
                for item in all_remote_ckpt_path:
                    if item.startswith("best"):
                        refile.s3.s3_remove(self.remote_path + item)
                        break
            cur_best_ckpt_pth = smart_path_join(self.remote_path, "best_checkpoint.pth")

        thread = Thread(target=worker, args=(source, dest, update_best_ckpt, cur_best_ckpt_pth), daemon=False)
        thread.start()

    def _make_checkpoint(self, trainer: Trainer):
        model_state = None
        if hasattr(trainer, "ema_model"):
            model = trainer.ema_model.ema
        else:
            model = trainer.model
        if model:
            if isinstance(model, torch.nn.parallel.DistributedDataParallel):
                model_state = model.module.state_dict()
                model_state_cpu = type(model_state)()
                for key, val in model_state.items():
                    model_state_cpu[key] = val.cpu()
                model_state = model_state_cpu
            else:
                model_state = model.state_dict()

        optim_state = trainer.optimizer.state_dict() if trainer.optimizer else None

        callback_states = {}
        for cb in trainer.callbacks:
            if hasattr(cb, "state_dict"):
                cls_name = cb.__class__.__name__
                callback_states[cls_name] = cb.state_dict()

        ckpt = {
            "epoch": trainer.epoch,
            "it": trainer.global_step,
            "global_step": trainer.global_step,
            "epoch_step": trainer.step,
            "model_state": model_state,
            "optimizer_state": optim_state,
            # "lr_scheduelr": trainer.lr_scheduler.state_dict(),
            "lr_scheduler": trainer.lr_scheduler.state_dict(),
            "callback": callback_states,
            "iter_base": trainer.iter_base,
        }

        # save grad_scaler
        if hasattr(trainer, "grad_scaler"):
            ckpt["grad_scaler_state"] = trainer.grad_scaler.state_dict()

        return ckpt

    def after_epoch(self, trainer: Trainer, epoch: int, update_best_ckpt: bool = False):
        if (epoch + 1) % self.save_interval != 0:
            return
        filename = self.filename.format(epoch=epoch)
        save_path = smart_path_join(self.local_path, filename)
        torch.save(self._make_checkpoint(trainer), save_path)
        if self.remote_path:
            remote_path = smart_path_join(self.remote_path, filename)
            self._copy_to_oss_background(save_path, remote_path, update_best_ckpt)
        self._remove_out_of_date_ckpt()

    def after_step(self, trainer: Trainer, data_dict, *args, **kwargs):
        global_step = kwargs.get("global_step", -1)
        total_step = kwargs.get("total_step", -1)
        update_best_ckpt = kwargs.get("update_best_ckpt", False)

        if global_step == -1 or (global_step + 1) % self.save_step_interval != 0 or total_step == -1:
            return
        epoch = kwargs["epoch"]
        filename = self.step_filename.format(epoch=epoch, step=global_step)
        save_path = smart_path_join(self.local_path, filename)
        torch.save(self._make_checkpoint(trainer), save_path)
        if self.remote_path:
            remote_path = smart_path_join(self.remote_path, filename)
            self._copy_to_oss_background(save_path, remote_path, update_best_ckpt)
        self._remove_out_of_date_ckpt_by_setp()

    def _remove_out_of_date_ckpt(self):
        if not self.num_keep_latest:
            return

        ckpt_list = glob.glob(smart_path_join(self.local_path, self.filename.format(epoch="*")))
        ckpt_list.sort(key=os.path.getmtime)
        if len(ckpt_list) > self.num_keep_latest:
            for cur_file_idx in range(0, len(ckpt_list) - self.num_keep_latest):
                os.remove(ckpt_list[cur_file_idx])

    def _remove_out_of_date_ckpt_by_setp(self):
        if not self.num_step_keep_latest:
            return

        ckpt_list = glob.glob(smart_path_join(self.local_path, self.step_filename.format(epoch="*", step="*")))
        ckpt_list.sort(key=os.path.getmtime)
        if len(ckpt_list) > self.num_step_keep_latest:
            for cur_file_idx in range(0, len(ckpt_list) - self.num_step_keep_latest):
                os.remove(ckpt_list[cur_file_idx])


class CheckPointLoader(Callback):
    def __init__(
        self,
        path,
        weight_only=False,
        loggera=None,
    ):
        self.path = self._resolve_path(path)
        self.weight_only = weight_only
        self.logger = loggera if loggera else logger

    @staticmethod
    def _resolve_path(path):
        if refile.is_s3(path):
            return path

        path = os.path.realpath(path)
        if refile.smart_exists(path):
            output = subprocess.check_output(["file", "--mime-type", path, "-F", "@"])
            mime_type = output.decode().strip().split("@")[-1].strip()

            # text file contains an oss path
            if mime_type.startswith("text"):
                with open(path) as f:
                    path = f.read().strip()
        else:
            raise ValueError(f"{path} is not exists!!")
        return path

    def load_checkpoint(self, trainer: Trainer):
        self.logger.info(f"Loading parameters from checkpoint {self.path}")
        logger.info(f"Loading parameters from checkpoint {self.path}")
        with refile.smart_open(self.path, "rb") as f:
            checkpoint = torch.load(f, map_location=torch.device("cpu"), weights_only=False)

        # TODO bulid model finetune callback
        model_state_dict = trainer.model.state_dict()
        if "model_state" in checkpoint:
            checkpoint_state_dict = checkpoint["model_state"]
        elif "state_dict" in checkpoint:
            checkpoint_state_dict = checkpoint["state_dict"]
        else:
            checkpoint_state_dict = checkpoint

        for k in list(model_state_dict.keys()):
            if k not in checkpoint_state_dict:
                self.logger.info(f"{k} not in checkpoint_state_dict")
                logger.info(f"{k} not in checkpoint_state_dict")

        for k in list(checkpoint_state_dict.keys()):
            if k in model_state_dict:
                shape_model = tuple(model_state_dict[k].shape)
                shape_checkpoint = tuple(checkpoint_state_dict[k].shape)
                if shape_model != shape_checkpoint:
                    self.logger.info(
                        "'{}' has shape {} in the checkpoint but {} in the "
                        "model! Skipped.".format(k, shape_checkpoint, shape_model)
                    )
                    checkpoint_state_dict.pop(k)
        trainer.model.load_state_dict(checkpoint_state_dict, strict=False)

        if self.weight_only:
            return

        trainer.epoch = checkpoint.get("epoch", -1) + 1
        trainer.global_step = checkpoint.get("global_step", -1) + 1
        if checkpoint.get("epoch_step", -1) != -1 and checkpoint.get("iter_base", False):
            trainer.step_from_ckpt = checkpoint.get("epoch_step", -1) + 1
            trainer.epoch = checkpoint.get("epoch", -1)
            print(f"trainer.step_from_ckpt:{trainer.step_from_ckpt}, trainer.epoch:{trainer.epoch}")
            logger.info("Please note the epoch value for restarting !")
        trainer.optimizer.load_state_dict(checkpoint["optimizer_state"])
        # trainer.lr_scheduler.load_state_dict(checkpoint.get("lr_scheduler", {}))
        if "lr_scheduler" in checkpoint:  # 遗留问题，之前打错单词导致 bug
            trainer.lr_scheduler.load_state_dict(checkpoint.get("lr_scheduler", {}))
        else:
            trainer.lr_scheduler.load_state_dict(checkpoint.get("lr_scheduelr", {}))
        if not hasattr(trainer.lr_scheduler, "load_from_dict") or not trainer.lr_scheduler.load_from_dict:
            trainer.lr_scheduler.load_state_dict(checkpoint["optimizer_state"]["param_groups"][0]["lr"])
        if "lr_scheduelr" in checkpoint:
            if not checkpoint["lr_scheduelr"]:
                self.logger.info(
                    "!!!!!!!!!xewdegfyewugchdjscgdkjgvcfdvfdj", checkpoint["optimizer_state"]["param_groups"][0]["lr"]
                )
                trainer.lr_scheduler.load_state_dict(checkpoint["optimizer_state"]["param_groups"][0]["lr"])

        trainer.lr_scheduler.step(trainer.global_step - 1)
        # resume callback
        for cb in trainer.callbacks:
            if hasattr(cb, "state_dict"):
                cls_name = cb.__class__.__name__
                if cls_name in checkpoint["callback"]:
                    cb.load_state_dict(checkpoint["callback"][cls_name])
        # resume grad_scaler
        if hasattr(trainer, "grad_scaler") and "grad_scaler_state" in checkpoint:
            trainer.grad_scaler.load_state_dict(checkpoint["grad_scaler_state"])

        logger.info(
            f"resume from {self.path}, current epoch: {trainer.epoch}, current global_step: {trainer.global_step}"
        )
        self.logger.info(
            f"resume from {self.path}, current epoch: {trainer.epoch}, current global_step: {trainer.global_step}"
        )

    @classmethod
    def find_lastest_checkpoint_from_dump_model(self, dump_model_dir: str):
        ckpt_list = glob.glob(smart_path_join(dump_model_dir, "checkpoint_epoch_*.pth"))
        ckpt_list.sort(key=os.path.getmtime)
        return ckpt_list[-1] if ckpt_list else None


class CheckPointC2Loader(CheckPointLoader):
    def load_checkpoint(self, trainer: Trainer):
        if not self.path.endswith(".pkl"):
            return super().load_checkpoint(trainer)

        logger.info(f"Loading parameters from checkpoint {self.path}")
        checkpoint = self._reverse_c2_model(trainer)

        trainer.model.load_state_dict(checkpoint["model_state"])
        if self.weight_only:
            return

    def _convert_ndarray_to_tensor(self, state_dict: dict):
        """
        In-place convert all numpy arrays in the state_dict to torch tensor.
        Args:
            state_dict (dict): a state-dict to be loaded to the model.
        """
        for k in list(state_dict.keys()):
            if "weight_order" in k:
                continue
            v = state_dict[k]
            if not isinstance(v, np.ndarray) and not isinstance(v, torch.Tensor):
                raise ValueError("Unsupported type found in checkpoint! {}: {}".format(k, type(v)))
            if not isinstance(v, torch.Tensor):
                state_dict[k] = torch.from_numpy(v)

    # in order to convert caffe model to C2
    # just use in pretrained model
    def _reverse_c2_model(self, trainer: Trainer):
        with refile.smart_open(self.path, "rb") as f:
            data = pickle.load(f, encoding="latin1")

        self._convert_ndarray_to_tensor(data)
        model_state_dict = trainer.model.state_dict()
        align_and_update_state_dicts(
            model_state_dict,
            data,
            c2_conversion=True,
        )
        checkpoint = {}
        checkpoint["model_state"] = model_state_dict
        return checkpoint


class MultiplePretrainLoader(CheckPointLoader):
    def __init__(
        self,
        param_config,
        weight_only=True,
    ):
        self.param_config = {k: self._resolve_path(v) for k, v in param_config.items()}
        self.weight_only = weight_only

    def load_checkpoint(self, trainer: Trainer):
        for key, key_path in self.param_config.items():
            logger.info(f"Loading {key} parameters from checkpoint {key_path}")
            with refile.smart_open(key_path, "rb") as f:
                checkpoint = torch.load(f, map_location=torch.device("cpu"), weights_only=False)  # NOTE: weights_only

            model_state_dict = trainer.model.state_dict()
            checkpoint_state_dict = {
                param_key: param for param_key, param in checkpoint["model_state"].items() if key in param_key
            }

            for k in list(model_state_dict.keys()):
                if k not in checkpoint_state_dict:
                    logger.info(f"{k} not in checkpoint_state_dict")
            for k in list(checkpoint_state_dict.keys()):
                if k in model_state_dict:
                    shape_model = tuple(model_state_dict[k].shape)
                    shape_checkpoint = tuple(checkpoint_state_dict[k].shape)
                    if shape_model != shape_checkpoint:
                        logger.info(
                            "'{}' has shape {} in the checkpoint but {} in the "
                            "model! Skipped.".format(k, shape_checkpoint, shape_model)
                        )
                        checkpoint_state_dict.pop(k)
            trainer.model.load_state_dict(checkpoint_state_dict, strict=False)


class MultiCheckPointLoader(CheckPointLoader):
    supported_keys = ["od", "occ", "god", "lidar_only", "map"]

    def __init__(
        self,
        ckpt_path_dict,
        weight_only=True,
        save_new_ckpt_path=None,
    ):
        assert set(ckpt_path_dict.keys()).issubset(
            self.supported_keys
        ), f"Supported keys are {self.supported_keys}, but got {ckpt_path_dict.keys()}, please check --ckpt"

        self.ckpt_path_dict = {k: self._resolve_path(v) for k, v in ckpt_path_dict.items()}
        self.weight_only = weight_only
        assert self.weight_only, "MultiCheckPointLoader only support weight_only=True"
        self.save_new_ckpt_path = save_new_ckpt_path

    def load_checkpoint_state_dict(self, path):
        with refile.smart_open(path, "rb") as f:
            checkpoint = torch.load(f, map_location=torch.device("cpu"), weights_only=False)
            if "model_state" in checkpoint:
                checkpoint_state_dict = checkpoint["model_state"]
            elif "state_dict" in checkpoint:
                checkpoint_state_dict = checkpoint["state_dict"]
            else:
                checkpoint_state_dict = checkpoint
        return checkpoint_state_dict

    @staticmethod
    def replace_param_key(state_dict, replace_pattern_list):
        new_state_dict = {}

        for param_key in list(state_dict.keys()):
            save_flag = False
            for pattern, replacement in replace_pattern_list:
                if pattern in param_key:
                    new_param_key = param_key.replace(pattern, replacement)
                    # logger.info(f"Replacing {param_key} with {new_param_key}")
                    new_state_dict[new_param_key] = state_dict[param_key]
                    save_flag = True
                    break
            if not save_flag:
                new_state_dict[param_key] = state_dict[param_key]
        return new_state_dict

    def check_and_merge_shared_params(
        self, checkpoint_state_dict_main, checkpoint_state_dict_to_merge, model_state_dict, to_merge_name=None
    ):
        """合并两个检查点的共享参数，以checkpoint_state_dict_main为主，checkpoint_state_dict_to_merge为辅。

        1. 如果checkpoint_state_dict_to_merge中的参数在checkpoint_state_dict_main中不存在，则直接添加。
        2. 如果两者都有该参数，则检查形状是否一致，如果不一致则跳过, 并且报错。
        3. 如果形状一致，则检查值是否相同，如果不同则使用checkpoint_state_dict_main中的值，并且报错。

        Args:
            checkpoint_state_dict_main (dict): 主检查点的状态字典
            checkpoint_state_dict_to_merge (dict): 要合并的检查点的状态字典
            model_state_dict (dict): 模型的状态字典
        """
        merged_checkpoint_state_dict = {k: v for k, v in checkpoint_state_dict_to_merge.items()}

        for param_key in checkpoint_state_dict_main.keys():
            if param_key not in model_state_dict:
                logger.warning(f"Parameter {param_key} not found in model state dict. Skipped.")
            elif param_key not in merged_checkpoint_state_dict:
                merged_checkpoint_state_dict[param_key] = checkpoint_state_dict_main[param_key]
            else:
                shape1 = tuple(checkpoint_state_dict_main[param_key].shape)
                shape2 = tuple(merged_checkpoint_state_dict[param_key].shape)
                if shape1 != shape2:
                    logger.warning(
                        f"current and {to_merge_name}: Parameter {param_key} has different shapes in the two checkpoints: "
                        f"{shape1} vs {shape2}. Skipped."
                    )
                else:
                    param_value1 = checkpoint_state_dict_main[param_key]
                    param_value2 = merged_checkpoint_state_dict[param_key]
                    if not torch.equal(param_value1, param_value2):
                        if not any(
                            [
                                param_key.endswith(suffix)
                                for suffix in [".num_batches_tracked", ".running_mean", ".running_var"]
                            ]
                        ):
                            # Skip these parameters
                            logger.warning(
                                f"current and {to_merge_name}:  Parameter {param_key} has different values in the two checkpoints. {param_value1.flatten()[:min(10, param_value1.numel())]} vs {param_value2.flatten()[:min(10, param_value1.numel())]}. "
                                "Using checkpoint 1 value."
                            )
                        else:
                            logger.warning(
                                f"current and {to_merge_name}: Parameter {param_key} has different bn values"
                            )
                merged_checkpoint_state_dict[param_key] = checkpoint_state_dict_main[param_key]

        return merged_checkpoint_state_dict

    def load_checkpoint(self, trainer: Trainer):
        logger.info(f"Loading parameters from checkpoint {self.ckpt_path_dict}")
        img_neck_tasks = set()
        model_state_dict = trainer.model.state_dict()
        # get all img neck list, using re '.img_neck*?.'
        pattern = r"\.img_neck_(.*?)\."
        for key in model_state_dict.keys():
            match = re.findall(pattern, key)
            if match:
                assert len(match) == 1, f"Multiple img_neck tasks found in {key}: {match}"
                task_name = match[0]
                img_neck_tasks.add(task_name)

        checkpoint_state_task_dicts = {
            task: self.load_checkpoint_state_dict(self.ckpt_path_dict[task]) for task in self.ckpt_path_dict.keys()
        }

        checkpoint_state_dict = {}
        if len(checkpoint_state_task_dicts) == 1:
            checkpoint_state_dict = list(checkpoint_state_task_dicts.values())[0]
        else:
            # 更新img_neck参数
            for task, checkpoint_state_dict_one_task in checkpoint_state_task_dicts.items():
                if task in img_neck_tasks:
                    # replace img_neck_occ, img_neck_other, img_neck_map to img_neck
                    checkpoint_state_task_dicts[task] = self.replace_param_key(
                        checkpoint_state_dict_one_task, [(".img_neck.", f".img_neck_{task}.")]
                    )
            # lidar only head重名了
            if "lidar_only" in self.ckpt_path_dict:
                checkpoint_state_task_dicts["lidar_only"] = self.replace_param_key(
                    checkpoint_state_task_dicts["lidar_only"], [("det_head.", "lidar_only_head.")]
                )

            # if "god" in self.ckpt_path_dict:
            #     god_ckpt = checkpoint_state_task_dicts["god"]
            #     # 移除可能多余的det_head参数
            #     god_ckpt = {k: v for k, v in god_ckpt.items() if ("det_head." not in k) or ("god_head.det_head." in k)}
            #     checkpoint_state_task_dicts["god"] = god_ckpt
            for task in self.supported_keys:
                if task in checkpoint_state_task_dicts:
                    checkpoint_state_dict_to_merge = checkpoint_state_task_dicts[task]
                    checkpoint_state_dict = self.check_and_merge_shared_params(
                        checkpoint_state_dict, checkpoint_state_dict_to_merge, model_state_dict, to_merge_name=task
                    )

        for k in list(model_state_dict.keys()):
            if k not in checkpoint_state_dict:
                logger.info(f"{k} not in checkpoint_state_dict")

        for k in list(checkpoint_state_dict.keys()):
            if k in model_state_dict:
                shape_model = tuple(model_state_dict[k].shape)
                shape_checkpoint = tuple(checkpoint_state_dict[k].shape)
                if shape_model != shape_checkpoint:
                    logger.info(
                        "'{}' has shape {} in the checkpoint but {} in the "
                        "model! Skipped.".format(k, shape_checkpoint, shape_model)
                    )
                    checkpoint_state_dict.pop(k)
        if self.save_new_ckpt_path:
            logger.info(f"Saving merged checkpoint to {self.save_new_ckpt_path}")
            new_checkpoint = {
                "model_state": checkpoint_state_dict,
            }

            if torch_dist.get_rank() == 0:
                torch.save(new_checkpoint, self.save_new_ckpt_path)
            exit()
        trainer.model.load_state_dict(checkpoint_state_dict, strict=False)
