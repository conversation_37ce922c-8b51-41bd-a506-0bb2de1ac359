import os
import cv2
from loguru import logger

from .base_callback import Callback
from perceptron.engine.executors import Trainer

# 可视化函数
from perceptron.utils.map_utils.vistools.map_vis_cb import Visual_Map_Data_Callback
from perceptron.utils.map_utils.vistools.map_vis_cb_maptr import Visual_Map_Data_Callback_Maptr

__all__ = ["Visualize"]


class Visualize(Callback):
    def __init__(self, local_path, vis_interval, vis_interval_maptr):
        self.local_path = local_path
        self.vis_interval = vis_interval
        self.vis_interval_maptr = vis_interval_maptr
        os.makedirs(local_path, exist_ok=True)

    def after_grad(self, trainer: Trainer, global_step, data_dict, pred_dicts_det, *args, **kwargs):
        # if global_step % self.vis_interval == 0 and torch_dist.get_rank() == 0:
        if self.vis_interval > 0:
            if global_step % self.vis_interval == 0:
                logger.info("Visualizing...")
                pred_maps = pred_dicts_det[1]  # fix: batched pred
                my_vis = Visual_Map_Data_Callback()
                vis_img_list, index_in_dataset_list = my_vis.vis_train_data(data_dict, pred_maps)
                logger.info("totally {} frames, idx: {}".format(len(vis_img_list), index_in_dataset_list))
                for vis_img, index_in_dataset in zip(vis_img_list, index_in_dataset_list):
                    cv2.imwrite(
                        os.path.join(
                            self.local_path, "step_{:0>7d}_idx_{:d}.jpg".format(global_step, index_in_dataset[-1])
                        ),
                        vis_img,
                    )
                    # cv2.imwrite(os.path.join(self.local_path, "step_{:0>7d}_idx_{:d}_{:d}.jpg".format(global_step, index_in_dataset[-1], torch_dist.get_rank())), vis_img)
        if self.vis_interval_maptr > 0:
            if global_step % self.vis_interval_maptr == 0:
                logger.info("Visualizing...")
                pred_maps = pred_dicts_det[1]  # fix: batched pred
                my_vis_maptr = Visual_Map_Data_Callback_Maptr()
                vis_img_list, index_in_dataset_list = my_vis_maptr.vis_train_data(data_dict, pred_maps)
                logger.info("totally {} frames, idx: {}".format(len(vis_img_list), index_in_dataset_list))
                for vis_img, index_in_dataset in zip(vis_img_list, index_in_dataset_list):
                    cv2.imwrite(
                        os.path.join(
                            self.local_path, "step_{:0>7d}_idx_{:d}.jpg".format(global_step, index_in_dataset[-1])
                        ),
                        vis_img,
                    )
                    # cv2.imwrite(os.path.join(self.local_path, "step_{:0>7d}_idx_{:d}_{:d}.jpg".format(global_step, index_in_dataset[-1], torch_dist.get_rank())), vis_img)
        return
