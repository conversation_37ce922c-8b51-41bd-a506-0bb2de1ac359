import sys

from perceptron.engine.callbacks import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EvalResultsSaver
from perceptron.engine.executors import MultiEvaluator
from perceptron.utils.log_utils import setup_logger
from perceptron.utils.misc import PyDecorator

from .base_cli import BaseCli

__all__ = ["MultiCli"]


class MultiCli(BaseCli):
    @PyDecorator.overrides(BaseCli)
    def get_evaluator(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None:
            sys.exit("No checkpoint is specified for evaluation")
        output_dir = self._get_exp_output_dir()
        exp.output_dir = output_dir
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename="eval.log")
        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [self.env, CheckPointLoader(self.args.ckpt), EvalResultsSaver(exp.output_dir)]

        evaluator = MultiEvaluator(exp=exp, callbacks=callbacks, logger=logger)
        return evaluator
