import sys
import warnings

from perceptron.engine.callbacks import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EvalResultsSaver
from perceptron.engine.executors.evaluators import FreespaceEvaluator
from perceptron.utils.log_utils import setup_logger
from perceptron.utils.misc import PyDecorator
import refile

from .base_cli import BaseCli

__all__ = ["FreespaceCli"]


class FreespaceCli(BaseCli):
    @PyDecorator.overrides(BaseCli)
    def get_evaluator(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None and self.args.eval:
            sys.exit("No checkpoint is specified for evaluation")
        if not hasattr(self.exp, "output_dir"):
            output_dir = self._get_exp_output_dir()
            exp.output_dir = output_dir
        if callbacks is None:
            callbacks = [self.env, CheckPointLoader(self.args.ckpt, weight_only=True), EvalResultsSaver(exp.output_dir)]

        if self.args.eval:
            exp.ckpt_path = self.args.ckpt
            epoch = exp.ckpt_path.split("checkpoint_")[-1][: -len(".pth")]
            exp.output_dir = refile.smart_path_join(exp.output_dir, epoch)
            logger = setup_logger(exp.output_dir, distributed_rank=self.env.global_rank(), filename="eval.log")
            self._set_basic_log_message(logger)
            evaluator = FreespaceEvaluator(exp=exp, callbacks=callbacks, logger=logger)
        elif self.args.train_and_eval:
            logger = setup_logger(exp.output_dir, distributed_rank=self.env.global_rank(), filename="eval.log")
            self._set_basic_log_message(logger)

            eval_interval = -1 if exp.eval_interval is None else exp.eval_interval
            evaluator = FreespaceEvaluator(exp=exp, callbacks=callbacks, logger=logger, eval_interval=eval_interval)
        else:
            raise NotImplementedError("Train Mode has no evaluator!")
        return evaluator

    @PyDecorator.overrides(BaseCli)
    def get_infer(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None:
            warnings.warn("No checkpoint is specified for inference")

        if not hasattr(self.exp, "output_dir"):
            output_dir = self._get_exp_output_dir()
            exp.output_dir = output_dir
        exp.ckpt_path = self.args.ckpt
        logger = setup_logger(exp.output_dir, distributed_rank=self.env.global_rank(), filename="infer.log")
        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [
                self.env,
                CheckPointLoader(self.args.ckpt, weight_only=True),
            ]
        infer = FreespaceEvaluator(exp=exp, callbacks=callbacks, logger=logger)
        return infer

    def executor(self):
        if self.args.eval:
            self.get_evaluator().eval_and_dump()
        elif self.args.train_and_eval:
            evaluator = self.get_evaluator(callbacks=[EvalResultsSaver(self.exp.output_dir)])
            self.get_trainer(evaluator=evaluator).train()
        elif self.args.export:
            self.get_exports().export()
        elif self.args.infer:
            self.get_infer().infer()
        else:
            self.get_trainer().train()
