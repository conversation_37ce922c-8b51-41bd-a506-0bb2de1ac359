import sys
import argparse

from perceptron.engine.callbacks import (
    <PERSON><PERSON>oint<PERSON>oa<PERSON>,
    EvalResultsSaver,
    ClearMLCallback,
)
from perceptron.engine.callbacks.checkpoint_callback import MultiCheckPointLoader
from perceptron.engine.executors import OneModelEvaluator
from perceptron.utils.log_utils import setup_logger
from perceptron.utils.misc import PyDecorator

from .det3d_cli import Det3DCli
from perceptron.engine.executors.inference.onemodel_inference import OneModel_Infer

__all__ = ["OneModelCli"]


class OneModelCli(Det3DCli):
    @PyDecorator.overrides(Det3DCli)
    def get_evaluator(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None and self.args.eval:
            sys.exit("No checkpoint is specified for evaluation")
        output_dir = self._get_exp_output_dir(training=False)
        exp.output_dir = output_dir
        logger_file_name = (
            "eval_" + self.args.ckpt.split("/")[-1].replace("checkpoint_", "").replace(".pth", "") + ".log"
        )
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename=logger_file_name)

        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [
                self.env,
                CheckPointLoader(self.args.ckpt, weight_only=True)
                if self.args.ckpt_n is None
                else MultiCheckPointLoader(self.args.ckpt_n, weight_only=True),
                EvalResultsSaver(exp.output_dir),
            ]
        if self.args.clearml:
            callbacks.append(ClearMLCallback())

        evaluator = OneModelEvaluator(
            exp=exp, callbacks=callbacks, logger=logger, cli_args=self.args, output_dir=output_dir
        )
        return evaluator

    @PyDecorator.overrides(Det3DCli)
    def get_infer(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None and self.args.eval:
            sys.exit("No checkpoint is specified for evaluation")
        output_dir = self._get_exp_output_dir()
        exp.output_dir = output_dir
        logger_file_name = (
            "eval_" + self.args.ckpt.split("/")[-1].replace("checkpoint_", "").replace(".pth", "") + ".log"
        )
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename=logger_file_name)

        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [
                self.env,
                CheckPointLoader(self.args.ckpt, weight_only=True)
                if self.args.ckpt_n is None
                else MultiCheckPointLoader(self.args.ckpt_n, weight_only=True),
                EvalResultsSaver(exp.output_dir),
            ]

        if self.args.infer:
            infer = OneModel_Infer(exp=exp, callbacks=callbacks, logger=logger, cli_args=self.args)
        else:
            raise NotImplementedError("Train Mode has no evaluator!")
        return infer

    def add_argparse_args(self, parser: argparse.ArgumentParser):
        parser = super(OneModelCli, self).add_argparse_args(parser)
        parser.add_argument("--eval_od", dest="eval_od", action="store_true", help="conduct evaluation od")
        parser.add_argument("--eval_god", dest="eval_god", action="store_true", help="conduct evaluation god")
        parser.add_argument(
            "--eval_lidar_only", dest="eval_lidar_only", action="store_true", help="conduct evaluation lidar only"
        )
        return parser

    def executor(self):
        if (
            self.args.eval
            or self.args.eval_od
            or self.args.eval_map
            or self.args.eval_occ
            or self.args.eval_god
            or self.args.eval_lidar_only
        ):
            self.get_evaluator().eval()
        elif self.args.train_and_eval:
            evaluator = self.get_evaluator(callbacks=[])
            self.get_trainer(evaluator=evaluator).train()
        elif self.args.export:
            self.get_exports().export()
        elif self.args.infer:
            self.get_infer().infer()
        else:
            self.get_trainer().train()
