import sys

from .base_cli import Base<PERSON>li
from perceptron.utils.misc import PyDecorator
from perceptron.utils.log_utils import setup_logger
from perceptron.engine.callbacks import CheckPointLoader
from perceptron.engine.executors import Seg2DEvaluator

__all__ = ["SegCli"]


class SegCli(BaseCli):
    @PyDecorator.overrides(BaseCli)
    def get_evaluator(self, callbacks=None):
        exp = self.exp
        if self.args.ckpt is None:
            sys.exit("No checkpoint is specified for evaluation")
        output_dir = self._get_exp_output_dir()
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename="eval.log")
        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [
                self.env,
                CheckPointLoader(self.args.ckpt),
            ]

        evaluator = Seg2DEvaluator(exp=exp, callbacks=callbacks, logger=logger)
        return evaluator
