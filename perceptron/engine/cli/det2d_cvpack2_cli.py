import os
from perceptron.utils.misc import PyDecorator

from .base_cli import BaseCli
from perceptron.engine.callbacks import (
    CheckPointC2Loader,
    CheckPointLoader,
    CheckPointSaver,
    ClearMLCallback,
    ProgressBar,
    TensorBoardMonitor,
    TextMonitor,
    ClipGrad,
)
from perceptron.engine.executors import Trainer
from perceptron.utils.log_utils import setup_logger

__all__ = ["Det2dCvpack2Cli"]


class Det2dCvpack2Cli(BaseCli):
    def get_trainer(self, callbacks=None, evaluator=None):
        args = self.args
        exp = self.exp
        output_dir = self._get_exp_output_dir()

        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename="train.log")
        self._set_basic_log_message(logger)

        if callbacks is None:
            callbacks = [
                self.env,
                ProgressBar(logger=logger),
                TextMonitor(interval=exp.print_interval),
                TensorBoardMonitor(os.path.join(output_dir, "tensorboard"), interval=exp.print_interval),
                CheckPointSaver(
                    local_path=os.path.join(output_dir, "dump_model"),
                    save_interval=exp.dump_interval,
                    num_keep_latest=exp.num_keep_latest_ckpt,
                ),
            ]
        if "grad_clip_value" in exp.__dict__:
            callbacks.append(ClipGrad(exp.grad_clip_value))
        if args.clearml:
            callbacks.append(ClearMLCallback())
        if args.ckpt:
            callbacks.append(CheckPointLoader(args.ckpt))
        if args.pretrained_model:
            callbacks.append(CheckPointC2Loader(args.pretrained_model, weight_only=True))
        callbacks.extend(exp.callbacks)

        trainer = Trainer(exp=exp, callbacks=callbacks, logger=logger, use_amp=args.use_amp, evaluator=evaluator)
        return trainer

    @PyDecorator.overrides(BaseCli)
    def get_evaluator(self, callbacks=None):
        exp = self.exp
        output_dir = self._get_exp_output_dir()
        exp.output_dir = output_dir
        logger = setup_logger(output_dir, distributed_rank=self.env.global_rank(), filename="eval.log")
        self._set_basic_log_message(logger)
        if callbacks is None:
            callbacks = [self.env, CheckPointLoader(self.args.ckpt, weight_only=True)]
        evaluator = exp.eval_executor_class(exp=exp, callbacks=callbacks, logger=logger)
        return evaluator
