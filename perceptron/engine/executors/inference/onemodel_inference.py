import time
import refile
import torch
from tqdm import tqdm
from typing import Sequence
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist
from ..base_executor import BaseExecutor
from perceptron.data.det3d.modules.evaluation import EvaluationBase
from perceptron.data.det3d.modules.evaluation_occ import EvaluationOcc

from mmdet.core import encode_mask_results
import os
from typing import List


__all__ = ["OneModel_Infer"]


class OneModel_Infer(BaseExecutor):
    def __init__(
        self, exp: BaseExp, callbacks: Sequence[Callback], logger=None, eval_interval: int = -1, cli_args=None
    ):
        super().__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval
        self.exp = exp
        self.callbacks = callbacks
        self.logger = logger
        self.cli_args = cli_args
        self.tasks: List = exp.supported_tasks
        self.stored_tasks = ["occ", "god", "e2e"]

    def inference(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()
        torch.set_grad_enabled(False)

        results_task_dict = {f"pred_{stored_task}": [] for stored_task in self.stored_tasks}  #
        dataset = self.val_dataloader.dataset
        class_names = exp.model.module.class_names
        for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
            data = next(self.val_iter)
            with torch.no_grad():
                if hasattr(dataset, "batch_postcollate_fn"):
                    dataset.batch_postcollate_fn(data)
                if hasattr(dataset, "batch_preforward_fn"):
                    dataset.batch_preforward_fn(data)
                result = exp.infer_step(data)  # all tasks output

                if isinstance(result, dict):
                    for stored_task in self.stored_tasks:
                        if f"{stored_task}_pred" in result:
                            if stored_task == "occ":
                                pred_occ = result["occ_pred"]
                                pred_new_occ = postprocess_occ(pred_occ, data, class_names)
                                results_task_dict["pred_occ"].extend(pred_new_occ)
                            if stored_task == "god":
                                pred_god = result["god_pred"]
                                pred_new_god = postprocess_god(pred_god, data, exp)
                                results_task_dict["pred_god"].extend(pred_new_god)
                            if stored_task == "e2e":
                                pred_e2e = result["e2e_pred"]
                                pred_new_e2e = postprocess_e2e(pred_e2e)
                                results_task_dict["pred_e2e"].extend(pred_new_e2e)
                else:
                    raise NotImplementedError

                self._invoke_callback("after_step", step, {})

        torch_dist.synchronize()
        # 多GPU收集  先同样处理
        det_annos = {}

        for stored_task in self.stored_tasks:
            cur_result = results_task_dict[f"pred_{stored_task}"]
            preds = torch_dist.rank0_gather_object_chunked(obj_list=cur_result, chunk_size=100, show_progress=True)
            if preds is not None:
                preds = sum(map(list, zip(*preds)), [])[: len(self.val_dataloader.dataset)]
            det_annos[f"pred_{stored_task}"] = preds
        torch_dist.synchronize()
        # process for pkl reformat()
        if local_rank == 0:
            eval_cfg = exp.data_val_cfg.eval_cfg
            kwargs = {}
            kwargs["jsonfile_prefix"] = refile.smart_path_join(
                exp.output_dir,
                refile.SmartPath(self.exp.cfg_path).stem + "_" + "_".join(eval_cfg.eval_ppl),
                time.ctime().replace(" ", "_").replace(":", "_"),
            )
            # add occ range
            kwargs["occ_range"] = exp.model_cfg.freespace_head.pc_range
            # reformat and dump
            results = self.val_dataloader.dataset.inference(det_annos, class_names, **kwargs)
        self._invoke_callback("after_eval", det_annos=results)

    def run_task(self, task):
        self.exp.task_flag = task
        old_exp_output_dir = self.exp.output_dir
        self.exp.output_dir = os.path.join(self.exp.output_dir, task)
        self.logger.info(f"\n\n==> Inference of task: {task}, saving to {self.exp.output_dir}\n")
        self.inference()
        self.logger.info("\n\n==> One Task Ending\n")
        self.exp.output_dir = old_exp_output_dir

    def infer(self):
        for task in self.tasks:
            model = (
                self.exp.model.module
                if isinstance(self.exp.model, torch.nn.parallel.DistributedDataParallel)
                else self.exp.model
            )
            if task in ["box", "e2e"] and (self.cli_args.eval_od or self.cli_args.infer):
                model.class_names = self.exp.od_class_names
            elif task == "occ" and (self.cli_args.eval_occ or self.cli_args.infer):
                model.class_names = self.exp.occ_class_names
            elif task == "map" and (self.cli_args.eval_map or self.cli_args.infer):
                model.class_names = self.exp.map_class_names
            elif task == "god" and (self.cli_args.eval_god or self.cli_args.infer):
                model.class_names = self.exp.god_class_names
            else:
                raise NotImplementedError(f"Task {task} is not supported in OneModelInfer.")

            self.run_task(task)


def postprocess_occ(pred_occ, data, class_names):
    if isinstance(pred_occ, dict) and "pred_dicts" in pred_occ:
        pred_dicts = pred_occ["pred_dicts"]
        pred_new_occ = EvaluationOcc.generate_prediction_dicts(
            data,
            pred_dicts,
            class_names,  # 未用到
        )  # frame_id
    elif isinstance(pred_occ, list):
        pred_new_occ = pred_occ
    else:
        raise NotImplementedError

    return pred_new_occ


def postprocess_god(pred_god, data, exp):
    if isinstance(pred_god, dict) and "pred_dicts" in pred_god:
        pred_dicts = pred_god["pred_dicts"]
        pred_new_god = EvaluationBase.generate_prediction_dicts(data, pred_dicts, exp.model.module.god_head.class_names)
    elif isinstance(pred_god, list):
        pred_new_god = pred_god
    else:
        raise NotImplementedError

    return pred_new_god


def postprocess_e2e(pred_e2e):
    if isinstance(pred_e2e, dict):
        pred_e2e = pred_e2e["pred_dicts"]
    if not isinstance(pred_e2e, dict) and isinstance(pred_e2e[0], tuple):
        pred_e2e = [(bbox_results, encode_mask_results(mask_results)) for bbox_results, mask_results in pred_e2e]
    # This logic is only used in panoptic segmentation test.
    elif isinstance(pred_e2e[0], dict) and "ins_results" in pred_e2e[0]:
        for j in range(len(pred_e2e)):
            bbox_results, mask_results = pred_e2e[j]["ins_results"]
            pred_e2e[j]["ins_results"] = (
                bbox_results,
                encode_mask_results(mask_results),
            )
    pred_new_e2e = pred_e2e

    return pred_new_e2e
