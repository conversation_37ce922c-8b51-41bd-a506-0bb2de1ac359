import torch
import os
from typing import List, Sequence

from perceptron.engine.executors.evaluators import Det3DEvaluator, OCCEvaluator, End2endEvaluator, GODDet3DEvaluator
from perceptron.engine.executors import BaseExecutor
from perceptron.engine.executors.evaluators.map_evaluators import Map_Evaluator_Subrange
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp


__all__ = ["OneModelEvaluator"]


class OneModelEvaluator(Det3DEvaluator, OCCEvaluator, Map_Evaluator_Subrange, End2endEvaluator, GODDet3DEvaluator):
    def __init__(
        self,
        exp: BaseExp,
        callbacks: Sequence[Callback],
        logger=None,
        eval_interval: int = -1,
        cli_args=None,
        output_dir=None,
    ) -> None:
        BaseExecutor.__init__(self, exp=exp, callbacks=callbacks, logger=logger)
        self.eval_interval = eval_interval
        self.exp = exp
        self.callbacks = callbacks
        self.logger = logger
        self.cli_args = cli_args
        self.output_dir = output_dir
        self.evaluators = {
            "box": Det3DEvaluator,
            "e2e": End2endEvaluator,
            "occ": OCCEvaluator,
            "map": Map_Evaluator_Subrange,
            "god": GODDet3DEvaluator,
            "lidar_only": Det3DEvaluator,
        }
        self.tasks: List = exp.supported_tasks

    def run_task(self, task):

        self.exp.task_flag = task
        # 设置保存的路径
        old_exp_output_dir = self.exp.output_dir
        self.exp.output_dir = os.path.join(self.exp.output_dir, task)
        self.logger.info(f"\n\n==> Evaluation of task: {task}, saving to {self.exp.output_dir}\n")
        self.evaluators[task].eval(self)
        self.exp.output_dir = old_exp_output_dir

    def eval(self):
        for task in self.tasks:
            model = (
                self.exp.model.module
                if isinstance(self.exp.model, torch.nn.parallel.DistributedDataParallel)
                else self.exp.model
            )
            if task in ["box", "e2e"] and (self.cli_args.eval_od or self.cli_args.eval):
                model.class_names = self.exp.od_class_names
            elif task == "occ" and (self.cli_args.eval_occ or self.cli_args.eval):
                model.class_names = self.exp.occ_class_names
            elif task == "map" and (self.cli_args.eval_map or self.cli_args.eval):
                pass
            elif task == "god" and (self.cli_args.eval_god or self.cli_args.eval):
                model.class_names = self.exp.god_class_names
            elif task == "lidar_only" and (self.cli_args.eval_lidar_only or self.cli_args.eval):
                model.class_names = self.exp.lidar_only_class_names
            elif task not in ["box", "e2e", "occ", "map", "god", "lidar_only"]:
                raise NotImplementedError(f"Task {task} is not supported in OneModelEvaluator.")
            else:
                continue
            self.run_task(task)
