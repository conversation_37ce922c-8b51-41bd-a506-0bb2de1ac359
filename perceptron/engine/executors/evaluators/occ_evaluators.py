from typing import Sequence
from tqdm import tqdm

from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset

from ..base_executor import BaseExecutor
import refile
import tempfile
import mmcv
from mmcv.runner import get_dist_info
import torch
import numpy as np
import torch.distributed as dist
import shutil

__all__ = ["OCCEvaluator"]


class OCCEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence[Callback], logger=None, eval_interval: int = -1) -> None:
        super().__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval

    def eval(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        dataset = self.val_dataloader.dataset
        evaluator = self.val_dataloader.dataset.evaluation(None, exp.model.module.class_names)
        evaluator.evaluation_init()
        for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
            data = next(self.val_iter)

            semantic = data.pop("semantic")
            semantic_mask = data.pop("semantic_mask")
            semantic = semantic.cpu().numpy().astype(np.uint8)
            semantic_mask = semantic_mask.cpu().numpy().astype(np.bool_)

            if hasattr(dataset, "batch_postcollate_fn"):
                PrivateE2EDataset.load_tensor2gpu(data)
            if hasattr(dataset, "batch_preforward_fn"):
                dataset.batch_preforward_fn(data)

            pred_item = exp.test_step(data)
            if step % 10 == 0:
                torch.cuda.empty_cache()
            if isinstance(pred_item, dict) and "pred_dicts" in pred_item:
                pred_dicts = pred_item["pred_dicts"]
                for batch_idx, item in enumerate(pred_dicts):
                    item.update({"semantic": semantic[batch_idx], "semantic_mask": semantic_mask[batch_idx]})
                pred = self.val_dataloader.dataset.generate_prediction_dicts(
                    data, pred_dicts, exp.model.module.class_names
                )
            elif isinstance(pred_item, list):
                pred = pred_item
            else:
                raise NotImplementedError
            evaluator.evaluation_process(pred)
            self._invoke_callback("after_step", step, {})

        evaluator = collect_results_cpu(evaluator, exp.output_dir)
        torch_dist.synchronize()

        if local_rank == 0:
            self.logger.info("start layer0 evaluation......")
            mIoU_layer0, print_str_layer0 = evaluator.occ_eval_metrics_layer0.count_miou()
            mIoU_layer0_, _ = evaluator.IoU_eval_metrics_layer0.count_miou()
            mIoU_layer0["IoU_occupied"] = mIoU_layer0_["IoU_occupied"]
            IoU_str_layer0 = "\n Occupied IoU : {0}".format(mIoU_layer0["IoU_occupied"])
            print_str_layer0 += IoU_str_layer0
            self.logger.info(print_str_layer0)
            border_result_layer0 = evaluator.border_eval_metric_layer0.count_border_results(worker_async=True)
            eval_result_dir = refile.smart_path_join(exp.output_dir, "eval_result_occ")
            evaluator.save_eval_results(mIoU_layer0, refile.smart_path_join(eval_result_dir, "eval.log"))

            self.logger.info("start layer1 evaluation......")
            mIoU_layer1, print_str_layer1 = evaluator.occ_eval_metrics_layer1.count_miou()
            mIoU_layer1_, _ = evaluator.IoU_eval_metrics_layer1.count_miou()
            mIoU_layer1["IoU_occupied"] = mIoU_layer1_["IoU_occupied"]
            IoU_str_layer1 = "\n Occupied IoU : {0}".format(mIoU_layer1["IoU_occupied"])
            print_str_layer1 += IoU_str_layer1
            self.logger.info(print_str_layer1)
            border_result_layer1 = evaluator.border_eval_metric_layer1.count_border_results(worker_async=True)
            eval_result_dir = refile.smart_path_join(exp.output_dir, "eval_result_occ")
            evaluator.save_eval_results(mIoU_layer1, refile.smart_path_join(eval_result_dir, "eval.log"))

            self.logger.info("start visualmask evaluation......")
            mIoU_vismask, print_str_vismask = evaluator.vismask_eval_metrics_layer0.count_miou()
            mIoU_layer0["IoU_visible"] = mIoU_vismask["IoU_visible"]
            IoU_vismask_str = "\n Visible IoU : {0}".format(mIoU_layer0["IoU_visible"])
            print_str_vismask += IoU_vismask_str

            self.logger.info(print_str_vismask)

            for iou_name, iou_value in mIoU_layer1.items():
                new_iou_name = iou_name + "_layer2"
                mIoU_layer0[new_iou_name] = iou_value

            print(f"IoU eval result: {mIoU_layer0}")
            print("====================================")
            print(f"layer0 border eval result: {border_result_layer0}")
            print("====================================")
            print(f"layer1 border eval result: {border_result_layer1}")


def collect_results_cpu(evaluator, output_dir):

    tmpdir = refile.smart_path_join(output_dir, "tmp")
    rank, world_size = get_dist_info()
    # create a tmp dir if it is not specified
    if tmpdir is None:
        MAX_LEN = 512
        # 32 is whitespace
        dir_tensor = torch.full((MAX_LEN,), 32, dtype=torch.uint8, device="cuda")
        if rank == 0:
            mmcv.mkdir_or_exist(".dist_test")
            tmpdir = tempfile.mkdtemp(dir=".dist_test")
            tmpdir = torch.tensor(bytearray(tmpdir.encode()), dtype=torch.uint8, device="cuda")
            dir_tensor[: len(tmpdir)] = tmpdir
        torch_dist.synchronize()
        dist.broadcast(dir_tensor, 0)
        tmpdir = dir_tensor.cpu().numpy().tobytes().decode().rstrip()
    else:
        mmcv.mkdir_or_exist(tmpdir)
    # dump the part result to the dir
    import pickle

    print(f"{tmpdir}/part_{rank}.pkl\n")
    with refile.smart_open(f"{tmpdir}/part_{rank}_occ_eval_metrics_layer0.pkl", "wb") as f:
        pickle.dump(evaluator.occ_eval_metrics_layer0, f)
    with refile.smart_open(f"{tmpdir}/part_{rank}_IoU_eval_metrics_layer0.pkl", "wb") as f:
        pickle.dump(evaluator.IoU_eval_metrics_layer0, f)
    with refile.smart_open(f"{tmpdir}/part_{rank}_border_eval_metric_layer0.pkl", "wb") as f:
        pickle.dump(evaluator.border_eval_metric_layer0, f)
    with refile.smart_open(f"{tmpdir}/part_{rank}_vismask_eval_metrics_layer0.pkl", "wb") as f:
        pickle.dump(evaluator.vismask_eval_metrics_layer0, f)
    with refile.smart_open(f"{tmpdir}/part_{rank}_occ_eval_metrics_layer1.pkl", "wb") as f:
        pickle.dump(evaluator.occ_eval_metrics_layer1, f)
    with refile.smart_open(f"{tmpdir}/part_{rank}_IoU_eval_metrics_layer1.pkl", "wb") as f:
        pickle.dump(evaluator.IoU_eval_metrics_layer1, f)
    with refile.smart_open(f"{tmpdir}/part_{rank}_border_eval_metric_layer1.pkl", "wb") as f:
        pickle.dump(evaluator.border_eval_metric_layer1, f)

    dist.barrier()
    # collect all parts
    if rank != 0:
        return None
    else:
        evaluator._init_metrics()
        # load results of all parts from tmp dir
        for i in range(world_size):
            part_file = f"{tmpdir}/part_{i}_occ_eval_metrics_layer0.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.occ_eval_metrics_layer0.merge(pickle.load(f))
            part_file = f"{tmpdir}/part_{i}_IoU_eval_metrics_layer0.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.IoU_eval_metrics_layer0.merge(pickle.load(f))
            part_file = f"{tmpdir}/part_{i}_border_eval_metric_layer0.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.border_eval_metric_layer0.merge(pickle.load(f))
            part_file = f"{tmpdir}/part_{i}_vismask_eval_metrics_layer0.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.vismask_eval_metrics_layer0.merge(pickle.load(f))
            part_file = f"{tmpdir}/part_{i}_occ_eval_metrics_layer1.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.occ_eval_metrics_layer1.merge(pickle.load(f))
            part_file = f"{tmpdir}/part_{i}_IoU_eval_metrics_layer1.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.IoU_eval_metrics_layer1.merge(pickle.load(f))
            part_file = f"{tmpdir}/part_{i}_border_eval_metric_layer1.pkl"
            with refile.smart_open(part_file, "rb") as f:
                evaluator.border_eval_metric_layer1.merge(pickle.load(f))

        shutil.rmtree(tmpdir)
        return evaluator
