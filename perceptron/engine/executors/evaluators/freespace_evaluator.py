from typing import Sequence
import copy
import os
import pickle
from refile import smart_open


from tqdm import tqdm
import torch
import numpy as np

from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist
from ..base_executor import BaseExecutor

__all__ = ["FreespaceEvaluator"]


class FreespaceEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence[Callback], logger=None, eval_interval: int = -1) -> None:
        super().__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval

    @property
    def test_dataloader(self):
        return self.exp.test_dataloader

    def eval(self, global_step=-1):
        # 每帧每卡评测， 而非把所有帧结果gather在一起后再评测。--train_and_eval 模式下调用
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return False, {"meta": None}
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        with torch.no_grad():
            res = []
            for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
                data = next(self.val_iter)
                pred_item = exp.test_step(data)  # List[List]
                pred_batch = self.val_dataloader.dataset.generate_prediction_dicts(pred_item, data)
                result_dict = self.val_dataloader.dataset.evaluation_batch(
                    copy.deepcopy(pred_batch), class_names=exp.model.module.class_names, output_dir=exp.output_dir
                )
                res.append(result_dict)

            torch_dist.synchronize()
            res = sum(map(list, zip(*torch_dist.all_gather_object(res))), [])
            if local_rank == 0:
                result_str, md_str, result_dict = self.val_dataloader.dataset.evaluation_result_reduce(
                    res, class_names=exp.model.module.class_names, output_dir=exp.output_dir
                )
                self.logger.info(result_str)
                self.after_eval(det_annos=None, res_str=md_str, epoch=self.epoch - 1)
                self.logger.info("Dump val results.")

        res_meta = {"iteration": global_step, "result": result_dict, "dataset": "default", "func": self.report_tfboard}
        return False, {"meta": res_meta}

    def infer(self):
        # --infer 模式下调用
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.test_iter = iter(self.test_dataloader)  # TODO test?
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        with torch.no_grad():
            preds = []
            for step in tqdm(range(len(self.test_dataloader)), disable=(local_rank > 0)):
                data = next(self.test_iter)
                pred_item = exp.test_step(data)  # List[List]
                pred_dict = self.test_dataloader.dataset.generate_prediction_dicts(pred_item, data)
                pred_dict = self.test_dataloader.dataset.select_dump_result(pred_dict)
                preds.extend(pred_dict)

            # 如果infer dataset很短，可以sync到卡0上一起dump, 否则可以分卡dump, 再多的话需要自己拆一下dataset或者另写存储逻辑。
            # torch_dist.synchronize()
            # preds = sum(map(list, zip(*torch_dist.all_gather_object(preds))), [])
            with open(exp.output_dir + f"/preds_{local_rank}.pkl", "wb") as f:
                pickle.dump(preds, f)

    def eval_and_dump(self):
        # eval and dump predict results, --eval 模式下调用
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        with torch.no_grad():
            preds = []
            res = []
            for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
                data = next(self.val_iter)
                pred_item = exp.test_step(data)  # List[List]
                pred_batch = self.val_dataloader.dataset.generate_prediction_dicts(pred_item, data)
                result_dict = self.val_dataloader.dataset.evaluation_batch(
                    copy.deepcopy(pred_batch), class_names=exp.model.module.class_names, output_dir=exp.output_dir
                )
                res.append(result_dict)
                pred_batch = self.val_dataloader.dataset.select_dump_result(pred_batch, result_dict)
                preds.extend(pred_batch)

            torch_dist.synchronize()
            preds = sum(map(list, zip(*torch_dist.all_gather_object(preds))), [])
            res = sum(map(list, zip(*torch_dist.all_gather_object(res))), [])
            if local_rank == 0:
                result_str, md_str, _ = self.val_dataloader.dataset.evaluation_result_reduce(
                    res, class_names=exp.model.module.class_names, output_dir=exp.output_dir
                )
                self.logger.info(result_str)
                self.logger.info("Dump val results.")
                self.dump(det_annos=preds, res_str=md_str)

    def report_tfboard(self, logger, iteration=0, dataset="defaults", result={}):
        # The function to add eval results to tfboard.
        observe_scene = ["all", "cq", "gszd"]  # 少打印一些场景指标
        for scene in result:
            if scene not in observe_scene:
                continue
            ths = set([k.split("@th")[-1] for k in result[scene].keys() if "@th" in k])
            used_keys = set()
            for th in ths:
                logger.add_scalar(f"{scene}.bev iou/th{th}", result[scene][f"bevmap_iou@th{th}"], iteration)
                logger.add_scalar(f"{scene}.point iou/th{th}", result[scene][f"point_iou@th{th}"], iteration)
                used_keys.add(f"bevmap_iou@th{th}")
                used_keys.add(f"point_iou@th{th}")

            for k, v in result[scene].items():
                if k in used_keys or "n_pair" in k:
                    continue
                if isinstance(v, np.ndarray):
                    for class_name, value in enumerate(v):
                        logger.add_scalar(f"{scene}.{k}/{class_name}", value, iteration)
                elif isinstance(v, float):
                    logger.add_scalar(f"{scene}.{k}", v, iteration)

    def after_eval(self, det_annos: list, res_str=None, epoch=0):
        out_file = os.path.join(self.exp.output_dir, f"epoch{epoch}_result.pkl")
        pickle.dump(det_annos, open(out_file, "wb"))

        if res_str:
            out_file = os.path.join(self.exp.output_dir, f"epoch{epoch}_result.md")
            with smart_open(out_file, "w") as wf:
                wf.writelines(res_str)

    def dump(self, det_annos: list, res_str=None):
        out_file = os.path.join(self.exp.output_dir, "result.pkl")
        pickle.dump(det_annos, open(out_file, "wb"))

        if res_str:
            out_file = os.path.join(self.exp.output_dir, "result.md")
            with smart_open(out_file, "w") as wf:
                wf.writelines(res_str)
