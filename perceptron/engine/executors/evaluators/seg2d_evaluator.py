from typing import Sequence
import torch
from tqdm import tqdm
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.callbacks import Callback
from perceptron.utils.torch_dist import get_rank
import numpy as np
from ..base_executor import BaseExecutor
import tabulate

__all__ = ["Seg2DEvaluator"]

task2label_map = [
    "road obstacles",
    "car",
    "motorcycle",
    "bicycle",
    "person",
    "rider",
    "truck",
    "bus",
    "tricycle",
    "road",
    "sidewalk",
    "sky",
    "building",
    "vegetation",
    "traffic light",
    "pole",
    "traffic sign",
    "wall",
    "ego vehicle",
]


def convert_three_index_to_wiki(
    IoU,
    task2label_map,
    tablefmt="orgtbl",
    version="",
):
    data = []
    class_num = len(task2label_map)
    headers = [version + "Class name", "IoU"]
    for i in range(class_num + 1):
        sub_data = []
        if i == class_num:
            sub_data.append("mIoU")
            sub_data.append(np.round(np.mean(IoU), 4))
        else:
            sub_data.append(task2label_map[i])
            sub_data.append(np.round(IoU[i], 4))
        data.append(sub_data)
    s = tabulate.tabulate(data, headers, tablefmt=tablefmt)
    lines = s.split("\n")
    lines[1] = lines[1].replace("+", "|")
    return "\n".join(lines)


class Seg2DEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None) -> None:
        super(Seg2DEvaluator, self).__init__(exp, callbacks, logger)

    def eval(self):
        exp = self.exp
        n_classes = exp.n_classes
        local_rank = get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()
        if local_rank == 0:
            hist = torch.zeros(n_classes, n_classes).cuda().detach()

        for step in tqdm(range(len(self.val_dataloader))):
            data = next(self.val_iter)
            res = exp.test_step(data)
            if local_rank == 0:
                hist += res

            self._invoke_callback("after_step", step, {})

        if local_rank == 0:
            ious = hist.diag() / (hist.sum(dim=0) + hist.sum(dim=1) - hist.diag())
            miou = ious.mean()
            markdown = convert_three_index_to_wiki(
                np.array(ious.cpu()),
                task2label_map,
                tablefmt="orgtbl",
                version="",
            )
            self.logger.info("Experiment name: {}, mIoU is: {}".format(exp.exp_name, miou))
            self.logger.info(markdown)

        self._invoke_callback("after_eval")
