from typing import Sequence

from tqdm import tqdm
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.callbacks import Callback
from perceptron.utils.torch_dist import get_rank

from ..base_executor import BaseExecutor

__all__ = ["Cls2DEvaluator"]


class Cls2DEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None) -> None:
        super(Cls2DEvaluator, self).__init__(exp, callbacks, logger)

    def eval(self):
        exp = self.exp
        local_rank = get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        # TODO[@lzm]: move to before eval
        from perceptron.utils.log_utils import AvgMeter

        if local_rank == 0:
            top1, top5 = AvgMeter(), AvgMeter()
        for step in tqdm(range(len(self.val_dataloader))):
            data = next(self.val_iter)
            res = exp.test_step(data)

            # TODO[@lzm]: move to after step
            if local_rank == 0:
                top1.update(res[0].item(), data[0].size(0))
                top5.update(res[1].item(), data[0].size(0))

            self._invoke_callback("after_step", step, {})

        # TODO[@lzm]: move to after eval
        if local_rank == 0:
            self.logger.info("Experiment name: {}, result is: {}/{}".format(exp.exp_name, top1.avg, top5.avg))

        self._invoke_callback("after_eval")
