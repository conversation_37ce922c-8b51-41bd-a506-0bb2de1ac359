"""
//<! The Test Prj of Traffic Lights Attrs
@author: PengXs
@date: 2021.12.27

"""
from typing import Sequence

import torch
from tqdm import tqdm
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist as dist
from ..base_executor import BaseExecutor

__all__ = ["TrafficCls2DEvaluator"]


class TrafficCls2DEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None) -> None:
        super(TrafficCls2DEvaluator, self).__init__(exp, callbacks, logger)

    @torch.no_grad()
    def eval(self):
        exp = self.exp
        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()
        exp.evaluator.init()
        for step in tqdm(range(len(self.val_dataloader))):

            data = next(self.val_iter)
            dataset, shape_score, color_score, shape_labels, color_labels = exp.test_step(data)

            if dist.is_available():
                dist.synchronize()

            exp.evaluator.process(dataset, shape_score, color_score, shape_labels, color_labels)
            self._invoke_callback("after_step", step, {})

        value = exp.evaluator.evaluate_and_statistic()
        return value
