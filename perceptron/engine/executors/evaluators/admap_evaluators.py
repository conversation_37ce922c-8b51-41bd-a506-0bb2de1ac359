import numpy as np
import torch

try:
    import cupy as cp

    use_cupy = True
except ImportError:
    use_cupy = False
from typing import Sequence
from tqdm import tqdm
from perceptron.utils import torch_dist as dist
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.executors.base_executor import BaseExecutor
from perceptron.utils import torch_dist

# from perceptron.utils.admap_utils.evaltools.eval import Lane3DEvaluator
import pickle
from mmcv.runner import get_dist_info
import itertools


def get_fork_from_dt(
    sequence_data_all, range_quantize=500, max_x=100, min_x=0, max_y=15, min_y=-15, max_z=10, min_z=-10
):

    sequence_data_all_det = [[] for _ in range(len(sequence_data_all))]
    fork_point = []
    end_point = []
    for i, sequence_data in enumerate(sequence_data_all):
        if len(sequence_data) % 3 != 0:
            # return
            print("!!!!!!!!!!!!!!!!! num seq %3 !=0 !!!!!!!!!!!")
            continue

        last_ego_x = 0
        last_ego_y = 0
        last_ego_z = 0
        for _i in range(len(sequence_data) // 3):
            ind_x = 3 * _i + 1
            ind_y = 3 * _i
            ind_z = 3 * _i + 2

            if sequence_data[ind_y] == range_quantize:
                continue
            if sequence_data[ind_y] == range_quantize + 1:
                end_point.append([i, len(sequence_data_all_det[i])])
                sequence_data_all_det[i].append([last_ego_x, last_ego_y, last_ego_z])
                continue

            ego_x = sequence_data[ind_x] / range_quantize * (max_x - min_x) + min_x
            ego_y = sequence_data[ind_y] / range_quantize * (max_y - min_y) + min_y
            ego_z = sequence_data[ind_z] / range_quantize * (max_z - min_z) + min_z

            if 0 <= ind_y - 1 < len(sequence_data) and sequence_data[ind_y - 1] == range_quantize:
                fork_point.append([i, len(sequence_data_all_det[i])])

            sequence_data_all_det[i].append([ego_x, ego_y, ego_z])

            last_ego_x = ego_x
            last_ego_y = ego_y
            last_ego_z = ego_z

    return sequence_data_all_det, fork_point, end_point


def inter_lane(lane, interp_dist=0.1):
    if len(lane) < 2:
        return lane
    lane = lane[lane[:, 0].argsort()]
    lane_x, lane_y, lane_z = lane[:, 0], lane[:, 1], lane[:, 2]
    lane_x_min, lane_x_max = min(lane_x), max(lane_x)
    lane_x_inter = np.append(np.arange(lane_x_min, lane_x_max, interp_dist), lane_x_max)
    if use_cupy:
        lane_x_inter_cp = cp.array(lane_x_inter)
        lane_x, lane_y, lane_z = cp.array(lane_x), cp.array(lane_y), cp.array(lane_z)
        lane_y_inter = cp.interp(lane_x_inter_cp, lane_x, lane_y).get()
        lane_z_inter = cp.interp(lane_x_inter_cp, lane_x, lane_z).get()
    else:
        lane_x_inter_cp = np.array(lane_x_inter)
        lane_x, lane_y, lane_z = np.array(lane_x), np.array(lane_y), np.array(lane_z)
        lane_y_inter = np.interp(lane_x_inter_cp, lane_x, lane_y)  # .get()
        lane_z_inter = np.interp(lane_x_inter_cp, lane_x, lane_z)  # .get()
    lane_x_inter, lane_y_inter, lane_z_inter = lane_x_inter, lane_y_inter, lane_z_inter
    lane_inter = np.stack([lane_x_inter, lane_y_inter, lane_z_inter], axis=1)
    return lane_inter


def out_inter_points(sequence_data_all, fork_idx, end_idx):
    interpoint = [[] for _ in range(len(sequence_data_all))]
    if fork_idx:
        for fork in fork_idx:
            interpoint[fork[0]].append(fork[1])
    if end_idx:
        for end in end_idx:
            interpoint[end[0]].append(end[1])
    interpoint = [sorted([-1] + inter) for inter in interpoint]

    # 分段
    sect_list = [[] for _ in range(len(sequence_data_all))]
    for i in range(len(sequence_data_all)):
        for j in range(len(interpoint[i]) - 1):
            sect_list[i].append(sequence_data_all[i][interpoint[i][j] + 1 : interpoint[i][j + 1] + 1])

    # 插值

    for i in range(len(sect_list)):
        for j in range(len(sect_list[i])):
            sect_list[i][j] = inter_lane(np.array(sect_list[i][j]))

    # 恢复

    for i in range(len(sequence_data_all)):
        # tmp_sequence = []
        # for sect in sect_list[i]:
        #     tmp_sequence.extend(sect)
        # sequence_data_all[i] = tmp_sequence
        sequence_data_all[i] = sect_list[i]

    return sequence_data_all


def get_ego_xyz(sequence_data_all, range_quantize=500, max_x=100, min_x=0, max_y=15, min_y=-15, max_z=10, min_z=-10):
    dt_lanes = []
    for sequence_data in sequence_data_all:
        dt_points = []
        if len(sequence_data) % 3 != 0:
            # return []
            print("!!!!!!!!!!!!!!!!! num seq %3 !=0 !!!!!!!!!!!")
            continue
        last_ego_x = 0
        last_ego_y = 0
        last_ego_z = 0
        for _i in range(len(sequence_data) // 3):
            ind_x = 3 * _i + 1
            ind_y = 3 * _i
            ind_z = 3 * _i + 2

            if sequence_data[ind_y] == range_quantize:
                continue
            if sequence_data[ind_y] == range_quantize + 1:
                dt_points.append([last_ego_x, last_ego_y, last_ego_z])
                continue

            ego_x = sequence_data[ind_x] / range_quantize * (max_x - min_x) + min_x
            ego_y = sequence_data[ind_y] / range_quantize * (max_y - min_y) + min_y
            ego_z = sequence_data[ind_z] / range_quantize * (max_z - min_z) + min_z
            dt_points.append([ego_x, ego_y, ego_z])

            last_ego_x = ego_x
            last_ego_y = ego_y
            last_ego_z = ego_z
        dt_lanes.append(dt_points)
    return dt_lanes


class MapEvaluating:
    def __init__(
        self,
        eval_range=[100, 0, 15, -15, 10, -10],  # [max_x, min_x, max_y, min_y, max_z, min_z]
        model_range=[100, 0, 15, -15, 10, -10],  # [max_x, min_x, max_y, min_y, max_z, min_z]
        model_range_quantize=500,
        **kwargs
    ):
        self.num_tp = 0
        self.num_gt = 0
        self.num_dt = 0
        self.decay_ratio = 0
        self.fov_region = None
        self.x_start_valid = 300

        self.eval_range = eval_range
        self.model_range = model_range
        self.model_range_quantize = model_range_quantize

    def __call__(self, result, result_GT, thresh_hold, fork_idx, end_idx):
        thresh_conf = dict(xyz=thresh_hold * 2, xy=thresh_hold * 2, xz=thresh_hold * 2, INF=thresh_hold * 10)
        evaluator = Lane3DEvaluator(  # noqa
            region_conf=dict(
                range=self.eval_range,
                step=1,
                decay_ratio=self.decay_ratio,
                fov_region=self.fov_region,
                x_start_valid=self.x_start_valid,
            ),
            thresh_conf=thresh_conf,
            metric_conf=dict(interp_dist=0.1, keep_ratio=0.9, mode="d<->g"),
            use_category=False,
            error_distance=2,
        )

        max_x, min_x, max_y, min_y, max_z, min_z = self.model_range
        gt_lanes = get_ego_xyz(result_GT, self.model_range_quantize, max_x, min_x, max_y, min_y, max_z, min_z)
        gt_lanes = out_inter_points(gt_lanes, fork_idx, end_idx)
        sequence_data_all_det, fork_ind, end_ind = get_fork_from_dt(
            result, self.model_range_quantize, max_x, min_x, max_y, min_y, max_z, min_z
        )
        dt_lanes = out_inter_points(sequence_data_all_det, fork_ind, end_ind)

        results = []
        thresh_result = []
        dt_status = []
        if all(tmp_dt for tmp_dt in sequence_data_all_det):
            res_dict = evaluator.add_once(dt_lanes, gt_lanes)
            if res_dict:
                dt_status = res_dict["dt_status"]
                results.append(res_dict)
                if thresh_hold == 0.3:
                    self.num_tp += results[0]["num_tp"]
                    self.num_gt += results[0]["num_gt"]
                    self.num_dt += results[0]["num_dt"]
                thresh_result.append(
                    [round(results[0]["precision"], 4), round(results[0]["recall"], 4), round(results[0]["f_score"], 4)]
                )
            # print("####thresh_hold, precision, recall, f1-score####", thresh_hold, results[0]['precision'], results[0]['recall'], results[0]['f_score'])
        return thresh_result, dt_status, res_dict

    def summary(self):
        print("num_tp, num_dt, num_gt", self.num_tp, self.num_dt, self.num_gt)
        precision = self.num_tp / max(self.num_dt, 1)
        recall = self.num_tp / max(self.num_gt, 1)
        f_score = 2 * recall * precision / max(recall + precision, 1e-6)
        print("#----Avg Precision, Recall, F_score----", precision, recall, f_score)
        return self.num_tp, self.num_dt, self.num_gt


class ADMapEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None, eval_interval: int = -1) -> None:
        super(ADMapEvaluator, self).__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval

    def eval(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        preds = []
        dataset = self.val_dataloader.dataset
        for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
            data = next(self.val_iter)
            if hasattr(dataset, "batch_postcollate_fn"):
                dataset.batch_postcollate_fn(data)
            if hasattr(dataset, "batch_preforward_fn"):
                dataset.batch_preforward_fn(data)
            pred_item = exp.test_step(data)

            outs_map = pred_item["pred_maps"]
            # evaluation
            thresh_hold = 0.3
            sequence_data = data["sequence_data"]
            valid_mask_seq = data["valid_mask_seq"]
            fork_idx = data["fork_idx"]
            end_idx = data["end_idx"]
            for bs_idx in range(len(outs_map[0])):
                for f_idx in range(len(outs_map)):
                    result = outs_map[f_idx][bs_idx]
                    # print(sequence_data.shape, valid_mask_seq.shape, len(outs_map[0]))
                    seq_gt = sequence_data[bs_idx][f_idx]
                    seq_gt_mask = valid_mask_seq[bs_idx][f_idx]
                    result_GT = []
                    for n_idx in range(seq_gt.shape[0]):
                        if not seq_gt_mask[n_idx][0]:
                            continue
                        single_seq = seq_gt[n_idx][seq_gt_mask[n_idx]]
                        result_GT.append(single_seq.cpu().numpy())

                    tmp_fork_idx = fork_idx[bs_idx][f_idx]
                    tmp_end_idx = end_idx[bs_idx][f_idx]
                    thr_res, dt_status, res_dict = self.val_dataloader.dataset.evaluator(
                        result, result_GT, thresh_hold, tmp_fork_idx, tmp_end_idx
                    )
                    print(thr_res)
            preds.append(res_dict)
            self._invoke_callback("after_step", step, {})
            # from IPython import embed; embed()

        dist.synchronize()
        num_tp, num_dt, num_gt = self.val_dataloader.dataset.evaluator.summary()
        num_tp = dist.reduce_sum(torch.tensor(num_tp).cuda()).item()
        num_dt = dist.reduce_sum(torch.tensor(num_dt).cuda()).item()
        num_gt = dist.reduce_sum(torch.tensor(num_gt).cuda()).item()
        scene_order = hasattr(self.val_dataloader.dataset, "scene_order") and self.val_dataloader.dataset.scene_order
        preds = collect_results_gpu(preds, len(self.val_dataloader.dataset), scene_order=scene_order)

        if local_rank == 0:
            self.logger.info("ADMap Evaluation -- SUMMARY ALL : ")
            self.logger.info("num_tp, num_dt, num_gt: {} {} {}".format(num_tp, num_dt, num_gt))
            precision = num_tp / max(num_dt, 1)
            recall = num_tp / max(num_gt, 1)
            f_score = 2 * recall * precision / max(recall + precision, 1e-6)
            self.logger.info(
                "#----ADMap Avg Precision, Recall, F_score---- : {} {} {}".format(precision, recall, f_score)
            )

        self._invoke_callback("after_eval", map_annos=preds)


def collect_results_gpu(result_part, size, scene_order):
    rank, world_size = get_dist_info()
    # dump result part to tensor with pickle
    part_tensor = torch.tensor(bytearray(pickle.dumps(result_part)), dtype=torch.uint8, device="cuda")
    # gather all result part tensor shape
    shape_tensor = torch.tensor(part_tensor.shape, device="cuda")
    shape_list = [shape_tensor.clone() for _ in range(world_size)]
    torch.distributed.all_gather(shape_list, shape_tensor)
    # padding result part tensor to max length
    shape_max = torch.tensor(shape_list).max()
    part_send = torch.zeros(shape_max, dtype=torch.uint8, device="cuda")
    part_send[: shape_tensor[0]] = part_tensor
    part_recv_list = [part_tensor.new_zeros(shape_max) for _ in range(world_size)]
    # gather all result part
    torch.distributed.all_gather(part_recv_list, part_send)

    if rank == 0:
        part_list = []
        for recv, shape in zip(part_recv_list, shape_list):
            part_list.append(pickle.loads(recv[: shape[0]].cpu().numpy().tobytes()))
        # sort the results
        if scene_order:
            ordered_results = list(itertools.chain(*part_list))
        else:
            ordered_results = []
            for res in zip(*part_list):
                ordered_results.extend(list(res))
        # the dataloader may pad some samples
        ordered_results = ordered_results[:size]
        return ordered_results
