import torch

from typing import Sequence

from tqdm import tqdm
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.callbacks import Callback
from perceptron.utils import torch_dist as dist

from ..base_executor import BaseExecutor

__all__ = ["Det2DEvaluator"]


class Det2DEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None) -> None:
        super(Det2DEvaluator, self).__init__(exp, callbacks, logger)

    @torch.no_grad()
    def eval(self):
        exp = self.exp

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        exp.evaluator.reset()

        for step in tqdm(range(len(self.val_dataloader))):

            data = next(self.val_iter)
            inputs, outputs = exp.test_step(data)
            if dist.is_available():
                dist.synchronize()

            exp.evaluator.process(inputs, outputs)

            self._invoke_callback("after_step", step, {})

        results = exp.evaluator.evaluate()
        # An evaluator may return None when not in rank0.
        # Replace it by an empty dict instead to make it easier for downstream code to handle
        if results is None:
            results = {}
