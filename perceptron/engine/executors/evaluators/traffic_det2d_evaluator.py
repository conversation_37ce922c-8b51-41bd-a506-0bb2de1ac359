from typing import Sequence

import torch
from tqdm import tqdm
from perceptron.engine.callbacks import Callback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist as dist

from ..base_executor import BaseExecutor

__all__ = ["TrafficDet2DEvaluator"]


class TrafficDet2DEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence["Callback"], logger=None) -> None:
        super(TrafficDet2DEvaluator, self).__init__(exp, callbacks, logger)

    @torch.no_grad()
    def eval(self):
        exp = self.exp
        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()
        exp.extra_evaluator.init()
        for step in tqdm(range(len(self.val_dataloader))):

            data = next(self.val_iter)
            inputs, outputs, all_dt, all_gt = exp.test_step(data)

            if dist.is_available():
                dist.synchronize()

            exp.extra_evaluator.process(all_dt, all_gt)
            self._invoke_callback("after_step", step, {})

        exp.extra_evaluator.evaluate_and_statistic()
