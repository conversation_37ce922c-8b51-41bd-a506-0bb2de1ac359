from typing import Sequence

from tqdm import tqdm

from perceptron.engine.callbacks import Callback
from perceptron.engine.callbacks.clearml_callback import ClearMLCallback
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist

from ..base_executor import BaseExecutor


__all__ = ["OCCSerialEvaluator"]


class OCCSerialEvaluator(BaseExecutor):
    def __init__(self, exp: BaseExp, callbacks: Sequence[Callback], logger=None, eval_interval: int = -1) -> None:
        super().__init__(exp, callbacks, logger)
        self.eval_interval = eval_interval

    def eval(self):
        self.epoch += 1
        if self.eval_interval != -1 and self.epoch % self.eval_interval != 0:
            return
        exp = self.exp
        local_rank = torch_dist.get_rank()

        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()

        preds = []
        dataset = self.val_dataloader.dataset
        class_names = exp.model.module.class_names
        for step in tqdm(range(len(self.val_dataloader)), disable=(local_rank > 0)):
            data = next(self.val_iter)
            semantic = data.pop("semantic")
            semantic_mask = data.pop("semantic_mask")
            semantic = semantic.cpu().numpy()
            semantic_mask = semantic_mask.cpu().numpy()

            if hasattr(dataset, "batch_postcollate_fn"):
                dataset.batch_postcollate_fn(data)
            if hasattr(dataset, "batch_preforward_fn"):
                dataset.batch_preforward_fn(data)

            pred_item = exp.test_step(data)
            if isinstance(pred_item, dict) and "pred_dicts" in pred_item:
                pred_dicts = pred_item["pred_dicts"]
                for batch_idx, item in enumerate(pred_dicts):
                    item.update({"semantic": semantic[batch_idx], "semantic_mask": semantic_mask[batch_idx]})
                preds += self.val_dataloader.dataset.generate_prediction_dicts(
                    data,
                    pred_dicts,
                    class_names,
                )
            # mmdetection e.g. FCOS3D
            elif isinstance(pred_item, list):
                pred_item = [{**pred, "semantic": semantic, "semantic_mask": semantic_mask} for pred in pred_item]
                preds += pred_item
            else:
                raise NotImplementedError

            self._invoke_callback("after_step", step, {})
        torch_dist.synchronize()
        preds = torch_dist.rank0_gather_object_chunked(obj_list=preds, chunk_size=100, show_progress=True)
        if preds is not None:
            preds = sum(map(list, zip(*preds)), [])[: len(self.val_dataloader.dataset)]
        torch_dist.synchronize()
        if local_rank == 0:
            self.logger.info("eval ...")
            clearml = [cb for cb in self.callbacks if isinstance(cb, ClearMLCallback)]
            kwargs = {}
            if clearml:
                clearml = clearml[0]
                kwargs = {"clearml_task": clearml.task, "iteration": self.global_step}
            result_str, result_dict = self.val_dataloader.dataset.evaluation(
                preds, class_names, output_dir=exp.output_dir, **kwargs
            )
            self.logger.info(result_str)
        self._invoke_callback("after_eval", det_annos=preds)
