import torch
from torch2trt import TRTModule

from perceptron.data.det3d.private.split_front_back_data import DatasetSplitFrontBack
from perceptron.data.det3d.sampler import InfiniteSampler, WeightedInfiniteSampler
from perceptron.engine.cli import Det3DCli
from perceptron.exps.camera_det.private.baseline.bevdet_official.base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.camera_det.private.release.orin_7v import (
    model_config_res991_256_res50_512_64x200 as model_config,
)
from perceptron.exps.camera_det.private.release.orin_7v.data_config_geely101_7v import (
    train_dataset_cfg,
    val_dataset_cfg,
    weights,
)
from perceptron.layers.lr_scheduler import StepLRScheduler
from perceptron.models.detbev.bev_det import BevDetPrivateSplitBackbone as BevDetPrivate

__all__ = ["Exp"]


class Exp(BaseExp):
    def __init__(self, **kwargs):
        super(Exp, self).__init__(**kwargs)
        # model config
        self.backbone_conf = model_config.backbone_conf
        self.head_conf = model_config.head_conf
        self.class_names = model_config.class_names
        self.trt_mode = False
        self.vis_imgs = False
        # dataset config
        self.dataset = DatasetSplitFrontBack
        self.collate_fn = self.dataset.collate_fn
        self.train_data_cfg = train_dataset_cfg
        self.val_data_cfg = val_dataset_cfg
        self.weights = weights

    def training_step(self, batch):
        front_back_imgs, imgs, mats_dict, gt_boxes, gt_labels = (
            batch["front_back_imgs"],
            batch["side_imgs"],
            batch["mats_dict"],
            batch["gt_boxes"],
            batch["gt_labels"],
        )

        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = torch.from_numpy(value).cuda()
            front_back_imgs = torch.from_numpy(front_back_imgs).cuda()
            imgs = torch.from_numpy(imgs).cuda()
            gt_boxes = [torch.from_numpy(gt_box).cuda() for gt_box in gt_boxes]
            gt_labels = [torch.from_numpy(gt_label).cuda() for gt_label in gt_labels]
        preds = self.model(
            front_back_imgs.float(),
            imgs.float(),
            mats_dict,
        )
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            with torch.no_grad():
                targets = self.model.module.get_targets(gt_boxes, gt_labels)
            loss = self.model.module.loss(targets, preds)
        else:
            with torch.no_grad():
                targets = self.model.get_targets(gt_boxes, gt_labels)
            loss = self.model.loss(targets, preds)
        return loss

    @torch.no_grad()
    def test_step(self, batch):
        if self.trt_mode:
            return self.test_step_trt(batch)

        front_back_imgs, imgs, mats_dict = (
            batch["front_back_imgs"],
            batch["side_imgs"],
            batch["mats_dict"],
        )
        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = torch.from_numpy(value).cuda()
            front_back_imgs = torch.from_numpy(front_back_imgs).cuda()
            imgs = torch.from_numpy(imgs).cuda()
        with torch.no_grad():
            preds = self.model(
                front_back_imgs.float(),
                imgs.float(),
                mats_dict,
            )
            if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
                results = self.model.module.get_bboxes(preds)
            else:
                results = self.model.get_bboxes(preds)

        return results

    def test_step_trt(self, batch):
        front_back_imgs, imgs, mats_dict = (
            batch["front_back_imgs"],
            batch["imgs"],
            batch["mats_dict"],
        )
        front_back_imgs = front_back_imgs.permute(0, 1, 3, 4, 2)
        imgs = imgs.permute(0, 1, 3, 4, 2)
        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = torch.from_numpy(value).cuda()
            front_back_imgs = torch.from_numpy(front_back_imgs).cuda()
            imgs = torch.from_numpy(imgs).cuda()

        lidar2imgs = mats_dict["lidar2imgs"]
        ida_mats = mats_dict["ida_mats"]
        bda_mat = None
        if "bda_mat" in mats_dict:
            bda_mat = mats_dict["bda_mat"]
        front_back_geom_xyz = self.model.backbone.get_geometry(
            lidar2imgs[:, 4:], ida_mats[:, 4:], bda_mat, self.model.backbone.front_back_frustum
        )
        left_right_geom_xyz = self.model.backbone.get_geometry(
            lidar2imgs[:, :4], ida_mats[:, :4], bda_mat, self.model.backbone.frustum
        )

        front_back_geom_xyz = (
            (front_back_geom_xyz - (self.model.backbone.voxel_coord - self.model.backbone.voxel_size / 2.0))
            / self.model.backbone.voxel_size
        ).int()
        left_right_geom_xyz = (
            (left_right_geom_xyz - (self.model.backbone.voxel_coord - self.model.backbone.voxel_size / 2.0))
            / self.model.backbone.voxel_size
        ).int()

        with torch.no_grad():
            outputs = self.model.trt_model(
                front_back_geom_xyz,
                left_right_geom_xyz,
                front_back_imgs.float(),
                imgs.float(),
            )
            preds = []
            for i in range(len(self.head_conf["tasks"])):
                pred = {
                    "reg": outputs[5 * i + 0],
                    "height": outputs[5 * i + 1],
                    "dim": outputs[5 * i + 2],
                    "rot": outputs[5 * i + 3],
                    "heatmap": outputs[5 * i + 4],
                }
                preds.append([pred])
            results = self.model.get_bboxes(preds)
        return results

    def _configure_train_dataloader(self):
        train_dataset = self._configure_train_dataset()
        dataset_weights = None
        if isinstance(train_dataset, list):
            dataset_weights = []
            for idx, dataset in enumerate(train_dataset):
                dataset_weights += [self.weights[idx] / len(dataset)] * len(dataset)
            train_dataset = torch.utils.data.ConcatDataset(train_dataset)
            sampler = WeightedInfiniteSampler(dataset_weights, len(train_dataset))
        else:
            sampler = InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=True,
            shuffle=False,
            collate_fn=self.collate_fn,
            num_workers=self.num_workers,
            sampler=sampler,
            pin_memory=True,
        )
        return train_loader

    def _configure_model(self):
        model = BevDetPrivate(self.backbone_conf, self.head_conf)
        model.class_names = self.class_names
        if self.trt_mode:
            model.trt_model = TRTModule()
        return model

    def _configure_optimizer(self):
        lr = self.basic_lr
        param_wd = []
        param_no_wd = []
        for name, p in self.model.named_parameters():
            if "weight" in name:
                param_wd.append(p)
            else:
                param_no_wd.append(p)
        optimizer = torch.optim.AdamW(
            [{"params": param_wd}, {"params": param_no_wd, "weight_decay": 0.0}], lr=lr, weight_decay=1e-4
        )
        return optimizer

    def _configure_lr_scheduler(self):
        # TODO: Finetune training config.
        scheduler = StepLRScheduler(
            self.optimizer,
            self.basic_lr,
            len(self.train_dataloader),
            self.max_epoch,
            milestones=[19, 23],
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
