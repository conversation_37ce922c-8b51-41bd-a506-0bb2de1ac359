import os

import torch
import torchvision
from torch.utils.data import DistributedSampler

from perceptron.data.det3d.private.base import DatasetBase
from perceptron.data.det3d.sampler import InfiniteSampler
from perceptron.engine.cli import Det3DCli
from perceptron.engine.executors import Det3DInfer
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.camera_det.private.baseline.bevdet_official import (
    model_config_r50_512x1408_128x160 as model_config,
)
from perceptron.exps.camera_det.private.baseline.bevdet_official.data_config_wm2 import (
    train_dataset_cfg as train_data_cfg,
)
from perceptron.exps.camera_det.private.baseline.bevdet_official.data_config_wm2 import (
    val_dataset_cfg as val_data_cfg,
)
from perceptron.layers.lr_scheduler import StepLRScheduler
from perceptron.models.detbev.bev_det import BevDetPrivate
from perceptron.utils import torch_dist as dist

__all__ = ["Exp"]


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=8,
        total_devices=1,
        max_epoch=24,
        class_names=model_config.class_names,
        backbone_conf=model_config.backbone_conf,
        head_conf=model_config.head_conf,
        max_grad_norm=5.0,
        dump_interval=1,
        warmup_epochs=0.0,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.basic_lr = 2e-4
        self.class_names = class_names
        self.backbone_conf = backbone_conf
        self.head_conf = head_conf
        self.grad_clip_value = max_grad_norm
        self.dump_interval = dump_interval
        self.warmup_epochs = warmup_epochs
        self.infer_executor_class = Det3DInfer
        self.train_data_cfg = train_data_cfg
        self.val_data_cfg = val_data_cfg
        self.dataset = DatasetBase
        self.collate_fn = self.dataset.collate_fn
        self.num_workers = self.batch_size_per_device * 2

    def training_step(self, batch):
        imgs, mats_dict, gt_boxes, gt_labels = (
            batch["imgs"],
            batch["mats_dict"],
            batch["gt_boxes"],
            batch["gt_labels"],
        )
        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = torch.from_numpy(value).cuda()
            imgs = torch.from_numpy(imgs).cuda()
            gt_boxes = [torch.from_numpy(gt_box).cuda() for gt_box in gt_boxes]
            gt_labels = [torch.from_numpy(gt_label).cuda() for gt_label in gt_labels]
        preds = self.model(
            imgs.float(),
            mats_dict,
        )
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            targets = self.model.module.get_targets(gt_boxes, gt_labels)
            loss = self.model.module.loss(targets, preds)
        else:
            targets = self.model.get_targets(gt_boxes, gt_labels)
            loss = self.model.loss(targets, preds)
        return loss

    @torch.no_grad()
    def test_step(self, batch):
        imgs, mats_dict = (
            batch["imgs"],
            batch["mats_dict"],
        )
        if hasattr(self, "vis_imgs") and self.vis_imgs:
            save_path = "/data/project/vis_imgs"
            if not os.path.exists(save_path):
                os.mkdir(save_path)
            image_grid = torchvision.utils.make_grid(imgs[0], nrow=3, padding=0, normalize=True)
            torchvision.utils.save_image(image_grid, "/data/project/vis_imgs/cameras_final_eval.png", nrow=3)
            print("save as /data/project/vis_imgs/cameras_final_eval.png")
            exit(0)
        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = torch.from_numpy(value).cuda()
            imgs = torch.from_numpy(imgs).cuda()
        preds = self.model(
            imgs.float(),
            mats_dict,
        )
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            results = self.model.module.get_bboxes(preds)
        else:
            results = self.model.get_bboxes(preds)
        return results

    def _configure_train_dataset(self):
        if isinstance(self.train_data_cfg, dict):
            train_dataset = self.dataset(**self.train_data_cfg)
        elif isinstance(self.train_data_cfg, list):
            train_dataset = []
            for cfg in self.train_data_cfg:
                train_dataset.append(self.dataset(**cfg))
        else:
            raise (f'The "train_data_cfg" must be dict or list type! Not {type(self.train_data_cfg)}')
        return train_dataset

    def _configure_train_dataloader(self):
        train_dataset = self._configure_train_dataset()

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=self.num_workers,
            drop_last=True,
            shuffle=False,
            collate_fn=self.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = self.dataset(**self.val_data_cfg)
        sampler = None
        if dist.is_distributed():
            sampler = DistributedSampler(val_dataset, shuffle=False, drop_last=False)
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=self.collate_fn,
            num_workers=self.num_workers,
            sampler=sampler,
            pin_memory=True,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_dataset = self.dataset(**self.val_data_cfg)
        sampler = None
        if dist.is_distributed():
            sampler = DistributedSampler(test_dataset, shuffle=False, drop_last=False)
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=self.collate_fn,
            num_workers=self.num_workers,
            sampler=sampler,
            pin_memory=True,
        )
        return test_loader

    def _configure_model(self):
        model = BevDetPrivate(self.backbone_conf, self.head_conf)
        model.class_names = self.class_names
        return model

    def _configure_optimizer(self):
        lr = self.basic_lr
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=1e-7)
        return optimizer

    def _configure_lr_scheduler(self):
        # TODO: Finetune training config.
        scheduler = StepLRScheduler(
            self.optimizer,
            self.basic_lr,
            len(self.train_dataloader),
            self.max_epoch,
            milestones=[16, 22],
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
