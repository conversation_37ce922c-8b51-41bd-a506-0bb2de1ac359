final_dim = (512, 1408)

bev_backbone = dict(
    type="ResNet",
    in_channels=80,
    depth=18,
    num_stages=3,
    strides=(1, 2, 2),
    dilations=(1, 1, 1),
    out_indices=[0, 1, 2],
    norm_eval=False,
    base_channels=160,
)

bev_neck = dict(
    type="SECONDFPN", in_channels=[80, 160, 320, 640], upsample_strides=[1, 2, 4, 8], out_channels=[64, 64, 64, 64]
)

backbone_conf = {
    "x_bound": [-51.2, 51.2, 0.8],
    "y_bound": [-64.0, 64.0, 0.8],
    "z_bound": [-5, 3, 8],
    "d_bound": [2.0, 58.0, 0.5],
    "final_dim": final_dim,
    "output_channels": 80,
    "downsample_factor": 16,
    "img_backbone_conf": dict(
        type="ResNet",
        depth=50,
        frozen_stages=-1,
        out_indices=[0, 1, 2, 3],
        norm_eval=False,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    "img_neck_conf": dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[128, 128, 128, 128],
    ),
    "depth_net_conf": dict(in_channels=512, mid_channels=512),
}

class_names = [
    "car",
    "bus",
    "bicycle",
    "pedestrian",
]

TASKS = [
    dict(num_class=1, class_names=["car"], task_name="car"),
    dict(num_class=1, class_names=["bus"], task_name="bus"),
    dict(num_class=1, class_names=["bicycle"], task_name="bicycle"),
    dict(num_class=1, class_names=["pedestrian"], task_name="pedestrian"),
]

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "bus",
    "工程车": "bus",
    "巴士": "bus",
    "摩托车": "bicycle",
    "自行车": "bicycle",
    "三轮车": "bicycle",
    "骑车人": "bicycle",
    "骑行的人": "bicycle",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "拖挂": "other",
    "遮挡": "occlude",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

bbox_coder = dict(
    type="CenterPointBBoxCoder",
    post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
    max_num=500,
    score_threshold=0.1,
    out_size_factor=4,
    voxel_size=[0.2, 0.2, 8],
    pc_range=[-51.2, -64, -5, 51.2, 64, 3],
    code_size=7,
)

train_cfg = dict(
    point_cloud_range=[-51.2, -64, -5, 51.2, 64, 3],
    grid_size=[512, 640, 1],
    voxel_size=[0.2, 0.2, 8],
    out_size_factor=4,
    dense_reg=1,
    gaussian_overlap=0.1,
    max_objs=500,
    min_radius=2,
    code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
)

test_cfg = dict(
    post_center_limit_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
    max_per_img=500,
    max_pool_nms=False,
    min_radius=[4, 12, 0.85, 1],
    score_threshold=0.1,
    out_size_factor=4,
    voxel_size=[0.2, 0.2, 8],
    nms_type="circle",
    pre_max_size=1000,
    post_max_size=83,
    nms_thr=0.2,
)

head_conf = {
    "bev_backbone_conf": bev_backbone,
    "bev_neck_conf": bev_neck,
    "tasks": TASKS,
    "common_heads": dict(reg=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2)),
    "bbox_coder": bbox_coder,
    "train_cfg": train_cfg,
    "test_cfg": test_cfg,
    "in_channels": 256,  # Equal to bev_neck output_channels.
    "loss_cls": dict(type="GaussianFocalLoss", reduction="mean"),
    "loss_bbox": dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    "gaussian_overlap": 0.1,
    "min_radius": 2,
}
