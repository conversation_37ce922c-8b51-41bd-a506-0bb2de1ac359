import copy

from perceptron.exps.camera_det.private._base_.base_pivate_data_cfg_wm2_256x704_5v import (
    base_dataset_cfg as wm2_cfg_base,
)

wm2_data_cfg = copy.deepcopy(wm2_cfg_base)
scale = 2
wm2_data_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (256 * scale, 704 * scale)
wm2_data_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.34 * scale, 0.55 * scale)
wm2_data_cfg["loader"]["datasets_names"] = ["det_tracking_18w"]

train_dataset_cfg = wm2_data_cfg

# config for eval dataset
val_dataset_cfg = copy.deepcopy(train_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["CHENGQU_BMK"])
val_dataset_cfg["annotation"].update(occlusion_threshold=1)
