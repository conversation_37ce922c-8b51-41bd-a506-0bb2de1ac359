# Private Dataset Experiments

记录私有数据集实验结果

## 目录结构
```
└── private
    ├── baseline            进化路线
    ├── playground          正在研究方向
    ├── archived            探索过的内容
    ├── release             发版模型
```
说明：实验分为四个类别，baseline（类似进化路线/天梯图）、playground（正在研究的方向），archived（所有已经探索过的内容），release（发版模型）。

## 基本原则
1\. 除release exp外尽可能采用继承方式，py文件里写明继承关系。
2\. 所有成熟的模型（baseline、release）需要有详细的运行命令、模型结果（详细数据保留在exp中）、ckpt和训练评测log放到统一的oss上。

## 存储地址
s3://camera-perceptron/model-zoo/
格式：

```
└── model-zoo
    └── <some exp>                         exp文件名
        ├── checkpoint_epoch_xx.pth        ckpt，一般是last epoch
        ├── train.log                      训练log
        ├── eval.log                       评测log
        ├── tfboard_vis_data.pkl           可视化pkl
```
