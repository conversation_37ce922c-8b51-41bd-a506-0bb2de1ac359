from perceptron.engine.cli import Det3DCli
from perceptron.exps.camera_det.private.baseline.bevdet_official.base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.camera_det.private.playground.fisheye.data_config_wm4_fisheye import (
    base_dataset_cfg as train_data_cfg,
)
from perceptron.exps.camera_det.private.playground.fisheye.data_config_wm4_fisheye import (
    val_dataset_cfg as val_data_cfg,
)

__all__ = ["Exp"]


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=8, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.basic_lr = 2e-4
        self.train_data_cfg = train_data_cfg
        self.val_data_cfg = val_data_cfg


if __name__ == "__main__":
    Det3DCli(Exp).run()
