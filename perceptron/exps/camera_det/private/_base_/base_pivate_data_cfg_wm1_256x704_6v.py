import copy

from perceptron.data.det3d.modules import *
from perceptron.data.det3d.private import *
from perceptron.data.det3d.source.config import WMCar2
from perceptron.exps.camera_det.private._base_ import (
    model_config_r50_512x1408_128x160 as model_config,
)

_CAR = WMCar2
_CAMERA_LIST = [
    "cam_back_left_120",
    "cam_front_right_120",
    "cam_back_right_120",
    "cam_front_left_120",
    "cam_front_120",
    "cam_front_60",
]
_SENSOR_NAMES = dict(
    camera_names=_CAMERA_LIST,
)
_CAMERA_UNDISTORT_FUNC = dict(
    cam_front_left_120=(UndistortStandard,),
    cam_front_right_120=(UndistortStandard,),
    cam_back_left_120=(UndistortStandard,),
    cam_back_right_120=(UndistortStandard,),
    cam_front_120=(UndistortStandard,),
    cam_front_60=(UndistortStandard,),
)
_PIPELINE = dict(
    ida_aug=dict(
        type=ImageAffineTransformation,
        aug_conf=dict(
            final_dim=(256, 704),
            resize_lim=(0.34, 0.55),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=True,
            rot_lim=(-5.4, 5.4),
        ),
        camera_names=_CAMERA_LIST,
        mode="train",
        img_norm=False,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
    bda_aug=dict(
        type=BevAffineTransformation,
        aug_conf=dict(
            rot_lim=(-22.5, 22.5),
            scale_lim=(0.95, 1.05),
            trans_lim=(-4, 4),
            flip_dx_ratio=0.5,
            flip_dy_ratio=0,
        ),
        mode="train",
    ),
)

base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    sensor_names=_SENSOR_NAMES,
    loader=dict(
        type=LoaderBase,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["train_6w_checked"],
        only_key_frame=True,
        rebuild=False,
    ),
    image=dict(
        type=ImageBase,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        undistort=True,
        undistort_func=_CAMERA_UNDISTORT_FUNC,
    ),
    annotation=dict(
        type=AnnotationDet,
        category_map=model_config.category_map,
        class_names=model_config.class_names,
        occlusion_threshold=2,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=True,
        filter_empty_frames=False,
    ),
    pipeline=_PIPELINE,
    evaluator=dict(
        type=EvaluationBase,
        category_map=model_config.category_map_reverse,
        dump_det_results=True,
    ),
)

train_dataset_cfg = [base_dataset_cfg]

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["WM_CAR1_BMK"])
val_dataset_cfg["annotation"].update(occlusion_threshold=1)
