"""
train:  DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=32 --gpu=8 --memory=204800 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_24e.py --no-clearml --sync_bn 2 -b 4 --amp

val: rlaunch --cpu=16 --gpu=2 --memory=20480 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_24e.py --ckpt [CKPT_PATH] --eval -b 2

test: rlaunch --cpu=16 --gpu=2 --memory=20480 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_24e.py --ckpt [CKPT_PATH] --eval -b 2  --exp_options mode=test --eval

mAP: 0.2785
mATE: 0.7679
mASE: 0.2805
mAOE: 0.6984
mAVE: 1.2208
mAAE: 0.3795
NDS: 0.3266
Eval time: 230.9s
Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.444   0.632   0.170   0.246   1.379   0.265
truck   0.235   0.772   0.231   0.280   1.409   0.374
bus     0.314   0.800   0.211   0.337   2.714   0.562
trailer 0.169   0.975   0.227   0.509   1.069   0.220
construction_vehicle    0.061   1.048   0.517   1.455   0.109   0.341
pedestrian      0.220   0.830   0.299   1.439   0.814   0.720
motorcycle      0.258   0.786   0.255   0.841   1.703   0.422
bicycle 0.249   0.632   0.267   0.983   0.570   0.132
traffic_cone    0.365   0.623   0.345   nan     nan     nan
barrier 0.471   0.582   0.283   0.197   nan     nan
"""

import torch
from torch.utils.data import DistributedSampler

from perceptron.data.detbev.dataset.nuscenes import NuscBevDetData, collate_fn
from perceptron.engine.cli import BaseCli
from perceptron.engine.executors import DetBevEvaluator
from perceptron.engine.executors.exports.bevdet_exports import BevDetExports
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.lr_scheduler import StepLRScheduler
from perceptron.models.detbev.bev_det import BevDet
from perceptron.utils import torch_dist as dist

__all__ = ["Exp"]

H = 900
W = 1600
final_dim = (256, 704)
img_conf = dict(img_mean=[123.675, 116.28, 103.53], img_std=[58.395, 57.12, 57.375], to_rgb=True)

backbone_conf = {
    "x_bound": [-51.2, 51.2, 0.8],
    "y_bound": [-51.2, 51.2, 0.8],
    "z_bound": [-5, 3, 8],
    "d_bound": [2.0, 58.0, 0.5],
    "final_dim": final_dim,
    "output_channels": 80,
    "downsample_factor": 16,
    "img_backbone_conf": dict(
        type="ResNet",
        depth=50,
        frozen_stages=0,
        out_indices=[0, 1, 2, 3],
        norm_eval=False,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    "img_neck_conf": dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[128, 128, 128, 128],
    ),
    "depth_net_conf": dict(in_channels=512, mid_channels=512),
}
ida_aug_conf = {
    "resize_lim": (0.386, 0.55),
    "final_dim": final_dim,
    "rot_lim": (-5.4, 5.4),
    "H": H,
    "W": W,
    "rand_flip": True,
    "bot_pct_lim": (0.0, 0.0),
    "cams": ["CAM_FRONT_LEFT", "CAM_FRONT", "CAM_FRONT_RIGHT", "CAM_BACK_LEFT", "CAM_BACK", "CAM_BACK_RIGHT"],
    "Ncams": 6,
}

bda_aug_conf = {"rot_lim": (-22.5, 22.5), "scale_lim": (0.95, 1.05), "flip_dx_ratio": 0.5, "flip_dy_ratio": 0.5}

bev_backbone = dict(
    type="ResNet",
    in_channels=80,
    depth=18,
    num_stages=3,
    strides=(1, 2, 2),
    dilations=(1, 1, 1),
    out_indices=[0, 1, 2],
    norm_eval=False,
    base_channels=160,
)

bev_neck = dict(
    type="SECONDFPN", in_channels=[80, 160, 320, 640], upsample_strides=[1, 2, 4, 8], out_channels=[64, 64, 64, 64]
)

CLASSES = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "trailer",
    "barrier",
    "motorcycle",
    "bicycle",
    "pedestrian",
    "traffic_cone",
]

TASKS = [
    dict(num_class=1, class_names=["car"]),
    dict(num_class=2, class_names=["truck", "construction_vehicle"]),
    dict(num_class=2, class_names=["bus", "trailer"]),
    dict(num_class=1, class_names=["barrier"]),
    dict(num_class=2, class_names=["motorcycle", "bicycle"]),
    dict(num_class=2, class_names=["pedestrian", "traffic_cone"]),
]

common_heads = dict(reg=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2))

bbox_coder = dict(
    type="CenterPointBBoxCoder",
    post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
    max_num=500,
    score_threshold=0.1,
    out_size_factor=4,
    voxel_size=[0.2, 0.2, 8],
    pc_range=[-51.2, -51.2, -5, 51.2, 51.2, 3],
    code_size=9,
)

train_cfg = dict(
    point_cloud_range=[-51.2, -51.2, -5, 51.2, 51.2, 3],
    grid_size=[512, 512, 1],
    voxel_size=[0.2, 0.2, 8],
    out_size_factor=4,
    dense_reg=1,
    gaussian_overlap=0.1,
    max_objs=500,
    min_radius=2,
    code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5],
)

test_cfg = dict(
    post_center_limit_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
    max_per_img=500,
    max_pool_nms=False,
    min_radius=[4, 12, 10, 1, 0.85, 0.175],
    score_threshold=0.1,
    out_size_factor=4,
    voxel_size=[0.2, 0.2, 8],
    nms_type="circle",
    pre_max_size=1000,
    post_max_size=83,
    nms_thr=0.2,
)

head_conf = {
    "bev_backbone_conf": bev_backbone,
    "bev_neck_conf": bev_neck,
    "tasks": TASKS,
    "common_heads": common_heads,
    "bbox_coder": bbox_coder,
    "train_cfg": train_cfg,
    "test_cfg": test_cfg,
    "in_channels": 256,  # Equal to bev_neck output_channels.
    "loss_cls": dict(type="GaussianFocalLoss", reduction="mean"),
    "loss_bbox": dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    "gaussian_overlap": 0.1,
    "min_radius": 2,
}


class Exp(BaseExp):
    def __init__(
        self,
        data_root="data/nuscenes",
        eval_interval=1,
        batch_size_per_device=8,
        total_devices=1,
        max_epoch=24,
        env=None,
        class_names=CLASSES,
        backbone_conf=backbone_conf,
        head_conf=head_conf,
        ida_aug_conf=ida_aug_conf,
        bda_aug_conf=bda_aug_conf,
        pos_weight=2.13,
        max_grad_norm=5.0,
        dump_interval=1,
        **kwargs
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, env)
        self.eval_interval = eval_interval
        self.data_root = data_root
        self.pos_weight = pos_weight
        self.basic_lr_per_img = 2e-4 / 64
        self.class_names = class_names
        self.backbone_conf = backbone_conf
        self.head_conf = head_conf
        self.ida_aug_conf = ida_aug_conf
        self.bda_aug_conf = bda_aug_conf
        self.grad_clip_value = max_grad_norm
        self.dump_interval = dump_interval
        self.eval_executor_class = DetBevEvaluator
        self.export_executor_class = BevDetExports
        self.model_class = BevDet
        self.mode = "valid"
        self.img_conf = img_conf
        self.data_use_cbgs = False
        self.num_sweeps = 1
        self.sweeps_idx = []
        self.key_idxes = []
        self.data_return_depth = False
        self.model_use_ema = False

    def training_step(self, batch):
        (
            sweep_imgs,
            mats,
            _,
            _,
            gt_boxes,
            gt_labels,
        ) = batch
        if torch.cuda.is_available():
            for key, value in mats.items():
                mats[key] = value.cuda()
            sweep_imgs = sweep_imgs.cuda()
            gt_boxes = [gt_box.cuda() for gt_box in gt_boxes]
            gt_labels = [gt_label.cuda() for gt_label in gt_labels]
        preds = self.model(sweep_imgs, mats)
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            targets = self.model.module.get_targets(gt_boxes, gt_labels)
            loss = self.model.module.loss(targets, preds)
        else:
            targets = self.model.get_targets(gt_boxes, gt_labels)
            loss = self.model.loss(targets, preds)
        return loss

    def test_step(self, batch):
        (sweep_imgs, mats, _, img_metas, _, _) = batch
        if torch.cuda.is_available():
            for key, value in mats.items():
                mats[key] = value.cuda()
            sweep_imgs = sweep_imgs.cuda()
        preds = self.model(sweep_imgs, mats)
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            results = self.model.module.get_bboxes(preds, img_metas)
        else:
            results = self.model.get_bboxes(preds, img_metas)
        for i in range(len(results)):
            results[i][0] = results[i][0].tensor.detach().cpu().numpy()
            results[i][1] = results[i][1].detach().cpu().numpy()
            results[i][2] = results[i][2].detach().cpu().numpy()
        return results

    def _configure_callbacks(self):
        callbacks_list = list()
        if self.model_use_ema:
            # from perceptron.engine.callbacks.checknan_callback import CheckNan
            from perceptron.engine.callbacks.model_ema_callback import EMACallback

            callbacks_list.extend([EMACallback()])
        return callbacks_list

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_data = NuscBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            use_cbgs=self.data_use_cbgs,
            img_conf=self.img_conf,
            num_sweeps=self.num_sweeps,
            sweeps_idx=self.sweeps_idx,
            key_idxes=self.key_idxes,
            return_depth=self.data_return_depth,
        )
        from functools import partial

        train_loader = torch.utils.data.DataLoader(
            train_data,
            batch_size=self.batch_size_per_device,
            num_workers=4,
            drop_last=True,
            shuffle=False,
            collate_fn=partial(collate_fn, is_return_depth=self.data_return_depth),
            sampler=InfiniteSampler(len(train_data), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_data = NuscBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            img_conf=self.img_conf,
            data_split="validation_12hz",
            num_sweeps=self.num_sweeps,
            key_idxes=self.key_idxes,
            sweeps_idx=self.sweeps_idx,
        )
        if self.env == "local":
            sampler = None
        else:
            sampler = DistributedSampler(val_data, shuffle=False, drop_last=False)
        val_loader = torch.utils.data.DataLoader(
            val_data,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=4,
            sampler=sampler,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_data = NuscBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            img_conf=self.img_conf,
            data_split="testing_12hz",
            num_sweeps=self.num_sweeps,
            key_idxes=self.key_idxes,
            sweeps_idx=self.sweeps_idx,
        )
        if self.env == "local":
            sampler = None
        else:
            sampler = DistributedSampler(test_data, shuffle=False, drop_last=False)
        test_loader = torch.utils.data.DataLoader(
            test_data,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=4,
            sampler=sampler,
        )
        return test_loader

    def _configure_model(self):
        model = self.model_class(self.backbone_conf, self.head_conf)
        return model

    def _configure_optimizer(self):
        lr = self.basic_lr_per_img * self.batch_size_per_device * self.total_devices
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=1e-7)
        return optimizer

    def _configure_lr_scheduler(self):
        # TODO: Finetune training config.
        if self.model_use_ema:
            from perceptron.layers.lr_scheduler import ConstantLRScheduler

            scheduler = ConstantLRScheduler(
                self.optimizer,
                self.basic_lr_per_img * self.batch_size_per_device * self.total_devices,
                len(self.train_dataloader),
                self.max_epoch,
            )
        else:
            scheduler = StepLRScheduler(
                self.optimizer,
                self.basic_lr_per_img * self.batch_size_per_device * self.total_devices,
                len(self.train_dataloader),
                self.max_epoch,
                milestones=[19, 23],
            )
        return scheduler


if __name__ == "__main__":
    BaseCli(Exp).run()
