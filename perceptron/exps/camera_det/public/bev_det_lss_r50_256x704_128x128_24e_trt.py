"""
python3 perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_24e_trt.py --ckpt [model.pth] --export --env local
"""
import torch
from perceptron_ops.voxel_pooling import voxel_pooling

from perceptron.engine.cli import BaseCli
from perceptron.exps.detbev.bev_det_lss_r50_256x704_128x128_24e import Exp
from perceptron.layers.blocks_3d.mmdet3d.lss_fpn import LSSFPN
from perceptron.models.detbev.bev_det import BevDet, BevDetHead


class LSSFPNTRT(LSSFPN):
    def get_geometry(self, sensor2ego_mat, inverse_intrin_mat, inverse_ida_mat, bda_mat):
        """Transfer points from camera coord to ego coord.

        Args:
            rots(Tensor): Rotation matrix from camera to ego.
            trans(Tensor): Translation matrix from camera to ego.
            intrins(Tensor): Intrinsic matrix.
            post_rots_ida(Tensor): Rotation matrix for ida.
            post_trans_ida(Tensor): Translation matrix for ida
            post_rot_bda(Tensor): Rotation matrix for bda.

        Returns:
            Tensors: points ego coord.
        """
        batch_size, num_cams, _, _ = sensor2ego_mat.shape

        # undo post-transformation
        # B x N x D x H x W x 3
        points = self.frustum
        inverse_ida_mat = inverse_ida_mat.view(batch_size, num_cams, 1, 1, 1, 4, 4)
        points = inverse_ida_mat.matmul(points.unsqueeze(-1))

        # cam_to_ego
        points = torch.cat((points[:, :, :, :, :, :2] * points[:, :, :, :, :, 2:3], points[:, :, :, :, :, 2:]), 5)

        combine = sensor2ego_mat.matmul(inverse_intrin_mat)
        points = combine.view(batch_size, num_cams, 1, 1, 1, 4, 4).matmul(points)
        if bda_mat is not None:
            bda_mat = bda_mat.unsqueeze(1).repeat(1, num_cams, 1, 1).view(batch_size, num_cams, 1, 1, 1, 4, 4)
            out_points = torch.matmul(bda_mat, points).squeeze(-1)
        return out_points[..., :3]

    def get_cam_feats(self, imgs):
        """Get feature maps from images."""
        batch_size, num_sweeps, num_cams, num_channels, imH, imW = imgs.shape

        imgs = imgs.flatten().view(batch_size * num_sweeps * num_cams, num_channels, imH, imW)
        img_feats = self.img_neck(self.img_backbone(imgs))[0]
        img_feats = img_feats.reshape(
            batch_size, num_sweeps, num_cams, img_feats.shape[1], img_feats.shape[2], img_feats.shape[3]
        )
        return img_feats

    def _forward_depth_net(self, depth_net_index, feat, *args, **kwargs):
        return self.depth_net[depth_net_index](feat)

    def _forward_voxel_net(self, img_feat_with_depth):
        return img_feat_with_depth

    @torch.no_grad()
    def _forward_single_sweep(self, sweep_index, sweep_imgs, mats_dict, is_return_depth=False):
        batch_size, num_sweeps, num_cams, num_channels, img_height, img_width = sweep_imgs.shape
        img_feats = self.get_cam_feats(sweep_imgs)
        source_features = img_feats[:, 0, ...]
        depth_feature = self._forward_depth_net(
            0,
            source_features.reshape(
                batch_size * num_cams, source_features.shape[2], source_features.shape[3], source_features.shape[4]
            ),
            mats_dict,
            sweep_index,
        )
        depth = depth_feature[:, : self.depth_channels].softmax(1)
        img_feat_with_depth = depth.unsqueeze(1) * depth_feature[
            :, self.depth_channels : (self.depth_channels + self.output_channels)
        ].unsqueeze(2)

        img_feat_with_depth = self._forward_voxel_net(img_feat_with_depth)

        img_feat_with_depth = img_feat_with_depth.reshape(
            batch_size,
            num_cams,
            img_feat_with_depth.shape[1],
            img_feat_with_depth.shape[2],
            img_feat_with_depth.shape[3],
            img_feat_with_depth.shape[4],
        )
        img_feat_with_depth = img_feat_with_depth.permute(0, 1, 3, 4, 5, 2)

        geom_xyz = self.get_geometry(
            mats_dict["sensor2ego_mats"][:, sweep_index, ...],
            mats_dict["intrin_mats"][:, sweep_index, ...],
            mats_dict["ida_mats"][:, sweep_index, ...],
            mats_dict.get("bda_mat", None),
        )

        geom_xyz = (geom_xyz - (self.voxel_coord - self.voxel_size / 2.0)).cuda()
        geom_xyz = (geom_xyz / self.voxel_size).int()

        geom_xyz = geom_xyz.reshape(batch_size, -1, 3)
        img_feat_with_depth = img_feat_with_depth.reshape(batch_size, -1, self.output_channels)
        feature_map = voxel_pooling(geom_xyz, img_feat_with_depth.contiguous(), self.voxel_num.tolist())
        return feature_map.contiguous()


class BevDetTRT(BevDet):
    def __init__(self, backbone_conf, head_conf, is_train_depth=False):
        super(BevDet, self).__init__()
        self.backbone = LSSFPNTRT(**backbone_conf)
        self.head = BevDetHead(**head_conf)
        self.is_train_depth = is_train_depth


class Exp(Exp):
    def __init__(self, batch_size_per_device=8, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(
            batch_size_per_device=batch_size_per_device, total_devices=total_devices, max_epoch=max_epoch
        )
        self.model_class = BevDetTRT

    def export_onnx(self, output_path):
        """transfer torch model to onnx model.
        Args:
            model: traced model.
            output_path (str): export path of onnx model.
        """
        num_sweeps = 1

        inputs = torch.randn(1, num_sweeps, 6, 3, 256, 704)
        sensor2ego_mats = torch.randn(1, num_sweeps, 6, 4, 4)
        inverse_intrin_mats = torch.randn(1, num_sweeps, 6, 4, 4)
        inverse_ida_mats = torch.randn(1, num_sweeps, 6, 4, 4)
        bda_mat = torch.randn(1, 4, 4)
        mats_dict = {
            "sensor2ego_mats": sensor2ego_mats.cuda(),
            "intrin_mats": inverse_intrin_mats.cuda(),
            "ida_mats": inverse_ida_mats.cuda(),
            "bda_mat": bda_mat.cuda(),
        }

        args = (inputs.cuda(), mats_dict, {})

        with open(output_path, "wb") as f:
            torch.onnx.export(
                self.model.cuda(),
                args,
                f,
                opset_version=11,
                do_constant_folding=True,
                input_names=["images", "sensor2ego_mats", "intrin_mats", "ida_mats", "bda_mat"],
                enable_onnx_checker=False,
            )


if __name__ == "__main__":
    BaseCli(Exp).run()
