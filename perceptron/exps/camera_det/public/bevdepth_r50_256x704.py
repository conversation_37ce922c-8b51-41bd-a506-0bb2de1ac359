"""
train: python3 perceptron/exps/detbev/bevdepth_r50_256x704.py -b 8 --amp --no-clearml

resutls:
mAP: 0.3219
mATE: 0.7070
mASE: 0.2766
mAOE: 0.6361
mAVE: 1.1110
mAAE: 0.3206
NDS: 0.3669
Eval time: 162.8s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.495   0.543   0.165   0.198   1.245   0.241
truck   0.262   0.723   0.219   0.218   1.214   0.281
bus     0.380   0.675   0.215   0.194   2.191   0.367
trailer 0.192   1.092   0.224   0.522   1.140   0.220
construction_vehicle    0.072   1.053   0.512   1.270   0.107   0.381
pedestrian      0.264   0.756   0.299   1.471   0.830   0.729
motorcycle      0.316   0.669   0.253   0.758   1.613   0.220
bicycle 0.298   0.543   0.258   0.907   0.549   0.126
traffic_cone    0.434   0.525   0.346   nan     nan     nan
barrier 0.506   0.491   0.274   0.188   nan     nan
"""
import torch
from mmdet.models.backbones.resnet import BasicBlock
from torch import nn as nn
from torch.cuda.amp import autocast
from torch.nn import functional as F

from perceptron.engine.cli import BaseCli
from perceptron.exps.detbev.bev_det_lss_r50_256x704_128x128_24e import Exp as BaseExp
from perceptron.layers.blocks_3d.mmdet3d.lss_fpn import LSSFPN as BaseLSSFPN
from perceptron.layers.head.mmdet3d.bevdet_head import BevDetHead
from perceptron.models.detbev.bev_det import BevDet as BaseBEVDet


# ----------- using ASPP module to enlarge the receptive field for depth-prediction----------------------------
# todo(<EMAIL>): aspp module may be inefficient
class _ASPPModule(nn.Module):
    def __init__(self, inplanes, planes, kernel_size, padding, dilation, BatchNorm):
        super(_ASPPModule, self).__init__()
        self.atrous_conv = nn.Conv2d(
            inplanes, planes, kernel_size=kernel_size, stride=1, padding=padding, dilation=dilation, bias=False
        )
        self.bn = BatchNorm(planes)
        self.relu = nn.ReLU()

        self._init_weight()

    def forward(self, x):
        x = self.atrous_conv(x)
        x = self.bn(x)

        return self.relu(x)

    def _init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                torch.nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()


class ASPP(nn.Module):
    def __init__(self, inplanes, mid_channels=256, BatchNorm=nn.BatchNorm2d):
        super(ASPP, self).__init__()

        dilations = [1, 6, 12, 18]

        self.aspp1 = _ASPPModule(inplanes, mid_channels, 1, padding=0, dilation=dilations[0], BatchNorm=BatchNorm)
        self.aspp2 = _ASPPModule(
            inplanes, mid_channels, 3, padding=dilations[1], dilation=dilations[1], BatchNorm=BatchNorm
        )
        self.aspp3 = _ASPPModule(
            inplanes, mid_channels, 3, padding=dilations[2], dilation=dilations[2], BatchNorm=BatchNorm
        )
        self.aspp4 = _ASPPModule(
            inplanes, mid_channels, 3, padding=dilations[3], dilation=dilations[3], BatchNorm=BatchNorm
        )

        self.global_avg_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Conv2d(inplanes, mid_channels, 1, stride=1, bias=False),
            BatchNorm(mid_channels),
            nn.ReLU(),
        )
        self.conv1 = nn.Conv2d(int(mid_channels * 5), mid_channels, 1, bias=False)
        self.bn1 = BatchNorm(mid_channels)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.5)
        self._init_weight()

    def forward(self, x):
        x1 = self.aspp1(x)
        x2 = self.aspp2(x)
        x3 = self.aspp3(x)
        x4 = self.aspp4(x)
        x5 = self.global_avg_pool(x)
        x5 = F.interpolate(x5, size=x4.size()[2:], mode="bilinear", align_corners=True)
        x = torch.cat((x1, x2, x3, x4, x5), dim=1)

        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)

        return self.dropout(x)

    def _init_weight(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                torch.nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()


# ------------------------ using MLP and SElayer to encode intrinsics ------------------------
class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.ReLU, drop=0.0):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.drop1 = nn.Dropout(drop)
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop2 = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop1(x)
        x = self.fc2(x)
        x = self.drop2(x)
        return x


class SELayer(nn.Module):
    def __init__(self, channels, act_layer=nn.ReLU, gate_layer=nn.Sigmoid):
        super().__init__()
        self.conv_reduce = nn.Conv2d(channels, channels, 1, bias=True)
        self.act1 = act_layer()
        self.conv_expand = nn.Conv2d(channels, channels, 1, bias=True)
        self.gate = gate_layer()

    def forward(self, x, x_se):
        x_se = self.conv_reduce(x_se)
        x_se = self.act1(x_se)
        x_se = self.conv_expand(x_se)
        return x * self.gate(x_se)


# --------- using a residual-block to enhance the FrustumBev feature ---------------------- #
class FrustumBevNet(nn.Module):
    """
    feature extraction in frustum-bev view
    """

    def __init__(self, in_channels, mid_channels, out_channels):
        super(FrustumBevNet, self).__init__()

        self.reduce_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
        )

        self.conv = nn.Sequential(
            nn.Conv2d(mid_channels, mid_channels, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, mid_channels, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
        )

        self.out_conv = nn.Sequential(
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=True),
            # nn.BatchNorm3d(out_channels),
            # nn.ReLU(inplace=True),
        )

    def forward(self, x):
        x = self.reduce_conv(x)
        x = self.conv(x) + x
        x = self.out_conv(x)
        return x


class DepthNet(nn.Module):
    def __init__(self, in_channels, mid_channels, context_channels, depth_channels):
        super(DepthNet, self).__init__()
        self.reduce_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
        )
        self.context_conv = nn.Conv2d(mid_channels, context_channels, kernel_size=1, stride=1, padding=0)
        self.mlp = Mlp(1, mid_channels, mid_channels)
        self.se = SELayer(mid_channels)  # NOTE: add camera-aware
        self.depth_conv = nn.Sequential(
            BasicBlock(mid_channels, mid_channels),
            BasicBlock(mid_channels, mid_channels),
            BasicBlock(mid_channels, mid_channels),
        )
        self.aspp = ASPP(mid_channels)
        self.depth_pred = nn.Conv2d(mid_channels, depth_channels, kernel_size=1, stride=1, padding=0)

    def forward(self, x, sweep_intrins, sweep_post_rots_ida, scale_depth_factor=1000.0):
        x = self.reduce_conv(x)
        context = self.context_conv(x)
        inv_intrinsics = torch.inverse(sweep_intrins)
        pixel_size = torch.norm(
            torch.stack([inv_intrinsics[..., 0, 0], inv_intrinsics[..., 1, 1]], dim=-1), dim=-1
        ).reshape(-1, 1)
        aug_scale = torch.sqrt(sweep_post_rots_ida[..., 0, 0] ** 2 + sweep_post_rots_ida[..., 0, 1] ** 2).reshape(-1, 1)
        scaled_pixel_size = pixel_size * scale_depth_factor / aug_scale
        x_se = self.mlp(scaled_pixel_size)[..., None, None]
        x = self.se(x, x_se)
        x = self.depth_conv(x)
        x = self.aspp(x)
        depth = self.depth_pred(x)
        return torch.cat([depth, context], dim=1)


class LSSFPN(BaseLSSFPN):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.frustum_bev_net = FrustumBevNet(self.output_channels, self.output_channels, self.output_channels)

    def _forward_voxel_net(self, img_feat_with_depth):
        # BEVConv2D [n, c, d, h, w] -> [n, h, c, w, d]
        img_feat_with_depth = img_feat_with_depth.permute(
            0, 3, 1, 4, 2
        ).contiguous()  # [n, c, d, h, w] -> [n, h, c, w, d]
        n, h, c, w, d = img_feat_with_depth.shape
        img_feat_with_depth = img_feat_with_depth.view(-1, c, w, d)
        img_feat_with_depth = (
            self.frustum_bev_net(img_feat_with_depth).view(n, h, c, w, d).permute(0, 2, 4, 1, 3).contiguous().float()
        )
        return img_feat_with_depth

    def _configure_depth_net(self, depth_net_conf):
        return nn.Sequential(
            DepthNet(
                depth_net_conf["in_channels"], depth_net_conf["mid_channels"], self.output_channels, self.depth_channels
            )
        )

    def _forward_depth_net(self, feat, *args, **kwargs):
        mats_dict = args[0]
        sweep_index = args[1]
        return self.depth_net[0](
            feat,
            mats_dict["intrin_mats"][:, sweep_index : sweep_index + 1, ...],
            mats_dict["ida_mats"][..., :2, :2][:, sweep_index : sweep_index + 1, ...],
        )


class BEVDet(BaseBEVDet):
    def __init__(self, backbone_conf, head_conf, is_train_depth=True):
        super(BaseBEVDet, self).__init__()
        self.backbone = LSSFPN(**backbone_conf)
        self.head = BevDetHead(**head_conf)
        self.is_train_depth = is_train_depth


class Exp(BaseExp):
    def __init__(self, **kwargs):
        super(Exp, self).__init__(**kwargs)
        self.backbone_conf.update({"depth_net_conf": dict(in_channels=512, mid_channels=256)})
        self.model_class = BEVDet
        self.model_use_ema = True

        # ----------- for depth loss training ------------------ #
        self.data_return_depth = True
        self.downsample_factor = self.backbone_conf["downsample_factor"]
        self.dbound = self.backbone_conf["d_bound"]
        self.depth_channels = int((self.dbound[1] - self.dbound[0]) / self.dbound[2])

    def training_step(self, batch):
        (sweep_imgs, mats, _, _, gt_boxes, gt_labels, depth_labels) = batch
        if torch.cuda.is_available():
            for key, value in mats.items():
                mats[key] = value.cuda()
            sweep_imgs = sweep_imgs.cuda()
            gt_boxes = [gt_box.cuda() for gt_box in gt_boxes]
            gt_labels = [gt_label.cuda() for gt_label in gt_labels]
        preds, depth_preds = self.model(sweep_imgs, mats)
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            targets = self.model.module.get_targets(gt_boxes, gt_labels)
            loss = self.model.module.loss(targets, preds)
        else:
            targets = self.model.get_targets(gt_boxes, gt_labels)
            loss = self.model.loss(targets, preds)

        if len(depth_labels.shape) == 5:
            # only key-frame will calculate depth loss
            depth_labels = depth_labels[:, 0, ...]
        depth_loss = self.get_depth_loss(depth_labels.cuda(), depth_preds)

        return loss + depth_loss

    def get_depth_loss(self, depth_labels, depth_preds):
        depth_labels = self._get_downsampled_gt_depth(depth_labels)
        depth_preds = depth_preds.permute(0, 2, 3, 1).contiguous().view(-1, self.depth_channels)
        fg_mask = torch.max(depth_labels, dim=1).values > 0.0

        with autocast(enabled=False):
            depth_loss = (
                F.binary_cross_entropy(
                    depth_preds[fg_mask],
                    depth_labels[fg_mask],
                    reduction="none",
                ).sum()
                / max(1.0, fg_mask.sum())
            )

        return 3.0 * depth_loss

    def _get_downsampled_gt_depth(self, gt_depths):
        """
        Input:
            gt_depths: [B, N, H, W]
        Output:
            gt_depths: [B*N*h*w, d]
        """
        B, N, H, W = gt_depths.shape
        gt_depths = gt_depths.view(
            B * N,
            H // self.downsample_factor,
            self.downsample_factor,
            W // self.downsample_factor,
            self.downsample_factor,
            1,
        )
        gt_depths = gt_depths.permute(0, 1, 3, 5, 2, 4).contiguous()
        gt_depths = gt_depths.view(-1, self.downsample_factor * self.downsample_factor)
        gt_depths_tmp = torch.where(gt_depths == 0.0, 1e5 * torch.ones_like(gt_depths), gt_depths)
        gt_depths = torch.min(gt_depths_tmp, dim=-1).values
        gt_depths = gt_depths.view(B * N, H // self.downsample_factor, W // self.downsample_factor)

        gt_depths = (gt_depths - (self.dbound[0] - self.dbound[2])) / self.dbound[2]
        gt_depths = torch.where(
            (gt_depths < self.depth_channels + 1) & (gt_depths >= 0.0), gt_depths, torch.zeros_like(gt_depths)
        )
        gt_depths = F.one_hot(gt_depths.long(), num_classes=self.depth_channels + 1).view(-1, self.depth_channels + 1)[
            :, 1:
        ]

        return gt_depths.float()


if __name__ == "__main__":
    BaseCli(Exp).run()
