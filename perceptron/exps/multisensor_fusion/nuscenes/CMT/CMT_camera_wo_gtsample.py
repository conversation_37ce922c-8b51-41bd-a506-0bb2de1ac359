# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --topo-group yes --i-know-i-am-wasting-resource --max-wait-duration 48h --cpu=32 --gpu=8 --memory=250000 --positive-tags 2080ti --preemptible no -- python3 perceptron/exps/multisensor_fusion/nuscenes/CMT/CMT_camera_wo_gtsample.py -b 2 -e 20 --sync_bn 1 --no-clearml --amp --pretrained_model s3://zdk/per_remapping_pretrained_ckpt/nuim_r50_remapping.pth
from perceptron.engine.cli import Det3DCli
from perceptron.exps.multisensor_fusion.nuscenes.CMT.CMT_fusion import Exp as BaseExp


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_cfg.train.pipeline.pop("gt_sampling")
        self.data_cfg.train.loader.pop("lidar_dict")
        self.data_cfg.val.loader.pop("lidar_dict")
        self.model_cfg.pop("lidar_encoder")
        self.model_cfg["det_head"]["modal"] = "Camera"
        self._change_cfg_params()

    def _change_cfg_params(self):
        pass


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
