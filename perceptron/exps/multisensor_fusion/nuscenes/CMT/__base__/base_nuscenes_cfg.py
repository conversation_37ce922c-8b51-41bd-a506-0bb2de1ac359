from perceptron.data.det3d.modules.pipelines import (
    BevAffineTransformation,
    PubicImageAffineTransformation as ImageAffineTransformation,
    PointShuffle,
    ObjectRangeFilter,
    UnifiedObjectSample,
)
from perceptron.data.det3d.modules.loader import NuscenesLoader

_POINT_CLOUD_RANGE = [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
_IMG_DIM = (256, 704)

CLASS_NAMES = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "trailer",
    "barrier",
    "motorcycle",
    "bicycle",
    "pedestrian",
    "traffic_cone",
]

_TRAIN_PIPELINE = dict(
    gt_sampling=dict(
        type=UnifiedObjectSample,
        sample_2d=True,
        # stop_epoch = 16,
        db_sampler=dict(
            # type='UnifiedDataBaseSampler',
            data_root="",
            info_path="s3://zdk/datasets/nuscenes_unified_dbinfos_train_nori.pkl",
            rate=1.0,
            prepare=dict(
                filter_by_difficulty=[-1],
                filter_by_min_points=dict(
                    car=5,
                    truck=5,
                    bus=5,
                    trailer=5,
                    construction_vehicle=5,
                    traffic_cone=5,
                    barrier=5,
                    motorcycle=5,
                    bicycle=5,
                    pedestrian=5,
                ),
            ),
            classes=CLASS_NAMES,
            sample_groups=dict(
                car=2,
                truck=3,
                construction_vehicle=7,
                bus=4,
                trailer=6,
                barrier=2,
                motorcycle=6,
                bicycle=6,
                pedestrian=2,
                traffic_cone=2,
            ),
            points_loader=dict(
                # type='LoadPointsFromFile',
                coord_type="LIDAR",
                load_dim=5,
                use_dim=[0, 1, 2, 3, 4],
            ),
        ),
    ),
    ida=dict(
        type=ImageAffineTransformation,
        aug_conf=dict(
            final_dim=(256, 704),
            resize_lim=(0.34, 0.55),
            bot_pct_lim=(0.0, 0.0),
            H=900,
            W=1600,
            rand_flip=True,
            rot_lim=(0.0, 0.0),
        ),
        img_norm=True,
        img_conf=dict(img_mean=[103.530, 116.280, 123.675], img_std=[57.375, 57.120, 58.395], to_rgb=True),
    ),
    bda=dict(
        type=BevAffineTransformation,
        aug_conf=dict(
            rot_lim=(-22.5 * 2, 22.5 * 2),
            scale_lim=(0.90, 1.10),
            trans_lim=(-0.5, 0.5),
            flip_dx_ratio=0.5,
            flip_dy_ratio=0.5,
        ),
        with_trans_z=True,
    ),
    shuffle=dict(
        type=PointShuffle,
    ),
    filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=_POINT_CLOUD_RANGE,
    ),
)

_TEST_PIPELINE = dict(
    ida=dict(
        type=ImageAffineTransformation,
        aug_conf=dict(
            final_dim=(256, 704),
            resize_lim=(0.34, 0.55),
            bot_pct_lim=(0.0, 0.0),
            H=900,
            W=1600,
            rand_flip=False,
            rot_lim=(0.0, 0.0),
        ),
        img_norm=True,
        img_conf=dict(img_mean=[103.530, 116.280, 123.675], img_std=[57.375, 57.120, 58.395], to_rgb=True),
    ),
)

DATA_CFG = dict(
    train=dict(
        mode="train",
        class_names=CLASS_NAMES,
        use_cbgs=True,
        loader=dict(
            type=NuscenesLoader,
            data_split="training",
            lidar_dict=dict(
                load_dim=5,
                use_dim=5,
                num_sweeps=10,
            ),
            camera_dict=dict(),
            radar_dict=dict(
                load_dim=18,
                max_points_num=1500,
            ),
        ),
        pipeline=_TRAIN_PIPELINE,
    ),
    val=dict(
        mode="test",
        class_names=CLASS_NAMES,
        loader=dict(
            type=NuscenesLoader,
            data_split="validation",
            lidar_dict=dict(
                load_dim=5,
                use_dim=5,
                num_sweeps=10,
            ),
            camera_dict=dict(),
            radar_dict=dict(
                load_dim=18,
                max_points_num=1500,
            ),
        ),
        pipeline=_TEST_PIPELINE,
    ),
)
