from .base_nuscenes_cfg import CLASS_NAMES


VOXEL0075_POINT_CLOUD_RANGE = [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
VOXEL0075_VOXEL_SIZE = [0.075, 0.075, 0.2]
VOXEL0075_GRID_SIZE = [1440, 1440, 40]
VOXEL0075_OUT_SIZE_FACTOR = 8


VOXEL0075_LIDAR_ENCODER = dict(
    pts_voxel_layer=dict(
        max_num_points=10,
        voxel_size=VOXEL0075_VOXEL_SIZE,
        max_voxels=(120000, 160000),
        point_cloud_range=VOXEL0075_POINT_CLOUD_RANGE,
        src_num_point_features=5,
        use_num_point_features=5,
    ),
    pts_voxel_encoder=dict(
        type="HardSimpleVFE",
        num_features=5,
    ),
    pts_middle_encoder=dict(
        type="SparseEncoder",
        in_channels=5,
        sparse_shape=[41, 1440, 1440],
        output_channels=128,
        order=("conv", "norm", "act"),
        encoder_channels=((16, 16, 32), (32, 32, 64), (64, 64, 128), (128, 128)),
        encoder_paddings=((0, 0, 1), (0, 0, 1), (0, 0, [0, 1, 1]), (0, 0)),
        block_type="basicblock",
    ),
    pts_backbone=dict(
        type="SECOND",
        in_channels=256,
        out_channels=[128, 256],
        layer_nums=[5, 5],
        layer_strides=[1, 2],
        norm_cfg=dict(type="BN", eps=0.001, momentum=0.01),
        conv_cfg=dict(type="Conv2d", bias=False),
    ),
    pts_neck=dict(
        type="SECONDFPN",
        in_channels=[128, 256],
        out_channels=[256, 256],
        upsample_strides=[1, 2],
        norm_cfg=dict(type="BN", eps=0.001, momentum=0.01),
        upsample_cfg=dict(type="deconv", bias=False),
        use_conv_for_no_stride=True,
    ),
)
VOXEL0075_DET_HEAD = dict(
    type="CMTTransformer",
    in_channels=512,
    num_query=900,
    modal="Fusion",
    depth_num=64,
    hidden_dim=256,
    downsample_scale=VOXEL0075_OUT_SIZE_FACTOR,
    grid_size=VOXEL0075_GRID_SIZE,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=10,
            class_names=[
                "car",
                "truck",
                "construction_vehicle",
                "bus",
                "trailer",
                "barrier",
                "motorcycle",
                "bicycle",
                "pedestrian",
                "traffic_cone",
            ],
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
        pc_range=VOXEL0075_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=VOXEL0075_VOXEL_SIZE,
        num_classes=10,
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=3),
    transformer=dict(
        type="CMTTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=6,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="PETRMultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
)

VOXEL0075_TRAIN_CFG = dict(
    pts=dict(
        dataset="nuScenes",
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=VOXEL0075_POINT_CLOUD_RANGE,
        ),
        pos_weight=-1,
        gaussian_overlap=0.1,
        min_radius=2,
        grid_size=VOXEL0075_GRID_SIZE,  # [x_len, y_len, 1]
        voxel_size=VOXEL0075_VOXEL_SIZE,
        out_size_factor=VOXEL0075_OUT_SIZE_FACTOR,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        point_cloud_range=VOXEL0075_POINT_CLOUD_RANGE,
    )
)

VOXEL0075_TEST_CFG = dict(
    pts=dict(
        dataset="nuScenes",
        grid_size=VOXEL0075_GRID_SIZE,
        out_size_factor=VOXEL0075_OUT_SIZE_FACTOR,
        pc_range=VOXEL0075_POINT_CLOUD_RANGE[0:2],
        voxel_size=VOXEL0075_VOXEL_SIZE[:2],
        nms_type=None,
    )
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        out_indices=(2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=False,  # True,
        # init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50")
    ),
    img_neck=dict(
        # type='FPN',
        in_channels=[1024, 2048],
        out_channels=256,
        num_outs=2,
        # with_cp=True,
    ),
)

MODEL_CFG = dict(
    class_names=CLASS_NAMES,
    grid_mask=True,
    lidar_encoder=VOXEL0075_LIDAR_ENCODER,
    camera_encoder=CAMERA_ENCODER_CFG,
    det_head=VOXEL0075_DET_HEAD,
    train_cfg=VOXEL0075_TRAIN_CFG,
    test_cfg=VOXEL0075_TEST_CFG,
)
