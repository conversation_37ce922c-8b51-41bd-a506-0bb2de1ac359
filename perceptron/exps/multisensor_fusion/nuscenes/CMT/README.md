## Welcome to Multi-Modal Perception

+ Release Experiment Record On `nuScenes Dataset`.

## Results and Models

<table>
    <tr>
        <th width=20%>Model</th>
        <th width=5%>Modal</th>
        <th width=5%>P/B/Epoch</th>
        <th width=10%>mAP/NDS/mATE/mASE/mAOE/mAVE/mAAE</th>
        <th width=10%>Car/Truck/Bus/Trailer/ConVeh/Ped/Motor/Bicycle/Cone/Barrier</th>
        <th width=10%>Others</th>
        <th width=10%>Checkpoint</th>
    </tr>
    <tr>
        <td >
            <a href="./CMT_fusion.py">CMT-Fusion</a>
        </td>
        <td > C + L </td>
        <td > 4/32/20 </td>
        <td > 0.6383/0.6802/0.3380/0.2539/0.3664/0.2437/0.1869  </td>
        <td > 0.8576/0.5686/0.7249/0.4286/0.2654/0.8356/0.7193/0.5951/0.7269/0.6601 </td>
        <td >
            w unified gt-sampling
        </td>
        <td >
            s3://e2emodel-data/multi_modal_exps/CMT_nuScenes/cmt_voxel_0075_r50_256_704_cbgs_20e_fade_680.pth
        </td>
    </tr>
    <tr>
        <td >
            <a href="./CMT_fusion.py">CMT-Fusion</a>
        </td>
        <td > C + L </td>
        <td > 4/32/20 </td>
        <td > 0.6453/0.6795/0.3533/0.2566/0.3749/0.2603/0.1866  </td>
        <td > 0.8582/0.5957/0.7282/0.4045/0.3114/0.8373/0.7420/0.6104/0.7198/0.6470 </td>
        <td >
            w unified gt-sampling and modal mask
        </td>
        <td >
            s3://e2emodel-data/multi_modal_exps/CMT_nuScenes/cmt_voxel_0075_r50_256_704_cbgs_20e_fade_modalmask_680.pth
        </td>
    </tr>
    <tr>
        <td >
            <a href="./CMT_lidar.py">CMT-LiDAR</a>
        </td>
        <td > L </td>
        <td > 4/32/20 </td>
        <td > 0.6156/0.6741/0.3338/0.2582/0.3258/0.2316/0.1879  </td>
        <td > 0.8535/0.5700/0.7175/0.4318/0.2493/0.8203/0.6879/0.5135/0.6833/0.6291</td>
        <td >
            w unified gt-sampling
        </td>
        <td >
            s3://e2emodel-data/multi_modal_exps/CMT_nuScenes/cmt_voxel_0075_lidar_cbgs_20e_fade_674.pth
        </td>
    </tr>
    <tr>
        <td >
            <a href="./CMT_camera.py">CMT-Camera</a>
        </td>
        <td > C </td>
        <td > 4/32/20 </td>
        <td > 0.2976/0.3489/0.8177/0.2787/0.7216/0.9405/0.2405  </td>
        <td > 0.4567/0.2193/0.3051/0.0884/0.0477/0.3689/0.2757/0.2616/0.5176/0.4346</td>
        <td >
            w unified gt-sampling
        </td>
        <td >
            s3://e2emodel-data/multi_modal_exps/CMT_nuScenes/cmt_r50_256_704_cbgs_20e_fade_349.pth
        </td>
    <tr>
        <td >
            <a href="./CMT_camera_wo_gtsample.py">CMT-Camera-w/o-gtsample</a>
        </td>
        <td > C </td>
        <td > 4/32/20 </td>
        <td >   </td>
        <td > </td>
        <td >
            w/o unified gt-sampling
        </td>
        <td >
            s3://chenxiwu-share/longterm/e2emodel-data/multi_modal_exps/CMT_nuScenes/cmt_camera_wo_gtsample/dump_model/checkpoint_epoch_18.pth
        </td>
</table>

### NOTE
+ P and B mean the number of machines and batchsize
+ All exps were trained with 32 2080ti GPUs and batchsize = 1 per GPU. Refer to the training script at the top of the exp.py
+ Please refer to <a href="./_base_/base_model_cfg.py">base_nuscenes_cfg.py</a> for detailed configs
+ Fusion models and camera models were trained with R50 model pretrained on nuimage, s3://zdk/per_remapping_pretrained_ckpt/nuim_r50_remapping.pth.
+ Lidar models were trained from scratch
+ flash-attn < 1.0 when using 2080ti
