from perceptron.data.det3d.modules.pipelines import (
    BevAffineTransformation,
    PubicImageAffineTransformation as ImageAffineTransformation,
    PointShuffle,
    ObjectRangeFilter,
    GTSampling,
)
from perceptron.data.det3d.modules.loader import NuscenesLoader

_POINT_CLOUD_RANGE = [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
_VOXEL_SIZE = [0.075, 0.075, 0.2]
_GRID_SIZE = [1440, 1440, 40]
_IMG_DIM = (256, 704)
_OUT_SIZE_FACTOR = 8

COMMON_CFG = dict(
    point_cloud_range=_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL_SIZE,
    grid_size=_GRID_SIZE,
    img_dim=_IMG_DIM,
    out_size_factor=_OUT_SIZE_FACTOR,
)

CLASS_NAMES = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "trailer",
    "barrier",
    "motorcycle",
    "bicycle",
    "pedestrian",
    "traffic_cone",
]

_TRAIN_PIPELINE = dict(
    gt_sampling=dict(
        type=GTSampling,
        root_path="s3://generalDetection/3DDatasets/nuScenes/",
        data_name="nuScenes_multimodal",  # optional: nuScenes
        data_split="training",
        use_road_plane=False,
        filter_by_min_points_cfg=[
            "car:5",
            "truck:5",
            "construction_vehicle:5",
            "bus:5",
            "trailer:5",
            "barrier:5",
            "motorcycle:5",
            "bicycle:5",
            "pedestrian:5",
            "traffic_cone:5",
        ],
        num_point_feature=5,
        remove_extra_width=[0.0, 0.0, 0.0],
        limit_whole_scene=True,
        sampler_groups=[
            "car:2",
            "truck:3",
            "construction_vehicle:7",
            "bus:4",
            "trailer:6",
            "barrier:2",
            "motorcycle:6",
            "bicycle:6",
            "pedestrian:2",
            "traffic_cone:2",
        ],
        class_names=CLASS_NAMES,
    ),
    ida=dict(
        type=ImageAffineTransformation,
        aug_conf=dict(
            final_dim=(256, 704),
            resize_lim=(0.34, 0.55),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=True,
            rot_lim=(-5.4, 5.4),
        ),
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
    bda=dict(
        type=BevAffineTransformation,
        aug_conf=dict(
            rot_lim=(-22.5, 22.5),
            scale_lim=(0.95, 1.05),
            trans_lim=(-4.0, 4.0),
            flip_dx_ratio=0.5,
            flip_dy_ratio=0.5,
        ),
        with_trans_z=True,
    ),
    shuffle=dict(
        type=PointShuffle,
    ),
    filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=_POINT_CLOUD_RANGE,
    ),
)

_TEST_PIPELINE = [
    dict(
        type=ImageAffineTransformation,
        aug_conf=dict(
            final_dim=(256, 704),
            resize_lim=(0.34, 0.55),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=True,
            rot_lim=(-5.4, 5.4),
        ),
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    )
]

DATA_CFG = dict(
    train=dict(
        mode="train",
        class_names=CLASS_NAMES,
        use_cbgs=True,
        loader=dict(
            type=NuscenesLoader,
            data_split="training",
            lidar_dict=dict(
                load_dim=5,
                use_dim=5,
                num_sweeps=10,
            ),
            camera_dict=dict(),
            radar_dict=dict(
                load_dim=18,
                max_points_num=1500,
            ),
        ),
        pipeline=_TRAIN_PIPELINE,
    ),
    val=dict(
        mode="test",
        class_names=CLASS_NAMES,
        loader=dict(
            type=NuscenesLoader,
            data_split="validation",
            lidar_dict=dict(
                load_dim=5,
                use_dim=5,
                num_sweeps=10,
            ),
            camera_dict=dict(),
            radar_dict=dict(
                load_dim=18,
                max_points_num=1500,
            ),
        ),
        pipeline=_TEST_PIPELINE,
    ),
)


_DENSE_TASKS = [
    dict(num_class=1, class_names=["car"]),
    dict(num_class=2, class_names=["truck", "construction_vehicle"]),
    dict(num_class=2, class_names=["bus", "trailer"]),
    dict(num_class=1, class_names=["barrier"]),
    dict(num_class=2, class_names=["motorcycle", "bicycle"]),
    dict(num_class=2, class_names=["pedestrian", "traffic_cone"]),
]


MODEL_CFG = dict(
    class_names=CLASS_NAMES,
    lidar_encoder=dict(
        point_cloud_range=_POINT_CLOUD_RANGE,
        voxel_size=_VOXEL_SIZE,
        grid_size=_GRID_SIZE,
        max_num_points=10,
        max_voxels=(120000, 160000),
        src_num_point_features=5,
        use_num_point_features=5,
        map_to_bev_num_features=256,
    ),
    camera_encoder=dict(
        x_bound=[_POINT_CLOUD_RANGE[0], _POINT_CLOUD_RANGE[3], _VOXEL_SIZE[0] * _OUT_SIZE_FACTOR],
        y_bound=[_POINT_CLOUD_RANGE[1], _POINT_CLOUD_RANGE[4], _VOXEL_SIZE[1] * _OUT_SIZE_FACTOR],
        z_bound=[_POINT_CLOUD_RANGE[2], _POINT_CLOUD_RANGE[5], _POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]],
        d_bound=[2.0, 58.0, 0.5],
        final_dim=_IMG_DIM,
        output_channels=256,
        downsample_factor=16,
        img_backbone_conf=dict(
            type="ResNet",
            depth=50,
            frozen_stages=0,
            out_indices=[0, 1, 2, 3],
            norm_eval=False,
            init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
        ),
        img_neck_conf=dict(
            type="SECONDFPN",
            in_channels=[256, 512, 1024, 2048],
            upsample_strides=[0.25, 0.5, 1, 2],
            out_channels=[128, 128, 128, 128],
        ),
        depth_net_conf=dict(in_channels=512, mid_channels=512),
    ),
    radar_encoder=dict(
        x_bound=[_POINT_CLOUD_RANGE[0], _POINT_CLOUD_RANGE[3], _VOXEL_SIZE[0] * _OUT_SIZE_FACTOR],
        y_bound=[_POINT_CLOUD_RANGE[1], _POINT_CLOUD_RANGE[4], _VOXEL_SIZE[1] * _OUT_SIZE_FACTOR],
        z_bound=[_POINT_CLOUD_RANGE[2], _POINT_CLOUD_RANGE[5], _POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]],
        num_sweeps_radar=5,
        gather_multi_sweeps=True,
        extend_height=True,
        use_dims=[0, 1, 2, 5, 6, 7, 8, 9, 12, 13, 16, 17, 18],
        radar_backbone_conf=dict(type="MLP", input_channels=13, output_channels=256, knn=7),
        bev_bound=dict(
            x_bound=[_POINT_CLOUD_RANGE[0], _POINT_CLOUD_RANGE[3], _VOXEL_SIZE[0] * _OUT_SIZE_FACTOR],
            y_bound=[_POINT_CLOUD_RANGE[1], _POINT_CLOUD_RANGE[4], _VOXEL_SIZE[1] * _OUT_SIZE_FACTOR],
            z_bound=[_POINT_CLOUD_RANGE[2], _POINT_CLOUD_RANGE[5], _POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]],
            d_bound=[2.0, 58.0, 0.5],
        ),
    ),
    bev_encoder=dict(
        use_elementwise=False,
        input_channels=256 * 3,
        conv_cfg=dict(
            input_channels=256,
            layer_nums=[5, 5],
            layer_strides=[1, 2],
            num_filters=[128, 256],
            upsample_strides=[1, 2],
            num_upsample_filters=[256, 256],
        ),
    ),
    det_head=dict(
        class_name=CLASS_NAMES,
        target_assigner=dict(
            out_size_factor=_OUT_SIZE_FACTOR,
            tasks=_DENSE_TASKS,
            dense_reg=1,
            gaussian_overlap=0.1,
            max_objs=2500,
            min_radius=2,
            mapping={name: idx + 1 for idx, name in enumerate(CLASS_NAMES)},
            grid_size=_GRID_SIZE,
            pc_range=_POINT_CLOUD_RANGE[0:2],
            voxel_size=_VOXEL_SIZE[0:2],
            assign_topk=9,
            no_log=False,
            with_velocity=True,
        ),
        proposal_layer=dict(
            dataset_name="nuscenes",
            class_names=[t["class_names"] for t in _DENSE_TASKS],
            post_center_limit_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
            score_threshold=0.1,
            pc_range=_POINT_CLOUD_RANGE[0:2],
            out_size_factor=_OUT_SIZE_FACTOR,
            voxel_size=_VOXEL_SIZE[0:2],
            no_log=False,
            iou_aware_list=[0.65] * 10,
            nms_iou_threshold_train=0.8,
            nms_pre_max_size_train=1500,
            nms_post_max_size_train=80,
            nms_iou_threshold_test=0.1,
            nms_pre_max_size_test=1500,
            nms_post_max_size_test=100,
        ),
        dense_head=dict(
            dataset_name="nuscenes",
            tasks=_DENSE_TASKS,
            out_size_factor=_OUT_SIZE_FACTOR,
            input_channels=512,  # need to be careful!
            grid_size=_GRID_SIZE,
            point_cloud_range=_POINT_CLOUD_RANGE,
            code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
            loc_weight=0.25,
            iou_weight=5.0,
            share_conv_channel=64,
            common_heads=dict(  # common_heads,
                {
                    "iou": [1, 2],
                    "reg": [2, 2],
                    "height": [1, 2],
                    "dim": [3, 2],
                    "rot": [2, 2],
                    "vel": [2, 2],
                }
            ),
            upsample_for_pedestrian=False,
            predict_boxes_when_training=False,
            mode="3d",
            init_bias=-2.19,
        ),
        target_assigner_alpha=0.25,
        target_assigner_gamma=2,
    ),
)
