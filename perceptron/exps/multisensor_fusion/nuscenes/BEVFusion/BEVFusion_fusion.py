from perceptron.engine.cli import Det3DCli

from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_base import Exp as BaseExp


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_cfg.train.loader.pop("radar_dict")
        self.data_cfg.val.loader.pop("radar_dict")
        self.model_cfg.pop("radar_encoder")
        self.model_cfg.bev_encoder.input_channels = 256 * 2
        self._change_cfg_params()

    def _change_cfg_params(self):
        self.data_cfg.train.pipeline.pop("gt_sampling")


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
