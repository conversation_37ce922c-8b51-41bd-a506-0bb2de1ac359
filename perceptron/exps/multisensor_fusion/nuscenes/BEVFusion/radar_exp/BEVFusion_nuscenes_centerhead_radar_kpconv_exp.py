from perceptron.engine.cli import Det3DCli
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.radar_exp.BEVFusion_nuscenes_centerhead_radar_exp import (
    Exp as BaseExp,
)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_cfg["lidar_key_list"] = []
        self.model_cfg["lidar_encoder"] = None
        self.data_cfg["use_cbgs"] = True
        self.data_cfg["num_radar_sweeps"] = 5
        self.model_cfg["radar_encoder"]["radar_backbone_conf"] = dict(
            type="KPConv", input_channels=13, output_channels=256, deformable=False, knn=7
        )

    def _change_cfg_params(self):
        self.num_keep_latest_ckpt = 2


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
