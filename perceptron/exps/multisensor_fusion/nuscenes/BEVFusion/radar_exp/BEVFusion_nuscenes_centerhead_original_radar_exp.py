from perceptron.engine.cli import Det3DCli
from perceptron.models.multisensor_fusion import BaseEncoder
from perceptron.layers.blocks_3d.mmdet3d.lss_fpn_radar import LSSFPNRadar
from perceptron.exps.multisensor_fusion.nuscenes._base_.base_nuscenes_radar_cfg import (
    DATA_CFG,
    MODEL_CFG,
    CENTERPOINT_DET_HEAD_CFG,
)
from torch.utils.data import DistributedSampler
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_nuscenes_centerhead_original_fusion_exp import (
    Exp as BaseExp,
    BEVFusionCenterHead as BaseBEVFusion,
)
from functools import partial
from perceptron.models.multisensor_fusion import ForceFp32
from perceptron.utils import torch_dist as dist
from perceptron.data.multisensorfusion.nuscenes_multimodal import NuscenesMultiModalData, collate_fn
from perceptron.utils.det3d_utils.common_utils import _load_data_to_gpu
import mmcv
import torch

_IMG_BACKBONE_CONF = dict(
    type="ResNet",
    depth=50,
    frozen_stages=0,
    out_indices=[0, 1, 2, 3],
    norm_eval=False,
    init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
)


_IMG_NECK_CONF = dict(
    type="SECONDFPN",
    in_channels=[256, 512, 1024, 2048],
    upsample_strides=[0.25, 0.5, 1, 2],
    out_channels=[128, 128, 128, 128],
)

_DEPTH_NET_CONF = dict(in_channels=512, mid_channels=512)


class RadarEncoder(BaseEncoder):
    def __init__(self, radar_encoder_cfg: mmcv.Config, **kwargs):
        super().__init__()
        self.backbone = LSSFPNRadar(
            x_bound=radar_encoder_cfg["x_bound"],
            y_bound=radar_encoder_cfg["y_bound"],
            z_bound=radar_encoder_cfg["z_bound"],
            use_dims=radar_encoder_cfg["use_dims"],
            radar_backbone_conf=radar_encoder_cfg["radar_backbone_conf"],
        )

    @ForceFp32(apply_to=("radar_points"))
    def forward(
        self,
        radar_points: torch.tensor,
    ):
        feature_map = self.backbone(radar_points)
        return feature_map


class BEVFusion(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)
        if self.cfg.get("radar_encoder", None):
            self.radar_encoder = self._configure_radar_encoder()
        else:
            self.radar_encoder = None

        if (self.with_radar_encoder or self.with_lidar_encoder) and self.with_camera_encoder:
            self.fusion_encoder = self._configure_fusion_encoder()
        else:
            self.fusion_encoder = None

    def forward(self, radar_ego, lidar_points, cameras_imgs, metas, gt_boxes, **kwargs):
        if self.with_radar_encoder:
            radar_output = self.radar_encoder(radar_ego)
            model_output = radar_output

        if self.with_lidar_encoder:
            lidar_output = self.lidar_encoder(lidar_points)
            model_output = lidar_output

        if self.with_camera_encoder:
            camera_output = self.camera_encoder(cameras_imgs, metas)
            model_output = camera_output

        if self.with_fusion_encoder:
            if self.with_radar_encoder:
                multimodal_output = self.fusion_encoder(radar_output, camera_output)
                model_output = multimodal_output
            else:
                multimodal_output = self.fusion_encoder(lidar_output, camera_output)
                model_output = multimodal_output

        x = self.bev_encoder(model_output)
        forward_ret_dict = self.det_head(x[0], gt_boxes)
        if self.training:
            with torch.cuda.amp.autocast(enabled=False):
                for task_id, encoding in forward_ret_dict["box_encoding"].items():
                    encoding[torch.isinf(encoding)] = 0
                loss_rpn, tb_dict = self.det_head.dense_head.get_loss(forward_ret_dict)
                tb_dict.update({"loss_rpn": loss_rpn.item()})
                ret_dict = {"loss": loss_rpn}
                return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict

    def _configure_radar_encoder(self):
        return RadarEncoder(self.cfg.radar_encoder)


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.data_cfg = DATA_CFG
        self.model_cfg = MODEL_CFG
        self.model_cfg["camera_encoder"]["img_backbone_conf"] = _IMG_BACKBONE_CONF
        self.model_cfg["camera_encoder"]["img_neck_conf"] = _IMG_NECK_CONF
        self.model_cfg["camera_encoder"]["depth_net_conf"] = _DEPTH_NET_CONF
        self.model_cfg["det_head"] = CENTERPOINT_DET_HEAD_CFG
        self.model_cfg["det_head"]["dense_head"]["densehead_common_heads"] = {
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
            "vel": [2, 2],
        }
        self._change_cfg_params()

    def _change_cfg_params(self):
        pass

    def _configure_model(self):
        model = BEVFusion(
            model_cfg=mmcv.Config(self.model_cfg),
        )
        return model

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = NuscenesMultiModalData(
            **self.data_cfg,
            data_split=self.data_split["train"],
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=2,
            drop_last=False,
            shuffle=False,
            collate_fn=partial(collate_fn, is_return_depth=False, with_radar=True),
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = NuscenesMultiModalData(
            **self.data_cfg,
            data_split=self.data_split["val"],
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=partial(collate_fn, with_radar=True),
            num_workers=2,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        if "radar_points" in batch:
            radar_points = batch["radar_points"]
        else:
            radar_points = None

        imgs = batch.get("imgs", None)
        metas = batch.get("mats_dict", None)
        gt_boxes = batch["gt_boxes"]
        gt_labels = batch["gt_labels"]

        gt_labels += 1
        gt_boxes = torch.cat([gt_boxes, gt_labels.unsqueeze(dim=2)], dim=2)
        ret_dict, tf_dict, _ = self.model(radar_points, points, imgs, metas, gt_boxes)
        loss = ret_dict["loss"].mean()
        return loss, tf_dict

    @torch.no_grad()
    def test_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None

        if "radar_points" in batch:
            radar_points = batch["radar_points"]
        else:
            radar_points = None

        imgs = batch.get("imgs", None)
        metas = batch.get("mats_dict", None)
        ret_dict = self.model(radar_points, points, imgs, metas, None)
        for result in ret_dict["pred_dicts"]:
            result["pred_labels"] -= 1
        return dict(pred_dicts=ret_dict["pred_dicts"])


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
