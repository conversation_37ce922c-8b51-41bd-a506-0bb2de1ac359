## Welcome to Multi-Modal Perception

+ Release Experiment Record On `nuScenes Dataset`.

## Results and Models

<table>
    <tr>
        <th width=20%>Name</th>
        <th width=5%>Modality</th>
        <th width=10%>P/B/epochs/lr</th>
        <th width=10%>m(AP/ATE/ASE/AOE/AVE/AAE)/NDS</th>
        <th width=10%>Car/Truck/Bus/Trailer/ConVeh/Ped/Motor/Bicycle/Cone/Barrier</th>
        <th width=10%>Details</th>
    </tr>
    <tr>
        <td >
            <a href="../BEVFusion_nuscenes_centerhead_original_camera_exp.py">CenterPoint-Camera</a>
        </td>
        <td > C </td>
        <td> 2/5/20/1e-3 </td>
        <td > 0.2771/0.7094/0.2677/0.4440/0.9512/0.2096/0.3803 </td>
        <td > 0.483/0.217/0.268/0.111/0.057/0.270/0.238/0.203/0.414/0.509 </td>
        <td >
            w/o gt-sampling and w/o iou loss
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_radar_exp.py">CenterPoint-Radar-MLP</a>
        </td>
        <td > C + R </td>
        <td> 1/3/24/1e-3 </td>
        <td > 0.3426/0.6245/0.2937/0.3747/0.4410/0.1797/0.4800 </td>
        <td > 0.608/0.299/0.364/0.161/0.088/0.388/0.281/0.232/0.468/0.537 </td>
        <td >
            w/o gt-sampling and w/o iou loss
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_radar_kpconv_exp.py">CenterPoint-Radar-KPConv</a>
        </td>
        <td > C + R </td>
        <td> 1/3/24/1e-3 </td>
        <td > 0.3410/0.6239/0.2983/0.3819/0.4509/0.1733/0.4777 </td>
        <td > 0.607/0.290/0.377/0.159/0.086/0.377/0.285/0.245/0.464/0.520 </td>
        <td >
            w/o gt-sampling and w/o iou loss
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_radar_pillar_exp.py">CenterPoint-RADAR-Pillar</a>
        </td>
        <td > C + R </td>
        <td> 2/4/20/1e-3 </td>
        <td > 0.3402/0.6398/0.2872/0.4041/0.3920/0.1900/0.4788 </td>
        <td > 0.616/0.289/0.385/0.167/0.085/0.375/0.275/0.221/0.466/0.523 </td>
        <td >
            w/o gt-sampling and w/o iou loss
        </td>
    </tr>
</table>

### NOTE

+ P and B mean the number of machines and batchsize. If experiments in this folder are interrupted due to loading data, please reduce the number of workers;
+ All exps were trained with eight GeForce RTX 2080Ti;
+ Please refer to <a href="../../_base_/base_nuscenes_radar_cfg.py">base_nuscenes_radar_cfg.py</a> and <a href="../../_base_/base_nuscenes_radar_pillar_cfg.py">base_nuscenes_radar_pillar_cfg.py</a> for detailed configs;
+ Please refer to <a href="https://oss.iap.hh-b.brainpp.cn/e2emodel-data/BEVFusion_exps/">this url</a> for train logs and best models;
+ In the radar_exp folder, the <a href="./BEVFusion_nuscenes_centerhead_original_radar_exp.py">BEVFusion_nuscenes_centerhead_original_radar_exp</a> reserve the head without iouloss; the <a href="./BEVFusion_nuscenes_centerhead_radar_exp.py">BEVFusion_nuscenes_centerhead_radar_exp</a> supports MLP-based radar encoder; the <a href="./BEVFusion_nuscenes_centerhead_radar_kpconv_exp.py">BEVFusion_nuscenes_centerhead_radar_kpconv_exp</a> supports KPConv-based radar encoder; the <a href="./BEVFusion_nuscenes_centerhead_radar_pillar_exp.py">BEVFusion_nuscenes_centerhead_radar_pillar_exp</a> supports pillar-based radar encoder; the <a href="./BEVFusion_nuscenes_centerhead_radar_pillar_base_exp_ol_front.py">BEVFusion_nuscenes_centerhead_radar_pillar_base_exp_ol_front</a> supports pillar-based radar encoder with the front RADAR;
+ Radar fusion model without iou loss please refer to <a href="./BEVFusion_nuscenes_centerhead_original_radar_exp.py">BEVFusion_nuscenes_centerhead_original_radar_exp.py</a> for more details, you can write exps without iouloss by inherit it.
