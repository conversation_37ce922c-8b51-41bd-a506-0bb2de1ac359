from perceptron.engine.cli import Det3DCli
from perceptron.models.multisensor_fusion import BaseEncoder
from perceptron.exps.multisensor_fusion.nuscenes._base_.base_nuscenes_radar_pillar_cfg import (
    DATA_CFG,
    MODEL_CFG,
    CENTERPOINT_DET_HEAD_CFG,
)
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.radar_exp.BEVFusion_nuscenes_centerhead_radar_exp import (
    Exp as BaseExp,
    BEVFusion as BaseBEVFusion,
)
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.layers.blocks_3d.det3d.vfe.pillar_vfe import PillarVFE
from perceptron.layers.blocks_2d.det3d.map_to_bev.pointpillar_scatter import PointPillarScatter

import mmcv
import torch


class RadarEncoder(BaseEncoder):
    def __init__(self, radar_encoder_cfg: mmcv.Config, **kwargs):
        super().__init__()
        self.cfg = radar_encoder_cfg
        self.voxelizer = self.build_voxelizer()
        self.vfe = self.build_vfe()
        self.map_to_bev = self.build_map_to_bev()

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
            max_num_points=self.cfg.max_num_points,
            max_voxels=self.cfg.max_voxels,
            num_point_features=self.cfg.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = PillarVFE(
            use_norm=True,
            with_distance=False,
            use_absolute_xyz=True,
            num_filters=self.cfg.vfe_num_filters,
            num_point_features=self.cfg.use_num_point_features,
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
        )
        return vfe

    def build_map_to_bev(self):
        return PointPillarScatter(
            num_bev_features=self.cfg.map_to_bev_num_features,
            grid_size=self.cfg.grid_size,
        )

    def forward(self, radar_points):
        points = [frame_point.squeeze(0)[..., self.cfg.radar_use_dims] for frame_point in radar_points]
        voxels, voxel_coords, voxel_num_points = self.voxelizer(points)
        voxel_features = self.vfe(voxels, voxel_coords, voxel_num_points)
        spatial_features = self.map_to_bev(voxel_features, voxel_coords)
        return spatial_features


class BEVFusion(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)
        if self.cfg.get("radar_encoder", None):
            self.radar_encoder = self._configure_radar_encoder()
        else:
            self.radar_encoder = None

        if (self.with_radar_encoder or self.with_lidar_encoder) and self.with_camera_encoder:
            self.fusion_encoder = self._configure_fusion_encoder()
        else:
            self.fusion_encoder = None

    def forward(self, radar_ego, lidar_points, cameras_imgs, metas, gt_boxes, **kwargs):
        if self.with_camera_encoder:
            camera_output = self.camera_encoder(cameras_imgs, metas)
            model_output = camera_output

        if self.with_radar_encoder:
            radar_output = self.radar_encoder(radar_ego)
            model_output = radar_output

        if self.with_fusion_encoder:
            multimodal_output = self.fusion_encoder(radar_output, camera_output)
            model_output = multimodal_output

        x = self.bev_encoder(model_output)
        forward_ret_dict = self.det_head(x[0], gt_boxes)
        if self.training:
            with torch.cuda.amp.autocast(enabled=False):
                for task_id, encoding in forward_ret_dict["box_encoding"].items():
                    encoding[torch.isinf(encoding)] = 0
                loss_rpn, tb_dict = self.det_head.dense_head.get_loss(forward_ret_dict)
                tb_dict.update({"loss_rpn": loss_rpn.item()})
                ret_dict = {"loss": loss_rpn}
                return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict

    def _configure_radar_encoder(self):
        return RadarEncoder(self.cfg.radar_encoder)


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.data_cfg = DATA_CFG
        self.model_cfg = MODEL_CFG
        self.model_cfg["det_head"] = CENTERPOINT_DET_HEAD_CFG
        self.model_cfg["radar_encoder"]["max_voxels"] = (96000, 128000)
        self._change_cfg_params()

    def _change_cfg_params(self):
        pass

    def _configure_model(self):
        model = BEVFusion(
            model_cfg=mmcv.Config(self.model_cfg),
        )
        return model


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
