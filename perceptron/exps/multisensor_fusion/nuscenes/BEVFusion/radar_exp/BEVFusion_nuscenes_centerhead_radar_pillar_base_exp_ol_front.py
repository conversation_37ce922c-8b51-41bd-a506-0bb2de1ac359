from perceptron.engine.cli import Det3DCli

from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.radar_exp.BEVFusion_nuscenes_centerhead_radar_pillar_exp import (
    Exp as BaseExp,
)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_cfg["lidar_key_list"] = []
        self.model_cfg["lidar_encoder"] = None
        self.model_cfg["radar_encoder"]["max_voxels"] = (96000, 128000)
        self.data_cfg["radar_key_list"] = ["RADAR_FRONT"]
        self.num_keep_latest_ckpt = 2

    def _change_cfg_params(self):
        pass


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
