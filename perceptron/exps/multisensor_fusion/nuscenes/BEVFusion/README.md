## Welcome to Multi-Modal Perception

+ Release Experiment Record On `nuScenes Dataset`.

## Results and Models

<table>
    <tr>
        <th width=20%>Name</th>
        <th width=5%>Modality</th>
        <th width=10%>P/B/epochs</th>
        <th width=10%>m(AP/ATE/ASE/AOE/AVE/AAE)/NDS</th>
        <th width=10%>Car/Truck/Bus/Trailer/ConVeh/Ped/Motor/Bicycle/Cone/Barrier</th>
        <th width=10%>Details</th>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_fusion_exp.py">CenterPoint-Fusion</a>
        </td>
        <td > C + L </td>
        <td> 1/8/20 </td>
        <td > 0.6429/0.2815/0.2555/0.2601/0.2309/0.1970/0.6989 </td>
        <td > 0.872/0.601/0.720/0.400/0.229/0.859/0.717/0.558/0.751/0.721 </td>
        <td >
            w/o gt-sampling
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_fusion_exp.py">CenterPoint-Fusion</a>
        </td>
        <td > C + L </td>
        <td> 1/8/20 </td>
        <td > 0.6561/0.2771/0.2533/0.2815/0.2260/0.1966/0.7046 </td>
        <td > 0.874/0.603/0.737/0.392/0.254/0.864/0.730/0.610/0.754/0.741 </td>
        <td >
            LiDAR gt-sampling
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_lidar_exp.py">CenterPoint-LiDAR</a>
        </td>
        <td > L </td>
        <td> 1/8/20 </td>
        <td > 0.5984/0.2871/0.2530/0.2740/0.2326/0.1936/0.6752 </td>
        <td > 0.843/0.576/0.670/0.368/0.184/0.848/0.653/0.494/0.675/0.674 </td>
        <td >
            w/o gt-sampling
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_lidar_exp.py">CenterPoint-LiDAR</a>
        </td>
        <td > L </td>
        <td> 1/8/20 </td>
        <td > 0.6203/0.2889/0.2543/0.2773/0.2321/0.1895/0.6859 </td>
        <td > 0.852/0.571/0.699/0.378/0.221/0.854/0.683/0.539/0.693/0.713 </td>
        <td >
            gt-sampling
        </td>
    </tr>
    <tr>
        <td >
            <a href="./BEVFusion_nuscenes_centerhead_camera_exp.py">CenterPoint-Camera</a>
        </td>
        <td > C </td>
        <td> 1/8/20 </td>
        <td > 0.2608/0.7494/0.2801/0.4834/0.9639/0.2355/0.3892 </td>
        <td > 0.454/0.220/0.288/0.146/0.050/0.217/0.255/0.200/0.294/0.484 </td>
        <td >
        </td>
    </tr>
</table>

### NOTE

+ P and B mean the number of machines and batchsize
+ All exps were trained with single A100
+ Please refer to <a href="../_base_/base_nuscenes_cfg.py">base_nuscenes_cfg.py</a> for detailed configs
+ Please refer to <a href="https://oss.iap.hh-b.brainpp.cn/e2emodel-data/BEVFusion_exps/">this url</a> for train logs and best models
+ Fusion models were trained with pretrained best lidar model for 10 epochs. Please refer to logs for more details.
