import mmcv
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DistributedSampler
from functools import partial
import numpy as np

from perceptron.exps.base_exp import BaseExp
from typing import Any, Dict, List
from perceptron.utils import torch_dist as dist
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers
from perceptron.engine.executors import Det3DEvaluator
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.models.multisensor_fusion import BaseMultiSensorFusion, BaseEncoder, ForceFp32
from perceptron.layers.blocks_3d.mmdet3d.base_lss_fpn import LSSFPNPrivate as LSSFPN
from perceptron.layers.blocks_3d.mmdet3d.lss_fpn_radar import LSSFPNRadar
from perceptron.layers.blocks_2d.det3d import BaseBEVBackbone, HeightCompression
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.det3d.public import NuScenesDataset
from perceptron.layers.blocks_3d.det3d import MeanVFE, VoxelResBackBone8x
from perceptron.exps.multisensor_fusion.nuscenes._base_.base_nuscenes_cfg import DATA_CFG, MODEL_CFG
from perceptron.layers.losses.det3d import CenterNetRegLoss, FocalLoss
from perceptron.layers.head.det3d import IouAwareGenProposals, CenterHeadIouAware, FCOSAssigner


class LidarEncoder(BaseEncoder):
    def __init__(self, lidar_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super().__init__()
        self.cfg = lidar_encoder_cfg
        self.voxelizer = self.build_voxelizer()
        self.vfe = self.build_vfe()
        self.backbone_3d = self.build_backbone_3d()
        self.map_to_bev = self.build_map_to_bev()

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
            max_num_points=self.cfg.max_num_points,
            max_voxels=self.cfg.max_voxels,
            num_point_features=self.cfg.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = MeanVFE(
            num_point_features=self.cfg.use_num_point_features,
        )
        return vfe

    def build_backbone_3d(self):
        return VoxelResBackBone8x(
            input_channels=self.vfe.get_output_feature_dim(),
            grid_size=np.array(self.cfg.grid_size),
            last_pad=0,
        )

    def build_map_to_bev(self):
        return HeightCompression(num_bev_features=self.cfg.map_to_bev_num_features)

    @ForceFp32(apply_to=("lidar_points"))
    def forward(self, lidar_points: List[torch.tensor]) -> torch.tensor:
        voxels, voxel_coords, voxel_num_points = self.voxelizer(lidar_points)
        voxel_features = self.vfe(voxels, voxel_num_points)
        encoded_spconv_tensor, encoded_spconv_tensor_stride, _ = self.backbone_3d(
            voxel_features, voxel_coords, len(lidar_points)
        )

        spatial_features, encoded_spconv_tensor_stride = self.map_to_bev(
            encoded_spconv_tensor, encoded_spconv_tensor_stride
        )
        return spatial_features


class CameraEncoder(BaseEncoder):
    def __init__(self, camera_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super().__init__()
        self.backbone = LSSFPN(**camera_encoder_cfg)

    # @ForceFp32(apply_to=("imgs"))
    def forward(
        self,
        imgs: torch.tensor,
        mats_dict: Dict[str, torch.tensor],
        is_return_depth=False,
    ):
        feature_map = self.backbone(
            imgs,
            mats_dict,
            is_return_depth,
        )
        return feature_map


class RadarEncoder(BaseEncoder):
    def __init__(self, radar_encoder_cfg: mmcv.Config, **kwargs):
        super().__init__()
        self.backbone = LSSFPNRadar(
            x_bound=radar_encoder_cfg["x_bound"],
            y_bound=radar_encoder_cfg["y_bound"],
            z_bound=radar_encoder_cfg["z_bound"],
            use_dims=radar_encoder_cfg["use_dims"],
            radar_backbone_conf=radar_encoder_cfg["radar_backbone_conf"],
        )

    def forward(
        self,
        radar_ego: torch.tensor,
    ):
        feature_map = self.backbone(radar_ego)
        return feature_map


class BevEncoder(nn.Module):
    def __init__(self, use_elementwise, input_channels, conv_cfg, **kwargs):
        super().__init__()
        self.use_elementwise = use_elementwise
        if not self.use_elementwise:
            output_channels = conv_cfg["input_channels"]
            self.att = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(input_channels, input_channels, kernel_size=1, stride=1),
                nn.Sigmoid(),
            )
            self.reduce_conv = nn.Sequential(
                nn.Conv2d(input_channels, output_channels, 3, padding=1, bias=False),
                nn.BatchNorm2d(output_channels),
                nn.ReLU(True),
            )

        self.conv_cfg = conv_cfg
        self.backbone_2d = self.build_bev_encoder()

    def build_bev_encoder(self):
        bev_encoder = BaseBEVBackbone(**self.conv_cfg)
        return bev_encoder

    # @ForceFp32(apply_to=("lidar_output", "camera_output", "radar_outpu"))
    def forward(self, lidar_output, camera_output, radar_output):
        feat_list = [feat for feat in [lidar_output, camera_output, radar_output] if feat is not None]
        if self.use_elementwise:
            x = torch.sum(torch.stack(feat_list), 0)
        else:
            x = torch.cat(feat_list, dim=1)
            x = self.reduce_conv(x * self.att(x))

        spatial_features_2d, pyramid = self.backbone_2d(x)
        return spatial_features_2d, pyramid


class DetHead(nn.Module):
    def __init__(self, det_head_cfg: mmcv.Config, **kwargs):
        super().__init__()
        self.det_head_cfg = det_head_cfg
        self.dense_head = self.build_dense_head()

    def build_dense_head(self):
        target_cfg = self.det_head_cfg.target_assigner
        target_assigner = FCOSAssigner(**target_cfg)
        proposal_cfg = self.det_head_cfg.proposal_layer
        proposal_layer = IouAwareGenProposals(**proposal_cfg)
        head_cfg = self.det_head_cfg.dense_head
        dense_head_module = CenterHeadIouAware(
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            **head_cfg,
        )

        def _build_losses(m):
            m.add_module(
                "crit", FocalLoss(self.det_head_cfg.target_assigner_alpha, self.det_head_cfg.target_assigner_gamma)
            )
            m.add_module("crit_reg", CenterNetRegLoss())
            m.add_module("crit_iou_aware", CenterNetRegLoss())

        _build_losses(dense_head_module)

        return dense_head_module

    @ForceFp32(apply_to=("x", "gt_boxes"))
    def forward(self, x: torch.tensor, gt_boxes: torch.tensor) -> Any:
        forward_ret_dict = self.dense_head(x, gt_boxes)

        if self.training:
            for _, encoding in forward_ret_dict["box_encoding"].items():
                encoding[torch.isinf(encoding)] = 0
        return forward_ret_dict


class BEVFusion(BaseMultiSensorFusion):
    r"""
    `BEVFusion`: Multi-Task Multi-Sensor Fusion with Unified Bird's-Eye View Representation.

    `Reference`: https://arxiv.org/abs/2205.13542
    """

    def __init__(self, model_cfg) -> Any:
        super(BEVFusion, self).__init__()
        self.num_class = len(model_cfg.class_names)
        self.class_names = model_cfg.class_names
        self.cfg = model_cfg

        if self.cfg.get("lidar_encoder", None):
            self.lidar_encoder = self._configure_lidar_encoder()
        else:
            self.lidar_encoder = None

        if self.cfg.get("camera_encoder", None):
            self.camera_encoder = self._configure_camera_encoder()
        else:
            self.camera_encoder = None

        if self.cfg.get("radar_encoder", None):
            self.radar_encoder = self._configure_radar_encoder()
        else:
            self.radar_encoder = None

        self.bev_encoder = self._configure_bev_encoder()
        self.det_head = self._configure_det_head()

    def forward(
        self,
        lidar_points: List[torch.tensor] = None,
        imgs: torch.tensor = None,
        radar_points: torch.tensor = None,
        metas: Dict[str, torch.tensor] = None,
        gt_boxes: torch.tensor = None,
        **kwargs,
    ) -> Any:
        if self.with_lidar_encoder:
            lidar_output = self.lidar_encoder(lidar_points)
        else:
            lidar_output = None

        if self.with_camera_encoder:
            camera_output = self.camera_encoder(imgs, metas)
        else:
            camera_output = None

        if self.with_radar_encoder:
            radar_output = self.radar_encoder(radar_points)
        else:
            radar_output = None

        x = self.bev_encoder(lidar_output, camera_output, radar_output)
        forward_ret_dict = self.det_head(x[0], gt_boxes)
        if self.training:
            with torch.cuda.amp.autocast(enabled=False):
                for task_id, encoding in forward_ret_dict["box_encoding"].items():
                    encoding[torch.isinf(encoding)] = 0
                loss_rpn, tb_dict = self.det_head.dense_head.get_loss(forward_ret_dict)
                tb_dict.update({"loss_rpn": loss_rpn.item()})
                ret_dict = {"loss": loss_rpn}
                return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict

    def _configure_lidar_encoder(self):
        return LidarEncoder(self.cfg.lidar_encoder)

    def _configure_camera_encoder(self):
        return CameraEncoder(self.cfg.camera_encoder)

    def _configure_radar_encoder(self):
        return RadarEncoder(self.cfg.radar_encoder)

    def _configure_bev_encoder(self):
        return BevEncoder(**self.cfg.bev_encoder)

    def _configure_det_head(self):
        return DetHead(self.cfg.det_head)


def _load_data_to_gpu(data_dict):
    for k, v in data_dict.items():
        if isinstance(v, torch.Tensor):
            data_dict[k] = v.cuda()
        elif isinstance(v, dict):
            _load_data_to_gpu(data_dict[k])
        else:
            data_dict[k] = v


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 20
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.lr_scale_factor = {"camera_encoder": 0.1}
        self.grad_clip_value = 0.1
        self.data_cfg = mmcv.Config(DATA_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg temporarily. For those should be inherited, please change them in __init__
        """

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = NuScenesDataset(
            **self.data_cfg["train"],
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=6,
            drop_last=False,
            shuffle=False,
            collate_fn=NuScenesDataset.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = NuScenesDataset(
            **self.data_cfg["val"],
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=NuScenesDataset.collate_fn,
            num_workers=2,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        raise NotImplementedError

    def _configure_model(self):
        model = BEVFusion(
            model_cfg=self.model_cfg,
        )
        return model

    def training_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        images = batch.get("imgs", None)
        radar_points = batch.get("radar_points", None)
        metas = batch.get("mats_dict", None)
        gt_boxes = batch["gt_boxes"]
        gt_labels = batch["gt_labels"]

        gt_labels += 1
        gt_boxes = torch.cat([gt_boxes, gt_labels.unsqueeze(dim=2)], dim=2)
        ret_dict, tf_dict, _ = self.model(points, images, radar_points, metas, gt_boxes)
        loss = ret_dict["loss"].mean()
        return loss, tf_dict

    @torch.no_grad()
    def test_step(self, batch):
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)
        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None
        images = batch.get("imgs", None)
        radar_points = batch.get("radar_points", None)
        metas = batch.get("mats_dict", None)
        ret_dict = self.model(points, images, radar_points, metas, None)
        for result in ret_dict["pred_dicts"]:
            result["pred_labels"] -= 1
        return dict(pred_dicts=ret_dict["pred_dicts"])

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler

    def get_cfg_as_str(self) -> str:
        import functools
        from tabulate import tabulate

        config_table = []
        for c, v in self.__dict__.items():
            if isinstance(v, mmcv.Config):
                v = dict(v)
            if not isinstance(v, (int, float, str, list, tuple, dict, np.ndarray)):
                if hasattr(v, "__name__"):
                    v = v.__name__
                elif hasattr(v, "__class__"):
                    v = v.__class__
                elif isinstance(v, functools.partial):
                    v = v.func.__name__
            if c[0] == "_":
                c = c[1:]
            config_table.append((str(c), str(v)))

        headers = ["config key", "value"]
        config_table = tabulate(config_table, headers, tablefmt="plain")
        return config_table
