from perceptron.engine.cli import Det3DCli
from perceptron.layers.losses.det3d import CenterNetRegLoss, FocalLoss
from perceptron.layers.head.det3d import CenterHead, CenterPointGenProposals, FCOSAssigner
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_base import (
    BEVFusion as BaseBEVFusion,
    DetHead as BaseDetHead,
    Exp as BaseExp,
)


class DetHead(BaseDetHead):
    def build_dense_head(self):
        target_cfg = self.det_head_cfg.target_assigner
        target_assigner = FCOSAssigner(**target_cfg)

        proposal_cfg = self.det_head_cfg.proposal_layer
        proposal_layer = CenterPointGenProposals(**proposal_cfg)

        head_cfg = self.det_head_cfg.dense_head
        dense_head_module = CenterHead(
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            **head_cfg,
        )

        def _build_losses(m):
            m.add_module(
                "crit", FocalLoss(self.det_head_cfg.target_assigner_alpha, self.det_head_cfg.target_assigner_gamma)
            )
            m.add_module("crit_reg", CenterNetRegLoss())

        _build_losses(dense_head_module)

        return dense_head_module


class BEVFusion(BaseBEVFusion):
    def _configure_det_head(self):
        return DetHead(self.cfg.det_head)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.model_cfg["det_head"]["dense_head"]["densehead_common_heads"] = {
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
            "vel": [2, 2],
        }
        self._change_cfg_params()

    def _change_cfg_params(self):
        pass


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
