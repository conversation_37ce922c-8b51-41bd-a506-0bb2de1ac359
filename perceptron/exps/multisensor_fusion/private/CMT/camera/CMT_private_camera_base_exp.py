# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 2 --cpu=94 --gpu=8 --memory=340000 --group=transformer_3090 --preemptible no --positive-tags=GeForceRTX3090 --negative-tags=node/gpu-2080ti-0055.host.hh-e.brainpp.cn -- python ./perceptron/exps/multisensor_fusion/private/CMT/camera/CMT_private_camera_base_exp.py  --no-clearml -b 2 -d 0-7 -e 80 --amp  --sync_bn 2

from perceptron.engine.cli import Det3DCli
from perceptron.exps.multisensor_fusion.private.CMT.lidar_camera_fusion.CMT_private_fusion_base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_camera_256x704_cfg_wm34 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
import mmcv


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.model_cfg.pop("lidar_encoder")
        self.model_cfg["det_head"]["modal"] = "Camera"
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self._change_cfg_params()

    def _change_cfg_params(self):
        pass


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
