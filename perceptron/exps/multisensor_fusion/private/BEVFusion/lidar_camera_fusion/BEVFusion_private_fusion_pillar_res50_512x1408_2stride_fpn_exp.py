# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : <EMAIL>
# *Date         : 2023-04-13
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 \
# *               --cpu=32 --gpu=8 --memory=180000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/lidar_camera_fusion/BEVFusion_private_fusion_pillar_res50_256x704_2stride_fpn_exp.py \
# *               --no-clearml --sync_bn 3 -b 1 --amp -e 10
# -------------------------------------------------------------------------------------------
import mmcv
import torch
from perceptron.engine.cli import Det3DMultiModalCli
import torch.nn as nn
from typing import Dict, Any, List
from torch.utils.data import DistributedSampler

from perceptron.utils import torch_dist as dist
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer
from perceptron.layers.blocks_3d.mmdet3d.base_lss_fpn import LSSFPNPrivate

from perceptron.data.det3d.private.private_multimodal import PrivateMultiModalData
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_fusion_cfg_wm34 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.fusion_pillar_256x704_res50_2stride_woiou_cfg import (
    MODEL_CFG,
)

from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar.BEVFusion_private_lidar_pointpillar_mh_dr_base_exp import (
    LidarEncoder,
)
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_base import (
    CameraEncoder,
    BEVFusion as BaseBEVFusion,
    DetHead as BaseDetHead,
    BevEncoder as BaseBevEncoder,
)
from perceptron.layers.blocks_2d.det3d import BEVFPNTwoLayer
from perceptron.layers.losses.det3d import CenterNetRegLoss, WeightFocalLossWHDR, WeightedFocalLoss
from perceptron.layers.head.det3d import (
    IouAwareGenProposalsTwoLayer,
    CenterHeadIouAwareDRMaskTwoLayerAutoV1,
    FCOSAssignerMaskMSTwoLayer,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar_camera_fusion.BEVFusion_private_fusion_res50_256x704_4stride_base_exp import (
    Exp as BaseExp,
)


def conv3x3(in_planes, out_planes, ksize, padding, stride=1):
    "3x3 convolution with padding"
    return [
        nn.Conv2d(in_planes, out_planes, kernel_size=ksize, stride=stride, padding=padding, bias=False),
        nn.BatchNorm2d(out_planes),
        nn.ReLU(True),
    ]


class CameraEncoderAdjustChannel(CameraEncoder):
    def __init__(self, camera_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super(CameraEncoder, self).__init__()
        if "extra_channel_adjust" in camera_encoder_cfg:
            extra_channel_adjust = camera_encoder_cfg.pop("extra_channel_adjust")
            self.adjust_channel = nn.Sequential(
                nn.Conv2d(extra_channel_adjust["in_channels"], extra_channel_adjust["out_channels"], 1, bias=False),
                nn.BatchNorm2d(extra_channel_adjust["out_channels"]),
                nn.ReLU(True),
            )
        self.backbone = LSSFPNPrivate(**camera_encoder_cfg)

    def forward(
        self,
        imgs: torch.tensor,
        mats_dict: Dict[str, torch.tensor],
        is_return_depth=False,
    ):
        feature_map = self.backbone(
            imgs,
            mats_dict,
            is_return_depth,
        )
        if hasattr(self, "adjust_channel"):
            feature_map = self.adjust_channel(feature_map)
        return feature_map


class DetHead(BaseDetHead):
    def build_dense_head(self):
        target_cfg = self.det_head_cfg.target_assigner
        target_assigner = FCOSAssignerMaskMSTwoLayer(**target_cfg)

        proposal_cfg = self.det_head_cfg.proposal_layer
        proposal_layer = IouAwareGenProposalsTwoLayer(**proposal_cfg)

        head_cfg = self.det_head_cfg.dense_head
        dense_head_module = CenterHeadIouAwareDRMaskTwoLayerAutoV1(
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            **head_cfg,
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                WeightedFocalLoss(self.det_head_cfg.target_assigner_alpha, self.det_head_cfg.target_assigner_gamma),
            )
            m.add_module("crit_reg", CenterNetRegLoss())
            m.add_module("crit_iou_aware", CenterNetRegLoss())
            m.add_module("crit_dr", WeightFocalLossWHDR())

        _build_losses(dense_head_module)

        return dense_head_module


class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(in_planes, in_planes // 16, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(in_planes // 16, in_planes, 1, bias=False),
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()

        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv1(x)
        return self.sigmoid(x)


class BevEncoder(BaseBevEncoder):
    def __init__(self, use_elementwise, input_channels, conv_cfg, **kwargs):
        super().__init__(use_elementwise, input_channels, conv_cfg, **kwargs)

    def build_bev_encoder(self):
        bev_encoder = BEVFPNTwoLayer(**self.conv_cfg)
        return bev_encoder


class BEVFusionCenterHead(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)
        self.lidar_bev = self._configure_lidar_bev()

    def _configure_camera_encoder(self):
        return CameraEncoderAdjustChannel(self.cfg.camera_encoder)

    def _configure_lidar_encoder(self):
        return LidarEncoder(self.cfg.lidar_encoder)

    def _configure_lidar_bev(self):
        middle_block = [
            *conv3x3(128, 128, 3, 1, 2),
            *conv3x3(128, 128, 3, 1, 1),
            *conv3x3(128, 128, 3, 1, 1),
        ]

        return nn.Sequential(
            *conv3x3(64, 128, 1, 0, 1),
            *middle_block,
            *middle_block,
            *conv3x3(128, 256, 1, 0, 1),
        )

    def _configure_bev_encoder(self):
        return BevEncoder(**self.cfg.bev_encoder)

    def _configure_det_head(self):
        return DetHead(self.cfg.det_head)

    def forward(
        self,
        lidar_points: List[torch.tensor] = None,
        imgs: torch.tensor = None,
        radar_points: torch.tensor = None,
        metas: Dict[str, torch.tensor] = None,
        gt_boxes: torch.tensor = None,
        **kwargs,
    ) -> Any:
        if self.with_lidar_encoder:
            lidar_output = self.lidar_encoder(lidar_points)
            lidar_output = self.lidar_bev(lidar_output)
        else:
            lidar_output = None

        if self.with_camera_encoder:
            camera_output = self.camera_encoder(imgs, metas)
        else:
            camera_output = None

        if self.with_radar_encoder:
            radar_output = self.radar_encoder(radar_points)
        else:
            radar_output = None

        x = self.bev_encoder(lidar_output, camera_output, radar_output)
        forward_ret_dict = self.det_head({"1": x[0], "0": x[1]}, gt_boxes)
        if self.training:
            with torch.cuda.amp.autocast(enabled=False):
                for task_id, encoding in forward_ret_dict["box_encoding"].items():
                    encoding[torch.isinf(encoding)] = 0
                loss_rpn, tb_dict = self.det_head.dense_head.get_loss(forward_ret_dict)
                tb_dict.update({"loss_rpn": loss_rpn.item()})
                ret_dict = {"loss": loss_rpn}
                return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 20
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.infer_executor_class = Det3DInfer
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 512 input size
        self.data_train_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (512, 1408)
        self.data_train_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.772, 1.10)
        self.data_val_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (512, 1408)
        self.data_val_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.772, 1.10)
        self.model_cfg["camera_encoder"]["final_dim"] = (512, 1408)

        # smaller point range
        point_range = [-22.4, -75.2, -5.0, 22.4, 75.2, 3.0]
        self.data_train_cfg["pipeline"]["object_range_filter"]["point_cloud_range"] = point_range
        self.data_train_cfg["annotation"]["roi_range"] = point_range
        self.data_val_cfg["pipeline"]["object_range_filter"]["point_cloud_range"] = point_range
        self.data_val_cfg["annotation"]["roi_range"] = point_range

        # multi pretrain
        self.multi_pretrain_cfg = {
            # "camera_encoder": "s3://e2emodel-data/car234/bevfusion_private_centerhead_camera_exp_512input_80channel_4stride_woiou_170k_30epoch_amp_crop_center_300k_pretrain/checkpoint_epoch_29.pth",  # "s3://e2emodel-data/mulitimodal_private_exp/2_9_3_fix_resize_add_crop_9w_221205_wangnz/checkpoint_epoch_28.pth",
            "lidar_encoder": "s3://e2emodel-data/q3_sota_pointpillar/new_checkpoint.pth",
            "fusion_encoder": "s3://e2emodel-data/fusion_car34_baseline/checkpoint_epoch_9.pth",
            "bev_encoder": "s3://e2emodel-data/fusion_car34_baseline/checkpoint_epoch_9.pth",
            "det_head": "s3://e2emodel-data/fusion_car34_baseline/checkpoint_epoch_9.pth",
        }
        self._change_cfg_params()

    def _change_cfg_params(self):
        self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True
        self.model_cfg["bev_encoder"]["conv_cfg"]["witch_cp"] = True
        # self.data_train_cfg["loader"]["datasets_names"] = ["wm34-mini"]

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateMultiModalData(
            **self.data_train_cfg,
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=8,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            num_workers=4,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_model(self):
        model = BEVFusionCenterHead(
            model_cfg=self.model_cfg,
        )
        return model


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DMultiModalCli(Exp).run()
