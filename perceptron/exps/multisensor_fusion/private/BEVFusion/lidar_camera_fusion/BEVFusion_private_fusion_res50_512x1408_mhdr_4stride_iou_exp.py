# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : <EMAIL>
# *Date         : 2023-04-13
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 \
# *               --cpu=32 --gpu=8 --memory=180000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/lidar_camera_fusion/BEVFusion_private_fusion_res50_512x1408_mhdr_4stride_iou_exp.py \
# *               --no-clearml --sync_bn 3 -b 1 -e 10
# -------------------------------------------------------------------------------------------
import torch
import mmcv
from torch.utils.data import DistributedSampler

from perceptron.engine.cli import Det3DMultiModalCli
from perceptron.utils import torch_dist as dist

from perceptron.data.det3d.private.private_multimodal import PrivateMultiModalData
from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar_camera_fusion.BEVFusion_private_fusion_res50_256x704_mhdr_4stride_iou_base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_fusion_cfg_wm2 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.num_keep_latest_ckpt = 8
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)

        # load mulit pretrain
        self.multi_pretrain_cfg = {
            "camera_encoder": "s3://e2emodel-data/multi_modal_exps/BEVFusion_private_camera_res50_512input_4stride_4w_30e/checkpoint_epoch_29.pth",
            "lidar_encoder": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_lidar_exp_layerv2/checkpoint_epoch_19.pth",
            "bev_encoder": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_lidar_exp_layerv2/checkpoint_epoch_19.pth",
            "det_head": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_lidar_exp_layerv2/checkpoint_epoch_19.pth",
        }

        # 512 input size
        self.data_train_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (512, 1408)
        self.data_train_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.772, 1.10)
        self.data_val_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (512, 1408)
        self.data_val_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.772, 1.10)
        self.model_cfg["camera_encoder"]["final_dim"] = (512, 1408)
        self._change_cfg_params()

    def _change_cfg_params(self):
        # use checkpoint in camera encoder
        self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True
        self.model_cfg["bev_encoder"]["conv_cfg"]["witch_cp"] = True
        # self.model_cfg["det_head"]["dense_head"]["with_cp"] = True

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateMultiModalData(
            **self.data_train_cfg,
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=8,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            num_workers=4,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DMultiModalCli(Exp).run()
