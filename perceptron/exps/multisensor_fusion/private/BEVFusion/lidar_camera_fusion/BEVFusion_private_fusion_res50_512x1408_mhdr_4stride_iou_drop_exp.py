# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : <EMAIL>
# *Date         : 2023-04-13
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 \
# *               --cpu=32 --gpu=8 --memory=180000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/lidar_camera_fusion/BEVFusion_private_fusion_res50_512x1408_mhdr_4stride_iou_drop_exp.py \
# *               --no-clearml --sync_bn 3 -b 1 -e 10
# -------------------------------------------------------------------------------------------
import torch
import mmcv
from typing import Dict, List, Any
from torch.nn import functional as F
from torch.utils.data import DistributedSampler

from perceptron.engine.cli import Det3DMultiModalCli
from perceptron.utils import torch_dist as dist

from perceptron.data.det3d.private.private_multimodal import PrivateMultiModalData
from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar_camera_fusion.BEVFusion_private_fusion_res50_256x704_mhdr_4stride_iou_base_exp import (
    Exp as BaseExp,
    BEVFusionCenterHead as BaseBEVFusion,
)
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_fusion_cfg_wm2 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)


class BEVFusionCenterHead(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)

    def compute_kd_loss(self, lidar_output, camera_output):
        loss_kd = F.mse_loss(camera_output, lidar_output, reduction="none")
        loss_kd = loss_kd * ((camera_output > lidar_output) | (lidar_output > 0)).float()
        loss_kd = loss_kd.sum(-1)
        loss_kd = loss_kd.mean()

        return loss_kd

    def forward(
        self,
        lidar_points: List[torch.tensor] = None,
        imgs: torch.tensor = None,
        radar_points: torch.tensor = None,
        metas: Dict[str, torch.tensor] = None,
        gt_boxes: torch.tensor = None,
        **kwargs,
    ) -> Any:
        if self.with_lidar_encoder:
            lidar_output = self.lidar_encoder(lidar_points)
        else:
            lidar_output = None

        if self.with_camera_encoder:
            camera_output = self.camera_encoder(imgs, metas)
        else:
            camera_output = None

        if self.with_radar_encoder:
            radar_output = self.radar_encoder(radar_points)
        else:
            radar_output = None

        # random drop one modal input
        idx = torch.randint(0, 10, (1,))[0]
        if idx == 0:
            pass
        elif idx < 6:
            camera_output = lidar_output
        else:
            lidar_output = camera_output

        x = self.bev_encoder(lidar_output, camera_output, radar_output)
        forward_ret_dict = self.det_head({"1": x[0], "0": x[1]}, gt_boxes)
        if self.training:
            with torch.cuda.amp.autocast(enabled=False):
                for task_id, encoding in forward_ret_dict["box_encoding"].items():
                    encoding[torch.isinf(encoding)] = 0
                tea = x.detach()
                loss_kd_c3 = self.compute_kd_loss(tea, camera_output)
                loss_kd_c3_lidar = self.compute_kd_loss(tea, lidar_output)
                loss_rpn, tb_dict = self.det_head.dense_head.get_loss(forward_ret_dict)
                tb_dict.update({"loss_rpn": loss_rpn.item()})
                ret_dict = {"loss": loss_rpn + 0.01 * loss_kd_c3 + 0.01 * loss_kd_c3_lidar}
                return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.print_interval = 20
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 1

        self.multi_pretrain_cfg = {
            "camera_encoder": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_fusion_exp_crop/checkpoint_epoch_9.pth",
            "lidar_encoder": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_fusion_exp_crop/checkpoint_epoch_9.pth",
            "fusion_encoder": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_fusion_exp_crop/checkpoint_epoch_9.pth",
            "bev_encoder": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_fusion_exp_crop/checkpoint_epoch_9.pth",
            "det_head": "s3://e2emodel-data/multi_modal_private_dev_exps/bevfusion_private_centerhead_fusion_exp_crop/checkpoint_epoch_9.pth",
        }

        # 512 input size
        self.data_train_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (512, 1408)
        self.data_train_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.66, 0.8)
        self.data_val_cfg["pipeline"]["ida_aug"]["aug_conf"]["final_dim"] = (512, 1408)
        self.data_val_cfg["pipeline"]["ida_aug"]["aug_conf"]["resize_lim"] = (0.66, 0.8)
        self.model_cfg["camera_encoder"]["final_dim"] = (512, 1408)
        self._change_cfg_params()

    def _change_cfg_params(self):
        # use checkpoint in camera encoder
        self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True
        self.model_cfg["bev_encoder"]["conv_cfg"]["witch_cp"] = True
        # self.model_cfg["det_head"]["dense_head"]["with_cp"] = True

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateMultiModalData(
            **self.data_train_cfg,
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=8,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn,
            num_workers=4,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DMultiModalCli(Exp).run()
