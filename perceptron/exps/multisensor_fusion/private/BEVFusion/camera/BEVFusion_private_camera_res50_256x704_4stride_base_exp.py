# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : <EMAIL>
# *Date         : 2023-03-13
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 \
# *               --cpu=32 --gpu=8 --memory=180000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/camera/BEVFusion_private_camera_res50_256x704_4stride_base_exp.py \
# *               --no-clearml --sync_bn 3 -b 1 --amp -e 30
# -------------------------------------------------------------------------------------------
import mmcv
from perceptron.engine.cli import Det3DCli
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer

from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar_camera_fusion.BEVFusion_private_fusion_res50_256x704_4stride_base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_camera_256x704_cfg_wm34 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.fusion_base_256x704_res50_4stride_woiou_cfg import (
    MODEL_CFG,
)


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 3e-4
        self.print_interval = 50
        self.num_keep_latest_ckpt = 8
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.infer_executor_class = Det3DInfer
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.model_cfg["lidar_encoder"] = None
        self._change_cfg_params()

    def _change_cfg_params(self):
        # debug in a mini-dataset
        # self.data_train_cfg["loader"]["datasets_names"] = ["wm34-mini"]
        pass


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
