# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : <EMAIL>
# *Date         : 2023-03-13
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 \
# *               --cpu=32 --gpu=8 --memory=180000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/camera/BEVFusion_private_camera_convnext_256x704_8stride_4w_30e.py \
# *               --no-clearml --sync_bn 3 -b 1 --amp -e 30
# -------------------------------------------------------------------------------------------
from perceptron.engine.cli import Det3DCli

from perceptron.layers.blocks_2d.mmdet2d.convnext import ConvNeXt  # noqa
from perceptron.exps.multisensor_fusion.private.BEVFusion.camera.BEVFusion_private_camera_res50_256x704_8stride_base_exp import (
    Exp as BaseExp,
)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        # using ConvNext as image backbone
        self.model_cfg["camera_encoder"]["img_backbone_conf"] = dict(
            type="ConvNeXt",
            arch="base",
            out_indices=[0, 1, 2, 3],
            drop_path_rate=0.4,
            layer_scale_init_value=1.0,
            gap_before_final_norm=False,
            with_cp=False,  # use checkpoint in img_backbone
            init_cfg=dict(
                type="Pretrained",
                checkpoint="s3://basedet3d/perceptron_codebase/pretrain_weights/convnext_base_mmcls.pth",
                prefix="backbone.",
            ),
        )
        self.model_cfg["camera_encoder"]["img_neck_conf"] = dict(
            type="SECONDFPN",
            in_channels=[128, 256, 512, 1024],
            upsample_strides=[0.25, 0.5, 1, 2],
            out_channels=[128, 128, 128, 128],
        )

        self._change_cfg_params()

    def _change_cfg_params(self):
        # use checkpoint in camera encoder
        # self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True
        # self.model_cfg["bev_encoder"]["conv_cfg"]["witch_cp"] = True
        pass


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
