# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : gong<PERSON><PERSON><PERSON>@megvii.com
# *Date         : 2023-03-29
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 \
# *               --cpu=64 --gpu=8 --memory=1000000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/lidar/BEVFusion_private_lidar_pointpillar_mh_dr_voxel01_csp_pan_repvgg_exp.py \
# *               --no-clearml --sync_bn 2 -b 3 --amp --find_unused_parameters -e 40
# -------------------------------------------------------------------------------------------
import mmcv
import copy
from perceptron.engine.cli import Det3DCli
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer

from perceptron.layers.losses.det3d import CenterNetRegLoss, WeightFocalLossWHDR, WeightedFocalLoss
from perceptron.layers.head.det3d import (
    FCOSAssignerMaskRegFPN,
    IouAwareGenProposalsFPNMerged,
    CenterHeadIouAwareDRMaskMerged,
)
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_base import (
    BEVFusion as BaseBEVFusion,
    DetHead as BaseDetHead,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar_camera_fusion.BEVFusion_private_fusion_res50_256x704_4stride_base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_lidar_cfg_wm34 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.lidar_base_pointpillar_mhdr_voxel01_4stride_iou_cfg import (
    MODEL_CFG,
)
import torch
from torch import nn
from typing import Any, Dict, List
from perceptron.layers.blocks_2d.det3d import BEVFPNv4PillarNoasffCSPSimCSPSPPFRegVGGv6PAN
from perceptron.models.multisensor_fusion import BaseEncoder, ForceFp32
from perceptron.layers.blocks_2d.det3d import PointPillarScatter
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.layers.blocks_3d.det3d import PillarVFE


class DetHead(BaseDetHead):
    def __init__(self, det_head_cfg: mmcv.Config, **kwargs):
        super().__init__(det_head_cfg)

        self.dense_head = self.build_dense_head()

    def build_dense_head(self):
        dense_head_module_list = nn.ModuleList()
        densehead_out_size_factor = self.det_head_cfg.out_size_factor
        densehead_tasks = self.det_head_cfg.tasks
        for i in range(self.det_head_cfg.num_heads):
            target_cfg = self.det_head_cfg.target_assigner
            target_assigner = FCOSAssignerMaskRegFPN(
                out_size_factor=densehead_out_size_factor[i], tasks=densehead_tasks[i], **target_cfg
            )
            proposal_cfg = self.det_head_cfg.proposal_layer
            proposal_layer = IouAwareGenProposalsFPNMerged(
                class_names=[t["class_names"] for t in densehead_tasks[i]],
                out_size_factor=densehead_out_size_factor[i],
                **proposal_cfg,
            )
            head_cfg = self.det_head_cfg.dense_head
            dense_head_module = CenterHeadIouAwareDRMaskMerged(
                tasks=densehead_tasks[i],
                out_size_factor=densehead_out_size_factor[i],
                target_assigner=target_assigner,
                proposal_layer=proposal_layer,
                **head_cfg,
            )

            def _build_losses(m):
                m.add_module(
                    "crit",
                    WeightedFocalLoss(self.det_head_cfg.target_assigner_alpha, self.det_head_cfg.target_assigner_gamma),
                )
                m.add_module("crit_reg", CenterNetRegLoss())
                m.add_module("crit_iou_aware", CenterNetRegLoss())
                m.add_module("crit_dr", WeightFocalLossWHDR())

            _build_losses(dense_head_module)
            dense_head_module_list.append(dense_head_module)

        return dense_head_module_list

    # @ForceFp32(apply_to=("x", "gt_boxes"))
    def forward(self, outputs: torch.tensor, gt_boxes: torch.tensor) -> Any:
        forward_ret_dict_list = list()
        for head_id, x in enumerate(outputs):  # 输出P3-P4
            forward_ret_dict = self.dense_head[head_id](x, gt_boxes)
            forward_ret_dict_list.append(forward_ret_dict)

        return forward_ret_dict_list


class LidarEncoder(BaseEncoder):
    def __init__(self, lidar_encoder_cfg: mmcv.Config, **kwargs) -> Any:
        super().__init__()
        self.cfg = lidar_encoder_cfg
        self.voxelizer = self.build_voxelizer()
        self.vfe = self.build_vfe()
        self.map_to_bev = self.build_map_to_bev()

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
            max_num_points=self.cfg.max_num_points,
            max_voxels=self.cfg.max_voxels,
            num_point_features=self.cfg.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = PillarVFE(
            use_norm=True,
            with_distance=False,
            use_absolute_xyz=True,
            num_filters=self.cfg.vfe_num_filters,
            num_point_features=self.cfg.use_num_point_features,
            voxel_size=self.cfg.voxel_size,
            point_cloud_range=self.cfg.point_cloud_range,
        )
        return vfe

    def build_map_to_bev(self):
        return PointPillarScatter(
            num_bev_features=self.cfg.map_to_bev_num_features,
            grid_size=self.cfg.grid_size,
        )

    @ForceFp32(apply_to=("lidar_points"))
    def forward(self, lidar_points: List[torch.tensor]) -> torch.tensor:
        voxels, voxel_coords, voxel_num_points = self.voxelizer(lidar_points)
        pillar_features = self.vfe(voxels, voxel_coords, voxel_num_points)
        spatial_features = self.map_to_bev(pillar_features, voxel_coords)
        return spatial_features


class BevEncoder(nn.Module):
    def __init__(self, use_elementwise, input_channels, conv_cfg, **kwargs):
        super().__init__()
        self.use_elementwise = use_elementwise
        if not self.use_elementwise:
            output_channels = conv_cfg["input_channels"]
            self.att = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(input_channels, input_channels, kernel_size=1, stride=1),
                nn.Sigmoid(),
            )
            self.reduce_conv = nn.Sequential(
                nn.Conv2d(input_channels, output_channels, 3, padding=1, bias=False),
                nn.BatchNorm2d(output_channels),
                nn.ReLU(True),
            )

        self.conv_cfg = conv_cfg
        self.backbone_2d = self.build_bev_encoder()

    def build_bev_encoder(self):
        bev_encoder = BEVFPNv4PillarNoasffCSPSimCSPSPPFRegVGGv6PAN(**self.conv_cfg)
        return bev_encoder

    # @ForceFp32(apply_to=("lidar_output", "camera_output", "radar_output"))
    def forward(self, lidar_output, camera_output, radar_output):
        feat_list = [feat for feat in [lidar_output, camera_output, radar_output] if feat is not None]
        if self.use_elementwise:
            x = torch.sum(torch.stack(feat_list), 0)
        else:
            x = torch.cat(feat_list, dim=1)
            x = self.reduce_conv(x * self.att(x))

        spatial_features_2d, outputs, pyramids = self.backbone_2d(x)
        return outputs, pyramids


class BEVFusionCenterHead(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)

    def _configure_det_head(self):
        return DetHead(self.cfg.det_head)

    def _configure_lidar_encoder(self):
        return LidarEncoder(self.cfg.lidar_encoder)

    def _configure_bev_encoder(self):
        return BevEncoder(**self.cfg.bev_encoder)

    def forward(
        self,
        lidar_points: List[torch.tensor] = None,
        imgs: torch.tensor = None,
        radar_points: torch.tensor = None,
        metas: Dict[str, torch.tensor] = None,
        gt_boxes: torch.tensor = None,
        **kwargs,
    ) -> Any:
        if self.with_lidar_encoder:
            lidar_output = self.lidar_encoder(lidar_points)
        else:
            lidar_output = None

        if self.with_camera_encoder:
            camera_output = self.camera_encoder(imgs, metas)
        else:
            camera_output = None

        if self.with_radar_encoder:
            radar_output = self.radar_encoder(radar_points)
        else:
            radar_output = None

        x = self.bev_encoder(lidar_output, camera_output, radar_output)
        forward_ret_dict_list = self.det_head(x[0], gt_boxes)
        if self.training:
            with torch.cuda.amp.autocast(enabled=False):
                losses = list()
                tb_dict, ret_dict = dict(), dict()
                for head_id, _ in enumerate(x[0]):
                    forward_ret_dict = forward_ret_dict_list[head_id]
                    for task_id, encoding in forward_ret_dict["box_encoding"].items():
                        encoding[torch.isinf(encoding)] = 0
                    loss_rpn, tb_part_dict = self.det_head.dense_head[head_id].get_loss(forward_ret_dict)
                    tb_dict.update({"loss_rpn_{}".format(head_id): loss_rpn.item()})
                    for key, val in tb_part_dict.items():
                        tb_dict[key + "_{}".format(head_id)] = val
                    losses.append(loss_rpn)
                ret_dict = {"loss": sum(losses)}
                return ret_dict, tb_dict, {}
        else:
            pred_dicts = []
            for idx in range(len(forward_ret_dict_list[0]["pred_dicts"])):
                pred_dict = dict()
                pred_dict["pred_boxes"] = torch.cat(
                    [forward_ret_dict["pred_dicts"][idx]["pred_boxes"] for forward_ret_dict in forward_ret_dict_list]
                )
                pred_dict["pred_scores"] = torch.cat(
                    [forward_ret_dict["pred_dicts"][idx]["pred_scores"] for forward_ret_dict in forward_ret_dict_list]
                )
                pred_dict["pred_labels"] = torch.cat(
                    [forward_ret_dict["pred_dicts"][idx]["pred_labels"] for forward_ret_dict in forward_ret_dict_list]
                )
                pred_dicts.append(pred_dict)

            return {
                "pred_dicts": pred_dicts,
                "rois": torch.cat([forward_ret_dict["rois"] for forward_ret_dict in forward_ret_dict_list], dim=1),
                "roi_scores": torch.cat(
                    [forward_ret_dict["roi_scores"] for forward_ret_dict in forward_ret_dict_list], dim=1
                ),
                "roi_labels": torch.cat(
                    [forward_ret_dict["roi_labels"] for forward_ret_dict in forward_ret_dict_list], dim=1
                ),
                "has_class_labels": True,
            }


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 3e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 8
        self._max_epoch = 40
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.infer_executor_class = Det3DInfer
        self.data_train_cfg = copy.deepcopy(DATA_TRAIN_CFG)
        self.data_val_cfg = copy.deepcopy(DATA_VAL_CFG)

        # only use lidar branch
        self.model_cfg = copy.deepcopy(MODEL_CFG)
        self.model_cfg["camera_encoder"] = None
        self.data_split = {"train": "training", "val": "validation", "test": "testing"}
        self.data_train_cfg["loader"]["datasets_names"] = ["wm34-mini"]

    def _change_cfg_params(self):
        # use checkpoint in bev encoder
        # self.model_cfg["bev_encoder"]["with_cp"] = True
        pass

    def _configure_model(self):
        model = BEVFusionCenterHead(
            model_cfg=mmcv.Config(self.model_cfg),
        )
        return model


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
