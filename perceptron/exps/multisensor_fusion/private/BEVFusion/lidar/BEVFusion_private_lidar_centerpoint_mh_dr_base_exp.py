# -------------------------------------------------------------------------------------------
# *Description  : Runs a DET3D experiment using <PERSON><PERSON>-<PERSON>ope with rlaunch.
# *Author       : <EMAIL>
# *Date         : 2023-03-13
# *CMD          : DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 \
# *               --cpu=32 --gpu=8 --memory=180000 --preemptible=no \
# *               --charged-group=galvatron -- python3 \
# *               perceptron/exps/multisensor_fusion/private/BEVFusion/lidar/BEVFusion_private_centerhead_lidar_voxel01_mhdr_ignore_base_exp.py \
# *               --no-clearml --sync_bn 3 -b 1 --amp -e 40
# -------------------------------------------------------------------------------------------
import mmcv
from perceptron.engine.cli import Det3DCli
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer

from perceptron.layers.losses.det3d import CenterNetRegLoss, WeightFocalLossWHDR, WeightedFocalLoss
from perceptron.layers.head.det3d import IouAwareGenProposals, CenterHeadIouAwareDRMask, FCOSAssignerMaskReg
from perceptron.exps.multisensor_fusion.nuscenes.BEVFusion.BEVFusion_base import (
    BEVFusion as BaseBEVFusion,
    DetHead as BaseDetHead,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.lidar_camera_fusion.BEVFusion_private_fusion_res50_256x704_4stride_base_exp import (
    Exp as BaseExp,
)
from perceptron.exps.multisensor_fusion.private.data_base_cfg.base_pivate_lidar_cfg_wm34 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.lidar_base_centerpoint_mhdr_4stride_iou_cfg import (
    MODEL_CFG,
)


class DetHead(BaseDetHead):
    def __init__(self, det_head_cfg: mmcv.Config, **kwargs):
        super().__init__(det_head_cfg)

    def build_dense_head(self):
        target_cfg = self.det_head_cfg.target_assigner
        target_assigner = FCOSAssignerMaskReg(**target_cfg)

        proposal_cfg = self.det_head_cfg.proposal_layer
        proposal_layer = IouAwareGenProposals(**proposal_cfg)

        head_cfg = self.det_head_cfg.dense_head
        dense_head_module = CenterHeadIouAwareDRMask(
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            **head_cfg,
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                WeightedFocalLoss(self.det_head_cfg.target_assigner_alpha, self.det_head_cfg.target_assigner_gamma),
            )
            m.add_module("crit_reg", CenterNetRegLoss())
            m.add_module("crit_iou_aware", CenterNetRegLoss())
            m.add_module("crit_dr", WeightFocalLossWHDR())

        _build_losses(dense_head_module)

        return dense_head_module


class BEVFusionCenterHead(BaseBEVFusion):
    def __init__(self, model_cfg):
        super().__init__(model_cfg)

    def _configure_det_head(self):
        return DetHead(self.cfg.det_head)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=4, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 1e-3
        self.print_interval = 50
        self.num_keep_latest_ckpt = 8
        self.dump_interval = 1
        self.eval_executor_class = Det3DEvaluator
        self.infer_executor_class = Det3DInfer
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)

        # only use lidar branch
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.model_cfg["camera_encoder"] = None

    def _change_cfg_params(self):
        # use checkpoint in bev encoder
        # self.model_cfg["bev_encoder"]["witch_cp"] = True
        pass

    def _configure_model(self):
        model = BEVFusionCenterHead(
            model_cfg=self.model_cfg,
        )
        return model


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
