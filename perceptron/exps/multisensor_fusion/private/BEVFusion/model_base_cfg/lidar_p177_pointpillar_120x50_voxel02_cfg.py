_POINT_CLOUD_RANGE = [-24.8, 0, -5.0, 24.8, 120, 3.0]
_VOXEL_SIZE = [0.2, 0.2, 8]
_GRID_SIZE = [248, 600, 1]
_OUT_SIZE_FACTOR = 2

CLASS_NAMES = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    "masked_area",
]

_DENSE_TASKS = [
    dict(num_class=1, class_names=["car"]),
    dict(num_class=3, class_names=["truck", "construction_vehicle", "bus"]),
    dict(
        num_class=3,
        class_names=[
            "motorcycle",
            "bicycle",
            "tricycle",
        ],
    ),
    dict(num_class=1, class_names=["cyclist"]),
    dict(num_class=1, class_names=["pedestrian"]),
]

CENTERPOINT_DET_HEAD_CFG = dict(
    class_name=CLASS_NAMES,
    target_assigner=dict(
        densehead_out_size_factor=_OUT_SIZE_FACTOR,
        densehead_tasks=_DENSE_TASKS,
        target_assigner_dense_reg=1,
        target_assigner_gaussian_overlap=0.1,
        target_assigner_max_objs=2500,
        target_assigner_min_radius=2,
        target_assigner_mapping={name: idx + 1 for idx, name in enumerate(CLASS_NAMES)},
        grid_size=_GRID_SIZE,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL_SIZE[0:2],
        target_assigner_topk=9,
        target_assigner_no_log=False,
        with_velocity=False,
    ),
    proposal_layer=dict(
        densehead_dataset_name="private",
        densehead_tasks=_DENSE_TASKS,
        proposal_post_center_limit_range=[
            # [-25.6, -80.0, -5.0, 25.6, 120.0, 3.0],
            # [-25.6, -80.0, -5.0, 25.6, 120.0, 3.0],
            # [-25.6, -80.0, -5.0, 25.6, 80.0, 3.0],
            # [-25.6, -80.0, -5.0, 25.6, 80.0, 3.0],
            # [-25.6, -80.0, -5.0, 25.6, 80.0, 3.0],
            [-24.8, 0, -5.0, 24.8, 120, 3.0],
            [-24.8, 0, -5.0, 24.8, 120, 3.0],
            [-24.8, 0, -5.0, 24.8, 120, 3.0],
            [-24.8, 0, -5.0, 24.8, 120, 3.0],
            [-24.8, 0, -5.0, 24.8, 120, 3.0],
        ],
        proposal_score_threshold=0.1,
        proposal_pc_range=_POINT_CLOUD_RANGE[0:2],
        densehead_out_size_factor=_OUT_SIZE_FACTOR,
        proposal_voxel_size=_VOXEL_SIZE[0:2],
        no_log=False,
        proposal_iou_aware_list=[0.65] * len(_DENSE_TASKS),
        nms_iou_threshold_train=0.8,
        nms_pre_max_size_train=1500,
        nms_post_max_size_train=80,
        nms_iou_threshold_test=0.1,
        nms_pre_max_size_test=2000,
        nms_post_max_size_test=200,
        target_assigner_mapping={
            "car": 1,
            "truck": 2,
            "construction_vehicle": 3,
            "bus": 4,
            "motorcycle": 5,
            "bicycle": 6,
            "tricycle": 7,
            "cyclist": 8,
            "pedestrian": 9,
            "masked_area": 10,
        },
    ),
    dense_head=dict(
        densehead_dataset_name="private",
        densehead_tasks=_DENSE_TASKS,
        densehead_out_size_factor=_OUT_SIZE_FACTOR,
        input_channels=384,  # need to be careful!
        # input_channels=224,  # need to be careful!
        grid_size=_GRID_SIZE,
        point_cloud_range=_POINT_CLOUD_RANGE,
        densehead_loss_code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],  # 回归的weight
        densehead_loss_loc_weight=0.25,
        densehead_loss_iou_weight=5.0,
        densehead_share_conv_channel=64,
        densehead_common_heads=dict(  # common_heads,
            {
                "bbox": [25, 2],
            }
        ),
        upsample_for_pedestrian=False,
        densehead_mode="3d",
        densehead_init_bias=-2.19,
    ),
    target_assigner_alpha=0.25,
    target_assigner_gamma=2,
)

MODEL_CFG = dict(
    class_names=CLASS_NAMES,
    lidar_encoder=dict(
        point_cloud_range=_POINT_CLOUD_RANGE,
        voxel_size=_VOXEL_SIZE,
        grid_size=_GRID_SIZE,
        max_num_points=16,
        max_voxels=(67000, 335000),  # (20000, 100000) 也是够的，普遍在6000～15000之间
        # max_voxels=12000,
        src_num_point_features=4,
        use_num_point_features=4,
        map_to_bev_num_features=10,
        vfe_num_filters=[10],
        use_fake_quant=False,  # # QAT时设置为True
    ),
    bev_encoder=dict(
        # backbone2d_layer_nums=[2, 3, 3],
        # backbone2d_layer_nums=[2, 4, 4],
        backbone2d_layer_nums=[3, 5, 5],
        backbone2d_layer_strides=[2, 2, 2],
        backbone2d_num_filters=[64, 128, 256],
        backbone2d_upsample_strides=[1, 2, 4],
        backbone2d_num_upsample_filters=[128, 128, 128],
        num_bev_features=10,  # sp conv output channel
        backbone2d_use_scconv=False,
        # backbone2d_use_scconv=True,
        backbone2d_upsample_output=False,
        clap_cfg=dict(use_fake_quant=False, fake_quant_scale=1 / 16, quant_scale=8),  # QAT时设置为True
    ),
    det_head=CENTERPOINT_DET_HEAD_CFG,
)
