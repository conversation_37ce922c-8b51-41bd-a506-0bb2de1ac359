_POINT_CLOUD_RANGE = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
_VOXEL_SIZE = [0.1, 0.1, 0.2]
_GRID_SIZE = [1504, 1504, 40]
_IMG_DIM = (256, 704)
_OUT_SIZE_FACTOR = 4

CLASS_NAMES = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
]

_DENSE_TASKS = [
    dict(num_class=1, class_names=["car"]),
    dict(num_class=3, class_names=["truck", "construction_vehicle", "bus"]),
    dict(
        num_class=3,
        class_names=[
            "motorcycle",
            "bicycle",
            "tricycle",
        ],
    ),
    dict(num_class=1, class_names=["cyclist"]),
    dict(num_class=1, class_names=["pedestrian"]),
]

CENTERPOINT_DET_HEAD_CFG = dict(
    class_name=CLASS_NAMES,
    target_assigner=dict(
        out_size_factor=_OUT_SIZE_FACTOR,
        tasks=_DENSE_TASKS,
        dense_reg=1,
        gaussian_overlap=0.1,
        max_objs=2500,
        min_radius=2,
        mapping={name: idx + 1 for idx, name in enumerate(CLASS_NAMES)},
        grid_size=_GRID_SIZE,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL_SIZE[0:2],
        assign_topk=9,
        no_log=False,
        with_velocity=False,
    ),
    proposal_layer=dict(
        dataset_name="private",
        class_names=[t["class_names"] for t in _DENSE_TASKS],
        post_center_limit_range=[-75.2, -75.2, -5.0, 75.2, 75.2, 3.0],
        score_threshold=0.1,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        out_size_factor=_OUT_SIZE_FACTOR,
        voxel_size=_VOXEL_SIZE[0:2],
        no_log=False,
        nms_iou_threshold_train=0.8,
        nms_pre_max_size_train=1500,
        nms_post_max_size_train=80,
        nms_iou_threshold_test=0.1,
        nms_pre_max_size_test=1500,
        nms_post_max_size_test=100,
    ),
    dense_head=dict(
        dataset_name="private",
        tasks=_DENSE_TASKS,
        input_channels=512,  # need to be careful!
        grid_size=_GRID_SIZE,
        point_cloud_range=_POINT_CLOUD_RANGE,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
        loc_weight=0.25,
        share_conv_channel=64,
        common_heads=dict(  # common_heads,
            {
                "reg": [2, 2],
                "height": [1, 2],
                "dim": [3, 2],
                "rot": [2, 2],
            }
        ),
        upsample_for_pedestrian=False,
        predict_boxes_when_training=False,
        mode="3d",
        init_bias=-2.19,
    ),
    target_assigner_alpha=0.25,
    target_assigner_gamma=2,
)

MODEL_CFG = dict(
    class_names=CLASS_NAMES,
    lidar_encoder=dict(
        point_cloud_range=_POINT_CLOUD_RANGE,
        voxel_size=_VOXEL_SIZE,
        grid_size=_GRID_SIZE,
        max_num_points=10,
        max_voxels=(120000, 160000),
        src_num_point_features=4,
        use_num_point_features=4,
        map_to_bev_num_features=256,
    ),
    camera_encoder=dict(
        x_bound=[_POINT_CLOUD_RANGE[0], _POINT_CLOUD_RANGE[3], _VOXEL_SIZE[0] * _OUT_SIZE_FACTOR * 2],
        y_bound=[_POINT_CLOUD_RANGE[1], _POINT_CLOUD_RANGE[4], _VOXEL_SIZE[1] * _OUT_SIZE_FACTOR * 2],
        z_bound=[_POINT_CLOUD_RANGE[2], _POINT_CLOUD_RANGE[5], _POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]],
        d_bound=[2.0, 90.0, 0.5],
        final_dim=_IMG_DIM,
        output_channels=80,
        downsample_factor=16,
        img_backbone_conf=dict(
            type="ResNet",
            depth=50,
            frozen_stages=0,
            out_indices=[0, 1, 2, 3],
            norm_eval=False,
            init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
        ),
        img_neck_conf=dict(
            type="SECONDFPN",
            in_channels=[256, 512, 1024, 2048],
            upsample_strides=[0.25, 0.5, 1, 2],
            out_channels=[128, 128, 128, 128],
        ),
        depth_net_conf=dict(in_channels=512, mid_channels=512),
        extra_channel_adjust=dict(in_channels=80, out_channels=256),
    ),
    bev_encoder=dict(
        use_elementwise=True,
        input_channels=256 * 2,
        conv_cfg=dict(
            input_channels=256,
            layer_nums=[5, 5],
            layer_strides=[1, 2],
            num_filters=[128, 256],
            upsample_strides=[1, 2],
            num_upsample_filters=[256, 256],
            use_scconv=True,
            upsample_output=True,
        ),
    ),
    det_head=CENTERPOINT_DET_HEAD_CFG,
)
