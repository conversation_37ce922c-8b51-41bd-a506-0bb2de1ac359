_POINT_CLOUD_RANGE = [-22.4, -75.2, -5.0, 22.4, 75.2, 3.0]
_VOXEL_SIZE = [0.2, 0.2, 8]
_GRID_SIZE = [224, 752, 1]
_OUT_SIZE_FACTOR = 2

CLASS_NAMES = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    "masked_area",
]


_DENSE_TASKS = [
    dict(num_class=1, class_names=["car"]),
    dict(num_class=3, class_names=["truck", "construction_vehicle", "bus"]),
    dict(
        num_class=3,
        class_names=[
            "motorcycle",
            "bicycle",
            "tricycle",
        ],
    ),
    dict(num_class=1, class_names=["cyclist"]),
    dict(num_class=1, class_names=["pedestrian"]),
]

CENTERPOINT_DET_HEAD_CFG = dict(
    class_name=CLASS_NAMES,
    target_assigner=dict(
        out_size_factor=_OUT_SIZE_FACTOR,
        tasks=_DENSE_TASKS,
        dense_reg=1,
        gaussian_overlap=0.1,
        max_objs=2500,
        min_radius=2,
        mapping={name: idx + 1 for idx, name in enumerate(CLASS_NAMES)},
        grid_size=_GRID_SIZE,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL_SIZE[0:2],
        assign_topk=9,
        no_log=False,
        with_velocity=False,
    ),
    proposal_layer=dict(
        dataset_name="private",
        class_names=[t["class_names"] for t in _DENSE_TASKS],
        post_center_limit_range=_POINT_CLOUD_RANGE,
        score_threshold=0.1,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        out_size_factor=_OUT_SIZE_FACTOR,
        voxel_size=_VOXEL_SIZE[0:2],
        no_log=False,
        iou_aware_list=[0.65] * len(_DENSE_TASKS),
        nms_iou_threshold_train=0.8,
        nms_pre_max_size_train=1500,
        nms_post_max_size_train=80,
        nms_iou_threshold_test=0.1,
        nms_pre_max_size_test=1500,
        nms_post_max_size_test=100,
    ),
    dense_head=dict(
        dataset_name="private",
        tasks=_DENSE_TASKS,
        out_size_factor=_OUT_SIZE_FACTOR,
        input_channels=384,  # need to be careful!
        grid_size=_GRID_SIZE,
        point_cloud_range=_POINT_CLOUD_RANGE,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
        loc_weight=0.25,
        iou_weight=5.0,
        share_conv_channel=64,
        common_heads=dict(  # common_heads,
            {
                "bbox": [9, 2],
            }
        ),
        upsample_for_pedestrian=False,
        mode="3d",
        init_bias=-2.19,
        predict_boxes_when_training=False,
    ),
    target_assigner_alpha=0.25,
    target_assigner_gamma=2,
)

MODEL_CFG = dict(
    class_names=CLASS_NAMES,
    lidar_encoder=dict(
        point_cloud_range=_POINT_CLOUD_RANGE,
        voxel_size=_VOXEL_SIZE,
        grid_size=_GRID_SIZE,
        max_num_points=16,
        max_voxels=(120000, 160000),
        src_num_point_features=4,
        use_num_point_features=4,
        map_to_bev_num_features=64,
        vfe_num_filters=[64],
    ),
    bev_encoder=dict(
        use_elementwise=True,
        input_channels=64,
        conv_cfg=dict(
            input_channels=64,
            layer_nums=[3, 5, 5],
            layer_strides=[2, 2, 2],
            num_filters=[64, 128, 256],
            upsample_strides=[1, 2, 4],
            num_upsample_filters=[128, 128, 128],
            use_scconv=True,
            upsample_output=False,
        ),
    ),
    det_head=CENTERPOINT_DET_HEAD_CFG,
)
