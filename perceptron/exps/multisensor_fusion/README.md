# 融合感知实验说明

## 总体说明

+ 多模态融合感知模型及相关实验，包含公开数据集和业务数据集两大块。

## 目录结构

```zsh
./perceptron/exps/multisensor_fusion
├── multisensor_fusion
|   ├── nuscenes
│   │   ├── ....
│   ├── private   # 业务数据集
│   │   ├── data_base_cfg     # 配置dataset
│   │   ├── BEVFusion    # BEVFusion模型
|   |   |   ├── model_base_cfg # 配置不同模型结构
|   |   |   ├── lidar_camera_fusion # 融合模型实验
|   |   |   ├── lidar # 单Lidar实验
|   |   |   ├── camera # 单camera实验
```

## pytorch 1.13 docker使用说明

```
# step 1: 拉取镜像
rlaunch --image registry.hh-d.brainpp.cn/megvii-transformer-galvatron/perceptron_training:cu117-cudnn8.8-torch1.13 --enable-sshd=true --cpu=30 --gpu=1 --memory=163840 --max-wait-duration=16m0s --volume /data:/data -- zsh

# step 2: 设置环境变量
source /set_env.sh

# step 3: 配置 Perceptron 环境
cd /data/Perceptron
python3 setup.py develop --user

# step 4: 开启模型训练
python3 perceptron/exps/multisensor_fusion/private/BEVFusion/lidar/BEVFusion_private_lidar_centerpoint_mh_dr_base_exp.py -e 40 --no-clearml -b 1 --amp
```
