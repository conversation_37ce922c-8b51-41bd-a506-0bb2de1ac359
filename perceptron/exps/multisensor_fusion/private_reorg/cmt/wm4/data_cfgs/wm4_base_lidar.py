import copy

from perceptron.data.det3d.modules import (
    BevAffineTransformation,
    LidarBase,
    LoaderSimFov,
    AnnotationDet,
    EvaluationV3,
    PointShuffle,
    ObjectRangeFilter,
)
from perceptron.data.det3d.source.config import WMCar4


_CAR = WMCar4

# 暂时先只评测6V
_CAMERA_LIST = [
    # side cameras
    "cam_front_left_120",
    "cam_front_right_120",
    "cam_back_left_120",
    "cam_back_right_120",
    # front and back camera
    "cam_front_120",
    "cam_front_120_sim_fov30",
    "cam_back_120",
]

_LIDAR_LIST = [
    "fuser_lidar",
]

_SENSOR_NAMES = dict(
    camera_names=[],
    lidar_names=_LIDAR_LIST,
)


point_cloud_range = [-25.6, -80, -5, 25.6, 80.0, 3]

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    "masked_area",
]

_PIPELINE = dict(
    bda_aug=dict(
        type=BevAffineTransformation,
        aug_conf=dict(
            rot_lim=(-5, 5),
            scale_lim=(0.95, 1.05),
            trans_lim=(-0.5, 0.5),
            flip_dx_ratio=0.5,
            flip_dy_ratio=0.5,
        ),
        with_trans_z=True,  # 这里需要验证下是否与之前是对齐的！
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
)

base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    sensor_names=_SENSOR_NAMES,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["wm4_train_0601"],  # 这里需要修改！
        only_key_frame=True,
        rebuild=False,
    ),
    lidar=dict(
        type=LidarBase,
        car=_CAR,
        lidar_names=_LIDAR_LIST,
        referen_lidar="front_lidar",
        pc_fields=["x", "y", "z", "i"],
        used_echo_id=[1],
        lidar_sweeps_idx=[],
        lidar_with_timestamp=False,
    ),
    annotation=dict(
        type=AnnotationDet,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=4,  # lidar or fusion, use 4
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
    ),
    pipeline=_PIPELINE,
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="l3",
        eval_cfg_l2="l2",
    ),
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["CHENGQU_BMK"])
val_dataset_cfg["annotation"].update(occlusion_threshold=4)  # for lidar or fusion, use 4
