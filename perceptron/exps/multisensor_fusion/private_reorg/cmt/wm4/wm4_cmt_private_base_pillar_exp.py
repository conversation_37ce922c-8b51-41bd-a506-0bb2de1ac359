# DATA3D_REDIS_URL="redis://mcd-mc-redis-web-main-d-r-p.mcd-mc:6666/0" DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --positive-tags=feature/nvidia-driver=525 --gpu=8 --cpu=32 --memory=200000 --positive-tags=2080ti --preemptible no --max-wait-duration 48h -- python3 perceptron/exps/multisensor_fusion/private_reorg/cmt/wm4/wm4_cmt_private_base_pillar_exp.py -b 3 -d 0-7 -e 20 --no-clearml --amp --sync_bn 1

import mmcv
from perceptron.engine.cli import Det3DCli
from perceptron.models.multisensor_fusion.cmt.private_pillar_base import CMTPillar

from perceptron.exps.multisensor_fusion.private_reorg.cmt.wm4.wm4_cmt_private_base_fusion_exp import Exp as BaseExp
from perceptron.exps.multisensor_fusion.private_reorg.cmt.wm4.model_cfgs.wm4_base_fusion_pillar import (
    MODEL_CFG,
    CLASS_NAMES,
)
from perceptron.exps.multisensor_fusion.private_reorg.cmt.wm4.data_cfgs.wm4_base_fusion_512x960 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        # 加载指定车辆cfg
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg["annotation"]["class_names"] = CLASS_NAMES
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.class_name = CLASS_NAMES

        self._change_cfg_params()

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """

        self.model_cfg["camera_encoder"] = None
        self.model_cfg["det_head"]["modal"] = ["LiDAR"]

    def _configure_model(self):
        model = CMTPillar(
            model_cfg=self.model_cfg,
        )
        return model


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
