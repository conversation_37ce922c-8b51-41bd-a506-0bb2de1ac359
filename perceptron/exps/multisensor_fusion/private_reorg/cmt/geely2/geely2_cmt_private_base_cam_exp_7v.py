# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 2 --cpu=94 --gpu=8 --memory=340000 --group=transformer_3090 --preemptible no --positive-tags=GeForceRTX3090 --negative-tags=node/gpu-2080ti-0055.host.hh-e.brainpp.cn -- python3 ./perceptron/exps/multisensor_fusion/private_reorg/cmt/lidar_camera_fusion/geely2_cmt_private_base_fusion_exp.py  --no-clearml -b 4 -d 0-7 -e 80 --amp  --sync_bn 2
# This is an abstract cmt exp, which is not runnable.

import torch
import mmcv
from torch.utils.data import DistributedSampler
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.data.det3d.private.private_multimodal import PrivateMultiModalData
from perceptron.models.multisensor_fusion.cmt.private_base import CMTPrivate
from perceptron.exps.multisensor_fusion.private_reorg.cmt.geely2.geely2_cmt_private_base_fusion_exp import (
    Exp as BaseExp,
)
from perceptron.exps.multisensor_fusion.private_reorg.cmt.geely2.data_cfgs.geely2_base_cam_512x960_7v import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.multisensor_fusion.private_reorg.cmt.geely2.model_cfgs.geely2_base_fusion_voxel02 import (
    MODEL_CFG,
    CLASS_NAMES,
)
import numpy as np
from perceptron.utils.det3d_utils.common_utils import _load_data_to_gpu


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        # 加载指定车辆cfg
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg["annotation"]["class_names"] = CLASS_NAMES
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.class_name = CLASS_NAMES
        self._change_cfg_params()

        # 不同eval cfg下评测，需要同步修改下dataset里评测规则
        self.normal_eval_config = "cam_gaosu_l3"
        self.far_range_eval_config = "cam_l3_far"

        # import IPython; IPython.embed()

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.data_train_cfg["sensor_names"]["lidar_names"] = []
        self.data_val_cfg["sensor_names"]["lidar_names"] = []
        self.model_cfg["lidar_encoder"] = None
        self.model_cfg["det_head"]["modal"] = ["Camera"]

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateMultiModalData(
            **self.data_train_cfg,
        )
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=5,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn_fill_batch,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn_fill_batch,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_dataset = PrivateMultiModalData(
            **self.data_val_cfg,
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateMultiModalData.collate_fn_fill_batch,
            num_workers=4,
            sampler=DistributedSampler(test_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return test_loader

    def _configure_model(self):
        model = CMTPrivate(
            model_cfg=self.model_cfg,
        )
        return model

    def training_step(self, batch):
        # 支持dpflow的话，dataloader返回值必须为numpy
        batch["roi_mask"] = np.asarray([-85.0000, -25.6000, 85.0000, 25.6000])
        for key, value in batch.items():
            if key in ["imgs", "points", "gt_boxes", "gt_labels", "radar_points", "roi_mask", "frame_id"]:
                batch[key] = value if isinstance(value, torch.Tensor) else torch.from_numpy(value)
            else:
                sub_dict = {}
                for subkey, subvalue in value.items():
                    sub_dict[subkey] = subvalue if isinstance(subvalue, torch.Tensor) else torch.from_numpy(subvalue)
                batch[key] = sub_dict
        if torch.cuda.is_available():
            _load_data_to_gpu(batch)

        if "points" in batch:
            points = [frame_point for frame_point in batch["points"]]
        else:
            points = None

        radar_points = batch.get("radar_points", None)
        imgs = batch.get("imgs", None)
        metas = batch.get("mats_dict", dict())
        roi_mask = batch.get("roi_mask", None)
        gt_boxes = batch["gt_boxes"]
        gt_labels = batch["gt_labels"]

        if "ida_mats" in metas:
            metas["ida_mats"] = metas["ida_mats"].unsqueeze(1)

        gt_labels += 1
        gt_boxes = torch.cat([gt_boxes, gt_labels.unsqueeze(dim=2)], dim=2)
        ret_dict, loss_dict, _ = self.model(
            lidar_points=points,
            cameras_imgs=imgs,
            radar_points=radar_points,
            roi_mask=roi_mask,
            metas=metas,
            gt_boxes=gt_boxes,
        )

        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
