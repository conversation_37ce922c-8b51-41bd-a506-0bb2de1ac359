from perceptron.exps.multisensor_fusion.private_reorg.cmt.geely2.data_cfgs.geely2_base_fusion_512x960 import class_names


CLASS_NAMES = class_names[:-1]  # REMOVE MASK AREA
_POINT_CLOUD_RANGE = [-25.6, -80.0, -5.0, 25.6, 204.8, 3.0]
_VOXEL02_VOXEL_SIZE = [0.2, 0.2, 0.2]
_VOXEL02_GRID_SIZE = [
    (_POINT_CLOUD_RANGE[3] - _POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_POINT_CLOUD_RANGE[4] - _POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]
_VOXEL02_OUT_SIZE_FACTOR = 8
_POST_CENTER_RANGE = [
    _POINT_CLOUD_RANGE[0] - 10,
    _POINT_CLOUD_RANGE[1] - 10,
    -10,
    _POINT_CLOUD_RANGE[3] + 10,
    _POINT_CLOUD_RANGE[4] + 10,
    10,
]
LIDAR_ENCODER_PRIVATE_CFG = dict(
    point_cloud_range=_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    grid_size=_VOXEL02_GRID_SIZE,
    max_num_points=10,
    max_voxels=(120000, 160000),
    src_num_point_features=4,  # 4 or 5?
    use_num_point_features=4,
    map_to_bev_num_features=256,
    backbone_2d=dict(
        layer_nums=[5, 5],
        layer_strides=[1, 2],
        num_filters=[128, 256],
        upsample_strides=[1, 2],
        num_upsample_filters=[256, 256],
        input_channels=256,  # sp conv output channel
        # with_cp=False, # 是否开启，以前是不开的
        use_scconv=False,
        upsample_output=False,
    ),
)

VOXEL02_DET_HEAD = dict(
    type="CMTFusionHead",
    in_channels=512,
    num_query=900,
    # modal="Fusion",
    modal=["Camera", "LiDAR"],
    depth_num=64,
    hidden_dim=256,
    downsample_scale=_VOXEL02_OUT_SIZE_FACTOR,
    grid_size=_VOXEL02_GRID_SIZE,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=len(CLASS_NAMES),
            class_names=CLASS_NAMES,
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=_POST_CENTER_RANGE,
        pc_range=_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=_VOXEL02_VOXEL_SIZE,
        num_classes=len(CLASS_NAMES),
        score_threshold=0.1,  # 新增，默认使用0.1阈值过滤
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=3),
    transformer=dict(
        type="CMTTransformer",
        view_num=6,  # 这个参数务必需要与实际view数目保持一致
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=6,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="PETRMultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
)

VOXEL02_TRAIN_CFG = dict(
    pts=dict(
        dataset="Private",
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=_POINT_CLOUD_RANGE,
        ),
        pos_weight=-1,
        gaussian_overlap=0.1,
        min_radius=2,
        grid_size=_VOXEL02_GRID_SIZE,  # [x_len, y_len, 1]
        voxel_size=_VOXEL02_VOXEL_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        point_cloud_range=_POINT_CLOUD_RANGE,
    )
)

VOXEL01_TEST_CFG = dict(
    pts=dict(
        dataset="Private",
        grid_size=_VOXEL02_GRID_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL02_VOXEL_SIZE[:2],
        nms_type=None,
    )
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        out_indices=(2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=True,
        # init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50")
    ),
    img_neck=dict(
        # type='FPN',
        in_channels=[1024, 2048],
        out_channels=256,
        num_outs=2,
        # with_cp=True,
    ),
)

MODEL_CFG = dict(
    class_names=CLASS_NAMES,
    grid_mask=True,
    lidar_encoder=LIDAR_ENCODER_PRIVATE_CFG,
    camera_encoder=CAMERA_ENCODER_CFG,
    det_head=VOXEL02_DET_HEAD,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
)
