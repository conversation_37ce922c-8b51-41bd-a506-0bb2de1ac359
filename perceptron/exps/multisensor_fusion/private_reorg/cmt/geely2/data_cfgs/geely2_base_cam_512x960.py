import copy

from perceptron.data.det3d.modules import (
    UndistortStandard,
    ImageAffineTransformation,
    BevAffineTransformation,
    ImageBase,
    LoaderBase,
    AnnotationDet,
    EvaluationV3,
    PointShuffle,
    ObjectRangeFilter,
)
from perceptron.data.det3d.source.config import GeelyCar2


_CAR = GeelyCar2

# 暂时先只评测6V
_CAMERA_LIST = [
    "cam_front_70_left",
    "cam_front_left_120",
    "cam_front_right_120",
    "cam_back_120",
    "cam_back_left_120",
    "cam_back_right_120",
]

_LIDAR_LIST = []

_SENSOR_NAMES = dict(
    camera_names=_CAMERA_LIST,
    lidar_names=_LIDAR_LIST,
)

_CAMERA_UNDISTORT_FUNC = dict(
    cam_front_70_left=(UndistortStandard,),
    cam_front_left_120=(UndistortStandard,),
    cam_front_right_120=(UndistortStandard,),
    cam_back_120=(UndistortStandard,),
    cam_back_left_120=(UndistortStandard,),
    cam_back_right_120=(UndistortStandard,),
)

point_cloud_range = [-25.6, -80.0, -5.0, 25.6, 204.8, 3.0]

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    "masked_area",
]


_PIPELINE = dict(
    ida_aug=dict(
        type=ImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            resize_lim=(0.236, 0.25),
            bot_pct_lim=(0.0, 0.0),
            H=2165,
            W=3840,
            rand_flip=True,
            rot_lim=(-5.4, 5.4),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={
            "img_mean": [123.675, 116.28, 103.53],
            "img_std": [58.395, 57.12, 57.375],
            "to_rgb": False,
        },  # 若要兼容老分支模型，需要切换成True，并使用skimage读取图像
    ),
    bda_aug=dict(
        type=BevAffineTransformation,
        aug_conf=dict(
            rot_lim=(-5, 5),
            scale_lim=(0.95, 1.05),
            trans_lim=(-0.5, 0.5),
            flip_dx_ratio=0.5,
            flip_dy_ratio=0.5,
        ),
        with_trans_z=True,  # 这里需要验证下是否与之前是对齐的！
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
)

base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    sensor_names=_SENSOR_NAMES,
    loader=dict(
        type=LoaderBase,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["TRAIN_22Q4_DATALIST", "TRAIN_23Q1_DET_DATALIST", "TRAIN_23Q1_TRACKING_DATALIST"],
        only_key_frame=True,
        rebuild=False,
    ),
    image=dict(
        type=ImageBase,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        undistort=True,
        undistort_func=_CAMERA_UNDISTORT_FUNC,
        target_resolution="800w_org",
    ),
    annotation=dict(
        type=AnnotationDet,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=2,  # 这里需要确认下
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=True,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
    ),
    pipeline=_PIPELINE,
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="cam_gaosu_l3",
        eval_cfg_l2="cam_l3_far",
    ),
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["Geely2_GAOSU_BMK"])
val_dataset_cfg["annotation"].update(occlusion_threshold=1)  # 这里需要确认下!
