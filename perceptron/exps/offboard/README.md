# Offboard

## 1. Det/Tracking 预标生成。

安装 [Washing_Machine](https://git-core.megvii-inc.com/transformer/washing_machine), 进行“预标注环境配置”，其他环境本项目可不配置。

示例：
python3 washing_machine.py run --data_path xxx --det_result_path xxx --track_out_path xxx

批量生成offboard train/val set。

## 2. Offbord数据准备
(1) GT插值

修改补全 private_gt_interpolation.py 中 anno_path后，直接运行
```
python3 track_data/private_gt_interpolation.py
```

(2) TrackData抽取
修改补全 private_extract_track_data_per_class.py.py 中anno_dir (上一步保存地址），直接运行
```
python3 track_data/private_extract_track_data_per_class.py
```


(3) 数据合并: 生成单个训/测数据集pkl文件。
修改补全 merge_official_with_fps.py 中track_data_dir （上一步保存地址）后，直接运行
```
python3 track_data/private_merge_with_fps.py
```

## 3. 动静分类器训练
```bash
rlaunch --gpu=1 --cpu=10 --memory=150000 -- python3 perceptron/exps/offboard/train/motion_state_exp.py --no-clearml
```

## 4. 静态AL
```bash
rlaunch --gpu=8 --cpu=10 --memory=150000 -- python3 perceptron/exps/offboard/train/static_refine_exp.py --no-clearml
```

## 5. 动态AL
```bash
rlaunch --gpu=8 --cpu=10 --memory=150000 -- python3 perceptron/exps/offboard/train/dynamic_refine_exp.py --no-clearml
```

## 可视化

执行`test_xviz_visualizer.py`文件，可视化点云：

```bash
python3 test_xviz_visualizer.py ${JSON_FILE_PATH} ${VIS_FRAME_SAVE_PATH} --en ${END_FRAME}
```
