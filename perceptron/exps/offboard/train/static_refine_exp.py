# # from pcdet.ops.pointnet2.pointnet2_batch.pointnet2_utils import furthest_point_sample, gather_operation # TBD: check pointnet2: batch与stack的区别。
import time
import math
import refile
import IPython
import torch
import pickle
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
from easydict import EasyDict
from torch.utils.data import DataLoader
from torch.utils.data.dataset import Dataset

from loguru import logger
from data3d.transforms import transforms3d
from pointnet import PointNetSeg, PointNetReg
from torch_cluster import fps
from collections import defaultdict
from losses import FocalLoss, L1RegLoss, BinRegLoss
from scipy.spatial.transform.rotation import Rotation

from perceptron_ops.roiaware_pool3d import roiaware_pool3d_utils
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.engine.cli import Det3DCli


class Config:
    # data
    data_path = "s3://ruanhao/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0/merged_max1024_train_rrun.pkl"
    # val_data_path = "s3://ruanhao/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0_val/merged_max1024_val_rrun.pkl"
    # data_path = "s3://ruanhao/offboard/20220225/track_data/646items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0/fpsed_max1024_train_rrun"
    val_data_path = "s3://ruanhao/offboard/20220225/track_data/646items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0_val/merged_max1024_val_rrun.pkl"
    log_dir = "/data/offboard/static_refine/official"
    tag = "exp1_lr0.001_wd5e_4_wbn"
    batch_size = int(32)
    epochs = int(180)  # default 180
    workers = int(0)  # default is 8
    npoints = int(4096)
    no_drop_last = True

    # optimizer lr schedule
    lr = float(0.001)
    weight_decay = float(5e-4)
    milestones = [60, 100, 140]  # when to reduce lr.

    # model params
    seg_loss = "focal"
    reg_loss = "l1"
    seg_prob = float(0.2)  # when pred prob > seg_prob, assume a foreground point.
    seg_pred_dim = int(1)
    mask_mode = "pred"  # choices=["pred", "gt", "none"], use which mask mode when training

    pred_box_angle_type = "sin_cos"  # choices=["sin_cos", "bin_res"], angle representation in pred box

    # loss
    nr_angle_bin = 12
    range_per_angle_bin = 360 / nr_angle_bin

    if pred_box_angle_type == "sin_cos":
        pred_box_dim = 8  # x, y, z, l, w, h, sin, cos
    elif pred_box_angle_type == "bin_res":
        pred_box_dim = 6 + 2 * nr_angle_bin  # x, y, z, l, w, h, res*12, bin_cls*12

    # model
    seg_params = EasyDict(
        {
            "encoder_out_channels": [64, 64, 64, 128, 1024],
            "decoder_out_channels": [512, 256, 128, 128],
            "pred_dim": seg_pred_dim,
            "input_transform": False,
            "feature_transform": False,
            "nr_fore_points": 4096,
            "mask_mode": mask_mode,
            "seg_prob": seg_prob,
        }
    )

    reg_params = EasyDict(
        {
            "encoder_out_channels": [128, 128, 256, 512],
            "decoder_out_channels": [512, 256],
            "pred_dim": pred_box_dim,
            "input_transform": False,
            "feature_transform": False,
        }
    )


config = Config()

config.log_dir = refile.smart_path_join(config.log_dir, config.tag)
if not refile.smart_exists(config.log_dir):
    refile.smart_makedirs(config.log_dir)

logger.add(refile.smart_path_join(config.log_dir, "log.txt"))

logger.info(f"### {torch.cuda.device_count()} GPUS Available !!!")
logger.info(config)


class StaticALDataset(Dataset):
    def __init__(
        self,
        data_path,
        transforms=None,
        train=True,
        npoints=4096,
        max_frames=10,
        pred_box_angle_type="sin_cos",
        use_intensity=False,
    ):
        super().__init__()
        logger.info("Preparing Data...")
        t_start = time.time()

        if not data_path.endswith(".pkl"):  # not pkl file, is directory
            fpsed_data_paths = []
            fpsed_data_subdirs = refile.smart_glob(refile.smart_path_join(data_path, "*"))
            for fpsed_data_subdir in fpsed_data_subdirs:
                fpsed_data_paths.extend(refile.smart_glob(refile.smart_path_join(fpsed_data_subdir, "*.pkl")))
            static_data = []
            for fpsed_data_path in fpsed_data_paths:
                raw_data = pickle.load(refile.s3_load_from(fpsed_data_path))
                static_data.extend(raw_data["static"])
        elif data_path.endswith(".pkl"):  # is pkl file
            raw_data = pickle.load(refile.s3_load_from(data_path))
            static_data = raw_data["static"]
        else:
            assert False, "must be pkl file or directory !!!"

        self.max_frames = max_frames

        t_end = time.time()
        logger.info(f"Data Loading Finished. Taking {t_end - t_start} seconds. Found {len(static_data)} static tracks")

        self.data = static_data
        self.data_augmentor = transforms
        self.npoints = npoints
        self.train = train
        self.pred_box_angle_type = pred_box_angle_type

    def __getitem__(self, idx):

        track_points_world_list = self.data[idx]["track_points_world"]
        boxes_world = self.data[idx]["track_boxes_world"]  # list
        boxes_world = np.stack(boxes_world)  # Nx10

        gt_boxes_world = self.data[idx]["track_gt_world"]
        gt_boxes_world = np.stack(gt_boxes_world)

        nr_frames = len(track_points_world_list)

        # 从存在对应gt中选择start_idx(需要保证至少有1帧中存在前景点)
        # 先做一个简化，只从存在gt的框中进行挑选。
        valid_detbox_idxs = [
            i for i in range(nr_frames) if not np.all(self.data[idx]["track_gt_world"][i][:7] == 0)
        ]  # 存在对应gt的框

        valid_detbox_idxs = np.array(valid_detbox_idxs, dtype=np.int64)

        if self.train:
            if len(valid_detbox_idxs) >= self.max_frames:
                selected_frames = np.random.choice(valid_detbox_idxs, self.max_frames, replace=False)
            else:
                selected_frames = valid_detbox_idxs

            box_idx = np.random.choice(selected_frames)
            box_world = boxes_world[box_idx]
            gt_box_world = gt_boxes_world[box_idx]
        else:  # 消除评测的不确定性。

            # [伪]评测逻辑，真实情况下valid_detbox_idxs只能根据“非空”判断，无法判断是否“有效”。
            valid_boxes_world = boxes_world[valid_detbox_idxs]  # 先取出所有有效的detbox，然后从中选出分数最高的box。
            orders = np.argsort(valid_boxes_world[:, -1])[::-1]  # sort by score.

            valid_detbox_idxs = valid_detbox_idxs[orders]  # sorted by score
            if len(valid_detbox_idxs) >= self.max_frames:
                selected_frames = valid_detbox_idxs[: self.max_frames]
            else:
                selected_frames = valid_detbox_idxs

            # 从挑选出来的frames中挑选score最高的box。
            boxes_world_selected = boxes_world[selected_frames]
            box_world = boxes_world_selected[0]

            gt_boxes_world_selected = gt_boxes_world[selected_frames]
            gt_box_world = gt_boxes_world_selected[0]

        points_world = np.zeros((0, 3))
        for i in selected_frames:
            cur_frame_points = track_points_world_list[i][:, :3]
            points_world = np.concatenate((points_world, cur_frame_points), axis=0)

        points_world_xyz = torch.from_numpy(points_world[:, :3]).float()  # N, 3
        points_world_xyz_new = points_world_xyz

        in_box_idxs = roiaware_pool3d_utils.points_in_boxes_cpu(
            points_world_xyz_new[:, :3], gt_box_world[:7].reshape(1, -1)
        )[
            0
        ]  # MxN -> N #gpu版本有点问题。

        if np.sum(in_box_idxs) == 0:
            print("##############")
            IPython.embed()

        box_world = torch.from_numpy(box_world).float()
        gt_box_world = torch.from_numpy(gt_box_world).float()

        box_world_raw = box_world.clone()
        gt_box_world_raw = gt_box_world.clone()

        box_world = box_world[:7]
        gt_box_world = gt_box_world[:7]
        # box坐标系
        points_standard, gt_box_standard = convert_points_into_box_coord_torch(
            points_world_xyz_new, box_world, gt_box_world
        )  # Nx3, 10

        # 过augmentor, 要求 points： N, C; gt_boxes: M, C
        data_dict = {"gt_boxes": gt_box_standard.view(1, gt_box_standard.shape[0]), "points": points_standard}

        if self.data_augmentor is not None:
            data_dict = self.data_augmentor.forward(data_dict=data_dict)

        gt_box_standard = data_dict["gt_boxes"]  # 1, C
        points_standard = data_dict["points"]  # N, C

        # 生成seg_mask
        in_box_idxs = roiaware_pool3d_utils.points_in_boxes_cpu(points_standard[:, :3], gt_box_standard.reshape(1, -1))[
            0
        ]  # MxN -> N #gpu版本有点问题。
        gt_seg_mask = in_box_idxs

        # points_standard = points_standard.transpose(0, 1)  # C, N
        gt_box_standard = gt_box_standard[0]  # C

        gt_box_annos = torch.zeros(8)
        gt_box_annos[:3] = gt_box_standard[:3]
        gt_box_annos[3:6] = gt_box_standard[3:6] - box_world[3:6]  # 直接回归residual
        if self.pred_box_angle_type == "sin_cos":
            gt_box_annos[6] = math.sin(gt_box_standard[6])  # 先直接回归角度sin/cos值？后续尝试分bin方法！
            gt_box_annos[7] = math.cos(gt_box_standard[6])
        elif self.pred_box_angle_type == "bin_res":  # 尝试分bin方法！
            """x,y,z,l,w,h,res,bin_cls"""
            theta = gt_box_standard[6] / math.pi * 180
            theta = theta if theta >= 0 else (theta + 360)
            gt_box_annos[6] = theta % config.range_per_angle_bin - config.range_per_angle_bin / 2  # res
            gt_box_annos[7] = theta // config.range_per_angle_bin  # bin_cls
        else:
            assert False, "Unsupported value of pred_box_angle_type!!!"

        data_dict = {}
        data_dict["points"] = points_standard  # N, C
        data_dict["gt_boxes"] = gt_box_annos  # C
        data_dict["gt_seg_mask"] = gt_seg_mask  # N

        data_dict["pred_boxes_world"] = box_world_raw
        data_dict["gt_boxes_world"] = gt_box_world_raw
        return data_dict

    def __len__(self):
        return len(self.data)

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        # batch_list: list套dict。 data_dict: dict套list。
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points", "gt_seg_mask"]:
                    ret[key] = val
                elif key in ["gt_boxes", "pred_boxes_world", "gt_boxes_world"]:
                    batch_gt_boxes = torch.stack(val)
                    ret[key] = batch_gt_boxes
                else:
                    assert False
                    # ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class StaticAL_Eval_Dataset(StaticALDataset):
    def __getitem__(self, idx):

        track_points_world_list = self.data[idx]["track_points_world"]
        boxes_world = self.data[idx]["track_boxes_world"]  # list
        boxes_world = np.stack(boxes_world)  # Nx10

        selected_frames = np.argsort(boxes_world[:, -1])[::-1]  # sort by score.

        boxes_world_selected = boxes_world[selected_frames]
        box_world = boxes_world_selected[0]

        points_world = np.zeros((0, 3))
        for i in selected_frames:
            cur_frame_points = track_points_world_list[i][:, :3]
            points_world = np.concatenate((points_world, cur_frame_points), axis=0)

        points_world_xyz = torch.from_numpy(points_world[:, :3]).float()  # N, 3
        points_world_xyz_new = points_world_xyz

        in_box_idxs = roiaware_pool3d_utils.points_in_boxes_cpu(
            points_world_xyz_new[:, :3], box_world[:7].reshape(1, -1)
        )[
            0
        ]  # MxN -> N #gpu版本有点问题。

        box_world = torch.from_numpy(box_world).float()

        box_world_raw = box_world.clone()

        box_world = box_world[:7]
        # box坐标系
        points_standard, boxes_standard = eval_convert_points_into_box_coord_torch(
            points_world_xyz_new, box_world
        )  # Nx3, 10

        # 过augmentor, 要求 points： N, C; gt_boxes: M, C
        data_dict = {"boxes": boxes_standard.view(1, boxes_standard.shape[0]), "points": points_standard}

        if self.data_augmentor is not None:
            data_dict = self.data_augmentor.forward(data_dict=data_dict)

        box_standard = data_dict["boxes"]  # 1, C
        points_standard = data_dict["points"]  # N, C

        # 生成seg_mask
        in_box_idxs = roiaware_pool3d_utils.points_in_boxes_cpu(points_standard[:, :3], box_standard.reshape(1, -1))[
            0
        ]  # MxN -> N #gpu版本有点问题。
        seg_mask = in_box_idxs

        box_standard = box_standard[0]  # C

        data_dict = {}
        data_dict["points"] = points_standard  # N, C
        data_dict["seg_mask"] = seg_mask  # N

        data_dict["pred_boxes_world"] = box_world_raw
        return data_dict

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        # batch_list: list套dict。 data_dict: dict套list。
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points", "seg_mask"]:
                    ret[key] = val
                elif key in ["pred_boxes_world"]:
                    batch_gt_boxes = torch.stack(val)
                    ret[key] = batch_gt_boxes
                else:
                    assert False
                    # ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class StaticRefineModel(nn.Module):
    def __init__(self, seg_params, reg_params):
        super(StaticRefineModel, self).__init__()

        self.seg_net = PointNetSeg(
            encoder_out_channels=seg_params["encoder_out_channels"],
            decoder_out_channels=seg_params["decoder_out_channels"],
            k=seg_params["pred_dim"],
            input_transform=seg_params["input_transform"],
            feature_transform=seg_params["feature_transform"],
        )

        self.reg_net = PointNetReg(
            encoder_out_channels=reg_params["encoder_out_channels"],
            decoder_out_channels=reg_params["decoder_out_channels"],
            k=reg_params["pred_dim"],
            input_transform=reg_params["input_transform"],
            feature_transform=reg_params["feature_transform"],
        )
        self.npoints = seg_params["nr_fore_points"]
        self.mask_mode = seg_params["mask_mode"]
        self.seg_prob = seg_params["seg_prob"]

    # 训/测都有三种mask mode: 1) 使用pred mask 2) 使用gt mask 3) 不使用mask。 须验证哪种效果更佳。
    def forward(self, x, mask_mode, gt_fore_mask):
        mask_mode = self.mask_mode

        raw_points = x  # B, C, N
        seg_feats, seg_trans_i, seg_trans_f = self.seg_net(x)

        fore_points = []

        with torch.no_grad():
            valid_idx = torch.ones(seg_feats.shape[0], device=seg_feats.device).bool()
            if mask_mode == "pred":
                fore_mask_ = seg_feats.transpose(1, 2)  # BxCxN -> BxNxC
                fore_mask_ = F.sigmoid(fore_mask_)
                fore_mask = fore_mask_[:, :, 0] > self.seg_prob

                # TBD: what if preds are all background points? no grident bp for this case.
                nr_fore_points = torch.sum(fore_mask, dim=1)  # B

                valid_idx = nr_fore_points > 0
                nr_valid = torch.sum(valid_idx)
                if nr_valid < fore_mask.shape[0]:
                    logger.info(f"Keep {nr_valid} of {fore_mask.shape[0]} samples.")

            elif mask_mode == "gt":
                assert gt_fore_mask is not None, "Bad GT_FORE_MASK, got NONE!!!"
                fore_mask = gt_fore_mask.bool()

            elif mask_mode == "none":
                fore_mask = torch.ones(seg_feats.shape[0], seg_feats.shape[2]).bool()  # seg_feats: BxCxN

            else:
                assert False, "Unexpected Mask Mode !!!"

            for batch_idx in range(fore_mask.shape[0]):
                if torch.sum(fore_mask[batch_idx]) > 0:
                    fore_points.append(self.get_fore_points(raw_points[batch_idx], fore_mask[batch_idx]))
                else:
                    fore_points.append(
                        torch.zeros(self.npoints, 3, dtype=raw_points.dtype, device=raw_points.device)
                    )  # note that no grad bp for this case.

        fore_points = torch.stack(fore_points)  # BxNxC
        fore_points = fore_points.transpose(1, 2)  # B, N, C -> B, C, N

        reg_feats, reg_trans_i, reg_trans_f = self.reg_net(fore_points)

        # return {
        #     "seg_feats": seg_feats,
        #     "seg_trans_i": seg_trans_i,
        #     "seg_trans_f": seg_trans_f,
        #     "reg_feats": reg_feats,
        #     "reg_trans_i": reg_trans_i,
        #     "reg_trans_f": reg_trans_f,
        # }

        return seg_feats, reg_feats, valid_idx

    def get_fore_points(self, raw_points, fore_mask):  # CxN, N
        raw_points = raw_points.transpose(0, 1)  # CxN -> NxC
        fore_points = raw_points[fore_mask]  # N' x C

        fore_points = fps_for_single_pc(fore_points, self.npoints, random_start=True)

        return fore_points


# 非batch输入。input应为单个点云，shape为 (N, 3)
def fps_for_single_pc(pc, num_points, random_start, return_index=False):
    assert pc.shape[1] == 3, f"Expect feature-dim of 3 (x, y, z), but got {pc.shape[1]} !!!"
    # batch = torch.tensor([0] * pc.shape[0])
    batch = torch.zeros_like(pc[:, 0]).long()
    index = fps(pc, batch, ratio=num_points / pc.shape[0], random_start=random_start)

    if not return_index:
        return pc[index]
    else:
        return pc[index], index


# requir input of torch.Tensor
def convert_points_into_box_coord_torch(points, boxes, gt_boxes):
    boxes_ndim = boxes.ndim
    if boxes.ndim == 1:
        boxes = boxes.view(1, -1)

    if gt_boxes.ndim == 1:
        gt_boxes = gt_boxes.view(1, -1)

    if points.ndim == 2:
        points = points.view(1, points.shape[0], points.shape[1])

    nr_boxes = boxes.shape[0]

    points_standard = []
    gt_boxes_standard = []
    for idx in range(nr_boxes):
        cur_box = boxes[idx]  # xyzlwhr, cat, track_id, score
        cur_points = points[idx]

        cur_points_xyz = cur_points[:, :3]

        # 将点变换到权威坐标系
        translation = cur_box[:3]
        rotation = Rotation.from_euler("z", -cur_box[6]).as_matrix()

        # 先平移到中心，再进行旋转。
        cur_points_standard = cur_points_xyz - translation
        cur_points_standard = torch.matmul(
            torch.from_numpy(rotation).float(), cur_points_standard.transpose(0, 1)
        ).transpose(0, 1)
        # cur_points_box = np.matmul(rotation, cur_points_box)

        # 变换gt
        cur_gt_box = gt_boxes[idx].clone()
        cur_gt_box[:3] = cur_gt_box[:3] - translation  # center
        cur_gt_box[6] = cur_gt_box[6] - cur_box[6]  # rotation

        # gt center也要对应旋转。。。
        cur_gt_box_xyz = cur_gt_box[:3].view(1, -1)  # shape: 1*3
        cur_gt_box_xyz = (
            torch.matmul(torch.from_numpy(rotation).float(), cur_gt_box_xyz.transpose(0, 1)).transpose(0, 1).view(-1)
        )
        cur_gt_box[:3] = cur_gt_box_xyz

        points_standard.append(cur_points_standard)
        gt_boxes_standard.append(cur_gt_box)

    points_standard = torch.stack(points_standard)
    gt_boxes_standard = torch.stack(gt_boxes_standard)

    if boxes_ndim == 1:
        return points_standard[0], gt_boxes_standard[0]
    else:
        return points_standard, gt_boxes_standard


def eval_convert_points_into_box_coord_torch(points, boxes):
    boxes_ndim = boxes.ndim
    if boxes.ndim == 1:
        boxes = boxes.view(1, -1)

    if points.ndim == 2:
        points = points.view(1, points.shape[0], points.shape[1])

    nr_boxes = boxes.shape[0]

    points_standard = []
    boxes_standard = []
    for idx in range(nr_boxes):
        cur_box = boxes[idx]  # xyzlwhr, cat, track_id, score
        cur_points = points[idx]

        cur_points_xyz = cur_points[:, :3]

        # 将点变换到权威坐标系
        translation = cur_box[:3]
        rotation = Rotation.from_euler("z", -cur_box[6]).as_matrix()

        # 先平移到中心，再进行旋转。
        cur_points_standard = cur_points_xyz - translation
        cur_points_standard = torch.matmul(
            torch.from_numpy(rotation).float(), cur_points_standard.transpose(0, 1)
        ).transpose(0, 1)

        # 变换pred box
        cur_pred_box = boxes[idx].clone()
        cur_pred_box[:3] = cur_pred_box[:3] - translation  # center
        cur_pred_box[6] = cur_pred_box[6] - cur_box[6]  # rotation

        points_standard.append(cur_points_standard)
        boxes_standard.append(cur_pred_box)

    points_standard = torch.stack(points_standard)
    boxes_standard = torch.stack(boxes_standard)

    if boxes_ndim == 1:
        return points_standard[0], boxes_standard[0]
    else:
        return points_standard, boxes_standard


def preprocess_batch_data(batch_dict, fps_npoints, to_gpu=True):
    batch_data = batch_dict["points"]  # list 每个元素是 NxC
    batch_gt_boxes = batch_dict["gt_boxes"]  # numpy.array
    batch_gt_seg_mask = batch_dict["gt_seg_mask"]  # list

    # embed()

    # 每个sample分别过fps
    batch_points_list = []
    batch_gt_seg_mask_list = []
    for cur_points, cur_seg_mask in zip(batch_data, batch_gt_seg_mask):
        cur_points = cur_points.float().cuda()
        cur_seg_mask = cur_seg_mask.long().cuda()

        if fps_npoints == -1:  # use all points
            pass
        else:
            cur_points, index = fps_for_single_pc(cur_points, fps_npoints, random_start=True, return_index=True)
            cur_seg_mask = cur_seg_mask[index]

        batch_points_list.append(cur_points)
        batch_gt_seg_mask_list.append(cur_seg_mask)

    batch_points = torch.stack(batch_points_list).transpose(1, 2)  # BxNxC -> BxCxN
    batch_gt_seg_mask = torch.stack(batch_gt_seg_mask_list)  # BxN

    batch_gt_boxes = batch_gt_boxes.float().cuda()

    return batch_points, batch_gt_boxes, batch_gt_seg_mask


def eval_preprocess_batch_data(batch_dict, fps_npoints, to_gpu=True):
    batch_data = batch_dict["points"]  # list 每个元素是 NxC
    batch_seg_mask = batch_dict["seg_mask"]  # list
    batch_pred_boxes_world = batch_dict["pred_boxes_world"]  # 获取label和score

    # 每个sample分别过fps
    batch_points_list = []
    batch_seg_mask_list = []
    for cur_points, cur_seg_mask in zip(batch_data, batch_seg_mask):
        cur_points = cur_points.float().cuda()
        cur_seg_mask = cur_seg_mask.long().cuda()

        if fps_npoints == -1:  # use all points
            pass
        else:
            cur_points, index = fps_for_single_pc(cur_points, fps_npoints, random_start=True, return_index=True)
            cur_seg_mask = cur_seg_mask[index]

        batch_points_list.append(cur_points)
        batch_seg_mask_list.append(cur_seg_mask)

    batch_points = torch.stack(batch_points_list).transpose(1, 2)  # BxNxC -> BxCxN
    batch_seg_mask = torch.stack(batch_seg_mask_list)  # BxN

    batch_pred_boxes_world = batch_pred_boxes_world.float().cuda()

    return batch_points, batch_seg_mask, batch_pred_boxes_world


def get_seg_loss(seg_feats, gt_seg_label, args):
    if args.seg_loss == "ce":
        return F.cross_entropy(seg_feats, gt_seg_label)
    elif args.seg_loss == "focal":
        seg_feats = F.sigmoid(seg_feats)
        return FocalLoss()(seg_feats, gt_seg_label)
    else:
        assert False, "Unsupported Seg Loss Type !!!"


def get_reg_loss(reg_feats, gt_reg_feats, valid_idx, args):
    if args.pred_box_angle_type == "sin_cos":
        return L1RegLoss()(reg_feats, gt_reg_feats, valid_idx)
    elif args.pred_box_angle_type == "bin_res":
        """
        reg_feats: (B, 40), [x,y,z,l,w,h,res*12,bin_cls*12]
        gt_reg_feats: (B, 8), [x,y,z,l,w,h,res,bin_cls]
        """
        # new_reg_feats = torch.zeros((reg_feats.shape[0], 8)).cuda()
        # new_reg_feats[:, 7] = torch.argmax(torch.sigmoid(reg_feats[:, 7:19]), dim=1)    # bin_cls
        # new_reg_feats[:, :7] = reg_feats[:, :7]
        return BinRegLoss()(reg_feats, gt_reg_feats, valid_idx)
    else:
        assert False, "Unsupported Reg Loss Type !!!"


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=config.batch_size, total_devices=1, max_epoch=config.epochs, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 5

    def _configure_train_dataloader(self):
        data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 18, np.pi / 18]),
                # transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                # transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        train_dataset = StaticALDataset(
            data_path=config.data_path,
            train=True,
            transforms=data_augmentor,
            pred_box_angle_type=config.pred_box_angle_type,
        )
        train_loader = DataLoader(
            train_dataset,
            batch_size=config.batch_size,
            shuffle=True,
            num_workers=config.workers,
            pin_memory=True,
            # drop_last=True,
            drop_last=not config.no_drop_last,
            collate_fn=train_dataset.collate_batch,
        )

        return train_loader

    def _configure_val_dataloader(self):
        # data_augmentor = transforms3d.Compose(
        #     [
        #         transforms3d.RandomFlip3D(along_axis="X"),
        #         transforms3d.RandomFlip3D(along_axis="Y"),
        #         transforms3d.GlobalRotation(rot_range=[-np.pi / 18, np.pi / 18]),
        #         # transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
        #         # transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
        #     ]
        # )
        # val_dataset = StaticALDataset(data_path=config.val_data_path, train=False, transforms=data_augmentor)
        val_dataset = StaticALDataset(data_path=config.val_data_path, train=False)
        val_loader = DataLoader(
            val_dataset,
            batch_size=config.batch_size,
            shuffle=False,
            num_workers=config.workers,
            pin_memory=True,
            drop_last=False,  # maybe False is better
            collate_fn=val_dataset.collate_batch,
        )
        return val_loader

    def _configure_model(self):
        model = StaticRefineModel(config.seg_params, config.reg_params)
        logger.info(model)

        return model

    def _configure_optimizer(self):
        optimizer = torch.optim.Adam(self.model.parameters(), lr=config.lr, weight_decay=config.weight_decay)
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = torch.optim.lr_scheduler.MultiStepLR(self.optimizer, milestones=config.milestones, gamma=0.1)
        return scheduler

    def training_step(self, batch):
        batch_data, batch_gt_boxes, batch_gt_seg_mask = preprocess_batch_data(batch, config.npoints)
        preds = self.model(batch_data, mask_mode="gt", gt_fore_mask=batch_gt_seg_mask)  # N, 1

        seg_feats, reg_feats, valid_idx = preds

        batch_gt_seg_mask = batch_gt_seg_mask.unsqueeze(1)  # BxN -> Bx1xN

        seg_loss = get_seg_loss(seg_feats, batch_gt_seg_mask, config)
        reg_loss = get_reg_loss(reg_feats, batch_gt_boxes, valid_idx, config)
        loss = seg_loss + reg_loss

        return loss


if __name__ == "__main__":
    Det3DCli(Exp).run()
