import torch
import numpy as np
from data3d.transforms.transforms3d import check_numpy_to_torch


def rotate_points_along_z(points, angle):
    """
    Args:
        points: (B, N, 3 + C)
        angle: (B), angle along z-axis, angle increases x ==> y
    Returns:

    """
    points, is_numpy = check_numpy_to_torch(points)
    angle, _ = check_numpy_to_torch(angle)

    cosa = torch.cos(angle)
    sina = torch.sin(angle)
    zeros = angle.new_zeros(points.shape[0])
    ones = angle.new_ones(points.shape[0])
    rot_matrix = torch.stack((cosa, sina, zeros, -sina, cosa, zeros, zeros, zeros, ones), dim=1).view(-1, 3, 3).float()
    points_rot = torch.matmul(points[:, :, 0:3], rot_matrix)
    # points_rot = torch.cat((points_rot, points[:, :, 3:]), dim=-1)
    # embed()
    if points.shape[2] > 3:
        points_rot = torch.cat((points_rot, points[:, :, 3:]), dim=-1)
    return points_rot.numpy() if is_numpy else points_rot


class MyGlobalRotation(torch.nn.Module):
    """Global rotation around the Z axis with a random angle sampled from rot_range.

    Returns:
    """

    def __init__(self, rot_range=(-np.pi / 4, np.pi / 4)):
        super().__init__()
        assert len(rot_range) == 2 and rot_range[1] - rot_range[0] > 1e-3
        self.rot_range = rot_range

    def forward(self, data_dict):
        gt_boxes = data_dict["gt_boxes"]
        points = data_dict["points"]

        noise_rotation = np.random.uniform(self.rot_range[0], self.rot_range[1])
        points = rotate_points_along_z(points[np.newaxis, :, :], np.array([noise_rotation]))[0]
        gt_boxes[:, 0:3] = rotate_points_along_z(gt_boxes[np.newaxis, :, 0:3], np.array([noise_rotation]))[0]
        gt_boxes[:, 6] += noise_rotation

        data_dict["gt_boxes"] = gt_boxes
        data_dict["points"] = points
        return data_dict
