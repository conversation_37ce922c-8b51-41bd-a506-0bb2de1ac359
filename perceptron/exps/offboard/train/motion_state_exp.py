import time
import pickle
import refile
import torch
import numpy as np
from loguru import logger
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.utils.data.dataset import Dataset

from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp


class Config:
    # data
    # data_path = "s3://ruanhao/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0/merged_max1024_train_rrun.pkl"
    # val_data_path = "s3://ruanhao/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0_val/merged_max1024_val_rrun.pkl"
    data_path = "s3://ruanhao/offboard/20220225/track_data/646items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0/fpsed_max1024_train_rrun"
    val_data_path = "s3://ruanhao/offboard/20220225/track_data/646items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0_val/merged_max1024_val_rrun.pkl"
    log_dir = "/data/offboard/motion_state/official"
    tag = "exp1_lr0.05_wd5e_4_wobn"

    batch_size = int(64)
    epochs = int(50)
    workers = int(4)
    norm = "none"

    # optimizer and lr schedule
    lr = float(0.05)
    weight_decay = float(5e-4)
    milestones = [40]

    # model params
    loss = "bce"
    # model_path = "outputs/train_motion_reconstruct/2022-02-14T16:31:46/dump_model/checkpoint_epoch_49.pth"
    motion_state_ckpt = "outputs/train_motion_reconstruct/2022-03-06T22:08:42/dump_model/checkpoint_epoch_49.pth"

    out_dim = int(1)
    hidden_channels = [32, 64, 128]

    eval_freq = int(1)  # every n epoch for evaluation
    save_freq = int(10)


config = Config()

config.log_dir = refile.smart_path_join(config.log_dir, config.tag)
if not refile.smart_exists(config.log_dir):
    refile.smart_makedirs(config.log_dir)


class MotionStateDataset(Dataset):
    def __init__(self, data_path, transforms=None):
        super().__init__()
        logger.info("Preparing Data...")
        t_start = time.time()

        if not data_path.endswith(".pkl"):  # not pkl file, is directory
            fpsed_data_paths = []
            fpsed_data_subdirs = refile.smart_glob(refile.smart_path_join(data_path, "*"))
            for fpsed_data_subdir in fpsed_data_subdirs:
                fpsed_data_paths.extend(refile.smart_glob(refile.smart_path_join(fpsed_data_subdir, "*.pkl")))
            dynamic_data = []
            static_data = []
            for fpsed_data_path in fpsed_data_paths:
                raw_data = pickle.load(refile.s3_load_from(fpsed_data_path))
                dynamic_data.extend(raw_data["dynamic"])
                static_data.extend(raw_data["static"])

        elif data_path.endswith(".pkl"):  # is pkl file
            raw_data = pickle.load(refile.s3_load_from(data_path))
            dynamic_data = raw_data["dynamic"]
            static_data = raw_data["static"]
        else:
            assert False, "must be pkl file or directory !!!"

        self.data = dynamic_data + static_data
        self.labels = [1] * len(dynamic_data) + [0] * len(static_data)

        t_end = time.time()
        logger.info(
            f"Data Loading Finished. Taking {t_end - t_start} seconds. Found {len(dynamic_data)} dynamic tracks, {len(static_data)} static tracks, {len(self.data)} tracks"
        )

        self.transforms = transforms

    def __getitem__(self, idx):
        # possibly, tranforms

        # heuristic-based features： the detection box centers’ variance and the begin-to-end distance of the tracked boxes
        # center的方差怎么理解? x, y, z分别的方差吗?
        track_boxes = self.data[idx]["track_boxes_world"]
        track_boxes = np.asarray(track_boxes)  # (N, 10)

        # filter empty frame
        valid_idxs = [i for i in range(track_boxes.shape[0]) if not np.all(track_boxes[i, :7] == 0)]
        track_boxes = track_boxes[valid_idxs, :3]  # xyz

        hfeat = np.zeros(4, dtype=np.float32)
        hfeat[0] = np.var(track_boxes[:, 0])  # 使用方差还是标准差好呢?
        hfeat[1] = np.var(track_boxes[:, 1])
        hfeat[2] = np.var(track_boxes[:, 2])
        hfeat[3] = np.sqrt(np.sum((track_boxes[0] - track_boxes[-1]) ** 2))  # 使用欧氏距离还是欧式距离平方好呢？

        hfeat = torch.from_numpy(hfeat)
        label = self.labels[idx]

        return hfeat, label

    def __len__(self):
        return len(self.data)


class eval_MotionStateDataset(Dataset):
    def __init__(self, data_path, transforms=None):
        super().__init__()
        logger.info("Preparing Data...")
        t_start = time.time()

        if not data_path.endswith(".pkl"):  # not pkl file, is directory
            fpsed_data_paths = []
            fpsed_data_subdirs = refile.smart_glob(refile.smart_path_join(data_path, "*"))
            for fpsed_data_subdir in fpsed_data_subdirs:
                fpsed_data_paths.extend(refile.smart_glob(refile.smart_path_join(fpsed_data_subdir, "*.pkl")))
            raw_data = []
            for fpsed_data_path in fpsed_data_paths:
                one_raw_data = pickle.load(refile.s3_load_from(fpsed_data_path))
                raw_data.extend(one_raw_data)
        elif data_path.endswith(".pkl"):  # is pkl file
            raw_data = pickle.load(refile.s3_load_from(data_path))
        else:
            assert False, "must be pkl file or directory !!!"

        # raw_data = pickle.load(refile.s3_load_from(data_path))

        self.data = raw_data

        t_end = time.time()
        logger.info(f"Data Loading Finished. Taking {t_end - t_start} seconds. {len(self.data)} tracks")

        self.transforms = transforms

    def __getitem__(self, idx):
        track_boxes = self.data[idx]["track_boxes_world"]
        track_boxes = np.asarray(track_boxes)  # (N, 10)

        # filter empty frame
        valid_idxs = [i for i in range(track_boxes.shape[0]) if not np.all(track_boxes[i, :7] == 0)]
        track_boxes = track_boxes[valid_idxs, :3]  # xyz

        hfeat = np.zeros(4, dtype=np.float32)
        hfeat[0] = np.var(track_boxes[:, 0])  # 使用方差还是标准差好呢?
        hfeat[1] = np.var(track_boxes[:, 1])
        hfeat[2] = np.var(track_boxes[:, 2])
        hfeat[3] = np.sqrt(np.sum((track_boxes[0] - track_boxes[-1]) ** 2))  # 使用欧氏距离还是欧式距离平方好呢？

        hfeat = torch.from_numpy(hfeat)

        return hfeat

    def __len__(self):
        return len(self.data)


# 二元分类。根据静/动态样本比例，考虑是否使用focal loss
def get_model(hidden_channels, input_dim=4, out_dim=1, norm="bn"):
    if isinstance(hidden_channels, int):
        hidden_channels = [hidden_channels]
    layers = []
    fc_channels = [input_dim] + hidden_channels + [out_dim]
    for i in range(len(fc_channels) - 1):
        layers.append(nn.Linear(fc_channels[i], fc_channels[i + 1]))
        if i < len(fc_channels) - 2:
            if norm == "none":
                pass
            elif norm == "bn":
                layers.append(nn.BatchNorm1d(fc_channels[i + 1]))
            layers.append(nn.ReLU())

    layers.append(nn.Sigmoid())
    model = nn.Sequential(*layers)
    return model


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=config.batch_size, total_devices=1, max_epoch=config.epochs, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        # self.dump_interval = 5

    def _configure_train_dataloader(self):
        train_dataset = MotionStateDataset(data_path=config.data_path)
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=True,
            num_workers=config.workers,
            pin_memory=True,
            drop_last=True,
        )
        logger.info(f"#################### {len(train_loader)} batches in train #################")
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = MotionStateDataset(data_path=config.val_data_path)
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            num_workers=config.workers,
            pin_memory=True,
            drop_last=False,
        )
        logger.info(f"#################### {len(val_loader)} batches in val #################")
        return val_loader

    def _configure_train_loader_for_eval(self):
        train_dataset = MotionStateDataset(data_path=config.data_path)
        train_loader_for_eval = DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            num_workers=config.workers,
            pin_memory=True,
            drop_last=False,
        )
        return train_loader_for_eval

    def _configure_model(self):
        model = get_model(hidden_channels=config.hidden_channels, out_dim=config.out_dim, norm=config.norm)
        logger.info(model)
        return model

    def _configure_optimizer(self):
        optimizer = torch.optim.SGD(
            self.model.parameters(), lr=config.lr, momentum=0.9, weight_decay=config.weight_decay
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = torch.optim.lr_scheduler.MultiStepLR(self.optimizer, milestones=config.milestones, gamma=0.1)
        return scheduler

    def training_step(self, batch):
        batch_data, labels = batch
        batch_data.float().cuda()
        preds = self.model(batch_data)  # N, 1
        preds = preds.view(-1)  # N,
        loss = self.loss_func(preds, labels.float().cuda())
        return loss

    def test_step(self, batch):
        batch.float().cuda()
        return self.model(batch)

    def loss_func(self, pred, target):
        if config.loss == "bce":
            return F.binary_cross_entropy(pred, target)
        elif config.loss == "ce":
            return F.cross_entropy(pred, target)
        else:
            assert False, f"Currenlty, {config.loss} loss is not implemented"

    @torch.no_grad()
    def validation(self, model, dataloader):
        model.eval()
        all_preds = torch.zeros(0).cuda()
        all_labels = torch.zeros(0).cuda()
        for idx, (batch_data, batch_labels) in enumerate(dataloader):
            batch_data = batch_data.cuda()
            batch_labels = batch_labels.cuda()

            preds = model(batch_data).view(-1)
            all_preds = torch.cat((all_preds, preds), axis=0)  # N,
            all_labels = torch.cat((all_labels, batch_labels), axis=0)  # N,

        all_preds = (all_preds > 0.5).float()
        acc = torch.sum((all_preds == all_labels)) / all_labels.shape[0]
        return acc.item()

    def eval_acc(self):
        model_path = config.motion_state_ckpt
        self.model.load_state_dict(torch.load(model_path)["model_state"])

        # 5. possibly, validation
        acc = self.validation(self.model.cuda(), self._configure_val_dataloader())
        logger.info(f"Evaluation:  {acc: .4f} accuracy")


if __name__ == "__main__":
    # train
    # logger.add(refile.smart_path_join(config.log_dir, "log.txt"))
    Det3DCli(Exp).run()

    # eval
    # Exp().eval_acc()
