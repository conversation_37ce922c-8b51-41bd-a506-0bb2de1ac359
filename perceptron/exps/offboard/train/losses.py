import torch
import torch.nn as nn
import torch.nn.functional as F


class FocalLoss(nn.Module):
    """nn.Module warpper for focal loss"""

    def __init__(self, alpha=0.5, gamma=2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma

    def _neg_loss(self, pred, gt):
        """Modified focal loss. Exactly the same as CornerNet.
        Runs faster and costs a little bit more memory
        Arguments:
        #   pred (batch x c x h x w)
        #   gt_regr (batch x c x h x w)

        pred (batch x c x n)
        gt_regr (batch x c x n)
        """

        assert pred.shape == gt.shape, f"UnMatch Shape!!! pred: {pred.shape} vs gt: {gt.shape}"
        # import IPython; IPython.embed()
        pos_inds = gt.eq(1).long()
        neg_inds = gt.eq(0).long()

        loss = 0

        pos_loss = torch.log(pred) * torch.pow(1 - pred, self.gamma) * pos_inds * self.alpha
        neg_loss = torch.log(1 - pred) * torch.pow(pred, self.gamma) * neg_inds * (1 - self.alpha)

        num_pos = pos_inds.float().sum()

        pos_loss = pos_loss.sum()
        neg_loss = neg_loss.sum()
        # num_pos = reduce_mean(num_pos)

        if num_pos == 0:
            loss = loss - neg_loss
        else:
            loss = loss - (pos_loss + neg_loss) / num_pos
        return loss

    def forward(self, out, target):
        return self._neg_loss(out, target)


class L1RegLoss(nn.Module):
    """Regression loss for an output tensor
    Arguments:
      output (batch x dim x h x w)
      mask (batch x max_objects)
      ind (batch x max_objects)
      target (batch x max_objects x dim)
    """

    def __init__(self):
        super(L1RegLoss, self).__init__()

    def forward(self, regr, gt_regr, valid_mask=None):
        """L1 regression loss
        Arguments:
        regr (batch x dim)
        gt_regr (batch x dim)
        valid_mask (batch) bool
        """
        num = regr.shape[0]

        if valid_mask is not None:
            regr = regr[valid_mask]
            gt_regr = gt_regr[valid_mask]

        loss = torch.abs(regr - gt_regr)
        loss = torch.sum(loss)

        # num = reduce_mean(num)
        loss = loss / (num + 1e-4)
        return loss


class BinRegLoss(nn.Module):
    """
    bin_res = bin_loss + res_loss
    """

    def forward(self, regr, gt_regr, valid_mask=None):
        """
        regr (torch.Tensor): (B, 30), [x, y, z, l, w, h, res*12, bin_cls*12]
        gt_regr (torch.Tensor): (B, 8), [x, y, z, l, w, h, res, bin_cls]
        """

        assert (regr.shape[1] - 6) % 2 == 0, "Invalid Bin Num!!"
        nr_angle_bin = int((regr.shape[1] - 6) / 2)

        l1_loss = L1RegLoss()(regr[:, :6], gt_regr[:, :6])  # [x, y, z, l, w, h]

        # bin loss
        bin_pred = regr[:, 6 + nr_angle_bin :]
        bin_gt = gt_regr[:, 7].long()
        bin_loss = F.cross_entropy(bin_pred, bin_gt)

        # res loss
        raw_res_pred = regr[:, 6 : 6 + nr_angle_bin]  # (B, 12)
        mask = F.one_hot(bin_gt, num_classes=nr_angle_bin).bool()  # (B, 12)
        res_pred = torch.masked_select(raw_res_pred, mask)  # 选取bin_gt对应位置的res
        res_gt = gt_regr[:, 6]
        res_loss = L1RegLoss()(res_pred, res_gt)

        return l1_loss + res_loss + bin_loss
