# from pcdet.ops.pointnet2.pointnet2_batch.pointnet2_utils import furthest_point_sample, gather_operation # TBD: check pointnet2: batch与stack的区别。
import pickle
import time
import torch
import math
import refile
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
from loguru import logger
from easydict import EasyDict
from scipy.spatial.transform.rotation import Rotation
from collections import defaultdict
from torch.utils.data.dataset import Dataset
from torch.utils.data import DataLoader

from perceptron_ops.roiaware_pool3d import roiaware_pool3d_utils
from pointnet import PointNetRegLayer, PointNetSeg, PointNetReg
from losses import FocalLoss, L1RegLoss, BinRegLoss
from torch_cluster import fps
from data3d.transforms import transforms3d

from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.engine.cli import Det3DCli


class Config:
    # data
    data_path = "s3://ruanhao/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0/merged_max1024_train_rrun.pkl"
    # data_path = "s3://ruanhao/offboard/20220225/track_data/646items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0/fpsed_max1024_train_rrun"
    log_dir = "/data/offboard/dynamic_refine/official"
    tag = "exp1_lr0.001_wd5e_4_wbn"
    batch_size = int(32)
    epochs = int(500)
    workers = int(0)  # default is 8
    npoints = int(5120)
    no_drop_last = True

    # optimizer and lr schedule
    lr = float(0.001)
    weight_decay = float(5e-4)
    milestones = [180, 300, 420]  # when to reduce lr.

    # model params
    seg_loss = "focal"
    reg_loss = "l1"
    seg_prob = float(0.2)  # when pred prob > seg_prob, assume a foreground point.
    seg_pred_dim = int(1)
    weight_reg_point = float(0.3)
    weight_reg_traj = float(0.3)
    weight_reg_joint = float(0.4)
    mask_mode = "pred"  # choices=["pred", "gt", "none"], use which mask mode when training
    box_stream_angle_type = "sin_cos"  # choices=["sin_cos", "radius"], angle representation in box stream
    pred_box_angle_type = "sin_cos"  # choices=["sin_cos", "bin_res"], angle representation in pred box

    if box_stream_angle_type == "sin_cos":
        box_stream_in_channel = 9  # x, y, z, l, w, h, sin, cos, t
    elif box_stream_angle_type == "radius":
        box_stream_in_channel = 8  # x, y, z, l, w, h, radius, t

    nr_angle_bin = 12
    range_per_angle_bin = 360 / nr_angle_bin

    if pred_box_angle_type == "sin_cos":
        pred_box_dim = 8  # x, y, z, l, w, h, sin, cos
    elif pred_box_angle_type == "bin_res":
        pred_box_dim = 6 + 2 * nr_angle_bin  # x, y, z, l, w, h, res*12, bin_cls*12

    # model
    seg_params = EasyDict(
        {
            "in_channel": 4,
            "encoder_out_channels": [64, 64, 64, 128, 1024],
            "decoder_out_channels": [512, 256, 128, 128],
            "pred_dim": seg_pred_dim,
            "input_transform": False,
            "feature_transform": False,
            "nr_fore_points": 5120,
            "mask_mode": mask_mode,
            "seg_prob": seg_prob,
        }
    )
    point_seq_params = EasyDict(
        {
            "in_channel": 4,
            "encoder_out_channels": [64, 128, 256, 512],
            "decoder_out_channels": [512, 256],
            "pred_dim": -1,
            "input_transform": False,
            "feature_transform": False,
        }
    )
    box_seq_params = EasyDict(
        {
            "in_channel": box_stream_in_channel,
            "encoder_out_channels": [64, 64, 128, 512],
            "decoder_out_channels": [128, 128],
            "pred_dim": -1,
            "input_transform": False,
            "feature_transform": False,
        }
    )
    reg_point_params = EasyDict({"input_channel": 256, "out_channels": [128, 128], "pred_dim": pred_box_dim})
    reg_traj_params = EasyDict({"input_channel": 128, "out_channels": [128, 128], "pred_dim": pred_box_dim})
    reg_joint_params = EasyDict({"input_channel": 384, "out_channels": [128, 128], "pred_dim": pred_box_dim})


config = Config()

config.log_dir = refile.smart_path_join(config.log_dir, config.tag)

if not refile.smart_exists(config.log_dir):
    refile.smart_makedirs(config.log_dir)

logger.add(refile.smart_path_join(config.log_dir, "log.txt"))

logger.info(f"### {torch.cuda.device_count()} GPUS Available !!!")
logger.info(config)


def convert_points_into_box_coord_torch(mid_box, raw_points):
    """将点转为box坐标系
    Args:
        mid_box (torch.Tensor):    (8,), [x,y,z,l,w,h,r,time_embed]
        raw_points (torch.Tensor): (5120,4), [x,y,z,time_embed], 5120 = 1024fps * 5fSrames
    Return:
        points (torch.Tensor):     (5120,4), [x,y,z,time_embed], 5120 = 1024fps * 5fSrames
    """
    points = raw_points.clone()

    # center frame info
    translation = mid_box[:3]
    rotation = Rotation.from_euler("z", -mid_box[6]).as_matrix()

    # 将点变换到权威坐标系
    # 先平移到中心，再进行旋转。
    cur_points_standard = points[:, :3] - translation
    cur_points_standard = torch.matmul(
        torch.from_numpy(rotation).float(), cur_points_standard.transpose(0, 1)
    ).transpose(0, 1)
    points[:, :3] = cur_points_standard

    return points


def convert_box_into_box_coord_torch(mid_box, raw_boxes):
    """将框转为box坐标系
    Args:
        mid_box (torch.Tensor):      (8,), [x,y,z,l,w,h,r,time_embed]
        raw_boxes (torch.Tensor):    (101,8), [x,y,z,l,w,h,r,time_embed]
    Return:
        boxes_standard (torch.Tensor): (101,8), [x,y,z,l,w,h,r,time_embed]
    """
    # center frame info
    translation = mid_box[:3]
    rotation = Rotation.from_euler("z", -mid_box[6]).as_matrix()

    boxes = raw_boxes.clone()

    boxes_standard = []
    nr_boxes = raw_boxes.shape[0]
    for idx in range(nr_boxes):
        cur_box = boxes[idx]

        if not torch.all(cur_box[:7]):  # 如果全为0, 则不进行坐标转换
            boxes_standard.append(cur_box)
        else:
            cur_box[:3] = cur_box[:3] - translation  # center
            cur_box[6] = cur_box[6] - mid_box[6]  # rotation

            # center也要对应旋转。。。
            cur_gt_box_xyz = cur_box[:3].view(1, -1)  # shape: 1*3
            cur_gt_box_xyz = (
                torch.matmul(torch.from_numpy(rotation).float(), cur_gt_box_xyz.transpose(0, 1))
                .transpose(0, 1)
                .view(-1)
            )
            cur_box[:3] = cur_gt_box_xyz
            boxes_standard.append(cur_box)
    boxes_standard = torch.stack(boxes_standard)

    return boxes_standard


def gennerate_box_stream(center_idx, nr_frames_box_seq, nr_frames, track_boxes):
    # 构建box stream
    boxes_stream = []
    box_half_nframes = (nr_frames_box_seq - 1) // 2
    for i in range(-box_half_nframes, box_half_nframes + 1):
        real_i = center_idx + i
        if real_i >= 0 and real_i < nr_frames:
            cur_frame_box = track_boxes[real_i]  # C
        else:
            cur_frame_box = np.zeros((track_boxes.shape[1]), dtype=np.float)  # C

        box_seq_emd = np.ones((1,)) * 0.1 * i
        cur_frame_box = np.concatenate((cur_frame_box, box_seq_emd), axis=0)
        boxes_stream.append(cur_frame_box)

    boxes_stream = np.stack(boxes_stream)  # TxC, shape: (101,8)

    return boxes_stream


class DynamicALDataset(Dataset):
    def __init__(
        self,
        data_path,
        transforms=None,
        train=True,
        npoints=1024,
        nr_frames_point_seq=5,
        nr_frames_box_seq=101,
        use_intensity=False,
        pred_box_angle_type="sin_cos",
    ):
        super().__init__()
        logger.info("Preparing Data...")
        t_start = time.time()

        if not data_path.endswith(".pkl"):  # not pkl file, is directory
            fpsed_data_paths = []
            fpsed_data_subdirs = refile.smart_glob(refile.smart_path_join(data_path, "*"))
            for fpsed_data_subdir in fpsed_data_subdirs:
                fpsed_data_paths.extend(refile.smart_glob(refile.smart_path_join(fpsed_data_subdir, "*.pkl")))
            dynamic_data = []
            for fpsed_data_path in fpsed_data_paths:
                raw_data = pickle.load(refile.s3_load_from(fpsed_data_path))
                dynamic_data.extend(raw_data["dynamic"])
        elif data_path.endswith(".pkl"):  # is pkl file
            raw_data = pickle.load(refile.s3_load_from(data_path))
            dynamic_data = raw_data["dynamic"]
        else:
            assert False, "must be pkl file or directory !!!"

        t_end = time.time()
        logger.info(
            f"Data Loading Finished. Taking {t_end - t_start} seconds. Found {len(dynamic_data)} dynamic tracks"
        )

        self.data = dynamic_data
        self.data_augmentor = transforms
        self.npoints = npoints
        self.nr_frames_point_seq = nr_frames_point_seq
        self.nr_frames_box_seq = nr_frames_box_seq
        self.train = train
        self.pred_box_angle_type = pred_box_angle_type

    def __getitem__(self, idx):
        track_points_world_list = self.data[idx]["track_points_world"]

        track_boxes_world_list = self.data[idx]["track_boxes_world"]
        track_boxes_world = np.stack(track_boxes_world_list)[:, :7]

        track_gt_boxes_world_list = self.data[idx]["track_gt_world"]
        track_gt_boxes_world = np.stack(track_gt_boxes_world_list)[:, :7]
        nr_frames = len(track_points_world_list)

        # 先随机挑选一个存在gt的框，然后构建前后各2帧。即共计5帧。
        valid_detbox_idxs = [
            i for i in range(nr_frames) if not np.all(self.data[idx]["track_gt_world"][i][:7] == 0)
        ]  # 存在对应gt的框
        center_idx = np.random.choice(valid_detbox_idxs, 1)[0]

        # 构建point stream, 同时生成seg_mask
        points_stream_world = []
        point_half_nframes = (self.nr_frames_point_seq - 1) // 2
        gt_seg_mask = []
        for i in range(-point_half_nframes, point_half_nframes + 1):
            real_i = center_idx + i
            # print(real_i)
            if real_i >= 0 and real_i < nr_frames:
                cur_frame_points = track_points_world_list[real_i][:, :3]
            else:
                cur_frame_points = np.zeros((self.npoints, 3), dtype=np.float)

            seq_emd = np.ones((cur_frame_points.shape[0], 1)) * 0.1 * i
            cur_frame_points = np.concatenate((cur_frame_points, seq_emd), axis=1)  # shape: 1024*4
            points_stream_world.append(cur_frame_points)  # NxC

            if real_i >= 0 and real_i < nr_frames:
                gt_box_world = track_gt_boxes_world[real_i]
                in_box_idxs = roiaware_pool3d_utils.points_in_boxes_cpu(
                    cur_frame_points[:, :3], gt_box_world.reshape(1, -1)
                )[
                    0
                ]  # MxN -> N #gpu版本有点问题。
                cur_frame_gt_seg_mask = in_box_idxs  # shape: (2014,)
            else:
                cur_frame_gt_seg_mask = np.zeros((self.npoints,), dtype=np.float)
            gt_seg_mask.append(cur_frame_gt_seg_mask)
        gt_seg_mask = np.concatenate(gt_seg_mask, axis=0)  # shape: (5120,)
        points_stream_world = np.concatenate(points_stream_world, axis=0)  # NxC, shape: (5120,4)

        boxes_stream_world = gennerate_box_stream(center_idx, self.nr_frames_box_seq, nr_frames, track_boxes_world)
        gt_boxes_stream_world = gennerate_box_stream(
            center_idx, self.nr_frames_box_seq, nr_frames, track_gt_boxes_world
        )

        # box坐标系
        mid_boxes_id = boxes_stream_world.shape[0] // 2
        mid_box = boxes_stream_world[mid_boxes_id]

        mid_box = torch.from_numpy(mid_box).float()
        points_stream_world = torch.from_numpy(points_stream_world).float()
        boxes_stream_world = torch.from_numpy(boxes_stream_world).float()
        gt_boxes_stream_world = torch.from_numpy(gt_boxes_stream_world).float()

        points_stream_standard = convert_points_into_box_coord_torch(mid_box, points_stream_world)
        boxes_stream_standard = convert_box_into_box_coord_torch(mid_box, boxes_stream_world)
        gt_boxes_stream_standard = convert_box_into_box_coord_torch(mid_box, gt_boxes_stream_world)

        points_stream_standard = points_stream_standard.numpy()
        boxes_stream_standard = boxes_stream_standard.numpy()
        gt_boxes_stream_standard = gt_boxes_stream_standard.numpy()

        """
        # 暂时不过aug
        # 过augmentor, 要求 points： N, C; gt_boxes: M, C
        # gt_and_box_standard = np.concatenate(
        #     (gt_boxes_stream_standard, boxes_stream_standard), axis=0
        # )  # 注意box与gt_box都需要过aug。因此可以将它们concat后传入，之后再取出来。
        # data_dict = {"gt_boxes": gt_and_box_standard, "points": points_stream_standard}

        # data_dict = self.data_augmentor.forward(data_dict=data_dict)

        # points_stream_standard = data_dict["points"]  # N, C
        # gt_and_box_standard = data_dict["gt_boxes"]  # 1, C

        # gt_boxes_stream_standard = gt_and_box_standard[: self.nr_frames_box_seq, :7]  # shape: (101,7)
        # boxes_stream_standard = gt_and_box_standard[self.nr_frames_box_seq :, :]  # shape: (101,8)
        """

        assert gt_boxes_stream_standard.shape[0] == boxes_stream_standard.shape[0]

        # 这里需要将空帧置为0，sin和cos都置为0
        if config.box_stream_angle_type == "sin_cos":
            theta = boxes_stream_standard[:, 6:7]  # T, 1
            nr_theta = theta.shape[0]
            sin_theta, cos_theta = np.zeros((nr_theta, 1)), np.zeros((nr_theta, 1))
            for i in range(nr_theta):
                if not np.all(boxes_stream_standard[i, :6]):  # xyzlwh都是0的框，sin_cos置为0
                    sin_theta[i], cos_theta[i] = 0, 0
                else:
                    sin_theta[i] = np.sin(theta[i])
                    cos_theta[i] = np.cos(theta[i])
            sin_cos = np.concatenate((sin_theta, cos_theta), axis=1)  # T, 2
            boxes_stream_standard = np.concatenate(
                (boxes_stream_standard[:, :6], sin_cos, boxes_stream_standard[:, 7:8]), axis=1
            )
        elif config.box_stream_angle_type == "radius":
            pass
        else:
            assert False, "Unsupported value of box_stream_angle_type!!!"

        mid_idx = boxes_stream_standard.shape[0] // 2  # shape: 50
        gt_box_standard = gt_boxes_stream_standard[mid_idx]

        gt_box_annos = np.zeros((8,))
        gt_box_annos[:3] = gt_box_standard[:3]
        gt_box_annos[3:6] = gt_box_standard[3:6] - boxes_stream_standard[mid_idx, 3:6]  # 直接回归residual
        if self.pred_box_angle_type == "sin_cos":
            gt_box_annos[6] = math.sin(gt_box_standard[6])  # 直接回归角度sin/cos值
            gt_box_annos[7] = math.cos(gt_box_standard[6])
        elif self.pred_box_angle_type == "bin_res":  # bin方法
            """变为: x, y, z, l, w, h, res, bin_cls"""
            theta = gt_box_standard[6] / math.pi * 180
            theta = theta if theta >= 0 else (theta + 360)
            gt_box_annos[6] = theta % config.range_per_angle_bin - config.range_per_angle_bin / 2  # res
            gt_box_annos[7] = theta // config.range_per_angle_bin  # bin_cls

        data_dict = {}
        data_dict["points"] = torch.from_numpy(points_stream_standard).float()  # N, C
        data_dict["boxes"] = torch.from_numpy(boxes_stream_standard).float()
        data_dict["gt_boxes"] = torch.from_numpy(gt_box_annos).float()  # C
        data_dict["gt_seg_mask"] = torch.from_numpy(gt_seg_mask).float()  # N
        return data_dict

    def __len__(self):
        return len(self.data)

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        # batch_list: list套dict。 data_dict: dict套list。
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points", "gt_seg_mask"]:
                    ret[key] = val
                elif key in ["boxes", "gt_boxes"]:
                    batch_boxes = torch.stack(val)  # BxTxC
                    ret[key] = batch_boxes
                else:
                    assert False
                    # ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class DynamicAL_Eval_Dataset(DynamicALDataset):
    def __getitem__(self, idx):
        track_points_world_list = self.data[idx]["track_points_world"]

        track_boxes_world_list = self.data[idx]["track_boxes_world"]
        track_boxes_world = np.stack(track_boxes_world_list)[:, :7]
        track_boxes_world_raw = np.stack(track_boxes_world_list)

        nr_frames = len(track_points_world_list)

        # 先随机挑选一个存在gt的框，然后构建前后各2帧。即共计5帧。
        valid_detbox_idxs = [
            i for i in range(nr_frames) if not np.all(self.data[idx]["track_boxes_world"][i][:7] == 0)
        ]  # 存在对应pred的框
        center_idx = np.random.choice(valid_detbox_idxs, 1)[0]

        # 构建point stream, 同时生成seg_mask
        points_stream_world = []
        point_half_nframes = (self.nr_frames_point_seq - 1) // 2
        seg_mask = []
        for i in range(-point_half_nframes, point_half_nframes + 1):
            real_i = center_idx + i
            # print(real_i)
            if real_i >= 0 and real_i < nr_frames:
                cur_frame_points = track_points_world_list[real_i][:, :3]
            else:
                cur_frame_points = np.zeros((self.npoints, 3), dtype=np.float)

            seq_emd = np.ones((cur_frame_points.shape[0], 1)) * 0.1 * i
            cur_frame_points = np.concatenate((cur_frame_points, seq_emd), axis=1)
            points_stream_world.append(cur_frame_points)  # NxC

            if real_i >= 0 and real_i < nr_frames:
                box_world = track_boxes_world[real_i]
                in_box_idxs = roiaware_pool3d_utils.points_in_boxes_cpu(
                    cur_frame_points[:, :3], box_world.reshape(1, -1)
                )[
                    0
                ]  # MxN -> N #gpu版本有点问题。
                cur_frame_seg_mask = in_box_idxs
            else:
                cur_frame_seg_mask = np.zeros((self.npoints,), dtype=np.float)
            seg_mask.append(cur_frame_seg_mask)
        seg_mask = np.concatenate(seg_mask, axis=0)
        points_stream_world = np.concatenate(points_stream_world, axis=0)  # NxC

        # 构建box stream
        boxes_stream_world = []
        boxes_stream_world_raw = []
        box_half_nframes = (self.nr_frames_box_seq - 1) // 2
        for i in range(-box_half_nframes, box_half_nframes + 1):
            real_i = center_idx + i
            if real_i >= 0 and real_i < nr_frames:
                cur_frame_box = track_boxes_world[real_i]  # C
                cur_frame_box_raw = track_boxes_world_raw[real_i]  # C
            else:
                cur_frame_box = np.zeros((track_boxes_world.shape[1]))  # C
                cur_frame_box_raw = np.zeros((track_boxes_world_raw.shape[1]))  # C

            box_seq_emd = np.ones((1,)) * 0.1 * i
            cur_frame_box = np.concatenate((cur_frame_box, box_seq_emd), axis=0)
            cur_frame_box_raw = np.concatenate((cur_frame_box_raw, box_seq_emd), axis=0)
            boxes_stream_world.append(cur_frame_box)
            boxes_stream_world_raw.append(cur_frame_box_raw)

        boxes_stream_world = np.stack(boxes_stream_world)  # Tx8, [x, y, z, l, h, r, box_seq_emd]
        boxes_stream_world_raw = np.stack(
            boxes_stream_world_raw
        )  # Tx11, [x, y, z, l, h, r, label, track_id, score, box_seq_emd]

        # box坐标系
        mid_boxes_id = boxes_stream_world.shape[0] // 2
        mid_box = boxes_stream_world[mid_boxes_id]

        mid_box = torch.from_numpy(mid_box).float()
        points_stream_world = torch.from_numpy(points_stream_world).float()
        boxes_stream_world = torch.from_numpy(boxes_stream_world).float()

        points_stream_standard = convert_points_into_box_coord_torch(mid_box, points_stream_world)
        boxes_stream_standard = convert_box_into_box_coord_torch(mid_box, boxes_stream_world)

        points_stream_standard = points_stream_standard.numpy()
        boxes_stream_standard = boxes_stream_standard.numpy()

        data_dict = {
            "gt_boxes": boxes_stream_standard,
            "points": points_stream_standard,
        }  # gt_boxes只是临时当作keys值,aug后会取出来。改为其他的keys值会报错

        data_dict = self.data_augmentor.forward(data_dict=data_dict)

        points_stream_standard = data_dict["points"]  # N, C
        boxes_stream_standard = data_dict["gt_boxes"]  # 1, C

        # 这里需要将空帧置为0，sin和cos都置为0
        if config.box_stream_angle_type == "sin_cos":
            theta = boxes_stream_standard[:, 6:7]  # T, 1
            nr_theta = theta.shape[0]
            sin_theta, cos_theta = np.zeros((nr_theta, 1)), np.zeros((nr_theta, 1))
            for i in range(nr_theta):
                if not np.all(boxes_stream_standard[i, :6]):  # xyzlwh都是0的框，sin_cos置为0
                    sin_theta[i], cos_theta[i] = 0, 0
                else:
                    sin_theta[i] = np.sin(theta[i])
                    cos_theta[i] = np.cos(theta[i])
            sin_cos = np.concatenate((sin_theta, cos_theta), axis=1)  # T, 2
            boxes_stream_standard = np.concatenate(
                (boxes_stream_standard[:, :6], sin_cos, boxes_stream_standard[:, 7:8]), axis=1
            )
        elif config.box_stream_angle_type == "radius":
            pass
        else:
            assert False, "Unsupported value of box_stream_angle_type!!!"

        mid_idx = boxes_stream_standard.shape[0] // 2

        data_dict = {}
        data_dict["points"] = torch.from_numpy(points_stream_standard).float()  # N, C
        data_dict["boxes"] = torch.from_numpy(boxes_stream_standard).float()
        data_dict["seg_mask"] = torch.from_numpy(seg_mask).float()  # N
        data_dict["boxes_world_raw_mid_idx"] = torch.from_numpy(
            boxes_stream_world_raw[mid_idx]
        ).float()  # (11, ) [x, y, z, l, h, r, label, track_id, score, box_seq_emd]
        return data_dict

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        # batch_list: list套dict。 data_dict: dict套list。
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points", "seg_mask"]:
                    ret[key] = val
                elif key in ["boxes", "gt_boxes"]:
                    batch_boxes = torch.stack(val)  # BxTxC
                    ret[key] = batch_boxes
                elif key in ["boxes_world_raw_mid_idx"]:
                    batch_boxes = torch.stack(val)  # BxTxC
                    ret[key] = batch_boxes
                else:
                    assert False
                    # ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class DynamicRefineModel(nn.Module):
    def __init__(
        self, seg_params, point_seq_params, box_seq_params, reg_point_params, reg_traj_params, reg_joint_params
    ):
        super(DynamicRefineModel, self).__init__()

        self.seg_net = PointNetSeg(
            encoder_out_channels=seg_params["encoder_out_channels"],
            decoder_out_channels=seg_params["decoder_out_channels"],
            k=seg_params["pred_dim"],
            input_transform=seg_params["input_transform"],
            feature_transform=seg_params["feature_transform"],
            in_channel=seg_params["in_channel"],
        )

        # PointNetReg: encoder (global feat) + decoder
        self.point_seq_encoder = PointNetReg(
            encoder_out_channels=point_seq_params["encoder_out_channels"],
            decoder_out_channels=point_seq_params["decoder_out_channels"],
            k=point_seq_params["pred_dim"],
            input_transform=point_seq_params["input_transform"],
            feature_transform=point_seq_params["feature_transform"],
            in_channel=point_seq_params["in_channel"],
        )

        self.box_seq_encoder = PointNetReg(
            encoder_out_channels=box_seq_params["encoder_out_channels"],
            decoder_out_channels=box_seq_params["decoder_out_channels"],
            k=box_seq_params["pred_dim"],
            input_transform=box_seq_params["input_transform"],
            feature_transform=box_seq_params["feature_transform"],
            in_channel=box_seq_params["in_channel"],
        )

        self.point_reg_net = PointNetRegLayer(
            input_channel=reg_point_params["input_channel"],
            out_channels=reg_point_params["out_channels"],
            k=reg_point_params["pred_dim"],
        )

        self.traj_reg_net = PointNetRegLayer(
            input_channel=reg_traj_params["input_channel"],
            out_channels=reg_traj_params["out_channels"],
            k=reg_traj_params["pred_dim"],
        )

        self.joint_reg_net = PointNetRegLayer(
            input_channel=reg_joint_params["input_channel"],
            out_channels=reg_joint_params["out_channels"],
            k=reg_joint_params["pred_dim"],
        )

        self.npoints = seg_params["nr_fore_points"]
        self.mask_mode = seg_params["mask_mode"]
        self.seg_prob = seg_params["seg_prob"]

    # 训/测都有三种mask mode: 1) 使用pred mask 2) 使用gt mask 3) 不使用mask。 须验证哪种效果更佳。
    def forward(self, points, boxes, gt_fore_mask):
        mask_mode = self.mask_mode

        raw_points = points  # B, C, N
        seg_feats, seg_trans_i, seg_trans_f = self.seg_net(points)

        fore_points = []

        with torch.no_grad():
            valid_idx = torch.ones(seg_feats.shape[0], device=seg_feats.device).bool()
            if mask_mode == "pred":
                fore_mask_ = seg_feats.transpose(1, 2)  # BxCxN -> BxNxC
                fore_mask_ = F.sigmoid(fore_mask_)
                fore_mask = fore_mask_[:, :, 0] > self.seg_prob

                # TBD: what if preds are all background points? no grident bp for this case.
                nr_fore_points = torch.sum(fore_mask, dim=1)  # B

                valid_idx = nr_fore_points > 0
                nr_valid = torch.sum(valid_idx)
                if nr_valid < fore_mask.shape[0]:
                    logger.info(f"Keep {nr_valid} of {fore_mask.shape[0]} samples.")

            elif mask_mode == "gt":
                assert gt_fore_mask is not None, "Bad GT_FORE_MASK, got NONE!!!"
                fore_mask = gt_fore_mask.bool()

            elif mask_mode == "none":
                fore_mask = torch.ones(seg_feats.shape[0], seg_feats.shape[2]).bool()  # seg_feats: BxCxN

            else:
                assert False, "Unexpected Mask Mode !!!"

            for batch_idx in range(fore_mask.shape[0]):
                if torch.sum(fore_mask[batch_idx]) > 0:
                    fore_points.append(self.get_fore_points(raw_points[batch_idx], fore_mask[batch_idx]))
                else:
                    fore_points.append(
                        torch.zeros(self.npoints, 4, dtype=raw_points.dtype, device=raw_points.device)
                    )  # note that no grad bp for this case.

        fore_points = torch.stack(fore_points)  # BxNxC
        fore_points = fore_points.transpose(1, 2)  # B, N, C -> B, C, N

        point_emd, _, _ = self.point_seq_encoder(fore_points)  # B, C
        traj_emd, _, _ = self.box_seq_encoder(boxes)  # B, C

        joint_emd = torch.cat((point_emd, traj_emd), dim=1)

        point_reg = self.point_reg_net(point_emd)
        traj_reg = self.traj_reg_net(traj_emd)
        joint_reg = self.joint_reg_net(joint_emd)

        # return {
        #     "seg_feats": seg_feats,
        #     "seg_trans_i": seg_trans_i,
        #     "seg_trans_f": seg_trans_f,
        #     "reg_feats": reg_feats,
        #     "reg_trans_i": reg_trans_i,
        #     "reg_trans_f": reg_trans_f,
        # }

        return seg_feats, point_reg, traj_reg, joint_reg, valid_idx

    def get_fore_points(self, raw_points, fore_mask):  # CxN, N
        raw_points = raw_points.transpose(0, 1)  # CxN -> NxC
        fore_points = raw_points[fore_mask]  # N' x C

        # embed()
        fore_points = fps_for_single_pc_v2(fore_points, self.npoints, random_start=True)
        # for sanity
        fore_points = fore_points[: self.npoints, :]

        return fore_points


def get_seg_loss(seg_feats, gt_seg_label, args):
    if args.seg_loss == "ce":
        return F.cross_entropy(seg_feats, gt_seg_label)
    elif args.seg_loss == "focal":
        seg_feats = F.sigmoid(seg_feats)
        return FocalLoss()(seg_feats, gt_seg_label)
    else:
        assert False, "Unsupported Seg Loss Type !!!"


def get_reg_loss(reg_feats, gt_reg_feats, valid_idx, args):
    if args.pred_box_angle_type == "sin_cos":
        return L1RegLoss()(reg_feats, gt_reg_feats, valid_idx)
    elif args.pred_box_angle_type == "bin_res":
        """
        reg_feats: (B, 40), [x,y,z,l,w,h,res*12,bin_cls*12]
        gt_reg_feats: (B, 8), [x,y,z,l,w,h,res,bin_cls]
        """
        return BinRegLoss()(reg_feats, gt_reg_feats, valid_idx)
    else:
        assert False, "Unsupported Reg Loss Type !!!"


def preprocess_batch_data(batch_dict, fps_npoints, to_gpu=True):
    batch_data = batch_dict["points"]  # list 每个元素是 NxC
    batch_boxes = batch_dict["boxes"]  # numpy.array. BxTxC
    batch_gt_boxes = batch_dict["gt_boxes"]  # numpy.array BxC
    batch_gt_seg_mask = batch_dict["gt_seg_mask"]  # list

    # embed()

    # 每个sample分别过fps
    batch_points_list = []
    batch_gt_seg_mask_list = []
    for cur_points, cur_seg_mask in zip(batch_data, batch_gt_seg_mask):
        cur_points = cur_points.float().cuda()
        cur_seg_mask = cur_seg_mask.long().cuda()

        # print(cur_points.shape)

        if fps_npoints == -1:  # use all points
            pass
        else:
            cur_points_fps, index = fps_for_single_pc_v2(cur_points, fps_npoints, random_start=True, return_index=True)
            cur_seg_mask = cur_seg_mask[index]

        if cur_points_fps.shape[0] != fps_npoints:
            # print(f"{cur_points_fps.shape[0]} vs {fps_npoints}")
            cur_points_fps = cur_points_fps[:fps_npoints, :]
            cur_seg_mask = cur_seg_mask[:fps_npoints]

        batch_points_list.append(cur_points_fps)
        batch_gt_seg_mask_list.append(cur_seg_mask)

    batch_points = torch.stack(batch_points_list).transpose(1, 2)  # BxNxC -> BxCxN
    batch_gt_seg_mask = torch.stack(batch_gt_seg_mask_list)  # BxN

    batch_boxes = batch_boxes.float().cuda()  # B*N*9
    batch_boxes = batch_boxes.transpose(1, 2)  # B*9*N
    batch_gt_boxes = batch_gt_boxes.float().cuda()  # B*8

    return batch_points, batch_boxes, batch_gt_boxes, batch_gt_seg_mask


def eval_preprocess_batch_data(batch_dict, fps_npoints, to_gpu=True):
    batch_data = batch_dict["points"]  # list 每个元素是 NxC
    batch_boxes = batch_dict["boxes"]  # numpy.array. BxTxC
    batch_seg_mask = batch_dict["seg_mask"]  # list
    batch_boxes_world_raw = batch_dict["boxes_world_raw_mid_idx"]

    # 每个sample分别过fps
    batch_points_list = []
    batch_seg_mask_list = []
    for cur_points, cur_seg_mask in zip(batch_data, batch_seg_mask):
        cur_points = cur_points.float().cuda()
        cur_seg_mask = cur_seg_mask.long().cuda()

        if fps_npoints == -1:  # use all points
            pass
        else:
            cur_points_fps, index = fps_for_single_pc_v2(cur_points, fps_npoints, random_start=True, return_index=True)
            cur_seg_mask = cur_seg_mask[index]

        if cur_points_fps.shape[0] != fps_npoints:
            cur_points_fps = cur_points_fps[:fps_npoints, :]
            cur_seg_mask = cur_seg_mask[:fps_npoints]

        batch_points_list.append(cur_points_fps)
        batch_seg_mask_list.append(cur_seg_mask)

    batch_points = torch.stack(batch_points_list).transpose(1, 2)  # BxNxC -> BxCxN
    batch_seg_mask = torch.stack(batch_seg_mask_list)  # BxN

    batch_boxes = batch_boxes.float().cuda()  # B*N*9
    batch_boxes = batch_boxes.transpose(1, 2)  # B*9*N
    batch_boxes_world_raw = batch_boxes_world_raw.float().cuda()  # B*N*11

    return batch_points, batch_boxes, batch_seg_mask, batch_boxes_world_raw


# 支持传入pc shape为 Nx(3+C)，即除了xyz坐标外，还有C维的feature，例如time embedding。
def fps_for_single_pc_v2(pc, num_points, random_start, return_index=False):
    pc_xyz, pc_feat = pc[:, :3], pc[:, 3:]
    batch = torch.zeros_like(pc[:, 0]).long()
    index = fps(pc, batch, ratio=num_points / pc.shape[0], random_start=random_start)

    pc_xyz = pc_xyz[index]
    pc_feat = pc_feat[index]

    pc_final = torch.cat((pc_xyz, pc_feat), dim=1)
    if not return_index:
        return pc_final
    else:
        return pc_final, index


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=config.batch_size, total_devices=1, max_epoch=config.epochs, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 5

    def _configure_train_dataloader(self):
        data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 18, np.pi / 18]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        train_dataset = DynamicALDataset(
            data_path=config.data_path,
            train=True,
            transforms=data_augmentor,
            pred_box_angle_type=config.pred_box_angle_type,
        )
        train_loader = DataLoader(
            train_dataset,
            batch_size=config.batch_size,
            shuffle=True,
            num_workers=config.workers,
            pin_memory=True,
            # drop_last=True,
            drop_last=not config.no_drop_last,
            collate_fn=train_dataset.collate_batch,
        )

        return train_loader

    def _configure_model(self):
        model = DynamicRefineModel(
            config.seg_params,
            config.point_seq_params,
            config.box_seq_params,
            config.reg_point_params,
            config.reg_traj_params,
            config.reg_joint_params,
        )
        logger.info(model)

        return model

    def _configure_optimizer(self):
        optimizer = torch.optim.Adam(self.model.parameters(), lr=config.lr, weight_decay=config.weight_decay)
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = torch.optim.lr_scheduler.MultiStepLR(self.optimizer, milestones=config.milestones, gamma=0.1)
        return scheduler

    def training_step(self, batch):
        batch_data, batch_boxes, batch_gt_boxes, batch_gt_seg_mask = preprocess_batch_data(batch, config.npoints)
        seg_feats, point_reg, traj_reg, joint_reg, valid_idx = self.model(
            batch_data, batch_boxes, gt_fore_mask=batch_gt_seg_mask
        )  # N, 1

        batch_gt_seg_mask = batch_gt_seg_mask.unsqueeze(1)  # BxN -> Bx1xN

        seg_loss = get_seg_loss(seg_feats, batch_gt_seg_mask, config)
        reg_loss_point = get_reg_loss(point_reg, batch_gt_boxes, valid_idx, config) * config.weight_reg_point
        reg_loss_traj = get_reg_loss(traj_reg, batch_gt_boxes, valid_idx, config) * config.weight_reg_traj
        reg_loss_joint = get_reg_loss(joint_reg, batch_gt_boxes, valid_idx, config) * config.weight_reg_joint
        reg_loss = reg_loss_point + reg_loss_traj + reg_loss_joint

        loss = seg_loss + reg_loss

        return loss


if __name__ == "__main__":
    Det3DCli(Exp).run()
