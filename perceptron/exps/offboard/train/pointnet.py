from __future__ import print_function
import torch
import torch.nn as nn
import torch.nn.parallel
import torch.utils.data
from pointnet_raw import STN3d, STNkd


class PointNetFeatEncoder(nn.Module):
    def __init__(self, out_channels, in_channel=3, global_feat=True, input_transform=False, feature_transform=False):
        super(PointNetFeatEncoder, self).__init__()
        self.input_transform = input_transform
        self.feature_transform = feature_transform
        layers = []

        if input_transform:
            self.stn = STN3d()

        out_channels = [in_channel] + out_channels

        self.conv1 = nn.Sequential(
            torch.nn.Conv1d(out_channels[0], out_channels[1], 1),
            nn.BatchNorm1d(out_channels[1]),
            nn.ReLU(),
        )

        self.conv2 = nn.Sequential(
            torch.nn.Conv1d(out_channels[1], out_channels[2], 1),
            nn.BatchNorm1d(out_channels[2]),
            nn.ReLU(),
        )

        if feature_transform:
            self.fstn = STNkd(k=64)

        for i in range(2, len(out_channels) - 1):
            layers.append(torch.nn.Conv1d(out_channels[i], out_channels[i + 1], 1))
            layers.append(nn.BatchNorm1d(out_channels[i + 1]))
            layers.append(nn.ReLU())

        self.encoder = nn.Sequential(*layers)

        self.out_dim = out_channels[-1]
        self.global_feat = global_feat

    def forward(self, x):
        n_pts = x.size()[2]  # B, C, N
        if self.input_transform:
            trans = self.stn(x)
            x = x.transpose(2, 1)
            x = torch.bmm(x, trans)
            x = x.transpose(2, 1)
        else:
            trans = None

        x = self.conv1(x)
        x = self.conv2(x)

        if self.feature_transform:
            trans_feat = self.fstn(x)
            x = x.transpose(2, 1)
            x = torch.bmm(x, trans_feat)
            x = x.transpose(2, 1)
        else:
            trans_feat = None

        pointfeat = x  # out of the second MLP layer.

        x = self.encoder(pointfeat)  # B, C, N
        x = torch.max(x, 2, keepdim=True)[0]  # B, C, 1
        x = x.view(-1, self.out_dim)  # B, C
        if self.global_feat:
            return x, trans, trans_feat
        else:
            x = x.view(-1, self.out_dim, 1).repeat(1, 1, n_pts)  # B, C, N
            return torch.cat([x, pointfeat], 1), trans, trans_feat


# class PointNetCls(nn.Module):
#     def __init__(self, k=2, feature_transform=False):
#         super(PointNetCls, self).__init__()
#         self.feature_transform = feature_transform
#         self.feat = PointNetfeat(global_feat=True, feature_transform=feature_transform)
#         self.fc1 = nn.Linear(1024, 512)
#         self.fc2 = nn.Linear(512, 256)
#         self.fc3 = nn.Linear(256, k)
#         self.dropout = nn.Dropout(p=0.3)
#         self.bn1 = nn.BatchNorm1d(512)
#         self.bn2 = nn.BatchNorm1d(256)
#         self.relu = nn.ReLU()

#     def forward(self, x):
#         x, trans, trans_feat = self.feat(x)
#         x = F.relu(self.bn1(self.fc1(x)))
#         x = F.relu(self.bn2(self.dropout(self.fc2(x))))
#         x = self.fc3(x)
#         return F.log_softmax(x, dim=1), trans, trans_feat


class PointNetSeg(nn.Module):
    def __init__(
        self,
        encoder_out_channels,
        decoder_out_channels,
        k=2,
        input_transform=False,
        feature_transform=False,
        in_channel=3,
    ):
        super(PointNetSeg, self).__init__()
        self.k = k
        self.feature_transform = feature_transform
        self.encoder = PointNetFeatEncoder(
            encoder_out_channels,
            global_feat=False,
            input_transform=input_transform,
            feature_transform=feature_transform,
            in_channel=in_channel,
        )

        decoder_input_dim = encoder_out_channels[1] + encoder_out_channels[-1]
        decoder_out_channels = [decoder_input_dim] + decoder_out_channels + [k]

        decoder_layers = []
        for i in range(len(decoder_out_channels) - 1):
            decoder_layers.append(torch.nn.Conv1d(decoder_out_channels[i], decoder_out_channels[i + 1], 1))
            if i < len(decoder_out_channels) - 2:
                decoder_layers.append(nn.BatchNorm1d(decoder_out_channels[i + 1]))
                decoder_layers.append(nn.ReLU())

        self.decoder = nn.Sequential(*decoder_layers)

    def forward(self, x):
        x, trans, trans_feat = self.encoder(x)
        x = self.decoder(x)  # B, C, N
        return x, trans, trans_feat


class PointNetReg(nn.Module):
    def __init__(
        self,
        encoder_out_channels,
        decoder_out_channels,
        k=7,
        input_transform=False,
        feature_transform=False,
        in_channel=3,
    ):
        super(PointNetReg, self).__init__()
        self.k = k
        self.feature_transform = feature_transform
        self.encoder = PointNetFeatEncoder(
            encoder_out_channels,
            global_feat=True,
            input_transform=input_transform,
            feature_transform=feature_transform,
            in_channel=in_channel,
        )

        decoder_input_dim = encoder_out_channels[-1]
        if k > 0:
            decoder_out_channels = [decoder_input_dim] + decoder_out_channels + [k]
        else:
            decoder_out_channels = [decoder_input_dim] + decoder_out_channels

        decoder_layers = []
        for i in range(len(decoder_out_channels) - 1):
            decoder_layers.append(torch.nn.Conv1d(decoder_out_channels[i], decoder_out_channels[i + 1], 1))
            if k > 0:  # 最后一层是回归，不加bn/relu
                if i < len(decoder_out_channels) - 2:
                    decoder_layers.append(nn.BatchNorm1d(decoder_out_channels[i + 1]))
                    decoder_layers.append(nn.ReLU())
            else:  # 最后一层仍是feat
                decoder_layers.append(nn.BatchNorm1d(decoder_out_channels[i + 1]))
                decoder_layers.append(nn.ReLU())

        self.decoder = nn.Sequential(*decoder_layers)

    def forward(self, x):
        batchsize = x.size()[0]
        x, trans, trans_feat = self.encoder(x)
        x = x.view(batchsize, -1, 1)
        x = self.decoder(x)  # B, C, N

        return x.view(batchsize, -1), trans, trans_feat


# Simple Regression Layers
class PointNetRegLayer(nn.Module):
    def __init__(self, input_channel, out_channels, k=7):
        super(PointNetRegLayer, self).__init__()
        self.k = k
        out_channels = [input_channel] + out_channels + [k]

        decoder_layers = []
        for i in range(len(out_channels) - 1):
            decoder_layers.append(torch.nn.Conv1d(out_channels[i], out_channels[i + 1], 1))
            if i < len(out_channels) - 2:
                decoder_layers.append(nn.BatchNorm1d(out_channels[i + 1]))
                decoder_layers.append(nn.ReLU())

        self.decoder = nn.Sequential(*decoder_layers)

    def forward(self, x):
        batchsize = x.size()[0]

        x = x.view(batchsize, -1, 1)
        x = self.decoder(x)  # B, C, N

        return x.view(batchsize, -1)


def feature_transform_regularizer(trans):
    d = trans.size()[1]
    I = torch.eye(d)[None, :, :]
    if trans.is_cuda:
        I = I.cuda()
    loss = torch.mean(torch.norm(torch.bmm(trans, trans.transpose(2, 1)) - I, dim=(1, 2)))
    return loss


if __name__ == "__main__":
    pass
    # sim_data = Variable(torch.rand(32,3,2500))
    # trans = STN3d()
    # out = trans(sim_data)
    # print('stn', out.size())
    # print('loss', feature_transform_regularizer(out))

    # sim_data_64d = Variable(torch.rand(32, 64, 2500))
    # trans = STNkd(k=64)
    # out = trans(sim_data_64d)
    # print('stn64d', out.size())
    # print('loss', feature_transform_regularizer(out))

    # pointfeat = PointNetfeat(global_feat=True)
    # out, _, _ = pointfeat(sim_data)
    # print('global feat', out.size())

    # pointfeat = PointNetfeat(global_feat=False)
    # out, _, _ = pointfeat(sim_data)
    # print('point feat', out.size())

    # cls = PointNetCls(k = 5)
    # out, _, _ = cls(sim_data)
    # print('class', out.size())

    # seg = PointNetDenseCls(k = 3)
    # out, _, _ = seg(sim_data)
    # print('seg', out.size())
