import numpy as np
from nuscenes.utils.data_classes import Box
import copy
from pyquaternion import Quaternion
from scipy.spatial.transform import Rotation as R


def bbox_lidar_to_world(box_info, lidar_to_gnss, gnss_pose):
    """
    将bbox从主雷达坐标系转世界坐标系。lidar_to_gnss: {"rotation", "translation"}, gnss_pos: {"position", "orientation"}
    """
    box_dict = {}
    for key in ["coord_lidar", "coord_gnss", "coord_global"]:
        box_dict[key] = {}

    rot = Quaternion([box_info["angle_lidar"][k] for k in ["w", "x", "y", "z"]])
    xyz = [box_info["xyz_lidar"][k] for k in ["x", "y", "z"]]
    lwh = [box_info["lwh"][k] for k in ["l", "w", "h"]]
    xyz_lwh_rot = np.array(xyz + lwh + [rot.yaw_pitch_roll[0]])  # rotz
    sensor_pose_box = Box(center=xyz, size=np.array(lwh)[[1, 0, 2]], orientation=rot)
    box_dict["coord_lidar"]["xyz_lwh_rot"] = xyz_lwh_rot
    box_dict["coord_lidar"]["bbox3d"] = sensor_pose_box.corners().T  # 8*3
    box_dict["coord_lidar"]["pose_box"] = sensor_pose_box

    # gnss
    gnss_pose_box = copy.deepcopy(box_dict["coord_lidar"]["pose_box"])
    gnss_pose_box.rotate(Quaternion([lidar_to_gnss["rotation"][k] for k in ["w", "x", "y", "z"]]))
    gnss_pose_box.translate(+np.array([lidar_to_gnss["translation"][k] for k in ["x", "y", "z"]]))

    box_dict["coord_gnss"]["xyz_lwh_rot"] = np.array(
        list(gnss_pose_box.center) + list(gnss_pose_box.wlh[[1, 0, 2]]) + [gnss_pose_box.orientation.yaw_pitch_roll[0]]
    )
    box_dict["coord_gnss"]["bbox3d"] = gnss_pose_box.corners().T  # 8*3
    box_dict["coord_gnss"]["pose_box"] = gnss_pose_box

    # global
    global_pose_box = copy.deepcopy(box_dict["coord_gnss"]["pose_box"])
    global_pose_box.rotate(Quaternion([gnss_pose["orientation"][k] for k in ["w", "x", "y", "z"]]))
    global_pose_box.translate(+np.array([gnss_pose["position"][k] for k in ["x", "y", "z"]]))
    box_dict["coord_global"]["xyz_lwh_rot"] = np.array(
        list(global_pose_box.center)
        + list(global_pose_box.wlh[[1, 0, 2]])
        + [global_pose_box.orientation.yaw_pitch_roll[0]]
    )
    box_dict["coord_global"]["bbox3d"] = global_pose_box.corners().T  # 8*3
    box_dict["coord_global"]["pose_box"] = global_pose_box

    return box_dict["coord_global"]["xyz_lwh_rot"]


def bbox_world_to_lidar(box_world_info, lidar_to_gnss, gnss_pose):
    """
    Lidar to World: lidar -> gnss -> world
    World to Lidar: world -> gnss -> lidar
    lidar_to_gnss: {"rotation", "translation"}, gnss_pos: {"position", "orientation"}

    Parameters:
    ret_box_format: if "dict", return "pose_box"; elif "array", return "xyz_lwh_rot"
    """
    box_dict = {}
    for key in ["coord_lidar", "coord_gnss", "coord_global"]:
        box_dict[key] = {}

    rot = Quaternion([box_world_info["angle_world"][k] for k in ["w", "x", "y", "z"]])
    xyz = [box_world_info["xyz_world"][k] for k in ["x", "y", "z"]]
    lwh = [box_world_info["lwh"][k] for k in ["l", "w", "h"]]
    xyz_lwh_rot = np.array(xyz + lwh + [rot.yaw_pitch_roll[0]])  # rotz
    sensor_pose_box = Box(center=xyz, size=np.array(lwh)[[1, 0, 2]], orientation=rot)
    box_dict["coord_global"]["xyz_lwh_rot"] = xyz_lwh_rot
    box_dict["coord_global"]["bbox3d"] = sensor_pose_box.corners().T  # 8*3
    box_dict["coord_global"]["pose_box"] = sensor_pose_box

    # gnss
    gnss_pose_box = copy.deepcopy(box_dict["coord_global"]["pose_box"])
    gnss_pose_box.translate(-np.array([gnss_pose["position"][k] for k in ["x", "y", "z"]]))
    gnss_pose_box.rotate(Quaternion([gnss_pose["orientation"][k] for k in ["w", "x", "y", "z"]]).inverse)

    box_dict["coord_gnss"]["xyz_lwh_rot"] = np.array(
        list(gnss_pose_box.center) + list(gnss_pose_box.wlh[[1, 0, 2]]) + [gnss_pose_box.orientation.yaw_pitch_roll[0]]
    )
    box_dict["coord_gnss"]["bbox3d"] = gnss_pose_box.corners().T  # 8*3
    box_dict["coord_gnss"]["pose_box"] = gnss_pose_box

    # lidar
    lidar_pose_box = copy.deepcopy(box_dict["coord_gnss"]["pose_box"])
    lidar_pose_box.translate(-np.array([lidar_to_gnss["translation"][k] for k in ["x", "y", "z"]]))
    lidar_pose_box.rotate(Quaternion([lidar_to_gnss["rotation"][k] for k in ["w", "x", "y", "z"]]).inverse)
    box_dict["coord_lidar"]["xyz_lwh_rot"] = np.array(
        list(lidar_pose_box.center)
        + list(lidar_pose_box.wlh[[1, 0, 2]])
        + [lidar_pose_box.orientation.yaw_pitch_roll[0]]
    )
    box_dict["coord_lidar"]["bbox3d"] = lidar_pose_box.corners().T  # 8*3
    box_dict["coord_lidar"]["pose_box"] = lidar_pose_box

    return box_dict["coord_lidar"]["xyz_lwh_rot"]


def points_lidar_to_world(points_lidar, lidar_to_gnss, gnss_pose):
    lidar_to_gnss_t = np.array([lidar_to_gnss["translation"][k] for k in ["x", "y", "z"]])  # translation
    lidar_to_gnss_r = R.from_quat(
        np.array([lidar_to_gnss["rotation"][k] for k in ["x", "y", "z", "w"]])
    ).as_matrix()  # rotation, xyzw format.
    lidar_to_gnss = np.zeros((4, 4))
    lidar_to_gnss[3, 3] = 1
    lidar_to_gnss[:3, :3] = lidar_to_gnss_r
    lidar_to_gnss[:3, 3] = lidar_to_gnss_t

    # gnss
    points_lidar, points_feats = get_points_in_homogeneous(points_lidar)  # Nx4, NxC
    points_gnss = np.matmul(lidar_to_gnss, points_lidar.T).T  # Nx4
    points_gnss = np.concatenate((points_gnss[:, :3], points_feats), 1)

    gnss_pose_t = np.array([gnss_pose["position"][k] for k in ["x", "y", "z"]])  # translation
    gnss_pose_r = R.from_quat(
        np.array([gnss_pose["orientation"][k] for k in ["x", "y", "z", "w"]])
    ).as_matrix()  # rotation
    gnss_pose = np.zeros((4, 4))
    gnss_pose[3, 3] = 1
    gnss_pose[:3, :3] = gnss_pose_r
    gnss_pose[:3, 3] = gnss_pose_t

    # global
    points_gnss, points_feats = get_points_in_homogeneous(points_gnss)
    points_global = np.matmul(gnss_pose, points_gnss.T).T
    points_global = np.concatenate((points_global[:, :3], points_feats), 1)

    return points_global


# 齐次坐标表示
def get_points_in_homogeneous(points_data):
    points = points_data[:, :3]
    fetures = points_data[:, 3:]
    tmp_ones = np.ones((points.shape[0], 1))
    homo_points = np.concatenate((points, tmp_ones), axis=1)
    return homo_points, fetures
