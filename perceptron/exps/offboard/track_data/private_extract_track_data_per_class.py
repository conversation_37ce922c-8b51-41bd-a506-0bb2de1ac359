# 每个类别单独保存
import pickle
from perceptron_ops.roiaware_pool3d.roiaware_pool3d_utils import points_in_boxes_cpu
import os
import numpy as np
import nori2 as nori
import refile
import torch
from tqdm import tqdm
import rrun
from pathlib import Path
from perceptron_ops.iou3d_nms.iou3d_nms_utils import boxes_iou3d_gpu
import argparse
import copy
import json
import io
from numpy.lib import recfunctions as rfn
from scipy.spatial.transform import Rotation as R
import time
from perceptron.exps.offboard.track_data.utils import bbox_lidar_to_world, points_lidar_to_world

# TrackData Format
# 每个场景一个对应一个独立的pkl文件，每个pkl文件存放了两个numpy数组，分别是track_points数组以及track_dets数组，以及untracked数组。
# （1） track_points数组。维度为(N_t, N_f, N_p, C)，分别表示该场景下总track数，视频总帧数（20hz下)，每个det box中最大point数，point feature维度(x,y,z,反射强度r,伸长系数e)。基于世界坐标系。
# （2） track_detbox数组。维度为(N_t, N_f, 10), 分别表示该场景下总track数，视频总帧数（20hz下），物体的bbox（x,y,z,l,w,h,theta）+ pred_label + pred_trackID + det_score。基于世界坐标系。
# （3） track_gt_box数组。维度为(N_t, N_f, 10)。物体的bbox（x,y,z,l,w,h,theta）+ pred_label + pred_trackID + velocity
# （4） valid_track数组。维度为(N_t,), 0表示invalid。1表示valid。
# （5） gt_motion_state。维度为(N_t,)，0表示静态，1表示动态


# 判断id switch逻辑：首先将det box assign到gt box。如果gt track id一致，则未发生switch。否则发生switch。


CATEGORY = "vehicle"
MOTION_DIS_THRES = 1.0
MOTION_VEL_THRES = 1.0
ENLARGE_METER = 1.0  # 检测框沿每个维度扩大多少米。注意：仅抽取前景点时扩框，保存的框应该还是原始框，否则assign gt会有问题（iou会变小）。


if CATEGORY == "vehicle":
    DEALING_CLASS = ["car", "truck", "trailer", "bus", "construction_vehicle"]
else:
    assert False


NR_POINTS_FEATURE = 5  # (x, y, z, i, r)
PC_FIELDS = ["x", "y", "z", "i", "r"]

CATEGORY_MAPPING = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
}

CATEGORY_MAPPING_INVERSE = {}
for k, v in CATEGORY_MAPPING.items():
    CATEGORY_MAPPING_INVERSE[v] = k


AUTO_CATEGORY_2_LABEL_MAPPING = {
    "car": 1,
    "truck": 2,
    "construction_vehicle": 3,
    "bus": 4,
    "motorcycle": 5,
    "bicycle": 6,
    "tricycle": 7,
    "cyclist": 8,
    "pedestrian": 9,
    "other": 10,
    "ghost": 11,
    "masked_area": 12,
}

AUTO_LABEL_2_CATEGORY_MAPPING = {}
for k, v in AUTO_CATEGORY_2_LABEL_MAPPING.items():
    AUTO_LABEL_2_CATEGORY_MAPPING[v] = k


MATCHING_IOU = {
    "car": 0.55,
    "truck": 0.55,
    "construction_vehicle": 0.55,
    "bus": 0.55,
    "motorcycle": 0.3,
    "bicycle": 0.3,
    "tricycle": 0.3,  # 三轮车放到机还是非
    "cyclist": 0.3,
    "pedestrian": 0.3,
}

nori_fetcher = nori.Fetcher()


def load_angle_anno(anno):
    quat = np.zeros((4,), dtype=np.float32)
    quat[0] = anno["angle_lidar"]["x"]
    quat[1] = anno["angle_lidar"]["y"]
    quat[2] = anno["angle_lidar"]["z"]
    quat[3] = anno["angle_lidar"]["w"]
    return R.from_quat(quat).as_euler("xyz")[-1]  # 四元数转角度. 需要xyzw格式


def load_box_from_anno(anno):
    box_anno = np.zeros((7,), dtype=np.float32)  # x,y,z,l,w,h,angle
    box_anno[0] = anno["xyz_lidar"]["x"]
    box_anno[1] = anno["xyz_lidar"]["y"]
    box_anno[2] = anno["xyz_lidar"]["z"]
    box_anno[3] = anno["lwh"]["l"]
    box_anno[4] = anno["lwh"]["w"]
    box_anno[5] = anno["lwh"]["h"]
    box_anno[6] = load_angle_anno(anno)

    return box_anno


def filter_by_gt_assign_mild(scene_track_detboxes_world, scene_track_assigned_gt_world):
    """
    过滤短track/低质量track，输入均为dict。key表示track_id。注意track_id不一定连续（有的可能被删除了）
    """
    assert len(scene_track_detboxes_world) == len(scene_track_assigned_gt_world)

    track_id_list = sorted(scene_track_detboxes_world.keys())
    max_track_id = track_id_list[-1]
    track_valid = np.ones((max_track_id + 1))

    # nr_tracks = len(scene_track_detboxes_world)
    nr_invalid = 0

    for i in range(max_track_id + 1):
        if i not in track_id_list:
            track_valid[i] = 0  # track不存在
            continue

        track_id = i

        track_det_data = scene_track_detboxes_world[track_id]
        track_gt_data = scene_track_assigned_gt_world[track_id]
        nr_frames_assigned_gt = 0

        if len(track_det_data) == 0:
            track_valid[track_id] = 0
            continue

        for j in range(len(track_det_data)):  # 遍历每一帧
            if not np.all(track_det_data[j][:7] == 0) and not np.all(track_gt_data[j][:7] == 0):
                nr_frames_assigned_gt += 1

        if nr_frames_assigned_gt < 7:
            nr_invalid += 1
            track_valid[track_id] = 0

    return track_valid, nr_invalid


# 松散 id switch: 只关注是否switch，不关注是否assign上。
def filter_id_switch(scene_track_detboxes_world, scene_track_assigned_gt_world):
    """
    过滤id switch，输入均为dict。key表示track_id。注意track_id不一定连续（有的可能被删除了）
    """
    assert len(scene_track_detboxes_world) == len(scene_track_assigned_gt_world)

    track_id_list = sorted(scene_track_detboxes_world.keys())
    max_track_id = track_id_list[-1]
    track_valid = np.ones((max_track_id + 1))

    # nr_tracks = len(scene_track_detboxes_world)
    nr_invalid = 0

    # for track_id in track_id_list:  # 遍历track
    for i in range(max_track_id + 1):
        if i not in track_id_list:
            track_valid[i] = 0  # track不存在
            continue

        track_id = i
        gt_track_id = -1  # 首先初始化为负值
        track_det_data = scene_track_detboxes_world[track_id]
        track_gt_data = scene_track_assigned_gt_world[track_id]

        if len(track_det_data) == 0:
            track_valid[track_id] = 0
            continue

        for j in range(len(track_det_data)):  # 遍历frame
            if not np.all(track_det_data[j][:7] == 0):  # 如果有检测框：记录/对比gt track_id
                if gt_track_id < 0:
                    gt_track_id = track_gt_data[j][8]
                elif (
                    track_gt_data[j][8] >= 0 and track_gt_data[j][8] != gt_track_id
                ):  # 如果有检测框，但对应gt track_id 变化，说明发生了switch，需要过滤掉
                    track_valid[track_id] = 0
                    nr_invalid += 1
                    break  # 检查下一个track

    return track_valid, nr_invalid


def is_same_high_cat(cat1, cat2):
    GROUP1 = ["car", "truck", "construction_vehicle", "bus"]
    GROUP2 = ["pedestrian"]
    GROUP3 = ["motorcycle", "bicycle", "tricycle", "cyclist"]

    assert cat1 in GROUP1 or cat1 in GROUP2 or cat1 in GROUP3, "invalid categoty!!!"
    assert cat2 in GROUP1 or cat2 in GROUP2 or cat2 in GROUP3, "invalid categoty!!!"

    if cat1 in GROUP1 and cat2 in GROUP1:
        return True

    if cat1 in GROUP2 and cat2 in GROUP2:
        return True

    if cat1 in GROUP3 and cat2 in GROUP3:
        return True

    return False


def filter_high_level_category_change(scene_track_detboxes_world):

    track_id_list = sorted(scene_track_detboxes_world.keys())
    max_track_id = track_id_list[-1]
    track_valid = np.ones((max_track_id + 1))

    # nr_tracks = len(scene_track_detboxes_world)
    nr_invalid = 0

    for i in range(max_track_id + 1):
        if i not in track_id_list:
            track_valid[i] = 0  # track不存在
            continue

        track_id = i

        track_category_first = None  # 首个有效帧类别
        track_det_data = scene_track_detboxes_world[track_id]

        if len(track_det_data) == 0:
            track_valid[track_id] = 0
            continue

        for j in range(len(track_det_data)):  # 遍历所有frame
            if track_category_first is None:  # 物体类别未知
                if track_det_data[j][7] > 0:
                    track_category_first = AUTO_LABEL_2_CATEGORY_MAPPING[int(track_det_data[j][7])]
            else:  # 物体类别已知
                if track_det_data[j][7] > 0:
                    if not is_same_high_cat(
                        track_category_first, AUTO_LABEL_2_CATEGORY_MAPPING[int(track_det_data[j][7])]
                    ):
                        nr_invalid += 1
                        track_valid[i] = 0
                        break
    return track_valid, nr_invalid


def check_track(scene_track_detboxes_world, scene_track_assigned_gt_world):
    """
    检查track有效性: gt匹配检查 + id_switch检查 + 跨大类别跳变检查
    """
    # 计算匹配不上gt的track数
    valid_track_by_gt_assign, nr_invalid_by_gt_assign = filter_by_gt_assign_mild(
        scene_track_detboxes_world, scene_track_assigned_gt_world
    )

    # 计算id_switch
    valid_track_by_switch, nr_invalid_by_switch = filter_id_switch(
        scene_track_detboxes_world, scene_track_assigned_gt_world
    )

    # 过滤大类跳变
    valid_track_by_cc, nr_invalid_by_cc = filter_high_level_category_change(scene_track_detboxes_world)

    valid_track_mask = valid_track_by_gt_assign * valid_track_by_switch * valid_track_by_cc
    nr_valid_track = np.sum(valid_track_mask)

    print(
        f"{len(scene_track_detboxes_world)} non-empty tracks, filter {nr_invalid_by_gt_assign} short tracks , filter {nr_invalid_by_switch} switched tracks, filter {nr_invalid_by_cc} high-level category switch, keep {nr_valid_track} valid tracks"
    )
    return (
        valid_track_mask,
        nr_valid_track,
    )


# 保存前压缩一下存储结果。将存在大量0占位的数组转换成list形式。最终套一个NxC点云矩阵（无0占位的）
# 注：按此方式去除后，points和assign gt中仍然可能有空白帧。
def compress_front_end_empty_frames(track_points_world, track_detboxes_world, track_assigned_gt_world):

    for i in track_detboxes_world.keys():  # 遍历所有track
        track_points_data = track_points_world[i]
        track_det_data = track_detboxes_world[i]
        track_gt_data = track_assigned_gt_world[i]

        # 找到首尾第一个非空帧。(以detboxes为准)
        start, end = 0, len(track_det_data) - 1  # 包含首尾

        # 根据有无点过滤后，有可能整个track一个det框都没有
        while start < len(track_det_data) and np.all(track_det_data[start][:7] == 0):
            start += 1

        if start >= len(track_det_data):
            track_points_world[i] = []
            track_detboxes_world[i] = []
            track_assigned_gt_world[i] = []

            continue

        while np.all(track_det_data[end][:7] == 0):
            end -= 1  # 含

        track_points_compressed = track_points_data[start : end + 1]  # N_f, N_p, C
        track_det_compressed = track_det_data[start : end + 1]  # N_f, C
        track_gt_compressed = track_gt_data[start : end + 1]  # N_f, C

        track_points_world[i] = track_points_compressed
        track_detboxes_world[i] = track_det_compressed
        track_assigned_gt_world[i] = track_gt_compressed

    return track_points_world, track_detboxes_world, track_assigned_gt_world


def compress_assigned_gt(track_assigned_gt):
    track_assigned_gt_clone = copy.deepcopy(track_assigned_gt)
    for i in track_assigned_gt_clone.keys():  # 遍历所有track
        track_gt_data = track_assigned_gt_clone[i]
        # 找到首尾第一个非空帧。
        start, end = 0, len(track_gt_data) - 1  # 包含首尾

        while np.all(track_gt_data[start][:7] == 0):
            start += 1

        while np.all(track_gt_data[end][:7] == 0):
            end -= 1  # 含

        track_gt_compressed = track_gt_data[start : end + 1]  # N_f, C

        track_assigned_gt_clone[i] = track_gt_compressed

    return track_assigned_gt_clone


# 为有效track分配motion state。
def assign_gt_motion_state(scene_track_assigned_gt_world):
    motion_state = {}  # track_id: 0表示static
    for i in scene_track_assigned_gt_world.keys():
        motion_state[i] = 0

    # 去除首位空帧
    scene_track_assigned_gt_world_compressed = compress_assigned_gt(scene_track_assigned_gt_world)

    for i in scene_track_assigned_gt_world_compressed.keys():  # 遍历所有track
        # max distance (直接计算首尾中心点距离即可。)
        track_gt_data = scene_track_assigned_gt_world_compressed[i]
        ff_center = track_gt_data[0][:3]  # first frame center (3,)
        lf_center = track_gt_data[-1][:3]  # last frame center (3,)

        if np.sqrt(np.sum((ff_center - lf_center) ** 2)) > MOTION_DIS_THRES:
            motion_state[i] = 1  # dynamic
            continue

        # for i in range(len(track_gt_data)):  # 遍历检查每个frame是否超出速度阈值
        #     if track_gt_data[i][9] > MOTION_VEL_THRES:
        #         motion_state[i] = 1  # dynamic
        #         break

        for j in range(len(track_gt_data)):  # 遍历检查每个frame是否超出速度阈值
            if track_gt_data[j][9] > MOTION_VEL_THRES:
                motion_state[i] = 1  # dynamic
                print(f"Set *dynamic* of track-{i} with vel {track_gt_data[j][9]} larger than thres !!!")
                break

    return motion_state


def init_a_track(track_id, nr_frames, scene_track_points, scene_track_detboxes, scene_track_assigned_gt):
    if track_id not in scene_track_points.keys():
        scene_track_points[track_id] = [np.zeros((0, NR_POINTS_FEATURE)) for i in range(nr_frames)]
        scene_track_detboxes[track_id] = [np.zeros((10)) for i in range(nr_frames)]  # xyzlwhr + cat + track_id + score

        for i in range(nr_frames):  # np.sum(box[:7]) == 0 表示此处无框
            scene_track_detboxes[track_id][i][7] = -1  # cat置为-1， 表示此处无框
            scene_track_detboxes[track_id][i][8] = -1  # track id 置为-1，表示此处无框

        scene_track_assigned_gt[track_id] = [np.zeros((10)) for i in range(nr_frames)]
        for i in range(nr_frames):
            scene_track_assigned_gt[track_id][i][7] = -1
            scene_track_assigned_gt[track_id][i][8] = -1
            # scene_track_assigned_gt[i][9] 表示速度，默认为0，无问题。

    else:
        return


# 只保留有效的track
def filter_invalid_tracks(track_points, track_detboxes, track_assigned_gt, valid_mask):
    valid_track_points, valid_track_detboxes, valid_track_assigned_gt = {}, {}, {}
    valid_index = np.where(valid_mask == 1)[0]
    for idx in valid_index:
        valid_track_points[idx] = track_points[idx]
        valid_track_detboxes[idx] = track_detboxes[idx]
        valid_track_assigned_gt[idx] = track_assigned_gt[idx]

    return valid_track_points, valid_track_detboxes, valid_track_assigned_gt


# 抽取的时候，直接过滤掉det框/gt框内没有点的帧（这些帧不存在前景点，对后续训练有害）
def extract_track_data(scene_path, args):
    nr_valid_tracks = 0  # 有效track总数

    scene_basename = os.path.splitext(os.path.basename(scene_path))[0]
    scene_dirname = os.path.dirname(scene_path).split("/")[-1]
    cur_save_path = refile.smart_path_join(args.save_dir, scene_dirname, scene_basename + ".pkl")

    print(cur_save_path, scene_path)

    # TBD: 开启断点续做。
    # if refile.s3_exists(cur_save_path):
    #     tmp_data = pickle.load(refile.s3_load_from(cur_save_path))
    #     tmp_len = len(tmp_data["gt_motion_state"])
    #     print(f"###{cur_save_path} already exists: {tmp_len} valid tracks. Skip ...")
    #     nr_valid_tracks += tmp_len
    #     continue

    with refile.smart_open(scene_path, "r") as rf:
        scene_data = json.load(rf)  # 一个json对应一个视频

    scene_track_points_world = {}  # track_id -> list， 第i个元素是一个NxC矩阵，表示第i帧检测框内的点云
    scene_track_detboxes_world = {}  # track_id -> list, 第i个元素是一个9维矩阵，表示第i帧的检测框
    scene_track_assigned_gt_world = {}  # track_id -> list， 第i个元素是一个10维矩阵，表示第i帧的gt（额外多了一个速度）。

    scene_track_data = {}
    nr_frames = len(scene_data["frames"])

    for frame_id in tqdm(range(nr_frames)):  # 遍历frames
        frame_info = scene_data["frames"][frame_id]

        # 最后几帧可能都没有gt。需要直接跳过。
        if "labels" not in frame_info:
            continue

        lidar_to_gnss = scene_data["calibrated_sensors"]["middle_lidar"]["lidar_gnss"]["transform"]

        if not frame_info.get("odom_data", None) and not frame_info.get(
            "ins_data"
        ):  # odom_data 和 ins_data都不存在 ，将无法转换至world坐标系，跳过。
            continue

        if frame_info.get("odom_data", None):
            gnss_pose = frame_info["odom_data"]["pose"]["pose"]  # {"position", "orientation"}
        else:
            gnss_pose = dict(
                position=frame_info["ins_data"]["localization"]["position"],
                orientation=frame_info["ins_data"]["localization"]["orientation"],
            )

        if args.lidar_type == "middle":
            nori_id = frame_info["sensor_data"]["middle_lidar"]["nori_id"]
        elif args.lidar_type == "fuser":
            nori_id = frame_info["sensor_data"]["fuser_lidar"]["nori_id"]
        else:
            assert False, f"Unsupported lidar type of {args.lidar_type}"

        if nori_id is None:
            continue
        lidar_data = np.load(io.BytesIO(nori_fetcher.get(nori_id))).copy()
        lidar_data = rfn.structured_to_unstructured(lidar_data[np.array(PC_FIELDS)])  # Nx5

        # TBD: 获取世界坐标系下gt box 以及 velocity
        # TBD: 预先给非关键帧插值得到gt。
        nr_gt_boxes = len(frame_info["labels"])
        gt_boxes_lidar = []  # x,y,z,l,w,h,theta
        gt_boxes_world = []
        gt_boxes_vel = []
        gt_names = []
        gt_track_ids = np.zeros((nr_gt_boxes,), dtype=np.float32)

        for gt_idx in range(nr_gt_boxes):
            anno = frame_info["labels"][gt_idx]

            # 过滤掉一些不考虑的类别。
            cur_category = CATEGORY_MAPPING[anno["category"]]
            if cur_category in ["other", "ghost", "masked_area"]:
                continue

            gt_names.append(cur_category)
            gt_boxes_lidar.append(load_box_from_anno(anno))
            gt_boxes_world.append(bbox_lidar_to_world(anno, lidar_to_gnss, gnss_pose))

            if "velocity" in anno:
                gt_boxes_vel.append(anno["velocity"][-1])  # vx, vy, vz, v
            else:
                gt_boxes_vel.append(np.nan)

            # gt_names.append(CATEGORY_MAPPING[anno["category"]])
            gt_track_ids[gt_idx] = anno["track_id"]

        gt_boxes_lidar = np.asfarray(gt_boxes_lidar)
        gt_boxes_world = np.asfarray(gt_boxes_world)
        gt_names = np.asarray(gt_names)
        gt_boxes_vel = np.asfarray(gt_boxes_vel)

        # 如果这一帧没有gt，也不能直接跳过！！只要不进行assign就可以了。 因为没有gt的情况下，仍然可能会出检测框，连track，如果直接跳过，会导致缺track的情况，在生成valid_mask时就会有问题。)

        for box in frame_info["pre_labels"]:  # 遍历所有boxes
            box_lidar = load_box_from_anno(box)
            box_world = bbox_lidar_to_world(box, lidar_to_gnss, gnss_pose)

            pred_score = box["det_score"]
            pred_category = CATEGORY_MAPPING[box["category"]]
            pred_label = AUTO_CATEGORY_2_LABEL_MAPPING[pred_category]
            pred_track_id = box["track_id"]

            # 初始化。如果已经初始化，什么也不做。
            init_a_track(
                pred_track_id,
                nr_frames,
                scene_track_points_world,
                scene_track_detboxes_world,
                scene_track_assigned_gt_world,
            )

            # 1. 获取框内的点。
            # lidar坐标系下框内的点。
            box_sensor_enlarged = box_lidar.copy()
            box_sensor_enlarged[3:6] = box_sensor_enlarged[3:6] + 2 * ENLARGE_METER
            in_box_idxs = points_in_boxes_cpu(lidar_data[:, :3], box_sensor_enlarged.reshape(1, -1))[
                0
            ]  # MxN -> N #gpu版本有点问题。
            in_box_idxs_squeeze = np.nonzero(in_box_idxs)[0]
            points_in_box = lidar_data[in_box_idxs_squeeze, :]
            nr_points_in_box = points_in_box.shape[0]
            if nr_points_in_box == 0:
                print("### Skip a det box with no points inside !!!")
                continue  # 跳过该检测框

            # world坐标系下框内的点。
            points_in_box_world = points_lidar_to_world(points_in_box, lidar_to_gnss, gnss_pose)

            scene_track_points_world[pred_track_id][frame_id] = points_in_box_world  # NxC
            pred_box_info = np.zeros((10))
            pred_box_info[:7] = box_world
            pred_box_info[7] = pred_label
            pred_box_info[8] = pred_track_id
            pred_box_info[9] = pred_score

            scene_track_detboxes_world[pred_track_id][frame_id] = pred_box_info

            # gt assign
            if pred_category in DEALING_CLASS and gt_names.shape[0] > 0:  # 存在gt的情况下，才需要assign
                box_world = box_world.reshape(1, -1)
                # 该函数只能输入torch Tensor, 不支持np array
                ious = boxes_iou3d_gpu(
                    torch.from_numpy(box_world).float().cuda(), torch.from_numpy(gt_boxes_world).float().cuda()
                )  # 将det box与所有gt进行匹配。 1 x N
                ious = ious.cpu().numpy()
                iou_max = np.max(ious, axis=1)[0]
                iou_max_idx = np.argmax(ious, axis=1)[0]
                gt_category = gt_names[iou_max_idx]  # str
                gt_label = AUTO_CATEGORY_2_LABEL_MAPPING[gt_category]  # number
                gt_track_id = gt_track_ids[iou_max_idx]

                if iou_max > MATCHING_IOU[gt_category] and gt_category in DEALING_CLASS:
                    # 这里使用了一个更加严苛的判断条件：即不是“判断整个点云中是否有点落在gt中”，而是“判断是否有det box中的点落在gt中”，从而保证gt_seg_mask不为全0.
                    DetPoints_in_GtBox_idxs = points_in_boxes_cpu(
                        points_in_box_world[:, :3], gt_boxes_world[iou_max_idx].reshape(1, -1)
                    )[0]
                    if np.sum(DetPoints_in_GtBox_idxs) == 0:
                        print("### No Foreground Points in Assigned GT Box.")
                        continue  # 没有前景点

                    gt_box_info = np.zeros((10))
                    gt_box_info[:7] = gt_boxes_world[iou_max_idx]
                    gt_box_info[7] = gt_label
                    gt_box_info[8] = gt_track_id
                    gt_box_info[9] = gt_boxes_vel[iou_max_idx]

                    scene_track_assigned_gt_world[pred_track_id][frame_id] = gt_box_info

    # 压缩首尾冗余占位帧（track内占位帧不压缩, 基于detbox进行压缩（points/assigned_gt首尾仍有可能有空帧）
    (
        scene_track_points_world,
        scene_track_detboxes_world,
        scene_track_assigned_gt_world,
    ) = compress_front_end_empty_frames(
        scene_track_points_world, scene_track_detboxes_world, scene_track_assigned_gt_world
    )

    # 评估track的有效性。
    valid_track_mask, nr_valid = check_track(scene_track_detboxes_world, scene_track_assigned_gt_world)

    # 只保留有效的track
    scene_track_points_world, scene_track_detboxes_world, scene_track_assigned_gt_world = filter_invalid_tracks(
        scene_track_points_world, scene_track_detboxes_world, scene_track_assigned_gt_world, valid_track_mask
    )

    # 对有效的track，assgin motion state gt。
    gt_motion_state = assign_gt_motion_state(scene_track_assigned_gt_world)

    scene_track_data["track_points_world"] = scene_track_points_world
    scene_track_data["track_boxes_world"] = scene_track_detboxes_world
    scene_track_data["assigned_gt"] = scene_track_assigned_gt_world
    # scene_track_data["valid_track_mask"] = valid_track_mask
    scene_track_data["nr_raw_tracks"] = valid_track_mask.shape[0]  # track模型直出的track数
    scene_track_data["gt_motion_state"] = gt_motion_state

    with refile.smart_open(cur_save_path, "wb") as wf:
        pickle.dump(scene_track_data, wf)

    nr_valid_tracks += len(scene_track_data["gt_motion_state"])

    return nr_valid_tracks, scene_path


def main(args, scene_paths):
    nr_runners = min(len(scene_paths), 64)
    spec = rrun.RunnerSpec()
    time_str = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
    spec.name = "extract_track_data"
    spec.log_dir = os.path.join(os.getcwd(), "rrun_log", spec.name, time_str)
    Path(spec.log_dir).mkdir(exist_ok=True, parents=True)
    spec.charged_group = "transformer"
    # spec.scheduling_hint.group = "user" # user?
    if "BRAINPP_BLOCKLIST_PATH" in os.environ:
        with open(os.path.expanduser(os.environ["BRAINPP_BLOCKLIST_PATH"])) as rf:
            blocklist = [x.strip() for x in rf.readlines()]
        spec.scheduling_hint.negative_tags[:] = blocklist
    spec.resources.cpu = 4
    spec.resources.gpu = 1
    spec.resources.memory_in_mb = 20 * 1024
    spec.max_wait_time = 600 * int(1e9)
    spec.preemptible = False

    futures = []

    nr_valid_tracks_all = 0
    with rrun.RRunExecutor(runner_spec=spec, num_runners=nr_runners) as executor:
        for scene_path in scene_paths:
            futures.append(executor.submit(extract_track_data, scene_path, args))

        for i, f in enumerate(futures):
            try:
                nr_valid_tracks, scene_path = f.result()
                print(f" {nr_valid_tracks} valid tracks in {scene_path}!!!")
                nr_valid_tracks_all += nr_valid_tracks
            except Exception as e:
                # Catch remote exception
                print(f"Task {i} for {scene_paths[i]} Failed: {e}")

    print(f"ALL FINISHED! {nr_valid_tracks_all} in total in {len(scene_paths)} scenes!!!")


# debug usage
def main2(args, scene_paths):
    nr_valid_tracks_all = 0
    for scene_path in scene_paths:
        nr_valid_tracks, _ = extract_track_data(scene_path, args)
        print(f" {nr_valid_tracks} valid tracks in {scene_path}!!!")
        nr_valid_tracks_all += nr_valid_tracks
    print(f"ALL FINISHED! {nr_valid_tracks_all} in total in {len(scene_paths)} scenes!!!")


def get_all_paths(anno_dir):
    anno_sub_dirs = refile.smart_glob(refile.smart_path_join(anno_dir, "*"))
    scene_paths = []

    for anno_sub_dir in anno_sub_dirs:
        scene_paths.extend(refile.smart_glob(refile.smart_path_join(anno_sub_dir, "*.json")))

    return scene_paths


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--lidar_type", default="fuser", type=str)
    parser.add_argument("--val", action="store_true", default=False)
    args = parser.parse_args()

    # anno_dir = "s3://httdet3d/offboard/20220111/track_prelabels_gt_inter_rrun"  # train.
    anno_dir = "s3://ruanhao/offboard/20220225/track_prelabels_gt_inter_rrun"  # train.
    train_scene_paths = get_all_paths(anno_dir)
    nr_train_scenes = len(train_scene_paths)

    if args.val:
        anno_dir = anno_dir[:-1] if anno_dir.endswith("/") else anno_dir
        anno_dir = anno_dir + "_val"

        val_scene_paths = get_all_paths(anno_dir)
        nr_val_scenes = len(val_scene_paths)

        scene_paths = val_scene_paths
        nr_scenes = nr_val_scenes
    else:
        scene_paths = train_scene_paths
        nr_scenes = nr_train_scenes

    print(f"{nr_scenes} Found!!!")
    print(scene_paths)

    # SAVE_DIR = f"s3://httdet3d/offboard/20220111/track_data/{nr_train_scenes}items/{CATEGORY}_mild_7frames_dis{MOTION_DIS_THRES}_vel{MOTION_VEL_THRES}_enlarge{ENLARGE_METER}"
    # SAVE_DIR = f"s3://ruanhao/offboard/20220111/track_data/{nr_train_scenes}items/{CATEGORY}_mild_7frames_dis{MOTION_DIS_THRES}_vel{MOTION_VEL_THRES}_enlarge{ENLARGE_METER}"
    SAVE_DIR = f"s3://ruanhao/offboard/20220225/track_data/{nr_train_scenes}items/{CATEGORY}_mild_7frames_dis{MOTION_DIS_THRES}_vel{MOTION_VEL_THRES}_enlarge{ENLARGE_METER}"
    if args.val:  # val
        SAVE_DIR = SAVE_DIR[:-1] if SAVE_DIR.endswith("/") else SAVE_DIR
        SAVE_DIR = SAVE_DIR + "_val"

    args.save_dir = SAVE_DIR

    # import IPython; IPython.embed()

    main(args, scene_paths)  # train usage
    # main2(args, scene_paths)  # debug usage
