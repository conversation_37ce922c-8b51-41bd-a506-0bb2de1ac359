# 将所有场景track data对应的pkl，打包成一个完整的pkl文件。方便训练/评测时使用。
import refile
import pickle
import os
import argparse
import torch
from torch_cluster import fps
import numpy as np
import rrun
import time
from pathlib import Path


def fps_for_single_pc(pc, num_points, random_start, return_index=False):
    # 非batch输入。input应为单个点云，shape为 (N, 3)
    is_numpy = isinstance(pc, np.ndarray)
    if is_numpy:
        pc = torch.from_numpy(pc).float().cuda()

    batch = torch.zeros_like(pc[:, 0]).long().cuda()
    pc_xyz, pc_feats = pc[:, :3], pc[:, 3:]
    index = fps(pc_xyz, batch, ratio=num_points / pc.shape[0], random_start=random_start)
    index = index[:num_points]

    pc = torch.cat((pc_xyz[index], pc_feats[index]), axis=1).cpu()  # NxC

    if is_numpy:
        pc = pc.numpy()

    if not return_index:
        return pc
    else:
        return pc, index


def fps_a_scene(scene_path, fps_npoints):

    result_data = {}
    result_data["dynamic"] = []
    result_data["static"] = []

    track_data = pickle.load(refile.s3_load_from(scene_path))
    track_points_world = track_data["track_points_world"]
    track_boxes_world = track_data["track_boxes_world"]
    track_gt_world = track_data["assigned_gt"]
    track_gt_motion_state = track_data["gt_motion_state"]

    for track_id in track_boxes_world.keys():  # 遍历所有track
        track_dict = {}
        if fps_npoints == -1:
            track_dict["track_points_world"] = track_points_world[track_id]
        else:
            track_dict["track_points_world"] = []
            # 逐帧将点云预fps至指定数目
            for frame_idx, frame_pc in enumerate(track_points_world[track_id]):
                if frame_pc.shape[0] == 0:
                    track_dict["track_points_world"].append(frame_pc)
                else:
                    track_dict["track_points_world"].append(
                        fps_for_single_pc(frame_pc, num_points=fps_npoints, random_start=False)
                    )

        track_dict["track_boxes_world"] = track_boxes_world[track_id]
        track_dict["track_gt_world"] = track_gt_world[track_id]
        track_dict["scene_name"] = os.path.splitext(os.path.basename(scene_path))[0]
        track_dict["scene_track_id"] = track_id

        if track_gt_motion_state[track_id] == 0:  # static
            result_data["static"].append(track_dict)
        elif track_gt_motion_state[track_id] == 1:
            result_data["dynamic"].append(track_dict)
        else:
            assert False, f"Unexpected motion state type, should be 0 or 1, but got {track_gt_motion_state[track_id]}"

    return result_data, scene_path


def main(args, track_data_paths, nr_dynamic_tracks_all, nr_static_tracks_all, fps_save_path):
    # rrun并行fps
    nr_runners = min(len(track_data_paths), 64)
    spec = rrun.RunnerSpec()
    time_str = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
    spec.name = "merge_with_fps"
    spec.log_dir = os.path.join(os.getcwd(), "rrun_log", spec.name, time_str)
    Path(spec.log_dir).mkdir(exist_ok=True, parents=True)
    spec.charged_group = "transformer"
    # spec.scheduling_hint.group = "user" # user?
    if "BRAINPP_BLOCKLIST_PATH" in os.environ:
        with open(os.path.expanduser(os.environ["BRAINPP_BLOCKLIST_PATH"])) as rf:
            blocklist = [x.strip() for x in rf.readlines()]
        spec.scheduling_hint.negative_tags[:] = blocklist
    spec.resources.cpu = 4
    spec.resources.gpu = 1
    spec.resources.memory_in_mb = 80 * 1024
    spec.max_wait_time = 600 * int(1e9)
    spec.preemptible = False

    futures = []

    with rrun.RRunExecutor(runner_spec=spec, num_runners=nr_runners) as executor:
        for scene_path in track_data_paths:
            futures.append(executor.submit(fps_a_scene, scene_path, args.fps_npoints))

        for i, f in enumerate(futures):
            try:
                result_data, scene_path = f.result()
                nr_dynamic_tracks = len(result_data["dynamic"])
                nr_static_tracks = len(result_data["static"])

                nr_dynamic_tracks_all += nr_dynamic_tracks
                nr_static_tracks_all += nr_static_tracks

                result_data_all = {}
                result_data_all["dynamic"] = []
                result_data_all["static"] = []

                result_data_all["dynamic"].extend(result_data["dynamic"])
                result_data_all["static"].extend(result_data["static"])

                print(
                    f"There are {nr_dynamic_tracks} dynamic tracks and {nr_static_tracks} static tracks in {scene_path}!!!"
                )

                raw_basename = os.path.basename(scene_path)
                raw_dirname = os.path.dirname(scene_path)
                fpsed_save_path = refile.smart_path_join(fps_save_path, raw_dirname.split("/")[-1], raw_basename)
                with refile.smart_open(fpsed_save_path, "wb") as wf:
                    pickle.dump(result_data_all, wf)

            except Exception as e:
                # Catch remote exception
                print(f"Task {i} for {track_data_paths[i]} Failed: {e}")

    print(
        f"{nr_dynamic_tracks_all} dynmaic tracks, {nr_static_tracks_all} static tracks, {nr_dynamic_tracks_all + nr_static_tracks_all} tracks in total"
    )
    # with refile.smart_open(merged_save_path, "wb") as wf:
    #     pickle.dump(result_data_all, wf)


# debug
def main2(args, track_data_paths, nr_dynamic_tracks_all, nr_static_tracks_all):
    for scene_path in track_data_paths:
        result_data, scene_path = fps_a_scene(scene_path, args.fps_npoints)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--fps_npoints", default=1024, type=int, help="keep how many points in pre-fps process. if -1, keep all"
    )
    parser.add_argument(
        "--val", action="store_true", default=False, help="process val split or not. save name depends on this tag."
    )
    args = parser.parse_args()
    postfix = "val_rrun" if args.val else "train_rrun"

    # train
    track_data_paths = []
    # track_data_dir = "s3://httdet3d/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0"
    # track_data_dir = "s3://ruanhao/offboard/20220111/track_data/86items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0"
    track_data_dir = "s3://ruanhao/offboard/20220225/track_data/646items/vehicle_mild_7frames_dis1.0_vel1.0_enlarge1.0"
    if args.val:
        track_data_dir = track_data_dir[:-1] if track_data_dir.endswith("/") else track_data_dir
        track_data_dir = track_data_dir + "_val"

    track_data_subdirs = refile.smart_glob(refile.smart_path_join(track_data_dir, "*"))
    for track_data_subdir in track_data_subdirs:
        track_data_paths.extend(refile.smart_glob(refile.smart_path_join(track_data_subdir, "*.pkl")))

    # import IPython; IPython.embed()
    merge_dir = track_data_dir

    print(f"{len(track_data_paths)} in total !!!")

    # if args.fps_npoints == -1:
    #     merged_save_path = refile.smart_path_join(merge_dir, f"merged_{postfix}.pkl")
    # else:
    #     merged_save_path = refile.smart_path_join(merge_dir, f"merged_max{args.fps_npoints}_{postfix}.pkl")
    if args.fps_npoints == -1:
        fps_save_path = refile.smart_path_join(merge_dir, f"fpsed_{postfix}")
    else:
        fps_save_path = refile.smart_path_join(merge_dir, f"fpsed_max{args.fps_npoints}_{postfix}")

    # print(merged_save_path)
    print(fps_save_path)

    # assert not refile.smart_exists(fpsed_save_path), "File Already Exists !!!"

    # result_data_all = {}
    # result_data_all["dynamic"] = []
    # result_data_all["static"] = []

    nr_dynamic_tracks_all = 0
    nr_static_tracks_all = 0

    # main(args, track_data_paths, nr_dynamic_tracks_all, nr_static_tracks_all, merged_save_path)  # train
    main(args, track_data_paths, nr_dynamic_tracks_all, nr_static_tracks_all, fps_save_path)  # train
    # main2(args, track_data_paths, nr_dynamic_tracks_all, nr_static_tracks_all)  # debug
