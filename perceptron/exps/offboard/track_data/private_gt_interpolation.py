# 非关键帧gt插值。
import json
import refile
import numpy as np
import nori2 as nori
from pyquaternion import Quaternion
import os
from tqdm import tqdm
import rrun
from pathlib import Path
import time
from perceptron.exps.offboard.track_data.utils import bbox_lidar_to_world, bbox_world_to_lidar


class Encoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(
            obj,
            (
                np.int_,
                np.intc,
                np.intp,
                np.int8,
                np.int16,
                np.int32,
                np.int64,
                np.uint8,
                np.uint16,
                np.uint32,
                np.uint64,
            ),
        ):
            return int(obj)
        if isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
            return float(obj)
        if isinstance(obj, (np.bool_)):
            return bool(obj)
        if isinstance(obj, (np.void)):
            return None
        if obj.__class__.__name__ == "ObjectId":
            return str(obj)
        return json.JSONEncoder.default(self, obj)


fetcher = nori.Fetcher()


def get_frame_timestamp(frame_info):
    timestamp = frame_info["sensor_data"]["fuser_lidar"]["timestamp"]
    return np.double(timestamp)


def get_anno_files(anno_paths):
    anno_files = []
    for cur_path in anno_paths:
        if refile.smart_isdir(cur_path):
            anno_files.extend(refile.smart_glob(refile.smart_path_join(cur_path, "*.json")))
        else:
            anno_files.append(cur_path)
    return anno_files


def cal_velocity(box1, box2, timestamp1, timestamp2, lidar_to_gnss, gnss_pose1, gnss_pose2):
    if gnss_pose1 is None or gnss_pose2 is None:
        return np.array([np.nan, np.nan, np.nan, np.nan])

    box1_world = bbox_lidar_to_world(box1, lidar_to_gnss, gnss_pose1)
    box2_world = bbox_lidar_to_world(box2, lidar_to_gnss, gnss_pose2)

    vx = (box2_world[0] - box1_world[0]) / (timestamp2 - timestamp1)
    vy = (box2_world[1] - box1_world[1]) / (timestamp2 - timestamp1)
    vz = (box2_world[2] - box1_world[2]) / (timestamp2 - timestamp1)

    v = np.array([vx, vy, vz])
    v = np.sqrt(np.sum(v ** 2))
    return np.array([vx, vy, vz, v])


def get_gnss_data(frame_info):
    if not frame_info.get("odom_data", None) and not frame_info.get("ins_data"):  # odom_data 和 ins_data都不存在
        return None

    if frame_info.get("odom_data", None):
        gnss_pose = frame_info["odom_data"]["pose"]["pose"]  # {"position", "orientation"}
    else:
        gnss_pose = dict(
            position=frame_info["ins_data"]["localization"]["position"],
            orientation=frame_info["ins_data"]["localization"]["orientation"],
        )

    return gnss_pose


def gt_interpolation_a_frame(frame_info, prev_frame_info, next_frame_info, lidar_to_gnss):

    # 获取点云时间戳信息
    prev_timestamp = get_frame_timestamp(prev_frame_info)
    next_timestamp = get_frame_timestamp(next_frame_info)
    cur_timestamp = get_frame_timestamp(frame_info)

    # 获取变换矩阵： 适配新/旧两种json格式(odom/ins)。 注意：部分数据可能存在缺失的情况。
    prev_gnss_pose = get_gnss_data(prev_frame_info)
    next_gnss_pose = get_gnss_data(next_frame_info)

    # box插值（仅非关键帧需要）
    if not frame_info["is_key_frame"]:
        labels = []

        for next_gt_box in next_frame_info["labels"]:
            for prev_gt_box in prev_frame_info["labels"]:
                if next_gt_box["track_id"] == prev_gt_box["track_id"]:
                    # Interpolate center.
                    global_prev_gt_box = bbox_lidar_to_world(prev_gt_box, lidar_to_gnss, prev_gnss_pose)
                    global_next_gt_box = bbox_lidar_to_world(next_gt_box, lidar_to_gnss, next_gnss_pose)
                    global_prev_gt_box_xyz = global_prev_gt_box[:3]
                    global_next_gt_box_xyz = global_next_gt_box[:3]

                    global_center = [
                        np.interp(cur_timestamp, [prev_timestamp, next_timestamp], [c0, c1])
                        for c0, c1 in zip(global_prev_gt_box_xyz, global_next_gt_box_xyz)
                    ]

                    # Interpolate orientation.
                    global_rotation = Quaternion.slerp(
                        q0=Quaternion(axis=[0, 0, 1], radians=global_prev_gt_box[-1]),
                        q1=Quaternion(axis=[0, 0, 1], radians=global_next_gt_box[-1]),
                        amount=(cur_timestamp - prev_timestamp) / (next_timestamp - prev_timestamp),
                    )

                    # Size: 取mean? 还是取某一帧
                    prev_size = [prev_gt_box["lwh"][i] for i in ["l", "w", "h"]]
                    next_size = [prev_gt_box["lwh"][i] for i in ["l", "w", "h"]]
                    size = [(x1 + x2) / 2 for x1, x2 in zip(prev_size, next_size)]

                    # 转化为 LiDAR 坐标系
                    # 数据预处理
                    global_box_info = {}
                    global_box_info["xyz_world"] = {"x": global_center[0], "y": global_center[1], "z": global_center[2]}
                    global_box_info["lwh"] = {"l": size[0], "w": size[1], "h": size[2]}
                    global_box_info["angle_world"] = {
                        "w": global_rotation.w,
                        "x": global_rotation.x,
                        "y": global_rotation.y,
                        "z": global_rotation.z,
                    }
                    # 转换为 lidar coordinate
                    cur_gnss_poss = get_gnss_data(frame_info)
                    bbox_lidar = bbox_world_to_lidar(global_box_info, lidar_to_gnss, cur_gnss_poss)
                    center = bbox_lidar[:3]
                    size = bbox_lidar[3:6]
                    rotation = Quaternion(axis=[0, 0, 1], radians=bbox_lidar[-1])

                    inter_box = {}
                    inter_box["xyz_lidar"] = {"x": center[0], "y": center[1], "z": center[2]}
                    inter_box["lwh"] = {"l": size[0], "w": size[1], "h": size[2]}
                    inter_box["angle_lidar"] = {
                        "w": rotation.w,
                        "x": rotation.x,
                        "y": rotation.y,
                        "z": rotation.z,
                    }

                    assert prev_gt_box["category"] == next_gt_box["category"]
                    inter_box["category"] = prev_gt_box["category"]
                    inter_box["track_id"] = prev_gt_box["track_id"]

                    # 属性无法插值，暂不存储

                    labels.append(inter_box)
        frame_info["labels"] = labels

        # 计算速度
        cur_gt_velocity = [np.array([np.nan, np.nan, np.nan, np.nan])] * len(frame_info["labels"])  # vx, vy, vz, v
        for cur_gt_box_idx, cur_gt_box in enumerate(frame_info["labels"]):
            cur_track_id = cur_gt_box["track_id"]
            for next_gt_box in next_frame_info["labels"]:
                if next_gt_box["track_id"] == cur_track_id:
                    for prev_gt_box in prev_frame_info["labels"]:
                        if prev_gt_box["track_id"] == cur_track_id:
                            cur_gt_velocity[cur_gt_box_idx] = cal_velocity(
                                prev_gt_box,
                                next_gt_box,
                                prev_timestamp,
                                next_timestamp,
                                lidar_to_gnss,
                                prev_gnss_pose,
                                next_gnss_pose,
                            )
                            cur_gt_box["velocity"] = cur_gt_velocity[cur_gt_box_idx]


def gt_interpolation_a_file(anno_file, save_dir):  # noqa
    """
    对单个json文件进行gt插值。
    """
    raw_base_name = os.path.basename(anno_file)
    raw_dir_name = os.path.dirname(anno_file)[5:].split("/")[-1]
    save_file = refile.smart_path_join(save_dir, raw_dir_name, raw_base_name)
    print(save_file)

    with refile.smart_open(anno_file, "r") as rf:
        json_data = json.load(rf)

    # 如果存在旧文件，就先删除
    if refile.smart_exists(save_file):
        os.system(f"aws --endpoint-url=http://oss.i.brainpp.cn s3 rm {save_file}")

    with refile.smart_open(save_file, "w") as wf:
        interval = json_data["interval"]
        nr_frames = len(json_data["frames"])
        for frame_idx in tqdm(range(0, nr_frames)):
            frame_info = json_data["frames"][frame_idx]

            if frame_info["sensor_data"]["fuser_lidar"]["nori_id"] is None:  # TBD: middle or fuser?
                print("Skip a empty frame ...")
                continue

            if not frame_info["is_key_frame"]:
                prev_frame_idx = (frame_idx // interval) * interval
                next_frame_idx = min(prev_frame_idx + interval, nr_frames - 1)
            else:
                prev_frame_idx = max(0, frame_idx - interval)
                next_frame_idx = min(frame_idx + interval, nr_frames - 1)
                if not json_data["frames"][next_frame_idx]["is_key_frame"]:
                    next_frame_idx = prev_frame_idx

            prev_frame_info = json_data["frames"][prev_frame_idx]
            next_frame_info = json_data["frames"][next_frame_idx]

            if next_frame_idx == nr_frames - 1 and "labels" not in next_frame_info:  # 跳过最后无法插值的几帧
                continue

            lidar_to_gnss = json_data["calibrated_sensors"]["middle_lidar"]["lidar_gnss"]["transform"]

            gt_interpolation_a_frame(frame_info, prev_frame_info, next_frame_info, lidar_to_gnss)

        json.dump(json_data, wf, indent=4, ensure_ascii=False, cls=Encoder)
    return anno_file, save_file


# 正常跑实验用
def main(anno_files, save_dir):
    # rrun
    nr_runners = min(len(anno_files), 64)
    spec = rrun.RunnerSpec()
    time_str = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
    spec.name = "gt_interpolate"
    spec.log_dir = os.path.join(os.getcwd(), "rrun_log", spec.name, time_str)
    Path(spec.log_dir).mkdir(exist_ok=True, parents=True)
    spec.charged_group = "transformer"
    if "BRAINPP_BLOCKLIST_PATH" in os.environ:
        with open(os.path.expanduser(os.environ["BRAINPP_BLOCKLIST_PATH"])) as rf:
            blocklist = [x.strip() for x in rf.readlines()]
        spec.scheduling_hint.negative_tags[:] = blocklist
    spec.resources.cpu = 4
    # spec.resources.gpu = 1
    spec.resources.memory_in_mb = 20 * 1024
    spec.max_wait_time = 600 * int(1e9)
    spec.preemptible = False

    futures = []

    with rrun.RRunExecutor(runner_spec=spec, num_runners=nr_runners) as executor:
        for anno_file in anno_files:
            futures.append(executor.submit(gt_interpolation_a_file, anno_file, save_dir))

        for i, f in enumerate(futures):
            try:
                anno_file, _ = f.result()
                print(f"Finish {anno_file}!!!")

            except Exception as e:
                # Catch remote exception
                print(f"[Error] Ruuner {i} Failed!!! {e}")

    print(f"ALL FINISHED! {len(anno_files)} Processed!")


# 调试用
def main2(anno_files, save_dir):
    for anno_file in anno_files:
        gt_interpolation_a_file(anno_file, save_dir)
    print(f"ALL FINISHED! {len(anno_files)} Processed!")


if __name__ == "__main__":

    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--val", action="store_true", default=False)
    args = parser.parse_args()

    if not args.val:
        # train
        anno_paths = [
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211118_tracking_checked/21111116002_2021-11-15_changshu3/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/22.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/24.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/26.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/29.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/33.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/37.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/39.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/61.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/63.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/68.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/7.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211206_tracking_checked/ppl_bag_20211125_063714/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211206_tracking_checked/ppl_bag_20211125_184138/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211206_tracking_checked/ppl_bag_20211125_202010/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211206_tracking_checked/ppl_bag_20211126_070124/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211206_tracking_checked/ppl_bag_20211126_193849/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211206_tracking_checked/ppl_bag_20211126_205425/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211213_tracking_checked/ppl_bag_20211129_082157/",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211213_tracking_checked/ppl_bag_20211129_091729/",
        ]
    else:
        # val
        anno_paths = [
            # "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/11.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/13.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/16.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/18.json",
            "s3://httdet3d/offboard/2022111/track_prelabels/tf-labeled-res/20211119_tracking_checked/21111116002_2021-11-15_changshu3/20.json",
        ]

    anno_files = get_anno_files(anno_paths)
    # save_dir = "s3://httdet3d/offboard/20220111/track_prelabels_gt_inter_rrun"
    save_dir = "s3://ruanhao/offboard/20220111/track_prelabels_gt_inter_rrun"  # 保存到ruanhao路径下，插值使用global坐标系

    if args.val:
        save_dir = save_dir[:-1] if save_dir.endswith("/") else save_dir
        save_dir += "_val"

    print(save_dir)

    main(anno_files, save_dir)  # train usage
    # main2(anno_files, save_dir)     # debug usage
