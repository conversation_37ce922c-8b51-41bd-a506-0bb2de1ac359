# JL car101模型 https://tf-discourse.megvii-inc.com/t/topic/1733/30
# rlaunch --cpu=16 --gpu=4 --memory=40960 --positive-tags=2080ti --preemptible no -- python3 perceptron/exps/seg3d/seg3d_pointpillar_good4trt_up01_3_4class_jl.py -b 4 -d 0-3 --sync_bn 1 -te


import numpy as np
from perceptron.data.seg3d.dataset.private_data.dataset_freespace import (
    PrivateDatasetFreespaceBEVMultiClass,
    PrivateDatasetFreespaceInference,
)
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import FreespaceCli
from torch.utils.data import DataLoader

from perceptron.exps.seg3d.seg3d_pointpillar_good4trt_up01_3_4class import ModelConfigs
from perceptron.exps.seg3d.seg3d_pointpillar_good4trt_up01_3_4class import PointPillarIouFcos as PointPillarIouFcosBase
from perceptron.exps.seg3d.seg3d_pointpillar_good4trt_up01_3_4class import Exp as BaseExp
from perceptron.exps.seg3d.private_dataset_path import (
    TRAIN_DATALIST_CAR101,
    EVAL_BMK_DATALIST_CAR101,
    INFERENCE_DATALIST,
)


class DataConfigs:
    point_cloud_range = [-19.2, -30, -30.0, 19.2, 85.2, 30.0]  # NOTE 32的整数倍
    voxel_size = [0.2, 0.2, 60]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)

    max_num_points = 16
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系    label assign用
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])  # label assign用
    neighbor_roi = [1.2, 4, freespace_h]  # dataset get contur gt用
    downsample_rate = 5  # inference 关键帧下采样率


class DataConfigLabel(DataConfigs):
    point_cloud_range = [-19.2, -30, -30.0, 19.2, 85.2, 30.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])
    neighbor_roi = [1.2, 4, freespace_h]


class PointPillarIouFcos(PointPillarIouFcosBase):
    def __init__(self, model_config, data_config, training=True):
        super().__init__(model_config, data_config, training)
        self.model_config = model_config
        self.data_config = data_config
        self.data_config_label = DataConfigLabel()
        self.training = training
        self.voxelizer = self.build_voxelizer(self.data_config.src_num_point_features)
        self.voxelizer_with_label = self.build_voxelizer(self.data_config.src_num_point_features + 1)
        self.vfe = self.build_vfe()
        self.map_to_bev = self.build_map_to_bev()
        self.backbone_2d = self.build_backbone_2d()
        self.dense_head = self.build_dense_head()


class Exp(BaseExp):
    def _configure_model(self):
        model = PointPillarIouFcos(model_config=ModelConfigs(), data_config=DataConfigs(), training=self.training)
        return model

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetFreespaceBEVMultiClass(
            DataConfigs(),
            ModelConfigs(),
            data_paths=TRAIN_DATALIST_CAR101,
            training=True,
            data_configs_output=DataConfigLabel(),
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=4,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_test_dataloader(self):
        dataset = PrivateDatasetFreespaceInference(
            DataConfigs(),
            ModelConfigs(),
            data_paths=INFERENCE_DATALIST,
            training=False,
            data_configs_output=DataConfigLabel(),
        )
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=2,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return dataloader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetFreespaceBEVMultiClass(
            DataConfigs(),
            ModelConfigs(),
            data_paths=EVAL_BMK_DATALIST_CAR101,
            training=False,
            data_configs_output=DataConfigLabel(),
        )
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=1,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader


if __name__ == "__main__":
    FreespaceCli(Exp).run()
