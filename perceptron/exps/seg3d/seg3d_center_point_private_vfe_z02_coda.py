"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --cpu=64 --gpu=4 --memory=81920 --charged-group=transformer --preemptible no -- python3 perceptron/exps/seg3d/seg3d_center_point_private_vfe_z02_coda.py --ckpt s3://train-log/zhangsipeng/perceptron/seg3d_center_point_private_vfe_z02/checkpoint_epoch_39.pth -b 4 --infer
"""

import numpy as np

from torch.utils.data import DataLoader

from perceptron.data.seg3d.dataset.private_data.dataset_freespace import (
    PrivateDatasetFreespaceInferenceCODA,
)
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import FreespaceCli

from auto_washing.private_data_path import INFERENCE_DATALIST
from auto_washing.config import get_default_config

from perceptron.exps.seg3d.seg3d_center_point_private_vfe_z02 import ModelConfigs
from perceptron.exps.seg3d.seg3d_center_point_private_vfe_z02 import Exp as BaseExp


from freespace.modules.filter import (
    GradeFilter,
    GridMinZFilter,
    ROIFilter,
    DynamicObjectFilter,
    BevSegFilter,
)


class DataConfigs:
    point_cloud_range = [-20, -10, -30.0, 20, 70, 30.0]
    filter_roi = [-30, -60, -30.0, 30, 300, 30.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 6
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])
    neighbor_roi = [1.2, 4, freespace_h]
    filters = [
        # roi filter, first rule
        ROIFilter(roi=filter_roi),
        DynamicObjectFilter(),
        # filter rules, simple rules before comlex rules
        BevSegFilter(point_cloud_range=point_cloud_range, voxel_size=voxel_size, threshold=0.4),
    ]

    filters_backup = [
        # 模型失效时的filter
        # roi filter, first rule
        ROIFilter(roi=filter_roi),
        DynamicObjectFilter(),
        # filter rules, simple rules before comlex rules
        GradeFilter(grade_th=5, origin_z=0.0),  # footprint_RFU坐标系
        GridMinZFilter(max_height_diff=0.1, grid_resolution=2),
    ]


class Exp(BaseExp):
    data_list = []

    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=40, eval=False, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.training = not eval
        self.dump_interval = 5
        self.eval_interval = 5
        self.ckpt_oss_save_dir = "s3://train-log/name/perceptron/" + self.exp_name
        self.output_dir = "/data/tmp/output/" + self.exp_name
        cfg = get_default_config()
        self.coda_save_path = cfg.FREESPACE.SAVE

    def _configure_test_dataloader(self):
        if Exp.data_list == []:
            Exp.data_list = INFERENCE_DATALIST
        dataset = PrivateDatasetFreespaceInferenceCODA(
            DataConfigs(),
            ModelConfigs(),
            data_paths=Exp.data_list,
            coda_save_path=self.coda_save_path,
            training=False,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=1,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return dataloader


if __name__ == "__main__":
    cli = FreespaceCli(Exp)
    cli.run()
