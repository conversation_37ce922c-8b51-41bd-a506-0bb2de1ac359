# 多分类上车模型 https://tf-discourse.megvii-inc.com/t/topic/1733/29
# rlaunch --cpu=16 --gpu=4 --memory=40960 --positive-tags=2080ti --preemptible no -- python3 perceptron/exps/seg3d/seg3d_pointpillar_good4trt_up01_3_4class.py -b 4 -d 0-3 --sync_bn 1 -te

from functools import partial

import numpy as np
import torch
import torch.optim as optim
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.seg3d.dataset.private_data.dataset_freespace import (
    PrivateDatasetFreespaceBEVMultiClass,
    PrivateDatasetFreespaceInference,
)
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.cli import FreespaceCli
from perceptron.layers.blocks_2d.det3d.base_bev_backbone import BaseBEVBackbone2
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.models.det3d import load_data_to_gpu
from perceptron.models.det3d.base_3d_detector import Base3DDetector
from torch.utils.data import DataLoader
from perceptron.layers.blocks_3d.det3d.vfe.pillar_vfe import PillarVFE
from perceptron.layers.blocks_2d.det3d.map_to_bev.pointpillar_scatter import PointPillarScatter
from perceptron.layers.head.det3d.target_assigner.freespace_bev_assigner import FreepaceBevAssigner_voxelize
from perceptron.layers.losses.det3d import SigmoidFocalClassificationLoss
from perceptron.layers.losses.seg2d import MultiClassFocalLoss
from perceptron.layers.head.det3d.seg_head import GridSegFreespaceHeadMultiClass

from perceptron.exps.seg3d.private_dataset_path import TRAIN_DATALIST, EVAL_BMK_DATALIST, INFERENCE_DATALIST


class DataConfigs:
    point_cloud_range = [-19.2, -10, -30.0, 19.2, 73.2, 30.0]  # smaller than det
    voxel_size = [0.2, 0.2, 60]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)

    max_num_points = 16
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系    label assign用
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])  # label assign用
    neighbor_roi = [1.2, 4, freespace_h]  # dataset get contur gt用


class DataConfigLabel(DataConfigs):
    point_cloud_range = [-19.2, -10, -30.0, 19.2, 73.2, 30.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])
    neighbor_roi = [1.2, 4, freespace_h]


class ModelConfigs:
    labels = {
        0: "background",
        1: "freespace",
        2: "static",
        3: "dynamic",
    }

    learning_map = {0: 0, 1: 1, 2: 3, 3: 2}
    num_class = len(learning_map)
    N_grid = 360

    # voxel feature encoder
    vfe_num_filters = [64]

    # map_to_bev
    map_to_bev_num_features = 64

    # backbone2d
    backbone2d_layer_nums = [3, 5, 5]
    backbone2d_layer_strides = [2, 2, 2]
    backbone2d_num_filters = [64, 128, 256]
    backbone2d_upsample_strides = [1, 2, 4]
    backbone2d_num_upsample_filters = [128, 128, 128]
    backbone2d_use_scconv = True
    backbone2d_upsample_output = True  # Backbone2d 上采样
    backbone2d_upsample_output_channel = 128

    # assigner
    target_assigner_alpha = 0.25
    target_assigner_gamma = 2

    # seg head
    seg_head_confidence_threshold = 0.5
    seg_head_num_classes = len(labels)
    seghead_in_channels = 128
    seghead_out_channels = 128

    seg_head_cls_branch = True
    seg_head_conf_branch = False
    seg_head_metric_branch = False
    seg_head_regress_branch = False
    seg_head_binary_seg = True  # NOTE 训练时设为True 推理时设为False

    seg_head_regression_dim = 1

    # loss
    loss_regr_weight = 0
    loss_cls_weight = 2.0
    loss_cls_binary_weight = 2.0
    target_assigner_alpha_cls = 1
    target_assigner_infinite = -1


class PointPillarIouFcos(Base3DDetector):
    def __init__(self, model_config, data_config, training=True):
        super().__init__(num_class=len(model_config.labels), class_names=model_config.labels.values())
        self.model_config = model_config
        self.data_config = data_config
        self.data_config_label = DataConfigLabel()
        self.training = training
        self.voxelizer = self.build_voxelizer(self.data_config.src_num_point_features)
        self.voxelizer_with_label = self.build_voxelizer(self.data_config.src_num_point_features + 1)
        self.vfe = self.build_vfe()
        self.map_to_bev = self.build_map_to_bev()
        self.backbone_2d = self.build_backbone_2d()
        self.dense_head = self.build_dense_head()

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    def build_voxelizer(self, num_point_features):
        return Voxelization(
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
            max_num_points=self.data_config.max_num_points,
            max_voxels=self.data_config.max_voxels,
            num_point_features=num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = PillarVFE(
            use_norm=True,
            with_distance=False,
            use_absolute_xyz=True,
            num_filters=self.model_config.vfe_num_filters,
            num_point_features=self.data_config.use_num_point_features,
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
        )
        return vfe

    def build_map_to_bev(self):
        return PointPillarScatter(
            num_bev_features=self.model_config.map_to_bev_num_features,
            grid_size=self.data_config.grid_size,
        )

    def build_backbone_2d(self):
        backbone_2d_module = BaseBEVBackbone2(
            layer_nums=self.model_config.backbone2d_layer_nums,
            layer_strides=self.model_config.backbone2d_layer_strides,
            num_filters=self.model_config.backbone2d_num_filters,
            upsample_strides=self.model_config.backbone2d_upsample_strides,
            num_upsample_filters=self.model_config.backbone2d_num_upsample_filters,
            input_channels=self.map_to_bev.num_bev_features,
            use_scconv=self.model_config.backbone2d_use_scconv,
            upsample_output=self.model_config.backbone2d_upsample_output,
            upsample_output_channel=self.model_config.backbone2d_upsample_output_channel,
        )
        return backbone_2d_module

    def build_dense_head(self):
        # deconv for segmantation 1. point seg 2. bev seg
        target_assigner = FreepaceBevAssigner_voxelize(
            data_config=self.data_config,
            data_config_label=self.data_config_label,
            model_config=self.model_config,
            freespace_labels=["freespace"],
            multi_class=True,
        )
        seg_head_module = GridSegFreespaceHeadMultiClass(
            target_assigner=target_assigner,
            in_channel=self.model_config.seghead_in_channels,
            out_channel=self.model_config.seghead_out_channels,
            num_classes=self.model_config.num_class,
            model_cfg=self.model_config,
            data_cfg=self.data_config,
            output_shape=self.data_config_label.grid_size[:2][::-1],
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                MultiClassFocalLoss(
                    alpha=self.model_config.target_assigner_alpha_cls,
                    gamma=self.model_config.target_assigner_gamma,
                    num_classes=self.model_config.seg_head_num_classes,
                    ignore_labels=[0, -1],
                ),
            )
            m.add_module(
                "crit_binary",
                SigmoidFocalClassificationLoss(
                    alpha=self.model_config.target_assigner_alpha, gamma=self.model_config.target_assigner_gamma
                ),
            )

        _build_losses(seg_head_module)

        return seg_head_module

    def forward(self, batch_dict):
        points = batch_dict["points"]
        gt_lidarseg = batch_dict.get("gt_lidarseg", None)

        if gt_lidarseg:
            label_with_point = [torch.cat([p, g], 1) for p, g in zip(points, gt_lidarseg)]
        else:  # 为了给可视化output打mask
            label_with_point = []
            for p in points:
                g = torch.zeros_like(p)[:, 0].view(-1, 1)
                label_with_point.append(torch.cat([p, g], 1))

        voxels, voxel_coords, voxel_num_points = self.voxelizer(points)
        pillar_features = self.vfe(voxels, voxel_coords, voxel_num_points)
        spatial_features = self.map_to_bev(pillar_features, voxel_coords)
        spatial_features_2d, pyramid = self.backbone_2d(spatial_features)

        output = self.dense_head(spatial_features_2d, label_with_point)

        if self.training:
            loss_dict = self.dense_head.get_loss(output)
            return loss_dict
        else:
            return output


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, eval=False, **kwargs):
        self.lr = 0.003
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.training = not eval
        self.dump_interval = 5
        self.eval_interval = 5
        self.ckpt_oss_save_dir = "s3://train-log/name/perceptron/" + self.exp_name
        self.output_dir = "/data/tmp/output/" + self.exp_name

    def _configure_model(self):
        model = PointPillarIouFcos(model_config=ModelConfigs(), data_config=DataConfigs(), training=self.training)
        return model

    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict = self.model(batch)
        loss = ret_dict["loss"].mean()
        extra_dict = ret_dict
        return loss, extra_dict

    def test_step(self, batch):
        load_data_to_gpu(batch)
        batch = self.model(batch)
        batch["pred_seg"] = torch.argmax(batch["pred_seg"], -1)
        return batch

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetFreespaceBEVMultiClass(
            DataConfigs(),
            ModelConfigs(),
            data_paths=TRAIN_DATALIST,
            training=True,
            data_configs_output=DataConfigLabel(),
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=4,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_test_dataloader(self):
        dataset = PrivateDatasetFreespaceInference(
            DataConfigs(),
            ModelConfigs(),
            data_paths=INFERENCE_DATALIST,
            training=False,
            data_configs_output=DataConfigLabel(),
        )
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=2,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return dataloader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetFreespaceBEVMultiClass(
            DataConfigs(),
            ModelConfigs(),
            data_paths=EVAL_BMK_DATALIST,
            training=False,
            data_configs_output=DataConfigLabel(),
        )
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=1,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_optimizer(self):
        def children(m: torch.nn.Module):
            return list(m.children())

        def num_children(m: torch.nn.Module) -> int:
            return len(children(m))

        def flatten_model(m):
            return sum(map(flatten_model, m.children()), []) if num_children(m) else [m]  # noqa

        def get_layer_groups(m):
            return [torch.nn.Sequential(*flatten_model(m))]  # noqa

        optimizer_func = partial(optim.Adam, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func, self.lr, get_layer_groups(self.model), wd=0.01, true_wd=True, bn_wd=True
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    FreespaceCli(Exp).run()
