# 训练及测试用数据list

TRAIN_DATALIST = [
    "s3://zhangsipeng-share/tmp/freespace_label/20220729_freespace_checked/",  # 1740 0610数据
    "s3://zhangsipeng-share/tmp/freespace_label/20220611_freespace_label_all/",  # 2640
    "s3://zhangsipeng-share/tmp/freespace_label/20220612_freespace_label/",  # 2120
    "s3://zhangsipeng-share/tmp/freespace_label/20220622_freespace_label/",  # 880
    "s3://zhangsipeng-share/tmp/freespace_label/20220624_freespace_label/",  # 1320
    "s3://zhangsipeng-share/tmp/freespace_label/20220625_freespace_label/",  # 420
    "s3://zhangsipeng-share/tmp/freespace_label/20220702_freespace_label/",  # 2320
]
TRAIN_DATALIST_PSEUDO = [
    "s3://zhangsipeng-share/tmp/freespace_label/20220618_freespace_prelabel/",  # 2540
    "s3://zhangsipeng-share/tmp/freespace_label/20220619_freespace_prelabel/",  # 2760
    "s3://zhangsipeng-share/tmp/freespace_label/20220621_freespace_prelabel/",  # 2320
    "s3://zhangsipeng-share/tmp/freespace_label/20220623_freespace_prelabel/",  # 460
    "s3://zhangsipeng-share/tmp/freespace_label/20220601_freespace_prelabel/",  # 12220 0521-0525数据
    "s3://zhangsipeng-share/tmp/freespace_label/20220608_freespace_prelabel/",  # 1820 0529数据
    "s3://zhangsipeng-share/tmp/freespace_label/20220615_freespace_prelabel/",  # 660
    "s3://zhangsipeng-share/tmp/freespace_label/20220616_freespace_prelabel/",  # 700
    "s3://zhangsipeng-share/tmp/freespace_label/20220617_freespace_prelabel/",  # 440
]
TRAIN_DATALIST_PSEUDO_PART = [
    "s3://zhangsipeng-share/tmp/freespace_label/20220618_freespace_prelabel/",  #
    "s3://zhangsipeng-share/tmp/freespace_label/20220619_freespace_prelabel/",
    "s3://zhangsipeng-share/tmp/freespace_label/20220621_freespace_prelabel/",
    "s3://zhangsipeng-share/tmp/freespace_label/20220623_freespace_prelabel/",
]
TRAIN_DATALIST_CAR4 = [
    "s3://zhangsipeng-share/tmp/freespace_label/car4_20221011_freespace_label/",  # 1380
]
TRAIN_DATALIST_CAR101 = [
    "s3://zhangsipeng-share/tmp/freespace_label/car101_20221014_freespace_label/",  # 3014
    "s3://zhangsipeng-share/tmp/freespace_label/car101_20221111_freespace_label/",  # 2060
]
EVAL_BMK_DATALIST = [
    "s3://zhangsipeng-share/tmp/freespace_label/20220714_freespace_checked/",
    "s3://zhangsipeng-share/tmp/freespace_label/20220626_freespace_label/",
    "s3://zhangsipeng-share/tmp/freespace_label/20220701_freespace_label/",
    # "s3://zhangsipeng-share/tmp/freespace_label/car3_20220921_freespace_label/", # car3
]
EVAL_BMK_DATALIST_CAR4 = [
    "s3://zhangsipeng-share/tmp/freespace_label/car4_20221010_freespace_label/",
]
EVAL_BMK_DATALIST_CAR101 = [
    "s3://zhangsipeng-share/tmp/freespace_label/car101_20221006_freespace_label/",
]

INFERENCE_DATALIST = [("s3://tf-22q3-shared-data/labeled_data/car_101/20221006_3dbmk-tracking_yueying_checked/", "v0")]
