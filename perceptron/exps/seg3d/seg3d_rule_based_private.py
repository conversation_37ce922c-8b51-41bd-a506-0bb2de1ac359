"""
Rulebased freespace method. Implemented in perceptron for comparison.
"""
import numpy as np

from perceptron.models.det3d import load_data_to_gpu

from perceptron.exps.seg3d.seg3d_center_point_private_30 import Exp as BaseExp
from perceptron.engine.cli import FreespaceCli
from freespace.modules.filter import <PERSON><PERSON>ilter, GridMinZFilter, ransac_filter


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=40, eval=False, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.training = not eval
        self.dump_interval = 1
        self.ckpt_oss_save_dir = "s3://train-log/zhangsipeng/perceptron/" + self.exp_name
        self.output_dir = "/data/tmp/output/" + self.exp_name
        self.filters = [
            GradeFilter(grade_th=5, origin_z=0.0),  # footprint_RFU坐标系
            GridMinZFilter(max_height_diff=0.1, grid_resolution=2),
        ]

    def _configure_model(self):
        model = None
        return model

    def test_step(self, batch):
        pred_lidarseg = []
        for b in range(batch["batch_size"]):
            points = batch["points"][b]
            floor_flags = np.ones((len(points))).astype(bool)
            for i, filter in enumerate(self.filters):
                res = filter(points, floor_flags)
                points, floor_flags = res["points"], res["floor_flags"]
            floor_point, floor_flags = ransac_filter(points, floor_flags, thinkness_th=0.1)
            pred_lidarseg.append(floor_flags.reshape(-1, 1))

        batch["pred_lidarseg"] = pred_lidarseg
        load_data_to_gpu(batch)
        batch = self.model(batch)
        return batch

    def train_step(self, batch):
        raise NotImplementedError


if __name__ == "__main__":
    FreespaceCli(Exp).run()
