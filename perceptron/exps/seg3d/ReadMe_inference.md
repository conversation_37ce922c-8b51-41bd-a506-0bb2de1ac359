# Inference
1. 修改 `perceptron/exps/seg3d/private_dataset_path.py#L17` 为需要inference的ppls父目录。目前只支持障碍物检测标注过的路径，其他的路径需要改data provider
2. 如果有部分数据不需要Inference, 可以修改`perceptron/data/det3d/dataset/private_data/dataset_freespace.py#L507`
3. inference指令示例
> rlaunch --cpu=4 --gpu=4 --memory=40960 -- python3 perceptron/exps/seg3d/seg3d_center_point_private_vfe_z02.py --ckpt s3://train-log/zhangsipeng/perceptron/seg3d_center_point_private_vfe_z02/checkpoint_epoch_39.pth -d 0 -b 2 --infer
4. 结果会存到 `perceptron/exps/seg3d/seg3d_center_point_private_vfe_z02.py#L219` 指定文件夹下的preds_{gpu}.pkl。将repo freespace中的config-bev_pred_paths 改成这个路径即可。
