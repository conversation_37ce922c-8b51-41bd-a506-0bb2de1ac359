"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --cpu=64 --gpu=4 --memory=81920 --charged-group=transformer --preemptible no -- python3 perceptron/exps/seg3d/seg3d_center_point_private_30.py -d 0-3 -b 2 -te

"""

import numpy as np

import torch
from torch.utils.data import DataLoader

from perceptron.models.det3d import load_data_to_gpu
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.seg3d.dataset.private_data.dataset_freespace import (
    PrivateDatasetFreespace,
    PrivateDatasetFreespaceInference,
)
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import CenterPoints as BaseFCos
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import ModelConfigs as BaseConfigs
from perceptron.engine.cli import FreespaceCli
from perceptron.layers.blocks_3d.det3d import VoxelResBackBone8x

from perceptron.layers.head.det3d.target_assigner.freespace_bev_assigner import FreepaceBevAssigner
from perceptron.layers.losses.det3d import SigmoidFocalClassificationLoss
from perceptron.layers.head.det3d.seg_head import GridSegFreespaceHead
from perceptron.exps.seg3d.private_dataset_path import TRAIN_DATALIST, EVAL_BMK_DATALIST, INFERENCE_DATALIST


class DataConfigs:
    point_cloud_range = [-20, -10, -30.0, 20, 70, 30.0]
    voxel_size = [0.1, 0.1, 0.5]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)

    max_num_points = 6
    max_voxels = 40000
    src_num_point_features = 4
    use_num_point_features = 4

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])
    neighbor_roi = [1.2, 4, freespace_h]


class ModelConfigs(BaseConfigs):
    labels = {
        0: "background",
        1: "freespace",
        2: "static",
        3: "dynamic",
    }

    learning_map = {0: 0, 1: 1, 2: 2, 3: 3}
    N_grid = 360

    map_to_bev_num_features = 896  # 128 * grid_size_z / 16

    # seg head
    seg_head_confidence_threshold = 0.5
    seg_head_num_classes = len(labels)
    seghead_in_channels = 512
    seghead_out_channels = 64

    seg_head_cls_branch = True
    seg_head_conf_branch = False
    seg_head_metric_branch = False
    seg_head_regress_branch = False

    seg_head_regression_dim = 1

    # loss
    loss_regr_weight = 0
    loss_cls_weight = 1.0


class FCosIouAware(BaseFCos):
    def __init__(self, model_config, data_config, training=True):
        super().__init__(model_config=model_config, data_config=data_config)
        self.training = training
        self.voxelizer_with_label = self.build_voxelizer_label()
        self.voxelizer = self.build_voxelizer()
        self.backbone_3d = self.build_backbone_3d()
        self.dense_head = self.build_dense_head()

    def build_backbone_3d(self):
        return VoxelResBackBone8x(
            input_channels=self.vfe.get_output_feature_dim(),
            grid_size=self.data_config.grid_size,
            last_pad=0,
        )

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
            max_num_points=self.data_config.max_num_points,
            max_voxels=self.data_config.max_voxels,
            num_point_features=self.data_config.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_voxelizer_label(self):
        return Voxelization(
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
            max_num_points=self.data_config.max_num_points,
            max_voxels=self.data_config.max_voxels,
            num_point_features=self.data_config.src_num_point_features + 1,
            device=torch.device("cuda"),
        )

    def build_dense_head(self):
        target_assigner = FreepaceBevAssigner(
            data_config=self.data_config, model_config=self.model_config, freespace_labels=["freespace"]
        )
        seg_head_module = GridSegFreespaceHead(
            target_assigner=target_assigner,
            in_channel=self.model_config.seghead_in_channels,
            out_channel=self.model_config.seghead_out_channels,
            num_classes=1,  # freespace or barrier - > sigmoid loss
            model_cfg=self.model_config,
            data_cfg=self.data_config,
            output_shape=self.data_config.grid_size[:2][::-1],
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                SigmoidFocalClassificationLoss(
                    alpha=self.model_config.target_assigner_alpha, gamma=self.model_config.target_assigner_gamma
                ),  # 多分类适合sigmoid
            )

        _build_losses(seg_head_module)

        return seg_head_module

    def forward(self, batch_dict):
        points = batch_dict["points"]
        gt_lidarseg = batch_dict.get("gt_lidarseg", None)
        batch_size = batch_dict["batch_size"]

        if gt_lidarseg:
            label_with_point = [torch.cat([p, g], 1) for p, g in zip(points, gt_lidarseg)]
        else:  # 为了给可视化output打mask
            label_with_point = []
            for p in points:
                g = torch.zeros_like(p)[:, 0].view(-1, 1)
                label_with_point.append(torch.cat([p, g], 1))

        voxels, voxel_coords, voxel_num_points = self.voxelizer_with_label(
            label_with_point
        )  # min(40000, non_zero(400 x 800 x 10)) x max_num_points x 4 = M x max_num_points x 4
        voxel_labels = voxels[:, :, -1]
        voxels = voxels[:, :, : self.data_config.src_num_point_features]
        voxel_features = self.vfe(voxels, voxel_num_points)  # M x 4
        encoded_spconv_tensor, encoded_spconv_tensor_stride, multi_scale_3d_features = self.backbone_3d(
            voxel_features, voxel_coords, batch_size
        )  # non_zero(4, 100, 50) * 128 (x, y 8倍下采样， z 16倍下采样，中间有一次取整)
        spatial_features, encoded_spconv_tensor_stride = self.map_to_bev(
            encoded_spconv_tensor, encoded_spconv_tensor_stride
        )  # 1 x 512 x 100 x 50    n,c,h/8,w/8
        spatial_features_2d, pyramid = self.backbone_2d(spatial_features)  # 1 x 512 x 200 x 100, xy四倍下采样

        gt_meta = (voxel_labels, voxel_coords, voxel_num_points)
        output = self.dense_head(spatial_features_2d, gt_meta)

        if self.training:
            loss_dict = self.dense_head.get_loss(output)
            return loss_dict
        else:
            return output


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=40, eval=False, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.training = not eval
        self.dump_interval = 1
        self.ckpt_oss_save_dir = "s3://train-log/name/perceptron/" + self.exp_name
        self.output_dir = "/data/tmp/output/" + self.exp_name

    def _configure_model(self):
        model = FCosIouAware(model_config=ModelConfigs(), data_config=DataConfigs(), training=self.training)
        return model

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetFreespace(DataConfigs(), ModelConfigs(), data_paths=TRAIN_DATALIST, training=True)
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=2,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_test_dataloader(self):
        dataset = PrivateDatasetFreespaceInference(
            DataConfigs(), ModelConfigs(), data_paths=INFERENCE_DATALIST, training=False
        )
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=2,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return dataloader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetFreespace(DataConfigs(), ModelConfigs(), data_paths=EVAL_BMK_DATALIST, training=False)
        sampler = InfiniteSampler(len(dataset), shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=1,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict = self.model(batch)
        loss = ret_dict["loss"].mean()
        extra_dict = ret_dict
        return loss, extra_dict

    def test_step(self, batch):
        load_data_to_gpu(batch)
        batch = self.model(batch)
        batch["pred_seg"] = torch.sigmoid(batch["pred_seg"])
        return batch


if __name__ == "__main__":
    FreespaceCli(Exp).run()
