# det+freespace联合训练，2分类
# 实验贴 https://tf-discourse.megvii-inc.com/t/topic/1733/27 exp20.10
# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/seg3d/det3d_pointpillar_iou_fcos_binary_seg_joint_detall_e90_lm_aug.py -b 1 --sync_bn 3 --no-clearml --pretrained_model s3://httdet3d/to_zsp/det3d_pointpillar_iou_fcos_mh_dr_fulllr_largeinput2_5w6_goodfortrt_v2/checkpoint_epoch_39.pth

import easydict
import numpy as np
import torch
from torch.utils.data import DataLoader, DistributedSampler
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval
from perceptron.data.seg3d.dataset.private_data.dataset_det_freespace import (
    PrivateDatasetDetFreespaceAug,
    PrivateDatasetFreespace,
)
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import FreespaceCli
from perceptron.engine.cli.det3d_cli import Det3DCli  # noqa
from perceptron.layers.blocks_2d.det3d.base_bev_backbone import BaseBEVBackbone2
from perceptron.layers.losses.det3d import CenterNetRegLoss, WeightedFocalLoss, WeightFocalLossWHDR
from perceptron.models.det3d import load_data_to_gpu
from perceptron.models.det3d.base_3d_detector import Base3DDetector
from perceptron.layers.head.det3d import FCOSAssignerMaskReg, IouAwareGenProposals
from perceptron.layers.head.det3d.center_head_iou_aware import CenterHeadIouAwareDRMaskFIx
from perceptron.layers.head.det3d.target_assigner.freespace_bev_assigner import FreepaceBevAssigner_voxelize
from perceptron.layers.losses.det3d import SigmoidFocalClassificationLoss
from perceptron.layers.head.det3d.seg_head import GridSegFreespaceHead
from perceptron.layers.blocks_3d.det3d.vfe.pillar_vfe import PillarVFE
from perceptron.layers.blocks_2d.det3d.map_to_bev.pointpillar_scatter import PointPillarScatter

from perceptron.exps.det3d.pointpillar_iou_fcos import Exp as BaseExp
from perceptron.exps.det3d.pointpillar_iou_fcos import ModelConfigs as BaseConfigs

from perceptron.exps.det3d.release.private_dataset_path import EVAL_CAR2_BMK_CHENGQU_REPAIRED, TRAIN_CAR2_DATALIST
from perceptron.exps.seg3d.private_dataset_path import (
    TRAIN_DATALIST,
    TRAIN_DATALIST_PSEUDO,
    EVAL_BMK_DATALIST,
)

TRAIN_DATALIST = TRAIN_DATALIST + TRAIN_DATALIST_PSEUDO


class DataConfigs:
    point_cloud_range = [-22.4, -75.2, -5.0, 22.4, 75.2, 3.0]
    voxel_size = [0.1, 0.1, 8]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 16
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系    label assign用
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])  # label assign用
    neighbor_roi = [1.2, 4, freespace_h]  # dataset get contur gt用


class DataConfigLabel(DataConfigs):
    point_cloud_range = [-22.4, -75.2, -5.0, 22.4, 75.2, 3.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)

    freespace_h = 3  # 必须能被voxel_size_z整除, footprint_RFU坐标系
    freespace_grid = int((freespace_h - point_cloud_range[2]) / voxel_size[2])
    neighbor_roi = [1.2, 4, freespace_h]


class ModelConfigs(BaseConfigs):
    class_name = (
        "car",
        "truck",
        "construction_vehicle",
        "bus",
        "motorcycle",
        "bicycle",
        "tricycle",
        "cyclist",
        "pedestrian",
        "masked_area",
    )

    # dense head
    densehead_tasks = [
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "car",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 3,
                "class_names": [
                    "truck",
                    "construction_vehicle",
                    "bus",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 3,
                "class_names": [
                    "motorcycle",
                    "bicycle",
                    "tricycle",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "cyclist",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "pedestrian",
                ],
            }
        ),
    ]

    # target assigner
    target_assigner_mapping = easydict.EasyDict(
        {
            "car": 1,
            "truck": 2,
            "construction_vehicle": 3,
            "bus": 4,
            "motorcycle": 5,
            "bicycle": 6,
            "tricycle": 7,
            "cyclist": 8,
            "pedestrian": 9,
            "masked_area": 10,
        }
    )
    target_assigner_max_objs = 2500
    proposal_iou_aware_list = [0.65] * len(densehead_tasks)
    proposal_post_center_limit_range = [-22.4, -75.2, -5.0, 22.4, 75.2, 3.0]
    proposal_pc_range = [-22.4, -75.2]
    proposal_voxel_size = [0.1, 0.1]

    # ====== freespace ============
    labels = {
        0: "background",
        1: "freespace",
        2: "static",
        3: "dynamic",
    }

    learning_map = {0: 0, 1: 1, 2: 2, 3: 3}
    N_grid = 360

    # voxel feature encoder
    vfe_num_filters = [64]

    # map_to_bev
    map_to_bev_num_features = 64

    # backbone2d
    backbone2d_layer_nums = [3, 5, 5]
    backbone2d_layer_strides = [2, 2, 2]
    backbone2d_num_filters = [64, 128, 256]
    backbone2d_upsample_strides = [1, 2, 4]
    backbone2d_num_upsample_filters = [128, 128, 128]
    backbone2d_use_scconv = True
    backbone2d_upsample_output = False

    # assigner
    target_assigner_alpha = 0.25
    target_assigner_gamma = 2

    # seg head
    seg_head_confidence_threshold = 0.5
    seg_head_num_classes = len(labels)
    seghead_in_channels = 384
    seghead_out_channels = 64

    seg_head_cls_branch = True
    seg_head_conf_branch = False
    seg_head_metric_branch = False
    seg_head_regress_branch = False

    seg_head_regression_dim = 1

    # loss
    loss_regr_weight = 0
    loss_cls_weight = 10
    loss_label_smooth_lambda = 0.9


class PointPillarIouFcos(Base3DDetector):
    def __init__(self, model_config, data_config, training=True):
        super().__init__(num_class=len(model_config.labels), class_names=model_config.class_name)

        self.model_config = model_config
        self.data_config = data_config
        self.training = training
        self.voxelizer = self.build_voxelizer(self.data_config.src_num_point_features)
        self.voxelizer_with_label = self.build_voxelizer(self.data_config.src_num_point_features + 1)

        self.vfe = self.build_vfe()
        self.map_to_bev = self.build_map_to_bev()
        self.backbone_2d = self.build_backbone_2d()
        self.dense_head = self.build_dense_head_det()
        self.dense_head_seg = self.build_dense_head_seg()

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    def build_voxelizer(self, num_point_features):
        return Voxelization(
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
            max_num_points=self.data_config.max_num_points,
            max_voxels=self.data_config.max_voxels,
            num_point_features=num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = PillarVFE(
            use_norm=True,
            with_distance=False,
            use_absolute_xyz=True,
            num_filters=self.model_config.vfe_num_filters,
            num_point_features=self.data_config.use_num_point_features,
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
        )
        return vfe

    def build_map_to_bev(self):
        return PointPillarScatter(
            num_bev_features=self.model_config.map_to_bev_num_features,
            grid_size=self.data_config.grid_size,
        )

    def build_backbone_2d(self):
        backbone_2d_module = BaseBEVBackbone2(
            layer_nums=self.model_config.backbone2d_layer_nums,
            layer_strides=self.model_config.backbone2d_layer_strides,
            num_filters=self.model_config.backbone2d_num_filters,
            upsample_strides=self.model_config.backbone2d_upsample_strides,
            num_upsample_filters=self.model_config.backbone2d_num_upsample_filters,
            input_channels=self.map_to_bev.num_bev_features,
            use_scconv=self.model_config.backbone2d_use_scconv,
            upsample_output=self.model_config.backbone2d_upsample_output,
        )
        return backbone_2d_module

    def build_dense_head_det(self):
        target_assigner = FCOSAssignerMaskReg(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )

        proposal_layer = IouAwareGenProposals(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            iou_aware_list=self.model_config.proposal_iou_aware_list,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
        )

        dense_head_module = CenterHeadIouAwareDRMaskFIx(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            out_size_factor=self.model_config.densehead_out_size_factor,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            iou_weight=self.model_config.densehead_loss_iou_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                WeightedFocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma),
            )
            m.add_module("crit_dr", WeightFocalLossWHDR())
            m.add_module("crit_iou_aware", CenterNetRegLoss())
            m.add_module("crit_reg", CenterNetRegLoss())

        _build_losses(dense_head_module)

        return dense_head_module

    def build_dense_head_seg(self):
        # deconv for segmantation 1. point seg 2. bev seg
        target_assigner_seg = FreepaceBevAssigner_voxelize(
            data_config=self.data_config,
            model_config=self.model_config,
            data_config_label=DataConfigLabel(),
            freespace_labels=["freespace"],
        )
        seg_head_module = GridSegFreespaceHead(
            target_assigner=target_assigner_seg,
            in_channel=self.model_config.seghead_in_channels,
            out_channel=self.model_config.seghead_out_channels,
            num_classes=1,
            model_cfg=self.model_config,
            data_cfg=self.data_config,
            output_shape=self.data_config.grid_size[:2][::-1],
        )

        def _build_losses_seg(m):
            m.add_module(
                "crit",
                SigmoidFocalClassificationLoss(
                    alpha=self.model_config.target_assigner_alpha, gamma=self.model_config.target_assigner_gamma
                ),
            )

        _build_losses_seg(seg_head_module)

        return seg_head_module

    def forward(self, batch_dict):
        points = batch_dict["points"]
        gt_lidarseg = batch_dict.get("gt_lidarseg", None)
        gt_boxes = batch_dict.get("gt_boxes", None)

        if gt_lidarseg:
            label_with_point = [torch.cat([p, g], 1) for p, g in zip(points, gt_lidarseg)]
        else:
            label_with_point = []
            for p in points:
                g = torch.zeros_like(p)[:, 0].view(-1, 1)
                label_with_point.append(torch.cat([p, g], 1))

        voxels, voxel_coords, voxel_num_points = self.voxelizer(points)
        pillar_features = self.vfe(voxels, voxel_coords, voxel_num_points)
        spatial_features = self.map_to_bev(pillar_features, voxel_coords)
        spatial_features_2d, pyramid = self.backbone_2d(spatial_features)

        output_det = self.dense_head(spatial_features_2d, gt_boxes)
        output_seg = self.dense_head_seg(spatial_features_2d, label_with_point)

        if self.training:
            loss_rpn, tb_dict = self.dense_head.get_loss(output_det)
            ret_dict = {"det_loss": loss_rpn}
            output_seg["label_smooth_lambda"] = batch_dict["label_smooth_lambda"]
            ret_dict.update(self.dense_head_seg.get_loss(output_seg))
            ret_dict["loss"] += loss_rpn
            return ret_dict
        else:
            return output_det, output_seg


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 0.001
        self.training = not eval
        self.dump_interval = 5
        self.eval_interval = 5
        self.ckpt_oss_save_dir = "s3://train-log/name/perceptron/" + self.exp_name
        self.output_dir = "/data/tmp/output/" + self.exp_name

    def _configure_model(self):
        model = PointPillarIouFcos(
            model_config=ModelConfigs(),
            data_config=DataConfigs(),
        )
        return model

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetDetFreespaceAug(
            data_configs=DataConfigs(),
            model_configs=ModelConfigs(),
            data_paths=TRAIN_DATALIST,
            data_paths_det=TRAIN_CAR2_DATALIST,
            class_names_det=ModelConfigs().class_name,
            training=True,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=4,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetFreespace(
            data_configs=DataConfigs(),
            model_configs=ModelConfigs(),
            data_paths=EVAL_BMK_DATALIST,
            training=False,
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=0,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def training_step(self, batch):
        frame_token = batch.pop("frame_token", "_label")
        batch["label_smooth_lambda"] = []
        for token in frame_token:
            if "prelabel" in token:
                batch["label_smooth_lambda"].append(ModelConfigs().loss_label_smooth_lambda)
            elif "_label" in token:
                batch["label_smooth_lambda"].append(1)
        batch["label_smooth_lambda"] = np.array(batch["label_smooth_lambda"])

        load_data_to_gpu(batch)
        ret_dict = self.model(batch)
        loss = ret_dict["loss"].mean()
        extra_dict = ret_dict
        return loss, extra_dict

    def test_step(self, batch):
        load_data_to_gpu(batch)
        batch_det, batch_seg = self.model(batch)
        batch_seg["pred_seg"] = torch.sigmoid(batch_seg["pred_seg"])
        return batch_seg


class ExpDet(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 0.002
        self.training = not eval
        self.dump_interval = 5
        self.eval_interval = 5
        self.ckpt_oss_save_dir = "s3://train-log/zhangsipeng/perceptron/" + self.exp_name
        self.output_dir = "/data/tmp/output/" + self.exp_name

    def _configure_model(self):
        model = PointPillarIouFcos(
            model_config=ModelConfigs(),
            data_config=DataConfigs(),
        )
        return model

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=EVAL_CAR2_BMK_CHENGQU_REPAIRED,
            class_names=ModelConfigs().class_name,
            training=False,
            lidar_key_list=["fuser_lidar"],
            # 为 True的时候，需要搭配使用有 2d 遮挡属性标注的bmk EVAL_BMK_DATALIST_WITH_LABELED_2D
            use_occluded=False,
            # NOTE: use_occluded = True 需要设置 img_key_list，判断在该 img_key_list 下 object 是否可见。
            # img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=0,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def test_step(self, batch):
        load_data_to_gpu(batch)
        batch_det, batch_seg = self.model(batch)
        return batch_det


if __name__ == "__main__":
    FreespaceCli(Exp).run()
    # Det3DCli(ExpDet).run()    # eval det
