"""
-> train:
    python3 perceptron/exps/lane3d/bevlanefcn_privatelane_exp.py --no-clearml --sync_bn 8
-> test:
    python3 perceptron/exps/lane3d/bevlanefcn_privatelane_exp.py --eval --ckpt outputs/bevlanefcn_privatelane_exp/latest/dump_model/checkpoint_epoch_17.pth
-> eval:
    python3 perceptron/data/lane3d/metric/eval.py -p outputs/bevlanefcn_privatelane_exp/latest/evaluations/results.pkl
-> result:
    17e: F1=74, P@R=80@68.8
"""
import os
import pickle

import cv2
import numpy as np
import torch
import torchvision.transforms as transforms
from mmcv import Config
from torch.utils.data.dataloader import default_collate
from torch.utils.data.distributed import DistributedSampler
from torchinfo import summary
from tqdm import tqdm

from perceptron.data.lane3d.dataset import BEVLaneFCNDataset
from perceptron.data.lane3d.dataset.bevlanefcn_dataset import (
    AppendAffineMatrix,
    AppendFrustumCoord,
    AppendPixelCoord,
    BackupRawData,
    Bundle,
    Collect,
    Crop,
    FilterLanesBEV,
    FilterLanesRV,
    NormalizeImage,
    PreprocessRowAnchor,
    RandomHorizontalFlip,
    RandomRotate,
    RandomScale,
)
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import BaseCli
from perceptron.engine.executors import BaseExecutor
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler
from perceptron.models.lane3d import BEVLaneFCN
from perceptron.utils import torch_dist as dist


class BaseEvaluator(BaseExecutor):
    def eval(self):
        exp = self.exp
        self.val_iter = iter(self.val_dataloader)
        self._invoke_callback("before_eval")
        self.model.cuda()
        self.model.eval()
        output = []
        for step in tqdm(range(len(self.val_dataloader))):
            data = next(self.val_iter)
            output.extend(exp.test_step(data))
            self._invoke_callback("after_step", step, {})
        exp.test_epoch_end(output, output_dir=os.path.split(self.logger._core.handlers[1]._name)[0][1:])
        self._invoke_callback("after_eval")


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=8, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 1
        self.num_keep_latest_ckpt = max_epoch
        self.eval_executor_class = BaseEvaluator

        self.raw_dataset_cfg = dict(data_source="car_two_v2", use_redis=True)

        self.num_workers = 4

        self.basic_lr_per_img = 2e-4 / 16
        self.weight_decay = 2e-4

        self.voxel_range = [-15.6, 15.6, 3, 63, -2, 0]
        self.voxel_size = [0.15, 0.15, 2]

        self.bev_range = self.voxel_range[:4]
        self.bev_grid = self.voxel_size[:2]
        self.bev_downsample = 1

        self.grid_size = np.array(self.bev_grid) * self.bev_downsample
        self.x_steps = np.arange(self.bev_range[0] + self.grid_size[0] / 2, self.bev_range[1], self.grid_size[0])
        self.y_steps = np.arange(self.bev_range[2] + self.grid_size[1] / 2, self.bev_range[3], self.grid_size[1])

        self.z_range = self.voxel_range[-2:]

        self.depth_range = [2, 64]
        self.depth_step = 0.1

        self.n_classes = 1
        self.img_size = [512, 1408]

        self.model_cfg = Config(
            dict(
                img_backbone=dict(
                    type="ResNet",
                    depth=50,
                    out_indices=[0, 1, 2, 3],
                    frozen_stages=-1,
                    norm_eval=False,
                    init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
                ),
                img_neck=dict(
                    type="SECONDFPN",
                    in_channels=[256, 512, 1024, 2048],
                    out_channels=[128, 128, 128, 128],
                    upsample_strides=[0.25, 0.5, 1, 2],
                ),
                view_transformer=dict(
                    type="LSS",
                    in_channels=512,
                    out_channels=128,
                    voxel_range=self.voxel_range,
                    voxel_size=self.voxel_size,
                    depth_range=self.depth_range,
                    depth_step=self.depth_step,
                ),
                bev_backbone=dict(
                    type="BasicLayers",
                    in_channels=128,
                    channels=[128, 128, 128, 128],
                    num_blocks=[2, 2, 2, 2],
                    strides=[2, 2, 2, 2],
                ),
                bev_neck=dict(
                    type="SECONDFPN",
                    in_channels=[128, 128, 128, 128, 128],
                    out_channels=[64, 64, 64, 64, 64],
                    upsample_strides=[1, 2, 4, 8, 16],
                ),
                lanedet_head=dict(
                    type="RowAnchorHead",
                    n_instances=16,
                    n_classes=self.n_classes,
                    in_channels=320,
                    share_conv_channel=64,
                    head_conv=64,
                    grid_size=self.grid_size,
                    x_steps=self.x_steps,
                    y_steps=self.y_steps,
                    z_range=self.z_range,
                    post_process=dict(
                        score_thresh=0.5,
                        pad_end=True,
                    ),
                ),
            )
        )

    def _configure_model(self):
        m = BEVLaneFCN(self.model_cfg)
        if dist.get_rank() == 0:
            summary(m)
        return m

    def training_step(self, batch):
        for k in batch:
            if k in ["img", "frustum_coord"]:
                batch[k] = batch[k].cuda()
            elif k in ["targets"]:
                batch[k] = [{k: v.cuda() for k, v in tgt.items()} for tgt in batch[k]]
        loss_dict = self.model(batch)
        total_loss = sum(loss_dict.values())
        return total_loss, loss_dict

    def test_step(self, batch):
        for k in batch:
            if k in ["img", "frustum_coord"]:
                batch[k] = batch[k].cuda()
        output = self.model(batch)
        output = [dict(nid=i, bev_lanes=o["lanes"], lane_scores=o["scores"]) for i, o in zip(batch["nid"], output)]
        return output

    def test_epoch_end(self, output, output_dir):
        dist.synchronize()
        output = dist.all_gather_object(output)
        output = sum(map(list, zip(*output)), [])[: len(self.val_dataloader.dataset)]
        if dist.get_rank() == 0:
            item = self.val_dataloader.dataset[0]
            ref2cam = item["ref2cam"]
            cam2ego = np.linalg.inv(item["ego2cam"])
            for batch in output:
                lanes = []
                for pts in batch["bev_lanes"]:
                    pts = np.hstack([pts, np.ones_like(pts[:, 2:3])])
                    pts = pts @ ref2cam.T @ cam2ego.T
                    lanes.append(pts[:, :3])
                batch["bev_lanes"] = lanes
            evaluation_save_dir = os.path.join(output_dir, "evaluations")
            if not os.path.exists(evaluation_save_dir):
                os.makedirs(evaluation_save_dir)
            with open(os.path.join(evaluation_save_dir, "results.pkl"), "wb") as f:
                pickle.dump(output, f)

    def _configure_train_transforms(self):
        transform = transforms.Compose(
            [
                BackupRawData(backup_keys=["img", "lanes"]),
                AppendAffineMatrix(dims=[2, 3]),
                RandomHorizontalFlip(hflip_prob=0.5),
                RandomRotate(rotate_range=[-5.4, 5.4]),
                RandomScale(scale_range=[0.31, 0.48]),
                Crop(target_size=self.img_size, hmode="random", vmode="bottom"),
                NormalizeImage(mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True),
                AppendPixelCoord(downsample=16),
                AppendFrustumCoord(depth_range=self.depth_range, depth_step=self.depth_step),
                FilterLanesRV(),
                FilterLanesBEV(gt_range=self.bev_range),
                PreprocessRowAnchor(
                    grid_size=self.grid_size,
                    x_steps=self.x_steps,
                    y_steps=self.y_steps,
                    z_range=self.z_range,
                    n_classes=self.n_classes,
                ),
                Bundle(),
                Collect(["img", "frustum_coord", "targets"]),
            ]
        )
        return transform

    def _configure_val_transforms(self):
        transform = transforms.Compose(
            [
                BackupRawData(backup_keys=["img"]),
                AppendAffineMatrix(dims=[2, 3]),
                RandomScale(fixed_scale=0.367),
                Crop(target_size=self.img_size, hmode="center", vmode="bottom"),
                NormalizeImage(mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True),
                AppendPixelCoord(downsample=16),
                AppendFrustumCoord(depth_range=self.depth_range, depth_step=self.depth_step),
                Bundle(),
                Collect(["nid", "img", "frustum_coord", "ego2cam", "ref2cam"]),
            ]
        )
        return transform

    def _configure_collate_fn(self):
        def collate_fn(batch):
            keys = batch[0].keys()
            batch_dict = {}
            for k in keys:
                if k == "targets":
                    batch_dict[k] = [b[k] for b in batch]
                else:
                    batch_dict[k] = default_collate([b[k] for b in batch])
            return batch_dict

        return collate_fn

    def _configure_train_dataloader(self):
        train_set = BEVLaneFCNDataset(
            transforms=self._configure_train_transforms(),
            raw_dataset_cfg=getattr(self, "raw_dataset_cfg", {}),
        )
        train_loader = torch.utils.data.DataLoader(
            train_set,
            batch_size=self.batch_size_per_device,
            shuffle=not dist.is_distributed(),
            sampler=InfiniteSampler(len(train_set)) if dist.is_distributed() else None,
            num_workers=self.num_workers,
            pin_memory=True,
            drop_last=True,
            collate_fn=self._configure_collate_fn(),
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_set = BEVLaneFCNDataset(
            data_split="validation",
            transforms=self._configure_val_transforms(),
            raw_dataset_cfg=getattr(self, "raw_dataset_cfg", {}),
        )
        val_loader = torch.utils.data.DataLoader(
            val_set,
            batch_size=self.batch_size_per_device,
            sampler=DistributedSampler(val_set, shuffle=False) if dist.is_distributed() else None,
            num_workers=self.num_workers,
            collate_fn=self._configure_collate_fn(),
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        lr = self.basic_lr_per_img * self.batch_size_per_device * self.total_devices
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=self.weight_decay)
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            self.optimizer,
            self.basic_lr_per_img * self.batch_size_per_device * self.total_devices,
            len(self.train_dataloader),
            self.max_epoch,
        )
        return scheduler


if __name__ == "__main__":
    cv2.setNumThreads(0)
    torch.backends.cudnn.benchmark = True
    BaseCli(Exp).run()
