"""
-> train:
    python3 perceptron/exps/lane3d/bev_120x200_rvseg_upcrop_colorjitter_1decoder_depthbinx2.py -d 0-7 -b 2 -e 15 --sync_bn 8 --no-clearml
-> test:
    python3 perceptron/exps/lane3d/bev_120x200_rvseg_upcrop_colorjitter_1decoder_depthbinx2.py -d 0-7 --eval --ckpt outputs/.../checkpoint_epoch_xx.pth
-> eval (need gpu-machine):
    python3 perceptron/data/lane3d/metric/eval.py -p outputs/.../evaluations/results.pkl -data car_two

--> 一键化: bash auto_test-eval.sh bev_120x200_rvseg_upcrop_colorjitter_1decoder_depthbinx2 2

13e P@R=80@62.5
"""

from perceptron.engine.cli.base_cli import BaseCli
from perceptron.exps.lane3d.bevcond3dlane_r50_896x512_idabda_exp import ExpConfig
from perceptron.exps.lane3d.bevcond3dlane_r50_896x512_idabda_exp import Exp as BaseExp


ida_aug_conf = {
    "final_dim": ExpConfig.INPUT_SIZE[::-1],  # need h w
    "rot_lim": (-5.5, 5.5),
    "H": ExpConfig.IMG_SHAPE[1],
    "W": ExpConfig.IMG_SHAPE[0],
    "rand_flip": False,
    "color_jitter": True,
    "up_crop": True,
}

ExpConfig.MAP_RESOLUTION = (0.5, 0.15)
ExpConfig.MAP_SIZE = (120, 200)
ExpConfig.dataset_setup["dataset_name"] = "car_two_v2"  # 适配新格式数据
ExpConfig.dataset_setup["view_key_list"] = ("bev", "rv")
ExpConfig.dataset_setup["ida_aug_conf"] = ida_aug_conf  #
ExpConfig.dataset_setup["mask_conf"] = dict(
    bev=dict(map_range=ExpConfig.MAP_REGION, map_resolution=ExpConfig.MAP_RESOLUTION, dilate_ratio=-1),
    rv=dict(dilate_ratio=3, down_scale=4),
)  #


ExpConfig.model_setup["rvseg_head"] = dict(feat_upsample=4, feat_dim=512, category_num=2)
ExpConfig.model_setup["bev_encoder"]["res_conf"] = dict(
    num_blocks=[1, 1, 1, 2],
    in_channels=512,
    base_channel=64,
    strides=[1, 1, 1, 1],
)
ExpConfig.model_setup["bev_encoder"]["lss_conf"]["grid_conf"]["ybound"] = [-15.0, 15.0, 0.15]
ExpConfig.model_setup["bev_encoder"]["lss_conf"]["grid_conf"]["dbound"] = [4.0, 60.0, 0.5]
ExpConfig.model_setup["output_head"]["loss_weights"] = dict(
    obj_weight=10.0, cls_weight=1.0, loc_weight=1.0, reg_weight=1.0, rng_weight=20.0, rv_seg_weight=10.0
)
ExpConfig.model_setup["output_head"]["target_shape"] = ExpConfig.MAP_SIZE
ExpConfig.model_setup["output_head"]["use_offset"] = False
ExpConfig.model_setup["output_head"]["map_resolution"] = ExpConfig.MAP_RESOLUTION
ExpConfig.scheduler_setup = dict(
    milestones=[0.6, 0.8],
    gamma=0.1,
)
ExpConfig.model_setup["bev_decoder"]["num_decoder_layers"] = 1


class Exp(BaseExp):
    def __init__(self, *args, **kwargs):
        super(Exp, self).__init__(*args, **kwargs)
        self.data_loader_workers = 4  # 至少需要300G内存


if __name__ == "__main__":
    BaseCli(Exp).run()
