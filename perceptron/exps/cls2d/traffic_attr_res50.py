# encoding: utf-8
"""
//<! The Train Prj of Traffic Lights Attrs
@author: <PERSON><PERSON><PERSON>&<PERSON>
@contact: peng<PERSON><PERSON><PERSON><PERSON>@megvii.com

Train:
python3 perceptron/exps/cls2d/traffic_attr_res50.py -d 0-7 -te --sync_bn 8 --no-clearml --find_unused_parameters
Test:
python3 perceptron/exps/cls2d/traffic_attr_res50.py -d 0-7 --eval --ckpt outputdir

| color metric   |    fpr |   precision |   recall |
|----------------|--------|-------------|----------|
| red            | 0.0073 |      0.9956 |   0.9413 |
| yellow         | 0.0004 |      0.974  |   0.9444 |
| green          | 0.001  |      0.9977 |   0.9722 |
| others         | 0.0478 |      0.4092 |   0.9137 |

| type metric       |    fpr |   precision |   recall |
|-------------------|--------|-------------|----------|
| circle            | 0.0397 |      0.9459 |   0.9189 |
| turnleft          | 0.0297 |      0.9469 |   0.8789 |
| turnright         | 0.0001 |      0      | nan      |
| gostraight        | 0      |    nan      | nan      |
| turnaround        | 0.0014 |      0      | nan      |
| turnleftandaround | 0      |    nan      | nan      |
| bicycle           | 0.0006 |      0      | nan      |
| sidewalk          | 0.0335 |      0.7582 |   0.8127 |
| countdown         | 0      |    nan      |   0      |
| others            | 0.0807 |      0.3094 |   0.4268 |
"""

from typing import Any, List, Type, Union

import numpy as np
import torch
import torch.nn as nn
from cvpack2.modeling.losses.focal_loss import sigmoid_focal_loss
from data3d.datasets.traffic_lights_attr import Traffic_Lights_Attr
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import BaseCli
from perceptron.engine.executors import TrafficCls2DEvaluator
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler
from perceptron.utils import torch_dist as dist
from perceptron.utils.traffic_attr_utils.evaluate import TrafficAttrEvaluator
from torch import Tensor
from torch.optim import SGD
from torchvision.models.resnet import BasicBlock, Bottleneck, ResNet, model_urls

try:
    from torch.hub import load_state_dict_from_url
except ImportError:
    from torch.utils.model_zoo import load_url as load_state_dict_from_url


def trivial_batch_collator(batch):
    """
    A batch collator that does nothing.
    """
    return tuple(zip(*batch))


def label_mapping(labels, num_classes):
    assign_label = [[0.0] * num_classes for _ in labels]
    for i in range(len(labels)):
        assign_label[i][int(labels[i])] = 1.0
    return torch.tensor(assign_label).float().cuda()


class TorchvisionResNet(ResNet):
    def __init__(
        self,
        block: Type[Union[BasicBlock, Bottleneck]],
        layers: List[int],
        **kwargs,
    ) -> None:
        super(TorchvisionResNet, self).__init__(block, layers)
        if kwargs:
            num_type = len(kwargs["type_class"])
            num_color = len(kwargs["color_class"])
        self.fc_type = nn.Linear(512 * block.expansion, num_type)
        self.fc_color = nn.Linear(512 * block.expansion, num_color)
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def _forward_impl(self, x: Tensor) -> Tensor:
        # See note [TorchScript super()]
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        logits_type = self.fc_type(x)
        logits_color = self.fc_color(x)

        return logits_type, logits_color


def resnet(
    arch: str,
    block: Type[Union[BasicBlock, Bottleneck]],
    layers: List[int],
    pretrained: bool,
    progress: bool,
    **kwargs: Any,
) -> TorchvisionResNet:
    model = TorchvisionResNet(block, layers, **kwargs)
    if pretrained:
        state_dict = load_state_dict_from_url(model_urls[arch], progress=progress)
        model.load_state_dict(state_dict, strict=False)
    return model


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=256, total_devices=8, max_epoch=5, pretrained_backbone=True, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 1
        self.basic_lr_per_img = 3e-2
        self.num_keep_latest_ckpt = max_epoch
        self.ckpt_oss_save_dir = ""
        # inputs
        self.type_class = [
            "circle",
            "turnleft",
            "turnright",
            "gostraight",
            "turnaround",
            "turnleftandaround",
            "bicycle",
            "sidewalk",
            "countdown",
            "others",
        ]
        self.color_class = ["red", "yellow", "green", "others"]
        self.type_num = len(self.type_class)
        self.color_num = len(self.color_class)
        # eval
        self.val_list = {
            "gshl_02": "s3://perceptor-share/traffic-light-datasets/gshl_02/annotation_inserted_cleared.json",
            "gshl_04_30": "s3://perceptor-share/traffic-light-datasets/gshl_04/attr/30_degree_off2others.json",
            "gshl_04_60": "s3://perceptor-share/traffic-light-datasets/gshl_04/attr/60_degree_off2others.json",
            "gshl_04_120": "s3://perceptor-share/traffic-light-datasets/gshl_04/attr/120_degree_off2others.json",
        }
        self.eval_executor_class = TrafficCls2DEvaluator
        self.evaluator = TrafficAttrEvaluator(
            distributed=dist.is_distributed(),
            type_class=self.type_class,
            color_class=self.color_class,
            val_list=self.val_list,
        )
        self.area_thresh = 100
        self.image_size = [64, 64]
        self.iters_per_epoch = 102400

    def _configure_model(self):
        kwargs = {
            "type_class": self.type_class,
            "color_class": self.color_class,
        }
        model = resnet("resnet50", Bottleneck, [3, 4, 6, 3], pretrained=True, progress=True, **kwargs)
        return model

    def _configure_train_dataloader(self):
        train_set = Traffic_Lights_Attr(
            pipe_name="test_traffic_light_attr",
            mode="train",
            area_thresh=self.area_thresh,
            image_size=self.image_size,
            val_list=self.val_list,
            iters_per_epoch=self.iters_per_epoch,
        )
        if dist.is_distributed():
            sampler = InfiniteSampler(len(train_set), seed=self.seed if self.seed else 0)
        else:
            sampler = None
        train_loader = torch.utils.data.DataLoader(
            train_set,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=1,
            shuffle=sampler is None,
            drop_last=True,
            sampler=sampler,
        )
        return train_loader

    def _configure_val_dataloader(self):
        eval_set = Traffic_Lights_Attr(
            pipe_name="test_traffic_light_attr",
            mode="val",
            area_thresh=self.area_thresh,
            image_size=self.image_size,
            val_list=self.val_list,
            iters_per_epoch=self.iters_per_epoch,
        )
        val_loader = torch.utils.data.DataLoader(
            eval_set,
            batch_size=16,
            shuffle=False,
            num_workers=1,
            pin_memory=True,
            drop_last=False,
            collate_fn=trivial_batch_collator,
            sampler=torch.utils.data.distributed.DistributedSampler(eval_set) if dist.is_distributed() else None,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        params = [p for p in self.model.parameters() if p.requires_grad]
        optimizer = SGD(
            params,
            lr=self.basic_lr_per_img,
            momentum=0.9,
            weight_decay=1e-4,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            self.optimizer,
            self.basic_lr_per_img,
            len(self.train_dataloader),
            self.max_epoch,
        )
        return scheduler

    def training_step(self, batch):
        self.model.train()
        images, shape_labels, color_labels = batch
        images = torch.from_numpy(np.asarray(images, dtype=np.float32))
        images = images.cuda()
        shape_score, color_score = self.model(images)
        shape_loss = sigmoid_focal_loss(
            shape_score, label_mapping(shape_labels, self.type_num), alpha=-1, gamma=2, reduction="mean"
        )
        color_loss = sigmoid_focal_loss(
            color_score, label_mapping(color_labels, self.color_num), alpha=-1, gamma=2, reduction="mean"
        )
        train_loss_dict = shape_loss + color_loss
        return train_loss_dict

    def test_step(self, batch):
        dataset, images, shape_labels, color_labels = batch
        images = torch.from_numpy(np.asarray(images, dtype=np.float32))
        shape_labels = torch.from_numpy(np.asarray(shape_labels, dtype=np.int64))
        color_labels = torch.from_numpy(np.asarray(color_labels, dtype=np.int64))
        images = images.cuda()
        shape_score, color_score = self.model(images)
        return dataset, shape_score.cpu(), color_score.cpu(), shape_labels, color_labels


if __name__ == "__main__":
    BaseCli(Exp).run()
