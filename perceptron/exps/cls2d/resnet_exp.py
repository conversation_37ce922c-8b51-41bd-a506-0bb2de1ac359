# encoding: utf-8
"""
@author: zeming li
@contact: <EMAIL>
"""

import torch
import torch.nn.functional as F
import torchvision.models as models
import torchvision.transforms as transforms
from PIL import Image
from torch.optim import SGD

from data3d.datasets.imagenet import ImageNet
from perceptron.exps.base_exp import BaseExp
from perceptron.utils import torch_dist as dist
from perceptron.engine.cli import BaseCli
from perceptron.engine.executors.evaluators import Cls2DEvaluator


class ToRGB:
    def __call__(self, x):
        return x.convert("RGB")


def _accuracy(output, target, topk=(1,)):
    maxk = max(topk)
    batch_size = target.size(0)

    _, pred = output.topk(maxk, 1, True, True)
    pred = pred.t()
    correct = pred.eq(target.reshape(1, -1).expand_as(pred))

    res = []
    for k in topk:
        correct_k = correct[:k].reshape(-1).float().sum(0)
        res.append(correct_k.mul_(100.0 / batch_size))
    return res


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.basic_lr_per_img = 0.1 / 256.0
        self.eval_executor_class = Cls2DEvaluator

    def _configure_model(self):
        model = models.__dict__["resnet18"](pretrained=False)
        return model

    def training_step(self, batch):
        images, target = batch
        images = images.cuda()
        target = target.cuda()
        logits = self.model(images)
        loss = F.cross_entropy(logits, target, reduction="mean")
        return loss

    def test_step(self, batch):
        images, target = batch
        images = images.cuda()
        target = target.cuda()

        logits = self.model(images)
        acc1, acc5 = _accuracy(logits, target, (1, 5))
        one_step_res = (dist.reduce_mean(acc1), dist.reduce_mean(acc5))
        return one_step_res

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        transform = transforms.Compose(
            [
                ToRGB(),
                transforms.RandomResizedCrop(224, interpolation=Image.BILINEAR),
                transforms.RandomHorizontalFlip(),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ]
        )

        train_set = ImageNet(train=True, transform=transform)
        train_dataloader_kwargs = {
            "num_workers": 6,
            "pin_memory": False,
            "batch_size": self.batch_size_per_device,
            "shuffle": False,
            "drop_last": True,
            "sampler": InfiniteSampler(len(train_set), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
        }
        train_loader = torch.utils.data.DataLoader(train_set, **train_dataloader_kwargs)

        return train_loader

    def _configure_val_dataloader(self):
        transform = transforms.Compose(
            [
                ToRGB(),
                transforms.Resize(256, interpolation=Image.BILINEAR),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ]
        )
        eval_set = ImageNet(train=False, transform=transform)
        val_loader = torch.utils.data.DataLoader(
            eval_set,
            batch_size=100,
            shuffle=False,
            num_workers=2,
            pin_memory=False,
            drop_last=False,
            sampler=torch.utils.data.distributed.DistributedSampler(eval_set) if dist.is_distributed() else None,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        lr = self.basic_lr_per_img * self.batch_size_per_device * self.total_devices
        optimizer = SGD(
            self.model.parameters(),
            lr=lr,
            momentum=0.9,
            weight_decay=1e-4,
            nesterov=False,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        # scheduler = StepLRScheduler(
        #     self.optimizer,
        #     self.basic_lr_per_img * self.batch_size_per_device * self.total_devices,
        #     len(self.train_dataloader),
        #     self.max_epoch,
        #     milestones=[30, 60, 90],
        # )
        from perceptron.layers.lr_scheduler import TorchLRSchedulerWraper

        torch_lr_scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=30, gamma=0.1)
        scheduler = TorchLRSchedulerWraper(torch_lr_scheduler, len(self.train_dataloader), self.max_epoch)
        return scheduler


if __name__ == "__main__":
    BaseCli(Exp).run()
