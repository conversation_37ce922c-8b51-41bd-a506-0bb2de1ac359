"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/det3d/det3d_center_point_exp_better_iou_fcos_nuscenes.py -d 0-7 -b 1 -e 20 --sync_bn 3 --no-clearml

mAP: 0.4336
mATE: 0.3198
mASE: 0.2581
mAOE: 0.6248
mAVE: 1.0000
mAAE: 1.0000
NDS: 0.3965
Eval time: 122.8s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.717   0.206   0.151   0.309   1.000   1.000
truck   0.407   0.328   0.180   0.220   1.000   1.000
bus     0.547   0.328   0.167   0.148   1.000   1.000
trailer 0.213   0.540   0.226   0.936   1.000   1.000
construction_vehicle    0.054   0.763   0.453   1.223   1.000   1.000
pedestrian      0.709   0.144   0.283   1.411   1.000   1.000
motorcycle      0.417   0.210   0.232   0.544   1.000   1.000
bicycle 0.234   0.221   0.249   0.718   1.000   1.000
traffic_cone    0.466   0.168   0.372   nan     nan     nan
barrier 0.572   0.292   0.268   0.113   nan     nan
"""


import easydict
import numpy as np

import torch

from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.det3d.dataset.nuscenes.dataset import NuScenesDatasetWithEval
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import CenterPoints as BaseFCos
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import ModelConfigs as BaseConfigs
from perceptron.engine.cli import Det3DCli
from perceptron.layers.blocks_3d.det3d import VoxelResBackBone8x
from perceptron.layers.head.det3d import CenterHeadIouAware, FCOSAssigner, IouAwareGenProposals

from torch.utils.data import DataLoader, DistributedSampler
from perceptron.layers.losses.det3d import CenterNetRegLoss, FocalLoss


class DataConfigs:
    point_cloud_range = [-51.2, -51.2, -5.0, 51.2, 51.2, 3.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 5
    max_voxels = 40000
    src_num_point_features = 4
    use_num_point_features = 4


class ModelConfigs(BaseConfigs):
    class_name = [
        "car",
        "truck",
        "construction_vehicle",
        "bus",
        "trailer",
        "barrier",
        "motorcycle",
        "bicycle",
        "pedestrian",
        "traffic_cone",
    ]
    densehead_tasks = [
        easydict.EasyDict(num_class=1, class_names=["car"]),
        easydict.EasyDict(num_class=2, class_names=["truck", "construction_vehicle"]),
        easydict.EasyDict(num_class=2, class_names=["bus", "trailer"]),
        easydict.EasyDict(num_class=1, class_names=["barrier"]),
        easydict.EasyDict(num_class=2, class_names=["motorcycle", "bicycle"]),
        easydict.EasyDict(num_class=2, class_names=["pedestrian", "traffic_cone"]),
    ]

    target_assigner_mapping = {
        "car": 1,
        "truck": 2,
        "construction_vehicle": 3,
        "bus": 4,
        "trailer": 5,
        "barrier": 6,
        "motorcycle": 7,
        "bicycle": 8,
        "pedestrian": 9,
        "traffic_cone": 10,
    }

    densehead_common_heads = easydict.EasyDict(
        {
            "iou": [1, 2],
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
        }
    )
    densehead_loss_iou_weight = 5.0
    proposal_iou_aware_list = [0.65] * len(class_name)

    # for nms test
    nms_pre_max_size_test = 2000

    # proposal config
    proposal_post_center_limit_range = [-51.2, -51.2, -5.0, 51.2, 51.2, 3.0]
    proposal_pc_range = [-51.2, -51.2]


class FCosIouAware(BaseFCos):
    def __init__(self, model_config, data_config):
        super().__init__(model_config=model_config, data_config=data_config)
        self.voxelizer = self.build_voxelizer()
        self.backbone_3d = self.build_backbone_3d()
        self.dense_head = self.build_dense_head()

    def build_backbone_3d(self):
        return VoxelResBackBone8x(
            input_channels=self.vfe.get_output_feature_dim(),
            grid_size=self.data_config.grid_size,
            last_pad=0,
        )

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
            max_num_points=self.data_config.max_num_points,
            max_voxels=self.data_config.max_voxels,
            num_point_features=self.data_config.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_dense_head(self):
        target_assigner = FCOSAssigner(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )

        proposal_layer = IouAwareGenProposals(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            iou_aware_list=self.model_config.proposal_iou_aware_list,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
        )

        dense_head_module = CenterHeadIouAware(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            out_size_factor=self.model_config.densehead_out_size_factor,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            iou_weight=self.model_config.densehead_loss_iou_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit", FocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma)
            )
            m.add_module("crit_reg", CenterNetRegLoss())
            m.add_module("crit_iou_aware", CenterNetRegLoss())

        _build_losses(dense_head_module)
        return dense_head_module


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=20, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 1

    def _configure_model(self):
        model = FCosIouAware(
            model_config=ModelConfigs(),
            data_config=DataConfigs(),
        )
        return model

    def _configure_train_dataloader(self):
        dataset = NuScenesDatasetWithEval(DataConfigs(), ModelConfigs().class_name, training=True)
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = NuScenesDatasetWithEval(DataConfigs(), ModelConfigs().class_name, training=False)
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
