# once 结果
# |AP@50       |overall     |0-30m       |30-50m      |50m-inf     |
# |Vehicle     |78.61       |88.29       |74.68       |62.38       |
# |<PERSON><PERSON><PERSON><PERSON>  |52.36       |61.10       |43.71       |22.34       |
# |Cyclist     |73.89       |83.79       |69.01       |52.88       |
# |mAP         |68.29       |77.72       |62.47       |45.86       |


from functools import partial

import easydict
import numpy as np
import torch
import torch.optim as optim

from perceptron.data.det3d.preprocess.voxelization import Voxelization
from perceptron.data.det3d.dataset.once.dataset import OnceDatasetWithEval
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.blocks_2d.det3d import BaseBEVBackbone, HeightCompression
from perceptron.layers.blocks_3d.det3d import MeanVFE, VoxelResBackBone8x
from perceptron.layers.head.det3d import CenterHead, CenterPointGenProposals, FCOSAssigner
from perceptron.layers.losses.det3d import CenterNetRegLoss, <PERSON>ocalLoss
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.models.det3d import load_data_to_gpu
from perceptron.models.det3d.base_3d_detector import Base3DDetector
from perceptron.engine.cli import Det3DCli
from torch.utils.data import DataLoader, DistributedSampler


class DataConfigs:
    point_cloud_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 5
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4


class ModelConfigs:
    class_name = ("Car", "Bus", "Truck", "Pedestrian", "Cyclist")

    # map_to_bev
    map_to_bev_num_features = 256

    # backbone2d
    backbone2d_layer_nums = [5, 5]
    backbone2d_layer_strides = [1, 2]
    backbone2d_num_filters = [128, 256]
    backbone2d_upsample_strides = [1, 2]
    backbone2d_num_upsample_filters = [256, 256]
    backbone2d_use_scconv = True
    backbone2d_upsample_output = True  # False

    # dense head
    densehead_dataset_name = "once"
    densehead_mode = "3d"
    densehead_tasks = [
        easydict.EasyDict({"num_class": 4, "class_names": ["Car", "Bus", "Truck", "Pedestrian"]}),
        easydict.EasyDict({"num_class": 1, "class_names": ["Cyclist"]}),
    ]

    densehead_share_conv_channel = 64
    densehead_init_bias = -2.19
    densehead_common_heads = easydict.EasyDict(
        {
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
        }
    )
    densehead_loss_code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    densehead_loss_loc_weight = 0.25
    densehead_out_size_factor = 4  # 8
    densehead_upsample_for_pedestrian = False

    # target assigner
    target_assigner_dense_reg = 1
    target_assigner_gaussian_overlap = 0.1
    target_assigner_max_objs = 1500
    target_assigner_min_radius = 2
    target_assigner_mapping = easydict.EasyDict(
        {
            "Car": 1,
            "Bus": 2,
            "Truck": 3,
            "Pedestrian": 4,
            "Cyclist": 5,
        }
    )
    target_assigner_topk = 9
    target_assigner_alpha = 0.25
    target_assigner_gamma = 2
    target_assigner_no_log = False

    # proposal config
    proposal_post_center_limit_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
    proposal_max_per_img = 500
    proposal_score_threshold = 0.1
    proposal_pc_range = [-75.2, -75.2]
    proposal_voxel_size = [0.1, 0.1]

    # for nms train
    nms_iou_threshold_train = 0.8
    nms_pre_max_size_train = 1500
    nms_post_max_size_train = 80

    # for nms test
    nms_iou_threshold_test = 0.1
    nms_pre_max_size_test = 1500
    nms_post_max_size_test = 100

    # post process
    post_process_recall_thresh_list = [0.3, 0.5, 0.7]
    post_process_score_thresh = 0.1
    post_process_output_raw_score = False
    post_process_eval_metric = "once"

    post_process_multi_classes_nms = False
    post_process_nms_type = "nms_gpu"
    post_process_nms_thresh = 0.01
    post_process_nms_pre_maxsize = 4096
    post_process_nms_post_maxsize = 500


class CenterPoints(Base3DDetector):
    def __init__(self, model_config, data_config):
        super().__init__(num_class=len(model_config.class_name), class_names=model_config.class_name)
        self.model_config = model_config
        self.data_config = data_config

        self.voxelizer = self.build_voxelizer()
        self.vfe = self.build_vfe()
        self.backbone_3d = self.build_backbone_3d()
        self.map_to_bev = self.build_map_to_bev()
        self.backbone_2d = self.build_backbone_2d()
        self.dense_head = self.build_dense_head()

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    def build_voxelizer(self):
        return Voxelization(
            voxel_size=self.data_config.voxel_size,
            point_cloud_range=self.data_config.point_cloud_range,
            max_num_points=self.data_config.max_num_points,
            max_voxels=self.data_config.max_voxels,
            num_point_features=self.data_config.src_num_point_features,
            device=torch.device("cuda"),
        )

    def build_vfe(self):
        vfe = MeanVFE(
            num_point_features=self.data_config.use_num_point_features,
        )
        return vfe

    def build_backbone_3d(self):
        return VoxelResBackBone8x(
            input_channels=self.vfe.get_output_feature_dim(),
            grid_size=self.data_config.grid_size,
            last_pad=0,
        )

    def build_map_to_bev(self):
        return HeightCompression(num_bev_features=self.model_config.map_to_bev_num_features)

    def build_backbone_2d(self):
        backbone_2d_module = BaseBEVBackbone(
            layer_nums=self.model_config.backbone2d_layer_nums,
            layer_strides=self.model_config.backbone2d_layer_strides,
            num_filters=self.model_config.backbone2d_num_filters,
            upsample_strides=self.model_config.backbone2d_upsample_strides,
            num_upsample_filters=self.model_config.backbone2d_num_upsample_filters,
            input_channels=self.map_to_bev.num_bev_features,
            use_scconv=self.model_config.backbone2d_use_scconv,
            upsample_output=self.model_config.backbone2d_upsample_output,
        )
        return backbone_2d_module

    def build_dense_head(self):
        target_assigner = FCOSAssigner(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )
        proposal_layer = CenterPointGenProposals(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
        )

        dense_head_module = CenterHead(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit", FocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma)
            )
            m.add_module("crit_reg", CenterNetRegLoss())
            return

        _build_losses(dense_head_module)

        return dense_head_module

    def forward(self, batch_dict):
        points = batch_dict["points"]
        gt_boxes = batch_dict.get("gt_boxes", None)
        batch_size = batch_dict["batch_size"]

        voxels, voxel_coords, voxel_num_points = self.voxelizer(points)
        voxel_features = self.vfe(voxels, voxel_num_points)
        encoded_spconv_tensor, encoded_spconv_tensor_stride, multi_scale_3d_features = self.backbone_3d(
            voxel_features, voxel_coords, batch_size
        )
        spatial_features, encoded_spconv_tensor_stride = self.map_to_bev(
            encoded_spconv_tensor, encoded_spconv_tensor_stride
        )
        spatial_features_2d, pyramid = self.backbone_2d(spatial_features)
        forward_ret_dict = self.dense_head(spatial_features_2d, gt_boxes)

        if self.training:
            loss_rpn, tb_dict = self.dense_head.get_loss(forward_ret_dict)
            tb_dict.update({"loss_rpn": loss_rpn.item()})
            ret_dict = {"loss": loss_rpn}
            return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        self.lr = 0.003
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

    def _configure_model(self):
        model = CenterPoints(
            model_config=ModelConfigs(),
            data_config=DataConfigs(),
        )
        return model

    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict, _, _ = self.model(batch)
        loss = ret_dict["loss"].mean()
        return loss

    def test_step(self, batch):
        load_data_to_gpu(batch)
        return self.model(batch)

    def _configure_train_dataloader(self):
        dataset = OnceDatasetWithEval(DataConfigs(), ModelConfigs().class_name, training=True)
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = OnceDatasetWithEval(DataConfigs(), ModelConfigs().class_name, training=False)
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        def children(m: torch.nn.Module):
            return list(m.children())

        def num_children(m: torch.nn.Module) -> int:
            return len(children(m))

        def flatten_model(m):
            return sum(map(flatten_model, m.children()), []) if num_children(m) else [m]  # noqa

        def get_layer_groups(m):
            return [torch.nn.Sequential(*flatten_model(m))]  # noqa

        optimizer_func = partial(optim.Adam, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func, self.lr, get_layer_groups(self.model), wd=0.01, true_wd=True, bn_wd=True
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
