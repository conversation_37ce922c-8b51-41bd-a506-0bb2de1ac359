# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/det3d/release/centerpoint/det3d_centerpoint_iou_fcos.py -b 1 -e 80 --sync_bn 3 --no-clearml

import easydict
import numpy as np
from torch.utils.data import DataLoader, DistributedSampler

from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.exps.det3d.det3d_center_point_exp_better_iou_fcos import FCosIouAware
from perceptron.exps.det3d.det3d_center_point_exp_better_iou_fcos import IouConfigs
from perceptron.exps.det3d.release.private_dataset_path import (
    TRAIN_BASELINE_DATALIST,
    EVAL_BMK_DATALIST_WITH_LABELED_2D,
)
from perceptron.engine.cli import Det3DCli


class DataConfigs:
    point_cloud_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 5
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4


class IoUPrivateModelConfig(IouConfigs):
    class_name = (
        "car",
        "truck",
        "construction_vehicle",
        "bus",
        "motorcycle",
        "bicycle",
        "tricycle",
        "cyclist",
        "pedestrian",
    )
    densehead_tasks = [
        easydict.EasyDict(
            {
                "num_class": 9,
                "class_names": [
                    "car",
                    "truck",
                    "construction_vehicle",
                    "bus",
                    "motorcycle",
                    "bicycle",
                    "tricycle",
                    "cyclist",
                    "pedestrian",
                ],
            }
        ),
    ]  # , "other", "ghost", "masked_area")
    target_assigner_mapping = easydict.EasyDict(
        {
            "car": 1,
            "truck": 2,
            "construction_vehicle": 3,
            "bus": 4,
            "motorcycle": 5,
            "bicycle": 6,
            "tricycle": 7,
            "cyclist": 8,
            "pedestrian": 9,
        }
    )
    target_assigner_max_objs = 2500


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 5

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=TRAIN_BASELINE_DATALIST,
            class_names=IoUPrivateModelConfig().class_name,
            training=True,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=EVAL_BMK_DATALIST_WITH_LABELED_2D,
            class_names=IoUPrivateModelConfig().class_name,
            training=False,
            # 为 True的时候，需要搭配使用有 2d 遮挡属性标注的bmk EVAL_BMK_DATALIST_WITH_LABELED_2D
            use_occluded=False,
            # NOTE: use_occluded = True 需要设置 img_key_list，判断在该 img_key_list 下 object 是否可见。
            # img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=0,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_model(self):
        model = FCosIouAware(model_config=IoUPrivateModelConfig(), data_config=DataConfigs())
        return model


if __name__ == "__main__":
    Det3DCli(Exp).run()
