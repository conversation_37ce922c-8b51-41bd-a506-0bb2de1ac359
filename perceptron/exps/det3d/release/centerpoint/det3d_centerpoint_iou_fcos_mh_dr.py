# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --preemptible no -- python3 perceptron/exps/det3d/release/det3d_centerpoint_iou_fcos_mh_dr.py -b 1 -e 40 --sync_bn 3 --pretrained_model [pretrained_model_path] --no-clearml
import easydict
from torch.utils.data import DataLoader, DistributedSampler
from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import Det3DCli

from perceptron.layers.head.det3d import IouAwareGenProposals, CenterHeadIouAwareDRMask, FCOSAssignerMaskReg
from perceptron.layers.losses.det3d import WeightedFocalLoss, WeightFocalLossWHDR, CenterNetRegLoss
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.exps.det3d.det3d_center_point_exp_better_iou_fcos import FCosIouAware as BaseFcos
from perceptron.exps.det3d.det3d_center_point_exp_better_iou_fcos import IouConfigs
from perceptron.exps.det3d.det3d_center_point_exp_better_iou_fcos import DataConfigs
from perceptron.exps.det3d.release.private_dataset_path import TRAIN_CAR2_DATALIST, EVAL_CAR2_BMK_CHENGQU


class IoUPrivateModelConfig(IouConfigs):
    class_name = (
        "car",
        "truck",
        "construction_vehicle",
        "bus",
        "motorcycle",
        "bicycle",
        "tricycle",
        "cyclist",
        "pedestrian",
        "masked_area",
    )

    # dense head
    densehead_tasks = [
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "car",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 3,
                "class_names": [
                    "truck",
                    "construction_vehicle",
                    "bus",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 3,
                "class_names": [
                    "motorcycle",
                    "bicycle",
                    "tricycle",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "cyclist",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "pedestrian",
                ],
            }
        ),
    ]

    # target assigner
    target_assigner_mapping = easydict.EasyDict(
        {
            "car": 1,
            "truck": 2,
            "construction_vehicle": 3,
            "bus": 4,
            "motorcycle": 5,
            "bicycle": 6,
            "tricycle": 7,
            "cyclist": 8,
            "pedestrian": 9,
            "masked_area": 10,
        }
    )
    target_assigner_max_objs = 2500
    proposal_iou_aware_list = [0.65] * len(densehead_tasks)


class FCosIouAware(BaseFcos):
    def __init__(self, model_config, data_config):
        super().__init__(model_config=model_config, data_config=data_config)
        self.dense_head = self.build_dense_head()

    def build_dense_head(self):
        target_assigner = FCOSAssignerMaskReg(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )

        proposal_layer = IouAwareGenProposals(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            iou_aware_list=self.model_config.proposal_iou_aware_list,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
        )

        dense_head_module = CenterHeadIouAwareDRMask(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            out_size_factor=self.model_config.densehead_out_size_factor,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            iou_weight=self.model_config.densehead_loss_iou_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                WeightedFocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma),
            )
            m.add_module("crit_dr", WeightFocalLossWHDR())
            m.add_module("crit_iou_aware", CenterNetRegLoss())
            m.add_module("crit_reg", CenterNetRegLoss())

        _build_losses(dense_head_module)
        return dense_head_module


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 5
        self.lr = 0.003 * 0.2

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=TRAIN_CAR2_DATALIST,
            class_names=IoUPrivateModelConfig().class_name,
            training=True,
            lidar_key_list=["fuser_lidar"],
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=EVAL_CAR2_BMK_CHENGQU,
            class_names=IoUPrivateModelConfig().class_name,
            training=False,
            lidar_key_list=["fuser_lidar"],
            # 为 True的时候，需要搭配使用有 2d 遮挡属性标注的bmk EVAL_BMK_DATALIST_WITH_LABELED_2D
            use_occluded=False,
            # NOTE: use_occluded = True 需要设置 img_key_list，判断在该 img_key_list 下 object 是否可见。
            # img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=0,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_model(self):
        model = FCosIouAware(
            model_config=IoUPrivateModelConfig(),
            data_config=DataConfigs(),
        )
        return model


if __name__ == "__main__":
    Det3DCli(Exp).run()
