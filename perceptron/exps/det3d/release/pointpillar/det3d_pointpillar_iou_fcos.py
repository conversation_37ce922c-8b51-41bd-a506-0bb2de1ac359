# pointpillar in private dataset exp
# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/det3d/release/pointpillar/det3d_pointpillar_iou_fcos.py -b 1 -e 80 --sync_bn 3 --no-clearml

import easydict
from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.pointpillar_iou_fcos import Exp as BaseExp
from perceptron.exps.det3d.pointpillar_iou_fcos import PointPillarIouFcos
from perceptron.exps.det3d.pointpillar_iou_fcos import DataConfigs
from perceptron.exps.det3d.pointpillar_iou_fcos import ModelConfigs as BaseConfigs
from perceptron.exps.det3d.release.private_dataset_path import (
    TRAIN_BASELINE_DATALIST,
    EVAL_BMK_DATALIST_WITH_LABELED_2D,
)
from torch.utils.data import DataLoader, DistributedSampler


class ModelConfigs(BaseConfigs):
    class_name = (
        "car",
        "truck",
        "construction_vehicle",
        "bus",
        "motorcycle",
        "bicycle",
        "tricycle",
        "cyclist",
        "pedestrian",
    )

    # dense head
    densehead_tasks = [
        easydict.EasyDict(
            {
                "num_class": 9,
                "class_names": [
                    "car",
                    "truck",
                    "construction_vehicle",
                    "bus",
                    "motorcycle",
                    "bicycle",
                    "tricycle",
                    "cyclist",
                    "pedestrian",
                ],
            }
        ),
    ]  # , "other", "ghost", "masked_area")

    # target assigner
    target_assigner_max_objs = 2500
    target_assigner_mapping = easydict.EasyDict(
        {
            "car": 1,
            "truck": 2,
            "construction_vehicle": 3,
            "bus": 4,
            "motorcycle": 5,
            "bicycle": 6,
            "tricycle": 7,
            "cyclist": 8,
            "pedestrian": 9,
        }
    )


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

    def _configure_model(self):
        model = PointPillarIouFcos(
            model_config=ModelConfigs(),
            data_config=DataConfigs(),
        )
        return model

    def _configure_train_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=TRAIN_BASELINE_DATALIST,
            class_names=ModelConfigs().class_name,
            training=True,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=EVAL_BMK_DATALIST_WITH_LABELED_2D,
            class_names=ModelConfigs().class_name,
            training=False,
            # 为 True的时候，需要搭配使用有 2d 遮挡属性标注的bmk EVAL_BMK_DATALIST_WITH_LABELED_2D
            use_occluded=False,
            # NOTE: use_occluded = True 需要设置 img_key_list，判断在该 img_key_list 下 object 是否可见。
            # img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=0,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
