"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no $NEG_TAGS -- python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamw_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_detr3d_adamw_nuscenes/2021-12-08T21:09:49/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
mAP: 0.2177
mATE: 0.9065
mASE: 0.2864
mAOE: 0.6313
mAVE: 1.0674
mAAE: 0.2495
NDS: 0.3015
Eval time: 116.7s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.392   0.674   0.157   0.128   1.372   0.267
truck   0.154   0.950   0.238   0.220   1.158   0.294
bus     0.182   0.943   0.247   0.200   2.524   0.364
trailer 0.041   1.204   0.266   0.780   0.476   0.080
construction_vehicle    0.017   1.244   0.515   1.419   0.117   0.386
pedestrian      0.302   0.795   0.298   0.829   0.715   0.361
motorcycle      0.188   0.855   0.256   0.861   1.550   0.204
bicycle 0.211   0.771   0.272   1.035   0.627   0.039
traffic_cone    0.372   0.713   0.331   nan     nan     nan
barrier 0.317   0.916   0.285   0.210   nan     nan
"""
import copy
import os

import numpy as np
import refile
import torch
import torch.distributed as dist
import torch.optim as optim
from easydict import EasyDict
from torch.utils.data import DataLoader, DistributedSampler

from data3d.transforms import transforms3d_mmlab
from mmcv.parallel import DataContainer as DC
from mmcv.runner import auto_fp16, force_fp32
from mmdet3d.core import bbox3d2result
from mmdet3d.core.bbox import LiDARInstance3DBoxes, get_box_type
from mmdet3d.datasets.pipelines import (
    Collect3D,
    DefaultFormatBundle3D,
    LoadAnnotations3D,
    MultiScaleFlipAug3D,
    ObjectNameFilter,
    ObjectRangeFilter,
)
from mmdet3d.models.detectors.mvx_two_stage import MVXTwoStageDetector
from mmdet.datasets.pipelines import Compose
from mmdet.models import DETECTORS
from perceptron.data.det3d.dataset.utils.grid_mask import GridMask
from perceptron.data.det3d.dataset.nuscenes.dataset import NuScenesMonoDataset, NuScenesMonoEvalMixin
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import Det3DCli
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.blocks_3d.det3d.detr import Deformable3DDetrTransformerDecoder  # noqa: registry
from perceptron.layers.blocks_3d.det3d.detr3d_transformer import Detr3DTransformer  # noqa: registry
from perceptron.layers.head.det3d.bbox.coder import NMSFreeCoder  # noqa: registry
from perceptron.layers.head.det3d.bbox.match_cost import BBox3DL1Cost  # noqa: registry
from perceptron.layers.head.det3d.detr3d_head import Detr3DHead  # noqa: registry
from perceptron.layers.head.det3d.target_assigner.hungarian_assigner_3d import HungarianAssigner3D  # noqa: registry


class NuScenesMonoDatasetWithEval(NuScenesMonoEvalMixin):
    def __init__(self, class_names=None, attribute_names=None, training=True, **kwargs):
        self.training = training
        self.class_names = class_names
        self.attribute_names = attribute_names
        self.point_cloud_range = kwargs["point_cloud_range"]
        self.img_norm_cfg = kwargs["img_norm_cfg"]

        # TODO: move to outside
        self.box_type_3d = "LiDAR"
        self.box_type_3d, self.box_mode_3d = get_box_type(self.box_type_3d)
        self.use_valid_flag = True
        self.with_velocity = False

        self.camera_list = [
            "CAM_FRONT",
            "CAM_FRONT_RIGHT",
            "CAM_BACK_RIGHT",
            "CAM_BACK",
            "CAM_BACK_LEFT",
            "CAM_FRONT_LEFT",
        ]

        self.dataset = NuScenesMonoDataset(
            class_names=self.class_names,
            data_split="training" if training else "validation",
            root_path="s3://generalDetection/3DDatasets/nuScenes/",
            img_key_list=self.camera_list,
        )

        self.data_augmentor = Compose(
            [
                transforms3d_mmlab.LoadMultiViewImage(camera_list=self.camera_list, to_float32=True),
                LoadAnnotations3D(
                    with_bbox_3d=True,
                    with_label_3d=True,
                    with_attr_label=False,
                ),
                ObjectRangeFilter(point_cloud_range=self.point_cloud_range),
                ObjectNameFilter(classes=class_names),
                transforms3d_mmlab.NormalizeMultiviewImage(**self.img_norm_cfg),
                transforms3d_mmlab.PadMultiViewImage(size_divisor=32),
                DefaultFormatBundle3D(class_names=self.class_names),
                Collect3D(
                    keys=[
                        "img",
                        "gt_bboxes_3d",
                        "gt_labels_3d",
                    ]
                ),
            ]
        )
        self.test_augmentor = Compose(
            [
                transforms3d_mmlab.LoadMultiViewImage(camera_list=self.camera_list, to_float32=True),
                transforms3d_mmlab.NormalizeMultiviewImage(**self.img_norm_cfg),
                transforms3d_mmlab.PadMultiViewImage(size_divisor=32),
                MultiScaleFlipAug3D(
                    img_scale=(1333, 800),
                    # img_scale=(1600, 900),
                    pts_scale_ratio=1,
                    flip=False,
                    transforms=[
                        DefaultFormatBundle3D(class_names=self.class_names, with_label=False),
                        Collect3D(keys=["img"]),
                    ],
                ),
            ]
        )

        self.DefaultAttribute = {
            "car": "vehicle.parked",
            "pedestrian": "pedestrian.moving",
            "trailer": "vehicle.parked",
            "truck": "vehicle.parked",
            "bus": "vehicle.moving",
            "motorcycle": "cycle.without_rider",
            "construction_vehicle": "vehicle.parked",
            "bicycle": "cycle.without_rider",
            "barrier": "",
            "traffic_cone": "",
        }

        self.eval_detection_configs = EasyDict(
            {
                "class_range": {
                    "car": 50,
                    "truck": 50,
                    "bus": 50,
                    "trailer": 50,
                    "construction_vehicle": 50,
                    "pedestrian": 40,
                    "motorcycle": 40,
                    "bicycle": 40,
                    "traffic_cone": 30,
                    "barrier": 30,
                },
                "dist_fcn": "center_distance",
                "dist_ths": [0.5, 1.0, 2.0, 4.0],
                "dist_th_tp": 2.0,
                "min_recall": 0.1,
                "min_precision": 0.1,
                "max_boxes_per_sample": 500,
                "mean_ap_weight": 5,
            }
        )
        self.eval_version = "detection_cvpr_2019"
        meta_type_list = ["use_camera"]
        self.modality = {
            "use_camera": "use_camera" in meta_type_list,
            "use_lidar": "use_lidar" in meta_type_list,
            "use_radar": "use_radar" in meta_type_list,
            "use_map": "use_map" in meta_type_list,
            "use_external": "use_external" in meta_type_list,
        }

        if not training and dist.get_rank() == 0:
            nuscenes_meta_path = "s3://yzy-share/nuscenes_tmp/nuscenes_v1.0-trainval_meta.pkl"
            if refile.is_s3(nuscenes_meta_path):
                meta_file = "./tmp/{}".format(os.path.basename(nuscenes_meta_path))
                if not os.path.exists(meta_file):
                    refile.s3_download(nuscenes_meta_path, meta_file)
            else:
                meta_file = nuscenes_meta_path
            from perceptron.data.det3d.dataset.nuscenes.eval_utils.eval_utils import load_pkl

            self.meta_info = load_pkl(meta_file)

    def __len__(self):
        return self.dataset.__len__()

    def __getitem__(self, idx: int):
        item = self.dataset[idx]
        info = copy.deepcopy(item["info"])

        data_dict = {k: copy.deepcopy(v) for k, v in item.items() if k in self.camera_list}
        data_info = self._get_data_info(info)
        data_dict.update(data_info)
        data_dict.update(
            {
                "img_fields": [],
                "bbox3d_fields": [],
                "pts_mask_fields": [],
                "pts_seg_fields": [],
                "bbox_fields": [],
                "mask_fields": [],
                "seg_fields": [],
                "box_type_3d": self.box_type_3d,
                "box_mode_3d": self.box_mode_3d,
            }
        )

        # ann_info
        if self.training:
            ann_info = self._get_ann_info(info)
            data_dict.update({"ann_info": ann_info})

        # AUG
        augmentor = self.data_augmentor if self.training else self.test_augmentor
        auged_data = augmentor(data_dict)

        # no ground truth: continue
        # if auged_data['gt_labels_3d'].data.shape[0] == 0 or auged_data['gt_labels_3d'].data.max() == -1:
        #     rand_idx = np.random.randint(len(self.dataset))
        #     return self.__getitem__(rand_idx)
        return auged_data

    def _get_data_info(self, info):
        """
        Modified from: https://github.com/WangYueFt/detr3d/blob/main/projects/mmdet3d_plugin/datasets/nuscenes_dataset.py
        """
        transform_info = info["sensor_transform_matrix"]
        lidar2img_rts, cam_intrinsics, lidar2cam_rts = [], [], []
        for camera in self.camera_list:
            lidar2img_rts.append(transform_info[camera]["lidar2img"])
            lidar2cam_rts.append(transform_info[camera]["lidar2camera"])
            cam_intrinsics.append(transform_info[camera]["intrinsic"])
        return dict(lidar2img=lidar2img_rts, lidar2cam=lidar2cam_rts, cam_intrinsics=cam_intrinsics)

    def _get_ann_info(self, info):
        """
        Modified from: from mmdet3d.datasets import NuScenesDataset
        """
        if self.use_valid_flag:
            mask = (info["num_lidar_pts"] + info["num_radar_pts"]) > 0
            # mask = info['valid_flag']
        else:
            mask = info["num_lidar_pts"] > 0
        gt_bboxes_3d = info["gt_boxes"][mask]
        gt_names_3d = info["gt_names"][mask]
        gt_labels_3d = []
        for cat in gt_names_3d:
            if cat in self.class_names:
                gt_labels_3d.append(self.class_names.index(cat))
            else:
                gt_labels_3d.append(-1)
        gt_labels_3d = np.array(gt_labels_3d)

        if self.with_velocity:
            gt_velocity = info["gt_velocity"][mask]
            nan_mask = np.isnan(gt_velocity[:, 0])
            gt_velocity[nan_mask] = [0.0, 0.0]
            gt_bboxes_3d = np.concatenate([gt_bboxes_3d, gt_velocity], axis=-1)

        # the nuscenes box center is [0.5, 0.5, 0.5], we change it to be
        # the same as KITTI (0.5, 0.5, 0)
        gt_bboxes_3d = LiDARInstance3DBoxes(
            gt_bboxes_3d, box_dim=gt_bboxes_3d.shape[-1], origin=(0.5, 0.5, 0.5)
        ).convert_to(self.box_mode_3d)

        anns_results = dict(gt_bboxes_3d=gt_bboxes_3d, gt_labels_3d=gt_labels_3d, gt_names=gt_names_3d)
        return anns_results

    @staticmethod
    def collate_batch(batch_list):
        """Decode DataContainer"""
        batch_size = len(batch_list)
        assert batch_size > 0

        batch_dict = {k: [] for k in batch_list[0].keys()}
        # dict_keys(['img_metas', 'img', 'gt_bboxes', 'gt_labels', 'attr_labels', 'gt_bboxes_3d', 'gt_labels_3d', 'centers2d', 'depths'])

        for key in batch_dict.keys():
            for i in range(batch_size):
                batch_dict[key].append(batch_list[i][key])

        def merge_DC(DC_list):
            """Merge DataContainer list"""
            assert DC_list.__len__() > 0
            sample = DC_list[0]
            cpu_only, stack, padding_value, pad_dims = (
                sample.cpu_only,
                sample.stack,
                sample.padding_value,
                sample.pad_dims,
            )

            # data = [dc.data for dc in DC_list]
            return DC([dc.data for dc in DC_list], stack, padding_value, cpu_only, pad_dims)

        for k, v in batch_dict.items():
            if type(v[0]) is list:
                v = [merge_DC(v_ele) for v_ele in v]
                batch_dict[k] = v
            else:
                batch_dict[k] = merge_DC(v)

        return batch_dict


class DataConfigs:
    point_cloud_range = [-51.2, -51.2, -5.0, 51.2, 51.2, 3.0]
    voxel_size = [0.2, 0.2, 8]


class ModelConfigs:
    """
    Refer to https://github.com/WangYueFt/detr3d/blob/main/projects/configs/detr3d/detr3d_res101_gridmask.py
    """

    class_names = (
        "car",
        "truck",
        "trailer",
        "bus",
        "construction_vehicle",
        "bicycle",
        "motorcycle",
        "pedestrian",
        "traffic_cone",
        "barrier",
    )
    attribute_names = (
        "cycle.with_rider",
        "cycle.without_rider",
        "pedestrian.moving",
        "pedestrian.standing",
        "pedestrian.sitting_lying_down",
        "vehicle.moving",
        "vehicle.parked",
        "vehicle.stopped",
        "None",
    )

    img_norm_cfg = dict(mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False)

    img_backbone = EasyDict(
        type="ResNet",
        depth=50,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=EasyDict(type="BN", requires_grad=False),
        norm_eval=True,
        style="caffe",
        # init_cfg=dict(type='Pretrained', checkpoint='torchvision://resnet50')   # NOTE
    )
    img_neck = EasyDict(
        type="FPN",
        in_channels=[256, 512, 1024, 2048],
        out_channels=256,
        start_level=1,
        add_extra_convs="on_output",
        num_outs=4,
        relu_before_extra_convs=True,
    )
    pts_bbox_head = EasyDict(
        type="Detr3DHead",
        num_query=100,
        num_classes=10,
        in_channels=256,
        sync_cls_avg_factor=True,
        with_box_refine=True,
        as_two_stage=False,
        transformer=dict(
            type="Detr3DTransformer",
            decoder=dict(
                type="Detr3DTransformerDecoder",
                num_layers=6,
                return_intermediate=True,
                transformerlayers=dict(
                    type="DetrTransformerDecoderLayer",
                    attn_cfgs=[
                        dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                        dict(
                            type="Detr3DCrossAtten",
                            pc_range=DataConfigs.point_cloud_range,
                            num_points=1,
                            embed_dims=256,
                        ),
                    ],
                    feedforward_channels=512,
                    ffn_dropout=0.1,
                    operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
                ),
            ),
        ),
        bbox_coder=dict(
            type="NMSFreeCoder",
            post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
            pc_range=DataConfigs.point_cloud_range,
            max_num=100,
            score_threshold=0.1,
            voxel_size=DataConfigs.voxel_size,
            num_classes=10,
        ),
        positional_encoding=dict(type="SinePositionalEncoding", num_feats=128, normalize=True, offset=-0.5),
        loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=2.0),
        loss_bbox=dict(type="L1Loss", loss_weight=0.25),
        loss_iou=dict(type="GIoULoss", loss_weight=0.0),
    )

    train_cfg = EasyDict(
        pts=dict(
            grid_size=[512, 512, 1],
            voxel_size=DataConfigs.voxel_size,
            point_cloud_range=DataConfigs.point_cloud_range,
            out_size_factor=4,
            assigner=dict(
                type="HungarianAssigner3D",
                cls_cost=dict(type="FocalLossCost", weight=2.0),
                reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
                iou_cost=dict(
                    type="IoUCost", weight=0.0
                ),  # Fake cost. This is just to make it compatible with DETR head.
                pc_range=DataConfigs.point_cloud_range,
            ),
        )
    )

    pretrained = {"img": "open-mmlab://detectron2/resnet50_caffe"}  # NOTE: pretrained参数与init_cfg作用相似，不能一起用


@DETECTORS.register_module()
class Detr3D(MVXTwoStageDetector):
    """Detr3D."""

    def __init__(
        self,
        use_grid_mask=False,
        pts_voxel_layer=None,
        pts_voxel_encoder=None,
        pts_middle_encoder=None,
        pts_fusion_layer=None,
        img_backbone=None,
        pts_backbone=None,
        img_neck=None,
        pts_neck=None,
        pts_bbox_head=None,
        img_roi_head=None,
        img_rpn_head=None,
        train_cfg=None,
        test_cfg=None,
        pretrained=None,
        class_names=None,
    ):
        super(Detr3D, self).__init__(
            pts_voxel_layer,
            pts_voxel_encoder,
            pts_middle_encoder,
            pts_fusion_layer,
            img_backbone,
            pts_backbone,
            img_neck,
            pts_neck,
            pts_bbox_head,
            img_roi_head,
            img_rpn_head,
            train_cfg,
            test_cfg,
            pretrained,
        )
        self.grid_mask = GridMask(True, True, rotate=1, offset=False, ratio=0.5, mode=1, prob=0.7)
        self.use_grid_mask = use_grid_mask
        self.class_names = class_names

    def extract_img_feat(self, img, img_metas):
        """Extract features of images."""
        B = img.size(0)
        if img is not None:
            input_shape = img.shape[-2:]
            # update real input shape of each single img
            for img_meta in img_metas:
                img_meta.update(input_shape=input_shape)

            if img.dim() == 5 and img.size(0) == 1:
                img.squeeze_()
            elif img.dim() == 5 and img.size(0) > 1:
                B, N, C, H, W = img.size()
                img = img.view(B * N, C, H, W)
            if self.use_grid_mask:
                img = self.grid_mask(img)
            img_feats = self.img_backbone(img)
            if isinstance(img_feats, dict):
                img_feats = list(img_feats.values())
        else:
            return None
        if self.with_img_neck:
            img_feats = self.img_neck(img_feats)
        img_feats_reshaped = []
        for img_feat in img_feats:
            BN, C, H, W = img_feat.size()
            img_feats_reshaped.append(img_feat.view(B, int(BN / B), C, H, W))
        return img_feats_reshaped

    @auto_fp16(apply_to=("img"), out_fp32=True)
    def extract_feat(self, img, img_metas):
        """Extract features from images and points."""
        img_feats = self.extract_img_feat(img, img_metas)
        return img_feats

    def forward_pts_train(self, pts_feats, gt_bboxes_3d, gt_labels_3d, img_metas, gt_bboxes_ignore=None):
        """Forward function for point cloud branch.
        Args:
            pts_feats (list[torch.Tensor]): Features of point cloud branch
            gt_bboxes_3d (list[:obj:`BaseInstance3DBoxes`]): Ground truth
                boxes for each sample.
            gt_labels_3d (list[torch.Tensor]): Ground truth labels for
                boxes of each sampole
            img_metas (list[dict]): Meta information of samples.
            gt_bboxes_ignore (list[torch.Tensor], optional): Ground truth
                boxes to be ignored. Defaults to None.
        Returns:
            dict: Losses of each branch.
        """
        outs = self.pts_bbox_head(pts_feats, img_metas)
        loss_inputs = [gt_bboxes_3d, gt_labels_3d, outs]
        losses = self.pts_bbox_head.loss(*loss_inputs)
        return losses

    @force_fp32(apply_to=("img", "points"))
    def forward(self, return_loss=True, **kwargs):
        """Calls either forward_train or forward_test depending on whether
        return_loss=True.
        Note this setting will change the expected inputs. When
        `return_loss=True`, img and img_metas are single-nested (i.e.
        torch.Tensor and list[dict]), and when `resturn_loss=False`, img and
        img_metas should be double nested (i.e.  list[torch.Tensor],
        list[list[dict]]), with the outer list indicating test time
        augmentations.
        """
        if return_loss and self.training:
            losses = self.forward_train(**kwargs)
            loss, tb_dict = self._parse_losses(losses)
            return {"loss": loss}, tb_dict, tb_dict
        else:
            return self.forward_test(**kwargs)

    def forward_train(
        self,
        points=None,
        img_metas=None,
        gt_bboxes_3d=None,
        gt_labels_3d=None,
        gt_labels=None,
        gt_bboxes=None,
        img=None,
        proposals=None,
        gt_bboxes_ignore=None,
        img_depth=None,
        img_mask=None,
    ):
        """Forward training function.
        Args:
            points (list[torch.Tensor], optional): Points of each sample.
                Defaults to None.
            img_metas (list[dict], optional): Meta information of each sample.
                Defaults to None.
            gt_bboxes_3d (list[:obj:`BaseInstance3DBoxes`], optional):
                Ground truth 3D boxes. Defaults to None.
            gt_labels_3d (list[torch.Tensor], optional): Ground truth labels
                of 3D boxes. Defaults to None.
            gt_labels (list[torch.Tensor], optional): Ground truth labels
                of 2D boxes in images. Defaults to None.
            gt_bboxes (list[torch.Tensor], optional): Ground truth 2D boxes in
                images. Defaults to None.
            img (torch.Tensor optional): Images of each sample with shape
                (N, C, H, W). Defaults to None.
            proposals ([list[torch.Tensor], optional): Predicted proposals
                used for training Fast RCNN. Defaults to None.
            gt_bboxes_ignore (list[torch.Tensor], optional): Ground truth
                2D boxes in images to be ignored. Defaults to None.
        Returns:
            dict: Losses of different branches.
        """
        img_feats = self.extract_feat(img=img, img_metas=img_metas)
        losses = dict()
        losses_pts = self.forward_pts_train(img_feats, gt_bboxes_3d, gt_labels_3d, img_metas, gt_bboxes_ignore)
        losses.update(losses_pts)
        return losses

    def forward_test(self, img_metas, img=None, **kwargs):
        for var, name in [(img_metas, "img_metas")]:
            if not isinstance(var, list):
                raise TypeError("{} must be a list, but got {}".format(name, type(var)))
        img = [img] if img is None else img
        inf_res = self.simple_test(img_metas[0], img[0], **kwargs)

        # NOTE: data3d box_3d的box形式为lwh，mmdet3d为whl
        # convert lwh -> wlh for evalutation
        for inf in inf_res:
            inf["pts_bbox"]["boxes_3d"].tensor[:, 3:5] = inf["pts_bbox"]["boxes_3d"].tensor[:, 3:5][:, [1, 0]]
        return inf_res
        # if num_augs == 1:
        #     img = [img] if img is None else img
        #     return self.simple_test(None, img_metas[0], img[0], **kwargs)
        # else:
        #     return self.aug_test(None, img_metas, img, **kwargs)

    def simple_test_pts(self, x, img_metas, rescale=False):
        """Test function of point cloud branch."""
        outs = self.pts_bbox_head(x, img_metas)
        bbox_list = self.pts_bbox_head.get_bboxes(outs, img_metas, rescale=rescale)
        bbox_results = [bbox3d2result(bboxes, scores, labels) for bboxes, scores, labels in bbox_list]
        return bbox_results

    def simple_test(self, img_metas, img=None, rescale=False):
        """Test function without augmentaiton."""
        img_feats = self.extract_feat(img=img, img_metas=img_metas)

        bbox_list = [dict() for i in range(len(img_metas))]
        bbox_pts = self.simple_test_pts(img_feats, img_metas, rescale=rescale)
        for result_dict, pts_bbox in zip(bbox_list, bbox_pts):
            result_dict["pts_bbox"] = pts_bbox
        return bbox_list

    def aug_test_pts(self, feats, img_metas, rescale=False):
        feats_list = []
        for j in range(len(feats[0])):
            feats_list_level = []
            for i in range(len(feats)):
                feats_list_level.append(feats[i][j])
            feats_list.append(torch.stack(feats_list_level, -1).mean(-1))
        outs = self.pts_bbox_head(feats_list, img_metas)
        bbox_list = self.pts_bbox_head.get_bboxes(outs, img_metas, rescale=rescale)
        bbox_results = [bbox3d2result(bboxes, scores, labels) for bboxes, scores, labels in bbox_list]
        return bbox_results

    def aug_test(self, img_metas, imgs=None, rescale=False):
        """Test function with augmentaiton."""
        img_feats = self.extract_feats(img_metas, imgs)
        img_metas = img_metas[0]
        bbox_list = [dict() for i in range(len(img_metas))]
        bbox_pts = self.aug_test_pts(img_feats, img_metas, rescale)
        for result_dict, pts_bbox in zip(bbox_list, bbox_pts):
            result_dict["pts_bbox"] = pts_bbox
        return bbox_list

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    def load_params_from_file(self, filename, logger, to_cpu=False):
        if not os.path.isfile(filename):
            raise FileNotFoundError

        logger.info("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)
        model_state_disk = checkpoint["model_state"]

        if "version" in checkpoint:
            logger.info("==> Checkpoint trained from version: %s" % checkpoint["version"])

        update_model_state = {}
        for key, val in model_state_disk.items():
            if key in self.state_dict() and self.state_dict()[key].shape == model_state_disk[key].shape:
                update_model_state[key] = val
                # logger.info('Update weight %s: %s' % (key, str(val.shape)))

        state_dict = self.state_dict()
        state_dict.update(update_model_state)
        self.load_state_dict(state_dict)

        for key in state_dict:
            if key not in update_model_state:
                logger.info("Not updated weight %s: %s" % (key, str(state_dict[key].shape)))

        logger.info("==> Done (loaded %d/%d)" % (len(update_model_state), len(self.state_dict())))


def load_data_to_gpu(batch_dict):
    def decode_item(item):
        if item.cpu_only:
            content = item.data
        else:
            stack = item.stack
            if stack:
                stack_data = [ele.unsqueeze(0) for ele in item.data]
                content = torch.cat(stack_data, dim=0).cuda()
            else:
                content = [ele.cuda() for ele in item.data]
        return content

    for key, item in batch_dict.items():
        if type(item) is DC:  # train
            batch_dict[key] = decode_item(item)
        elif type(item) is list:  # test
            for i in range(len(item)):
                item[i] = decode_item(item[i])
        else:
            if not isinstance(item, np.ndarray):
                continue
            if key in [
                "frame_id",
                "metadata",
                "calib",
                "image_shape",
                "Token",
                "random_flip_horizontal",
                "batch_size",
                "img2bev",
                "camera_order_list",
                "ori_images",
            ]:
                continue
            batch_dict[key] = torch.from_numpy(item).float().cuda()


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-4
        self.dump_interval = 1

    def _configure_model(self):
        model = Detr3D(
            img_backbone=ModelConfigs.img_backbone,
            img_neck=ModelConfigs.img_neck,
            pts_bbox_head=ModelConfigs.pts_bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.train_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model

    @torch.no_grad()
    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(**batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict, _, _ = self.model(**batch)
        loss = ret_dict["loss"].mean()
        return loss

    @torch.no_grad()
    def test_step(self, batch):
        load_data_to_gpu(batch)
        return self.model(**batch)

    def _configure_train_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(
            ModelConfigs.class_names,
            ModelConfigs.attribute_names,
            training=True,
            point_cloud_range=DataConfigs.point_cloud_range,
            img_norm_cfg=ModelConfigs.img_norm_cfg,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(
            ModelConfigs.class_names,
            ModelConfigs.attribute_names,
            training=False,
            point_cloud_range=DataConfigs.point_cloud_range,
            img_norm_cfg=ModelConfigs.img_norm_cfg,
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        param_groups = [
            {
                "params": [p for n, p in self.model.named_parameters() if "img_backbone" not in n and p.requires_grad],
                "lr": self.lr,
            },
            {
                "params": [p for n, p in self.model.named_parameters() if "img_backbone" in n and p.requires_grad],
                "lr": self.lr * 0.1,
            },
        ]
        optimizer = optim.AdamW(param_groups, lr=self.lr, weight_decay=0.01)
        return optimizer

    def _configure_lr_scheduler(self):
        iters_per_epoch = len(self.train_dataloader)
        scheduler = optim.lr_scheduler.MultiStepLR(
            optimizer=self.optimizer, milestones=[iters_per_epoch * 16, iters_per_epoch * 22], gamma=0.1
        )
        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
