"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no $NEG_TAGS -- python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_detr3d_adamonecycle_nuscenes/2021-12-08T14:28:33/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
mAP: 0.2161
mATE: 0.9151
mASE: 0.2821
mAOE: 0.5493
mAVE: 1.1455
mAAE: 0.2852
NDS: 0.3049
Eval time: 104.0s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.385   0.673   0.156   0.097   1.593   0.277
truck   0.162   0.929   0.235   0.150   1.253   0.331
bus     0.188   0.989   0.212   0.195   2.617   0.599
trailer 0.055   1.213   0.265   0.632   0.718   0.109
construction_vehicle    0.018   1.212   0.520   1.234   0.119   0.341
pedestrian      0.307   0.783   0.295   0.647   0.638   0.358
motorcycle      0.196   0.921   0.259   0.781   1.547   0.208
bicycle 0.188   0.832   0.263   0.998   0.679   0.058
traffic_cone    0.347   0.720   0.328   nan     nan     nan
barrier 0.315   0.881   0.287   0.210   nan     nan
"""
import torch
import torch.optim as optim

from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import Exp as BaseExp


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-4
        self.dump_interval = 1

    def _configure_optimizer(self):
        from functools import partial

        from perceptron.layers.optimizer.det3d import OptimWrapper

        def children(m: torch.nn.Module):
            return list(m.children())

        def num_children(m: torch.nn.Module) -> int:
            return len(children(m))

        def flatten_model(m):
            return sum(map(flatten_model, m.children()), []) if num_children(m) else [m]  # noqa

        def get_layer_groups(m):
            return [torch.nn.Sequential(*flatten_model(m))]  # noqa

        optimizer_func = partial(optim.Adam, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func, self.lr, get_layer_groups(self.model), wd=0.01, true_wd=True, bn_wd=True
        )
        return optimizer

    def _configure_lr_scheduler(self):
        from perceptron.layers.lr_scheduler import OnecycleLRScheduler

        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
