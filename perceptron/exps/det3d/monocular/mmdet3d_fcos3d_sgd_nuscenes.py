"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/det3d/mmdet3d_fcos3d_sgd_nuscenes.py -d 0-7 -b 1 -e 12 --sync_bn 3 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_fcos3d_sgd_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_fcos3d_sgd_nuscenes/2021-12-04T18:55:37/dump_model/checkpoint_epoch_11.pth

3. metric:
epoch_11
mAP: 0.2183
mATE: 0.8862
mASE: 0.2768
mAOE: 0.7213
mAVE: 1.3931
mAAE: 0.1786
NDS: 0.3028
Eval time: 122.6s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.381   0.712   0.155   0.170   2.162   0.155
truck   0.125   0.943   0.218   0.327   1.326   0.226
bus     0.174   0.948   0.202   0.281   3.113   0.353
trailer 0.026   1.194   0.245   1.002   0.762   0.086
construction_vehicle    0.025   1.187   0.451   1.422   0.099   0.324
pedestrian      0.344   0.766   0.295   0.970   0.914   0.181
motorcycle      0.191   0.844   0.282   1.082   1.999   0.092
bicycle 0.168   0.863   0.292   1.011   0.769   0.013
traffic_cone    0.416   0.652   0.337   nan     nan     nan
barrier 0.332   0.752   0.291   0.227   nan     nan
"""
import copy
import io
import json
import os

import nori2 as nori
import numpy as np
import refile
import torch
import torch.distributed as dist
import torch.optim as optim
from easydict import EasyDict
from skimage import io as skimage_io
from torch.utils.data import DataLoader, DistributedSampler

from data3d.datasets.base import Dataset3D
from mmcv.parallel import DataContainer as DC
from mmcv.runner import auto_fp16
from mmdet3d.core.bbox import CameraInstance3DBoxes
from mmdet3d.datasets.pipelines import Collect3D, DefaultFormatBundle3D, LoadAnnotations3D, RandomFlip3D
from mmdet3d.models.detectors import FCOSMono3D
from mmdet.datasets.pipelines import Compose, MultiScaleFlipAug, Normalize, Pad, Resize
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import Det3DCli
from perceptron.exps.base_exp import BaseExp


class NuscenesMonoDataset(Dataset3D):
    """load nuscenes mono dataset"""

    def __init__(
        self,
        class_names=(
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "trailer",
            "barrier",
            "motorcycle",
            "bicycle",
            "pedestrian",
            "traffic_cone",
        ),
        data_split="training",
        logger=None,
        root_path=None,
        is_nori_read=True,
        transforms=None,
        img_key_list=["CAM_BACK", "CAM_BACK_LEFT", "CAM_BACK_RIGHT", "CAM_FRONT", "CAM_FRONT_LEFT", "CAM_FRONT_RIGHT"],
    ):
        assert set(img_key_list).issubset(
            ["CAM_BACK", "CAM_BACK_LEFT", "CAM_BACK_RIGHT", "CAM_FRONT", "CAM_FRONT_LEFT", "CAM_FRONT_RIGHT"]
        ), "Illegal image keys"

        super().__init__(
            class_names=class_names,
            data_split=data_split,
            logger=logger,
            root_path=root_path,
            is_nori_read=is_nori_read,
            transforms=transforms,
        )
        self.img_key_list = img_key_list
        self.nori_fetcher = None

        if data_split in ("training", "validation"):
            print("Loading annotations...")
            if data_split == "training":
                anno_file = "s3://yzy-share/nuscenes_tmp/nuscenes_infos_train_mono3d.det3d.json"
            else:
                anno_file = "s3://yzy-share/nuscenes_tmp/nuscenes_infos_val_mono3d.det3d.json"

            if refile.is_s3(anno_file):
                local_file = "./tmp/{}".format(os.path.basename(anno_file))
                if not os.path.exists(local_file):
                    refile.s3_download(anno_file, local_file)
            else:
                local_file = anno_file

            with refile.smart_open(local_file, "r") as f:
                all_infos = json.loads(f.read())

            self.infos = []

            if data_split == "training":
                for sensor_name in self.img_key_list:
                    self.infos += all_infos[sensor_name]
            else:
                # different views per scene are in order
                num_sample = len(all_infos[self.img_key_list[0]])
                for i in range(num_sample):
                    scene = []
                    for sensor_name in self.img_key_list:
                        scene.append(all_infos[sensor_name][i])
                    self.infos += scene

            assert len(self.infos) > 0

    def _get_image(self, idx, key):
        """
        Loading image with given sample index

        Args:
            idx (int):, Sampled index
        Returns:
            image (Dict[str, np.ndarray]): (H, W, 3), RGB Image
        """
        result = {}
        if self.is_nori_read:
            nori_img_id = self.infos[idx]["img_info"]["nori_id"]
            img_file = io.BytesIO(self.nori_fetcher.get(nori_img_id))
        else:
            pass
        result[key] = skimage_io.imread(img_file)
        return result

    def __len__(self):
        return len(self.infos)

    def __getitem__(self, idx: int):
        """
        loading data-of-all-sensors with given index

        Args:
            idx: int, Sampled index
        Returns:
            dict
        """

        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        item = {}
        item.update(self.infos[idx])
        item.update(self._get_image(idx, "image"))
        return item


class NuScenesEvalMixin:
    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels - 1]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        from perceptron.data.det3d.dataset.nuscenes.eval_utils.evaluation import get_evaluation_results

        self.CLASSES = class_names
        self.data_infos = [info["img_info"] for info in self.dataset.infos]

        output_dir = os.path.join(str(kwargs["output_dir"]), "nuscenes")
        _, __ = self.format_results(det_annos, output_dir)
        ap_dict = get_evaluation_results(
            nusc_meta_info=self.meta_info,
            result_path=os.path.join(output_dir, "nuscenes_results.json"),
            output_dir=output_dir,
            eval_set="val",
            verbose=False,
            plot_examples=0,
            render_curves=False,
        )
        ap_result_str = None
        return ap_result_str, ap_dict

    def format_results(self, results, jsonfile_prefix=None, **kwargs):
        """Format the results to json (standard format for COCO evaluation).

        Args:
            results (list[tuple | numpy.ndarray]): Testing results of the
                dataset.
            jsonfile_prefix (str | None): The prefix of json files. It includes
                the file path and the prefix of filename, e.g., "a/b/prefix".
                If not specified, a temp file will be created. Default: None.

        Returns:
            tuple: (result_files, tmp_dir), result_files is a dict containing \
                the json filepaths, tmp_dir is the temporal directory created \
                for saving json files when jsonfile_prefix is not specified.
        """
        import tempfile

        assert isinstance(results, list), "results must be a list"
        assert len(results) == len(self), "The length of results is not equal to the dataset len: {} != {}".format(
            len(results), len(self)
        )

        if jsonfile_prefix is None:
            tmp_dir = tempfile.TemporaryDirectory()
            jsonfile_prefix = os.path.join(tmp_dir.name, "results")
        else:
            tmp_dir = None

        # currently the output prediction results could be in two formats
        # 1. list of dict('boxes_3d': ..., 'scores_3d': ..., 'labels_3d': ...)
        # 2. list of dict('pts_bbox' or 'img_bbox':
        #     dict('boxes_3d': ..., 'scores_3d': ..., 'labels_3d': ...))
        # this is a workaround to enable evaluation of both formats on nuScenes
        # refer to https://github.com/open-mmlab/mmdetection3d/issues/449
        if not ("pts_bbox" in results[0] or "img_bbox" in results[0]):
            result_files = self._format_bbox(results, jsonfile_prefix)
        else:
            # should take the inner dict out of 'pts_bbox' or 'img_bbox' dict
            result_files = dict()
            for name in results[0]:
                # not evaluate 2D predictions on nuScenes
                if "2d" in name:
                    continue
                print(f"\nFormating bboxes of {name}")
                results_ = [out[name] for out in results]
                tmp_file_ = os.path.join(jsonfile_prefix, name)
                result_files.update({name: self._format_bbox(results_, tmp_file_)})

        return result_files, tmp_dir

    def _format_bbox(self, results, jsonfile_prefix=None):
        """Convert the results to the standard format.

        Args:
            results (list[dict]): Testing results of the dataset.
            jsonfile_prefix (str): The prefix of the output jsonfile.
                You can specify the output directory/filename by
                modifying the jsonfile_prefix. Default: None.

        Returns:
            str: Path of the output json file.
        """
        import mmcv
        from mmdet3d.datasets.nuscenes_mono_dataset import (
            output_to_nusc_box,
            cam_nusc_box_to_global,
            global_nusc_box_to_cam,
            nusc_box_to_cam_box3d,
        )
        from mmdet3d.core import bbox3d2result, box3d_multiclass_nms, xywhr2xyxyr

        nusc_annos = {}
        mapped_class_names = self.CLASSES

        print("Start to convert detection format...")

        CAM_NUM = 6

        for sample_id, det in enumerate(mmcv.track_iter_progress(results)):

            if sample_id % CAM_NUM == 0:
                boxes_per_frame = []
                attrs_per_frame = []

            # need to merge results from images of the same sample
            annos = []
            boxes, attrs = output_to_nusc_box(det)
            sample_token = self.data_infos[sample_id]["token"]
            boxes, attrs = cam_nusc_box_to_global(
                self.data_infos[sample_id],
                boxes,
                attrs,
                mapped_class_names,
                self.eval_detection_configs,
                self.eval_version,
            )

            boxes_per_frame.extend(boxes)
            attrs_per_frame.extend(attrs)
            # Remove redundant predictions caused by overlap of images
            if (sample_id + 1) % CAM_NUM != 0:
                continue
            boxes = global_nusc_box_to_cam(
                self.data_infos[sample_id + 1 - CAM_NUM],
                boxes_per_frame,
                mapped_class_names,
                self.eval_detection_configs,
                self.eval_version,
            )
            cam_boxes3d, scores, labels = nusc_box_to_cam_box3d(boxes)
            # box nms 3d over 6 images in a frame
            # TODO: move this global setting into config
            nms_cfg = dict(
                use_rotate_nms=True,
                nms_across_levels=False,
                nms_pre=4096,
                nms_thr=0.05,
                score_thr=0.01,
                min_bbox_size=0,
                max_per_frame=500,
            )
            from mmcv import Config

            nms_cfg = Config(nms_cfg)
            cam_boxes3d_for_nms = xywhr2xyxyr(cam_boxes3d.bev)
            boxes3d = cam_boxes3d.tensor
            # generate attr scores from attr labels
            attrs = labels.new_tensor([attr for attr in attrs_per_frame])
            boxes3d, scores, labels, attrs = box3d_multiclass_nms(
                boxes3d,
                cam_boxes3d_for_nms,
                scores,
                nms_cfg.score_thr,
                nms_cfg.max_per_frame,
                nms_cfg,
                mlvl_attr_scores=attrs,
            )
            cam_boxes3d = CameraInstance3DBoxes(boxes3d, box_dim=9)
            det = bbox3d2result(cam_boxes3d, scores, labels, attrs)
            boxes, attrs = output_to_nusc_box(det)
            boxes, attrs = cam_nusc_box_to_global(
                self.data_infos[sample_id + 1 - CAM_NUM],
                boxes,
                attrs,
                mapped_class_names,
                self.eval_detection_configs,
                self.eval_version,
            )

            for i, box in enumerate(boxes):
                name = mapped_class_names[box.label]
                attr = self.get_attr_name(attrs[i], name)
                nusc_anno = dict(
                    sample_token=sample_token,
                    translation=box.center.tolist(),
                    size=box.wlh.tolist(),
                    rotation=box.orientation.elements.tolist(),
                    velocity=box.velocity[:2].tolist(),
                    detection_name=name,
                    detection_score=box.score,
                    attribute_name=attr,
                )
                annos.append(nusc_anno)
            # other views results of the same frame should be concatenated
            if sample_token in nusc_annos:
                nusc_annos[sample_token].extend(annos)
            else:
                nusc_annos[sample_token] = annos

        nusc_submissions = {
            "meta": self.modality,
            "results": nusc_annos,
        }

        jsonfile_prefix = jsonfile_prefix.replace("img_bbox", "")
        mmcv.mkdir_or_exist(jsonfile_prefix)
        # res_path = os.path.join(jsonfile_prefix, 'results_nusc.json')
        res_path = os.path.join(jsonfile_prefix, "nuscenes_results.json")
        print("Results writes to", res_path)
        mmcv.dump(nusc_submissions, res_path)
        return res_path

    def get_attr_name(self, attr_idx, label_name):
        """Get attribute from predicted index.

        This is a workaround to predict attribute when the predicted velocity
        is not reliable. We map the predicted attribute index to the one
        in the attribute set. If it is consistent with the category, we will
        keep it. Otherwise, we will use the default attribute.

        Args:
            attr_idx (int): Attribute index.
            label_name (str): Predicted category name.

        Returns:
            str: Predicted attribute name.
        """
        # TODO: Simplify the variable name
        AttrMapping_rev2 = [
            "cycle.with_rider",
            "cycle.without_rider",
            "pedestrian.moving",
            "pedestrian.standing",
            "pedestrian.sitting_lying_down",
            "vehicle.moving",
            "vehicle.parked",
            "vehicle.stopped",
            "None",
        ]
        DefaultAttribute = {
            "car": "vehicle.parked",
            "pedestrian": "pedestrian.moving",
            "trailer": "vehicle.parked",
            "truck": "vehicle.parked",
            "bus": "vehicle.moving",
            "motorcycle": "cycle.without_rider",
            "construction_vehicle": "vehicle.parked",
            "bicycle": "cycle.without_rider",
            "barrier": "",
            "traffic_cone": "",
        }
        if (
            label_name == "car"
            or label_name == "bus"
            or label_name == "truck"
            or label_name == "trailer"
            or label_name == "construction_vehicle"
        ):
            if (
                AttrMapping_rev2[attr_idx] == "vehicle.moving"
                or AttrMapping_rev2[attr_idx] == "vehicle.parked"
                or AttrMapping_rev2[attr_idx] == "vehicle.stopped"
            ):
                return AttrMapping_rev2[attr_idx]
            else:
                return DefaultAttribute[label_name]
        elif label_name == "pedestrian":
            if (
                AttrMapping_rev2[attr_idx] == "pedestrian.moving"
                or AttrMapping_rev2[attr_idx] == "pedestrian.standing"
                or AttrMapping_rev2[attr_idx] == "pedestrian.sitting_lying_down"
            ):
                return AttrMapping_rev2[attr_idx]
            else:
                return DefaultAttribute[label_name]
        elif label_name == "bicycle" or label_name == "motorcycle":
            if AttrMapping_rev2[attr_idx] == "cycle.with_rider" or AttrMapping_rev2[attr_idx] == "cycle.without_rider":
                return AttrMapping_rev2[attr_idx]
            else:
                return DefaultAttribute[label_name]
        else:
            return DefaultAttribute[label_name]


class NuScenesMonoDatasetWithEval(NuScenesEvalMixin):
    def __init__(self, class_names=None, attribute_names=None, training=True):
        self.training = training
        self.class_names = class_names
        self.attribute_names = attribute_names

        self.dataset = NuscenesMonoDataset(
            class_names=self.class_names,
            data_split="training" if training else "validation",
            root_path="s3://generalDetection/3DDatasets/nuScenes/",
            img_key_list=[
                "CAM_FRONT",
                "CAM_FRONT_RIGHT",
                "CAM_BACK_RIGHT",
                "CAM_BACK",
                "CAM_BACK_LEFT",
                "CAM_FRONT_LEFT",
            ],
        )

        self.data_augmentor = Compose(
            [
                LoadAnnotations3D(
                    with_bbox=True,
                    with_label=True,
                    with_attr_label=True,
                    with_bbox_3d=True,
                    with_label_3d=True,
                    with_bbox_depth=True,
                ),
                Resize(img_scale=(1600, 900), keep_ratio=True),
                RandomFlip3D(flip_ratio_bev_horizontal=0.5),
                Normalize(mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False),
                Pad(size_divisor=32),
                DefaultFormatBundle3D(class_names=self.class_names),
                Collect3D(
                    keys=[
                        "img",
                        "gt_bboxes",
                        "gt_labels",
                        "attr_labels",
                        "gt_bboxes_3d",
                        "gt_labels_3d",
                        "centers2d",
                        "depths",
                    ]
                ),
            ]
        )
        self.test_augmentor = Compose(
            [
                MultiScaleFlipAug(
                    scale_factor=1.0,
                    flip=False,
                    transforms=[
                        Normalize(mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False),
                        Pad(size_divisor=32),
                        DefaultFormatBundle3D(class_names=self.class_names, with_label=False),
                        Collect3D(keys=["img"]),
                    ],
                )
            ]
        )

        self.eval_detection_configs = EasyDict(
            {
                "class_range": {
                    "car": 50,
                    "truck": 50,
                    "bus": 50,
                    "trailer": 50,
                    "construction_vehicle": 50,
                    "pedestrian": 40,
                    "motorcycle": 40,
                    "bicycle": 40,
                    "traffic_cone": 30,
                    "barrier": 30,
                },
                "dist_fcn": "center_distance",
                "dist_ths": [0.5, 1.0, 2.0, 4.0],
                "dist_th_tp": 2.0,
                "min_recall": 0.1,
                "min_precision": 0.1,
                "max_boxes_per_sample": 500,
                "mean_ap_weight": 5,
            }
        )
        self.eval_version = "detection_cvpr_2019"
        meta_type_list = ["use_camera"]
        self.modality = {
            "use_camera": "use_camera" in meta_type_list,
            "use_lidar": "use_lidar" in meta_type_list,
            "use_radar": "use_radar" in meta_type_list,
            "use_map": "use_map" in meta_type_list,
            "use_external": "use_external" in meta_type_list,
        }

        if not training and dist.get_rank() == 0:
            nuscenes_meta_path = "s3://yzy-share/nuscenes_tmp/nuscenes_v1.0-trainval_meta.pkl"
            if refile.is_s3(nuscenes_meta_path):
                meta_file = "./tmp/{}".format(os.path.basename(nuscenes_meta_path))
                if not os.path.exists(meta_file):
                    refile.s3_download(nuscenes_meta_path, meta_file)
            else:
                meta_file = nuscenes_meta_path
            # dist.barrier()
            from perceptron.data.det3d.dataset.nuscenes.eval_utils.eval_utils import load_pkl

            self.meta_info = load_pkl(meta_file)

    def __len__(self):
        return self.dataset.__len__()

    def _plot_annotations(self, dataset, img_num=5, start=None):
        """plot annotations for debug"""
        import cv2

        def plot_boxes(img, boxes):
            for box in boxes:
                x1, y1, w, h = box
                x1, y1, x2, y2 = list(map(int, [x1, y1, x1 + w, y1 + h]))
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)

        def plot_centers2d(img, centers):
            for center in centers:
                cx, cy = list(map(int, center[:2]))
                cv2.circle(img, (cx, cy), 1, (0, 0, 255), 2)

        import random

        if start is None:
            start = random.randint(0, len(dataset) - 1)
        for i in range(start, min(start + img_num, len(dataset))):
            item = dataset[i]
            img = item["image"]
            plot_boxes(img, item["ann_info"]["bbox"])
            plot_centers2d(img, item["ann_info"]["center2d"])
            cv2.imwrite("./{}.jpg".format(item["img_info"]["nori_id"]), item["image"])

    def __getitem__(self, idx: int):
        """mmdet3d dataset
        from mmdet3d.datasets import NuScenesMonoDataset
        nusc_ds = NuScenesMonoDataset('/data/dataset/3D/nuscenes', ann_file='/data/dataset/3D/nuscenes/nuscenes_infos_train_mono3d.coco.json', pipeline=[])
        """
        item = self.dataset[idx]
        data_dict = dict()
        data_dict.update(
            {
                "img": copy.deepcopy(item["image"]),
                "img_info": copy.deepcopy(item["img_info"]),
            }
        )

        if self.training:
            ann_info = copy.deepcopy(item["ann_info"])
            ann_info = self._parse_ann_info(data_dict["img_info"], ann_info)
            data_dict["ann_info"] = ann_info

        img_shape = data_dict["img"].shape
        # LoadImageFromFileMono3D
        data_dict.update(
            {
                "cam_intrinsic": copy.deepcopy(data_dict["img_info"]["cam_intrinsic"]),
                "cam2img": copy.deepcopy(data_dict["img_info"]["cam_intrinsic"]),
                "img_shape": img_shape,
                "ori_shape": img_shape,
                "img_fields": ["img"],
                "bbox_fields": [],
                "bbox3d_fields": [],
                "box_type_3d": CameraInstance3DBoxes,
            }
        )
        # AUG
        augmentor = self.data_augmentor if self.training else self.test_augmentor
        return augmentor(data_dict)

    def _parse_ann_info(self, img_info, ann_info):
        """Parse bbox annotation.

        Args:
            img_info (list[dict]): Image info.
            ann_info (list[dict]): Annotation info of an image.

        Returns:
            dict: A dict containing the following keys: bboxes, labels, \
                gt_bboxes_3d, gt_labels_3d, attr_labels, centers2d, \
                depths, bboxes_ignore, masks, seg_map
        """

        cls2id = {self.class_names[i]: i for i in range(len(self.class_names))}
        attr2id = {self.attribute_names[i]: i for i in range(len(self.attribute_names))}

        gt_bboxes = []
        gt_labels = []
        attr_labels = []
        gt_bboxes_ignore = []
        gt_bboxes_cam3d = []
        centers2d = []
        depths = []
        for i in range(len(ann_info["bbox"])):
            x1, y1, w, h = ann_info["bbox"][i]
            inter_w = max(0, min(x1 + w, img_info["width"]) - max(x1, 0))
            inter_h = max(0, min(y1 + h, img_info["height"]) - max(y1, 0))
            if inter_w * inter_h == 0:
                continue
            if ann_info["area"][i] <= 0 or w < 1 or h < 1:
                continue
            if ann_info["category_name"][i] not in self.class_names:
                continue

            bbox = [x1, y1, x1 + w, y1 + h]
            if ann_info["iscrowd"][i] == 1:
                gt_bboxes_ignore.append(bbox)
            else:
                gt_bboxes.append(bbox)
                gt_labels.append(cls2id[ann_info["category_name"][i]])
                attr_labels.append(attr2id[ann_info["attribute_name"][i]])
                # 3D annotations in camera coordinates
                bbox_cam3d = np.array(ann_info["bbox_cam3d"][i]).reshape(1, -1)
                velo_cam3d = np.array(ann_info["velo_cam3d"][i]).reshape(1, 2)
                nan_mask = np.isnan(velo_cam3d[:, 0])
                velo_cam3d[nan_mask] = [0.0, 0.0]
                bbox_cam3d = np.concatenate([bbox_cam3d, velo_cam3d], axis=-1)
                gt_bboxes_cam3d.append(bbox_cam3d.squeeze())
                # 2.5D annotations in camera coordinates
                center2d = ann_info["center2d"][i][:2]
                depth = ann_info["center2d"][i][2]
                centers2d.append(center2d)
                depths.append(depth)

        if gt_bboxes:
            gt_bboxes = np.array(gt_bboxes, dtype=np.float32)
            gt_labels = np.array(gt_labels, dtype=np.int64)
            attr_labels = np.array(attr_labels, dtype=np.int64)
        else:
            gt_bboxes = np.zeros((0, 4), dtype=np.float32)
            gt_labels = np.array([], dtype=np.int64)
            attr_labels = np.array([], dtype=np.int64)

        if gt_bboxes_cam3d:
            gt_bboxes_cam3d = np.array(gt_bboxes_cam3d, dtype=np.float32)
            centers2d = np.array(centers2d, dtype=np.float32)
            depths = np.array(depths, dtype=np.float32)
        else:
            gt_bboxes_cam3d = np.zeros((0, 9), dtype=np.float32)
            centers2d = np.zeros((0, 2), dtype=np.float32)
            depths = np.zeros((0), dtype=np.float32)

        gt_bboxes_cam3d = CameraInstance3DBoxes(
            gt_bboxes_cam3d, box_dim=gt_bboxes_cam3d.shape[-1], origin=(0.5, 0.5, 0.5)
        )
        gt_labels_3d = copy.deepcopy(gt_labels)

        if gt_bboxes_ignore:
            gt_bboxes_ignore = np.array(gt_bboxes_ignore, dtype=np.float32)
        else:
            gt_bboxes_ignore = np.zeros((0, 4), dtype=np.float32)

        ann = dict(
            bboxes=gt_bboxes,
            labels=gt_labels,
            gt_bboxes_3d=gt_bboxes_cam3d,
            gt_labels_3d=gt_labels_3d,
            attr_labels=attr_labels,
            centers2d=centers2d,
            depths=depths,
            bboxes_ignore=gt_bboxes_ignore,
            bbox_type_3d=CameraInstance3DBoxes,  # for test
        )

        return ann

    @staticmethod
    def collate_batch(batch_list):
        """Decode DataContainer"""
        batch_size = len(batch_list)
        assert batch_size > 0

        batch_dict = {k: [] for k in batch_list[0].keys()}
        # dict_keys(['img_metas', 'img', 'gt_bboxes', 'gt_labels', 'attr_labels', 'gt_bboxes_3d', 'gt_labels_3d', 'centers2d', 'depths'])

        for key in batch_dict.keys():
            for i in range(batch_size):
                batch_dict[key].append(batch_list[i][key])

        def merge_DC(DC_list):
            """Merge DataContainer list"""
            assert DC_list.__len__() > 0
            sample = DC_list[0]
            cpu_only, stack, padding_value, pad_dims = (
                sample.cpu_only,
                sample.stack,
                sample.padding_value,
                sample.pad_dims,
            )

            data = [dc.data for dc in DC_list]
            return DC(data, stack, padding_value, cpu_only, pad_dims)

        for k, v in batch_dict.items():
            if type(v[0]) is list:
                v = [merge_DC(v_ele) for v_ele in v]
                batch_dict[k] = v
            else:
                batch_dict[k] = merge_DC(v)

        return batch_dict


class ModelConfigs:
    class_names = (
        "car",
        "truck",
        "trailer",
        "bus",
        "construction_vehicle",
        "bicycle",
        "motorcycle",
        "pedestrian",
        "traffic_cone",
        "barrier",
    )
    attribute_names = (
        "cycle.with_rider",
        "cycle.without_rider",
        "pedestrian.moving",
        "pedestrian.standing",
        "pedestrian.sitting_lying_down",
        "vehicle.moving",
        "vehicle.parked",
        "vehicle.stopped",
        "None",
    )

    backbone = EasyDict(
        type="ResNet",
        # depth=101,
        depth=50,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=EasyDict(type="BN", requires_grad=False),
        norm_eval=True,
        style="caffe",
        # init_cfg=dict(type='Pretrained', checkpoint='torchvision://resnet50')   # NOTE
    )
    neck = EasyDict(
        type="FPN",
        in_channels=[256, 512, 1024, 2048],
        out_channels=256,
        start_level=1,
        add_extra_convs="on_output",
        num_outs=5,
        relu_before_extra_convs=True,
    )
    bbox_head = EasyDict(
        type="FCOSMono3DHead",
        num_classes=10,
        in_channels=256,
        stacked_convs=2,
        feat_channels=256,
        use_direction_classifier=True,
        diff_rad_by_sin=True,
        pred_attrs=True,
        pred_velo=True,
        dir_offset=0.7854,  # pi/4
        strides=[8, 16, 32, 64, 128],
        group_reg_dims=(2, 1, 3, 1, 2),  # offset, depth, size, rot, velo
        cls_branch=(256,),
        reg_branch=((256,), (256,), (256,), (256,), ()),  # offset  # depth  # size  # rot  # velo
        dir_branch=(256,),
        attr_branch=(256,),
        loss_cls=EasyDict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=1.0),
        loss_bbox=EasyDict(type="SmoothL1Loss", beta=1.0 / 9.0, loss_weight=1.0),
        loss_dir=EasyDict(type="CrossEntropyLoss", use_sigmoid=False, loss_weight=1.0),
        loss_attr=EasyDict(type="CrossEntropyLoss", use_sigmoid=False, loss_weight=1.0),
        loss_centerness=EasyDict(type="CrossEntropyLoss", use_sigmoid=True, loss_weight=1.0),
        norm_on_bbox=True,
        centerness_on_reg=True,
        center_sampling=True,
        conv_bias=True,
        dcn_on_last_conv=True,
    )
    train_cfg = EasyDict(
        allowed_border=0, code_weight=[1.0, 1.0, 0.2, 1.0, 1.0, 1.0, 1.0, 0.05, 0.05], pos_weight=-1, debug=False
    )
    test_cfg = EasyDict(
        use_rotate_nms=True,
        nms_across_levels=False,
        nms_pre=1000,
        nms_thr=0.8,
        score_thr=0.05,
        min_bbox_size=0,
        max_per_img=200,
    )

    pretrained = "open-mmlab://detectron2/resnet50_caffe"  # NOTE: pretrained参数与init_cfg作用相似，不能一起用


class FCOSMono(FCOSMono3D):
    def __init__(self, backbone, neck, bbox_head, train_cfg=None, test_cfg=None, pretrained=None, class_names=None):
        super().__init__(backbone, neck, bbox_head, train_cfg, test_cfg, pretrained)
        self.class_names = class_names

    @property
    def mode(self):
        return "TRAIN" if self.training else "TEST"

    def update_global_step(self):
        self.global_step += 1

    @auto_fp16(apply_to=("img",))
    def forward(self, img, img_metas, return_loss=True, **kwargs):
        """Calls either :func:`forward_train` or :func:`forward_test` depending
        on whether ``return_loss`` is ``True``.

        Note this setting will change the expected inputs. When
        ``return_loss=True``, img and img_meta are single-nested (i.e. Tensor
        and List[dict]), and when ``resturn_loss=False``, img and img_meta
        should be double nested (i.e.  List[Tensor], List[List[dict]]), with
        the outer list indicating test time augmentations.
        """
        if torch.onnx.is_in_onnx_export():
            assert len(img_metas) == 1
            return self.onnx_export(img[0], img_metas[0])

        # if return_loss:
        if self.training:
            losses = self.forward_train(img, img_metas, **kwargs)
            loss, tb_dict = self._parse_losses(losses)
            return {"loss": loss}, tb_dict, tb_dict
        else:
            return self.forward_test(img, img_metas, **kwargs)

    def load_params_from_file(self, filename, logger, to_cpu=False):
        if not os.path.isfile(filename):
            raise FileNotFoundError

        logger.info("==> Loading parameters from checkpoint %s to %s" % (filename, "CPU" if to_cpu else "GPU"))
        loc_type = torch.device("cpu") if to_cpu else None
        checkpoint = torch.load(filename, map_location=loc_type)
        model_state_disk = checkpoint["model_state"]

        if "version" in checkpoint:
            logger.info("==> Checkpoint trained from version: %s" % checkpoint["version"])

        update_model_state = {}
        for key, val in model_state_disk.items():
            if key in self.state_dict() and self.state_dict()[key].shape == model_state_disk[key].shape:
                update_model_state[key] = val
                # logger.info('Update weight %s: %s' % (key, str(val.shape)))

        state_dict = self.state_dict()
        state_dict.update(update_model_state)
        self.load_state_dict(state_dict)

        for key in state_dict:
            if key not in update_model_state:
                logger.info("Not updated weight %s: %s" % (key, str(state_dict[key].shape)))

        logger.info("==> Done (loaded %d/%d)" % (len(update_model_state), len(self.state_dict())))


def load_data_to_gpu(batch_dict):
    def decode_item(item):
        if item.cpu_only:
            content = item.data
        else:
            stack = item.stack
            if stack:
                stack_data = [ele.unsqueeze(0) for ele in item.data]
                content = torch.cat(stack_data, dim=0).cuda()
            else:
                content = [ele.cuda() for ele in item.data]
        return content

    for key, item in batch_dict.items():
        if type(item) is DC:  # train
            batch_dict[key] = decode_item(item)
        elif type(item) is list:  # test
            for i in range(len(item)):
                item[i] = decode_item(item[i])
        else:
            if not isinstance(item, np.ndarray):
                continue
            if key in [
                "frame_id",
                "metadata",
                "calib",
                "image_shape",
                "Token",
                "random_flip_horizontal",
                "batch_size",
                "img2bev",
                "camera_order_list",
                "ori_images",
            ]:
                continue
            batch_dict[key] = torch.from_numpy(item).float().cuda()


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=12, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-3
        self.dump_interval = 1

    def _configure_model(self):
        model = FCOSMono(
            backbone=ModelConfigs.backbone,
            neck=ModelConfigs.neck,
            bbox_head=ModelConfigs.bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.test_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model

    @torch.no_grad()
    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(**batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict, _, _ = self.model(**batch)
        loss = ret_dict["loss"].mean()
        return loss

    @torch.no_grad()
    def test_step(self, batch):
        load_data_to_gpu(batch)
        return self.model(**batch)

    def _configure_train_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(ModelConfigs.class_names, ModelConfigs.attribute_names, training=True)
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(ModelConfigs.class_names, ModelConfigs.attribute_names, training=False)
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        param_groups = self.model.parameters()
        optimizer = optim.SGD(param_groups, lr=self.lr, weight_decay=0, momentum=0.9)
        return optimizer

    def _configure_lr_scheduler(self):
        iters_per_epoch = len(self.train_dataloader)
        scheduler = optim.lr_scheduler.MultiStepLR(
            optimizer=self.optimizer, milestones=[iters_per_epoch * 16, iters_per_epoch * 22], gamma=0.1
        )
        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
