"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no $NEG_TAGS -- python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_detr3d_adamonecycle_nuscenes/2021-12-08T14:28:33/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
mAP: 0.1972
mATE: 0.9434
mASE: 0.2900
mAOE: 0.7482
mAVE: 1.1218
mAAE: 0.3494
NDS: 0.2655
Eval time: 94.1s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.371   0.695   0.159   0.181   1.651   0.317
truck   0.134   1.015   0.240   0.254   1.335   0.336
bus     0.174   1.020   0.240   0.272   2.391   0.524
trailer 0.032   1.268   0.285   0.959   0.440   0.087
construction_vehicle    0.010   1.217   0.494   1.547   0.123   0.404
pedestrian      0.292   0.806   0.298   1.148   0.860   0.611
motorcycle      0.168   0.913   0.277   0.925   1.459   0.405
bicycle 0.168   0.805   0.288   1.240   0.715   0.112
traffic_cone    0.334   0.742   0.334   nan     nan     nan
barrier 0.290   0.951   0.286   0.209   nan     nan
"""
import torch.distributed as dist

from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import (
    DataConfigs,
    DataLoader,
    Detr3D,
    DistributedSampler,
)
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import Exp as BaseExp
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import InfiniteSampler
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import ModelConfigs as OriginModelConfigs
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import NuScenesMonoDatasetWithEval


class ModelConfigs(OriginModelConfigs):
    img_norm_cfg = dict(mean=[103.53, 116.28, 123.675], std=[57.375, 57.12, 58.395], to_rgb=False)
    img_backbone = dict(
        type="RegNet",
        arch="regnetx_800mf",
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=True,
        init_cfg=dict(type="Pretrained", checkpoint="open-mmlab://regnetx_800mf"),
    )
    img_neck = dict(type="FPN", in_channels=[64, 128, 288, 672], out_channels=256, num_outs=4)

    pretrained = None


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-4
        self.dump_interval = 1

    def _configure_model(self):
        model = Detr3D(
            img_backbone=ModelConfigs.img_backbone,
            img_neck=ModelConfigs.img_neck,
            pts_bbox_head=ModelConfigs.pts_bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.train_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model

    def _configure_train_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(
            ModelConfigs.class_names,
            ModelConfigs.attribute_names,
            training=True,
            point_cloud_range=DataConfigs.point_cloud_range,
            img_norm_cfg=ModelConfigs.img_norm_cfg,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(
            ModelConfigs.class_names,
            ModelConfigs.attribute_names,
            training=False,
            point_cloud_range=DataConfigs.point_cloud_range,
            img_norm_cfg=ModelConfigs.img_norm_cfg,
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
