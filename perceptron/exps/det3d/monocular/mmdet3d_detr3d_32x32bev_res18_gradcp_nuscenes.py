"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no $NEG_TAGS -- python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml --find_unused_parameters
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_detr3d_adamonecycle_nuscenes/2021-12-08T14:28:33/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
mAP: 0.2174
mATE: 0.8897
mASE: 0.2825
mAOE: 0.6336
mAVE: 1.0748
mAAE: 0.2469
NDS: 0.3034
Eval time: 97.0s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.396   0.684   0.157   0.136   1.265   0.243
truck   0.147   0.937   0.230   0.246   1.087   0.285
bus     0.162   1.009   0.248   0.241   2.519   0.414
trailer 0.027   1.194   0.259   0.792   0.437   0.069
construction_vehicle    0.018   1.127   0.484   1.310   0.136   0.446
pedestrian      0.315   0.783   0.299   0.802   0.674   0.296
motorcycle      0.169   0.876   0.261   0.904   1.915   0.194
bicycle 0.179   0.753   0.269   1.081   0.566   0.027
traffic_cone    0.421   0.671   0.331   nan     nan     nan
barrier 0.339   0.864   0.288   0.190   nan     nan
"""
import torch.distributed as dist
from easydict import EasyDict

from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import DataConfigs
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_res18_gradcp_nuscenes import Detr3D
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_res18_gradcp_nuscenes import Exp as BaseExp
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_res18_gradcp_nuscenes import ModelConfigs as OriginModelConfigs


class ModelConfigs(OriginModelConfigs):
    pts_bbox_head = EasyDict(
        type="Detr3DHead",
        num_query=1024,
        num_classes=10,
        in_channels=256,
        sync_cls_avg_factor=True,
        with_box_refine=True,
        as_two_stage=False,
        transformer=dict(
            type="Detr3DTransformer",
            decoder=dict(
                type="Detr3DTransformerDecoder",
                num_layers=6,
                return_intermediate=True,
                transformerlayers=dict(
                    type="DetrTransformerDecoderLayer",
                    attn_cfgs=[
                        dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                        dict(
                            type="Detr3DCrossAtten",
                            pc_range=DataConfigs.point_cloud_range,
                            num_points=1,
                            embed_dims=256,
                        ),
                    ],
                    feedforward_channels=512,
                    ffn_dropout=0.1,
                    operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
                ),
            ),
        ),
        bbox_coder=dict(
            type="NMSFreeCoder",
            post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
            pc_range=DataConfigs.point_cloud_range,
            max_num=100,
            score_threshold=0.1,
            voxel_size=DataConfigs.voxel_size,
            num_classes=10,
        ),
        positional_encoding=dict(type="SinePositionalEncoding", num_feats=128, normalize=True, offset=-0.5),
        loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=2.0),
        loss_bbox=dict(type="L1Loss", loss_weight=0.25),
        loss_iou=dict(type="GIoULoss", loss_weight=0.0),
    )


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-4
        self.dump_interval = 1

    def _configure_model(self):
        model = Detr3D(
            img_backbone=ModelConfigs.img_backbone,
            img_neck=ModelConfigs.img_neck,
            pts_bbox_head=ModelConfigs.pts_bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.train_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model


if __name__ == "__main__":
    Det3DCli(Exp).run()
