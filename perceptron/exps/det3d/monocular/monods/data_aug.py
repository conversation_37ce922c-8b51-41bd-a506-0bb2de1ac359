import numpy as np
import mmcv
from mmdet3d.datasets.pipelines import RandomFlip3D


class RandomScale3D(object):
    """Apply global rotation, scaling and translation to a 3D scene.

    Args:
        rot_range (list[float]): Range of rotation angle.
            Defaults to [-0.78539816, 0.78539816] (close to [-pi/4, pi/4]).
        scale_ratio_range (list[float]): Range of scale ratio.
            Defaults to [0.95, 1.05].
        translation_std (list[float]): The standard deviation of translation
            noise. This applies random translation to a scene by a noise, which
            is sampled from a gaussian distribution whose standard deviation
            is set by ``translation_std``. Defaults to [0, 0, 0]
        shift_height (bool): Whether to shift height.
            (the fourth dimension of indoor points) when scaling.
            Defaults to False.
    """

    def __init__(
        self,
        rot_range=[-0.78539816, 0.78539816],
        scale_ratio_range=[0.95, 1.05],
        translation_std=[0, 0, 0],
        shift_height=False,
    ):
        seq_types = (list, tuple, np.ndarray)
        if not isinstance(rot_range, seq_types):
            assert isinstance(rot_range, (int, float)), f"unsupported rot_range type {type(rot_range)}"
            rot_range = [-rot_range, rot_range]
        self.rot_range = rot_range

        assert isinstance(scale_ratio_range, seq_types), f"unsupported scale_ratio_range type {type(scale_ratio_range)}"
        self.scale_ratio_range = scale_ratio_range

        if not isinstance(translation_std, seq_types):
            assert isinstance(
                translation_std, (int, float)
            ), f"unsupported translation_std type {type(translation_std)}"
            translation_std = [translation_std, translation_std, translation_std]
        assert all([std >= 0 for std in translation_std]), "translation_std should be positive"
        self.translation_std = translation_std
        self.shift_height = shift_height

    def _trans_bbox_points(self, input_dict):
        """Private function to translate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after translation, 'points', 'pcd_trans' \
                and keys in input_dict['bbox3d_fields'] are updated \
                in the result dict.
        """
        translation_std = np.array(self.translation_std, dtype=np.float32)
        trans_factor = np.random.normal(scale=translation_std, size=3).T  # 正态分布 标准差

        input_dict["points"].translate(trans_factor)
        input_dict["pcd_trans"] = trans_factor
        for key in input_dict["bbox3d_fields"]:
            input_dict[key].translate(trans_factor)

    def _rot_bbox_points(self, input_dict):
        """Private function to rotate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after rotation, 'points', 'pcd_rotation' \
                and keys in input_dict['bbox3d_fields'] are updated \
                in the result dict.
        """
        rotation = self.rot_range
        noise_rotation = np.random.uniform(rotation[0], rotation[1])

        # if no bbox in input_dict, only rotate points
        if len(input_dict["bbox3d_fields"]) == 0:  # ==1
            rot_mat_T = input_dict["points"].rotate(noise_rotation)
            input_dict["pcd_rotation"] = rot_mat_T
            return

        # rotate points with bboxes
        for key in input_dict["bbox3d_fields"]:  # key=['gt_bboxes_3d'] LiDARInstance3DBoxes
            if len(input_dict[key].tensor) != 0:
                points, rot_mat_T = input_dict[key].rotate(noise_rotation, input_dict["points"])  # todo
                input_dict["points"] = points
                input_dict["pcd_rotation"] = rot_mat_T

    def _random_scale_data_3d(self, input_dict):
        """Private function to randomly set the scale factor.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'pcd_scale_factor' are updated \
                in the result dict.
        """

        scale_factor = np.random.uniform(self.scale_ratio_range[0], self.scale_ratio_range[1])
        img = input_dict["img"]
        H, W = input_dict["img_shape"][0], input_dict["img_shape"][1]
        resize_dims = (int(W * scale_factor), int(H * scale_factor))
        img = mmcv.imresize(img, resize_dims)
        # input_dict['gt_bboxes_3d'].tensor *= scale_factor
        input_dict["gt_bboxes"] *= scale_factor
        input_dict["centers2d"] *= scale_factor
        input_dict["cam2img"][0][0] *= scale_factor
        input_dict["cam2img"][0][2] *= scale_factor
        input_dict["cam2img"][1][1] *= scale_factor
        input_dict["cam2img"][1][2] *= scale_factor
        input_dict["scale_factor"] = scale_factor
        input_dict["img_shape"] = img.shape
        input_dict["img"] = img

    def __call__(self, input_dict):
        if "transformation_3d_flow" not in input_dict:
            input_dict["transformation_3d_flow"] = []
        self._random_scale_data_3d(input_dict)  # 生成随机 缩放系数
        return input_dict

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f"(rot_range={self.rot_range},"
        repr_str += f" scale_ratio_range={self.scale_ratio_range},"
        repr_str += f" translation_std={self.translation_std},"
        repr_str += f" shift_height={self.shift_height})"
        return repr_str


class RandomFlip3Dmonods(RandomFlip3D):
    def __init__(self, sync_2d=True, flip_ratio_bev_horizontal=0.0, flip_ratio_bev_vertical=0.0, **kwargs):
        super(RandomFlip3D, self).__init__(flip_ratio=flip_ratio_bev_horizontal, **kwargs)
        self.sync_2d = sync_2d
        self.flip_ratio_bev_vertical = flip_ratio_bev_vertical
        if flip_ratio_bev_horizontal is not None:
            assert isinstance(flip_ratio_bev_horizontal, (int, float)) and 0 <= flip_ratio_bev_horizontal <= 1
        if flip_ratio_bev_vertical is not None:
            assert isinstance(flip_ratio_bev_vertical, (int, float)) and 0 <= flip_ratio_bev_vertical <= 1

    def random_flip_data_3d(self, input_dict, direction="horizontal"):
        """Flip 3D data randomly.

        Args:
            input_dict (dict): Result dict from loading pipeline.
            direction (str): Flip direction. Default: horizontal.

        Returns:
            dict: Flipped results, 'points', 'bbox3d_fields' keys are \
                updated in the result dict.
        """
        assert direction in ["horizontal", "vertical"]
        if len(input_dict["bbox3d_fields"]) == 0:  # test mode
            input_dict["bbox3d_fields"].append("empty_box3d")
            input_dict["empty_box3d"] = input_dict["box_type_3d"](np.array([], dtype=np.float32))
        assert len(input_dict["bbox3d_fields"]) == 1
        for key in input_dict["bbox3d_fields"]:
            if "points" in input_dict:
                input_dict["points"] = input_dict[key].flip(direction, points=input_dict["points"])
            else:
                input_dict[key].flip(direction)
        if "centers2d" in input_dict:
            assert (
                self.sync_2d is True and direction == "horizontal"
            ), "Only support sync_2d=True and horizontal flip with images"
            w = input_dict["img_shape"][1]
            input_dict["centers2d"][..., 0] = w - input_dict["centers2d"][..., 0]
            # need to modify the horizontal position of camera center
            # along u-axis in the image (flip like centers2d)
            # ['cam2img'][0][2] = c_u
            # see more details and examples at
            # https://github.com/open-mmlab/mmdetection3d/pull/744
            input_dict["cam2img"][0][2] = w - input_dict["cam2img"][0][2]
