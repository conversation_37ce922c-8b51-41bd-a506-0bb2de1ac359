"""
1. train 2x:
rlaunch --cpu=32 --gpu=8 --memory=200000 --preemptible no -- python perceptron/exps/det3d/monocular/monods/mono_depth_score_2x_camaware_multiscale_opt.py -d 0-7 -b 2 -e 24 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python perceptron/exps/det3d/monocular/monods/mono_depth_score_2x_camaware_multiscale_opt.py -d 0-7 -b 1 --no-clearml --eval --ckpt outputs/mono_depth_score_2x_camaware_multiscale_opt/latest/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
mAP: 0.3137
mATE: 0.7613
mASE: 0.2641
mAOE: 0.5114
mAVE: 1.1156
mAAE: 0.1539
NDS: 0.3878
Eval time: 189.9s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.488   0.587   0.149   0.084   1.497   0.138
truck   0.241   0.794   0.197   0.141   1.124   0.155
bus     0.316   0.778   0.183   0.093   2.379   0.338
trailer 0.086   1.169   0.227   0.855   0.662   0.068
construction_vehicle    0.047   1.069   0.475   1.087   0.131   0.273
pedestrian      0.409   0.687   0.286   0.687   0.757   0.155
motorcycle      0.286   0.770   0.264   0.622   1.643   0.091
bicycle 0.278   0.715   0.269   0.898   0.731   0.012                                                                                                                                                             traffic_cone    0.544   0.483   0.315   nan     nan     nan
barrier 0.443   0.561   0.276   0.136   nan     nan
"""
import numpy as np
import torch
import refile
import os
from functools import partial

import torch.distributed as dist
from easydict import EasyDict
from torch.utils.data import DataLoader, DistributedSampler

from mmcv.parallel import DataContainer as DC
from mmdet3d.core import bbox3d2result
from mmdet3d.datasets.pipelines import Collect3D, DefaultFormatBundle3D, LoadAnnotations3D
from mmdet.datasets.pipelines import Compose, MultiScaleFlipAug, Normalize, Pad, Resize
from perceptron.data.sampler import InfiniteSampler
from perceptron.engine.cli import Det3DCli
from perceptron.exps.base_exp import BaseExp
from mmcv.parallel import collate
from perceptron.exps.det3d.monocular.monods.data_aug import RandomFlip3Dmonods, RandomScale3D

from perceptron.layers.head.mmdet3d.monods_camaware_head import MonoDepthScoreCamHead  # noqa
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import NuscenesMonoDataset
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import NuScenesMonoDatasetWithEval as BaseDataset
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import ModelConfigs as BaseConfigs
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import FCOSMono as BaseMono


class NuScenesMonoDatasetWithEval(BaseDataset):
    def __init__(self, class_names=None, attribute_names=None, training=True):
        self.training = training
        self.class_names = class_names
        self.attribute_names = attribute_names
        self.dataset = NuscenesMonoDataset(
            class_names=self.class_names,
            data_split="training" if training else "validation",
            root_path="s3://generalDetection/3DDatasets/nuScenes/",
            img_key_list=[
                "CAM_FRONT",
                "CAM_FRONT_RIGHT",
                "CAM_BACK_RIGHT",
                "CAM_BACK",
                "CAM_BACK_LEFT",
                "CAM_FRONT_LEFT",
            ],
        )
        self.data_augmentor = Compose(
            [
                LoadAnnotations3D(
                    with_bbox=True,
                    with_label=True,
                    with_attr_label=True,
                    with_bbox_3d=True,
                    with_label_3d=True,
                    with_bbox_depth=True,
                ),
                Resize(img_scale=(1600, 900), keep_ratio=True),
                RandomScale3D(
                    rot_range=[0.0, 0.0],
                    scale_ratio_range=[0.8, 1.2],
                    translation_std=[0, 0, 0],
                    shift_height=False,
                ),
                RandomFlip3Dmonods(flip_ratio_bev_horizontal=0.5),
                Normalize(mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False),
                Pad(size_divisor=32),
                DefaultFormatBundle3D(class_names=self.class_names),
                Collect3D(
                    keys=[
                        "img",
                        "gt_bboxes",
                        "gt_labels",
                        "attr_labels",
                        "gt_bboxes_3d",
                        "gt_labels_3d",
                        "centers2d",
                        "depths",
                    ]
                ),
            ]
        )
        self.test_augmentor = Compose(
            [
                MultiScaleFlipAug(
                    scale_factor=1.0,
                    flip=False,
                    transforms=[
                        Normalize(mean=[103.530, 116.280, 123.675], std=[1.0, 1.0, 1.0], to_rgb=False),
                        Pad(size_divisor=32),
                        DefaultFormatBundle3D(class_names=self.class_names, with_label=False),
                        Collect3D(keys=["img"]),
                    ],
                )
            ]
        )
        self.eval_detection_configs = EasyDict(
            {
                "class_range": {
                    "car": 50,
                    "truck": 50,
                    "bus": 50,
                    "trailer": 50,
                    "construction_vehicle": 50,
                    "pedestrian": 40,
                    "motorcycle": 40,
                    "bicycle": 40,
                    "traffic_cone": 30,
                    "barrier": 30,
                },
                "dist_fcn": "center_distance",
                "dist_ths": [0.5, 1.0, 2.0, 4.0],
                "dist_th_tp": 2.0,
                "min_recall": 0.1,
                "min_precision": 0.1,
                "max_boxes_per_sample": 500,
                "mean_ap_weight": 5,
            }
        )
        self.eval_version = "detection_cvpr_2019"
        meta_type_list = ["use_camera"]
        self.modality = {
            "use_camera": "use_camera" in meta_type_list,
            "use_lidar": "use_lidar" in meta_type_list,
            "use_radar": "use_radar" in meta_type_list,
            "use_map": "use_map" in meta_type_list,
            "use_external": "use_external" in meta_type_list,
        }

        if not training and dist.get_rank() == 0:
            nuscenes_meta_path = "s3://yzy-share/nuscenes_tmp/nuscenes_v1.0-trainval_meta.pkl"
            if refile.is_s3(nuscenes_meta_path):
                meta_file = "./tmp/{}".format(os.path.basename(nuscenes_meta_path))
                if not os.path.exists(meta_file):
                    refile.s3_download(nuscenes_meta_path, meta_file)
            else:
                meta_file = nuscenes_meta_path
            # dist.barrier()
            from perceptron.data.det3d.dataset.nuscenes.eval_utils.eval_utils import load_pkl

            self.meta_info = load_pkl(meta_file)


class ModelConfigs(BaseConfigs):
    bbox_head = EasyDict(
        type="MonoDepthScoreCamHead",
        num_classes=10,
        in_channels=256,
        stacked_convs=2,
        feat_channels=256,
        use_direction_classifier=True,
        diff_rad_by_sin=True,
        pred_attrs=True,
        pred_velo=True,
        dir_offset=0.7854,  # pi/4
        strides=[8, 16, 32, 64, 128],
        group_reg_dims=(2, 1, 3, 1, 2),  # offset, depth, size, rot, velo
        cls_branch=(256,),
        reg_branch=((256,), (256,), (256,), (256,), ()),  # offset  # depth  # size  # rot  # velo
        dir_branch=(256,),
        attr_branch=(256,),
        loss_cls=EasyDict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=1.0),
        loss_bbox=EasyDict(type="SmoothL1Loss", beta=1.0 / 9.0, loss_weight=1.0),
        loss_dir=EasyDict(type="CrossEntropyLoss", use_sigmoid=False, loss_weight=1.0),
        loss_attr=EasyDict(type="CrossEntropyLoss", use_sigmoid=False, loss_weight=1.0),
        loss_centerness=EasyDict(type="CrossEntropyLoss", use_sigmoid=True, loss_weight=1.0),
        depthaware_branch=(64,),
        loss_depthaware=dict(type="CrossEntropyLoss", use_sigmoid=True, loss_weight=1.0),
        depthaware_beta=2,
        norm_on_bbox=True,
        centerness_on_reg=True,
        center_sampling=True,
        conv_bias=True,
        dcn_on_last_conv=True,
    )


class MonoDepthScore(BaseMono):
    def __init__(self, backbone, neck, bbox_head, train_cfg=None, test_cfg=None, pretrained=None, class_names=None):
        super().__init__(backbone, neck, bbox_head, train_cfg, test_cfg, pretrained)
        self.class_names = class_names
        # self.bbox_head = MonoDepthScoreHead(**bbox_head)

    def simple_test(self, img, img_metas, rescale=False):
        """Test function without test time augmentation.

        Args:
            imgs (list[torch.Tensor]): List of multiple images
            img_metas (list[dict]): List of image information.
            rescale (bool, optional): Whether to rescale the results.
                Defaults to False.

        Returns:
            list[list[np.ndarray]]: BBox results of each image and classes.
                The outer list corresponds to each image. The inner list
                corresponds to each class.
        """
        x = self.extract_feat(img)
        outs = self.bbox_head(x, img_metas)
        bbox_outputs = self.bbox_head.get_bboxes(*outs, img_metas, rescale=rescale)

        if self.bbox_head.pred_bbox2d:
            from mmdet.core import bbox2result

            bbox2d_img = [
                bbox2result(bboxes2d, labels, self.bbox_head.num_classes)
                for bboxes, scores, labels, attrs, bboxes2d in bbox_outputs
            ]
            bbox_outputs = [bbox_outputs[0][:-1]]

        bbox_img = [bbox3d2result(bboxes, scores, labels, attrs) for bboxes, scores, labels, attrs in bbox_outputs]

        bbox_list = [dict() for i in range(len(img_metas))]
        for result_dict, img_bbox in zip(bbox_list, bbox_img):
            result_dict["img_bbox"] = img_bbox
        if self.bbox_head.pred_bbox2d:
            for result_dict, img_bbox2d in zip(bbox_list, bbox2d_img):
                result_dict["img_bbox2d"] = img_bbox2d
        return bbox_list


def load_data_to_gpu(batch_dict):
    def decode_item(item):
        if item.cpu_only:
            content = item.data[0]
        else:
            stack = item.stack
            if stack:
                stack_data = [ele for ele in item.data]
                content = torch.cat(stack_data, dim=0).cuda()
            else:
                content = [ele.cuda() for ele in item.data[0]]
        return content

    for key, item in batch_dict.items():
        if type(item) is DC:  # train
            batch_dict[key] = decode_item(item)
        elif type(item) is list:  # test
            for i in range(len(item)):
                item[i] = decode_item(item[i])
        else:
            if not isinstance(item, np.ndarray):
                continue
            if key in [
                "frame_id",
                "metadata",
                "calib",
                "image_shape",
                "Token",
                "random_flip_horizontal",
                "batch_size",
                "img2bev",
                "camera_order_list",
                "ori_images",
            ]:
                continue
            batch_dict[key] = torch.from_numpy(item).float().cuda()


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-3
        self.dump_interval = 1
        self.num_keep_latest_ckpt = 12
        self.base_wd = 0.0001
        self.warmup_ratio = 1.0 / 3
        # self.warmup = 'linear'
        self.warmup_iters = 500
        self.warmup_ratio = 1.0 / 3
        # NOTE:
        if max_epoch == 12:
            self.step_epoch = [8, 11]  # 1x
        elif max_epoch == 24:
            self.step_epoch = [16, 22]  # 2x
        else:
            self.step_epoch = [max_epoch, max_epoch]
        # self.seed = 666

    def _configure_model(self):
        model = MonoDepthScore(
            backbone=ModelConfigs.backbone,
            neck=ModelConfigs.neck,
            bbox_head=ModelConfigs.bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.test_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model

    @torch.no_grad()
    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(**batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict, _, _ = self.model(**batch)
        loss = ret_dict["loss"].mean()
        return loss

    @torch.no_grad()
    def test_step(self, batch):
        load_data_to_gpu(batch)
        return self.model(**batch)

    def _configure_train_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(ModelConfigs.class_names, ModelConfigs.attribute_names, training=True)
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=partial(collate, samples_per_gpu=self.batch_size_per_device),
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(ModelConfigs.class_names, ModelConfigs.attribute_names, training=False)
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            # collate_fn=dataset.collate_batch,
            collate_fn=partial(collate, samples_per_gpu=self.batch_size_per_device),
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        pg0, pg1, pg2 = [], [], []  # optimizer parameter groups

        for k, v in self.model.named_modules():
            if hasattr(v, "bias") and isinstance(v.bias, torch.nn.Parameter):
                pg2.append(v.bias)  # biases
            if isinstance(v, torch.nn.BatchNorm2d) or "bn" in k:
                pg0.append(v.weight)
            elif hasattr(v, "weight") and isinstance(v.weight, torch.nn.Parameter):
                pg1.append(v.weight)

        optimizer = torch.optim.SGD(pg0, lr=self.lr, weight_decay=self.base_wd, momentum=0.9)
        optimizer.add_param_group({"params": pg1})  # add pg1 with weight_decay
        optimizer.add_param_group({"params": pg2, "lr": 2.0 * self.lr, "weight_decay": 0.0 * self.base_wd})

        return optimizer

    def _configure_lr_scheduler(self):
        # warm_up_with_multistep_lr
        iters_per_epoch = len(self.train_dataloader)
        milestones = [ep * iters_per_epoch for ep in self.step_epoch]
        warm_up_with_multistep_lr = (
            lambda iter: (1 - self.warmup_ratio) * iter / self.warmup_iters + self.warmup_ratio
            if iter <= self.warmup_iters
            else 0.1 ** len([m for m in milestones if m <= iter])
        )
        scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer=self.optimizer, lr_lambda=warm_up_with_multistep_lr)

        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
