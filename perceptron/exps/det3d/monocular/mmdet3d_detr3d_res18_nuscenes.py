"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no $NEG_TAGS -- python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_detr3d_adamonecycle_nuscenes/2021-12-08T14:28:33/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
mAP: 0.1764
mATE: 0.9518
mASE: 0.2935
mAOE: 0.8137
mAVE: 1.1216
mAAE: 0.3065
NDS: 0.2517
Eval time: 90.6s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.357   0.715   0.162   0.150   1.586   0.311
truck   0.116   0.982   0.252   0.316   1.317   0.317
bus     0.111   1.054   0.266   0.361   2.533   0.533
trailer 0.027   1.253   0.278   0.904   0.442   0.072
construction_vehicle    0.009   1.230   0.488   1.690   0.128   0.371
pedestrian      0.274   0.813   0.298   1.148   0.857   0.420
motorcycle      0.146   0.902   0.289   1.120   1.484   0.340
bicycle 0.152   0.781   0.275   1.420   0.625   0.088
traffic_cone    0.302   0.799   0.338   nan     nan     nan
barrier 0.270   0.988   0.289   0.213   nan     nan
"""
import torch.distributed as dist

from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import (
    DataConfigs,
    DataLoader,
    Detr3D,
    DistributedSampler,
)
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import Exp as BaseExp
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import InfiniteSampler
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import ModelConfigs as OriginModelConfigs
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_adamw_nuscenes import NuScenesMonoDatasetWithEval


class ModelConfigs(OriginModelConfigs):
    img_norm_cfg = dict(mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)
    img_backbone = dict(
        type="ResNet",
        depth=18,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=dict(type="BN", requires_grad=False),
        norm_eval=True,
        style="pytorch",
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet18"),
    )
    img_neck = dict(type="FPN", in_channels=[64, 128, 256, 512], out_channels=256, num_outs=4)

    pretrained = None


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-4
        self.dump_interval = 1

    def _configure_model(self):
        model = Detr3D(
            img_backbone=ModelConfigs.img_backbone,
            img_neck=ModelConfigs.img_neck,
            pts_bbox_head=ModelConfigs.pts_bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.train_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model

    def _configure_train_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(
            ModelConfigs.class_names,
            ModelConfigs.attribute_names,
            training=True,
            point_cloud_range=DataConfigs.point_cloud_range,
            img_norm_cfg=ModelConfigs.img_norm_cfg,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        dataset = NuScenesMonoDatasetWithEval(
            ModelConfigs.class_names,
            ModelConfigs.attribute_names,
            training=False,
            point_cloud_range=DataConfigs.point_cloud_range,
            img_norm_cfg=ModelConfigs.img_norm_cfg,
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
