"""
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no $NEG_TAGS -- python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml
(ps.没有设置NEG_TAGS可以去掉$NEG_TAGS)

2. test:
python3 perceptron/exps/det3d/monocular/mmdet3d_detr3d_adamonecycle_nuscenes.py -d 0-7 -b 1 -e 24 --no-clearml --eval --ckpt outputs/mmdet3d_detr3d_adamonecycle_nuscenes/2021-12-08T14:28:33/dump_model/checkpoint_epoch_23.pth

3. metric:
epoch_23
"""
from bisect import bisect_right

import torch.distributed as dist
from torch import optim

from perceptron.engine.cli import Det3DCli
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_res18_nuscenes import Detr3D
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_res18_nuscenes import Exp as BaseExp
from perceptron.exps.det3d.monocular.mmdet3d_detr3d_res18_nuscenes import ModelConfigs as OriginModelConfigs


class ModelConfigs(OriginModelConfigs):
    img_backbone = dict(
        type="ResNet",
        depth=18,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=dict(type="BN", requires_grad=False),
        norm_eval=True,
        style="pytorch",
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet18"),
        with_cp=True,  # gradient checkpoint
    )


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=1, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.lr = 2e-4
        self.dump_interval = 1

    def _configure_model(self):
        model = Detr3D(
            img_backbone=ModelConfigs.img_backbone,
            img_neck=ModelConfigs.img_neck,
            pts_bbox_head=ModelConfigs.pts_bbox_head,
            train_cfg=ModelConfigs.train_cfg,
            test_cfg=ModelConfigs.train_cfg,
            pretrained=ModelConfigs.pretrained,
            class_names=ModelConfigs.class_names,
        )
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()
        return model

    def _configure_lr_scheduler(self):
        iters_per_epoch = len(self.train_dataloader)
        scheduler = MultiStepLRwithWarmup(
            optimizer=self.optimizer,
            warmup_iters=200,
            warmup_ratio=1 / 3,
            milestones=[iters_per_epoch * 16, iters_per_epoch * 22],
            gamma=0.1,
        )
        return scheduler


class MultiStepLRwithWarmup(optim.lr_scheduler.MultiStepLR):
    def __init__(self, optimizer, warmup_iters, warmup_ratio, milestones, gamma=0.1, last_epoch=-1, verbose=False):
        super().__init__(optimizer, milestones, gamma, last_epoch, verbose)
        self.warmup_iters = warmup_iters
        self.warmup_ratio = warmup_ratio

    def _get_closed_form_lr(self):
        milestones = list(sorted(self.milestones.elements()))
        if self.last_epoch < self.warmup_iters:
            warmup_ratio = (1 - self.warmup_ratio) * self.last_epoch / max(self.warmup_iters, 1) + self.warmup_ratio
            return [
                base_lr * warmup_ratio * self.gamma ** bisect_right(milestones, self.last_epoch)
                for base_lr in self.base_lrs
            ]
        else:
            return [base_lr * self.gamma ** bisect_right(milestones, self.last_epoch) for base_lr in self.base_lrs]


if __name__ == "__main__":
    Det3DCli(Exp).run()
