# once 结果
# |AP@50       |overall     |0-30m       |30-50m      |50m-inf     |
# |Vehicle     |80.13       |89.45       |74.64       |62.13       |
# |<PERSON><PERSON><PERSON><PERSON>  |59.07       |68.10       |49.95       |29.93       |
# |Cyclist     |73.67       |83.68       |68.95       |51.65       |
# |mAP         |70.96       |80.41       |64.51       |47.90       |


import easydict
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import CenterPoints as BaseFCos
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import Exp as BaseExp
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import ModelConfigs as BaseConfigs
from perceptron.exps.det3d.det3d_center_point_exp_better_fcos import DataConfigs
from perceptron.layers.head.det3d import IouAwareGenProposals, CenterHeadIouAware, FCOSAssigner
from perceptron.layers.losses.det3d import CenterNetRegLoss, <PERSON><PERSON><PERSON><PERSON><PERSON>
from perceptron.engine.cli import Det3DCli


class IouConfigs(BaseConfigs):
    densehead_common_heads = easydict.EasyDict(
        {
            "iou": [1, 2],
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
        }
    )
    densehead_loss_iou_weight = 5.0
    proposal_iou_aware_list = [0.65, 0.65]


class FCosIouAware(BaseFCos):
    def __init__(self, model_config, data_config):
        super().__init__(model_config=model_config, data_config=data_config)
        self.dense_head = self.build_dense_head()

    def build_dense_head(self):
        target_assigner = FCOSAssigner(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )

        proposal_layer = IouAwareGenProposals(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            iou_aware_list=self.model_config.proposal_iou_aware_list,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
        )

        dense_head_module = CenterHeadIouAware(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            out_size_factor=self.model_config.densehead_out_size_factor,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            iou_weight=self.model_config.densehead_loss_iou_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit", FocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma)
            )
            m.add_module("crit_reg", CenterNetRegLoss())
            m.add_module("crit_iou_aware", CenterNetRegLoss())

        _build_losses(dense_head_module)

        return dense_head_module


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)

    def _configure_model(self):
        model = FCosIouAware(
            model_config=IouConfigs(),
            data_config=DataConfigs(),
        )
        return model


if __name__ == "__main__":
    Det3DCli(Exp).run()
