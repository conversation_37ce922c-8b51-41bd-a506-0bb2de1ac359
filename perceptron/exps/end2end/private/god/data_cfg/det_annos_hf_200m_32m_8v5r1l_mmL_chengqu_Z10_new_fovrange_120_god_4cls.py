from perceptron.data.det3d.modules.annotation import AnnotationGOD
import copy

from perceptron.data.det3d.source.config import Z10
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_z10_8v5r1l_new import (
    _SENSOR_NAMES,
    _LIDAR_LIST,
    _CAR,
)
from perceptron.data.det3d.modules import (
    UndistortWarp,
    LidarBase,
    ImageSimFov,
)
import numpy as np
from perceptron.utils.env import get_cluster
from refile import smart_open
from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)
from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_god import (
    category_map_god,
    category_map_reverse_god,
    class_names_god,
)
from perceptron.data.det3d.modules.evaluation import EvaluationV3
from perceptron.data.det3d.modules.evaluation_all import InferenceMultitask

_CAMERA_LIST = [
    "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
    "cam_front_120_sim_fov70",
    "cam_front_30",
    "cam_back_70",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
    "cam_back_left_100_sim_fov104",
    "cam_back_right_100_sim_fov104",
]


lidar_cfg = dict(
    type=LidarBase,
    car=_CAR,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[4],  # 4 在hf/Z10中对应前视速腾M1P lidar
)


point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)

annotation_cfg = dict(
    god=dict(
        type=AnnotationGOD,
        category_map=category_map_god,
        class_names=class_names_god,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        HF=False,
        fixyaw=False,
    ),
)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)

target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))
_CAMERA_UNDISTORT_FUNC_Z10 = dict(
    cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    # front and back camera
    cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120_sim_fov70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
)

image_cfg = dict(
    type=ImageSimFov,
    car=_CAR,
    camera_names=_CAMERA_LIST,
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC_Z10,
    target_resolution=(1920, 1080),
)


_CAR = Z10
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["debug_sample_track"],
        only_key_frame=True,
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=image_cfg,
    # radar=radar_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
    roi_mask=[-32, -80, 32, 120],
)

val_dataset_god_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_god_cfg.update(mode="god_val")
val_dataset_god_cfg["annotation"]["god"]["label_key"] = "labels"
val_dataset_god_cfg["annotation"]["god"]["occlusion_threshold"] = -1
val_dataset_god_cfg["annotation"]["god"]["with_occlusion"] = False
val_dataset_god_cfg["annotation"]["god"]["soft_occ_threshold"] = 0.4
val_dataset_god_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
val_dataset_god_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
val_dataset_god_cfg["image"][
    "target_extrinsic"
] = "s3://mtx-qy/e2e/perceptron/pretrain/gongjiahao-share/end2end/calib/target_extrinsic_z08.npz"
val_dataset_god_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse_god,
        dump_det_results=False,
        eval_cfg_l3=None,
        eval_cfg_l2=None,
        extra_eval_cfgs=["god_cam_l3_cq"],
    )
)

val_dataset_god_cfg.update(
    inferer=dict(
        type=InferenceMultitask,
        category_map=category_map_reverse_god,
        dump_det_results=False,
        class_names=[
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "motorcycle",
            "bicycle",
            "tricycle",
            "cyclist",
            "pedestrian",
            # "masked_area",
        ],
    )
)
