"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4  --cpu=40 --gpu=8 --memory=300000 --max-wait-duration=1000m  --preemptible no  --charged-group=galvatron  --topo-group=yes --positive-tags=2080ti -- python3 perceptron/exps/end2end/private/object/track/track_private_base_exp_8v5r1l_y200x32_urban_highway.py        --no-clearml -b 1 -e 28 --amp --pretrained_model s3://wyh-share/ckpts/det_map/fusion__det_only_8v5r1l_mm_gs_cq_bsl_9w6_y200m_x32m_80e_dn_vr_loadfs_ft40e_fovroimask_laug/2024-09-18T22:16:12/dump_model/checkpoint_epoch_30.pth   --sync_bn 4


ckpt: s3://gxt-share-qy/e2e-ckpt/deformable_version__track_private_base_exp_8v1l_deformable_z10/2025-04-17T20:36:44/dump_model/checkpoint_epoch_19.pth

2025-04-21 18:29:34.359 | INFO     | perceptron_eval.detection.reporter:dump_pretty_table:101 - Config:e2e_l3_far_32m_front

2025-04-21 18:29:34.360 | INFO     | perceptron_eval.detection.reporter:dump_pretty_table:102 - mAP:0.6523097349235024

2025-04-21 18:29:34.360 | INFO     | perceptron_eval.detection.reporter:dump_pretty_table:103 - |-----------------|-------|--------------------|--------------------|---------------------|---------------------|---------------------|
---------------------|---------------------|---------------------|---------------------|
| merged_category | count |         AP         |     max_recall     |      trans_err      |     trans_err_x     |     trans_err_y     |      scale_err      |      orient_err     |     speed_err_x     |     speed_err_y     |
|-----------------|-------|--------------------|--------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|
|       Bus       |  365  | 0.7839049713372731 |       0.794        | 0.37337859162960013 | 0.10148217343436233 | 0.34206625516084826 |  0.0991254802397726 | 0.02012065530710897 | 0.10699263740402473 |  2.2909887634928943 |
|       Car       | 33826 | 0.7457383156759448 |       0.765        | 0.31800481273613485 | 0.12975118266304053 | 0.25704232797931964 | 0.08711147156473446 | 0.04285841667631168 |  0.5112659398991908 |  1.2915459054511553 |
|      Cycle      |  7130 | 0.5446261771938166 | 0.5700000000000001 |  0.424813325183364  | 0.20584281956970296 |  0.3191366134312492 | 0.15014517984137482 | 0.10356983511874271 |  0.6420879002410957 |  0.9770814472566633 |
|    Pedestrian   |  4694 | 0.4893876793899745 |       0.531        | 0.41502331779243046 | 0.24986061948619506 | 0.26569731326651785 |  0.1998236103586436 |  0.3613416898379883 |  0.2823421866281196 | 0.40990937994621535 |
|     Tricycle    |  2242 | 0.6327011788163037 |       0.666        | 0.43870613298734706 | 0.18699135137979314 | 0.35355137112076723 | 0.19592903088600144 |  0.0674333520391926 | 0.37943182851227936 |  0.8544476467649719 |
|      Truck      |  6342 | 0.7175000871277014 |       0.754        |  0.4509694992694904 | 0.15741282388625613 |  0.3814998083718882 |  0.1542495277944224 | 0.04237497789079337 |  0.4421627020268369 |  1.3688321896304052 |
|-----------------|-------|--------------------|--------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|


"""
import os
import sys
import refile
import torch
import mmcv
import numpy as np
from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler
import copy
from perceptron.exps.end2end.private.mot_god.model_cfg.track_model_cfg_8v1l_sprase_y120x32m_urban_god_repvgg import (
    MODEL_CFG,
)
from perceptron.exps.base_exp import BaseExp
from torch.utils.data import DistributedSampler
from perceptron.exps.end2end.private.object.data_cfg.track_annos_p177_reorg_y120m import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)

from perceptron.exps.end2end.private.mot_god.data_cfg.track_annos_z10_reorg_y120m_god import (
    base_dataset_cfg as DATA_TRAIN_CFG_Z10,
    val_dataset_cfg as DATA_VAL_CFG_Z10,
    val_dataset_god_cfg,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import GroupEachSampleInBatchSampler
from perceptron.models.end2end.perceptron.perceptron_multitask import VisionEncoder_MultiTask
from perceptron.data.det3d.private.multi_task_dataset import (
    FlexibleMultiTaskIntervalSampler,
    get_dataset_boundaries,
    collate_fn_wrapper,
)

SOFT_OCC_THRESHOLD = 0.4


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.DEBUG = os.environ.get("DEBUG", "False").lower() in ("true", "1")
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.cfg_path = os.path.basename(os.path.abspath(__file__))
        # self.lr = 1e-3 * 0.2
        self.lr = 1e-4  #
        self.init_scale = 2
        self.print_interval = 50
        self.num_keep_latest_ckpt = 2
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 2. Dataset and model configuration
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)

        self.data_train_cfg_cq_p177_human = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg_cq_p177_human["loader"]["datasets_names"] = [
            # "p177_human_batch1_city_normal_21k",
            # "p177_human_batch2_city_normal_25k",
            # "P177_human_batch3_city_normal_80k"
            "p177_human_20"
        ]
        self.data_train_cfg_cq_p177_human["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_cq_p177_human["annotation"]["box"]["occlusion_threshold"] = 1  # 因为没有刷遮挡属性 所以先用-1 训练一下
        self.data_train_cfg_cq_p177_human["num_frames_per_sample"] = 1  # TODO
        self.data_train_cfg_cq_p177_human["annotation"]["box"]["with_occlusion"] = True
        self.data_train_cfg_cq_p177_human["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        self.data_train_cfg_cq_p177_human["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_cq_p177_human["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_cq_p177_human["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_cq_p177_human["annotation"]["box"]["with_plain_velocity"] = True

        # # Z10
        self.data_train_cfg_cq_z10 = mmcv.Config(DATA_TRAIN_CFG_Z10)
        # self.data_train_cfg_cq_z10["loader"]["datasets_names"] = ["Z10_label_0207_7w"]
        # # self.data_train_cfg_cq_z10["loader"]["datasets_names"] = ["od_20"]
        # self.data_train_cfg_cq_z10["annotation"]["box"]["label_key"] = "labels"
        # self.data_train_cfg_cq_z10["annotation"]["box"]["occlusion_threshold"] = 1
        # self.data_train_cfg_cq_z10["num_frames_per_sample"] = 1 # TODO
        # self.data_train_cfg_cq_z10["loader"]["only_key_frame"] = True
        # self.data_train_cfg_cq_z10["annotation"]["box"]["with_occlusion"] = True
        # self.data_train_cfg_cq_z10["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        # self.data_train_cfg_cq_z10["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        # self.data_train_cfg_cq_z10["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        # self.data_train_cfg_cq_z10["image"]["target_extrinsic"] = "s3://xwh-data/camera_target_extrinsics/target_extrinsic_all_P177_new.npz"
        # self.data_train_cfg_cq_z10["annotation"]["box"]["with_plain_velocity"] = True

        # self.data_train_cfg_z10_od = copy.deepcopy(self.data_train_cfg_cq_z10)
        # self.data_train_cfg_z10_od["annotation"].pop("god")

        # god
        self.data_train_cfg_z10_god = copy.deepcopy(self.data_train_cfg_cq_z10)
        self.data_train_cfg_z10_god["num_frames_per_sample"] = 1
        self.data_train_cfg_z10_god["annotation"].pop("box")
        self.data_train_cfg_z10_god["loader"]["datasets_names"] = ["god_train_352keyclips"]
        self.data_train_cfg_z10_god["annotation"]["god"]["label_key"] = "labels"
        self.data_train_cfg_z10_god["annotation"]["god"]["occlusion_threshold"] = 1  # no occlusion
        self.data_train_cfg_z10_god["annotation"]["god"]["with_occlusion"] = True
        self.data_train_cfg_z10_god["loader"]["only_key_frame"] = True
        self.data_train_cfg_z10_god["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_z10_god["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_z10_god["annotation"]["god"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD

        self.data_val_god_cfg = mmcv.Config(val_dataset_god_cfg)
        self.data_val_god_cfg["loader"]["datasets_names"] = ["god_val"]
        self.data_val_god_cfg["num_frames_per_sample"] = 1
        self.data_val_god_cfg["annotation"].pop("box")

        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG_Z10)
        self.data_val_cfg["annotation"].pop("god")

        self.data_val_cfg_177od = mmcv.Config(DATA_VAL_CFG)
        self.eval_name = "P177_BMK"
        self.data_val_cfg_177od["loader"]["datasets_names"] = ["P177_BMK"]
        self.data_val_cfg_177od["annotation"]["box"]["label_key"] = "labels"
        self.data_val_cfg_177od["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_cfg_177od["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_cfg_177od["annotation"]["box"]["occlusion_threshold"] = 1

        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.use_ray_nms = False
        self.data_val_god_cfg["loader"]["only_key_frame"] = True

        # 3. other configuration change in this function
        self._change_cfg_params()

        self.train_data = {
            "box": self.data_train_cfg_cq_p177_human["loader"]["datasets_names"],
            "god": self.data_train_cfg_z10_god["loader"]["datasets_names"],
        }
        self.eval_name = "Z10_eval_od_god"
        self.val_data = {
            # "box": self.data_val_cfg["loader"]["datasets_names"],
            "box": self.data_val_cfg_177od["loader"]["datasets_names"],
            "god": self.data_val_god_cfg["loader"]["datasets_names"],
        }
        self.task_flag = None

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.0
        self.model_cfg.num_query = 0 + 250 + 50
        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 250 + 50

        self.model_cfg["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.num_near_query = 50

        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_lidar"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_img"] = True
        self.model_cfg.tracking_module.hist_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.spatial_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.fut_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1

        self.model_cfg.tracking_module["hist_len"] = 5
        self.model_cfg.tracking_module["use_timestamp"] = True
        self.model_cfg["use_relative_ts"] = True
        self.model_cfg["contrastive"] = True

        # freeze backbone
        self.model_cfg["train_backbone"] = False
        self.model_cfg["freeze_bn"] = True  # 多帧

        # velocity related
        self.model_cfg.tracking_module["with_velocity"] = True

        # occlusion related
        self.model_cfg["keep_pos"] = True
        self.model_cfg["disappear_time"] = 3
        # with occlusion prediction
        self.model_cfg["tracking_module"]["with_occlusion"] = True
        self.model_cfg["track_loss"]["loss_occlusion"] = True

        # more track loss
        self.model_cfg["with_roi_mask"] = True
        self.model_cfg["track_loss"]["contrastive_mask_fix"] = True
        self.model_cfg["track_loss"]["hard_k"] = 1

        # refinement deformable
        self.model_cfg["det_head"]["refine_reg_branch"] = True

        # lightened prediction module
        self.model_cfg["tracking_module"]["fut_temporal_transformer"]["decoder"]["num_layers"] = 1
        self.model_cfg["tracking_module"]["spatial_transformer"]["decoder"]["num_layers"] = 1

        self.model_cfg.runtime_tracker = dict(
            output_threshold=0.1,
            score_threshold=0.4,
            record_threshold=0.4,
            # max_age_since_update=3,
            max_age_since_update=0,  # eval
            drop_probability=0.2,
            fp_ratio=0.1,
            hist_fp_num=9,
            hist_fp_memory_len=2000,
            fp_threshold=0.1,
            fp_cls_limit=True,
        )

        self.data_val_cfg["loader"]["datasets_names"] = [
            "bmk_0508"
        ]  # ["gaosu"]#["bmk_0508"]  # ["z1_label_1230_bmk_qy"]
        self.data_val_cfg["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_val_cfg["lidar"]["lidar_names"] = [
            "rfu_front_2_lidar",
            "fuser_lidar",
        ]
        self.data_val_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_val_cfg["image"][
            "target_extrinsic"
        ] = "s3://xwh-data/camera_target_extrinsics/target_extrinsic_all_P177_new.npz"

        # god model
        self.model_cfg.god_head.det_head.bbox_coder.score_threshold = 0.1
        self.model_cfg.god_head.num_query = 0 + 300 + 100
        self.model_cfg.god_head["det_head"]["init_radar_num_query"] = 0
        self.model_cfg.god_head["det_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg.god_head["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.god_head.num_near_query = 100

        self.model_cfg["radar_encoder"] = None
        self.model_cfg.god_head["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1][
            "with_lidar"
        ] = True
        self.model_cfg.god_head["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1][
            "with_img"
        ] = True
        self.model_cfg.god_head["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg.god_head["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg.god_head["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg.god_head["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask

        self.model_cfg.god_head["det_head"]["refine_reg_branch"] = True

        self.model_cfg["train_backbone"] = False
        self.model_cfg["freeze_modules"] = ["img_neck", "vfe", "obstacle_head"]
        self.model_cfg["freeze_bn"] = True

        self.multi_pretrain = {
            "s3://xwh-data/OD_GOD_model/P177_OD_0626_repvgg_opt.pth": ["backbone", "img_neck", "vfe", "obstacle_head"],
            # 's3://ppe-qy/songfan/god/models/det__det_private_base_exp_8v1l_y300x32_deformable_120m_400q_refine_28w_fixoptim_god_head_mini/2025-06-20T18:46:00/dump_model/checkpoint_epoch_39.pth': ['god_head']
            # '/data/Envs/megvii/outputs/mot_god__track_8v1l_deformable_p177_z10_hf_master_vel_god_train_repvgg_lite_300q/2025-06-26T20:15:40/dump_model/checkpoint_epoch_59.pth': ['god_head']
            "/data/Envs/megvii/outputs/mot_god__track_8v1l_deformable_p177_z10_hf_master_vel_god_train_repvgg_lite_300q/2025-06-26T23:13:57/checkpoint_epoch_59.pth": [
                "god_head"
            ],
        }

    def _configure_model(self):
        model = VisionEncoder_MultiTask(
            model_cfg=self.model_cfg,
        )
        # n_parameters = sum(p.numel() for p in model.parameters() if p.requires_grad)
        # # logger.info(f'Number of params: {n_parameters}')
        # print(f'Number of params: {n_parameters}')
        # import sys
        # sys.exit()
        return model

    def _init_scene_flag(self, datasets):
        """
        organize data in scene for multi gpu eval
        """

        scene_flag = []
        scene_frame_idx = []
        for dataset in datasets:
            cumulative_size = dataset.loader_output["cummulative_sizes"]
            scene_len = np.array([0] + cumulative_size)
            scene_len = scene_len[1:] - scene_len[:-1]

            for i, s in enumerate(scene_len):
                scene_flag.extend([i] * s)
                scene_frame_idx.extend(list(range(s)))
        scene_flag = np.array(scene_flag)  # must be np.array
        scene_frame_idx = np.array(scene_frame_idx)
        assert len(scene_flag) == len(scene_frame_idx)
        scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )

        return scene_flag, scene_frame_idx, scene_order

    def _configure_train_dataloader(self):
        train_dataset_od = PrivateE2EDataset(**self.data_train_cfg_cq_p177_human)

        train_dataset_god = PrivateE2EDataset(
            **self.data_train_cfg_z10_god,
        )

        dataset_boundaries = get_dataset_boundaries([train_dataset_od, train_dataset_god])
        train_dataset = torch.utils.data.ConcatDataset([train_dataset_od, train_dataset_god])

        scene_flag = []
        scene_frame_idx = []
        pre_sum = 0
        for dataset in train_dataset.datasets:
            scene_flag.append(dataset.scene_flag + pre_sum)
            pre_sum = scene_flag[-1][-1] + 1
            scene_frame_idx.append(dataset.scene_frame_idx)
        scene_flag = np.concatenate(scene_flag)
        scene_frame_idx = np.concatenate(scene_frame_idx)
        setattr(train_dataset, "scene_flag", scene_flag)
        setattr(train_dataset, "scene_frame_idx", scene_frame_idx)

        train_dataset.batch_postcollate_fn = train_dataset_od.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset_od.batch_preforward_fn
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn),
            sampler=FlexibleMultiTaskIntervalSampler(
                train_dataset,
                dataset_boundaries,
                per_gpu_dataset_batch_sizes=[1, 1],  # TODO
                shuffle=True,
                intervals=[1, 1],  # TODO
                drop_last=False,
            ),
            pin_memory=True,
            num_workers=8,  # 此处设置为0，可以通过Ctrl-C 看出问题出现在哪里
        )

        return train_loader

    @property
    def val_dataloader(self):
        if self.task_flag == "box" and "_val_od_dataloader" not in self.__dict__:
            self._val_od_dataloader = self._configure_val_od_dataloader()
        elif self.task_flag == "occ" and "_val_occ_dataloader" not in self.__dict__:
            self._val_occ_dataloader = self._configure_val_occ_dataloader()
        elif self.task_flag == "god" and "_val_god_dataloader" not in self.__dict__:
            self._val_god_dataloader = self._configure_val_god_dataloader()
        elif self.task_flag == "map" and "_val_map_dataloader" not in self.__dict__:
            self._val_map_dataloader = self._configure_val_map_dataloader()

        elif self.task_flag is None:
            # 默认初始化od的dataloader
            self._val_od_dataloader = self._configure_val_od_dataloader()

        if self.task_flag == "box" or self.task_flag is None:
            return self._val_od_dataloader
        elif self.task_flag == "occ":
            return self._val_occ_dataloader
        elif self.task_flag == "god":
            return self._val_god_dataloader
        elif self.task_flag == "map":
            return self._val_map_dataloader
        else:
            raise NotImplementedError(f"no supported task_flag: {self.task_flag}")

    def _configure_val_dataloader(self):
        raise NotImplementedError

    def _configure_val_od_dataloader(self):
        val_dataset = PrivateE2EDataset(
            # **self.data_val_cfg,
            **self.data_val_cfg_177od,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=False),
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_occ_dataloader(self):
        num_workers = 0 if self.DEBUG else 5
        val_dataset = PrivateE2EDataset(
            **self.data_val_occ_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=num_workers,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_god_dataloader(self):
        num_workers = 0 if self.DEBUG else 5
        val_dataset = PrivateE2EDataset(
            **self.data_val_god_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=num_workers,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=4,
            batch_sampler=GroupEachSampleInBatchSampler(val_dataset, shuffle=False, drop_last=False)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step_ori(self, batch):
        pred_dicts, _ = self.model(**batch)
        return pred_dicts

    @torch.no_grad()
    def test_step(self, batch):
        if self.model_cfg["det_head"] and self.model_cfg["god_head"]:
            outs_obstacle, outs_god_obstacle = self.model(**batch)
            if self.task_flag == "box":
                return outs_obstacle
            elif self.task_flag == "god":
                return self.test_od_step(outs_god_obstacle)
            else:
                raise NotImplementedError
        else:
            raise NotImplementedError

    @torch.no_grad()
    def test_od_step(self, pred_dicts):
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            # thresh=0.8,
                            thresh=0.1,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        from torch.optim import AdamW

        backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if "encoder" in name:
                backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW(
            [
                {"params": backbone_params, "lr": self.lr * 0.1, "weight_decay": 0.01},
                {"params": other_params, "lr": self.lr, "weight_decay": 0.01},
            ]
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0 / 3 * self.lr,
            end_lr=1e-6,  # eta_min
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
