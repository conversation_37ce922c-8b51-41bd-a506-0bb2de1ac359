import copy
import numpy as np
from refile import smart_open

from perceptron.data.det3d.source.config import Z10
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_z10_8v5r1l_new import (
    lidar as lidar_cfg,
    _SENSOR_NAMES,
)
from perceptron.data.det3d.modules import (
    LoaderSimFov,
    ImageSimFov,
    UndistortWarp,
    EvaluationE2E,
    MultiFrameImageAffineTransformation,
    AnnotationTrack,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
    EvaluationV3,
)
from perceptron.data.det3d.modules.annotation import AnnotationGOD

from .det_annos_hf_y200m_x32m_8v5r1l import (
    # category_map,
    category_map_reverse,
    class_names,
    # point_cloud_range,
)
from perceptron.utils.env import get_cluster
from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_god import (
    category_map_god,
    category_map_reverse_god,
    class_names_god,
)

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "suv": "car",
    "SUV": "car",
    "van": "car",
    "VAN": "car",
    "Van": "car",
    "皮卡": "car",
    "pika": "car",
    "cart": "car",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
    "遮挡": "occlusion",
    "短障碍物": "short_track",
    "大货车": "truck",
    "dahuoche": "truck",
    "dauhoche": "truck",
    "小货车": "truck",
    "xiaohuoche": "truck",
    "骑三轮车的人": "tricycle",
    "骑自行车的人": "cyclist",
    "骑摩托车的人": "cyclist",
    "et": "pedestrian",
    "儿童": "pedestrian",
    "成年人": "pedestrian",
    "蒙版": "masked_area",
    "mask": "masked_area",
    "正向蒙版": "masked_area",
    "负向蒙版": "masked_area",
    "拖挂": "truck",
    "tuogua": "truck",
    "其他非机动车": "other",
    "其他机动车": "other",
    "小动物类": "other",
    "大动物类": "other",
}

_CAR = Z10
# camera 被注释掉了
_CAMERA_LIST = [
    "cam_front_120",
    "cam_front_120_sim_fov70",
    "cam_front_30",
    "cam_back_70",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
    "cam_back_left_100_sim_fov104",
    "cam_back_right_100_sim_fov104",
]
Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)
target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))
_CAMERA_UNDISTORT_FUNC_Z10 = dict(
    cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    # front and back camera
    cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120_sim_fov70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
)

loader_cfg = dict(
    type=LoaderSimFov,
    car=Z10,
    camera_names=[
        "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
        "cam_front_120_sim_fov70",
        "cam_front_30",
        "cam_back_70",
        "cam_front_left_100_sim_fov104",
        "cam_front_right_100_sim_fov104",
        "cam_back_left_100_sim_fov104",
        "cam_back_right_100_sim_fov104",
    ],
    mode="train",
    datasets_names=["z10_label_1230_train", "Z10_label_0207_7w"],
    only_key_frame=False,
    rebuild=False,
)

image_cfg = dict(
    type=ImageSimFov,
    car=loader_cfg["car"],
    camera_names=loader_cfg["camera_names"],
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC_Z10,
)

point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)

annotation_cfg = dict(
    box=dict(
        type=AnnotationTrack,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        with_predict=False,
        HF=False,
        hist_traj_len=20,
        fut_traj_len=13,
    ),
    god=dict(
        type=AnnotationGOD,
        category_map=category_map_god,
        class_names=class_names_god,
        occlusion_threshold=-1,
        filter_outlier_boxes=False,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        HF=False,
        fixyaw=False,
    ),
)

base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=3,
    loader=loader_cfg,
    lidar=lidar_cfg,
    image=image_cfg,
    # radar=radar_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
    roi_mask=[-32, -80, 32, 120],
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
bmk_name = "z1_label_1230_bmk_qy" if get_cluster() == "https://qy.machdrive.cn" else "z1_label_1230_bmk"
val_dataset_cfg["loader"].update(datasets_names=[bmk_name])
val_dataset_cfg["annotation"]["box"].update(occlusion_threshold=1)  # 这里需要确认下!
val_dataset_cfg.update(num_frames_per_sample=1)
# val_dataset_cfg["radar"]["with_virtual_radar"] = False
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationE2E,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="cam_l3_front",
        eval_cfg_l2="e2e_l3_far_32m_front",
    )
)
val_dataset_cfg.update(
    eval_cfg=dict(
        evaluation=dict(interval=12),
        eval_ppl=["detection", "tracking", "prediction"],
        format_only=False,
        eval=True,
    )
)

val_dataset_god_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_god_cfg.update(mode="god_val")
val_dataset_god_cfg["annotation"]["box"]["label_key"] = "labels"
val_dataset_god_cfg["annotation"]["box"]["occlusion_threshold"] = -1
val_dataset_god_cfg["annotation"]["box"]["with_occlusion"] = False
val_dataset_god_cfg["annotation"]["box"]["soft_occ_threshold"] = 0.4
val_dataset_god_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
val_dataset_god_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
val_dataset_god_cfg["image"][
    "target_extrinsic"
] = "s3://mtx-qy/e2e/perceptron/pretrain/gongjiahao-share/end2end/calib/target_extrinsic_z08.npz"
val_dataset_god_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse_god,
        dump_det_results=False,
        eval_cfg_l3=None,
        eval_cfg_l2=None,
        extra_eval_cfgs=["god_cam_l3_cq"],
    )
)
