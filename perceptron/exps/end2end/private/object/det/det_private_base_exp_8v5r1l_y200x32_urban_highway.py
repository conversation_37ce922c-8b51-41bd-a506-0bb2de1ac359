""" HF: 8v5r1l
Description:
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/object/det/det_private_base_exp_8v5r1l_y200x32_urban_highway.py --no-clearml -b 3 -e 10 --amp --sync_bn 1 --pretrained_model s3://yrn-share/checkpoint/fusion__det_only_8v5r1l_mm_gs_cq_bsl_9w6_y200m_x32m_80e_dn_vr_loadfs_ft40e_fovroimask_laug_warp_cq_addcqdet_track_prelabel_ft10e_test1_notonlykey_1012/dump_model/checkpoint_epoch_9.pth
Log + Ckpt: s3://wyh-share/ckpts/det_map/v1130__det_only_8v5r1l_mm_gs_cq_load_yrnsota_warp15_add23_24label_filterbmk_roimask/2024-11-26T18:29:55/dump_model/checkpoint_epoch_9.pth
Author: WYH
Data: 2024-11-29
"""
import sys
import refile
import torch
import mmcv
import copy
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.exps.end2end.private.object.model_cfg.det_model_cfg_8v5r1l_y200x32m import MODEL_CFG
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.end2end.private.object.data_cfg.det_annos_hf_y200m_x32m_8v5r1l import (
    base_dataset_cfg as DATA_TRAIN_CFG_GS,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.end2end.private.object.data_cfg.det_annos_hf_y200m_x32m_8v5r1l_chengqu import (
    base_dataset_cfg as DATA_TRAIN_CFG_CQ,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfinitePartIntervalSampler
from torch.utils.data import DistributedSampler
from perceptron.data.det3d.modules.radar.radar_hf_virtual_aug import HFRadarVirtualAug
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder
from perceptron.data.det3d.modules import MultiFrameImageAffineTransformationWarp

seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 3e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 3
        self.dump_interval = 1
        self.lr_scale_factor = {
            "camera_encoder.img_backbone": 0.1,
            "camera_encoder.img_neck": 0.1,
            "lidar_encoder": 0.1,
        }  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35
        print("!" * 10, "lr  changed!!!!!!!!!!!!")

        # 2. Dataset and model configuration
        DATA_TRAIN_CFG_GS["pipeline"]["modal_mask"]["drop_ratio"] = [1.0, 1.0]
        DATA_TRAIN_CFG_CQ["pipeline"]["modal_mask"]["drop_ratio"] = [1.0, 1.0]
        DATA_TRAIN_CFG_CQ["pipeline"]["object_range_filter"]["box_range"] = [-32.0, -80.0, -5.0, 32.0, 120.0, 3.0]

        # -------------------------------------高速good radar labels-------------------------------------
        self.data_train_cfg_gs = mmcv.Config(DATA_TRAIN_CFG_GS)
        self.data_train_cfg_gs["loader"]["datasets_names"] = [
            # 23年回流的23年数据
            "0901_0926_filter_bmk",
            # 24年回流的24年数据
            "labeled_gs_20241126_car_15_204",
        ]
        self.data_train_cfg_gs["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_gs["annotation"]["box"]["occlusion_threshold"] = -1
        self.data_train_cfg_gs["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
        self.data_train_cfg_gs["pipeline"]["ida_aug"]["target_extrinsic"] = "target_extrinsic_all_hf15_new.npz"
        self.data_train_cfg_gs["roi_mask"] = [
            -32.0,
            -80.0,
            32.0,
            204.8,
        ]
        self.data_train_cfg_gs["pipeline"]["ida_aug"]["two_stage_warp"] = True
        self.data_train_cfg_gs["image"]["two_stage_warp"] = True

        # -------------------------------------高速bad radar labels-------------------------------------
        self.data_train_cfg_gs_bad_radar = copy.deepcopy(self.data_train_cfg_gs)
        self.data_train_cfg_gs_bad_radar["loader"]["datasets_names"] = ["0720_0831"]
        self.data_train_cfg_gs_bad_radar["radar"]["type"] = HFRadarVirtualAug

        # -------------------------------------城区good radar labels-------------------------------------
        self.data_train_cfg_cq = mmcv.Config(DATA_TRAIN_CFG_CQ)
        self.data_train_cfg_cq["loader"]["datasets_names"] = [
            # 23年回流的23年数据
            "hf_label_det_cq_good_radar_1246json_filter",
            "hf_label_1410_jsons_goodradar_filter",
            # 24年回流的23年数据
            "hf_newlabel_20241105_jiushujuhuiliu_good_radar",
            "hf_newlabel_20241121-20241105_jiushujuhuiliu_good_radar",
            # 24年回流的24年数据
            "labeled_cq_20241126_car_11_2039",
            "labeled_cq_20241126_car_12_748",
            "labeled_cq_20241126_car_15_346",
        ]
        self.data_train_cfg_cq["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_cq["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_train_cfg_cq["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
        self.data_train_cfg_cq["pipeline"]["ida_aug"]["target_extrinsic"] = "target_extrinsic_all_hf15_new.npz"
        self.data_train_cfg_cq["roi_mask"] = [-32.0, -80.0, 32.0, 120.0]
        self.data_train_cfg_cq["pipeline"]["ida_aug"]["two_stage_warp"] = True
        self.data_train_cfg_cq["image"]["two_stage_warp"] = True

        # -------------------------------------城区bad radar labels-------------------------------------
        self.data_train_cfg_cq_bad_radar = copy.deepcopy(self.data_train_cfg_cq)
        self.data_train_cfg_cq_bad_radar["loader"]["datasets_names"] = [
            "hf_label_det_cq_bad_radar_938json_filter",
            "hf_label_1410_jsons_badradar_filter",
            "hf_newlabel_20241105_jiushujuhuiliu_bad_radar",
            "hf_newlabel_20241121-20241105_jiushujuhuiliu_bad_radar",
        ]
        self.data_train_cfg_cq_bad_radar["radar"]["type"] = HFRadarVirtualAug

        # -------------------------------------城区good radar prelabel-------------------------------------
        self.data_train_cfg_cq_prelabel = copy.deepcopy(self.data_train_cfg_cq)
        self.data_train_cfg_cq_prelabel["loader"]["datasets_names"] = [
            "hf_prelabel_0718_det_2103_goodradar_filter_rm20241121label",
            "hf_prelabel_0717_track_4441_goodradar_filter_rm20241121label",
        ]
        self.data_train_cfg_cq_prelabel["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_cq_prelabel["loader"]["only_key_frame"] = False

        # -------------------------------------城区bad radar prelabel-------------------------------------
        self.data_train_cfg_cq_bad_radar_prelabel = copy.deepcopy(self.data_train_cfg_cq_bad_radar)
        self.data_train_cfg_cq_bad_radar_prelabel["loader"]["datasets_names"] = [
            "hf_prelabel_0718_det_2103_badradar_filter_rm20241121label",
            "hf_prelabel_0717_track_4441_badradar_filter_rm20241121label",
        ]
        self.data_train_cfg_cq_bad_radar_prelabel["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_cq_bad_radar_prelabel["loader"]["only_key_frame"] = False

        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()

        self.data_val_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
        self.data_val_cfg["pipeline"]["ida_aug"]["target_extrinsic"] = "target_extrinsic_all_hf15_new.npz"
        self.data_val_cfg["pipeline"]["ida_aug"]["two_stage_warp"] = True
        self.data_val_cfg["image"]["two_stage_warp"] = True

        self.eval_name = "chengqu_2024_bmk"
        self.data_val_cfg["evaluator"]["eval_cfg_l3"] = "e2e_l3_far_32m_cq"
        self.data_val_cfg["evaluator"]["eval_cfg_l2"] = "cam_l3_cq"
        self.data_val_cfg["evaluator"]["extra_eval_cfgs"] = []
        self.data_val_cfg["loader"]["datasets_names"] = ["hf2024_chengqu_bmk"]  # ["bmk_new_withOCC"]

        # self.eval_name = 'gaosu_bmk'
        # self.data_val_cfg['evaluator']['eval_cfg_l3'] = 'e2e_l3_far_32m'
        # self.data_val_cfg['evaluator']['eval_cfg_l2'] = ''
        # self.data_val_cfg['evaluator']['extra_eval_cfgs'] = []
        # self.data_val_cfg["loader"]["datasets_names"] = ["bmk_new_withOCC"]  # ["bmk_new_withOCC"]

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.1
        self.model_cfg.num_query = 300
        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300

        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask

        self.data_val_cfg["loader"]["datasets_names"] = ["bmk_new_withOCC"]  # ["bmk_new_withOCC"]
        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(**self.data_train_cfg_gs)
        train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_gs_bad_radar)
        train_dataset3 = PrivateE2EDataset(**self.data_train_cfg_cq)
        train_dataset4 = PrivateE2EDataset(**self.data_train_cfg_cq_bad_radar)
        train_dataset5 = PrivateE2EDataset(**self.data_train_cfg_cq_prelabel)
        train_dataset6 = PrivateE2EDataset(**self.data_train_cfg_cq_bad_radar_prelabel)

        train_dataset = torch.utils.data.ConcatDataset(
            [train_dataset1, train_dataset2, train_dataset3, train_dataset4, train_dataset5, train_dataset6]
        )
        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfinitePartIntervalSampler(
                len(train_dataset),
                seed=self.seed if self.seed else 0,
                interval=16,
                _len_dataset_for_not_interval=len(train_dataset1)
                + len(train_dataset2)
                + len(train_dataset3)
                + len(train_dataset4),
                _len_dataset_for_interval=len(train_dataset5) + len(train_dataset6),
            )
            if dist.is_distributed()
            else None,
            pin_memory=False,
            num_workers=3,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        # for b in range(len(batch['imgs'])):
        #     if not (batch['imgs'][b][0][0][0]==batch['imgs'][b][0][0][0,0,0]).all(): # 如果图像没有被mask，应该用的roi mask
        #         batch['fov_boardline'][b] *= 0
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):

        pred_dicts, _ = self.model(**batch)
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
