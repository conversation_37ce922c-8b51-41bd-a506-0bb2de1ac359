""" Z10 8V1L
Description:  Trained with Z10 labeled
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/object/det/det_private_base_exp_8v1l_y300x32_deformable.py --no-clearml -b 4 -e 80 --amp --sync_bn 4 --pretrained_model /data/code/e2e/perceptron/pretrain/deformable_0116__det_only_8v1l_q300_n100_onlykey_z10label_fov104_20e_virtualcamera_lr2e-4_0118/2025-02-03T10:40:27/dump_model/checkpoint_epoch_39.pth
Author: GXT
Data: 2024-03-02

pretrained_model: s3://gxt-share-qy/pretrain/deformable_0116__det_only_8v1l_q300_n100_onlykey_z10label_fov104_20e_virtualcamera_lr2e-4_0118/2025-02-03T10:40:27/dump_model/checkpoint_epoch_39.pth

ckpt: s3://wyh-stepmind-public/8V1L-0312_add8w_finetune_50epoch.pth

2025-03-12 14:31:41.725 | INFO     | perceptron_eval.detection.reporter:dump_pretty_table:102 - Config:cam_l3_cq

2025-03-12 14:31:41.727 | INFO     | perceptron_eval.detection.reporter:dump_pretty_table:103 - mAP:0.9440330199876494

2025-03-12 14:31:41.727 | INFO     | perceptron_eval.detection.reporter:dump_pretty_table:104 - |-----------------|-------|--------------------|------------|---------------------|---------------------|---------------------|---------------------|---------------------|
| merged_category | count |         AP         | max_recall |      trans_err      |     trans_err_x     |     trans_err_y     |      scale_err      |      orient_err     |
|-----------------|-------|--------------------|------------|---------------------|---------------------|---------------------|---------------------|---------------------|
|       Bus       |  1034 | 0.9694380352779063 |   0.973    | 0.24050295981350153 | 0.08339672752530462 | 0.20789309620007013 | 0.10603312511096931 | 0.02808545519615814 |
|       Car       |  7113 | 0.9612012645040974 |    0.97    | 0.25962150134972845 | 0.10013371805733734 | 0.21742026748871876 | 0.07789340375948231 |  0.0360376145576824 |
|      Cycle      |  594  | 0.9492788808767442 |   0.973    | 0.28735798589547695 | 0.10458947474598661 |  0.2448617285160936 | 0.14793116542050275 | 0.07857822071740556 |
|    Pedestrian   |  340  | 0.8962138992918497 |   0.964    | 0.43286218530402754 | 0.21110992421042127 |  0.3238514028230716 | 0.19717469168372773 | 0.39406667772605025 |
|-----------------|-------|--------------------|------------|---------------------|---------------------|---------------------|---------------------|---------------------|

python3 perceptron/exps/end2end/private/object/det/det_private_base_exp_8v1l_y300x32_deformable.py --no-clearml -b 4 -e 80 --amp --sync_bn 1 --pretrained_model /data/code/e2e/perceptron/pretrain/deformable_0116__det_only_8v1l_q300_n100_onlykey_z10label_fov104_20e_virtualcamera_lr2e-4_0118/2025-02-03T10:40:27/dump_model/checkpoint_epoch_39.pth
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/object/det/det_private_base_exp_8v1l_y300x32_deformable.py --no-clearml -b 4 -e 80 --amp --sync_bn 4 --pretrained_model /data/code/e2e/perceptron/pretrain/deformable_0116__det_only_8v1l_q300_n100_onlykey_z10label_fov104_20e_virtualcamera_lr2e-4_0118/2025-02-03T10:40:27/dump_model/checkpoint_epoch_39.pth
python3 /data/code/e2e/perceptron/perceptron/exps/end2end/private/object/det/det_private_base_exp_8v1l_y300x32_deformable.py --eval -b 1 --ckpt /data/outputs/det__det_private_base_exp_8v1l_y300x32_deformable/2025-03-18T15:07:09/dump_model/checkpoint_epoch_36.pth

"""
import sys
import refile
import torch
import mmcv
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.exps.end2end.private.object.model_cfg.det_model_cfg_8v1l_sparse_y200x32m import MODEL_CFG
from perceptron.exps.base_exp import BaseExp

from perceptron.exps.end2end.private.object.data_cfg.det_annos_hf_200m_32m_8v5r1l_mmL_chengqu_Z10_new import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteSampler
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder

seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 1
        self.lr_scale_factor = {
            "camera_encoder.img_backbone": 0.001,
            "camera_encoder.img_neck": 0.01,
            "lidar_encoder": 0.001,
        }  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35
        print("!" * 10, "lr  changed!!!!!!!!!!!!")

        # 2. Dataset and model configuration

        # -------------------------------------Z10 label-------------------------------------
        self.data_train_cfg_cq_z10 = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg_cq_z10["loader"]["datasets_names"] = ["z10_label_1230_train", "Z10_label_0207_7w"]
        self.data_train_cfg_cq_z10["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_cq_z10["loader"]["only_key_frame"] = True
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()

        self.eval_name = "Z10_eval_bmk79"
        self.data_val_cfg["loader"]["datasets_names"] = ["z1_label_1230_bmk_qy"]  # ["bmk_new_withOCC"]
        self.data_val_cfg["evaluator"]["extra_eval_cfgs"] = ["e2e_l3_far_32m_cq", "cam_l3_cq"]
        self.data_val_cfg["annotation"]["box"]["label_key"] = "labels"
        self.data_val_cfg["image"][
            "target_extrinsic"
        ] = "/data/code/e2e/perceptron/pretrain/gongjiahao-share/end2end/calib/target_extrinsic_z08.npz"  # "/data/outputs/npz/target_extrinsic_z08.npz"

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.1
        self.model_cfg.num_query = 0 + 300 + 100
        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.num_near_query = 100

        self.model_cfg["radar_encoder"] = None
        # self.model_cfg["lidar_encoder"] = None
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_lidar"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_img"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask

        self.data_val_cfg["loader"]["datasets_names"] = ["1230_100bmk"]  # ["bmk_new_withOCC"]
        self.data_val_cfg["evaluator"]["extra_eval_cfgs"] = ["e2e_l3_far_32m_cq", "cam_l3_cq"]
        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset8 = PrivateE2EDataset(**self.data_train_cfg_cq_z10)
        train_dataset = train_dataset8
        train_dataset.batch_postcollate_fn = train_dataset8.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset8.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=8,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):

        pred_dicts, _ = self.model(**batch)
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
