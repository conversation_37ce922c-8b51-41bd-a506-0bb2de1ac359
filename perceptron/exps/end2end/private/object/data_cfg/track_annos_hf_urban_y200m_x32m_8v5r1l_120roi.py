from perceptron.data.det3d.modules.annotation import AnnotationTrack
import copy

from perceptron.data.det3d.source.config import HFCar9
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_hf_8v5r1l import (
    lidar as lidar_cfg,
    image as image_cfg,
    radar as radar_cfg,
    _CAMERA_LIST,
    _SENSOR_NAMES,
)
from perceptron.data.det3d.modules import (
    EvaluationE2E,
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)
from .det_annos_hf_y200m_x32m_8v5r1l import (
    category_map,
    category_map_reverse,
    class_names,
    # point_cloud_range,
)

point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)

annotation_cfg = dict(
    box=dict(
        type=AnnotationTrack,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        with_predict=False,
        HF=False,
        hist_traj_len=20,
        fut_traj_len=13,
    ),
)

_CAR = HFCar9
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=3,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["debug_sample_track"],
        only_key_frame=False,
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=image_cfg,
    radar=radar_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
    roi_mask=[-32, -80, 32, 120],
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["CAR9_BMK_OCC_DAY"])
val_dataset_cfg["annotation"]["box"].update(occlusion_threshold=1)  # 这里需要确认下!
val_dataset_cfg.update(num_frames_per_sample=1)
# val_dataset_cfg["radar"]["with_virtual_radar"] = False
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationE2E,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="cam_l3_front",
        eval_cfg_l2="e2e_l3_far_32m_front",
    )
)
val_dataset_cfg.update(
    eval_cfg=dict(
        evaluation=dict(interval=12),
        eval_ppl=["detection", "tracking", "prediction"],
        format_only=False,
        eval=True,
    )
)
