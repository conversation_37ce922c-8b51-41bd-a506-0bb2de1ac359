from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_hf_e2e import class_names
import numpy as np

CLASS_NAMES = class_names
_POINT_CLOUD_RANGE = [-32.0, 10.0, -5.0, 32.0, 122, 3.0]
_FRONT_LIDAR_POINT_CLOUD_RANGE = [-32.0, 10.0, -5.0, 32.0, 122, 3.0]
_VOXEL02_VOXEL_SIZE = [0.2, 0.2, 8]  # pointpillar
_VOXEL02_GRID_SIZE = [
    (_POINT_CLOUD_RANGE[3] - _POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_POINT_CLOUD_RANGE[4] - _POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]
_FRONT_LIDAR_VOXEL02_GRID_SIZE = [
    (_FRONT_LIDAR_POINT_CLOUD_RANGE[3] - _FRONT_LIDAR_POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_FRONT_LIDAR_POINT_CLOUD_RANGE[4] - _FRONT_LIDAR_POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_FRONT_LIDAR_POINT_CLOUD_RANGE[5] - _FRONT_LIDAR_POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]


_VOXEL02_GRID_SIZE = np.round(np.array(_VOXEL02_GRID_SIZE)).astype(np.int64)
_FRONT_LIDAR_VOXEL02_GRID_SIZE = np.round(np.array(_FRONT_LIDAR_VOXEL02_GRID_SIZE)).astype(np.int64)
_VOXEL02_OUT_SIZE_FACTOR = 2
_POST_CENTER_RANGE = [
    _POINT_CLOUD_RANGE[0] - 10,
    _POINT_CLOUD_RANGE[1] - 10,
    -10,
    _POINT_CLOUD_RANGE[3] + 10,
    _POINT_CLOUD_RANGE[4] + 10,
    10,
]

VOXEL02_DET_HEAD = dict(
    type="SparseE2EHead",
    in_channels=512,
    num_query=900,
    modal=["Camera"],
    depth_num=64,
    hidden_dim=256,
    downsample_scale=_VOXEL02_OUT_SIZE_FACTOR,
    grid_size=_VOXEL02_GRID_SIZE,
    front_lidar_grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    # check
    use_dn=False,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=len(CLASS_NAMES),
            class_names=CLASS_NAMES,
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=_POST_CENTER_RANGE,
        pc_range=_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=_VOXEL02_VOXEL_SIZE,
        num_classes=len(CLASS_NAMES),
        score_threshold=0.1,
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=3),
    transformer=dict(
        type="MOTDeformableTransformer",
        decoder=dict(
            type="DeformableTransformerDecoder",
            num_layers=6,
            transformerlayers=dict(
                type="DeformableTransformerDecoderLayer",
                batch_first=True,
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(
                        type="DeformableFeatureAggregationCuda",
                        embed_dims=256,
                        num_groups=8,
                        num_levels=1,
                        num_cams=8,  # TODO 与实际相机数量保持一致
                        dropout=0.1,
                        num_pts=13,
                        bias=2.0,
                    ),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
    use_roi_mask=False,
)


VOXEL02_TRAIN_CFG = dict(
    pts=dict(
        dataset="Private",
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=_POINT_CLOUD_RANGE,
        ),
        pos_weight=-1,
        gaussian_overlap=0.1,
        min_radius=2,
        grid_size=_VOXEL02_GRID_SIZE,  # [x_len, y_len, 1]
        voxel_size=_VOXEL02_VOXEL_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        point_cloud_range=_POINT_CLOUD_RANGE,
    )
)

VOXEL01_TEST_CFG = dict(
    pts=dict(
        dataset="Private",
        grid_size=_VOXEL02_GRID_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL02_VOXEL_SIZE[:2],
        nms_type=None,
    )
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        # out_indices=(2, 3),
        out_indices=(0, 1, 2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=True,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)

RADAR_ENCODER_CFG = dict(out_channels=[11, 256, 256])

LIDAR_ENCODER_CFG = dict(
    point_cloud_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    max_num_points=16,
    # max_voxels=(260000, 280000),
    max_voxels=(100000, 120000),
    # max_voxels=(120000, 120000),
    src_num_point_features=4,
    use_num_point_features=4,
    map_to_bev_num_features=64,
    vfe=dict(
        vfe_num_filters=[64], num_point_features=4, voxel_size=_VOXEL02_VOXEL_SIZE, point_cloud_range=_POINT_CLOUD_RANGE
    ),
    backbone_2d=dict(
        layer_nums=[3, 5, 5],
        layer_strides=[2, 2, 2],
        num_filters=[64, 128, 256],
        upsample_strides=[1, 2, 4],
        num_upsample_filters=[128, 128, 128],
        input_channels=64,  # sp conv output channel
        with_cp=True,
        use_scconv=True,
        upsample_output=False,
    ),
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=False,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    radar_encoder=RADAR_ENCODER_CFG,
    lidar_encoder=LIDAR_ENCODER_CFG,
    det_head=VOXEL02_DET_HEAD,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
)
