from .det_model_cfg_8v5r1l_y200x32m import (
    CLASS_NAMES,
    _POINT_CLOUD_RANGE,
    _POST_CENTER_RANGE,
    _VOXEL02_VOXEL_SIZE,
    # CAMERA_ENCODER_CFG,
    VOXEL02_DET_HEAD,
    VOXEL02_TRAIN_CFG,
    VOXEL01_TEST_CFG,
    _FRONT_LIDAR_POINT_CLOUD_RANGE,
    _FRONT_LIDAR_VOXEL02_GRID_SIZE,
)

VOXEL02_DET_HEAD["bbox_coder"] = dict(
    type="TrackNMSFreeCoder",
    post_center_range=_POST_CENTER_RANGE,
    pc_range=_POINT_CLOUD_RANGE,
    max_num=300,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    num_classes=len(CLASS_NAMES),
    score_threshold=0.0,
)

TRACKING_MODULE_CFG = dict(  # past and future reasoning
    history_reasoning=True,  # use past reasoning
    future_reasoning=True,  # use future reasoning
    apply_feat_transform=False,
    apply_autoencoder=False,
    trajectory_encoding=False,
    use_hist_locs=False,
    add_disturb=False,
    gt_traj_frag_prob=0.0,
    gt_traj_valid_prob=1.0,
    hist_len=3,
    fut_len=8,
    num_classes=len(CLASS_NAMES),
    pc_range=_POINT_CLOUD_RANGE,
    hist_temporal_transformer=dict(
        type="TemporalTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=2,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    spatial_transformer=dict(
        type="TemporalTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=2,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    fut_temporal_transformer=dict(
        type="TemporalTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=2,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
)

TRACK_LOSS = dict(
    type="TrackingLossCombo",
    num_classes=len(CLASS_NAMES),
    interm_loss=True,
    code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", loss_weight=0.25),
    loss_iou=dict(type="GIoULoss", loss_weight=0.0),
    loss_prediction=dict(type="L1Loss", loss_weight=0.5),
    assigner=dict(
        type="HungarianAssigner3D",
        cls_cost=dict(type="FocalLossCost", weight=2.0),
        reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
        iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
        pc_range=_POINT_CLOUD_RANGE,
    ),
)

RUNTIME_TRACKER = dict(
    output_threshold=0.2,
    score_threshold=0.4,
    record_threshold=0.4,
    max_age_since_update=7,
)

LIDAR_ENCODER_CFG = dict(
    point_cloud_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    max_num_points=16,
    # max_voxels=(260000, 280000),
    max_voxels=(100000, 120000),
    # max_voxels=(120000, 120000),
    src_num_point_features=4,
    use_num_point_features=4,
    map_to_bev_num_features=64,
    vfe=dict(
        vfe_num_filters=[64], num_point_features=4, voxel_size=_VOXEL02_VOXEL_SIZE, point_cloud_range=_POINT_CLOUD_RANGE
    ),
    backbone_2d=dict(
        layer_nums=[3, 5, 5],
        layer_strides=[2, 2, 2],
        num_filters=[64, 128, 256],
        upsample_strides=[1, 2, 4],
        num_upsample_filters=[128, 128, 128],
        input_channels=64,  # sp conv output channel
        with_cp=True,
        use_scconv=True,
        upsample_output=False,
    ),
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=True,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    # img_neck=dict(
    #     # type='FPN',
    #     in_channels=[1024, 2048],
    #     out_channels=256,
    #     num_outs=2,
    #     # with_cp=True,
    # ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=True,
    grid_mask=True,
    train_backbone=False,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    radar_encoder=None,
    lidar_encoder=LIDAR_ENCODER_CFG,
    det_head=VOXEL02_DET_HEAD,
    tracking_module=TRACKING_MODULE_CFG,
    track_loss=TRACK_LOSS,
    runtime_tracker=RUNTIME_TRACKER,
    if_update_ego=True,  # update the ego-motion
    fut_prediction_ref_update=True,  # use forecasting to update cross-frame movements
    # map_head = None,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
)
