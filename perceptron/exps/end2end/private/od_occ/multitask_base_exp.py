import sys

import refile
import torch
import mmcv
from abc import abstractmethod

from perceptron.exps.end2end.private.od_occ.model_cfg.det_model_cfg_4v1l_sparse_y120x32m_occ_multiclass import MODEL_CFG
from perceptron.exps.base_exp import BaseExp


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class MultiTaskBaseExp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(MultiTaskBaseExp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 3. model configuration and other configuration change in this function
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 4. other configuration
        self.task_flag = None

    @property
    def val_dataloader(self):
        if self.task_flag in ["box", "e2e"] and "_val_od_dataloader" not in self.__dict__:
            self._val_od_dataloader = self._configure_val_od_dataloader()
        elif self.task_flag == "occ" and "_val_occ_dataloader" not in self.__dict__:
            self._val_occ_dataloader = self._configure_val_occ_dataloader()
        elif self.task_flag == "map" and "_val_map_dataloader" not in self.__dict__:
            self._val_map_dataloader = self._configure_val_map_dataloader()
        elif self.task_flag == "god" and "_val_god_dataloader" not in self.__dict__:
            self._val_god_dataloader = self._configure_val_god_dataloader()
        elif self.task_flag is None:
            # 默认初始化od的dataloader
            self._val_od_dataloader = self._configure_val_od_dataloader()

        if self.task_flag in ["box", "e2e"] or self.task_flag is None:
            return self._val_od_dataloader
        elif self.task_flag == "occ":
            return self._val_occ_dataloader
        elif self.task_flag == "map":
            return self._val_map_dataloader
        elif self.task_flag == "god":
            return self._val_god_dataloader
        else:
            raise NotImplementedError(f"no supported task_flag: {self.task_flag}")

    def _configure_val_od_dataloader(self):
        pass

    def _configure_val_occ_dataloader(self):
        pass

    def _configure_val_map_dataloader(self):
        pass

    @torch.no_grad()
    def test_step(self, batch):
        outputs_dict = self.model(**batch)
        outs_obstacle = outputs_dict["obstacle"]
        outs_freespace = outputs_dict["freespace"]
        outs_map = outputs_dict["map"]
        outs_god = outputs_dict["god"]

        if self.task_flag == "box":
            return self.test_od_step(outs_obstacle)
        elif self.task_flag == "occ":
            return self.test_occ_step(outs_freespace)
        elif self.task_flag == "map":
            return self.test_map_step(outs_map)
        elif self.task_flag == "e2e":
            return self.test_e2e_step(outs_obstacle)
        elif self.task_flag == "god":
            return self.test_od_step(outs_god)
        else:
            raise NotImplementedError

    @torch.no_grad()
    def test_od_step(self, pred_dicts):
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_e2e_step(self, pred_dicts):
        return pred_dicts

    @torch.no_grad()
    def test_occ_step(self, pred_dicts):
        pred_dicts["pred_seg"] = pred_dicts["pred_seg"].argmax(-1).to(torch.uint8)
        remap_pred_dicts = []
        for pred_seg in pred_dicts["pred_seg"]:
            remap_pred_dict = {"pred_seg": pred_seg}
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_map_step(self, pred_dicts):
        raise NotImplementedError  # TODO

    @property
    @abstractmethod
    def supported_tasks(self):
        raise NotImplementedError("Please implement the supported_tasks property in the subclass.")
