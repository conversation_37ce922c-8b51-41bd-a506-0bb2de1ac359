"""

python3 perceptron/exps/end2end/private/od_occ/deploy/f3t_30w_pretrain.py -b 1 --eval --ckpt s3://pengbo/mot_exp/f3t_30w_pretrain/2025-06-21T11:21:10/dump_model/checkpoint_epoch_23.pth

"""
import os
import torch
import mmcv
import numpy as np

from perceptron.engine.cli import End2endCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler

from perceptron.exps.end2end.private.object.model_cfg.track_model_cfg_8v1l_sprase_y120x32m_urban import MODEL_CFG
from perceptron.exps.base_exp import BaseExp

from perceptron.exps.end2end.private.object.data_cfg.track_annos_z10_reorg_y120m import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.exps.end2end.private.object.data_cfg.track_annos_hf_urban_y200m_x32m_8v5r1l import (
    base_dataset_cfg as DATA_TRAIN_CFG_HF_GS,
)
from perceptron.exps.end2end.private.object.data_cfg.track_annos_hf_urban_y200m_x32m_8v5r1l_120roi import (
    base_dataset_cfg as DATA_TRAIN_CFG_HF,
)


from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import DistributedSamplerInterval2Stack, GroupEachSampleInBatchSampler
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder

SOFT_OCC_THRESHOLD = 0.4


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "f3t_30w_pretrain"
        # 1. Training setting
        self.cfg_path = os.path.basename(os.path.abspath(__file__))
        self.lr = 1e-3 * 0.2
        self.init_scale = 2
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 2. Dataset and model configuration
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        # Z10
        self.data_train_cfg_cq_z10 = mmcv.Config(DATA_TRAIN_CFG)

        self.data_train_cfg_cq_z10["loader"]["datasets_names"] = ["z10_label_1230_train", "Z10_label_0207_7w"]
        self.data_train_cfg_cq_z10["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_cq_z10["annotation"]["box"]["occlusion_threshold"] = 1  # 因为没有刷遮挡属性 所以先用-1 训练一下
        self.data_train_cfg_cq_z10["num_frames_per_sample"] = 3
        self.data_train_cfg_cq_z10["annotation"]["box"]["with_occlusion"] = True
        self.data_train_cfg_cq_z10["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD

        self.data_train_cfg_hf_gs = mmcv.Config(DATA_TRAIN_CFG_HF_GS)
        self.data_train_cfg_hf_gs["loader"]["datasets_names"] = ["hf_20250508_2"]
        self.data_train_cfg_hf_gs["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_hf_gs["annotation"]["box"]["occlusion_threshold"] = 1  # 因为没有刷遮挡属性 所以先用-1 训练一下
        self.data_train_cfg_hf_gs["num_frames_per_sample"] = 3
        self.data_train_cfg_hf_gs["annotation"]["box"]["with_occlusion"] = True
        self.data_train_cfg_hf_gs["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD

        self.data_train_cfg_hf = mmcv.Config(DATA_TRAIN_CFG_HF)
        self.data_train_cfg_hf["loader"]["datasets_names"] = ["hf_20250508_1"]
        self.data_train_cfg_hf["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_hf["annotation"]["box"]["occlusion_threshold"] = 1  # 因为没有刷遮挡属性 所以先用-1 训练一下
        self.data_train_cfg_hf["num_frames_per_sample"] = 3
        self.data_train_cfg_hf["annotation"]["box"]["with_occlusion"] = True
        self.data_train_cfg_hf["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.0
        self.model_cfg.num_query = 0 + 300 + 100
        self.model_cfg["train_backbone"] = False
        self.model_cfg["freeze_bn"] = True

        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300 + 100

        self.model_cfg["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.num_near_query = 100
        self.model_cfg["drop_lidar"] = 0.75

        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_lidar"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_img"] = True
        self.model_cfg.tracking_module.hist_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.spatial_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.fut_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1

        self.model_cfg.tracking_module["hist_len"] = 5
        self.model_cfg.tracking_module["use_timestamp"] = True
        self.model_cfg["ts_fix"] = True
        self.model_cfg["use_relative_ts"] = True
        self.model_cfg["contrastive"] = True

        # velocity related
        self.model_cfg.tracking_module["with_velocity"] = True

        # occlusion related
        self.model_cfg["keep_pos"] = True
        self.model_cfg["disappear_time"] = 3
        # with occlusion prediction
        self.model_cfg["tracking_module"]["with_occlusion"] = True
        self.model_cfg["track_loss"]["loss_occlusion"] = True

        # more track loss
        self.model_cfg["with_roi_mask"] = True
        self.model_cfg["track_loss"]["contrastive_mask_fix"] = True
        self.model_cfg["track_loss"]["hard_k"] = 1

        # refinement deformable
        self.model_cfg["det_head"]["refine_reg_branch"] = True

        self.model_cfg.runtime_tracker = dict(
            output_threshold=0.1,
            score_threshold=0.4,
            record_threshold=0.4,
            max_age_since_update=3,
            drop_probability=0.2,
            fp_ratio=0.1,
            hist_fp_num=9,
            hist_fp_memory_len=2000,
            fp_threshold=0.1,
            fp_cls_limit=True,
        )

        # city bmk
        self.data_val_cfg["loader"]["datasets_names"] = ["od_eval_debug"]
        self.data_val_cfg["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_val_cfg["sensor_names"]["lidar_names"] = ["fuser_lidar", "rfu_front_2_lidar"]
        self.data_val_cfg["lidar"]["lidar_names"] = ["fuser_lidar", "rfu_front_2_lidar"]

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(**self.data_train_cfg_cq_z10)
        train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_hf_gs)
        train_dataset3 = PrivateE2EDataset(**self.data_train_cfg_hf)

        train_dataset = torch.utils.data.ConcatDataset(
            [
                train_dataset1,
                train_dataset2,
                train_dataset3,
            ]
        )

        scene_flag = []
        scene_frame_idx = []
        pre_sum = 0
        for dataset in train_dataset.datasets:
            scene_flag.append(dataset.scene_flag + pre_sum)
            pre_sum = scene_flag[-1][-1] + 1
            scene_frame_idx.append(dataset.scene_frame_idx)
        scene_flag = np.concatenate(scene_flag)
        scene_frame_idx = np.concatenate(scene_frame_idx)
        setattr(train_dataset, "scene_flag", scene_flag)
        setattr(train_dataset, "scene_frame_idx", scene_frame_idx)

        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=DistributedSamplerInterval2Stack(
                train_dataset,
                stream_len=9,
                interval=20,
                num_frames=self.data_train_cfg_cq_z10["num_frames_per_sample"],  # change
            )
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=8,  # 此处设置为0，可以通过Ctrl-C 看出问题出现在哪里
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=8,
            batch_sampler=GroupEachSampleInBatchSampler(val_dataset, shuffle=False, drop_last=False)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=8,
            batch_sampler=GroupEachSampleInBatchSampler(val_dataset, shuffle=False, drop_last=False)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts, _ = self.model(**batch)
        return pred_dicts

    def _configure_optimizer(self):
        from torch.optim import AdamW

        backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if "encoder" in name:
                backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW(
            [
                {"params": backbone_params, "lr": self.lr * 0.1, "weight_decay": 0.01},
                {"params": other_params, "lr": self.lr, "weight_decay": 0.01},
            ]
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0 / 3 * self.lr,
            end_lr=1e-6,  # eta_min
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    End2endCli(Exp).run()
