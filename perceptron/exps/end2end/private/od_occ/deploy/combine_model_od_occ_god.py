"""
python3 perceptron/exps/end2end/private/od_occ/deploy/combine_model_od_occ_god.py -b 1 --eval --ckpt s3://pengbo/mot_exp/f5v_mix_data/2025-06-24T11:03:24/dump_model/checkpoint_epoch_23.pth \
--ckpt_occ s3://mcy-data/occ/ckpts/occ__freespace_only_1l4v_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1_v1p1_datascale_loadpt_rp_jx_data_4v_validation_freeze_vx0p2/2025-06-25T00:28:19/dump_model/checkpoint_epoch_23.pth \
--ckpt_god s3://ppe-qy/songfan/god/models/od_god__det_private_base_exp_8v1l_y300x32_deformable_120m_400q_refine_28w_fixoptim_god_4cls_freeze_od/2025-07-03T01:44:37/dump_model/checkpoint_epoch_59.pth
"""
import os
import torch
import mmcv

from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler

from perceptron.exps.end2end.private.od_occ.deploy.combined_model_cfg import MODEL_CFG

# from perceptron.exps.end2end.private.occ.model_cfg.fs_model_cfg_1l4v_15m_deform_reorg_vx0p2_multicls import MODEL_CFG as MODEL_CFG_OCC
from perceptron.exps.end2end.private.multitask.multitask_base_exp import MultiTaskBaseExp

from perceptron.exps.end2end.private.object.data_cfg.track_annos_z10_reorg_y120m import (
    val_dataset_cfg as DATA_VAL_OD_CFG,
)
from perceptron.exps.end2end.private.occ.data_cfg.freespace_annos_80m_15m_1L4V_z10_multicls import (
    val_dataset_cfg as DATA_VAL_OCC_CFG,
)

from perceptron.exps.end2end.private.god.data_cfg.det_annos_hf_200m_32m_8v5r1l_mmL_chengqu_Z10_new_fovrange_120_god_4cls import (
    val_dataset_god_cfg as DATA_VAL_GOD_CFG,
)
from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_god import class_names_god as CLASS_NAMES_GOD
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import GroupEachSampleInBatchSampler
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron_multi_task import VisionEncoder_MultiTask
from perceptron.data.det3d.private.multi_task_dataset import (
    collate_fn_wrapper,
)

SOFT_OCC_THRESHOLD = 0.4


class Exp(MultiTaskBaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.cfg_path = os.path.basename(os.path.abspath(__file__))
        # 1. Training setting
        self.print_interval = 50
        # 3. model configuration and other configuration change in this function
        self._change_model_cfg_params()

        # 2. Dataset model configuration
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

    def _get_train_dataset_cfg(self):
        pass

    def _get_val_dataset_cfg(self):

        self.data_val_od_cfg = mmcv.Config(DATA_VAL_OD_CFG)
        # city bmk
        self.data_val_od_cfg["loader"]["datasets_names"] = ["od_eval_debug"]
        self.data_val_od_cfg["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_val_od_cfg["sensor_names"]["lidar_names"] = ["fuser_lidar", "rfu_front_2_lidar"]
        self.data_val_od_cfg["lidar"]["lidar_names"] = ["fuser_lidar", "rfu_front_2_lidar"]
        self.data_val_od_cfg["annotation"].pop("occ", None)

        # 这里采用默认occ 4v的dataloader
        # self.data_val_occ_cfg = mmcv.Config(DATA_VAL_OCC_CFG)
        # 这里通过使用od的8v来验证实车情况
        self.data_val_occ_cfg = mmcv.Config(DATA_VAL_OD_CFG)
        occ_cfg_val = mmcv.Config(DATA_VAL_OCC_CFG)
        self.data_val_occ_cfg["evaluator"] = occ_cfg_val["evaluator"]
        self.data_val_occ_cfg["annotation"] = occ_cfg_val["annotation"]
        self.data_val_occ_cfg["loader"]["only_key_frame"] = True

        self.data_val_occ_cfg["evaluator"]["use_image_mask"] = self.model_cfg["freespace_head"]["use_mask"]
        self.data_val_occ_cfg["loader"]["datasets_names"] = ["z10_occ_trainset_part_for_combine_model"]
        self.data_val_occ_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_occ_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_occ_cfg["annotation"].pop("box", None)

        # 用于End2endEvaluator中读取
        self.data_val_cfg = self.data_val_od_cfg

        # GOD BMK
        cam = DATA_VAL_GOD_CFG["image"]["camera_names"]
        assert len(DATA_VAL_GOD_CFG["image"]["camera_names"]) == 8, f"{cam} != 8"
        self.data_val_god_cfg = mmcv.Config(DATA_VAL_GOD_CFG)
        self.data_val_god_cfg["loader"]["datasets_names"] = ["god_val"]
        self.data_val_god_cfg["annotation"]["god"]["occlusion_threshold"] = 1
        self.data_val_god_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_god_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_god_cfg["loader"]["only_key_frame"] = True
        self.data_val_god_cfg["num_frames_per_sample"] = 1

    def _change_model_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # import numpy as np
        # def check_value(path, value1, value2):
        #     if isinstance(value1, dict) and isinstance(value2, dict):
        #         for k in value1.keys():
        #             if k not in value2:
        #                 print(f"Warning: {path}.{k} not in value2, ")
        #             else:
        #                 check_value(path+"."+k, value1[k], value2[k])
        #     elif isinstance(value1, list) and isinstance(value2, list):
        #         if len(value1) != len(value2):
        #             print(f"Warning: {path} length not match, {len(value1)} vs {len(value2)}")
        #         else:
        #             for i in range(len(value1)):
        #                 check_value(path+"["+str(i)+"]", value1[i], value2[i])
        #     elif isinstance(value1, (np.ndarray, torch.Tensor)) and isinstance(value2, (np.ndarray, torch.Tensor)):
        #         if value1.shape != value2.shape:
        #             print(f"Warning: {path} shape not match, {value1.shape} vs {value2.shape}")
        #         else:
        #             if not np.allclose(value1, value2, rtol=1e-05, atol=1e-08):
        #                 print(f"Warning: {path} value not match, {value1} vs {value2}")
        #     else:
        #         if value1 != value2:
        #             print(f"Warning: {path} not match, {value1} vs {value2}")
        # self.model_cfg = mmcv.Config(MODEL_CFG_OCC)
        # model_cfg_od = mmcv.Config(MODEL_CFG)
        # # 对比两个cfg中差异的部分
        # for k in model_cfg_od.keys():
        #     if k not in self.model_cfg:
        #         print(f"Warning: {k} not in model_cfg, ")
        #     else:
        #         check_value(k, model_cfg_od[k], self.model_cfg[k])
        # exit()
        # self.model_cfg["det_head"] = model_cfg_od["det_head"]
        # for k in model_cfg_od.keys():
        #     if k not in self.model_cfg:
        #         self.model_cfg[k] = model_cfg_od[k]

        self.model_cfg = mmcv.Config(MODEL_CFG)
        self.od_class_names = self.model_cfg.class_names
        self.occ_class_names = [
            "free",
            "freespace",
            "dynamic",
            "static",
            "noise",  # default water
            "car",
            "larger_vehicle",
            "bicycle",
            "pedestrian",
        ]
        self.god_class_names = CLASS_NAMES_GOD

        # Tracking model
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.0
        self.model_cfg.num_query = 0 + 300 + 100
        self.model_cfg["train_backbone"] = False
        self.model_cfg["freeze_bn"] = True

        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300 + 100

        self.model_cfg["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.num_near_query = 100
        self.model_cfg["drop_lidar"] = 0.75

        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_lidar"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_img"] = True
        self.model_cfg.tracking_module.hist_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.spatial_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg.tracking_module.fut_temporal_transformer.decoder.transformerlayers.attn_cfgs[1][
            "type"
        ] = "PETRMultiheadAttention"
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1

        self.model_cfg.tracking_module["hist_len"] = 5
        self.model_cfg.tracking_module["use_timestamp"] = True
        self.model_cfg["ts_fix"] = True
        self.model_cfg["use_relative_ts"] = True
        self.model_cfg["contrastive"] = True

        # velocity related
        self.model_cfg.tracking_module["with_velocity"] = True

        # occlusion related
        self.model_cfg["keep_pos"] = True
        self.model_cfg["disappear_time"] = 3
        # with occlusion prediction
        self.model_cfg["tracking_module"]["with_occlusion"] = True
        self.model_cfg["track_loss"]["loss_occlusion"] = True

        # more track loss
        self.model_cfg["with_roi_mask"] = True
        self.model_cfg["track_loss"]["contrastive_mask_fix"] = True
        self.model_cfg["track_loss"]["hard_k"] = 1

        # refinement deformable
        self.model_cfg["det_head"]["refine_reg_branch"] = True

        self.model_cfg.runtime_tracker = dict(
            output_threshold=0.1,
            score_threshold=0.4,
            record_threshold=0.4,
            max_age_since_update=3,
            drop_probability=0.2,
            fp_ratio=0.1,
            hist_fp_num=9,
            hist_fp_memory_len=2000,
            fp_threshold=0.1,
            fp_cls_limit=True,
        )

        # Occ model
        self.model_cfg["freespace_head"]["use_mask"] = False
        print(self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"])
        self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"] = 4
        self.model_cfg["freespace_head"]["lidar_init"] = True
        self.model_cfg["freespace_head"]["loss_weight"].update(loss_dice=2.0)

        # Mask
        self.model_cfg["freespace_head"]["use_mask"] = False
        self.model_cfg["freespace_head"]["use_weighted_mask"] = True
        self.model_cfg["freespace_head"]["dis_weight"] = True
        self.model_cfg["freespace_head"]["mask_loss_type"] = 0

        # crop lidar feat
        self.model_cfg["freespace_head"]["lidar_feat_crop"] = dict(
            crop_h=(0, 204), crop_w=(160 // 2 - 76 // 2, 160 // 2 + 76 // 2)
        )

        """
        _CAMERA_LIST = [
            "cam_front_120",
            "cam_front_120_sim_fov70",
            "cam_front_30",
            "cam_back_70",
            "cam_front_left_100_sim_fov104",
            "cam_front_right_100_sim_fov104",
            "cam_back_left_100_sim_fov104",
            "cam_back_right_100_sim_fov104",
        ] =>
        _CAMERA_LIST = [
            "cam_front_120",
            "cam_front_30",
            "cam_front_left_100_sim_fov104",
            "cam_front_right_100_sim_fov104",
        ]
        """
        self.model_cfg["occ_cam_idx_or_mask"] = [0, 2, 4, 5]

        # god model
        self.model_cfg.god_head.det_head.bbox_coder.score_threshold = 0.1
        self.model_cfg.god_head.num_query = 0 + 300 + 100
        self.model_cfg.god_head["det_head"]["init_radar_num_query"] = 0
        self.model_cfg.god_head["det_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg.god_head["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.god_head.num_near_query = 100

        self.model_cfg["radar_encoder"] = None
        self.model_cfg.god_head["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1][
            "with_lidar"
        ] = True
        self.model_cfg.god_head["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1][
            "with_img"
        ] = True
        self.model_cfg.god_head["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg.god_head["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg.god_head["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg.god_head["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask

        self.model_cfg.god_head["det_head"]["refine_reg_branch"] = True
        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)

    def _configure_model(self):
        model = VisionEncoder_MultiTask(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        raise NotImplementedError

    def _configure_val_dataloader(self):
        raise NotImplementedError

    def _configure_val_od_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_od_cfg,
        )
        val_dataset.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=8,
            batch_sampler=GroupEachSampleInBatchSampler(val_dataset, shuffle=False, drop_last=False)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_god_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_god_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=8,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_occ_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_occ_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn),
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        raise NotImplementedError

    def training_step(self, batch):
        raise NotImplementedError

    def _configure_optimizer(self):
        from torch.optim import AdamW

        backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if "encoder" in name:
                backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW(
            [
                {"params": backbone_params, "lr": self.lr * 0.1, "weight_decay": 0.01},
                {"params": other_params, "lr": self.lr, "weight_decay": 0.01},
            ]
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0 / 3 * self.lr,
            end_lr=1e-6,  # eta_min
        )
        return scheduler

    @property
    def supported_tasks(self):
        return ["god", "occ", "e2e"]


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
