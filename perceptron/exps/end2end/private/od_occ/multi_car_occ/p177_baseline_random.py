"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --set-env DISTRIBUTED_JOB=true --cpu=70 --gpu=8 --memory=$((1024*300)) --max-wait-duration=1000h --positive-tags 3090 --custom-resources rdma/mlnx_shared=0 --preemptible no -n mach-dynamic  --group=dynamic_gpu --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=juicefs+s3://oss.i.machdrive.cn/perceptron-cache:/mnt/cache --replica-restart=never -- python3 perceptron/exps/end2end/private/od_occ/multi_car_occ/p177_baseline_random.py --no-clearml -b 2 -e 20 --sync_bn 1 --amp
"""
import sys

import copy
import refile
import torch
import mmcv
from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist


from perceptron.exps.end2end.private.od_occ.model_cfg.det_model_cfg_4v1l_sparse_y120x32m_occ_multiclass import MODEL_CFG
from perceptron.exps.end2end.private.occ.data_cfg.freespace_annos_80m_15m_1L4V_p177_5cls import (
    base_dataset_cfg as DATA_TRAIN_CFG,
)


from perceptron.exps.end2end.private.od_occ.multi_car_occ.z10_baseline import Exp as BaseExpZ10
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteIntervalSampler
from perceptron.data.det3d.private.multi_task_dataset import (
    collate_fn_wrapper,
)


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class Exp(BaseExpZ10):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(BaseExpZ10, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 3. model configuration and other configuration change in this function
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 4. other configuration
        self.task_flag = None

        # to record: 用于在log中记录需要保存的参数
        self.train_data = {
            # "od": self.data_train_cfg_cq_prelabel_p177["loader"]["datasets_names"],
            "occ": self.data_train_cfg_cq_prelabel_p177["loader"]["datasets_names"],
        }
        self.eval_name = "Z10_eval_od_bmk02_occ_bmk02_filter_ok"

    def _get_train_dataset_cfg(self):
        self.data_train_cfg_z10 = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg_cq_prelabel_p177 = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg_cq_prelabel_p177["loader"]["datasets_names"] = [
            # p177
            # reproduce
            "car_p177_occ_prelabel_20250513_huiliu_jixie_19685_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_3d_h5",
        ]
        self.data_train_cfg_cq_prelabel_p177["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_cq_prelabel_p177["loader"]["only_key_frame"] = True
        self.data_train_cfg_cq_prelabel_p177["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_train_cfg_cq_prelabel_p177["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_train_cfg_cq_prelabel_p177["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_cfg_cq_prelabel_p177["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]

        self.data_jixie_cfg = copy.deepcopy(self.data_train_cfg_cq_prelabel_p177)
        self.data_jixie_cfg["annotation"].pop("box")

    def _configure_train_dataloader(self):
        train_dataset_jixie = PrivateE2EDataset(**self.data_jixie_cfg)
        train_dataset = train_dataset_jixie

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=True),
            sampler=InfiniteIntervalSampler(len(train_dataset), seed=self.seed if self.seed else 0, interval=32)
            if dist.is_distributed()
            else None,
            # sampler=FlexibleMultiTaskIntervalSamplerV2(
            #     train_dataset,
            #     dataset_boundaries,
            #     per_gpu_dataset_batch_sizes=[2, 2],
            #     shuffle=True,
            #     intervals=[16, 12],
            #     drop_last=False,
            # ),
            pin_memory=True,
            num_workers=6,
        )
        return train_loader


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
