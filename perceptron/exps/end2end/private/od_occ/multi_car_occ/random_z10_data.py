from perceptron.data.det3d.source.z10 import TRAINSET_PARTIAL

import refile
import json
import random


if __name__ == "__main__":
    datalist = [
        # "car_z10_occ_prelabel_20250504_huiliu_jixie_4881_3d_multicls",  # 0504
        # "car_z10_occ_prelabels_20250514_huiliu_jixie_occv1.2.0_occdumpv1.2.0_pointsegv1.1.1_15000_3d",
        "car_p177_occ_prelabel_20250513_huiliu_jixie_19685_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_3d_h5",
    ]
    all_json_data_list = []
    for data in datalist:
        json_list = TRAINSET_PARTIAL[data]
        json_list_tmp = json.load(refile.smart_open(json_list))
        if isinstance(json_list_tmp, list):
            all_json_data_list += json_list_tmp
        elif isinstance(json_list_tmp, dict):
            all_json_data_list += json_list_tmp["json_list"]
        else:
            raise ValueError(f"Unexpected data type: {type(json_list_tmp)} for {data}")
        print(json_list)
    print(len(all_json_data_list))

    random.shuffle(all_json_data_list)
    json_50_list = all_json_data_list[::2]
    with open("p177_50_part.json", "w") as f:
        json.dump(json_50_list, f, indent=4, ensure_ascii=False)

    # with open("z10_50_part.json", "w") as f:
    #     json.dump(json_50_list, f, indent=4, ensure_ascii=False)
