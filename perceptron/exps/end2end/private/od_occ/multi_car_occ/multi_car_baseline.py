"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --set-env DISTRIBUTED_JOB=true --cpu=96 --gpu=8 --memory=$((1024*600)) --max-wait-duration=1000h --positive-tags A800 --preemptible no -n mach-aigc  --group=aigc_gpu --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=juicefs+s3://oss.i.machdrive.cn/perceptron-cache:/mnt/cache --replica-restart=never -- python3 perceptron/exps/end2end/private/od_occ/multi_car_occ/multi_car_baseline.py --no-clearml -b 6 -e 20 --sync_bn 1


DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --set-env DISTRIBUTED_JOB=true --cpu=80 --gpu=8 --memory=$((1024*600)) --max-wait-duration=1000h --positive-tags H20 --custom-resources rdma/mlnx_shared=0  --preemptible no -n mach-dynamic  --group=dynamic_gpu --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=juicefs+s3://oss.i.machdrive.cn/perceptron-cache:/mnt/cache --replica-restart=never -- \
python3 perceptron/exps/end2end/private/od_occ/multi_car_occ/multi_car_baseline.py --no-clearml -b 4 -e 4 --sync_bn 1 --eval_occ --ckpt /data/outputs/multi_car_occ__multi_car_baseline/latest/dump_model/checkpoint_epoch_19.pth

"""
import sys

import refile
import torch
import mmcv
from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist


from perceptron.exps.end2end.private.od_occ.model_cfg.det_model_cfg_4v1l_sparse_y120x32m_occ_multiclass import MODEL_CFG
from perceptron.exps.end2end.private.occ.data_cfg.freespace_annos_80m_15m_1L4V_p177_5cls import (
    base_dataset_cfg as DATA_TRAIN_CFG_p177,
)
from perceptron.exps.end2end.private.od_occ.data_cfg.det_annos_hf_120m_32m_4v1l_mmL_chengqu_Z10_new_occ_multiclass import (
    base_dataset_cfg as DATA_TRAIN_CFG_z10,
)


from perceptron.exps.end2end.private.od_occ.multi_car_occ.z10_baseline import Exp as BaseExpZ10
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteIntervalSamplerDeterministic
from perceptron.data.det3d.private.multi_task_dataset import (
    collate_fn_wrapper,
)


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class Exp(BaseExpZ10):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(BaseExpZ10, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 3. model configuration and other configuration change in this function
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 4. other configuration
        self.task_flag = None

        # to record: 用于在log中记录需要保存的参数
        self.train_data = {
            # "od": self.data_train_cfg_cq_prelabel_p177["loader"]["datasets_names"],
            "occ": self.data_train_cfg_cq_prelabel_p177["loader"]["datasets_names"]
            + self.data_train_cfg_z10["loader"]["datasets_names"],
        }
        self.eval_name = "Z10_eval_od_bmk02_occ_bmk02_filter_ok"

    def _get_train_dataset_cfg(self):
        # z10
        self.data_train_cfg_z10 = mmcv.Config(DATA_TRAIN_CFG_z10)
        self.data_train_cfg_z10["loader"]["datasets_names"] = [
            "z10_50_part",
        ]
        self.data_train_cfg_z10["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_z10["loader"]["only_key_frame"] = True
        self.data_train_cfg_z10["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_train_cfg_z10["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_train_cfg_z10["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_cfg_z10["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_cfg_z10["annotation"].pop("box")

        # p177
        self.data_train_cfg_cq_prelabel_p177 = mmcv.Config(DATA_TRAIN_CFG_p177)
        self.data_train_cfg_cq_prelabel_p177["loader"]["datasets_names"] = [
            "p177_50_part",
        ]
        self.data_train_cfg_cq_prelabel_p177["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_cq_prelabel_p177["loader"]["only_key_frame"] = True
        self.data_train_cfg_cq_prelabel_p177["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_train_cfg_cq_prelabel_p177["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_train_cfg_cq_prelabel_p177["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_cfg_cq_prelabel_p177["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_cfg_cq_prelabel_p177["annotation"].pop("box")

    def _configure_train_dataloader(self):
        train_dataset_z10 = PrivateE2EDataset(**self.data_train_cfg_z10)
        train_dataset_p177 = PrivateE2EDataset(**self.data_train_cfg_cq_prelabel_p177)

        train_dataset = torch.utils.data.ConcatDataset([train_dataset_z10, train_dataset_p177])
        train_dataset.batch_postcollate_fn = train_dataset_z10.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset_z10.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=True),
            sampler=InfiniteIntervalSamplerDeterministic(
                len(train_dataset), seed=self.seed if self.seed else 0, interval=32
            )
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=12,
        )
        return train_loader


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
