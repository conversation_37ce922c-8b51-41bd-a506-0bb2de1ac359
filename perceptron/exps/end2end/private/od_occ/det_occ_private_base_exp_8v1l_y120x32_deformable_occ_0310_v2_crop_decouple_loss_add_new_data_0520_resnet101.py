""" Z10 8V1L

python3 /data/code/e2e/cxv_perceptron/perceptron/perceptron/exps/end2end/private/od_occ/det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_add_new_data_0520_resnet.py --no-clearml -b 4 -e 2 --sync_bn 1 --amp --pretrained_model /data/code/e2e/perceptron/pretrain/occ_od_0422_od_backbone_v3.pth
python3 perceptron/exps/end2end/private/od_occ/det_private_base_exp_8v1l_y300x32_deformable_occ_0310_v2_crop_decouple_loss_add_new_data.py --no-clearml -b 4 -e 30 --sync_bn 1 --amp  --pretrained_model /data/code/e2e/perceptron/pretrain/occ_od_0310.pth
rlaunch --cpu=160 --gpu=8 --memory=$((1024*900)) --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp --mount=gpfs://gpfs1/acceldata/dynamic:/mnt/acceldata/dynamic --group=dynamic_gpu --max-wait-duration=12h --positive-tags=H20 -- python3 perceptron/exps/end2end/private/od_occ/det_private_base_exp_8v1l_y300x32_deformable_occ_0310_v2_crop_decouple_loss.py --no-clearml -b 4 -e 30 --sync_bn 1 --amp  --pretrained_model /data/code/e2e/perceptron/pretrain/occ_od_0310.pth

/data/code/e2e/perceptron/pretrain/occ_od_0310_od_backbone_v2.pth
rlaunch --cpu=120 --gpu=8 --memory=$((1024*900)) --mount=gpfs://gpfs1/tf-rhea-data-bpp:/mnt/tf-rhea-data-bpp --mount=gpfs://gpfs1/acceldata/dynamic:/mnt/acceldata/dynamic --group=dynamic_gpu --max-wait-duration=12h bash
/data/code/e2e/perceptron/pretrain/occ_od_0422_od_backbone_v3.pth
"""
import sys
import copy
import refile
import torch
import mmcv
from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler


from perceptron.exps.end2end.private.od_occ.model_cfg.det_model_cfg_8v1l_sparse_y120x32m_occ_0310 import MODEL_CFG
from perceptron.exps.base_exp import BaseExp

from perceptron.exps.end2end.private.od_occ.data_cfg.det_annos_hf_120m_32m_8v1l_mmL_chengqu_Z10_new_occ_0310 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_od_bmk02_cfg as DATA_VAL_OD_BMK02_CFG,
    val_dataset_occ_bmk02_cfg as DATA_VAL_OCC_BMK02_CFG,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron_multi_task import VisionEncoder_MultiTask
from perceptron.data.det3d.private.multi_task_dataset import (
    MultiTaskDistributedSampler,
    get_dataset_boundaries,
    collate_fn_wrapper,
)


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 3. model configuration and other configuration change in this function
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 4. other configuration
        self.task_flag = None

        # to record: 用于在log中记录需要保存的参数
        self.train_data = {
            "od": self.data_train_cfg_z10_od["loader"]["datasets_names"]
            + self.data_train_cfg_z10_od_jixie["loader"]["datasets_names"],
            "occ": self.data_train_cfg_z10_occ_jixie["loader"]["datasets_names"],
        }
        self.eval_name = "Z10_eval_od_bmk02_occ_bmk02_ok"
        self.val_data = {
            "od": self.data_val_od_cfg["loader"]["datasets_names"],
            "occ": self.data_val_occ_cfg["loader"]["datasets_names"],
        }

    def _get_train_dataset_cfg(self):
        self.data_train_cfg_z10 = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg_z10["loader"]["datasets_names"] = ["z10_label_1230_train", "Z10_label_0207_7w"]

        self.data_train_cfg_z10["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg_z10["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_train_cfg_z10["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        self.data_train_cfg_z10["loader"]["only_key_frame"] = True

        self.data_train_cfg2 = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg2["loader"]["datasets_names"] = [
            "z10_label_0401_teshucheliang",
            "z10_label_0401_mijivru",
        ]

        self.data_train_cfg2["lidar"]["lidar_names"] = ["fuser_lidar", "rfu_front_2_lidar"]
        self.data_train_cfg2["sensor_names"]["lidar_names"] = ["fuser_lidar", "rfu_front_2_lidar"]
        self.data_train_cfg2["annotation"]["box"]["label_key"] = "labels"
        self.data_train_cfg2["loader"]["only_key_frame"] = True
        self.data_train_cfg2["annotation"]["box"]["occlusion_threshold"] = 1
        self.data_train_cfg2["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD

        self.train_data = self.data_train_cfg_z10["loader"]["datasets_names"]
        self.data_train_cfg_z10_od = copy.deepcopy(self.data_train_cfg_z10)
        self.data_train_cfg_z10_od["annotation"].pop("occ")

        self.data_train_cfg_z10_od_jixie = copy.deepcopy(self.data_train_cfg2)
        self.data_train_cfg_z10_od_jixie["annotation"].pop("occ")

        self.data_train_cfg_z10_occ_jixie = copy.deepcopy(self.data_train_cfg_z10)
        self.data_train_cfg_z10_occ_jixie["annotation"].pop("box")
        self.data_train_cfg_z10_occ_jixie["loader"]["datasets_names"] = [
            # "car_z10_occ_prelabel_20250415_huiliu_jixie_1634_3d_multiclass",
            # "car_z10_occ_prelabel_20250414_huiliu_jixie_3117_3d",
            # "car_z10_occ_prelabel_20250415_huiliu_jixie_1687_3d",
            # "car_z10_occ_prelabel_20250417_huiliu_jixie_1571_3d",
            # "car_z10_occ_humanlabels_20250427_huiliu_jixie_2947_3d_multicls",  # 0427
            # "car_z10_occ_prelabel_20250504_huiliu_jixie_4881_3d_multicls",  # 0504
            "occ_train1_2915",
            "occ_train2_1485",
            "occ_train3_4881",
            "occ_train4_1571",
            "occ_train5_3117",  # 0427
            "occ_train6_1633",  # 0504
        ]
        self.data_train_cfg_z10_occ_jixie["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_train_cfg_z10_occ_jixie["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_z10_occ_jixie["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]

    def _get_val_dataset_cfg(self):
        self.data_val_od_cfg = mmcv.Config(DATA_VAL_OD_BMK02_CFG)
        self.data_val_occ_cfg = mmcv.Config(DATA_VAL_OCC_BMK02_CFG)

    def _change_model_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.1
        self.model_cfg.num_query = 0 + 300 + 100
        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.num_near_query = 100

        self.model_cfg["radar_encoder"] = None
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_lidar"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_img"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask
        self.model_cfg["det_head"]["refine_reg_branch"] = True

        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)

        self.model_cfg["freespace_head"]["use_mask"] = False
        self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"] = 4
        self.model_cfg["freespace_head"]["lidar_init"] = True
        self.model_cfg["freespace_head"]["loss_weight"].update(loss_dice=2.0)

        self.model_cfg["freespace_head"]["lidar_feat_crop"] = dict(
            crop_h=(0, 204), crop_w=(160 // 2 - 76 // 2, 160 // 2 + 76 // 2)
        )

        # resnet101
        self.model_cfg["camera_encoder"]["img_backbone"]["depth"] = 101
        self.model_cfg["camera_encoder"]["img_backbone"]["init_cfg"] = dict(
            type="Pretrained", checkpoint="torchvision://resnet101"
        )

    def _configure_model(self):
        model = VisionEncoder_MultiTask(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset_od = PrivateE2EDataset(
            **self.data_train_cfg_z10_od,
        )
        train_dataset_od_jixie = PrivateE2EDataset(
            **self.data_train_cfg_z10_od_jixie,
        )
        # train_dataset_occ = PrivateE2EDataset(
        #     **self.data_train_cfg_z10_occ,
        # )
        train_dataset_occ_jixie = PrivateE2EDataset(
            **self.data_train_cfg_z10_occ_jixie,
        )

        # map
        # train_dataset_occ = PrivateE2EDataset(
        #     **self.data_train_cfg_z10,
        #     fov_boardline=[[0, 0], [-32.0, -0.55 * (-32)], [-32.0, 120], [32.0, 120], [32.0, 0.57 * 32]],
        # )
        # train_dataset_occ_mix = train_dataset_occ_jixie #torch.utils.data.ConcatDataset([train_dataset_occ, train_dataset_occ_jixie])
        train_dataset_od_mix = torch.utils.data.ConcatDataset([train_dataset_od, train_dataset_od_jixie])
        dataset_boundaries = get_dataset_boundaries([train_dataset_od_mix, train_dataset_occ_jixie])
        train_dataset = torch.utils.data.ConcatDataset([train_dataset_od_mix, train_dataset_occ_jixie])
        train_dataset.batch_postcollate_fn = train_dataset_od.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset_od.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn),
            sampler=MultiTaskDistributedSampler(
                train_dataset, dataset_boundaries, batch_size=self.batch_size_per_device, shuffle=True
            ),
            pin_memory=False,
            num_workers=12,
        )
        return train_loader

    @property
    def val_dataloader(self):
        if self.task_flag == "box" and "_val_od_dataloader" not in self.__dict__:
            self._val_od_dataloader = self._configure_val_od_dataloader()
        elif self.task_flag == "occ" and "_val_occ_dataloader" not in self.__dict__:
            self._val_occ_dataloader = self._configure_val_occ_dataloader()
        elif self.task_flag == "map" and "_val_map_dataloader" not in self.__dict__:
            self._val_map_dataloader = self._configure_val_map_dataloader()
        elif self.task_flag is None:
            # 默认初始化od的dataloader
            self._val_od_dataloader = self._configure_val_od_dataloader()

        if self.task_flag == "box" or self.task_flag is None:
            return self._val_od_dataloader
        elif self.task_flag == "occ":
            return self._val_occ_dataloader
        elif self.task_flag == "map":
            return self._val_map_dataloader
        else:
            raise NotImplementedError(f"no supported task_flag: {self.task_flag}")

    def _configure_val_dataloader(self):
        raise NotImplementedError

    def _configure_val_od_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_od_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=False),
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_occ_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_occ_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_od_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)
        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        if self.model_cfg["det_head"] and self.model_cfg["freespace_head"]:
            outs_obstacle, outs_map, outs_freespace = self.model(**batch)
            if self.task_flag == "box":
                return self.test_od_step(outs_obstacle)
            elif self.task_flag == "occ":
                return self.test_occ_step(outs_freespace)
            elif self.task_flag == "map":
                return self.test_map_step(outs_map)
            else:
                raise NotImplementedError
        else:
            raise NotImplementedError

    @torch.no_grad()
    def test_od_step(self, pred_dicts):
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_occ_step(self, pred_dicts):
        pred_dicts["pred_seg"] = pred_dicts["pred_seg"].argmax(-1).to(torch.uint8)
        remap_pred_dicts = []
        for pred_seg in pred_dicts["pred_seg"]:
            remap_pred_dict = {"pred_seg": pred_seg}
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_map_step(self, pred_dicts):
        raise NotImplementedError  # TODO

    def _configure_optimizer(self):
        from torch.optim import AdamW

        backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if "encoder" in name:
                backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW(
            [
                {"params": backbone_params, "lr": self.lr * 0.1, "weight_decay": 0.01},
                {"params": other_params, "lr": self.lr, "weight_decay": 0.01},
            ]
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0 / 3 * self.lr,
            end_lr=1e-6,  # eta_min
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
