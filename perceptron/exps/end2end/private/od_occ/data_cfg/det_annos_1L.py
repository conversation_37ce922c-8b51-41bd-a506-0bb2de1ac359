from perceptron.data.det3d.modules.annotation.multitask_annos import AnnotationDet

from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    PointShuffle,
)
from perceptron.data.det3d.modules.pipelines.transformation_multitask import (
    MultiTaskObjectRangeFilter,
    MultiTaskObjectRangeWOMaskFilter,
)
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_z10_8v5r1l_new import _CAMERA_LIST


CATEGORY = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    "masked_area",
]

CATEGORY_MAPPING = {
    "汽车": "car",
    "小汽车": "car",  # N/A
    "Car": "car",  # N/A
    "suv": "car",
    "pika": "car",
    "皮卡": "car",  # in test
    "car": "car",  # N/A
    "SUV": "car",
    "VAN": "car",
    "van": "car",
    "SUV_MPV": "car",  # N/A
    "MPV": "car",  # N/A
    "货车": "truck",
    "大货车": "truck",
    "小货车": "truck",
    "拖挂": "truck",  # 仅拖车车厢
    "tuogua": "truck",  # 仅拖车车厢, N/A
    "truck": "truck",  # N/A
    "特种车": "truck",  # NOTE, N/A
    "带斗的货车": "truck",  # N/A
    "dauhoche": "truck",
    "dahuoche": "truck",  # N/A
    "xiaohuoche": "truck",
    "工程车": "construction_vehicle",
    "construction_vehicle": "construction_vehicle",  # N/A
    "engineeringvehicle": "construction_vehicle",  # NOTE(ori:truck), N/A
    "巴士": "bus",
    "bus": "bus",  # N/A
    "摩托车": "motorcycle",
    "Motorcycle": "motorcycle",  # N/A
    "motorcycle": "motorcycle",  # N/A
    "motorbike": "motorcycle",  # N/A
    "Motor": "motorcycle",  # N/A
    "自行车": "bicycle",
    "Bicycle": "bicycle",  # N/A
    "bicycle": "bicycle",  # N/A
    "三轮车": "tricycle",
    "骑三轮车的人": "tricycle",
    "Tricyclist": "tricycle",
    "tricycle": "tricycle",  # N/A
    "骑三轮车的人带斗": "tricycle",  # N/A
    "骑三轮的人带箱": "tricycle",  # N/A
    "Tricycle": "tricycle",  # N/A
    "骑车人": "cyclist",
    "骑行的人": "cyclist",  # N/A
    "骑自行车的人": "cyclist",
    "骑摩托车的人": "cyclist",
    "Motorcyclist": "cyclist",
    "cyclist": "cyclist",  # N/A
    "人": "pedestrian",
    "行人": "pedestrian",  # N/A
    "儿童": "pedestrian",
    "成年人": "pedestrian",
    "穿警服的人": "pedestrian",  # N/A
    "Ped": "pedestrian",  # N/A
    "pedestrian": "pedestrian",  # N/A
    "推儿童车的人": "pedestrian",  # N/A
    "推购物车的人": "pedestrian",  # N/A
    "推轮椅不带人的人": "pedestrian",  # N/A
    "推轮椅带人的人": "pedestrian",  # N/A
    "et": "pedestrian",  # 小孩子
    "Pedestrian": "pedestrian",  # N/A
    "蒙版": "masked_area",
    "mask": "masked_area",  # N/A
    "正向蒙版": "masked_area",
    "负向蒙版": "masked_area",
    "positive": "masked_area",
    "negative": "masked_area",
    "Other motor vehicles": "masked_area",  # (ori:other)
    "其他机动车": "masked_area",  # (ori:other)
    "其他非机动车": "masked_area",  # (ori:other)
    "Other non motor vehicles": "masked_area",  # (ori:other)
    "婴儿车": "masked_area",  # (ori:other), N/A
    "Stroller": "masked_area",  # (ori:other)
    "轮椅": "masked_area",  # (ori:other), in test
    "Wheelchair": "masked_area",  # (ori:other)
    "手推车": "masked_area",  # (ori:other), in test
    "cart": "masked_area",  # (ori:other)
    "其它": "other",
    "其他": "other",
    "Small animals\xa0": "other",
    "小动物类": "other",
    "大动物类": "other",
    "Macrofauna": "other",
    "残影": "other",
}

CATEGORY_MAPPING_REVERSE = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "masked_area": "蒙版",
    "other": "其它",
}


point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]
lidar_only_point_cloud_range = [-24.8, 0, -5.0, 24.8, 120, 3.0]
log_bad_data = False
use_oss_data = False


lidar_only_annos = dict(
    type=AnnotationDet,
    category_map=CATEGORY_MAPPING,
    class_names=CATEGORY,
    occlusion_threshold=-1,
    filter_outlier_boxes=True,
    filter_outlier_frames=True,
    filter_empty_2d_bboxes=False,
    filter_empty_frames=True,
    roi_range=lidar_only_point_cloud_range,
    HF=False,
    label_key="pre_labels",
    # fov_convexthull=[[0, 0], [-32.0, -0.55 * (-32)], [-32.0, 120], [32.0, 120], [32.0, 0.57 * 32]],
)

_PIPELINE_MULTIFRAME = dict(
    # object_range_filter=dict(
    #     type=ObjectRangeFilter,
    #     point_cloud_range=point_cloud_range,  # occ是否还需要过滤，感觉不需要
    # ),
    object_range_filter=dict(
        type=MultiTaskObjectRangeFilter,
        task="box",
        filter_pc=True,
        box_range=point_cloud_range,
        point_cloud_range=point_cloud_range,
    ),
    object_range_filter_god=dict(
        type=MultiTaskObjectRangeFilter,
        task="god",
        filter_pc=False,
        box_range=point_cloud_range,
        point_cloud_range=point_cloud_range,
    ),
    object_range_filter_lidar_only=dict(
        type=MultiTaskObjectRangeWOMaskFilter,
        task="lidar_only",
        filter_pc=False,
        box_range=lidar_only_point_cloud_range,
        point_cloud_range=lidar_only_point_cloud_range,
        class_names=CATEGORY,
        fov_convexthull=[[0, 0], [-32.0, -0.55 * (-32)], [-32.0, 120], [32.0, 120], [32.0, 0.57 * 32]],
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)
