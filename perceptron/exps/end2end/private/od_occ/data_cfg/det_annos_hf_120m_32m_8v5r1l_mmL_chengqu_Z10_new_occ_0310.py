from perceptron.data.det3d.modules.annotation import AnnotationDet, AnnotationFreespace
import copy

from perceptron.data.det3d.source.config import Z10
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_z10_8v5r1l_new import (
    # lidar as lidar_cfg,
    image as image_cfg,
    radar as radar_cfg,
    LidarBase,
    _LIDAR_LIST,
    _CAR,
)
from perceptron.data.det3d.modules import (
    EvaluationV3,
    ObjectRangeFilter,
    PointShuffle,
)

_RADAR_KEY_LIST = []
_CAMERA_LIST = []

_SENSOR_NAMES = dict(camera_names=_CAMERA_LIST, lidar_names=_LIDAR_LIST, radar_names=_RADAR_KEY_LIST)


lidar_cfg = dict(
    type=LidarBase,
    car=_CAR,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[4],  # 4 在hf/Z10中对应前视速腾M1P lidar
)


category_map = {
    # "小汽车": "car",
    # "汽车": "car",
    # "货车": "truck",
    # "工程车": "construction_vehicle",
    # "巴士": "bus",
    # "摩托车": "motorcycle",
    # "自行车": "bicycle",
    # "三轮车": "tricycle",
    # "骑车人": "cyclist",
    # "骑行的人": "cyclist",
    # "人": "pedestrian",
    # "行人": "pedestrian",
    # "其它": "other",
    # "其他": "other",
    # "残影": "ghost",
    # "蒙版": "masked_area",
    # "car": "car",
    # "truck": "truck",
    # "construction_vehicle": "construction_vehicle",
    # "bus": "bus",
    # "motorcycle": "motorcycle",
    # "bicycle": "bicycle",
    # "tricycle": "tricycle",
    # "cyclist": "cyclist",
    # "pedestrian": "pedestrian",
    # "other": "other",
    # "ghost": "ghost",
    # "masked_area": "masked_area",
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
    "遮挡": "occlusion",
    "短障碍物": "short_track",
    "大货车": "truck",
    "小货车": "truck",
    "骑三轮车的人": "tricycle",
    "骑自行车的人": "cyclist",
    "骑摩托车的人": "cyclist",
    "儿童": "pedestrian",
    "成年人": "pedestrian",
    "蒙版": "masked_area",
    "mask": "masked_area",
    "正向蒙版": "masked_area",
    "负向蒙版": "masked_area",
    "拖挂": "truck",
    "tuogua": "truck",
    "其他非机动车": "other",
    "其他机动车": "other",
    "小动物类": "other",
    "大动物类": "other",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]

point_cloud_range = [-32.0, 0.0, -5.0, 32.0, 120, 3.0]

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,  # occ是否还需要过滤，感觉不需要
        min_points=5,
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    # undistort=dict(
    #     type=CameraUndistortCPU,
    # ),
    # # bda_aug: need to be adapted
    # ida_aug=dict(
    #     type=MultiFrameImageAffineTransformation,
    #     aug_conf=dict(
    #         final_dim=(512, 960),
    #         # resize_lim=((0.472, 0.5), (0.472, 0.5)),
    #         resize_lim=(0.472, 0.5),
    #         bot_pct_lim=(0.0, 0.0),
    #         H=1080,
    #         W=1920,
    #         rand_flip=False,
    #         rot_lim=(-0.0, 0.0),
    #     ),
    #     camera_names=_CAMERA_LIST,
    #     img_norm=True,
    #     img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    # ),
)


annotation_cfg = dict(
    occ=dict(type=AnnotationFreespace, label_mapping={0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 255, 6: 255, 255: 255}),
    box=dict(
        type=AnnotationDet,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        # with_predict=False,
        HF=False,
        # fut_traj_len=65,
    ),
)

_CAR = Z10
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=False,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["debug_sample_track"],
        only_key_frame=True,
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=None,
    radar=None,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
    roi_mask=[-32, 0, 32, 120],
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["CAR9_BMK_OCC_DAY"])
val_dataset_cfg["annotation"]["box"].update(occlusion_threshold=1)  # 这里需要确认下!
# val_dataset_cfg["radar"]["with_virtual_radar"] = False
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="e2e_l3_far_32m_front",
        eval_cfg_l2="cam_l3_front_sixclass",  # "",
    )
)
print("train_cfg ", base_dataset_cfg)
print("----" * 10)
print("val_cfg ", val_dataset_cfg)
