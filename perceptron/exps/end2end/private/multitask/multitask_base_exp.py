import sys

import refile
import torch
import mmcv
import json
from abc import abstractmethod

from perceptron.exps.end2end.private.multitask.model_cfg.det_model_cfg_8v1l_sparse_y120x32m_occ_multiclass_repvggqopt_front4vocc_god_1l import (
    MODEL_CFG,
)
from perceptron.exps.base_exp import BaseExp


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class MultiTaskBaseExp(BaseExp):
    TASKS = ["od", "occ", "god", "map", "lidar_only"]

    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(MultiTaskBaseExp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35

        self.kwargs = kwargs

        # 3. model configuration and other configuration change in this function
        self.model_cfg = mmcv.Config(MODEL_CFG)
        # self._change_model_cfg_params()

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()
        self._update_bmk_by_command()

        # 4. other configuration
        self.task_flag = None

    @property
    def val_dataloader(self):
        if self.task_flag in ["box", "e2e"] and "_val_od_dataloader" not in self.__dict__:
            self._val_od_dataloader = self._configure_val_od_dataloader()
        elif self.task_flag == "occ" and "_val_occ_dataloader" not in self.__dict__:
            self._val_occ_dataloader = self._configure_val_occ_dataloader()
        elif self.task_flag == "map" and "_val_map_dataloader" not in self.__dict__:
            self._val_map_dataloader = self._configure_val_map_dataloader()
        elif self.task_flag == "god" and "_val_god_dataloader" not in self.__dict__:
            self._val_god_dataloader = self._configure_val_god_dataloader()
        elif self.task_flag == "lidar_only" and "_val_lidar_only_dataloader" not in self.__dict__:
            self._val_lidar_only_dataloader = self._configure_val_lidar_only_dataloader()
        elif self.task_flag is None:
            # 默认初始化od的dataloader
            self._val_od_dataloader = self._configure_val_od_dataloader()

        if self.task_flag in ["box", "e2e"] or self.task_flag is None:
            return self._val_od_dataloader
        elif self.task_flag == "occ":
            return self._val_occ_dataloader
        elif self.task_flag == "map":
            return self._val_map_dataloader
        elif self.task_flag == "god":
            return self._val_god_dataloader
        elif self.task_flag == "lidar_only":
            return self._val_lidar_only_dataloader
        else:
            raise NotImplementedError(f"no supported task_flag: {self.task_flag}")

    def _configure_val_od_dataloader(self):
        pass

    def _configure_val_occ_dataloader(self):
        pass

    def _configure_val_map_dataloader(self):
        pass

    def _configure_val_god_dataloader(self):
        pass

    def _configure_val_lidar_only_dataloader(self):
        pass

    @torch.no_grad()
    def test_step(self, batch):
        outputs_dict = self.model(**batch)
        if self.task_flag == "box":
            outs_obstacle = outputs_dict["obstacle"]
            return self.test_od_step(outs_obstacle)
        elif self.task_flag == "occ":
            outs_freespace = outputs_dict["freespace"]
            return self.test_occ_step(outs_freespace)
        elif self.task_flag == "map":
            outs_map = outputs_dict["map"]
            return self.test_map_step(outs_map)
        elif self.task_flag == "e2e":
            return self.test_e2e_step(outs_obstacle)
        elif self.task_flag == "god":
            outs_god = outputs_dict["god"]
            return self.test_od_step(outs_god)
        elif self.task_flag == "lidar_only":
            outs_lidar_only = outputs_dict["lidar_only"]
            return self.test_lidar_only_step(outs_lidar_only)
        else:
            raise NotImplementedError

    @torch.no_grad()
    def test_od_step(self, pred_dicts):
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_e2e_step(self, pred_dicts):
        return pred_dicts

    @torch.no_grad()
    def test_occ_step(self, pred_dicts):
        pred_dicts["pred_seg"] = pred_dicts["pred_seg"].argmax(-1).to(torch.uint8)
        remap_pred_dicts = []
        for pred_seg in pred_dicts["pred_seg"]:
            remap_pred_dict = {"pred_seg": pred_seg}
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_map_step(self, pred_dicts):
        raise NotImplementedError  # TODO

    @torch.no_grad()
    def test_lidar_only_step(self, pred_dicts):
        if "pred_dicts" in pred_dicts:
            pred_dicts = pred_dicts["pred_dicts"]
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "pred_boxes":
                    remap_pred_dict[k] = v
                elif k == "pred_labels":
                    remap_pred_dict[k] = v
                elif k == "pred_scores":
                    remap_pred_dict[k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
                    remap_pred_dict["pred_labels"] -= 1
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _update_bmk_by_command(self):
        def get_datasets_names(bmk_path):
            with refile.smart_open(bmk_path, "r") as f:
                data = json.load(f)
            if isinstance(data, dict):
                return data["paths"]
            elif isinstance(data, list):
                return data
            else:
                raise ValueError(f"Unsupported data format in {bmk_path}. Expected dict or list, got {type(data)}.")

        for task in self.TASKS:
            bmk_arg = f"bmk_{task}"
            bmk_path = self.kwargs.get(bmk_arg, None)
            if bmk_path is not None:
                cfg_name = f"data_val_{task}_cfg"
                if hasattr(self, cfg_name):
                    getattr(self, cfg_name)["loader"]["datasets_names"] = get_datasets_names(bmk_path)
                else:
                    raise ValueError(f"data val cfg for {cfg_name} not found in {self.__class__.__name__}.")

    @classmethod
    def add_argparse_args(cls, parser):  # pragma: no-cover
        parser = super(MultiTaskBaseExp, cls).add_argparse_args(parser)
        # For better discoverability, we explicitly list the benchmark arguments.
        # The logic to handle them is in _update_bmk_by_command.
        parser.add_argument(
            "--bmk_od", type=str, default=None, help="Path to benchmark file for Object Detection task."
        )
        parser.add_argument("--bmk_occ", type=str, default=None, help="Path to benchmark file for Occupancy task.")
        parser.add_argument(
            "--bmk_god", type=str, default=None, help="Path to benchmark file for General Object Detection task."
        )
        parser.add_argument("--bmk_map", type=str, default=None, help="Path to benchmark file for Map task.")
        parser.add_argument(
            "--bmk_lidar_only", type=str, default=None, help="Path to benchmark file for Lidar-only task."
        )
        return parser

    @torch.no_grad()
    def infer_step(self, batch):
        outputs_dict = self.model(**batch)
        outs_obstacle = outputs_dict["obstacle"]
        outs_freespace = outputs_dict["freespace"]
        # outs_map = outputs_dict["map"]
        outs_god = outputs_dict["god"]

        # occ + god + e2e
        pred_e2e = self.test_e2e_step(outs_obstacle)
        pred_occ = self.test_occ_step(outs_freespace)
        pred_god = self.test_od_step(outs_god)
        pred_dicts = {
            "occ_pred": pred_occ,
            "god_pred": pred_god,
            "e2e_pred": pred_e2e,
        }

        return pred_dicts

    @property
    @abstractmethod
    def supported_tasks(self):
        raise NotImplementedError("Please implement the supported_tasks property in the subclass.")
