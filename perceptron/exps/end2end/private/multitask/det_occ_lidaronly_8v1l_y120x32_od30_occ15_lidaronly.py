"""
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --cpu=80 --gpu=8 --memory=$((1024*400)) --max-wait-duration=1000h --positive-tags H20   --set-env DISTRIBUTED_JOB=true --set-env NCCL_IB_TIMEOUT=7200000 --set-env CUDA_DEVICE_MAX_CONNECTIONS=1 \
  --set-env NCCL_ALGO=tree --set-env TORCH_DDP_GRAD_AS_BUCKET_VIEW=1 --set-env TORCH_DDP_BUCKET_CAP_MB=64 \
  --custom-resources rdma/mlnx_shared=8 --custom-resources mellanox.com/mlnx_rdma=8 \
  --preemptible no -n mach-generator  --group=generator_gpu --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=juicefs+s3://oss.i.machdrive.cn/perceptron-cache:/mnt/cache --replica-restart=never -- \
python3 perceptron/exps/end2end/private/multitask/det_occ_god_lidaronly_8v1l_y120x32_od30_occ15_god1.py -b 4 -e 24 --no-clearml --pretrained_model s3://mtx-qy/model/release/0710/checkpoint_epoch_4.pth --find_unused_parameters

"""
import sys
import copy
import refile
import torch
import mmcv
from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler


from perceptron.exps.end2end.private.multitask.model_cfg.det_occ_god_model_cfg import (
    MODEL_CFG,
    CLASS_NAMES,
    OCC_CLASS_NAMES,
)

from perceptron.exps.end2end.private.multitask.data_cfg.multitask_data_cfg import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_od_bmk02_cfg as DATA_VAL_OD_BMK02_CFG,
    val_dataset_occ_bmk01_cfg as DATA_VAL_OCC_BMK01_CFG,
)
from perceptron.exps.end2end.private.god.data_cfg.det_annos_hf_200m_32m_8v5r1l_mmL_chengqu_Z10_new_fovrange_120_god_4cls import (
    val_dataset_god_cfg as DATA_VAL_GOD_CFG,
    class_names_god as CLASS_NAMES_GOD,
)
from perceptron.exps.end2end.private.od_occ.data_cfg.det_annos_1L import _PIPELINE_MULTIFRAME, lidar_only_annos
from perceptron.exps.end2end.private.od_occ.data_cfg.datasets_names import OD_0615_30W_PRELABEL  # noqa
from perceptron.exps.end2end.private.od_occ.data_cfg.datasets_names_occ import OCC_H5_15W  # noqa
from perceptron.exps.end2end.private.multitask.multitask_base_exp import MultiTaskBaseExp
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.det3d.private.private_multimodal_multitask_data import PrivateMultitaskDataset
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron_multi_task import VisionEncoder_MultiTaskV2
from perceptron.data.det3d.private.multi_task_dataset import (
    FlexibleMultiTaskIntervalSamplerV2,
    get_dataset_boundaries,
    collate_fn_wrapper,
)


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子


class Exp(MultiTaskBaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, **kwargs)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        # self.lr = 2e-5  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35
        self.have_log = False

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 3. data cfg
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 4. other configuration
        self.task_flag = None

        # to record: 用于在log中记录需要保存的参数
        self.train_data = {
            "od": self.data_train_cfg_z10_od["loader"]["datasets_names"],
            "occ": self.data_train_occ["loader"]["datasets_names"],
            "god": self.data_train_god["loader"]["datasets_names"],
        }
        self.eval_name = "Z10_eval_od_bmk02_occ_bmk02_{}_filter_vis".format(
            self.data_val_occ_cfg["loader"]["datasets_names"][0]
        )
        self.val_data = {
            "od": self.data_val_od_cfg["loader"]["datasets_names"],
            "occ": self.data_val_occ_cfg["loader"]["datasets_names"],
        }
        self.train_tag = "not_amp_all_occ_mask_false"
        self.od_class_names = CLASS_NAMES
        self.occ_class_names = OCC_CLASS_NAMES

    def _get_train_dataset_cfg(self):
        self.data_train_od = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_od["pipeline"] = _PIPELINE_MULTIFRAME

        self.data_train_od["loader"]["datasets_names"] = [
            "prelabel_vru_tmp_0402",
            "prelabel_rainy_daily_0402",
            "prelabel_rainy_night_0402",
            "prelabel_cut_in_0402",
            "prelabel_diaotou_0402",
            "prelabel_huandao_0402",
            "prelabel_zhuanwan_0402",
            "prelabel_pose_0403",
            "prelabel_vru_0325",
            "prelabel_vru_0315",
        ]
        self.data_train_od["loader"]["datasets_names"] = [
            "car_z10_occ_humanlabeled_20250410_huiliu_jixie_288_3d_h5_multicls"
        ]
        self.data_train_od["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_od["loader"]["only_key_frame"] = True
        self.data_train_od["annotation"]["box"]["occlusion_threshold"] = 1
        # self.data_train_od["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        self.data_train_od["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_od["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]

        self.data_train_cfg_z10_od = copy.deepcopy(self.data_train_od)
        self.data_train_cfg_z10_od["annotation"].pop("occ")
        self.data_train_cfg_z10_od["annotation"].pop("god")
        self.data_train_cfg_z10_od["annotation"]["lidar_only"] = lidar_only_annos

        self.data_train_occ = copy.deepcopy(self.data_train_od)
        self.data_train_occ["annotation"].pop("box")
        self.data_train_occ["annotation"].pop("god")
        self.data_train_occ["loader"]["datasets_names"] = [
            "car_z10_occ_humanlabeled_20250410_huiliu_jixie_288_3d_h5_multicls",
            "car_z10_occ_prelabel_20250414_huiliu_jixie_2984_3d_h5_multicls",
            "car_z10_occ_prelabel_20250415_huiliu_jixie_1624_3d_multiclass_h5",
            "car_z10_occ_prelabel_20250415_huiliu_jixie_1538_3d_h5_multicls",
            "car_z10_occ_prelabel_20250417_huiliu_jixie_1430_3d_h5_multicls",
            "car_z10_occ_humanlabels_20250423_huiliu_jixie_1959_3d_h5_multicls",
            "car_z10_occ_humanlabels_20250427_huiliu_jixie_2941_3d_multicls_h5",
            "car_z10_occ_prelabel_20250504_huiliu_jixie_4498_3d_multicls_h5",
            "car_z10_occ_humanlabels_20250513_184912_huiliu_jixie_306_3d_multicls",
            "car_z10_occ_humanlabels_20250513_184299_huiliu_jixie_53_3d_multicls",
            "car_z10_occ_humanlabels_20250513_183404_huiliu_jixie_344_3d_multicls",
        ]
        self.data_train_occ["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_train_occ["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_occ["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]

        self.data_train_god = copy.deepcopy(self.data_train_od)
        self.data_train_god["annotation"].pop("box")
        self.data_train_god["annotation"].pop("occ")

        self.data_train_god["num_frames_per_sample"] = 1
        self.data_train_god["loader"]["datasets_names"] = ["god_train_352keyclips"]
        self.data_train_god["loader"]["only_key_frame"] = True
        self.data_train_god["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_god["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]

    def _get_val_dataset_cfg(self):
        self.data_val_od_cfg = mmcv.Config(DATA_VAL_OD_BMK02_CFG)
        self.data_val_occ_cfg = mmcv.Config(DATA_VAL_OCC_BMK01_CFG)
        self.data_val_occ_cfg["loader"]["datasets_names"] = ["occ_cloud_bmk_200"]
        self.data_val_occ_cfg["evaluator"]["use_image_mask"] = self.model_cfg["freespace_head"]["use_mask"]

        # GOD BMK
        cam = DATA_VAL_GOD_CFG["image"]["camera_names"]
        assert len(DATA_VAL_GOD_CFG["image"]["camera_names"]) == 8, f"{cam} != 8"
        self.data_val_god_cfg = mmcv.Config(DATA_VAL_GOD_CFG)
        self.data_val_god_cfg["loader"]["datasets_names"] = ["god_val"]
        self.data_val_god_cfg["annotation"]["god"]["occlusion_threshold"] = 1
        self.data_val_god_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_god_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_god_cfg["loader"]["only_key_frame"] = True
        self.data_val_god_cfg["num_frames_per_sample"] = 1

        self.occ_class_names = [
            "free",
            "freespace",
            "dynamic",
            "static",
            "noise",  # default water
            "car",
            "larger_vehicle",
            "bicycle",
            "pedestrian",
        ]
        self.god_class_names = CLASS_NAMES_GOD

    def _change_model_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg["radar_encoder"] = None
        # self.model_cfg["god_det_head"] = None
        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)
        from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.lidar_p177_pointpillar_120x50_voxel02_cfg import (
            CENTERPOINT_DET_HEAD_CFG,
        )

        self.model_cfg["lidar_only_head"] = CENTERPOINT_DET_HEAD_CFG

    def _configure_model(self):
        model = VisionEncoder_MultiTaskV2(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset_od = PrivateMultitaskDataset(
            **self.data_train_cfg_z10_od,
            sub_level_annos=True,
        )

        train_dataset_occ = PrivateMultitaskDataset(
            **self.data_train_occ,
            sub_level_annos=True,
        )

        # map
        # train_dataset_occ = PrivateE2EDataset(
        #     **self.data_train_cfg_z10,
        #     fov_boardline=[[0, 0], [-32.0, -0.55 * (-32)], [-32.0, 120], [32.0, 120], [32.0, 0.57 * 32]],
        # )
        # train_dataset_occ_mix = train_dataset_occ #torch.utils.data.ConcatDataset([train_dataset_occ, train_dataset_occ])
        dataset_boundaries = get_dataset_boundaries([train_dataset_od, train_dataset_occ])
        train_dataset = torch.utils.data.ConcatDataset([train_dataset_od, train_dataset_occ])
        train_dataset.batch_postcollate_fn = train_dataset_od.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset_od.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateMultitaskDataset.collate_fn),
            sampler=FlexibleMultiTaskIntervalSamplerV2(
                train_dataset,
                dataset_boundaries,
                per_gpu_dataset_batch_sizes=[2, 2],
                shuffle=True,
                intervals=[16, 12],
                drop_last=True,
                sample_len_type="min",
            ),
            pin_memory=True,
            num_workers=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        raise NotImplementedError

    def _configure_val_od_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_od_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=False),
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_occ_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_occ_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_god_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_god_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=8,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_od_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None

        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)
        return loss, loss_dict

    def _configure_optimizer(self):
        from torch.optim import AdamW

        backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if "encoder" in name:
                backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW(
            [
                {"params": backbone_params, "lr": self.lr * 0.1, "weight_decay": 0.01},
                {"params": other_params, "lr": self.lr, "weight_decay": 0.01},
            ]
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0 / 3 * self.lr,
            end_lr=1e-6,  # eta_min
        )
        return scheduler

    @property
    def supported_tasks(self):
        supported_tasks = ["god", "box", "occ"]
        return supported_tasks


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
