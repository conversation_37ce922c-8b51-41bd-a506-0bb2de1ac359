from perceptron.exps.end2end.private.object.model_cfg.det_model_cfg_8v1l_sparse_y120x32m import (
    CLASS_NAMES,
    _POINT_CLOUD_RANGE,
    _POST_CENTER_RANGE,
    _VOXEL02_VOXEL_SIZE,
    # CAMERA_ENCODER_CFG,
    VOXEL02_DET_HEAD,
    VOXEL02_TRAIN_CFG,
    VOXEL01_TEST_CFG,
    _FRONT_LIDAR_POINT_CLOUD_RANGE,
    _FRONT_LIDAR_VOXEL02_GRID_SIZE,
    _VOXEL02_OUT_SIZE_FACTOR,
    _VOXEL02_GRID_SIZE,
)
from perceptron.exps.end2end.private.occ.model_cfg.fs_model_cfg_1l4v_15m_deform_reorg_vx0p1_multicls import (
    Freespace_head,
)
from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_god import class_names_god as CLASS_NAMES_GOD

VOXEL02_DET_HEAD["bbox_coder"] = dict(
    type="TrackNMSFreeCoder",
    post_center_range=_POST_CENTER_RANGE,
    pc_range=_POINT_CLOUD_RANGE,
    max_num=300,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    num_classes=len(CLASS_NAMES),
    score_threshold=0.0,
)

TRACKING_MODULE_CFG = dict(  # past and future reasoning
    history_reasoning=True,  # use past reasoning
    future_reasoning=True,  # use future reasoning
    apply_feat_transform=False,
    apply_autoencoder=False,
    trajectory_encoding=False,
    use_hist_locs=False,
    add_disturb=False,
    gt_traj_frag_prob=0.0,
    gt_traj_valid_prob=1.0,
    hist_len=3,
    fut_len=8,
    num_classes=len(CLASS_NAMES),
    pc_range=_POINT_CLOUD_RANGE,
    hist_temporal_transformer=dict(
        type="TemporalTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=2,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    spatial_transformer=dict(
        type="TemporalTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=2,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    fut_temporal_transformer=dict(
        type="TemporalTransformer",
        decoder=dict(
            type="PETRTransformerDecoder",
            return_intermediate=True,
            num_layers=2,
            transformerlayers=dict(
                type="PETRTransformerDecoderLayer",
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
)

TRACK_LOSS = dict(
    type="TrackingLossCombo",
    num_classes=len(CLASS_NAMES),
    interm_loss=True,
    code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2.0, alpha=0.25, loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", loss_weight=0.25),
    loss_iou=dict(type="GIoULoss", loss_weight=0.0),
    loss_prediction=dict(type="L1Loss", loss_weight=0.5),
    assigner=dict(
        type="HungarianAssigner3D",
        cls_cost=dict(type="FocalLossCost", weight=2.0),
        reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
        iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
        pc_range=_POINT_CLOUD_RANGE,
    ),
)

RUNTIME_TRACKER = dict(
    output_threshold=0.2,
    score_threshold=0.4,
    record_threshold=0.4,
    max_age_since_update=7,
)

LIDAR_ENCODER_CFG = dict(
    point_cloud_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    max_num_points=16,
    # max_voxels=(260000, 280000),
    max_voxels=(100000, 120000),
    # max_voxels=(120000, 120000),
    src_num_point_features=4,
    use_num_point_features=4,
    map_to_bev_num_features=64,
    vfe=dict(
        vfe_num_filters=[64], num_point_features=4, voxel_size=_VOXEL02_VOXEL_SIZE, point_cloud_range=_POINT_CLOUD_RANGE
    ),
    backbone_2d=dict(
        layer_nums=[3, 5, 5],
        layer_strides=[2, 2, 2],
        num_filters=[64, 128, 256],
        upsample_strides=[1, 2, 4],
        num_upsample_filters=[128, 128, 128],
        input_channels=64,  # sp conv output channel
        with_cp=True,
        use_scconv=True,
        upsample_output=False,
    ),
)

CAMERA_ENCODER_CFG = dict(
    type="CameraEncoderMultiNeck",
    img_backbone=dict(
        type="RepVGGQOPT",
        num_blocks=[4, 6, 16, 1],
        width_multiplier=[2, 2, 2, 4],
        override_groups_map=None,
        use_checkpoint=False,
        pretrained="s3://yutao/tmp/ckpt/pretrained/RepVGG-B1-train.pth",
    ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[128, 256, 512, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
    img_neck_occ=dict(
        type="SECONDFPN",
        in_channels=[128, 256, 512, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
    img_neck_god=dict(
        type="SECONDFPN",
        in_channels=[128, 256, 512, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)

GOD_VOXEL02_DET_HEAD = dict(
    type="SparseE2EHead",
    init_radar_num_query=0,
    in_channels=512,
    num_query=200,
    modal=["Lidar", "Camera"],
    depth_num=64,
    hidden_dim=256,
    downsample_scale=_VOXEL02_OUT_SIZE_FACTOR,
    grid_size=_VOXEL02_GRID_SIZE,
    front_lidar_grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    # check
    use_dn=False,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=len(CLASS_NAMES_GOD),
            class_names=CLASS_NAMES_GOD,
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=_POST_CENTER_RANGE,
        pc_range=_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=_VOXEL02_VOXEL_SIZE,
        num_classes=len(CLASS_NAMES_GOD),
        score_threshold=0.1,
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=3),
    transformer=dict(
        type="MOTDeformableTransformer",
        decoder=dict(
            type="DeformableTransformerDecoder",
            num_layers=6,
            transformerlayers=dict(
                type="DeformableTransformerDecoderLayer",
                batch_first=True,
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(
                        type="DeformableFeatureAggregationCuda",
                        embed_dims=256,
                        num_groups=8,
                        num_levels=1,
                        num_cams=8,  # TODO 与实际相机数量保持一致
                        dropout=0.1,
                        num_pts=13,
                        bias=2.0,
                    ),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
    use_roi_mask=False,
)


MODEL_GOD_CFG = dict(
    num_query=400,
    num_near_query=100,
    num_classes=len(CLASS_NAMES_GOD),
    tracking=False,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES_GOD,
    det_head=GOD_VOXEL02_DET_HEAD,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=True,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    radar_encoder=None,
    lidar_encoder=LIDAR_ENCODER_CFG,
    det_head=VOXEL02_DET_HEAD,
    god_head=MODEL_GOD_CFG,
    tracking_module=TRACKING_MODULE_CFG,
    track_loss=TRACK_LOSS,
    runtime_tracker=RUNTIME_TRACKER,
    if_update_ego=True,  # update the ego-motion
    fut_prediction_ref_update=True,  # use forecasting to update cross-frame movements
    # map_head = None,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
    freespace_head=Freespace_head,
)
