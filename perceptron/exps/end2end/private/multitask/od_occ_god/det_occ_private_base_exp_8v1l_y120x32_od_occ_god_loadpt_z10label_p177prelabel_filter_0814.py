"""
python /data/code/e2e/perceptron_v2/perceptron/perceptron/exps/end2end/private/od_occ/multi_car/det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0720_release_test_mini_data.py -b 4 -e 40 --no-clearml --sync_bn 1

E2E_USE_UNIFIED_CUDA_IMAGE_PROCESSOR=True python /data/code/e2e/perceptron/perceptron/exps/end2end/private/od_occ/multi_car/det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0807_release.py -b 4 -e 40 --no-clearml --sync_bn 1
CUDA_VISIBLE_DEVICES=0
E2E_USE_UNIFIED_CUDA_IMAGE_PROCESSOR=True python /data/code/e2e/perceptron/perceptron/exps/end2end/private/od_occ/multi_car/det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0720_release_test_mini_data_occ.py -b 4 -e 1 --no-clearml --sync_bn 1
 > check_iterbase.log 2>&1
CUDA_VISIBLE_DEVICES=0
E2E_USE_UNIFIED_CUDA_IMAGE_PROCESSOR=True python /data/code/e2e/perceptron/perceptron/exps/end2end/private/od_occ/multi_car/det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0720_release_test_mini_data_occ.py --eval -b 4 --ckpt /data/outputs/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0720_release_test_mini_data_occ/2025-08-02T10:45:04/dump_model/checkpoint_epoch_7.pth
/data/outputs/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0720_release_test_mini_data/2025-07-31T21:47:14/dump_model/checkpoint_epoch_12.pth

E2E_USE_UNIFIED_CUDA_IMAGE_PROCESSOR=True python /data/code/e2e/perceptron/perceptron/exps/end2end/private/od_occ/multi_car/det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0807_release.py --eval_occ -b 4 --ckpt /data/outputs/exp-20250802-222950-mot/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0807_release/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0807_release/dump_model/checkpoint_epoch_2.pth
# /data/outputs/exp-20250802-222950-mot/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0807_release/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0807_release/dump_model/checkpoint_epoch_2.pth/data/outputs/multi_car__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_multicar_nowarp_0720_release_test_mini_data_occ/2025-08-02T10:45:04/dump_model/checkpoint_epoch_7.pth

"""
import sys
import copy
import refile
import torch
import mmcv
from perceptron.engine.cli import OneModelCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler


from perceptron.exps.end2end.private.multitask.model_cfg.det_model_cfg_8v1l_sparse_y120x32m_occ_multiclass_repvggqopt_front4vocc_god_1l import (
    MODEL_CFG,
    CLASS_NAMES,
    OCC_CLASS_NAMES,
)
from perceptron.exps.multisensor_fusion.private.BEVFusion.model_base_cfg.lidar_p177_pointpillar_120x50_voxel02_cfg import (
    CLASS_NAMES as LIDAR_ONLY_CLASS_NAMES,
)
from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_god import class_names_god as GOD_CLASS_NAMES
from perceptron.exps.end2end.private.multitask.data_cfg.multitask_data_cfg import (
    z10_base_dataset_cfg as Z10_DATA_TRAIN_CFG,
    p177_base_dataset_cfg as P177_DATA_TRAIN_CFG,
    val_z10_dataset_od_bmk02_cfg as DATA_VAL_Z10_OD_BMK02_CFG,
    val_z10_dataset_occ_bmk01_cfg as DATA_VAL_Z10_OCC_BMK01_CFG,
    val_z10_dataset_god_cfg as DATA_VAL_Z10_GOD_BMK01_CFG,
)

# from perceptron.exps.end2end.private.od_occ.data_cfg.det_annos_hf_120m_32m_8v1l_mmL_chengqu_P177_new_occ_multiclass_nowarp import (
#     base_dataset_cfg as P177_DATA_TRAIN_CFG,
# )
from perceptron.exps.end2end.private.multitask.multitask_base_exp import MultiTaskBaseExp
from perceptron.exps.end2end.private.od_occ.data_cfg.det_annos_1L import lidar_only_annos, _PIPELINE_MULTIFRAME
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.det3d.private.private_multimodal_multitask_data import PrivateMultitaskDataset
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron_multi_task import VisionEncoder_MultiTaskV2
from perceptron.data.det3d.private.multi_task_dataset import (
    FlexibleMultiTaskIntervalSamplerV2,
    get_dataset_boundaries,
    collate_fn_wrapper,
)


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class Exp(MultiTaskBaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, **kwargs)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        # self.lr = 2e-5  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35
        self.have_log = False

        # self.iter_base = True
        # self.dump_step_interval = 4000
        # self.num_step_keep_latest = 2000

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 3. data cfg
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 4. other configuration
        self.task_flag = None

        # to record: 用于在log中记录需要保存的参数
        self.train_data = {
            "od_z10": self.data_train_cfg_cq_prelabel_z10["loader"]["datasets_names"],
            "occ_z10": self.data_train_cfg_z10_occ_jixie["loader"]["datasets_names"],
        }
        self.eval_name = "Z10_eval_od_bmk02_occ_bmk02_{}_filter_vis".format(
            self.data_val_occ_cfg["loader"]["datasets_names"][0]
        )
        self.val_data = {
            "od": self.data_val_od_cfg["loader"]["datasets_names"],
            "occ": self.data_val_occ_cfg["loader"]["datasets_names"],
        }
        self.train_tag = "use_can_bus_True"
        self.od_class_names = CLASS_NAMES
        self.occ_class_names = OCC_CLASS_NAMES
        self.lidar_only_class_names = LIDAR_ONLY_CLASS_NAMES
        self.god_class_names = GOD_CLASS_NAMES

    def _get_train_dataset_cfg(self):
        self.data_train_cfg_cq_prelabel_z10 = mmcv.Config(Z10_DATA_TRAIN_CFG)
        self.data_train_cfg_cq_prelabel_z10["pipeline"] = _PIPELINE_MULTIFRAME

        self.data_train_cfg_cq_p177 = mmcv.Config(P177_DATA_TRAIN_CFG)
        self.data_train_cfg_cq_p177["pipeline"] = _PIPELINE_MULTIFRAME

        self.data_train_cfg_cq_prelabel_z10["loader"]["datasets_names"] = ["multi_car_z10_od_50w_filter"]

        self.data_train_cfg_cq_prelabel_z10["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg_cq_prelabel_z10["loader"]["only_key_frame"] = True
        self.data_train_cfg_cq_prelabel_z10["annotation"]["box"]["occlusion_threshold"] = 1
        # self.data_train_cfg_cq_prelabel_z10["annotation"]["box"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        self.data_train_cfg_cq_prelabel_z10["annotation"]["box"]["cam_vis_state"] = [0, 1, 2, 3]
        self.data_train_cfg_cq_prelabel_z10["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
        self.data_train_cfg_cq_prelabel_z10["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]

        self.data_train_cfg_z10_od = copy.deepcopy(self.data_train_cfg_cq_prelabel_z10)
        self.data_train_cfg_z10_od["annotation"].pop("occ")
        self.data_train_cfg_z10_od["annotation"].pop("god")
        self.data_train_cfg_z10_od["annotation"]["lidar_only"] = lidar_only_annos

        self.data_train_cfg_z10_occ_jixie = copy.deepcopy(self.data_train_cfg_cq_prelabel_z10)
        self.data_train_cfg_z10_occ_jixie["annotation"].pop("box")
        self.data_train_cfg_z10_occ_jixie["annotation"].pop("god")
        self.data_train_cfg_z10_occ_jixie["loader"]["datasets_names"] = ["multi_car_z10_occ_50w_gpfs"]

        self.data_train_cfg_z10_occ_jixie["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_train_cfg_z10_occ_jixie["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_train_cfg_z10_occ_jixie["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]

        self.z10_data_train_god = copy.deepcopy(self.data_train_cfg_cq_prelabel_z10)
        self.z10_data_train_god["annotation"].pop("box")
        self.z10_data_train_god["annotation"].pop("occ")

        self.z10_data_train_god["num_frames_per_sample"] = 1
        self.z10_data_train_god["loader"]["datasets_names"] = ["z10_god_label_323clips_14213frame"]
        self.z10_data_train_god["loader"]["only_key_frame"] = True
        self.z10_data_train_god["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.z10_data_train_god["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.z10_data_train_god["annotation"]["god"]["occlusion_threshold"] = 1  # occlusion
        self.z10_data_train_god["annotation"]["god"]["with_occlusion"] = True
        self.z10_data_train_god["annotation"]["god"]["soft_occ_threshold"] = SOFT_OCC_THRESHOLD
        self.z10_data_train_god["annotation"]["god"]["fixyaw"] = True
        self.z10_data_train_god["annotation"]["god"]["filter_outlier_boxes"] = True

        self.z10_data_train_god_2 = copy.deepcopy(self.z10_data_train_god)
        self.z10_data_train_god_2["loader"]["datasets_names"] = [
            "z10_god_label_chengqu_392clips_15992frame",
            "z10_god_label_gaosu_19clips_371frame",
        ]
        self.z10_data_train_god_2["sensor_names"]["lidar_names"] = ["fuser_lidar"]
        self.z10_data_train_god_2["lidar"]["lidar_names"] = ["fuser_lidar"]

        self.p177_data_train_god_prelabel = copy.deepcopy(self.data_train_cfg_cq_p177)
        self.p177_data_train_god_prelabel["annotation"].pop("box")
        self.p177_data_train_god_prelabel["annotation"].pop("occ")
        self.p177_data_train_god_prelabel["loader"]["datasets_names"] = ["p177_god_prelabel_70wframe"]
        self.p177_data_train_god_prelabel["annotation"]["god"]["label_key"] = "pre_labels"
        self.p177_data_train_god_prelabel["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.p177_data_train_god_prelabel["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]

    def _get_val_dataset_cfg(self):
        self.data_val_od_cfg = mmcv.Config(DATA_VAL_Z10_OD_BMK02_CFG)
        self.data_val_od_cfg["annotation"].pop("god", None)
        self.data_val_occ_cfg = mmcv.Config(DATA_VAL_Z10_OCC_BMK01_CFG)
        self.data_val_occ_cfg["loader"]["datasets_names"] = [
            "occ_cloud_bmk_200"
            # "occ_cloud_bmk_debug_2"
        ]  # ["occ_cloud_bmk_debug"] #["bad_occ_bmk2"] #["bad_occ_train2"] #["bad_occ_bmk1"]
        self.data_val_occ_cfg["evaluator"]["use_image_mask"] = self.model_cfg["freespace_head"]["use_mask"]
        self.data_val_occ_cfg["annotation"].pop("god", None)
        # self.data_val_occ_cfg["evaluator"]["use_image_mask"]

        self.data_val_lidar_only_cfg = copy.deepcopy(self.data_val_od_cfg)
        self.data_val_lidar_only_cfg["evaluator"]["eval_cfg_l3"] = "p177_50x120"
        self.data_val_lidar_only_cfg["evaluator"]["eval_cfg_l2"] = None
        self.data_val_god_cfg = mmcv.Config(DATA_VAL_Z10_GOD_BMK01_CFG)
        self.data_val_god_cfg["loader"]["datasets_names"] = ["god_val"]
        self.data_val_god_cfg["annotation"].pop("occ", None)
        self.data_val_god_cfg["annotation"].pop("box", None)

    def _change_model_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        self.model_cfg.det_head.bbox_coder.score_threshold = 0.1
        self.model_cfg.num_query = 0 + 300 + 100
        self.model_cfg["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["det_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg.num_near_query = 100

        self.model_cfg["radar_encoder"] = None
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_lidar"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1]["with_img"] = True
        self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask
        self.model_cfg["det_head"]["refine_reg_branch"] = True

        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)

        self.model_cfg["freespace_head"]["use_mask"] = False
        self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"] = 4
        self.model_cfg["freespace_head"]["lidar_init"] = True
        self.model_cfg["freespace_head"]["loss_weight"].update(loss_dice=2.0)

        self.model_cfg["freespace_head"]["lidar_feat_crop"] = dict(
            crop_h=(0, 204), crop_w=(160 // 2 - 76 // 2, 160 // 2 + 76 // 2)
        )
        # Mask
        self.model_cfg["freespace_head"]["use_mask"] = False
        self.model_cfg["freespace_head"]["use_weighted_mask"] = True
        self.model_cfg["freespace_head"]["dis_weight"] = True
        self.model_cfg["freespace_head"]["mask_loss_type"] = 0
        self.model_cfg["freespace_head"]["surround"] = False
        self.model_cfg["freespace_head"]["norm_cfg"] = dict(type="BN2d", eps=1e-3, momentum=0.1)
        self.model_cfg["freespace_head"]["transformer"]["num_feature_levels"] = 1
        self.model_cfg["freespace_head"]["transformer"]["use_can_bus"] = True

        # 修改lidar cfg
        self.model_cfg["lidar_encoder"]["backbone_2d"]["type"] = "PointPillarBackbone"

        # add visible head
        self.model_cfg["freespace_head"]["use_visible_head"] = True
        self.model_cfg["freespace_head"]["height_layer_num"] = 2

        self.model_cfg["lidar_only_head"] = None

        # god model
        self.model_cfg["god_head"]["det_head"]["bbox_coder"]["score_threshold"] = 0.1
        self.model_cfg["god_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg["god_head"]["det_head"]["init_radar_num_query"] = 0
        self.model_cfg["god_head"]["det_head"]["num_query"] = 0 + 300 + 100
        self.model_cfg["god_head"]["det_head"]["modal"] = ["Lidar", "Camera"]
        self.model_cfg["god_head"]["num_near_query"] = 100

        self.model_cfg["radar_encoder"] = None
        self.model_cfg["god_head"]["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1][
            "with_lidar"
        ] = True
        self.model_cfg["god_head"]["det_head"]["transformer"]["decoder"]["transformerlayers"]["attn_cfgs"][1][
            "with_img"
        ] = True
        self.model_cfg["god_head"]["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        self.model_cfg["god_head"]["det_head"]["separate_head"]["final_kernel"] = 1
        self.model_cfg["god_head"]["dn_cfg"] = dict(
            use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        )
        self.model_cfg["god_head"]["det_head"]["use_roi_mask"] = True  # gs cq 联合训练必须有roi mask

        self.model_cfg["god_head"]["det_head"]["refine_reg_branch"] = True

        self.model_cfg["load_od2god"] = True

    def _configure_model(self):
        model = VisionEncoder_MultiTaskV2(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):

        train_dataset_z10_occ_jixie = PrivateMultitaskDataset(
            **self.data_train_cfg_z10_occ_jixie,
        )

        train_dataset_z10_od = PrivateMultitaskDataset(
            **self.data_train_cfg_z10_od,
        )

        train_dataset_god = PrivateMultitaskDataset(
            **self.z10_data_train_god,
        )
        train_dataset_god2 = PrivateMultitaskDataset(
            **self.z10_data_train_god_2,
        )

        train_dataset_god3 = PrivateMultitaskDataset(
            **self.p177_data_train_god_prelabel,
        )

        train_dataset_god_total = torch.utils.data.ConcatDataset(
            [train_dataset_god, train_dataset_god2, train_dataset_god3]
        )
        dataset_boundaries = get_dataset_boundaries(
            [train_dataset_z10_od, train_dataset_z10_occ_jixie, train_dataset_god_total]
        )
        train_dataset = torch.utils.data.ConcatDataset(
            [train_dataset_z10_od, train_dataset_z10_occ_jixie, train_dataset_god_total]
        )
        train_dataset.batch_postcollate_fn = train_dataset_god3.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset_god3.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn),
            sampler=FlexibleMultiTaskIntervalSamplerV2(
                train_dataset,
                dataset_boundaries,
                per_gpu_dataset_batch_sizes=[2, 2, 2],
                # per_gpu_dataset_batch_sizes=[1, 1, 1, 1],
                shuffle=True,
                intervals=[8, 8, 12],
                drop_last=False,
            ),
            # sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0),
            # sampler=InfiniteIntervalSampler(len(train_dataset), seed=self.seed if self.seed else 0, interval=3),
            pin_memory=True,
            num_workers=12,
        )
        return train_loader

    def _configure_val_dataloader(self):
        raise NotImplementedError

    def _configure_val_od_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_od_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=False),
            num_workers=12,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_occ_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_occ_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=12,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_god_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_god_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=12,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_val_lidar_only_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_lidar_only_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn_wrapper(PrivateE2EDataset.collate_fn, is_training=False),
            num_workers=12,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            # sampler=None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_od_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None

        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(
            _value for _key, _value in loss_dict.items() if ("loss" in _key and isinstance(_value, torch.Tensor))
        )
        return loss, loss_dict

    def _configure_optimizer(self):
        from torch.optim import AdamW

        backbone_params = []
        other_params = []
        for name, param in self.model.named_parameters():
            if "encoder" in name:
                backbone_params.append(param)
            else:
                other_params.append(param)

        optimizer = AdamW(
            [
                {"params": backbone_params, "lr": self.lr * 0.1, "weight_decay": 0.01},
                {"params": other_params, "lr": self.lr, "weight_decay": 0.01},
            ]
        )
        return optimizer

    @torch.no_grad()
    def test_occ_step(self, pred_dicts):
        remap_pred_dicts = []
        for i in range(len(pred_dicts["pred_seg"])):
            remap_pred_dict = {
                "pred_seg": pred_dicts["pred_seg"][i].argmax(-1).to(torch.uint8),
                "vismask_pred": pred_dicts["vismask_pred"][i].argmax(-1).to(torch.uint8),
            }
            remap_pred_dicts.append(remap_pred_dict)

        return {"pred_dicts": remap_pred_dicts}

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            warmup_epochs=0.5,
            warmup_lr_start=1.0 / 3 * self.lr,
            end_lr=1e-6,  # eta_min
        )
        return scheduler

    @property
    def supported_tasks(self):
        supported_tasks = ["box", "god", "occ"]
        return supported_tasks


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    OneModelCli(Exp).run()
    # exp = Exp()
    # import tqdm
    # train_dataset_occ_jixie = PrivateE2EDataset(
    #     **exp.data_train_cfg_z10_occ_jixie,
    # )
    # for i in tqdm.tqdm(range(len(train_dataset_occ_jixie))):
    #     batch = train_dataset_occ_jixie[i]
    #     print(i)
