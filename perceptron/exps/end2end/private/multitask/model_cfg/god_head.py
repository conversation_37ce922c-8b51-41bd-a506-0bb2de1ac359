from perceptron.exps.end2end.private.sensor_cfg.e2e_annos_god import class_names_god as CLASS_NAMES_GOD
from perceptron.exps.end2end.private.mot_god.model_cfg.det_model_cfg_8v1l_sparse_y120x32m import (
    CLASS_NAMES,
    _POINT_CLOUD_RANGE,
    _POST_CENTER_RANGE,
    _VOXEL02_VOXEL_SIZE,
    # CAMERA_ENCODER_CFG,
    VOXEL02_DET_HEAD,
    VOXEL02_TRAIN_CFG,
    VOXEL01_TEST_CFG,
    _FRONT_LIDAR_POINT_CLOUD_RANGE,
    _FRONT_LIDAR_VOXEL02_GRID_SIZE,
    _VOXEL02_OUT_SIZE_FACTOR,
    _VOXEL02_GRID_SIZE,
)

GOD_VOXEL02_DET_HEAD = dict(
    type="SparseE2EHead",
    in_channels=512,
    num_query=400,
    init_radar_num_query=0,  # radar query number
    modal=["Lidar", "Camera"],
    depth_num=64,
    hidden_dim=256,
    downsample_scale=_VOXEL02_OUT_SIZE_FACTOR,
    grid_size=_VOXEL02_GRID_SIZE,
    front_lidar_grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    # check
    use_dn=False,
    common_heads=dict(center=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2), vel=(2, 2)),
    tasks=[
        dict(
            num_class=len(CLASS_NAMES_GOD),
            class_names=CLASS_NAMES_GOD,
        ),
    ],
    bbox_coder=dict(
        type="MultiTaskBBoxCoder",
        post_center_range=_POST_CENTER_RANGE,
        pc_range=_POINT_CLOUD_RANGE,
        max_num=300,
        voxel_size=_VOXEL02_VOXEL_SIZE,
        num_classes=len(CLASS_NAMES_GOD),
        score_threshold=0.1,
    ),
    separate_head=dict(type="SeparateTaskHead", init_bias=-2.19, final_kernel=1),
    transformer=dict(
        type="MOTDeformableTransformer",
        decoder=dict(
            type="DeformableTransformerDecoder",
            num_layers=3,
            transformerlayers=dict(
                type="DeformableTransformerDecoderLayer",
                batch_first=True,
                with_cp=False,
                attn_cfgs=[
                    dict(type="MultiheadAttention", embed_dims=256, num_heads=8, dropout=0.1),
                    dict(
                        type="DeformableFeatureAggregationCuda",
                        embed_dims=256,
                        num_groups=8,
                        num_levels=1,
                        num_cams=8,  # TODO 与实际相机数量保持一致
                        dropout=0.1,
                        num_pts=13,
                        bias=2.0,
                        with_lidar=True,
                        with_img=True,
                    ),
                ],
                ffn_cfgs=dict(
                    type="FFN",
                    embed_dims=256,
                    feedforward_channels=1024,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type="ReLU", inplace=True),
                ),
                feedforward_channels=1024,  # unused
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    loss_cls=dict(type="FocalLoss", use_sigmoid=True, gamma=2, alpha=0.25, reduction="mean", loss_weight=2.0),
    loss_bbox=dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    loss_heatmap=dict(type="GaussianFocalLoss", reduction="mean", loss_weight=1.0),
    use_roi_mask=True,
    refine_reg_branch=True,
)
