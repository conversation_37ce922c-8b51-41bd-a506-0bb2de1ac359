OCC_CLASS_NAMES = [
    "free",
    "freespace",
    "dynamic",
    "static",
    "noise",
    "car",
    "larger_vehicle",
    "bicycle",
    "pedestrian",
]

_dim_ = 256
_ffn_dim_ = _dim_ * 2
_pos_dim_ = _dim_ // 2
_num_levels_ = 1

_OCC_POINT_CLOUD_RANGE = [-15.2, -27.2, -5.0, 15.2, 81.6, 3.0]
_OCC_FRONT_LIDAR_POINT_CLOUD_RANGE = [-15.2, 0, -5.0, 15.2, 81.6, 3.0]

# model_size = "small"

# size_mode = 4
cam_scale_factor = 2
lidar_scale_factor = 2

query_shape = [76, 204, 8]
bev_h_ = query_shape[1]
bev_w_ = query_shape[0]
Freespace_head = dict(
    type="",
    in_channel=64,
    fusion_dim=384 + 128,
    pc_range=_OCC_FRONT_LIDAR_POINT_CLOUD_RANGE,
    num_classes=len(OCC_CLASS_NAMES),
    loss_weight=dict(
        loss_cls=10.0,
        loss_dice=2.0,
    ),
    aspp_cfg=None,  # 在unet前面增加aspp
    unet_cfg=None,
    norm_cfg=dict(type="BN2d", eps=1e-3, momentum=0.1),
    modal=["Lidar", "Camera"],
    # model_size=model_size,
    # size_mode=size_mode,
    # upsample_cfg=dict(size=(408 * 2, 152 * 2), mode="bilinear", align_corners=True),
    upsample_cfg=dict(scale_factor=lidar_scale_factor, mode="bilinear", align_corners=True),
    img_encoder_type="Deformable",
    as_two_stage=False,
    use_mask=False,
    use_weighted_mask=True,
    dis_weight=True,
    mask_loss_type=0,
    surround=False,
    query_shape=query_shape,
    lidar_feat_crop=dict(crop_h=(0, 204), crop_w=(160 // 2 - 76 // 2, 160 // 2 + 76 // 2)),
    lidar_init=True,
    transformer=dict(
        type="DeformableTransformer",
        view_num=4,  # 这个参数务必需要与实际view数目保持一致
        num_cams=4,
        num_feature_levels=1,
        pillar_h=query_shape[2],
        num_classes=len(OCC_CLASS_NAMES),
        norm_cfg=dict(
            type="BN",
        ),
        norm_cfg_3d=dict(
            type="BN3d",
        ),
        use_3d=True,
        use_conv=False,
        rotate_prev_bev=True,
        use_shift=True,
        use_can_bus=True,
        embed_dims=_dim_,
        encoder=dict(
            type="BEVFormerEncoder",
            num_layers=2,
            pc_range=_OCC_FRONT_LIDAR_POINT_CLOUD_RANGE,
            num_points_in_pillar=query_shape[2],
            return_intermediate=False,
            transformerlayers=dict(
                type="BEVFormerLayer",
                attn_cfgs=[
                    dict(type="TemporalSelfAttention", embed_dims=_dim_, num_levels=1),
                    dict(
                        type="SpatialCrossAttention",
                        num_cams=4,
                        pc_range=_OCC_FRONT_LIDAR_POINT_CLOUD_RANGE,
                        deformable_attention=dict(
                            type="MSDeformableAttention3D",
                            embed_dims=_dim_,
                            num_points=query_shape[2],
                            num_levels=_num_levels_,
                        ),
                        embed_dims=_dim_,
                    ),
                ],
                feedforward_channels=_ffn_dim_,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    decoder2d=dict(
        hidden_dims=64,
        out_dims=128,
        num_layers=3,
        scale_factor=cam_scale_factor,
    ),
    positional_encoding=dict(
        type="LearnedPositionalEncoding",
        num_feats=_pos_dim_,
        row_num_embed=bev_h_,
        col_num_embed=bev_w_,
    ),
)
