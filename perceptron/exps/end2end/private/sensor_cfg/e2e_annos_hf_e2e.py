from perceptron.data.det3d.modules.annotation import AnnotationTrack

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]

MAP_BEZIER_CONF = dict(
    lane=dict(num_instances=20, num_pieces=5, num_degree=2, num_points=500),
    forkpoint=dict(num_instances=5, num_pieces=1, num_degree=0, num_points=1),
)

MAP_CONF = dict(
    ego_region=[200, 0, 15, -15, 6, -6],  # ego坐标系下的真实距离, 对应 [front, back, left, right, down, up], 单位 m
    ego_range=[200, 30, 12],
    mask_shape_xyz=[400, 200, 200],
    mask_shape_xy=[400, 200],
    mask_shape_xz=[400, 200],
    mask_shape_uv=[512, 896],  # 只对主相机产生rv_mask  在1/2输入尺寸上进行rv-mask的监督
    line_thickness=dict(lane={"xy": 4, "xz": 4, "uv": 8}, forkpoint={"xy": -1, "xz": -1, "uv": -1}),
    map_camera_keys=["cam_front_120"],
    map_classes=list(MAP_BEZIER_CONF.keys()),
    bezier_param=MAP_BEZIER_CONF,
)
point_cloud_range = [-15.2, -80.0, -5.0, 15.2, 204.8, 3.0]

annotation = dict(
    box=dict(
        type=AnnotationTrack,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        with_predict=True,
        # label_key="pre_labels",
        HF=False,
        fut_traj_len=65,
    ),
    # map=dict(
    #     type=AnnotationMap,
    #     map_conf=MAP_CONF,
    # ),
)
