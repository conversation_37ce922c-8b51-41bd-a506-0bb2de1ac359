from perceptron.data.det3d.modules import (
    UndistortWarp,
    LidarBase,
    ImageSimFov,
    HFRadar,
)
import numpy as np
from refile import smart_open
from perceptron.data.det3d.source.config import P177
from perceptron.utils.env import get_cluster

_CAR = P177
# camera 被注释掉了
_CAMERA_LIST = [
    "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
    "cam_front_120_sim_fov70",
    "cam_front_30",
    "cam_back_100",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
    "cam_back_left_100_sim_fov104",
    "cam_back_right_100_sim_fov104",
]
_LIDAR_LIST = ["fuser_lidar"]

_RADAR_KEY_LIST = ["0", "1", "2", "3", "4"]

_SENSOR_NAMES = dict(camera_names=_CAMERA_LIST, lidar_names=_LIDAR_LIST, radar_names=_RADAR_KEY_LIST)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)

target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))
target_z10_extrinsic["cam_back_120_sim_fov70"] = target_z10_extrinsic["cam_back_70"]
target_z10_extrinsic["cam_back_100"] = target_z10_extrinsic["cam_back_70"]
target_z10_extrinsic["cam_front_left_120_sim_fov104"] = target_z10_extrinsic["cam_front_left_100_sim_fov104"]
target_z10_extrinsic["cam_front_right_120_sim_fov104"] = target_z10_extrinsic["cam_front_right_100_sim_fov104"]
target_z10_extrinsic["cam_back_left_120_sim_fov104"] = target_z10_extrinsic["cam_back_left_100_sim_fov104"]
target_z10_extrinsic["cam_back_right_120_sim_fov104"] = target_z10_extrinsic["cam_back_right_100_sim_fov104"]
_CAMERA_UNDISTORT_FUNC = dict(
    cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    # front and back camera
    cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120_sim_fov70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_100=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
)

lidar = dict(
    type=LidarBase,
    car=_CAR,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[4],  # 1 在hf中对应前视lidar #TODO 需要检查是否其他hf数据如此
)

image = dict(
    type=ImageSimFov,
    car=_CAR,
    camera_names=_CAMERA_LIST,
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC,
    target_resolution=(1920, 1080),
)

radar = dict(
    type=HFRadar,
    car=_CAR,
    radar_mode="mlp",
    with_virtual_radar=True,
)
