""" HF9: 7V5R
Description:  Trained with prelabeled data shared with map
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/det/det_private_base_exp_hf_7v5r.py --no-clearml -b 4 -e 80 --amp --sync_bn 4
Log+Ckpt: s3://gongjiahao-share/end2end/visionEncoder/exp/det_7v5r_dn_virtual_q300/
Author: GJH
Data: 2024-03-02
"""
import json
import sys
import os
import refile
import torch
import mmcv
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.exps.end2end.private.occ.model_cfg.fs_model_cfg_1l4v_15m_deform_reorg_vx0p1_p177_repvggb1 import (
    MODEL_CFG,
)
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.end2end.private.occ.data_cfg.freespace_annos_80m_15m_1L4V_p177_5cls import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)


from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteIntervalSampler
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron_light_v4 import VisionEncoder
import copy

seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        # 当环境变量 DEBUG 为 True 时，DEBUG 模式开启
        self.DEBUG = os.environ.get("DEBUG", "False").lower() in ("true", "1")
        # self.DEBUG = True
        if self.DEBUG:
            print("DEBUG Start")
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 3e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 1
        self.lr_scale_factor = {
            "lidar_encoder": 0.1,
            "camera_encoder": 0.1,
        }  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35
        print("!" * 10, "lr  changed!!!!!!!!!!!!")
        self.bmk = kwargs.get("bmk", None)

        # 2. Dataset and model configuration

        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg["loader"]["datasets_names"] = [
            # "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_train699",
            # 'carz10_1358_occ_labels_z10_20250222_huiliu_odlabel_data_vx0p1_restore_940_rmv1bmk_odbmk',
        ]
        self.data_jixie_cfg = copy.deepcopy(self.data_train_cfg)
        self.data_jixie_cfg["loader"]["datasets_names"] = [
            # p177
            # reproduce
            "car_p177_occ_prelabel_20250513_huiliu_jixie_19685_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_3d_h5",
            "car_p177_occ_prelabel_20250520_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_15709_3d_h5",
            "car_p177_occ_prelabel_20250523_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_12569_3d_multicls_h5",
            "car_p177_occ_humanlabels_20250602_189215_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_96_3d_h5_multicls",
            "car_p177_occ_humanlabels_20250602_189217_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_66_3d_h5_multicls",
            "car_p177_occ_humanlabels_20250602_189025_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_66_3d_h5_multicls",
            "car_p177_occ_humanlabels_20250602_189027_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_54_3d_h5_multicls",
            "car_p177_occ_humanlabels_20250602_189028_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_55_3d_h5_multicls",
            "car_p177_occ_humanlabels_20250602_189471_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_17_3d_h5_multicls",
            "car_p177_occ_humanlabels_20250602_189475_huiliu_jixie_occv1.2.0_occdumpv1.3.0_pointsegv1.1.1_plat_hub_train_46_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_trunk_night_1615_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_cut_i_1110_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_vru_1187_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_island_1837_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_cut_1731_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_vru_1899_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_rainy_1157_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_island_4062_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_tunnel_61_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_constructionsign_32_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_rainy_5542_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_yixingche_39_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_trunk_daytime_7698_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_aily_yixingche_235_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_tunnel_60_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_daily_constructionsign_313_3d_h5_multicls",
            "car_p177_occ_prelabel_20250606_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_changeline_2772_3d_h5_multicls",
            "car_p177_occ_prelabel_20250609_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_night_highway_zadao_44_3d_h5_multicls",
            "car_p177_occ_prelabel_20250609_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_esidential_parking_lot_162_3d_h5_multicls",
            "car_p177_occ_prelabel_20250609_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_park_parking_lot_410_3d_h5_multicls",
            "car_p177_occ_prelabel_20250609_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_ground_parking_lot_336_3d_h5_multicls",
            "car_p177_occ_prelabel_20250609_qy_huiliu_jixie_occv1.3.0_occdumpv1.3.0_pointsegv1.1.2_underground_parking_lot_159_3d_h5_multicls",
        ]
        self.data_jixie_cfg["lidar"]["referen_lidar"] = "middle_lidar"
        self.data_jixie_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_jixie_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)

        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # bmk = "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps"
        bmk = "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk32_2fps"
        # bmk = "car_p177_occ_prelabel_20250430_huiliu_jixie_23_3d_val"
        bmk = "JIALI_BMK_177"
        bmk = "car_p177_occ_prelabel_20250430_huiliu_jixie_22_3d_val_h5"
        bmk = "car_p177_occ_prelabel_20250430_huiliu_jixie_5_3d_val_h5"
        # bmk = "car_p177_occ_prelabel_20250430_huiliu_jixie_228_3d_val"
        # bmk = "test_small"
        # bmk = "E2E-15510"
        # bmk = "humanlabeled_data_0410_val_552"
        # bmk = "humanlabeled_data_0410_val_56"

        if not bmk.endswith("_2fps"):
            self.data_val_cfg["lidar"]["referen_lidar"] = "middle_lidar"
            self.data_val_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
            self.data_val_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]

        if bmk.startswith("E2E-"):
            self.data_val_cfg["lidar"]["referen_lidar"] = "front_lidar"
            self.data_val_cfg["lidar"]["lidar_names"] = ["front_2_lidar"]
            self.data_val_cfg["sensor_names"]["lidar_names"] = ["front_2_lidar"]
            self.data_val_cfg["lidar"]["pc_fields"] = [
                "x",
                "y",
                "z",
                "i",
            ]
            self.data_val_cfg["lidar"]["lidar_ids"] = []

        if self.bmk is not None:
            with refile.smart_open(self.bmk, "r") as f:
                data = json.load(f)
            self.data_val_cfg["loader"]["datasets_names"] = data
            self.eval_name = self.bmk
        else:
            self.data_val_cfg["loader"]["datasets_names"] = [bmk]
            self.eval_name = bmk

        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)
        self.model_cfg["freespace_head"]["use_mask"] = False
        self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"] = 2
        print(self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"])
        self.model_cfg["freespace_head"]["lidar_init"] = True
        self.model_cfg["freespace_head"]["loss_weight"].update(loss_dice=2.0)
        # self.model_cfg["freespace_head"]["query_shape"] = [76, 204, 16]
        # self.model_cfg["freespace_head"]["transformer"]["pillar_h"] = 16
        # self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_points_in_pillar"] = 16
        # self.model_cfg["freespace_head"]["transformer"]["encoder"]["transformerlayers"]["attn_cfgs"][1][
        #     "deformable_attention"
        # ]["num_points"] = 16

        # OCCFLOWCONFIG
        # Train
        # self.model_cfg["freespace_head"]["future_nums"] = 2
        # self.model_cfg["freespace_head"]["feat_enhance"] = True
        # self.model_cfg["freespace_head"]["use_can_bus"] = False
        # self.data_train_cfg["num_future_frames_per_sample"] = self.model_cfg["freespace_head"]['future_nums']
        # self.data_jixie_cfg["num_future_frames_per_sample"] = self.model_cfg["freespace_head"]['future_nums']
        # self.model_cfg["flow"] = True if self.model_cfg["freespace_head"]["future_nums"] > 0 else False,

        # Val
        # self.data_val_cfg["evaluator"]["type"] = EvaluationOccFlow
        # self.data_val_cfg["num_future_frames_per_sample"] = self.model_cfg["freespace_head"]['future_nums']

        # Multi Modal
        # self.model_cfg['modal'] = ['Lidar', 'Camera']
        # self.model_cfg['freespace_head']['modal'] = self.model_cfg['modal']

        # Mask
        self.model_cfg["freespace_head"]["use_mask"] = False
        self.model_cfg["freespace_head"]["use_weighted_mask"] = True
        self.model_cfg["freespace_head"]["dis_weight"] = True
        self.data_val_cfg["evaluator"]["use_image_mask"] = self.model_cfg["freespace_head"]["use_mask"]
        self.model_cfg["freespace_head"]["mask_loss_type"] = 0

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        # 扩两倍 再itv10
        # 当 DEBUG 模式开启时，设置 num_workers 为 0
        num_workers = 0 if self.DEBUG else 16
        os.environ["FILTER"] = "True"
        # train_dataset1 = PrivateE2EFlowDataset(**self.data_train_cfg)
        train_dataset_jixie = PrivateE2EDataset(**self.data_jixie_cfg)
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1,train_dataset_jixie,])
        train_dataset = train_dataset_jixie
        train_dataset.batch_postcollate_fn = train_dataset.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset.batch_preforward_fn
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteIntervalSampler(len(train_dataset), seed=self.seed if self.seed else 0, interval=5)
            if dist.is_distributed()
            else None,
            pin_memory=False,
            num_workers=num_workers,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        # 当 DEBUG 模式开启时，设置 num_workers 为 0
        num_workers = 0 if self.DEBUG else 5

        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=num_workers,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)
        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts = self.model(**batch)
        pred_dicts["pred_seg"] = pred_dicts["pred_seg"].argmax(-1).to(torch.uint8)
        remap_pred_dicts = []
        for pred_seg in pred_dicts["pred_seg"]:
            remap_pred_dict = {"pred_seg": pred_seg}
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
