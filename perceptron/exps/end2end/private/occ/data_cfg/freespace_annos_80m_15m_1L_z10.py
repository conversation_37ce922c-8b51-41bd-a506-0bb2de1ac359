from perceptron.data.det3d.modules.annotation import AnnotationFreespace
import copy

from perceptron.data.det3d.source.config import Z10
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_z10_8v5r1l_new import (
    lidar as lidar_cfg,
    image as image_cfg,
    # _CAMERA_LIST,
    # _SENSOR_NAMES,
)
from perceptron.utils.env import get_cluster
from perceptron.data.det3d.modules import (
    ObjectRangeFilter,
    PointShuffle,
)
from perceptron.data.det3d.modules.evaluation_occ import EvaluationOcc

_CAMERA_LIST = []
_SENSOR_NAMES = {
    "camera_names": [],
    "lidar_names": ["fuser_lidar"],
    "radar_names": [],
}
image_cfg["camera_names"] = []


Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)

# target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))
# _CAMERA_UNDISTORT_FUNC_Z10 = dict(
#     cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
#     cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
#     # front and back camera
#     cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
#     cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
# )

# image_cfg["undistort_func"] = _CAMERA_UNDISTORT_FUNC_Z10


lidar_cfg["lidar_names"] = ["fuser_lidar"]
lidar_cfg["pc_fields"] = ["x", "y", "z", "i", "lidar_id"]
lidar_cfg["lidar_ids"] = [4]  # 4 在z10 中对应前视m1p lidar #TODO 需要检查是否其他hf数据如此

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
    "大货车": "truck",
    "小货车": "truck",
    "骑三轮车的人": "tricycle",
    "骑自行车的人": "cyclist",
    "骑摩托车的人": "cyclist",
    "儿童": "pedestrian",
    "成年人": "pedestrian",
    "蒙版": "masked_area",
    "mask": "masked_area",
    "正向蒙版": "masked_area",
    "负向蒙版": "masked_area",
    "拖挂": "truck",
    "tuogua": "truck",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]
point_cloud_range = [-15.2, -27.2, -5.0, 15.2, 81.6, 3.0]

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    # undistort=dict(
    #     type=CameraUndistortCPU,
    # ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    # modal_mask = dict(
    #     type = ModalMask3D,
    #      mask_modal="none",
    #      drop_ratio=[1.0, 0.6]
    # ),
    # FrontLidarBoxPostFilter = dict(
    #     type = FrontLidarBoxPostFilter, # 用来删drop camera 后 的框
    #     fov_convexthull=[[0, 0], [-32.0, -0.74 * (-32)], [-32.0, 204.8], [32.0, 204.8], [32.0, 0.59 * 32]],
    # ),
    # bda_aug: need to be adapted
    # ida_aug=dict(
    #     type=MultiFrameImageAffineTransformation,
    #     aug_conf=dict(
    #         final_dim=(512, 960),
    #         # resize_lim=((0.472, 0.5), (0.472, 0.5)),
    #         resize_lim=(0.5, 0.5),
    #         bot_pct_lim=(0.0, 0.0),
    #         H=1080,
    #         W=1920,
    #         rand_flip=False,
    #         rot_lim=(-0.0, 0.0),
    #     ),
    #     camera_names=_CAMERA_LIST,
    #     img_norm=True,
    #     img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    # ),
)

annotation_cfg = dict(
    occ=dict(type=AnnotationFreespace, label_mapping={0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 255, 6: 255, 255: 255}),
)

_CAR = Z10
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=False,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["debug_sample_track"],
        only_key_frame=True,
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=None,
    radar=None,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["occ_prelabels_v20241205"])

val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationOcc,
        output_mode="freespace",
        label_mapping=annotation_cfg["occ"]["label_mapping"],
        num_classes=5,
        class_names=["free", "freespace", "dynamic", "static", "water"],
        annotaion_cls=AnnotationFreespace,
    )
)
