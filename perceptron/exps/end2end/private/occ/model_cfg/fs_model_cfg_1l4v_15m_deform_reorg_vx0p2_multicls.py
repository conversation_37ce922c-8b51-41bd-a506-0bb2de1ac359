from perceptron.exps.end2end.private.occ.data_cfg.freespace_annos_80m_15m_1L4V_z10_multicls import class_names
import numpy as np

CLASS_NAMES = class_names
_POINT_CLOUD_RANGE = [-15.2, -27.2, -5.0, 15.2, 81.6, 3.0]
_FRONT_LIDAR_POINT_CLOUD_RANGE = [-15.2, 0.0, -5.0, 15.2, 81.6, 3.0]
_VOXEL02_VOXEL_SIZE = [0.2, 0.2, 8]  # pointpillar
_VOXEL02_GRID_SIZE = [
    (_POINT_CLOUD_RANGE[3] - _POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_POINT_CLOUD_RANGE[4] - _POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_POINT_CLOUD_RANGE[5] - _POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]
_FRONT_LIDAR_VOXEL02_GRID_SIZE = [
    (_FRONT_LIDAR_POINT_CLOUD_RANGE[3] - _FRONT_LIDAR_POINT_CLOUD_RANGE[0]) / _VOXEL02_VOXEL_SIZE[0],
    (_FRONT_LIDAR_POINT_CLOUD_RANGE[4] - _FRONT_LIDAR_POINT_CLOUD_RANGE[1]) / _VOXEL02_VOXEL_SIZE[1],
    (_FRONT_LIDAR_POINT_CLOUD_RANGE[5] - _FRONT_LIDAR_POINT_CLOUD_RANGE[2]) / _VOXEL02_VOXEL_SIZE[2],
]


_VOXEL02_GRID_SIZE = np.round(np.array(_VOXEL02_GRID_SIZE)).astype(np.int64)
_FRONT_LIDAR_VOXEL02_GRID_SIZE = np.round(np.array(_FRONT_LIDAR_VOXEL02_GRID_SIZE)).astype(np.int64)
_VOXEL02_OUT_SIZE_FACTOR = 2
_POST_CENTER_RANGE = [
    _POINT_CLOUD_RANGE[0] - 10,
    _POINT_CLOUD_RANGE[1] - 10,
    -10,
    _POINT_CLOUD_RANGE[3] + 10,
    _POINT_CLOUD_RANGE[4] + 10,
    10,
]

_dim_ = 256
_ffn_dim_ = _dim_ * 2
_pos_dim_ = _dim_ // 2
_num_levels_ = 1


query_shape = [152, 408, 16]
bev_h_ = query_shape[1]
bev_w_ = query_shape[0]
Freespace_head = dict(
    type="",
    in_channel=64,
    fusion_dim=384 + 128,
    pc_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
    num_classes=len(CLASS_NAMES),
    loss_weight=dict(
        loss_cls=10.0,
    ),
    aspp_cfg=None,  # 在unet前面增加aspp
    unet_cfg=None,
    modal=["Lidar", "Camera"],
    upsample_cfg=dict(size=(408 * 2, 152 * 2), mode="bilinear", align_corners=True),
    img_encoder_type="Deformable",
    as_two_stage=False,
    use_mask=False,
    query_shape=query_shape,
    transformer=dict(
        type="DeformableTransformer",
        view_num=4,  # 这个参数务必需要与实际view数目保持一致
        pillar_h=query_shape[2],
        num_classes=len(CLASS_NAMES),
        norm_cfg=dict(
            type="BN",
        ),
        norm_cfg_3d=dict(
            type="BN3d",
        ),
        use_3d=True,
        use_conv=False,
        rotate_prev_bev=True,
        use_shift=True,
        use_can_bus=True,
        embed_dims=_dim_,
        encoder=dict(
            type="BEVFormerEncoder",
            num_layers=4,
            pc_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
            num_points_in_pillar=query_shape[2],
            return_intermediate=False,
            transformerlayers=dict(
                type="BEVFormerLayer",
                attn_cfgs=[
                    dict(type="TemporalSelfAttention", embed_dims=_dim_, num_levels=1),
                    dict(
                        type="SpatialCrossAttention",
                        num_cams=4,
                        pc_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
                        deformable_attention=dict(
                            type="MSDeformableAttention3D",
                            embed_dims=_dim_,
                            num_points=query_shape[2],
                            num_levels=_num_levels_,
                        ),
                        embed_dims=_dim_,
                    ),
                ],
                feedforward_channels=_ffn_dim_,
                ffn_dropout=0.1,
                operation_order=("self_attn", "norm", "cross_attn", "norm", "ffn", "norm"),
            ),
        ),
    ),
    decoder2d=dict(
        hidden_dims=64,
        out_dims=128,
        num_layers=3,
        scale_factor=2,
    ),
    positional_encoding=dict(
        type="LearnedPositionalEncoding",
        num_feats=_pos_dim_,
        row_num_embed=bev_h_,
        col_num_embed=bev_w_,
    ),
)


VOXEL02_TRAIN_CFG = dict(
    pts=dict(
        dataset="Private",
        assigner=dict(
            type="HungarianAssigner3D",
            cls_cost=dict(type="FocalLossCost", weight=2.0),
            reg_cost=dict(type="BBox3DL1Cost", weight=0.25),
            iou_cost=dict(type="IoUCost", weight=0.0),  # Fake cost. This is just to make it compatible with DETR head.
            pc_range=_POINT_CLOUD_RANGE,
        ),
        pos_weight=-1,
        gaussian_overlap=0.1,
        min_radius=2,
        grid_size=_VOXEL02_GRID_SIZE,  # [x_len, y_len, 1]
        voxel_size=_VOXEL02_VOXEL_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2],
        point_cloud_range=_POINT_CLOUD_RANGE,
    )
)

VOXEL01_TEST_CFG = dict(
    pts=dict(
        dataset="Private",
        grid_size=_VOXEL02_GRID_SIZE,
        out_size_factor=_VOXEL02_OUT_SIZE_FACTOR,
        pc_range=_POINT_CLOUD_RANGE[0:2],
        voxel_size=_VOXEL02_VOXEL_SIZE[:2],
        nms_type=None,
    )
)

CAMERA_ENCODER_CFG = dict(
    img_backbone=dict(
        # type='ResNet',
        depth=50,
        num_stages=4,
        # out_indices=(2, 3),
        out_indices=(0, 1, 2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        with_cp=True,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    img_neck=dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[32, 32, 64, 128],
    ),
)
LIDAR_ENCODER_CFG = dict(
    point_cloud_range=_FRONT_LIDAR_POINT_CLOUD_RANGE,
    voxel_size=_VOXEL02_VOXEL_SIZE,
    grid_size=_FRONT_LIDAR_VOXEL02_GRID_SIZE,
    max_num_points=16,
    # max_voxels=(260000, 280000),
    max_voxels=(100000, 120000),
    # max_voxels=(120000, 120000),
    src_num_point_features=4,
    use_num_point_features=4,
    map_to_bev_num_features=64,
    vfe=dict(
        vfe_num_filters=[64], num_point_features=4, voxel_size=_VOXEL02_VOXEL_SIZE, point_cloud_range=_POINT_CLOUD_RANGE
    ),
    backbone_2d=dict(
        layer_nums=[3, 5, 5],
        layer_strides=[2, 2, 2],
        num_filters=[64, 128, 256],
        upsample_strides=[1, 2, 4],
        num_upsample_filters=[128, 128, 128],
        input_channels=64,  # sp conv output channel
        with_cp=True,
        use_scconv=True,
        upsample_output=False,
    ),
)

MODEL_CFG = dict(
    num_query=900,
    num_classes=len(CLASS_NAMES),
    tracking=False,
    grid_mask=True,
    train_backbone=True,
    class_names=CLASS_NAMES,
    camera_encoder=CAMERA_ENCODER_CFG,
    radar_encoder=None,
    lidar_encoder=LIDAR_ENCODER_CFG,
    det_head=None,
    freespace_head=Freespace_head,
    train_cfg=VOXEL02_TRAIN_CFG,
    test_cfg=VOXEL01_TEST_CFG,
)
