""" HF9: 7V5R
Description:  Trained with prelabeled data shared with map
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/det/det_private_base_exp_hf_7v5r.py --no-clearml -b 4 -e 80 --amp --sync_bn 4
Log+Ckpt: s3://gongjiahao-share/end2end/visionEncoder/exp/det_7v5r_dn_virtual_q300/
Author: GJH
Data: 2024-03-02
s3://wyh-share/ckpts/occ/pretrain/z10_occ_lidar_imgdeform_huiliu_newtrain_vx0.1_itv5_24e.pth
python3 perceptron/exps/end2end/private/occ/freespace_only_1L4V_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1.py -b 8 --no-clearml --amp --sync_bn 1 -e 24 --pretrained_model /data/code/e2e/perceptron/pretrain/z10_occ_lidar_imgdeform_huiliu_newtrain_vx0.1_itv5_24e.pth
python3 perceptron/exps/end2end/private/occ/freespace_only_1L4V_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1_fixwarp.py -b 8 --no-clearml --amp --sync_bn 1 -e 24 --pretrained_model /data/code/e2e/perceptron/pretrain/z10_occ_lidar_imgdeform_huiliu_newtrain_vx0.1_itv5_24e.pth

python perceptron/exps/end2end/private/occ/freespace_only_1L4V_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1.py --eval --ckpt /data/outputs/occ__freespace_only_1l4v_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1/2025-03-16T12:07:35/dump_model/checkpoint_epoch_23.pth -b 4 --no-clearml --amp --eval_occ
python perceptron/exps/end2end/private/occ/freespace_only_1L4V_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1.py --eval --ckpt /data/outputs/occ__freespace_only_1l4v_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1/2025-03-16T12:07:35/dump_model/checkpoint_epoch_23.pth -b 4 --no-clearml --amp --eval_occ

python perceptron/exps/end2end/private/occ/freespace_only_1L4V_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1_fixwarp.py --eval --ckpt /data/outputs/occ__freespace_only_1l4v_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1_fixwarp/2025-03-17T17:15:51/dump_model/checkpoint_epoch_23.pth -b 4 --no-clearml --amp --eval_occ

"""
import sys
import refile
import torch
import mmcv
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.exps.end2end.private.occ.model_cfg.fs_model_cfg_1l4v_15m_deform_reorg_vx0p1 import MODEL_CFG
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.end2end.private.occ.data_cfg.freespace_annos_hf_80m_15m_1L4V_z10_fixwarp import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)


from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteIntervalSampler
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder

seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 3e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 3
        self.dump_interval = 1
        self.lr_scale_factor = {
            "lidar_encoder": 0.1,
            "camera_encoder": 0.1,
        }  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35
        print("!" * 10, "lr  changed!!!!!!!!!!!!")

        # 2. Dataset and model configuration

        # -------------------------------------高速good radar labels-------------------------------------
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_train_cfg["loader"]["datasets_names"] = [
            # 23年回流的23年数据
            "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore",
        ]

        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)

        # add warp
        self.data_train_cfg["image"]["target_extrinsic"] = "/data/Envs/megvii/target_extrinsic_z08.npz"
        self.data_val_cfg["image"]["target_extrinsic"] = "/data/Envs/megvii/target_extrinsic_z08.npz"

        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()

        # self.eval_name = 'gaosu_bmk'
        # self.data_val_cfg['evaluator']['eval_cfg_l3'] = 'e2e_l3_far_32m'
        # self.data_val_cfg['evaluator']['eval_cfg_l2'] = ''
        # self.data_val_cfg['evaluator']['extra_eval_cfgs'] = []
        # self.data_val_cfg["loader"]["datasets_names"] = ["bmk_new_withOCC"]  # ["bmk_new_withOCC"]

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # self.model_cfg.det_head.bbox_coder.score_threshold = 0.1
        # self.model_cfg.num_query = 300
        # self.model_cfg["det_head"]["init_radar_num_query"] = 0
        # self.model_cfg["det_head"]["num_query"] = 0 + 300

        # self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        # self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        # self.model_cfg["dn_cfg"] = dict(
        #     use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        # )
        # self.model_cfg['det_head']['use_roi_mask'] = True  # gs cq 联合训练必须有roi mask

        self.data_val_cfg["loader"]["datasets_names"] = [
            # "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk32_2fps"
            "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps"
            # 'carz10_1358_occ_prelabels_v20250101_train_1635'
        ]  # ["bmk_new_withOCC"]
        # self.eval_name = "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk32_2fps"
        self.eval_name = "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps"
        self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)
        self.model_cfg["freespace_head"]["use_mask"] = False
        print(self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"])
        self.model_cfg["freespace_head"]["transformer"]["encoder"]["num_layers"] = 4
        self.model_cfg["freespace_head"]["lidar_init"] = True
        self.model_cfg["freespace_head"]["loss_weight"].update(loss_dice=2.0)

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        # for name, param in model.named_parameters():
        #     if name and  'freespace' in name:
        #         print(f"Parameter name: {name}, Shape: {param.shape}, {param.stride()}")
        return model

    def _configure_train_dataloader(self):
        train_dataset = PrivateE2EDataset(**self.data_train_cfg)

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteIntervalSampler(len(train_dataset), seed=self.seed if self.seed else 0, interval=10)
            if dist.is_distributed()
            else None,
            pin_memory=False,
            num_workers=8,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False, seed=42)
            if dist.is_distributed()
            else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        # for b in range(len(batch['imgs'])):
        #     if not (batch['imgs'][b][0][0][0]==batch['imgs'][b][0][0][0,0,0]).all(): # 如果图像没有被mask，应该用的roi mask
        #         batch['fov_boardline'][b] *= 0
        ret_dict, loss_dict, _ = self.model(**batch)
        # print(ret_dict)
        # print(loss_dict)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts = self.model(**batch)
        pred_dicts["pred_seg"] = pred_dicts["pred_seg"].argmax(-1).to(torch.uint8)
        remap_pred_dicts = []
        for pred_seg in pred_dicts["pred_seg"]:
            remap_pred_dict = {"pred_seg": pred_seg}
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
