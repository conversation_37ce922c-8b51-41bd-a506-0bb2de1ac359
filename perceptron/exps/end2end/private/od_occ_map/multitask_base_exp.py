import sys

import refile
import torch
import mmcv
import bisect
import numpy as np
from abc import abstractmethod

from perceptron.exps.end2end.private.od_occ.model_cfg.det_model_cfg_4v1l_sparse_y120x32m_occ_multiclass import MODEL_CFG
from perceptron.exps.base_exp import BaseExp


seed = 42  # 你可以使用任何整数作为种子

torch.manual_seed(seed)  # 设置 PyTorch 的种子
torch.cuda.manual_seed(seed)  # 如果你使用 GPU，设置 CUDA 的种子
torch.cuda.manual_seed_all(seed)  # 如果你使用多个 GPU，设置所有 GPU 的种子

SOFT_OCC_THRESHOLD = 0.4


class MultiTaskBaseExp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(MultiTaskBaseExp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 2e-4  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 30
        self.dump_interval = 1
        self.grad_clip_value = 35

        # 3. model configuration and other configuration change in this function
        self.model_cfg = mmcv.Config(MODEL_CFG)
        self._change_model_cfg_params()

        # 2. Dataset model configuration
        # -------------------------------------Z10 label------------------------------------
        self._get_train_dataset_cfg()
        self._get_val_dataset_cfg()

        # 4. other configuration
        self.task_flag = None

    @property
    def val_dataloader(self):
        if self.task_flag in ["box", "e2e"] and "_val_od_dataloader" not in self.__dict__:
            self._val_od_dataloader = self._configure_val_od_dataloader()
        elif self.task_flag == "occ" and "_val_occ_dataloader" not in self.__dict__:
            self._val_occ_dataloader = self._configure_val_occ_dataloader()
        elif self.task_flag == "map" and "_val_map_dataloader" not in self.__dict__:
            self._val_map_dataloader = self._configure_val_map_dataloader()
        elif self.task_flag == "god" and "_val_god_dataloader" not in self.__dict__:
            self._val_god_dataloader = self._configure_val_god_dataloader()
        elif self.task_flag is None:
            # 默认初始化od的dataloader
            self._val_od_dataloader = self._configure_val_od_dataloader()

        if self.task_flag in ["box", "e2e"] or self.task_flag is None:
            return self._val_od_dataloader
        elif self.task_flag == "occ":
            return self._val_occ_dataloader
        elif self.task_flag == "map":
            return self._val_map_dataloader
        elif self.task_flag == "god":
            return self._val_god_dataloader
        else:
            raise NotImplementedError(f"no supported task_flag: {self.task_flag}")

    def _configure_val_od_dataloader(self):
        pass

    def _configure_val_occ_dataloader(self):
        pass

    def _configure_val_map_dataloader(self):
        pass

    @torch.no_grad()
    def test_step(self, batch):
        outputs_dict = self.model(**batch)
        outs_obstacle = outputs_dict["obstacle"]
        outs_freespace = outputs_dict["freespace"]
        outs_map = outputs_dict["map"]
        outs_god = outputs_dict["god"]

        if self.task_flag == "box":
            return self.test_od_step(outs_obstacle)
        elif self.task_flag == "occ":
            return self.test_occ_step(outs_freespace)
        elif self.task_flag == "map":
            return self.test_map_step(outs_map, batch)
        elif self.task_flag == "e2e":
            return self.test_e2e_step(outs_obstacle)
        elif self.task_flag == "god":
            return self.test_od_step(outs_god)
        else:
            raise NotImplementedError

    @torch.no_grad()
    def test_od_step(self, pred_dicts):
        remap_pred_dicts = []
        for pred_dict in pred_dicts:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_e2e_step(self, pred_dicts):
        return pred_dicts

    @torch.no_grad()
    def test_occ_step(self, pred_dicts):
        pred_dicts["pred_seg"] = pred_dicts["pred_seg"].argmax(-1).to(torch.uint8)
        remap_pred_dicts = []
        for pred_seg in pred_dicts["pred_seg"]:
            remap_pred_dict = {"pred_seg": pred_seg}
            remap_pred_dicts.append(remap_pred_dict)
        return dict(pred_dicts=remap_pred_dicts)

    @torch.no_grad()
    def test_map_step(self, pred_maps, batch):
        for bs_i in range(len(pred_maps)):
            index = batch["index_in_dataset"][bs_i][0]
            # fake items
            pred_maps[bs_i]["stop_scores"] = np.zeros((50,))
            pred_maps[bs_i]["stop_labels"] = (np.ones((50,)) * 2).astype(np.int64)
            pred_maps[bs_i]["box_scores"] = np.zeros((100,))
            pred_maps[bs_i]["box_labels"] = (np.ones((100,)) * 5).astype(np.int64)
            pred_maps[bs_i]["entrance_scores"] = np.zeros((50,))
            pred_maps[bs_i]["entrance_labels"] = (np.ones((50,)) * 6).astype(np.int64)
            pred_maps[bs_i]["stops"] = np.zeros((50, 4, 3))
            pred_maps[bs_i]["boxes"] = np.zeros((100, 4, 3))
            pred_maps[bs_i]["entrances"] = np.zeros((50, 4, 3)).astype(np.int32)
            pred_maps[bs_i]["attrs_boxes"] = np.zeros((100, 4, 6)).astype(np.int32)

            if (
                "s3_path"
                in self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"]
            ):
                cam120_ts = (
                    self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_120"][
                        "s3_path"
                    ]
                    .split("/")[-1]
                    .split(".jpg")[0]
                )
                cam30_ts = (
                    self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"]["cam_front_30"][
                        "s3_path"
                    ]
                    .split("/")[-1]
                    .split(".jpg")[0]
                )
                camleft100_ts = (
                    self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"][
                        "cam_front_left_100"
                    ]["s3_path"]
                    .split("/")[-1]
                    .split(".jpg")[0]
                )
                camright100_ts = (
                    self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"][
                        "cam_front_right_100"
                    ]["s3_path"]
                    .split("/")[-1]
                    .split(".jpg")[0]
                )
            else:
                cam120_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"][
                    "cam_front_120"
                ]["timestamp"]
                cam30_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"][
                    "cam_front_30"
                ]["timestamp"]
                camleft100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"][
                    "cam_front_left_100"
                ]["timestamp"]
                camright100_ts = self.val_dataloader.dataset.loader_output["frame_data_list"][index]["sensor_data"][
                    "cam_front_right_100"
                ]["timestamp"]
            json_idx = bisect.bisect_right(
                self.val_dataloader.dataset.loader_output["frame_data_list"].cumulative_sizes, index
            )
            json_path = self.val_dataloader.dataset.loader_output["json_collection"][json_idx]

            line_scores = pred_maps[bs_i]["line_scores"]
            lines = pred_maps[bs_i]["lines"]
            line_roi_index = np.where(line_scores > 0.3)
            line_roi_scores = line_scores[line_roi_index]  # (N, )
            lines_roi = lines[line_roi_index]  # (N, 20, 3)

            json_res = dict(
                cam120_ts=cam120_ts,
                cam30_ts=cam30_ts,
                camleft100_ts=camleft100_ts,
                camright100_ts=camright100_ts,
                line_roi_index=line_roi_index[0].tolist(),
                line_roi_scores=line_roi_scores.tolist(),
                line_roi=lines_roi.tolist(),
                json_path=json_path,
            )
            pred_maps[bs_i]["json_res"] = json_res
        return dict(pred_maps=pred_maps)  # remap

    @property
    @abstractmethod
    def supported_tasks(self):
        raise NotImplementedError("Please implement the supported_tasks property in the subclass.")
