from perceptron.data.det3d.modules.annotation import AnnotationDet, AnnotationFreespace, AnnotationMap
import copy

from perceptron.data.det3d.source.config import Z10
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_z10_8v5r1l_nowarp import (
    _CAMERA_UNDISTORT_FUNC_Z10_MAP_NOWARP,
    image as image_cfg,
    _CAMERA_LIST,
    _CAMERA_MAP_LIST,
    _SENSOR_NAMES,
    LidarBase,
    _LIDAR_LIST,
    _CAR,
)

from perceptron.data.det3d.modules import (
    EvaluationV3,
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)
from perceptron.utils.map_utils.evaltools.eval_map_subdivision_range_maptrv2 import MapEvaluator
from perceptron.data.det3d.modules.evaluation_occ import EvaluationOcc

lidar_cfg = dict(
    type=LidarBase,
    car=_CAR,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[4],  # 4 在hf/Z10中对应前视速腾M1P lidar
)


category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "suv": "car",
    "SUV": "car",
    "van": "car",
    "VAN": "car",
    "Van": "car",
    "皮卡": "car",
    "pika": "car",
    "cart": "car",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
    "遮挡": "occlusion",
    "短障碍物": "short_track",
    "大货车": "truck",
    "dahuoche": "truck",
    "dauhoche": "truck",
    "小货车": "truck",
    "xiaohuoche": "truck",
    "骑三轮车的人": "tricycle",
    "骑自行车的人": "cyclist",
    "骑摩托车的人": "cyclist",
    "et": "pedestrian",
    "儿童": "pedestrian",
    "成年人": "pedestrian",
    "蒙版": "masked_area",
    "mask": "masked_area",
    "正向蒙版": "masked_area",
    "负向蒙版": "masked_area",
    "拖挂": "truck",
    "tuogua": "truck",
    "其他非机动车": "other",
    "其他机动车": "other",
    "小动物类": "other",
    "大动物类": "other",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]

occ_class_names = [
    "free",
    "freespace",
    "dynamic",
    "static",
    "noise",  # default water
    "car",
    "larger_vehicle",
    "bicycle",
    "pedestrian",
]

point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]
log_bad_data = False
use_oss_data = False
_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,  # occ是否还需要过滤，感觉不需要
    ),
    point_shuffle=dict(
        type=PointShuffle,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)

map_class_dict = {
    "laneline": {  # 注意数据json中 laneline为lane
        "id": 0,
        "pt_num": 20,
        "w_guass": False,
        "point_vector_width": False,
        "dt_scores_th": 0.3,  # 0.1, # 0.4,
    },
    "curb": {  # 注意数据json中 为curb存在在lane中
        "id": 1,
        "pt_num": 20,
        "w_guass": False,
        "point_vector_width": False,
        "dt_scores_th": 0.3,  # 0.4,
    },
    "stopline": {
        "id": 2,
        "pt_num": 4,
        "w_guass": False,
        "point_vector_width": True,
        "dt_scores_th": 0.4,
        "filter_range": [-10.0, 0.0, -6.0, 10.0, 60, 6],
    },
    "crosswalk": {
        "id": 3,
        "pt_num": 4,
        "w_guass": True,
        "point_vector_width": False,
        "dt_scores_th": 0.4,
        "filter_range": [-10.0, 0.0, -6.0, 10.0, 60, 6],
    },
    "arrow": {
        "id": 4,
        "pt_num": 4,
        "w_guass": True,
        "point_vector_width": False,
        "dt_scores_th": 0.4,
        "filter_range": [-6.0, 5.0, -6.0, 6.0, 60, 6],
    },
    "noparking": {
        "id": 5,
        "pt_num": 4,
        "w_guass": True,
        "point_vector_width": False,
        "dt_scores_th": 0.4,
        "filter_range": [-12.0, 0.0, -6.0, 12.0, 40.0, 6.0],
    },
    "entrance": {
        "id": 6,
        "pt_num": 4,
        "w_guass": False,
        "point_vector_width": True,
        "dt_scores_th": 0.3,
        "filter_range": [-25.0, 5.0, -6.0, 25.0, 60, 6],
    },
    "mask": {
        "id": 7,
        "pt_num": 4,
        "w_guass": True,
        "point_vector_width": False,
        "dt_scores_th": 0.4,
    },
}  # 'centerline'

map_cam_dict = {
    "cam_front_120": {},
    "cam_front_30": {},
    "cam_front_left_100": {},  # z10
    "cam_front_right_100": {},  # z10
}

arrow_name_id_map = {  # TODO：id-name map
    "unknown": 0,
    "guide_turn": 1,
    "guide_turn+guide_through": 2,
    "guide_turn+guide_left": 3,
    "guide_turn+guide_right": 4,
    "guide_left": 5,
    "guide_left+guide_through": 6,
    "guide_left+guide_right": 7,
    "guide_left+guide_through+guide_right": 8,
    "guide_through": 9,
    "guide_through+guide_right": 10,
    "guide_right": 11,
    "forbid_turn": 12,
    "forbid_left": 13,
    "forbid_left+forbid_right": 14,
    "forbid_through": 15,
    "forbid_through+forbid_right": 16,
    "forbid_through+forbid_left": 17,
    "forbid_right": 18,
    "merge_left": 19,
    "merge_right": 20,
    "forbid_left+forbid_turn": 21,
}
arrow_id_name_map = {}
for k, v in arrow_name_id_map.items():
    arrow_id_name_map[v] = k

map_ego_range = [0, -30, -6, 100, 30, 6]  # ego [xmin, ymin, zmin, xmax, ymax, zmax]
map_lidar_range = [
    -30.0,
    0.0,
    -6.0,
    30.0,
    100,
    6,
]
bev_h = 100
bev_w = 60
annotation_cfg = dict(
    # fmt: off
    occ=dict(
        type=AnnotationFreespace,
        label_mapping={0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 4, 6: 4,
                       7: 5, 8: 6, 9: 6, 10: 6, 11: 7, 12: 7, 13: 7, 14: 7, 15: 8,
                       16: 255,  # 人标错误类别
                       17: 3, 18: 3, 19: 3, 20: 3, 21: 3, 22: 3, 23: 3, 24: 3, 25: 3, 26: 3, 27: 3, 28: 3,
                       255: 255,},
        use_oss_data=use_oss_data,
        log_bad_data=log_bad_data
        ),
    # fmt: on
    box=dict(
        type=AnnotationDet,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        HF=False,
    ),
    map=dict(
        type=AnnotationMap,
        map_class_dict=map_class_dict,
        map_cam_dict=map_cam_dict,
        arrow_name_id_map=arrow_name_id_map,
        map_point_cloud_range=map_ego_range,
        map_lidar_range=map_lidar_range,
        seg_canvas_size=[64, 120],
        seg_thickness=2,
        scene_weight=False,
        bev_h=bev_h,
        bev_w=bev_w,
        attr_num=3,
        filter_start=60,
        filter_length=20,
        except_dist=30,
        bda_aug_conf={"rot_lim": (-0.0, 0.0), "scale_lim": (1.0, 1.0), "flip_dx_ratio": 0.0, "flip_dy_ratio": 0.0},
        remove_reverse=False,
    ),
)

_CAR = Z10
if "radar_names" in _SENSOR_NAMES:
    del _SENSOR_NAMES["radar_names"]
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["debug_sample_track"],
        only_key_frame=True,
        rebuild=False,
        log_bad_data=log_bad_data,
    ),
    lidar=lidar_cfg,
    image=image_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
    roi_mask=[-32, -80, 32, 120],
)

# config for map dataset
undistort_func = _CAMERA_UNDISTORT_FUNC_Z10_MAP_NOWARP
base_dataset_map_cfg = copy.deepcopy(base_dataset_cfg)
base_dataset_map_cfg["loader"]["camera_names"] = _CAMERA_MAP_LIST
base_dataset_map_cfg["image"]["camera_names"] = _CAMERA_MAP_LIST
base_dataset_map_cfg["pipeline"]["ida_aug"]["camera_names"] = _CAMERA_MAP_LIST
base_dataset_map_cfg["image"]["undistort_func"] = dict(list(undistort_func.items()))

# config for eval dataset

val_dataset_od_bmk_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_od_bmk_cfg.update(mode="val")
val_dataset_od_bmk_cfg["annotation"]["box"]["label_key"] = "labels"
val_dataset_od_bmk_cfg["annotation"]["box"]["occlusion_threshold"] = 1
val_dataset_od_bmk_cfg["image"][
    "target_extrinsic"
] = "s3://mtx-qy/e2e/perceptron/pretrain/gongjiahao-share/end2end/calib/target_extrinsic_z08.npz"
val_dataset_od_bmk_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="cam_l3_cq_sixclass",
        eval_cfg_l2="e2e_l3_far_32m_sixclass",
    )
)

# od old bmk, 各类别评测数量较少
val_dataset_od_bmk01_cfg = copy.deepcopy(val_dataset_od_bmk_cfg)
val_dataset_od_bmk01_cfg["loader"]["datasets_names"] = ["z1_label_1230_bmk_qy"]
val_dataset_od_bmk01_cfg["annotation"]["box"].update(occlusion_threshold=1)  # 这里需要确认下!
val_dataset_od_bmk01_cfg["annotation"].pop("occ", None)  # remove box annotation
val_dataset_od_bmk01_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="cam_l3_cq_sixclass",
        eval_cfg_l2="e2e_l3_far_32m_sixclass",
    )
)

val_dataset_od_bmk02_cfg = copy.deepcopy(val_dataset_od_bmk_cfg)
val_dataset_od_bmk02_cfg["loader"]["datasets_names"] = ["OD_BMK_02_v2"]
val_dataset_od_bmk02_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]  # , "fuser_lidar"
val_dataset_od_bmk02_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
val_dataset_od_bmk02_cfg["annotation"].pop("occ", None)  # remove

# occ val
val_dataset_occ_bmk_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_occ_bmk_cfg.update(mode="val")
val_dataset_occ_bmk_cfg["image"][
    "target_extrinsic"
] = "s3://mtx-qy/e2e/perceptron/pretrain/gongjiahao-share/end2end/calib/target_extrinsic_z08.npz"
val_dataset_occ_bmk_cfg.update(
    evaluator=dict(
        type=EvaluationOcc,
        output_mode="freespace",
        label_mapping=annotation_cfg["occ"]["label_mapping"],
        num_classes=len(occ_class_names),
        class_names=occ_class_names,
        annotaion_cls=AnnotationFreespace,
        surround=False,
    )
)


val_dataset_occ_bmk01_cfg = copy.deepcopy(val_dataset_occ_bmk_cfg)
val_dataset_occ_bmk01_cfg["annotation"].pop("box", None)  # remove box annotation
val_dataset_occ_bmk01_cfg["loader"]["datasets_names"] = ["occ_multiclass_0613"]  # ["occ_cloud_bmk_debug"]
val_dataset_occ_bmk01_cfg["lidar"]["referen_lidar"] = "middle_lidar"
val_dataset_occ_bmk01_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
val_dataset_occ_bmk01_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]


val_dataset_occ_bmk02_cfg = copy.deepcopy(val_dataset_occ_bmk_cfg)
val_dataset_occ_bmk02_cfg["annotation"].pop("box", None)  # remove box annotation
val_dataset_occ_bmk02_cfg["loader"]["datasets_names"] = [
    "ok_occ_bmk_multiclass_mini_143"
]  # ["car_z10_occ_prelabel_20250415_huiliu_jixie_20_3d_multiclass"]
val_dataset_occ_bmk02_cfg["lidar"]["referen_lidar"] = "middle_lidar"
val_dataset_occ_bmk02_cfg["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
val_dataset_occ_bmk02_cfg["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]

# map val
val_dataset_cfg_map = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg_map.update(
    evaluator=dict(
        type=MapEvaluator,
        map_range=map_lidar_range,
        eval_range=[
            -15.0,
            0.0,
            -6.0,
            15.0,
            100,
            6,
        ],
        core_range=[-6, 5, -2, 6, 50, 2],
        line_eval_cfg=dict(
            inter_dis=0.1,
            threash_dict=dict(fp_cd_thresh_list=[0.3, 0.6, 0.9, 1.2], inf_th_dis=99999),
            metric_conf=dict(keep_ratio=0.8, mode="d<->g"),
            decay_dis=dict(x=60, y=600, z=12),
        ),
        box_eval_cfg=dict(
            iou_th=[0.5, 0.3, 0.1],
            inf_iou=0.0,
        ),
        map_class_dict=map_class_dict,
        arrow_name_id_map=arrow_name_id_map,
        arrow_id_name_map=arrow_id_name_map,
        with_attr=False,
        with_cd=False,
        with_vd=False,
        with_pl=True,
        with_iou=True,
        with_kl_d=False,
        with_wt_d=False,
        with_vis=True,
    )
)
val_dataset_cfg_map["loader"]["datasets_names"] = ["map20w_val_mf"]
val_dataset_cfg_map["loader"]["only_key_frame"] = False
