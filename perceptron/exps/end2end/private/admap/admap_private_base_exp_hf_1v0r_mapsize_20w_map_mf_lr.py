"""Test Cmd: python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_300w_sampler.py --no-clearml -b 4 -e 80 --sync_bn 1 --eval_map
DATA3D_REDIS_URL="redis://mcd-mc-redis-web-main-d-r-p.mcd-mc:6666/0" DET3D_EXPID=18880 RLAUNCH_REPLICA_TOTAL=4 RLAUNCH_REPLICA=0 python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_diffhw_300w_keyframe.py -b 1 -e 15 --no-clearml --sync_bn 1

相对 det_map_unify_private_base_exp_hf_7v5r_new_sampler_inter20.py 修改：
1. MODEL_CFG --> 去掉 det head
2. DATA_VAL_CFG_MAP  --> resize lim 修改
LR修改为原step方式
"""


import torch
from torch.optim import AdamW
from perceptron.layers.lr_scheduler import TorchLRSchedulerWraperForStep, SequentialLR
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import DistributedSampler
from perceptron.exps.end2end.private.admap.admap_private_base_exp_hf_1v0r_detsize_300w_sampler_single import Exp


class ExpDev(Exp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(ExpDev, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_train_cfg["loader"]["only_key_frame"] = False  # det取关键帧，多帧训练不能抽帧
        self.data_train_cfg_bad_radar["loader"]["only_key_frame"] = False

        self.data_train_cfg.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        self.data_train_cfg.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        self.data_train_cfg_bad_radar.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        self.data_train_cfg_bad_radar.pipeline.ida_aug.aug_conf.resize_lim = (
            (0.711111, 0.711111),
            (0.466667, 0.466667),
        )

        self.data_val_cfg_map["loader"]["datasets_names"] = ["map20w_val"]  # ["map20w_val_down100_mf"] # ["map20w_val"]
        self.data_val_cfg_map.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        self.data_val_cfg_map.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        # self.data_train_cfg["annotation"]["admap"]["skip_det"] = False # 联合训练数据map based, 存在未标注det的帧
        # self.data_train_cfg_bad_radar["annotation"]["admap"]["skip_det"] = False

    def _configure_train_dataloader(self):
        # train_dataset1 = PrivateE2EDataset(
        #     **self.data_train_cfg,
        # )
        # train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_bad_radar)
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2])
        # train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        # train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_dataset = PrivateE2EDataset(
            **self.data_train_cfg,
        )
        train_dataset.batch_postcollate_fn = train_dataset.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            # sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            # sampler= GroupEachSampleInBatchSampler(train_dataset, shuffle=False, drop_last=False)
            sampler=DistributedSampler(train_dataset) if dist.is_distributed() else None,
            pin_memory=True,
            num_workers=6,
        )
        return train_loader

    def _configure_optimizer(self):
        optimizer = AdamW(self.model.parameters(), lr=1e-4)  # 5*1e-4
        return optimizer

    def _configure_lr_scheduler(self):
        def warmup(current_step: int):
            warmup_steps = 512
            return float(current_step / warmup_steps)

        warmup_scheduler = torch.optim.lr_scheduler.LambdaLR(self.optimizer, lr_lambda=warmup)
        # multistep_scheduler = torch.optim.lr_scheduler.MultiStepLR(self.optimizer, milestones=[18000,36000,54000], gamma=0.1)
        multistep_scheduler = torch.optim.lr_scheduler.MultiStepLR(
            self.optimizer, milestones=[25000, 50000, 75000, 100000], gamma=0.32
        )
        torch_lr_scheduler = SequentialLR(self.optimizer, [warmup_scheduler, multistep_scheduler], [512])
        scheduler = TorchLRSchedulerWraperForStep(torch_lr_scheduler, len(self.train_dataloader), self.max_epoch)
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(ExpDev).run()
