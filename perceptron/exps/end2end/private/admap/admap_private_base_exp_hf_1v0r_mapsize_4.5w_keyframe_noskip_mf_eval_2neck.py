"""Test Cmd: python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_300w_sampler.py --no-clearml -b 4 -e 80 --sync_bn 1 --eval_map
DATA3D_REDIS_URL="redis://mcd-mc-redis-web-main-d-r-p.mcd-mc:6666/0" DET3D_EXPID=18880 RLAUNCH_REPLICA_TOTAL=4 RLAUNCH_REPLICA=0 python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_diffhw_300w_keyframe.py -b 1 -e 15 --no-clearml --sync_bn 1

相对 det_map_unify_private_base_exp_hf_7v5r_new_sampler_inter20.py 修改：
1. MODEL_CFG --> 去掉 det head
2. DATA_VAL_CFG_MAP  --> resize lim 修改
"""
import torch
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import DistributedSampler
from perceptron.exps.end2end.private.admap.admap_private_base_exp_hf_1v0r_detsize_300w_sampler import Exp
from perceptron.Perceptron.perceptron.models.end2end.perceptron_2neck import VisionEncoder


class ExpDev(Exp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(ExpDev, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_train_cfg["loader"]["only_key_frame"] = False  # det取关键帧，多帧训练不能抽帧
        self.data_train_cfg_bad_radar["loader"]["only_key_frame"] = False

        # self.data_train_cfg.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        # self.data_train_cfg.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        # self.data_train_cfg_bad_radar.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        # self.data_train_cfg_bad_radar.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        # self.data_val_cfg_map.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        # self.data_val_cfg_map.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        self.data_train_cfg["annotation"]["admap"]["skip_det"] = False  # 联合训练数据map based, 存在未标注det的帧
        self.data_train_cfg_bad_radar["annotation"]["admap"]["skip_det"] = False

        self.model_cfg["camera_encoder"] = dict(
            img_backbone=dict(
                # type='ResNet',
                depth=50,
                num_stages=4,
                # out_indices=(2, 3),
                out_indices=(0, 1, 2, 3),
                frozen_stages=-1,
                norm_cfg=dict(type="BN", requires_grad=True),
                norm_eval=True,
                style="pytorch",
                with_cp=True,
                init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
            ),
            img_neck=dict(
                # type='FPN',
                in_channels=[1024, 2048],
                out_channels=256,
                num_outs=2,
                # with_cp=True,
            ),
            img_neck_map=dict(
                type="SECONDFPN",
                in_channels=[256, 512, 1024, 2048],
                upsample_strides=[0.25, 0.5, 1, 2],
                out_channels=[32, 32, 64, 128],
            ),
        )

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        # train_dataset1 = PrivateE2EDataset(
        #     **self.data_train_cfg,
        # )
        # train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_bad_radar)
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2])
        # train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        # train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_dataset = PrivateE2EDataset(
            **self.data_train_cfg,
        )
        train_dataset.batch_postcollate_fn = train_dataset.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            # sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            # sampler= GroupEachSampleInBatchSampler(train_dataset, shuffle=False, drop_last=False)
            sampler=DistributedSampler(train_dataset) if dist.is_distributed() else None,
            pin_memory=True,
            num_workers=6,
        )
        return train_loader


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(ExpDev).run()
