""" HF9: 1v0r, interval sampler
Description:  Trained with prelabeled data with map only
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_300w_sampler.py --no-clearml -b 4 -e 80 --sync_bn 4

Test Cmd: python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_300w_sampler.py --no-clearml -b 4 -e 80 --sync_bn 1 --eval_map

相对 det_map_unify_private_base_exp_hf_7v5r_new_sampler_inter20.py 修改：
1. MODEL_CFG --> 去掉 det head
2. DATA_VAL_CFG_MAP  --> resize lim 修改
"""
import sys
import refile
import torch
import mmcv
import copy
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.Perceptron.perceptron.exps.end2end.private.pretrain.model_cfg.det_map_model_cfg_7v5r_single import (
    MODEL_CFG,
)
from perceptron.exps.base_exp import BaseExp
from perceptron.Perceptron.perceptron.exps.end2end.private.pretrain.data_cfg.det_admap_annos_hf import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)
from perceptron.Perceptron.perceptron.exps.end2end.private.admap.data_cfg.admap_annos_hf_evalonly import (
    val_dataset_cfg_map as DATA_VAL_CFG_MAP,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset

# from perceptron.data.sampler import InfiniteSampler
from perceptron.data.sampler import InfiniteIntervalSampler
from torch.utils.data import DistributedSampler
from perceptron.data.det3d.modules.radar.radar_hf_virtual_aug import HFRadarVirtualAug
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 1e-3  #
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 10
        self.dump_interval = 2
        self.lr_scale_factor = {}  # 当加载pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35

        # 2. Dataset and model configuration
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)
        # 2.1 map val config
        self.data_val_cfg_map = mmcv.Config(DATA_VAL_CFG_MAP)

        # self.data_train_cfg["pipeline"]["bda_aug"]["aug_conf"]["flip_dy_ratio"] = 0.0
        self.data_train_cfg["loader"]["datasets_names"] = ["map_share_prelabels"]
        self.data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1

        self.data_train_cfg_bad_radar = copy.deepcopy(self.data_train_cfg)
        self.data_train_cfg_bad_radar["loader"]["datasets_names"] = ["map_share_prelabels_bad_radar"]
        self.data_train_cfg_bad_radar["radar"]["type"] = HFRadarVirtualAug
        self.data_train_cfg["loader"]["only_key_frame"] = False
        self.data_train_cfg_bad_radar["loader"]["only_key_frame"] = False

        # 3. other configuration change in this function
        self._change_cfg_params()

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # model
        self.model_cfg.pop("det_head")

        # data
        # 根据训练 setting 来决定下面两行是否解注释
        # self.data_val_cfg_map.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        # self.data_val_cfg_map.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        camera_name = "cam_front_120"
        self.data_val_cfg_map.pipeline.ida_aug.camera_names = [camera_name]

        # use first cam to train
        self.data_train_cfg.image.undistort_func = dict(
            cam_front_120=self.data_train_cfg.image.undistort_func.cam_front_120
        )
        self.data_train_cfg.image.camera_names = [camera_name]
        self.data_train_cfg.sensor_names.camera_names = [camera_name]
        self.data_train_cfg.loader.camera_names = [camera_name]
        self.data_train_cfg.pipeline.ida_aug.camera_names = [camera_name]

        self.data_train_cfg_bad_radar.image.undistort_func = dict(
            cam_front_120=self.data_train_cfg_bad_radar.image.undistort_func.cam_front_120
        )
        self.data_train_cfg_bad_radar.image.camera_names = [camera_name]
        self.data_train_cfg_bad_radar.sensor_names.camera_names = [camera_name]
        self.data_train_cfg_bad_radar.loader.camera_names = [camera_name]
        self.data_train_cfg_bad_radar.pipeline.ida_aug.camera_names = [camera_name]

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(
            **self.data_train_cfg,
        )
        train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_bad_radar)
        train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2])
        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteIntervalSampler(
                len(train_dataset), seed=self.seed if self.seed else 0, interval=80
            )  # 需要给定interval
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=5,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts_det, pred_maps = self.model(**batch)
        remap_pred_dicts = self.process_det_outs(pred_dicts_det)
        return dict(pred_dicts=remap_pred_dicts, pred_maps=pred_maps)  # remap

    def process_det_outs(self, pred_dicts_det):
        if pred_dicts_det is None:
            return []
        remap_pred_dicts = []
        for pred_dict in pred_dicts_det:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return remap_pred_dicts

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
