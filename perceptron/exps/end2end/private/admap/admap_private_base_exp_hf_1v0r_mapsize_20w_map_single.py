"""Test Cmd: python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_300w_sampler.py --no-clearml -b 4 -e 80 --sync_bn 1 --eval_map
DATA3D_REDIS_URL="redis://mcd-mc-redis-web-main-d-r-p.mcd-mc:6666/0" DET3D_EXPID=18880 RLAUNCH_REPLICA_TOTAL=4 RLAUNCH_REPLICA=0 python3 perceptron/exps/end2end/private/admap/admap_private_base_exp_hf_1v0r_diffhw_300w_keyframe.py -b 1 -e 15 --no-clearml --sync_bn 1

相对 det_map_unify_private_base_exp_hf_7v5r_new_sampler_inter20.py 修改：
1. MODEL_CFG --> 去掉 det head
2. DATA_VAL_CFG_MAP  --> resize lim 修改

1. 支持单帧/多帧训练
"""
import torch
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.end2end.private.admap.admap_private_base_exp_hf_1v0r_detsize_300w_sampler_single import Exp


class ExpDev(Exp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(ExpDev, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.data_train_cfg["loader"]["datasets_names"] = ["map20w_train"]
        self.data_train_cfg["loader"]["only_key_frame"] = False
        self.data_train_cfg_bad_radar["loader"]["only_key_frame"] = False

        self.data_train_cfg.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        self.data_train_cfg.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        self.data_val_cfg_map["loader"]["datasets_names"] = ["map20w_val"]
        self.data_val_cfg_map.pipeline.ida_aug.aug_conf.final_dim = (512, 896)
        self.data_val_cfg_map.pipeline.ida_aug.aug_conf.resize_lim = ((0.711111, 0.711111), (0.466667, 0.466667))

        self.data_train_cfg["annotation"]["admap"]["skip_det"] = False
        self.data_train_cfg_bad_radar["annotation"]["admap"]["skip_det"] = False

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(
            **self.data_train_cfg,
        )
        train_dataset = torch.utils.data.ConcatDataset([train_dataset1])
        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=6,
        )
        return train_loader


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(ExpDev).run()
