from perceptron.data.det3d.modules.annotation import AnnotationDet, AnnotationADMap
import copy

from perceptron.data.det3d.source.config import HFCar9
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.exps.end2end.private.sensor_cfg.e2e_sensors_hf_7v5r import (
    lidar as lidar_cfg,
    image as image_cfg,
    radar as radar_cfg,
    _CAMERA_LIST,
    _SENSOR_NAMES,
)
from perceptron.data.det3d.modules import (
    EvaluationV3,
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    ObjectRangeFilter,
)

category_map = {
    "小汽车": "car",
    "汽车": "car",
    "货车": "truck",
    "工程车": "construction_vehicle",
    "巴士": "bus",
    "摩托车": "motorcycle",
    "自行车": "bicycle",
    "三轮车": "tricycle",
    "骑车人": "cyclist",
    "骑行的人": "cyclist",
    "人": "pedestrian",
    "行人": "pedestrian",
    "其它": "other",
    "其他": "other",
    "残影": "ghost",
    "蒙版": "masked_area",
    "car": "car",
    "truck": "truck",
    "construction_vehicle": "construction_vehicle",
    "bus": "bus",
    "motorcycle": "motorcycle",
    "bicycle": "bicycle",
    "tricycle": "tricycle",
    "cyclist": "cyclist",
    "pedestrian": "pedestrian",
    "other": "other",
    "ghost": "ghost",
    "masked_area": "masked_area",
}

category_map_reverse = {
    "car": "汽车",
    "truck": "货车",
    "construction_vehicle": "工程车",
    "bus": "巴士",
    "motorcycle": "摩托车",
    "bicycle": "自行车",
    "tricycle": "三轮车",
    "cyclist": "骑车人",
    "pedestrian": "人",
    "other": "其它",
    "ghost": "残影",
    "masked_area": "蒙版",
}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
    # "masked_area",
]

# point_cloud_range = [-15.2, -80.0, -5.0, 15.2, 204.8, 3.0]
point_cloud_range = [0, -15, -6, 100, 15, 6]  # dwj 这里做 test 的时候要和模型设置对齐

_PIPELINE_MULTIFRAME = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
    ),
    # bda_aug: need to be adapted
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 896),
            resize_lim=((0.711111, 0.711111), (0.466667, 0.466667)),  # h, w 先 resize 成 (768, 896)  # was (0.472, 0.5)
            bot_pct_lim=(0.0, 0.0),
            H=1080,  # 这里 H, W 和 resize_lim 一起起作用
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={
            "img_mean": [123.675, 116.28, 103.53],
            "img_std": [58.395, 57.12, 57.375],
            "to_rgb": False,
        },  # 这里 的 to_rgb 字段在 gpu aug=True 时没有用到
    ),
)

annotation_cfg = dict(
    box=dict(
        type=AnnotationDet,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=False,  # was True,
        filter_outlier_frames=False,  # was True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=False,  # was True,
        roi_range=point_cloud_range,
        # with_predict=False,
        HF=True,
        # fut_traj_len=65,
    ),
    admap=dict(
        type=AnnotationADMap,
    ),
)

_CAR = HFCar9
base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=dict(
        type=LoaderSimFov,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["debug_sample_track"],
        only_key_frame=False,  # was True
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=image_cfg,
    radar=radar_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
)

# config for eval dataset
val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
val_dataset_cfg["loader"].update(datasets_names=["CAR9_BMK_OCC_DAY"])
val_dataset_cfg["annotation"]["box"].update(occlusion_threshold=1)  # 这里需要确认下!
val_dataset_cfg["radar"]["with_virtual_radar"] = False
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationV3,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="e2e_l3_far",
        eval_cfg_l2="e2e_gaosu_l3",
    )
)
