""" HF9: 7V5R
Description:  Trained with prelabeled data shared with map
Cmd: DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch --max-wait-duration=72h -P 4  --cpu=48 --gpu=8 --memory=300000 --private-machine yes --preemptible no -- python3 perceptron/exps/end2end/private/det/det_private_base_exp_hf_7v5r.py --no-clearml -b 4 -e 80 --amp --sync_bn 4
Log+Ckpt: s3://gongjiahao-share/end2end/visionEncoder/exp/det_7v5r_dn_virtual_q300/
Author: GJH
Data: 2024-03-02
"""
import sys
import refile
import torch
import mmcv
import torch.nn as nn
import torch.optim as optim
from functools import partial
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.utils.det3d_utils.initialize_utils import model_named_layers

from perceptron.Perceptron.perceptron.exps.end2end.private.admap.model_cfg.map_model_cfg_1v0r import MODEL_CFG
from perceptron.exps.base_exp import BaseExp
from perceptron.Perceptron.perceptron.exps.end2end.private.admap.data_cfg.admap_annos_hf import (
    base_dataset_cfg as DATA_TRAIN_CFG,
    val_dataset_cfg as DATA_VAL_CFG,
)

from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.sampler import InfiniteSampler
from torch.utils.data import DistributedSampler
from perceptron.models.end2end.perceptron.perceptron import VisionEncoder


class Exp(BaseExp):
    def __init__(
        self,
        batch_size_per_device=4,
        total_devices=1,
        max_epoch=20,
        **kwargs,
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.exp_name = "__".join(refile.SmartPath(sys.argv.copy()[0]).parts[-2:])[:-3]
        # 1. Training setting
        self.lr = 1e-3
        self.init_scale = 512
        self.print_interval = 50
        self.num_keep_latest_ckpt = 5
        self.dump_interval = 2
        self.lr_scale_factor = {
            "camera_encoder.img_backbone": 0.01,
            "camera_encoder.img_neck": 0.1,
        }  # 当加载 pretrain的时候，可以将lr_scale_factor设置为该形式，training from scratch的时候，设置为{}.
        self.grad_clip_value = 35

        # 2. Dataset and model configuration
        self.data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)
        self.data_val_cfg = mmcv.Config(DATA_VAL_CFG)
        self.model_cfg = mmcv.Config(MODEL_CFG)

        # 3. other configuration change in this function
        self._change_cfg_params()
        # self.data_train_cfg["pipeline"]["bda_aug"]["aug_conf"]["flip_dy_ratio"] = 0.0
        # self.data_train_cfg["loader"]["datasets_names"] = ["map_share_prelabels"]
        self.data_train_cfg["loader"]["datasets_names"] = ["mapbmk_align"]
        self.data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
        self.data_train_cfg["annotation"]["box"]["occlusion_threshold"] = -1
        self.data_train_cfg["loader"]["only_key_frame"] = False

        self.data_val_cfg["loader"]["datasets_names"] = ["mapbmk_align"]
        self.data_val_cfg["loader"]["only_key_frame"] = False

    def _change_cfg_params(self):
        r"""
        This func is designed to change cfg `optionally`. Such as, `open training with checkpint`, `set print interval` \
        which depend on your requirement. For those should be inherited, should be called in `self.__init__`

        Example:
        ----------
        ```
        >>> class YourExp(BaseExp):
                def __init__(self, *args, **kwargs):
                    self._change_cfg_params()

                def _change_cfg_params(self):
                    self.model_cfg["camera_encoder"]["img_backbone_conf"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["bev_encoder"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.model_cfg["det_head"]["dense_head"]["with_cp"] = True  # open checkpoint save gpu mem.
                    self.print_interval = 20 # set print interval.
                    pass
                ....
        ```
        """
        # self.model_cfg.det_head.bbox_coder.score_threshold = 0.1
        # self.model_cfg.num_query = 300
        # self.model_cfg["det_head"]["init_radar_num_query"] = 0
        # self.model_cfg["det_head"]["num_query"] = 0 + 300
        # self.model_cfg["det_head"]["modal"] = ["Camera", "Radar"]

        # self.model_cfg["radar_encoder"] = dict(out_channels=[11, 256, 256])
        # self.model_cfg["det_head"]["transformer"]["decoder"]["num_layers"] = 3
        # self.model_cfg["det_head"]["separate_head"]["final_kernel"] = 1
        # self.model_cfg["dn_cfg"] = dict(
        #     use_dn=True, scalar=6, noise_scale=1.0, noise_trans=0.0, split=0.75, dn_weight=1.0
        # )

        # self.data_val_cfg["loader"]["datasets_names"] = ["CAR9_BMK_OCC_DAY"]
        # self.use_ray_nms = self.model_cfg.get("use_ray_nms", False)

    def _configure_model(self):
        model = VisionEncoder(
            model_cfg=self.model_cfg,
        )
        return model

    def _configure_train_dataloader(self):
        train_dataset1 = PrivateE2EDataset(
            **self.data_train_cfg,
        )
        # train_dataset2 = PrivateE2EDataset(**self.data_train_cfg_bad_radar)

        train_dataset = torch.utils.data.ConcatDataset([train_dataset1])
        # train_dataset = torch.utils.data.ConcatDataset([train_dataset1, train_dataset2])
        train_dataset.batch_postcollate_fn = train_dataset1.batch_postcollate_fn
        train_dataset.batch_preforward_fn = train_dataset1.batch_preforward_fn

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
            num_workers=5,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def _configure_test_dataloader(self):
        val_dataset = PrivateE2EDataset(
            **self.data_val_cfg,
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=5,
            sampler=DistributedSampler(val_dataset, shuffle=False, drop_last=False) if dist.is_distributed() else None,
            pin_memory=False,
        )
        return val_loader

    def training_step(self, batch):
        if "roi_mask" not in batch:
            batch["roi_mask"] = None
        ret_dict, loss_dict, _ = self.model(**batch)
        loss = sum(_value for _key, _value in loss_dict.items() if "loss" in _key)

        return loss, loss_dict

    @torch.no_grad()
    def test_step(self, batch):
        pred_dicts_det, pred_maps = self.model(**batch)
        remap_pred_dicts = self.process_det_outs(pred_dicts_det)
        return dict(pred_dicts=remap_pred_dicts)

    def process_det_outs(self, pred_dicts_det):
        if pred_dicts_det is None:
            return []
        remap_pred_dicts = []
        for pred_dict in pred_dicts_det:
            remap_pred_dict = {}
            for k, v in pred_dict.items():
                if k == "bboxes":
                    remap_pred_dict["pred_boxes"] = v
                elif k == "labels":
                    remap_pred_dict["pred_" + k] = v
                else:
                    remap_pred_dict["pred_" + k] = v
            if True:  # nms
                from perceptron.data.det3d.modules.utils.post_process import StandardNMSPostProcess

                boxes3d = remap_pred_dict["pred_boxes"]
                top_scores = remap_pred_dict["pred_scores"]
                if top_scores.shape[0] != 0:
                    if not self.use_ray_nms:
                        selected = StandardNMSPostProcess._nms_gpu_3d(
                            boxes3d[:, :7],
                            top_scores,
                            thresh=0.8,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    else:
                        selected = StandardNMSPostProcess._ray_nms(
                            boxes3d[:, :7].cpu().numpy(),
                            top_scores.cpu().numpy(),
                            thresh=5.0,
                            pre_maxsize=300,
                            post_max_size=300,
                        )
                    remap_pred_dict["pred_boxes"] = remap_pred_dict["pred_boxes"][selected]
                    remap_pred_dict["pred_scores"] = remap_pred_dict["pred_scores"][selected]
                    remap_pred_dict["pred_labels"] = remap_pred_dict["pred_labels"][selected]
            remap_pred_dicts.append(remap_pred_dict)
        return remap_pred_dicts

    def _configure_optimizer(self):
        layers_dict = model_named_layers(self.model)
        layer_groups = {name: [] for name, v in self.lr_scale_factor.items()}
        layer_groups.update({"others": []})
        for name, layer in layers_dict.items():
            exist = False
            for gallery_name in self.lr_scale_factor.keys():
                if gallery_name in name:
                    exist = True
                    break
            k = gallery_name if exist else "others"
            layer_groups[k].append(layer)

        lr_list = [v for k, v in self.lr_scale_factor.items()] + [1.0]
        lr_list = [self.lr * x for x in lr_list]

        optimizer_func = partial(optim.AdamW, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func,
            lr_list,
            [nn.Sequential(*v) for _, v in layer_groups.items()],
            wd=0.01,
            true_wd=True,
            bn_wd=True,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    import logging

    logging.getLogger("mmcv").disabled = True
    logging.getLogger("mmseg").disabled = True
    Det3DCli(Exp).run()
