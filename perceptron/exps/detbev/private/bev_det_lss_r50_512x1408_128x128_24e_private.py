"""
train:  DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --cpu=32 --gpu=8 --memory=204800 --preemptible=no --charged-group=transformer -- python perceptron/exps/detbev/bev_det_lss_r50_512x1408_128x128_24e_private.py --no-clearml --sync_bn 4 -b 1 --amp

test: rlaunch --cpu=8 --gpu=2 --memory=20480 -- python perceptron/exps/detbev/bev_det_lss_r50_512x1408_128x128_24e_private.py --ckpt [CKPT_PATH] --eval -b 2

mAP: 0.4541
mATE: 0.3996
mASE: 0.1632
mAOE: 0.3748
NDS: 0.5416
Eval time: 4.8s


Per-class results:
|Object Class             	|AP	|ATE	|ASE	|AOE	|
|Bus(609)                 	|0.536	|0.399	|0.178	|0.060	|
|Car(12277)               	|0.793	|0.275	|0.107	|0.157	|
|Cycle(2249)              	|0.457	|0.403	|0.132	|0.309	|
|Pedestrian(230)          	|0.338	|0.421	|0.160	|1.204	|
|Tricycle(21)             	|0.364	|0.355	|0.198	|0.485	|
|Truck(943)               	|0.236	|0.546	|0.204	|0.034	|


"""
import torch
from torch.utils.data import DistributedSampler
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.data.det3d.dataset.private_data.dataset_vision import collate_fn
from perceptron.data.det3d.dataset.private_data.dataset_vision import PrivateBevDetData  # noqa: registry
from perceptron.exps.detbev.base.private_occlution_data import bmk_split, trainval_220307
from perceptron.exps.detbev.private.bev_det_lss_r50_256x704_128x128_24e_private import Exp as BaseBevdetPrivate
from perceptron.exps.detbev.private.bev_det_lss_r50_256x704_128x128_24e_private import data_configs, class_names


__all__ = ["Exp"]


class Exp(BaseBevdetPrivate):
    def __init__(self, scale=2, **kwargs):
        super(Exp, self).__init__(**kwargs)
        self.backbone_conf.update({"final_dim": (256 * scale, 704 * scale)})
        self.ida_aug_conf.update({"final_dim": (256 * scale, 704 * scale), "resize_lim": (0.772, 1.10)})

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=trainval_220307,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11"],
            lidar_key_list=[],
            training=True,
            pipeline=[],
        )
        train_dataset.pre_only = True

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=4,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=bmk_split,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11"],
            lidar_key_list=[],
            training=False,
            pipeline=[],
            eval_configs=dict(xviz_visualize=dict(scenes_num=20)),
        )
        val_dataset.pre_only = False
        sampler = DistributedSampler(val_dataset, shuffle=False, drop_last=False)
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=2,
            sampler=sampler,
            pin_memory=True,
        )
        return val_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
