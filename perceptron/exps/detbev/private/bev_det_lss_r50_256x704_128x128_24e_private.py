"""
train:  DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --cpu=32 --gpu=8 --memory=204800 --preemptible=no --charged-group=transformer -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_24e_private.py --no-clearml --sync_bn 4 -b 2 --amp

test: rlaunch --cpu=8 --gpu=2 --memory=20480 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_24e_private.py --ckpt [CKPT_PATH] --eval -b 2


mAP: 0.3803
mATE: 0.4695
mASE: 0.1794
mAOE: 0.4653
NDS: 0.4734
Eval time: 3.9s


Per-class results:
|Object Class             	|AP	|ATE	|ASE	|AOE	|
|Bus(609)                 	|0.454	|0.605	|0.200	|0.050	|
|Car(12277)               	|0.773	|0.284	|0.106	|0.182	|
|Cycle(2249)              	|0.417	|0.423	|0.133	|0.415	|
|P<PERSON><PERSON><PERSON>(230)          	|0.225	|0.436	|0.168	|1.285	|
|Tricycle(21)             	|0.303	|0.575	|0.251	|0.809	|
|Truck(943)               	|0.110	|0.495	|0.220	|0.050	|


"""
import torch
import numpy as np
from functools import partial
from torch.utils.data import DistributedSampler
from perceptron.engine.cli import Det3DCli
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.executors import Det3DEvaluator, Det3DInfer
from perceptron.utils import torch_dist as dist
from perceptron.layers.lr_scheduler import StepLRScheduler
from perceptron.models.detbev.bev_det import BevDetPrivate
from perceptron.data.det3d.dataset.private_data.dataset_vision import collate_fn
from perceptron.data.det3d.dataset.private_data.dataset_vision import PrivateBevDetData  # noqa: registry
from perceptron.exps.detbev.base.private_occlution_data import bmk_split, trainval_220307, demo_split
from perceptron.exps.detbev.bev_det_lss_r50_256x704_128x128_24e import bev_backbone, bev_neck


__all__ = ["Exp"]

H = 1080
W = 1920
final_dim = (256, 704)
img_conf = dict(img_mean=[123.675, 116.28, 103.53], img_std=[58.395, 57.12, 57.375], to_rgb=True)

point_cloud_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
voxel_size = [0.1, 0.1, 0.2]
data_configs = dict(
    point_cloud_range=point_cloud_range,
    voxel_size=voxel_size,
    grid_size=np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64),
    max_num_points=5,
    max_voxels=60000,
    src_num_point_features=4,
    use_num_point_features=4,
)

backbone_conf = {
    "x_bound": [-51.2, 51.2, 0.8],
    "y_bound": [-51.2, 51.2, 0.8],
    "z_bound": [-5, 3, 8],
    "d_bound": [2.0, 58.0, 0.5],
    "final_dim": final_dim,
    "output_channels": 80,
    "downsample_factor": 16,
    "img_backbone_conf": dict(
        type="ResNet",
        depth=50,
        frozen_stages=0,
        out_indices=[0, 1, 2, 3],
        norm_eval=False,
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet50"),
    ),
    "img_neck_conf": dict(
        type="SECONDFPN",
        in_channels=[256, 512, 1024, 2048],
        upsample_strides=[0.25, 0.5, 1, 2],
        out_channels=[128, 128, 128, 128],
    ),
    "depth_net_conf": dict(in_channels=512, mid_channels=512),
}

ida_aug_conf = {
    "resize_lim": (0.386, 0.55),
    "final_dim": final_dim,
    "rot_lim": (-5.4, 5.4),
    "H": H,
    "W": W,
    "rand_flip": True,
    "bot_pct_lim": (0.0, 0.0),
}

bda_aug_conf = {"rot_lim": (-22.5, 22.5), "scale_lim": (0.95, 1.05), "flip_dx_ratio": 0.5, "flip_dy_ratio": 0.5}

class_names = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "motorcycle",
    "bicycle",
    "tricycle",
    "cyclist",
    "pedestrian",
]

TASKS = [
    dict(num_class=1, class_names=["car"]),
    dict(num_class=2, class_names=["truck", "construction_vehicle"]),
    dict(num_class=2, class_names=["bus"]),
    dict(num_class=2, class_names=["motorcycle", "bicycle", "tricycle", "cyclist"]),
    dict(num_class=2, class_names=["pedestrian"]),
]

common_heads = dict(reg=(2, 2), height=(1, 2), dim=(3, 2), rot=(2, 2))

bbox_coder = dict(
    type="CenterPointBBoxCoder",
    post_center_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
    max_num=500,
    score_threshold=0.1,
    out_size_factor=4,
    voxel_size=[0.2, 0.2, 8],
    pc_range=[-51.2, -51.2, -5, 51.2, 51.2, 3],
    code_size=7,
)

train_cfg = dict(
    point_cloud_range=[-51.2, -51.2, -5, 51.2, 51.2, 3],
    grid_size=[512, 512, 1],
    voxel_size=[0.2, 0.2, 8],
    out_size_factor=4,
    dense_reg=1,
    gaussian_overlap=0.1,
    max_objs=500,
    min_radius=2,
    code_weights=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
)

test_cfg = dict(
    post_center_limit_range=[-61.2, -61.2, -10.0, 61.2, 61.2, 10.0],
    max_per_img=500,
    max_pool_nms=False,
    min_radius=[4, 12, 10, 1, 0.85, 0.175],
    score_threshold=0.1,
    out_size_factor=4,
    voxel_size=[0.2, 0.2, 8],
    nms_type="circle",
    pre_max_size=1000,
    post_max_size=83,
    nms_thr=0.2,
)

head_conf = {
    "bev_backbone_conf": bev_backbone,
    "bev_neck_conf": bev_neck,
    "tasks": TASKS,
    "common_heads": common_heads,
    "bbox_coder": bbox_coder,
    "train_cfg": train_cfg,
    "test_cfg": test_cfg,
    "in_channels": 256,  # Equal to bev_neck output_channels.
    "loss_cls": dict(type="GaussianFocalLoss", reduction="mean"),
    "loss_bbox": dict(type="L1Loss", reduction="mean", loss_weight=0.25),
    "gaussian_overlap": 0.1,
    "min_radius": 2,
}


class Exp(BaseExp):
    def __init__(
        self,
        eval_interval=1,
        batch_size_per_device=8,
        total_devices=1,
        max_epoch=24,
        class_names=class_names,
        backbone_conf=backbone_conf,
        head_conf=head_conf,
        ida_aug_conf=ida_aug_conf,
        bda_aug_conf=bda_aug_conf,
        pos_weight=2.13,
        max_grad_norm=5.0,
        dump_interval=1,
        warmup_epochs=0.0,
        **kwargs
    ):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.eval_interval = eval_interval
        self.pos_weight = pos_weight
        self.loss_func = self._configure_loss_func().cuda()
        self.basic_lr = 2e-4
        self.class_names = class_names
        self.backbone_conf = backbone_conf
        self.head_conf = head_conf
        self.ida_aug_conf = ida_aug_conf
        self.bda_aug_conf = bda_aug_conf
        self.grad_clip_value = max_grad_norm
        self.dump_interval = dump_interval
        self.warmup_epochs = warmup_epochs
        self.eval_executor_class = partial(Det3DEvaluator, eval_interval=24)
        self.infer_executor_class = Det3DInfer
        self.data_return_depth = False

    def training_step(self, batch):
        imgs, mats_dict, gt_boxes, gt_labels = (
            batch["imgs"],
            batch["mats_dict"],
            batch["gt_boxes"],
            batch["gt_labels"],
        )
        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = value.cuda()
            imgs = imgs.cuda()
            gt_boxes = [gt_box.cuda() for gt_box in gt_boxes]
            gt_labels = [gt_label.cuda() for gt_label in gt_labels]
        preds = self.model(
            imgs,
            mats_dict,
        )
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            targets = self.model.module.get_targets(gt_boxes, gt_labels)
            loss = self.model.module.loss(targets, preds)
        else:
            targets = self.model.get_targets(gt_boxes, gt_labels)
            loss = self.model.loss(targets, preds)
        return loss

    def test_step(self, batch):
        imgs, mats_dict, img_metas = (
            batch["imgs"],
            batch["mats_dict"],
            batch["img_metas"],
        )
        if torch.cuda.is_available():
            for key, value in mats_dict.items():
                mats_dict[key] = value.cuda()
            imgs = imgs.cuda()
        preds = self.model(
            imgs.cuda(),
            mats_dict,
        )
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            results = self.model.module.get_bboxes(preds, img_metas)
        else:
            results = self.model.get_bboxes(preds, img_metas)
        return results

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=trainval_220307,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11"],
            lidar_key_list=[],
            training=True,
            pipeline=[],
            return_depth=self.data_return_depth,
        )

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=2,
            drop_last=True,
            shuffle=False,
            collate_fn=partial(collate_fn, is_return_depth=self.data_return_depth),
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=bmk_split,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11"],
            lidar_key_list=[],
            training=False,
            pipeline=[],
            eval_configs=dict(xviz_visualize=dict(scenes_num=20)),
        )
        val_dataset.pre_only = False
        sampler = DistributedSampler(val_dataset, shuffle=False, drop_last=False)
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=2,
            sampler=sampler,
            pin_memory=True,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=demo_split,
            only_key_frame=False,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11"],
            lidar_key_list=[],
            training=False,
            pipeline=[],
            eval_configs=dict(xviz_visualize=dict(scenes_num=20)),
        )
        test_dataset.pre_only = False
        sampler = DistributedSampler(test_dataset, shuffle=False, drop_last=False)
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=2,
            sampler=sampler,
            pin_memory=True,
        )
        return test_loader

    def _configure_model(self):
        model = BevDetPrivate(self.backbone_conf, self.head_conf)
        model.class_names = [
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "motorcycle",
            "bicycle",
            "tricycle",
            "cyclist",
            "pedestrian",
        ]
        return model

    def _configure_loss_func(self):
        return torch.nn.BCEWithLogitsLoss(pos_weight=torch.Tensor([self.pos_weight]))

    def _configure_optimizer(self):
        lr = self.basic_lr
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=1e-7)
        return optimizer

    def _configure_lr_scheduler(self):
        # TODO: Finetune training config.
        scheduler = StepLRScheduler(
            self.optimizer,
            self.basic_lr,
            len(self.train_dataloader),
            self.max_epoch,
            milestones=[19, 23],
        )
        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
