"""
train:  DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --cpu=32 --gpu=8 --memory=204800 --preemptible=no --charged-group=transformer -- python perceptron/exps/detbev/bev_det_lss_r50_512x1408_128x128_24e_private_van.py --no-clearml --sync_bn 4 -b 1

test: rlaunch --cpu=32 --gpu=8 --memory=204800 -- python perceptron/exps/detbev/bev_det_lss_vanbase_512x1408_128x128_24e_private.py --ckpt [CKPT_PATH] --eval -b 1

mAP: 0.5271
mATE: 0.3918
mASE: 0.1492
mAOE: 0.2424
NDS: 0.6065
Eval time: 3.2s


Per-class results:
|Object Class                   |AP     |ATE    |ASE    |AOE    |
|Bus(609)                       |0.583  |0.357  |0.134  |0.050  |
|Car(12277)                     |0.834  |0.212  |0.095  |0.078  |
|Cycle(2249)                    |0.605  |0.342  |0.126  |0.131  |
|Pedestrian(230)                |0.438  |0.421  |0.148  |0.917  |
|Tricycle(21)                   |0.294  |0.513  |0.216  |0.252  |
|Truck(943)                     |0.410  |0.506  |0.177  |0.026  |

"""
import torch
from torch.utils.data import DistributedSampler
from perceptron.engine.cli import Det3DCli
from perceptron.utils import torch_dist as dist
from perceptron.layers.blocks_2d.mmdet2d import van  # noqa: registry
from perceptron.data.det3d.dataset.private_data.dataset_vision import collate_fn
from perceptron.data.det3d.dataset.private_data.dataset_vision import PrivateBevDetData  # noqa: registry
from perceptron.exps.detbev.base.private_occlution_data import bmk_split, trainval_220307, demo_split
from perceptron.exps.detbev.private.bev_det_lss_r50_256x704_128x128_24e_private import Exp as BaseBevdetPrivate
from perceptron.exps.detbev.private.bev_det_lss_r50_256x704_128x128_24e_private import data_configs, class_names

__all__ = ["Exp"]


class Exp(BaseBevdetPrivate):
    def __init__(self, scale=2, **kwargs):
        super(Exp, self).__init__(**kwargs)
        self.backbone_conf.update(
            {
                "final_dim": (256 * scale, 704 * scale),
                "img_backbone_conf": dict(
                    type="van_base",
                    with_cp=True,
                    pretrained="s3://e2emodel-data/pretrained_backbone/van_base_828.pth.tar",
                ),
                "img_neck_conf": dict(
                    type="SECONDFPN",
                    in_channels=[64, 128, 320, 512],
                    upsample_strides=[0.25, 0.5, 1, 2],
                    out_channels=[128, 128, 128, 128],
                ),
            }
        )
        self.ida_aug_conf.update({"final_dim": (256 * scale, 704 * scale), "resize_lim": (0.772, 1.10)})

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=trainval_220307,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
            lidar_key_list=[],
            training=True,
            pipeline=[],
        )
        train_dataset.pre_only = True

        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=4,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn,
            sampler=InfiniteSampler(len(train_dataset), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
            pin_memory=True,
        )
        return train_loader

    def _configure_val_dataloader(self):
        val_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=bmk_split,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
            lidar_key_list=[],
            training=False,
            pipeline=[],
            eval_configs=dict(xviz_visualize=dict(scenes_num=20)),
        )
        val_dataset.pre_only = False
        sampler = DistributedSampler(val_dataset, shuffle=False, drop_last=False)
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=2,
            sampler=sampler,
            pin_memory=True,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_dataset = PrivateBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            data_configs=data_configs,
            data_paths=demo_split,
            only_key_frame=False,
            class_names=class_names,
            undistort_alpha=0.0,
            img_key_list=["camera_2", "camera_3", "camera_5", "camera_10", "camera_11", "camera_15"],
            lidar_key_list=[],
            training=False,
            pipeline=[],
            eval_configs=dict(xviz_visualize=dict(scenes_num=20)),
        )
        test_dataset.pre_only = False
        sampler = DistributedSampler(test_dataset, shuffle=False, drop_last=False)
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=2,
            sampler=sampler,
            pin_memory=True,
        )
        return test_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
