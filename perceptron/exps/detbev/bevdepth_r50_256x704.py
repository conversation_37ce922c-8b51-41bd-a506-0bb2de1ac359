"""
train: python3 perceptron/exps/detbev/bevdepth_r50_256x704.py -b 8 --amp --no-clearml

resutls:
mAP: 0.3219
mATE: 0.7070
mASE: 0.2766
mAOE: 0.6361
mAVE: 1.1110
mAAE: 0.3206
NDS: 0.3669
Eval time: 162.8s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.495   0.543   0.165   0.198   1.245   0.241
truck   0.262   0.723   0.219   0.218   1.214   0.281
bus     0.380   0.675   0.215   0.194   2.191   0.367
trailer 0.192   1.092   0.224   0.522   1.140   0.220
construction_vehicle    0.072   1.053   0.512   1.270   0.107   0.381
pedestrian      0.264   0.756   0.299   1.471   0.830   0.729
motorcycle      0.316   0.669   0.253   0.758   1.613   0.220
bicycle 0.298   0.543   0.258   0.907   0.549   0.126
traffic_cone    0.434   0.525   0.346   nan     nan     nan
barrier 0.506   0.491   0.274   0.188   nan     nan
"""
import torch
import torch.nn.functional as F

from torch.cuda.amp import autocast

from perceptron.engine.cli import BaseCli
from perceptron.exps.detbev.bev_det_lss_r50_256x704_128x128_24e import Exp as BaseExp
from perceptron.models.detbev.bev_depth import BevDepth


class Exp(BaseExp):
    def __init__(self, **kwargs):
        super(Exp, self).__init__(**kwargs)
        self.backbone_conf.update({"depth_net_conf": dict(in_channels=512, mid_channels=256), "use_da": True})
        self.model_class = BevDepth
        self.model_use_ema = True

        # ----------- for depth loss training ------------------ #
        self.data_return_depth = True
        self.downsample_factor = self.backbone_conf["downsample_factor"]
        self.dbound = self.backbone_conf["d_bound"]
        self.depth_channels = int((self.dbound[1] - self.dbound[0]) / self.dbound[2])

    def training_step(self, batch):
        (sweep_imgs, mats, _, _, gt_boxes, gt_labels, depth_labels) = batch
        if torch.cuda.is_available():
            for key, value in mats.items():
                mats[key] = value.cuda()
            sweep_imgs = sweep_imgs.cuda()
            gt_boxes = [gt_box.cuda() for gt_box in gt_boxes]
            gt_labels = [gt_label.cuda() for gt_label in gt_labels]
        preds, depth_preds = self.model(sweep_imgs, mats)
        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            targets = self.model.module.get_targets(gt_boxes, gt_labels)
            loss = self.model.module.loss(targets, preds)
        else:
            targets = self.model.get_targets(gt_boxes, gt_labels)
            loss = self.model.loss(targets, preds)

        if len(depth_labels.shape) == 5:
            # only key-frame will calculate depth loss
            depth_labels = depth_labels[:, 0, ...]
        depth_loss = self.get_depth_loss(depth_labels.cuda(), depth_preds)

        return loss + depth_loss

    def get_depth_loss(self, depth_labels, depth_preds):
        depth_labels = self._get_downsampled_gt_depth(depth_labels)
        depth_preds = depth_preds.permute(0, 2, 3, 1).contiguous().view(-1, self.depth_channels)
        fg_mask = torch.max(depth_labels, dim=1).values > 0.0

        with autocast(enabled=False):
            depth_loss = (
                F.binary_cross_entropy(
                    depth_preds[fg_mask],
                    depth_labels[fg_mask],
                    reduction="none",
                ).sum()
                / max(1.0, fg_mask.sum())
            )

        return 3.0 * depth_loss

    def _get_downsampled_gt_depth(self, gt_depths):
        """
        Input:
            gt_depths: [B, N, H, W]
        Output:
            gt_depths: [B*N*h*w, d]
        """
        B, N, H, W = gt_depths.shape
        gt_depths = gt_depths.view(
            B * N,
            H // self.downsample_factor,
            self.downsample_factor,
            W // self.downsample_factor,
            self.downsample_factor,
            1,
        )
        gt_depths = gt_depths.permute(0, 1, 3, 5, 2, 4).contiguous()
        gt_depths = gt_depths.view(-1, self.downsample_factor * self.downsample_factor)
        gt_depths_tmp = torch.where(gt_depths == 0.0, 1e5 * torch.ones_like(gt_depths), gt_depths)
        gt_depths = torch.min(gt_depths_tmp, dim=-1).values
        gt_depths = gt_depths.view(B * N, H // self.downsample_factor, W // self.downsample_factor)

        gt_depths = (gt_depths - (self.dbound[0] - self.dbound[2])) / self.dbound[2]
        gt_depths = torch.where(
            (gt_depths < self.depth_channels + 1) & (gt_depths >= 0.0), gt_depths, torch.zeros_like(gt_depths)
        )
        gt_depths = F.one_hot(gt_depths.long(), num_classes=self.depth_channels + 1).view(-1, self.depth_channels + 1)[
            :, 1:
        ]

        return gt_depths.float()


if __name__ == "__main__":
    BaseCli(Exp).run()
