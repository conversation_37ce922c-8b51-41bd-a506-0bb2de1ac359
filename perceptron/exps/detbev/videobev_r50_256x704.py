import numpy as np
import random
import torch
import torch.nn.functional as F
import torch.nn as nn
from torch.utils.data import DistributedSampler
from mmcv.runner import force_fp32

from perceptron.data.detbev.dataset.videobev_nuscenes import NuscVideoBevDetData, collate_fn
from perceptron.engine.cli import BaseCli
from perceptron.exps.detbev.bevdepth_r50_256x704 import Exp as BaseExp
from perceptron.models.detbev.bev_det import BevDet as BaseBEVDet
from perceptron.layers.blocks_3d.mmdet3d.bevdepth_lss_fpn import BEVDepthLSSFPN as BaseLSSFPN
from perceptron.layers.head.mmdet3d.bevdet_head import BevDetHead
from perceptron_ops.voxel_pooling import voxel_pooling
from perceptron.utils import torch_dist as dist


class LSSFPN(BaseLSSFPN):
    def __init__(self, bev_fusion_conf=None, min_queue_len=5, **kwargs):
        super().__init__(**kwargs)
        self._ref_xy = None
        self.bev_fusion_model = Fusion(**bev_fusion_conf)
        self.min_queue_len = min_queue_len

    def _forward_single_sweep(self, sweep_index, sweep_imgs, mats_dict, is_return_depth=False):
        batch_size, num_sweeps, num_cams, num_channels, img_height, img_width = sweep_imgs.shape
        img_feats = self.get_cam_feats(sweep_imgs)
        source_features = img_feats[:, 0, ...]
        depth_feature = self._forward_depth_net(
            source_features.reshape(
                batch_size * num_cams, source_features.shape[2], source_features.shape[3], source_features.shape[4]
            ),
            mats_dict,
            sweep_index,
        )
        depth = depth_feature[:, : self.depth_channels].softmax(1)
        img_feat_with_depth = depth.unsqueeze(1) * depth_feature[
            :, self.depth_channels : (self.depth_channels + self.output_channels)
        ].unsqueeze(2)

        img_feat_with_depth = self._forward_voxel_net(img_feat_with_depth)

        img_feat_with_depth = img_feat_with_depth.reshape(
            batch_size,
            num_cams,
            img_feat_with_depth.shape[1],
            img_feat_with_depth.shape[2],
            img_feat_with_depth.shape[3],
            img_feat_with_depth.shape[4],
        )
        geom_xyz = self.get_geometry(
            mats_dict["sensor2sweepego_mats"][:, sweep_index, ...],
            mats_dict["intrin_mats"][:, sweep_index, ...],
            mats_dict["ida_mats"][:, sweep_index, ...],
            None,
        )
        img_feat_with_depth = img_feat_with_depth.permute(0, 1, 3, 4, 5, 2)
        geom_xyz = ((geom_xyz - (self.voxel_coord - self.voxel_size / 2.0)) / self.voxel_size).int()
        feature_map = voxel_pooling(geom_xyz, img_feat_with_depth.contiguous(), self.voxel_num.cuda())
        if is_return_depth:
            return feature_map.contiguous(), depth
        return feature_map.contiguous()

    def ref_xy(self, H, W, device="cuda", dtype=torch.float):
        """the 2d reference points.
        Args:
            H, W: spatial shape of bev.
            evice (obj:`device`): The device where
                reference_points should be.
        Returns:
            Tensor: reference points.
        """
        if self._ref_xy is not None:
            return self._ref_xy

        ref_y, ref_x = torch.meshgrid(
            torch.range(0, H - 1, dtype=dtype, device=device), torch.range(0, W - 1, dtype=dtype, device=device)
        )
        ref_xy = torch.stack((ref_x, ref_y), -1)
        self._ref_xy = ref_xy
        return ref_xy

    @force_fp32(apply_to=("bev_feat", "cur_ego2global", "last_ego2global", "bda_mat"))
    def bev_transform(self, bev_feat, cur_ego2global, last_ego2global, bda_mat=None):
        bs, c, h, w = bev_feat.shape

        ref_xy = self.ref_xy(h, w).clone().unsqueeze(0).repeat(bs, 1, 1, 1)  # 1 x 128 x 128 x 2
        ref_xy = ref_xy * self.voxel_size[:2] + self.voxel_coord[:2]
        ref_4d = torch.cat(
            [
                ref_xy,
                ref_xy.new_tensor(np.ones(ref_xy.shape[:-1] + (1,)), device=ref_xy.device),
                ref_xy.new_tensor(np.ones(ref_xy.shape[:-1] + (1,)), device=ref_xy.device),
            ],
            -1,
        )  # 1 x 128 x 128 x 4
        if bda_mat is not None:
            curego2lastego = bda_mat.inverse()
        else:
            last_ego2global = torch.mean(last_ego2global, 1)
            cur_ego2global = torch.mean(cur_ego2global, 1)
            curego2lastego = last_ego2global.inverse() @ cur_ego2global
        curego2lastego = curego2lastego.unsqueeze(1).unsqueeze(1)
        pix_coords = curego2lastego @ ref_4d.unsqueeze(-1)
        pix_coords_xy = pix_coords.squeeze(-1)[..., :2]
        pix_coords_xy = (pix_coords_xy - self.voxel_coord[:2]) / self.voxel_size[:2]

        pix_coords_xy[..., 0] /= w - 1
        pix_coords_xy[..., 1] /= h - 1
        pix_coords_xy = (pix_coords_xy - 0.5) * 2

        return F.grid_sample(
            bev_feat,
            pix_coords_xy,
        )

    def forward(
        self, sweep_imgs, mats_dict, prev_bev=None, prev_mats_dict=None, timestamps=None, is_return_depth=False
    ):
        """Forward function.

        Args:
            sweep_imgs(Tensor): Input images with shape of (B, num_sweeps, num_cameras, 3, H, W).
            mats_dict(dict):
                sensor2ego_mats(Tensor): Transformation matrix from camera to ego with shape of (B, num_sweeps, num_cameras, 4, 4).
                intrin_mats(Tensor): Intrinsic matrix with shape of (B, num_sweeps, num_cameras, 4, 4).
                ida_mats(Tensor): Transformation matrix for ida with shape of (B, num_sweeps, num_cameras, 4, 4).
                sensor2sensor_mats(Tensor): Transformation matrix from key frame camera to sweep frame camera with shape of (B, num_sweeps, num_cameras, 4, 4).
                bda_mat(Tensor): Rotation matrix for bda with shape of (B, 4, 4).
            prev_bev: the bev feature of previous frame
            prev_ego2global_mats: the ego to global matrix of last frame
            timestamps(Tensor): Timestamp for all images with the shape of(B, num_sweeps, num_cameras).
        Return:
            Tensor: bev feature map.
        """
        batch_size, num_sweeps, num_cams, num_channels, img_height, img_width = sweep_imgs.shape

        key_frame_res = self._forward_single_sweep(
            0, sweep_imgs[:, 0:1, ...], mats_dict, is_return_depth=is_return_depth
        )

        key_frame_feature = key_frame_res[0] if is_return_depth else key_frame_res

        ret_feature_list = [key_frame_feature]
        for sweep_index in range(1, num_sweeps):
            with torch.no_grad():
                feature_map = self._forward_single_sweep(
                    sweep_index, sweep_imgs[:, sweep_index : sweep_index + 1, ...], mats_dict, is_return_depth=False
                )
                ret_feature_list.append(feature_map)

        # bev sequence fusion
        if self.training and num_sweeps > self.min_queue_len:
            queue_len = random.randint(self.min_queue_len, num_sweeps)
        else:
            queue_len = num_sweeps
        if prev_bev is None:
            shape = ret_feature_list[-1].shape
            bev_feature = ret_feature_list[queue_len - 1].new_tensor(
                torch.zeros(shape[0], shape[1], shape[2], shape[3]),
                dtype=ret_feature_list[-1].dtype,
                device=ret_feature_list[-1].device,
            )
        else:
            bev_feature = self.bev_transform(
                prev_bev.float(),
                mats_dict["ego2global_mats"][:, -1].float(),
                prev_mats_dict["ego2global_mats"][:, 0].float(),
                None,
            )
        for idx in range(queue_len):
            if idx > 0:
                bev_feature = self.bev_transform(
                    bev_feature.float(),
                    mats_dict["ego2global_mats"][:, queue_len - 1 - idx].float(),
                    mats_dict["ego2global_mats"][:, queue_len - idx].float(),
                    None,
                )
            bev_feature = self.bev_fusion_model(bev_feature, ret_feature_list[queue_len - 1 - idx])
        # bev augmentation
        if self.training:
            bev_feature = self.bev_transform(bev_feature.float(), None, None, mats_dict.get("bda_mat", None).float())
        if is_return_depth:
            return bev_feature, key_frame_res[1]
        else:
            return bev_feature


class Fusion(nn.Module):
    def __init__(self, in_channel=80, out_channel=80):
        super(Fusion, self).__init__()
        self.conv = nn.Conv2d(in_channel + out_channel, out_channel, kernel_size=1, stride=1)

    def forward(self, history_bev, cur_bev):
        fused_bev = torch.cat([history_bev, cur_bev], 1)
        fused_bev = self.conv(fused_bev)
        return fused_bev


class BEVDet(BaseBEVDet):
    def __init__(self, backbone_conf, head_conf, is_train_depth=True):
        super(BaseBEVDet, self).__init__()
        self.backbone = LSSFPN(**backbone_conf)
        self.head = BevDetHead(**head_conf)
        self.is_train_depth = is_train_depth

    def forward(
        self,
        x,
        mats,
        prev_bev=None,
        prev_mats_dict=None,
        timestamps=None,
    ):
        if self.is_train_depth and self.training:
            x, depth_pred = self.backbone(x, mats, timestamps=timestamps, is_return_depth=True)
            preds = self.head(x)
            return preds, depth_pred
        else:
            x = self.backbone(x, mats, prev_bev, prev_mats_dict, timestamps=timestamps)
            preds = self.head(x)
            return preds, x


class Exp(BaseExp):
    def __init__(self, **kwargs):
        super(Exp, self).__init__(**kwargs)
        self.key_idxes = list(range(-1, -8, -1))
        self.head_conf["train_cfg"]["code_weights"] = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
        self.backbone_conf.update({"bev_fusion_conf": dict(in_channel=80, out_channel=80), "min_queue_len": 5})
        self.model_class = BEVDet
        # for video inference
        self.prev_scene = -1
        self.prev_bev = None
        self.prev_mats_dict = None
        self.video_test_mode = False

    def test_step(self, batch):
        (sweep_imgs, mats, _, img_metas, _, _) = batch
        if torch.cuda.is_available():
            for key, value in mats.items():
                mats[key] = value.cuda()
            sweep_imgs = sweep_imgs.cuda()

        if self.prev_scene == img_metas[0]["scene_token"] and self.video_test_mode:
            prev_bev = self.prev_bev
            prev_mats_dict = self.prev_mats_dict
        else:
            prev_bev = None
            prev_mats_dict = None

        preds, bev_feat = self.model(sweep_imgs, mats, prev_bev, prev_mats_dict)

        self.prev_scene = img_metas[0]["scene_token"]
        self.prev_bev = bev_feat.detach()
        self.prev_mats_dict = {k: v.detach() for k, v in mats.items()}

        if isinstance(self.model, torch.nn.parallel.DistributedDataParallel):
            results = self.model.module.get_bboxes(preds, img_metas)
        else:
            results = self.model.get_bboxes(preds, img_metas)
        for i in range(len(results)):
            results[i][0] = results[i][0].detach().cpu().numpy()
            results[i][1] = results[i][1].detach().cpu().numpy()
            results[i][2] = results[i][2].detach().cpu().numpy()
        return results

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_data = NuscVideoBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            use_cbgs=self.data_use_cbgs,
            img_conf=self.img_conf,
            num_sweeps=self.num_sweeps,
            sweeps_idx=self.sweeps_idx,
            key_idxes=self.key_idxes,
            return_depth=self.data_return_depth,
        )
        from functools import partial

        train_loader = torch.utils.data.DataLoader(
            train_data,
            batch_size=self.batch_size_per_device,
            num_workers=4,
            drop_last=True,
            shuffle=False,
            collate_fn=partial(collate_fn, is_return_depth=self.data_return_depth),
            sampler=InfiniteSampler(len(train_data), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
        )
        return train_loader

    def _configure_val_dataloader(self):
        if self.video_test_mode:
            self.key_idxes = []

        val_data = NuscVideoBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            img_conf=self.img_conf,
            data_split="validation_12hz",
            num_sweeps=self.num_sweeps,
            key_idxes=self.key_idxes,
            sweeps_idx=self.sweeps_idx,
        )
        sampler = DistributedSampler(val_data, shuffle=False, drop_last=False) if dist.is_distributed() else None
        val_loader = torch.utils.data.DataLoader(
            val_data,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=4,
            sampler=sampler,
        )
        return val_loader

    def _configure_test_dataloader(self):
        test_data = NuscVideoBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            img_conf=self.img_conf,
            data_split="testing_12hz",
            num_sweeps=self.num_sweeps,
            key_idxes=self.key_idxes,
            sweeps_idx=self.sweeps_idx,
        )
        sampler = DistributedSampler(test_data, shuffle=False, drop_last=False) if dist.is_distributed() else None
        test_loader = torch.utils.data.DataLoader(
            test_data,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=4,
            sampler=sampler,
        )
        return test_loader


if __name__ == "__main__":
    BaseCli(Exp).run()
