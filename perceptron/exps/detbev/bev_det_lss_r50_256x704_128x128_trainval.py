"""
train:  DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 4 --cpu=32 --gpu=8 --memory=204800 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_trainval.py --no-clearml --sync_bn 4 -b 2 --amp

val: rlaunch --cpu=8 --gpu=2 --memory=20480 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_trainval.py --ckpt [CKPT_PATH] --eval -b 2

test: rlaunch --cpu=8 --gpu=2 --memory=20480 -- python perceptron/exps/detbev/bev_det_lss_r50_256x704_128x128_trainval.py --ckpt [CKPT_PATH] --eval -b 2 --exp_options mode=test

mAP: 0.5833
mATE: 0.5014
mASE: 0.2384
mAOE: 0.4862
mAVE: 0.8520
mAAE: 0.3344
NDS: 0.5504
Eval time: 167.2s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.552   0.510   0.161   0.202   1.438   0.347
truck   0.571   0.479   0.190   0.187   1.009   0.323
bus     0.775   0.361   0.163   0.167   1.322   0.394
trailer 0.683   0.404   0.176   0.306   0.368   0.178
construction_vehicle    0.401   0.681   0.395   0.951   0.112   0.300
pedestrian      0.289   0.748   0.293   1.387   0.787   0.682
motorcycle      0.669   0.464   0.217   0.480   1.311   0.368
bicycle 0.734   0.379   0.233   0.506   0.471   0.084
traffic_cone    0.476   0.532   0.317   nan     nan     nan
barrier 0.682   0.455   0.240   0.189   nan     nan

"""
import torch

from perceptron.utils import torch_dist as dist
from perceptron.data.detbev.dataset.nuscenes import NuscBevDetData, collate_fn
from perceptron.engine.cli import BaseCli
from perceptron.exps.detbev.bev_det_lss_r50_256x704_128x128_24e import Exp


class Exp(Exp):
    def __init__(self, batch_size_per_device=8, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(
            batch_size_per_device=batch_size_per_device, total_devices=total_devices, max_epoch=max_epoch
        )

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        train_data = NuscBevDetData(
            ida_aug_conf=self.ida_aug_conf,
            bda_aug_conf=self.bda_aug_conf,
            classes=self.class_names,
            use_cbgs=False,
            img_conf=self.img_conf,
            data_split="trainval_12hz",
        )
        train_loader = torch.utils.data.DataLoader(
            train_data,
            batch_size=self.batch_size_per_device,
            num_workers=4,
            drop_last=True,
            shuffle=False,
            collate_fn=collate_fn,
            sampler=InfiniteSampler(len(train_data), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
        )
        return train_loader


if __name__ == "__main__":
    BaseCli(Exp).run()
