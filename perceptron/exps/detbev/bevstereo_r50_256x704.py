"""
mAP: 0.3456
mATE: 0.6544
mASE: 0.2812
mAOE: 0.5162
mAVE: 0.5201
mAAE: 0.2206
NDS: 0.4535
Eval time: 166.4s

Per-class results:
Object Class    AP      ATE     ASE     AOE     AVE     AAE
car     0.512   0.516   0.164   0.178   0.567   0.232
truck   0.282   0.696   0.217   0.204   0.474   0.230
bus     0.383   0.653   0.218   0.129   1.136   0.307
trailer 0.163   0.946   0.246   0.477   0.382   0.082
construction_vehicle    0.082   0.925   0.517   1.220   0.119   0.357
pedestrian      0.353   0.719   0.298   0.987   0.600   0.331
motorcycle      0.353   0.586   0.261   0.586   0.653   0.214
bicycle 0.336   0.523   0.260   0.684   0.230   0.011
traffic_cone    0.490   0.495   0.349   nan     nan     nan
barrier 0.500   0.485   0.281   0.182   nan     nan

"""
from perceptron.engine.cli import BaseCli
from perceptron.exps.detbev.bevdepth_r50_256x704 import Exp as BaseExp
from perceptron.models.detbev.bev_stereo import BevStereo


class Exp(BaseExp):
    def __init__(self, **kwargs):
        super(Exp, self).__init__(**kwargs)
        self.backbone_conf.update({"depth_net_conf": dict(in_channels=512, mid_channels=256), "use_da": False})
        self.model_class = BevStereo
        self.model_use_ema = True
        self.key_idxes = [-1]
        self.head_conf["test_cfg"]["thresh_scale"] = [0.6, 0.4, 0.4, 0.7, 0.8, 0.9]
        self.head_conf["test_cfg"]["nms_type"] = "size_aware_circle"
        self.head_conf["bev_backbone_conf"]["in_channels"] = 160
        self.head_conf["bev_neck_conf"]["in_channels"] = [160, 160, 320, 640]


if __name__ == "__main__":
    BaseCli(Exp).run()
