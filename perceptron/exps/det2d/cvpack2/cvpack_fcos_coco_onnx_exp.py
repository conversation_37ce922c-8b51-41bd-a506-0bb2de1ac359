"""
python3 perceptron/exps/det2d/cvpack2/cvpack_fcos_coco_onnx_exp.py \
    --no-clearml --export --ckpt {your model path} \
    --env local
cvpack2_fcos_r50 model path:
    s3://basedet3d/peceptron/cvpack2_fcos_r50_model_final.pth
"""

from loguru import logger

import torch
from cvpack2.layers import cat
from cvpack2.structures import ImageList
from cvpack2.modeling.anchor_generator import ShiftGenerator
from cvpack2.modeling.box_regression import Shift2BoxTransform
from cvpack2.modeling.meta_arch.fcos import FCOS

from perceptron.engine.cli import Det2dCvpack2Cli
from perceptron.engine.executors.exports import Det2DExports
from perceptron.exps.det2d.cvpack2.cvpack_fcos_coco_exp import Exp as BaseExp


def forward_onnx_helper(model, images):
    images = images.to(model.device)
    images = model.normalizer(images)

    images = ImageList(images.contiguous(), [images.shape[2:]])

    features = model.backbone(images.tensor)
    features = [features[f] for f in model.in_features]
    box_cls, box_delta, box_center = model.head(features)
    shifts = model.shift_generator(features)

    results = model.inference(box_cls, box_delta, box_center, shifts, images)
    return results


def inference_single_image_helper(model, box_cls, box_delta, box_center, shifts, image_size):
    boxes_all = []
    scores_all = []

    # Iterate over every feature level
    for box_cls_i, box_reg_i, box_ctr_i, shifts_i in zip(box_cls, box_delta, box_center, shifts):
        # (HxWxK,)
        box_cls_i = box_cls_i.sigmoid_()

        # predict boxes
        predicted_boxes = model.shift2box_transform.apply_deltas(box_reg_i, shifts_i)

        box_ctr_i = box_ctr_i.sigmoid_()
        predicted_prob = torch.sqrt(box_cls_i * box_ctr_i)

        boxes_all.append(predicted_boxes)
        scores_all.append(predicted_prob)

    scores_all, boxes_all = [cat(x) for x in [scores_all, boxes_all]]
    return scores_all, boxes_all


class FCOSImpl(FCOS):
    def __init__(self, cfg):
        self.cfg = cfg
        self.is_onnx = False
        self.is_batched_onnx = False
        self.is_batched_post_onnx = False
        self.cfg.pixel_mean = torch.Tensor(cfg.MODEL.PIXEL_MEAN).view(3, 1, 1)
        self.cfg.pixel_std = torch.Tensor(cfg.MODEL.PIXEL_STD).view(3, 1, 1)
        super(FCOSImpl, self).__init__(cfg)

        self.shift2box_transform = Shift2BoxTransformImpl(weights=cfg.MODEL.FCOS.BBOX_REG_WEIGHTS)
        self.ign_ioa_thr = self.cfg.MODEL.get("IGN_IOA_TH", 0.5)
        self.proxy_anchor_scale = self.cfg.MODEL.FCOS.get("ANCHOR_SCALE", 8)

    def to(self, device):
        super(FCOSImpl, self).to(device)
        self.device = torch.device(device)
        self.cfg.pixel_mean = self.cfg.pixel_mean.to(device)
        self.cfg.pixel_std = self.cfg.pixel_std.to(device)

    def forward(self, *args, **kwargs):
        if self.is_onnx:
            if not isinstance(self.shift2box_transform, Shift2BoxTransformImpl):
                self.shift2box_transform = Shift2BoxTransformImpl(weights=self.cfg.MODEL.FCOS.BBOX_REG_WEIGHTS)
                logger.warning(
                    "We replace the shift2box transform temporarily when the onnx network forward. The replacement will be deprecated in the future."
                )

            if not isinstance(self.shift_generator, ShiftGeneratorImpl):
                backbone_shape = self.backbone.output_shape()
                feature_shapes = [backbone_shape[f] for f in self.in_features]
                self.shift_generator = build_shift_generator(self.cfg, feature_shapes)
                logger.warning(
                    "We replace the shift generator temporarily when the onnx network forward. The replacement will be deprecated in the future."
                )
            return forward_onnx_helper(self, *args, **kwargs)
        else:
            return super(FCOSImpl, self).forward(*args, **kwargs)

    def inference_single_image(self, *args, **kwargs):
        if self.is_onnx:
            return inference_single_image_helper(self, *args, **kwargs)
        else:
            return super(FCOSImpl, self).inference_single_image(*args, **kwargs)

    def normalizer(self, x):
        if len(x.shape) == 3:
            return (x - self.cfg.pixel_mean) / self.cfg.pixel_std
        elif len(x.shape) == 4:
            return (x - self.cfg.pixel_mean.view(1, 3, 1, 1)) / self.cfg.pixel_std.view(1, 3, 1, 1)
        else:
            raise NotImplementedError

    def set_nms_threshold(self, nms_threshold):
        if self.is_onnx:
            raise AttributeError("ONNX version of FCOS does not support set nms theshold")
        self.nms_threshold = nms_threshold


class Shift2BoxTransformImpl(Shift2BoxTransform):
    def apply_deltas(self, deltas, shifts):
        """
        Apply transformation `deltas` (dl, dt, dr, db) to `shifts`.

        Args:
            deltas (Tensor): transformation deltas of shape (N, k*4), where k >= 1.
                deltas[i] represents k potentially different class-specific
                box transformations for the single shift shifts[i].
            shifts (Tensor): shifts to transform, of shape (N, 2)
        """
        # assert torch.isfinite(deltas).all().item()
        shifts = shifts.to(deltas.dtype)

        if deltas.numel() == 0:
            return torch.empty_like(deltas)

        deltas = deltas / shifts.new_tensor(self.weights)
        boxes = torch.cat((shifts - deltas[..., :2], shifts + deltas[..., 2:]), dim=-1)
        return boxes


def build_shift_generator(cfg, input_shape):
    return ShiftGeneratorImpl(cfg, input_shape)


def _create_grid_offsets(size, stride, offset, device):
    grid_height, grid_width = size
    shifts_start = int(offset * stride)
    shifts_x = torch.arange(
        shifts_start,
        grid_width * stride + shifts_start,
        step=stride,
        dtype=torch.int32,
        device=device,
    )
    shifts_y = torch.arange(
        shifts_start,
        grid_height * stride + shifts_start,
        step=stride,
        dtype=torch.int32,
        device=device,
    )
    shift_y, shift_x = torch.meshgrid(shifts_y, shifts_x)
    shift_x = shift_x.reshape(-1)
    shift_y = shift_y.reshape(-1)
    return shift_x, shift_y


class ShiftGeneratorImpl(ShiftGenerator):
    """
    For a set of image sizes and feature maps, computes a set of shifts.
    """

    def grid_shifts(self, grid_sizes, device):
        shifts_over_all = []
        for size, stride in zip(grid_sizes, self.strides):
            shift_x, shift_y = _create_grid_offsets(size, stride, self.offset, device)
            shifts = torch.stack((shift_x, shift_y), dim=1)

            shifts_over_all.append(shifts)

        return shifts_over_all

    def forward(self, features):
        """
        Args:
            features (list[Tensor]): list of backbone feature maps on which to generate shifts.

        Returns:
            list[list[Tensor]]: a list of #image elements. Each is a list of #feature level tensors.
                The tensors contains shifts of this image on the specific feature level.
        """
        num_images = len(features[0])
        grid_sizes = [feature_map.shape[-2:] for feature_map in features]
        shifts_over_all = self.grid_shifts(grid_sizes, features[0].device)

        shifts = [shifts_over_all for _ in range(num_images)]
        return shifts


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.export_executor_class = Det2DExports

        # ------------------------------------- config dataset --------------------------------------------- #

    def _configure_model(self):
        cfg = self._configure_config()
        cfg.build_shift_generator = build_shift_generator
        cfg.MODEL.DEVICE = "cpu"
        model = FCOSImpl(cfg)
        model.is_onnx = True
        return model

    def export_onnx(self, output_path):
        """transfer torch model to onnx model.
        Args:
            model: traced model.
            output_path (str): export path of onnx model.
        """
        dynamic_axes = {
            "scores": {0: "num_boxes"},
            "boxes": {0: "num_boxes"},
            "images": {0: "batch", 1: "channel", 2: "height", 3: "width"},
        }
        inputs = torch.randn(1, 3, 960, 960)

        with open(output_path, "wb") as f:
            torch.onnx.export(
                self.model,
                inputs,
                f,
                opset_version=11,
                do_constant_folding=True,
                input_names=["images"],
                output_names=["scores", "boxes"],
                dynamic_axes=dynamic_axes,
            )


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
