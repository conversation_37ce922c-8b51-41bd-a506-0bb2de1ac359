import contextlib
import copy
import io
from loguru import logger

import numpy as np
import nori2 as nori
from types import SimpleNamespace

import torch

from cvpack2.structures import BoxMode
from cvpack2.data.detection_utils import (
    imdecode,
    check_image_size,
    annotations_to_instances,
    filter_empty_instances,
    create_keypoint_hflip_indices,
)
from cvpack2.data.base_dataset import BaseDataset
from cvpack2.data.datasets import builtin_meta
from cvpack2.data.samplers import InferenceSampler, DistributedGroupSampler
from cvpack2.data.samplers import Infinite as C2Infinite
from cvpack2.data.build import build_transform_gens, trivial_batch_collator, worker_init_reset_seed
from cvpack2.solver.build import build_lr_scheduler, build_optimizer
from cvpack2.configs.base_detection_config import BaseDetectionConfig

from data3d.datasets.coco import COCODataset as coco_api
from perceptron.engine.executors.evaluators import Det2DEvaluator
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.det2d.coco_eval import COCOEvaluator
from perceptron.engine.cli import Det2dCvpack2Cli
from perceptron.utils import torch_dist as dist


_NORI_FETCHER = None


# ------------------------------------- sample --------------------------------------------- #
class Infinite(C2Infinite):
    def __init__(self, sampler, sample_len):
        super().__init__(sampler)
        self.sampler_len = sample_len

    def __len__(self):
        return self.sampler_len


# ------------------------------------- dataset --------------------------------------------- #
class COCODataset(BaseDataset):
    def __init__(self, cfg, transforms=None, data_split="training"):
        is_train = data_split == "training"
        with contextlib.redirect_stdout(io.StringIO()):
            self.coco_api = coco_api(data_split=data_split)
        self.img_ids = sorted(self.coco_api.imgs.keys())
        dataset_name = "coco_2017_train" if is_train else "coco_2017_val"
        super(COCODataset, self).__init__(cfg, dataset_name, transforms, is_train)

        self.task_key = "coco"
        self.meta = self._get_metadata()

        self.dataset_dicts = self._load_annotations()

        # fmt: off
        self.data_format = cfg.INPUT.FORMAT
        self.mask_on = cfg.MODEL.MASK_ON
        self.mask_format = cfg.INPUT.MASK_FORMAT
        self.filter_empty = cfg.DATALOADER.FILTER_EMPTY_ANNOTATIONS
        self.keypoint_on = cfg.MODEL.KEYPOINT_ON
        self.load_proposals = cfg.MODEL.LOAD_PROPOSALS
        self.proposal_files = cfg.DATASETS.PROPOSAL_FILES_TRAIN
        # fmt: on

        if is_train:
            # Remove images without instance-level GT even though the dataset has semantic labels.
            self.dataset_dicts = self._filter_annotations(
                filter_empty=self.filter_empty,
                min_keypoints=cfg.MODEL.ROI_KEYPOINT_HEAD.MIN_KEYPOINTS_PER_IMAGE if self.keypoint_on else 0,
                proposal_files=self.proposal_files if self.load_proposals else None,
            )
            self._set_group_flag()
        else:
            self.meta = SimpleNamespace(**self.meta)

        self.eval_with_gt = cfg.TEST.get("WITH_GT", False)

        if self.keypoint_on:
            # Flip only makes sense in training
            self.keypoint_hflip_indices = create_keypoint_hflip_indices(cfg.DATASETS.TRAIN, self.meta)
        else:
            self.keypoint_hflip_indices = None

    def __getitem__(self, index):
        """Load data, apply transforms, converto to Instances."""
        dataset_dict = copy.deepcopy(self.dataset_dicts[index])

        # read image
        image = read_image_from_nori(dataset_dict["nori_id"], format=self.data_format)
        check_image_size(dataset_dict, image)

        if "annotations" in dataset_dict:
            annotations = dataset_dict.pop("annotations")
            annotations = [ann for ann in annotations if ann.get("iscrowd", 0) == 0]
        else:
            annotations = None

        # apply transfrom
        image, annotations = self._apply_transforms(
            image, annotations, keypoint_hflip_indices=self.keypoint_hflip_indices
        )

        if annotations is not None:  # got instances in annotations
            image_shape = image.shape[:2]  # h, w

            instances = annotations_to_instances(annotations, image_shape, mask_format=self.mask_format)

            # Create a tight bounding box from masks, useful when image is cropped
            # if self.crop_gen and instances.has("gt_masks"):
            #     instances.gt_boxes = instances.gt_masks.get_bounding_boxes()

            dataset_dict["instances"] = filter_empty_instances(instances)

        # h, w, c -> c, h, w
        dataset_dict["image"] = torch.as_tensor(np.ascontiguousarray(image.transpose(2, 0, 1)))

        return dataset_dict

    def __len__(self):
        return len(self.dataset_dicts)

    def _get_metadata(self):
        meta = builtin_meta._get_builtin_metadata(self.task_key)
        meta["json_file"] = self.coco_api.json_file
        meta["evaluator_type"] = "coco"
        return meta

    # Convert COCO dataset to cvpack2 data format
    def _load_annotations(self, extra_annotation_keys=None):
        id_map = self.meta["thing_dataset_id_to_contiguous_id"]

        imgs = self.coco_api.loadImgs(self.img_ids)
        anns = [self.coco_api.imgToAnns[img_id] for img_id in self.img_ids]

        imgs_anns = list(zip(imgs, anns))
        logger.info("Loaded {} images in COCO format from {}".format(len(imgs_anns), "OSS"))

        dataset_dicts = []
        ann_keys = ["iscrowd", "bbox", "keypoints", "category_id"] + (extra_annotation_keys or [])

        for (img_dict, anno_dict_list) in imgs_anns:
            record = {}
            record["nori_id"] = img_dict["nori_id"]
            record["height"] = img_dict["height"]
            record["width"] = img_dict["width"]
            image_id = record["image_id"] = img_dict["id"]

            objs = []
            for anno in anno_dict_list:
                assert anno["image_id"] == image_id
                assert anno.get("ignore", 0) == 0

                obj = {key: anno[key] for key in ann_keys if key in anno}

                obj["bbox_mode"] = BoxMode.XYWH_ABS
                if id_map:
                    obj["category_id"] = id_map[obj["category_id"]]
                objs.append(obj)
            record["annotations"] = objs
            dataset_dicts.append(record)

        return dataset_dicts


def read_image_from_nori(nori_id, format=None):
    """
    Read an image into the given format.
    Will apply rotation and flipping if the image has such exif information.

    Args:
        nori_id (str): image nori id
        format (str): one of the supported image modes in PIL, or "BGR" or "YUV-BT.601".

    Returns:
        image (np.ndarray): an HWC image in the given format, which is 0-255, uint8 for
            supported image modes in PIL or "BGR"; float (0-1 for Y) for YUV-BT.601.
    """
    global _NORI_FETCHER
    if _NORI_FETCHER is None:
        _NORI_FETCHER = nori.Fetcher()

    image = imdecode(_NORI_FETCHER.get(nori_id))[..., :3]

    assert format in ["RGB", "BGR"], "Format {} not supported".format(format)
    if format == "RGB":
        # RGB to BGR, use step -1 slice is much faster than [2, 1, 0]
        image = image[..., ::-1]
    return image


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        self.cfg = None
        self.config_class = BaseDetectionConfig
        self.eval_tasks = ("bbox",)
        self.dump_interval = 1
        self.num_classes = 80
        self.eval_executor_class = Det2DEvaluator

    def _configure_config(self):
        if self.cfg is None:
            cfg = self.config_class()
            self.cfg = cfg
        return self.cfg

    def _configure_model(self):
        pass

    def _configure_train_dataloader(self):
        cfg = self._configure_config()

        # use subdivision batchsize
        images_per_minibatch = cfg.SOLVER.IMS_PER_DEVICE // cfg.SOLVER.BATCH_SUBDIVISIONS

        transform_gens = build_transform_gens(cfg.INPUT.AUG.TRAIN_PIPELINES)
        train_set = COCODataset(cfg, transforms=transform_gens, data_split="training")
        sampler = DistributedGroupSampler(train_set, images_per_minibatch) if dist.is_distributed() else None

        if cfg.DATALOADER.ENABLE_INF_SAMPLER:
            # Convert train_iters to train_epoch, 1 epoch = checkpoint_period iters
            sampler = Infinite(sampler, cfg.SOLVER.CHECKPOINT_PERIOD * images_per_minibatch)

        train_loader = torch.utils.data.DataLoader(
            train_set,
            batch_size=images_per_minibatch,
            sampler=sampler,
            num_workers=cfg.DATALOADER.NUM_WORKERS,
            collate_fn=trivial_batch_collator,
            worker_init_fn=worker_init_reset_seed,
        )
        return train_loader

    def _configure_val_dataloader(self):
        cfg = self._configure_config()
        transform_gens = build_transform_gens(cfg.INPUT.AUG.TEST_PIPELINES)

        val_set = COCODataset(cfg, transforms=transform_gens, data_split="validation")
        sampler = InferenceSampler(len(val_set))
        batch_sampler = torch.utils.data.sampler.BatchSampler(sampler, 1, drop_last=False)

        val_loader = torch.utils.data.DataLoader(
            val_set,
            num_workers=2,
            batch_sampler=batch_sampler,
            collate_fn=trivial_batch_collator,
            pin_memory=True,
        )
        self.evaluator = COCOEvaluator(val_set, distributed=dist.is_distributed(), tasks=self.eval_tasks)
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        optimizer = build_optimizer(self._configure_config(), self.model)
        return optimizer

    def _configure_lr_scheduler(self, **kwargs):
        lr_scheduler = build_lr_scheduler(self._configure_config(), self.optimizer, **kwargs)
        return lr_scheduler


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
