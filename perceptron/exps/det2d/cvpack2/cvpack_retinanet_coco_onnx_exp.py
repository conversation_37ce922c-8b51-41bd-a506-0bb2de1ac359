import torch
from cvpack2.layers import cat
from cvpack2.structures import Boxes, ImageList
from cvpack2.modeling.anchor_generator import <PERSON><PERSON>ultAnchorGenerator
from cvpack2.modeling.meta_arch import RetinaNet

from perceptron.engine.cli import Det2dCvpack2Cli
from perceptron.engine.executors.exports import Det2DExports
from perceptron.exps.det2d.cvpack2.cvpack_retinanet_coco_exp import Exp as BaseExp


def forward_onnx_helper(model, images):
    images = images.to(model.device)
    images = model.normalizer(images)

    images = ImageList(images.contiguous(), [images.shape[2:]])

    features = model.backbone(images.tensor)
    features = [features[f] for f in model.in_features]
    box_cls, box_delta = model.head(features)
    anchors = model.anchor_generator(features)

    results = model.inference(box_cls, box_delta, anchors, images.image_sizes)
    return results


def inference_single_image_helper(model, box_cls, box_delta, anchors, image_size):
    boxes_all = []
    scores_all = []

    # Iterate over every feature level
    for box_cls_i, box_reg_i, anchors_i in zip(box_cls, box_delta, anchors):
        # (HxWxK,)
        predicted_prob = box_cls_i.sigmoid_()

        # # predict boxes
        predicted_boxes = model.box2box_transform.apply_deltas(box_reg_i, anchors_i.tensor)

        boxes_all.append(predicted_boxes)
        scores_all.append(predicted_prob)

    boxes_all, scores_all = [cat(x) for x in [boxes_all, scores_all]]
    return scores_all, boxes_all


# ------------------------------------- backbone --------------------------------------------- #
class RetinaNetImpl(RetinaNet):
    def __init__(self, cfg):
        self.cfg = cfg
        self.is_onnx = False
        self.cfg.pixel_mean = torch.Tensor(cfg.MODEL.PIXEL_MEAN).view(3, 1, 1)
        self.cfg.pixel_std = torch.Tensor(cfg.MODEL.PIXEL_STD).view(3, 1, 1)
        super(RetinaNetImpl, self).__init__(cfg)

    def to(self, device):
        super(RetinaNetImpl, self).to(device)
        self.device = torch.device(device)
        self.cfg.pixel_mean = self.cfg.pixel_mean.to(device)
        self.cfg.pixel_std = self.cfg.pixel_std.to(device)

    def forward(self, *args, **kwargs):
        if self.is_onnx:
            # forward without gt_instances and post-preprocess
            return forward_onnx_helper(self, *args, **kwargs)
        else:
            return super(RetinaNet, self).forward(*args, **kwargs)

    def inference_single_image(self, *args, **kwargs):
        if self.is_onnx:
            return inference_single_image_helper(self, *args, **kwargs)
        else:
            return super(RetinaNetImpl, self).inference_single_image(*args, **kwargs)

    def normalizer(self, x):
        if len(x.shape) == 3:
            return (x - self.cfg.pixel_mean) / self.cfg.pixel_std
        elif len(x.shape) == 4:
            return (x - self.cfg.pixel_mean.view(1, 3, 1, 1)) / self.cfg.pixel_std.view(1, 3, 1, 1)
        else:
            raise NotImplementedError

    def set_nms_threshold(self, nms_threshold):
        if self.is_onnx:
            raise AttributeError("ONNX version of RetinaNet does not support set nms theshold")
        self.nms_threshold = nms_threshold


class DefaultAnchorGeneratorImpl(DefaultAnchorGenerator):
    def forward(self, features):
        num_images = len(features[0])
        grid_sizes = [feature_map.shape[-2:] for feature_map in features]
        anchors_over_all_feature_maps = self.grid_anchors(grid_sizes)

        anchors_in_image = []
        for anchors_per_feature_map in anchors_over_all_feature_maps:
            boxes = Boxes(anchors_per_feature_map)
            anchors_in_image.append(boxes)

        anchors = [anchors_in_image for _ in range(num_images)]
        return anchors


def build_anchor_generator(cfg, input_shape):

    return DefaultAnchorGeneratorImpl(cfg, input_shape)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.export_executor_class = Det2DExports

        # ------------------------------------- config dataset --------------------------------------------- #

    def _configure_model(self):
        cfg = self._configure_config()
        cfg.build_anchor_generator = build_anchor_generator
        cfg.MODEL.DEVICE = "cpu"
        model = RetinaNetImpl(cfg)
        model.is_onnx = True
        return model

    def export_onnx(self, output_path):
        """transfer torch model to onnx model.
        Args:
            model: traced model.
            output_path (str): export path of onnx model.
        """
        dynamic_axes = {
            "scores": {0: "num_boxes"},
            "boxes": {0: "num_boxes"},
            "images": {0: "batch", 1: "channel", 2: "height", 3: "width"},
        }
        inputs = torch.randn(1, 3, 640, 640)
        model = self.model.to("cpu")

        with open(output_path, "wb") as f:
            torch.onnx.export(
                model,
                inputs,
                f,
                opset_version=11,
                do_constant_folding=True,
                input_names=["images"],
                output_names=["scores", "boxes"],
                dynamic_axes=dynamic_axes,
            )


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
