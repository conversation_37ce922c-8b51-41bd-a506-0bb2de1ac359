"""
rlaunch --charged-group base_det --cpu 32 --gpu 8 --memory 100000 --preemptible no -- \
    python3 perceptron/exps/det2d/cvpack2/cvpack_retinanet_coco_exp.py --no-clearml -b 2 -d 0-7 \
        --pretrained_model s3://generalDetection/cvpack2/ImageNetPretrained/MSRA/R-50.pkl

Results:
|   AP   |  AP50  |  AP75  |  APs   |  APm   |  APl   |
|:------:|:------:|:------:|:------:|:------:|:------:|
| 35.955 | 55.944 | 38.437 | 20.020 | 39.916 | 47.141 |
"""

from cvpack2.layers import ShapeSpec
from cvpack2.modeling.backbone.fpn import build_retinanet_resnet_fpn_backbone
from cvpack2.modeling.anchor_generator import DefaultAnchorGenerator
from cvpack2.configs.retinanet_config import RetinaNetConfig
from cvpack2.modeling.meta_arch import RetinaNet


from perceptron.exps.det2d.cvpack2.cvpack_base_detection_exp import Exp as BaseExp
from perceptron.engine.cli import Det2dCvpack2Cli


_config_dict = dict(
    OSS=dict(DUMP_LOG_ENABLED=False),  # not upload to OSS
    MODEL=dict(
        WEIGHTS="s3://generalDetection/cvpack2/ImageNetPretrained/MSRA/R-50.pkl",
        RESNETS=dict(DEPTH=50),
    ),
    DATASETS=dict(
        TRAIN=("coco_2017_train",),
        TEST=("coco_2017_val",),
    ),
    SOLVER=dict(
        LR_SCHEDULER=dict(
            STEPS=(60000, 80000),
            MAX_ITER=90000,
        ),
        OPTIMIZER=dict(
            BASE_LR=0.01,
        ),
        IMS_PER_BATCH=16,
        IMS_PER_DEVICE=2,
    ),
    INPUT=dict(
        AUG=dict(
            TRAIN_PIPELINES=[
                ("ResizeShortestEdge", dict(short_edge_length=(800,), max_size=1333, sample_style="choice")),
                ("RandomFlip", dict()),
            ],
            TEST_PIPELINES=[
                ("ResizeShortestEdge", dict(short_edge_length=800, max_size=1333, sample_style="choice")),
            ],
        )
    ),
)


# ------------------------------------- config  --------------------------------------------- #
class CustomRetinaNetConfig(RetinaNetConfig):
    def __init__(self):
        super(CustomRetinaNetConfig, self).__init__()
        self._register_configuration(_config_dict)
        self.build_backbone = build_backbone


# ------------------------------------- backbone --------------------------------------------- #
def build_backbone(cfg, input_shape=None):
    """
    Build a backbone from `cfg.MODEL.BACKBONE.NAME`.

    Returns:
        an instance of :class:`Backbone`
    """
    if input_shape is None:
        input_shape = ShapeSpec(channels=len(cfg.MODEL.PIXEL_MEAN))

    backbone = build_retinanet_resnet_fpn_backbone(cfg, input_shape)
    return backbone


def build_anchor_generator(cfg, input_shape):
    return DefaultAnchorGenerator(cfg, input_shape)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.config_class = CustomRetinaNetConfig
        cfg = self._configure_config()

        # Convert train_iters to train_epoch, max_epoch = max_iter // ckpt_period
        self._max_epoch = cfg.SOLVER.LR_SCHEDULER.MAX_ITER // cfg.SOLVER.CHECKPOINT_PERIOD

    def _configure_config(self):
        if self.cfg is None:
            cfg = self.config_class()
            self.cfg = cfg
        return self.cfg

    def _configure_model(self):
        cfg = self._configure_config()
        cfg.build_anchor_generator = build_anchor_generator
        model = RetinaNet(cfg)
        return model

    def training_step(self, batch):

        loss_dict = self.model(batch)
        loss = sum([metrics_value for metrics_value in loss_dict.values() if metrics_value.requires_grad])

        return loss

    def test_step(self, batch):
        outputs = self.model(batch)
        return batch, outputs


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
