# encoding: utf-8
"""
rlaunch --charged-group base_det --cpu 32 --gpu 8 --memory 150000 --preemptible no -- \
    python3 perceptron/exps/det2d/torchvision/faster_rcnn_exp.py --no-clearml -b 2 -d 0-7

Results:
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.381
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.588
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.407
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.214
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.417
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.496
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.317
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.503
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.528
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.328
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.565
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.672
"""

from typing import Any, Optional

import contextlib
import io
import torch
import torch.nn as nn
from torch.optim import SGD

from cvpack2.structures import Instances, Boxes

from torchvision.models.detection.faster_rcnn import FasterRCNN as torchvision_FasterRCNN
from torchvision.models.detection.faster_rcnn import fasterrcnn_resnet50_fpn
from torchvision.models.detection.backbone_utils import resnet_fpn_backbone
from perceptron.engine.executors.evaluators import Det2DEvaluator

from perceptron.exps.base_exp import BaseExp
from perceptron.exps.det2d.coco_eval import COCOEvaluator
from perceptron.layers.lr_scheduler import WarmupStepLRScheduler
from perceptron.engine.cli import BaseCli
from perceptron.utils import torch_dist as dist

from perceptron.exps.det2d.torchvision import ToRGB, ToTensor, ConvertCocoPolysToMask, RandomHorizontalFlip, Compose
from perceptron.exps.det2d.torchvision import COCODetection, _coco_remove_images_without_annotations


def trivial_batch_collator(batch):
    """
    A batch collator that does nothing.
    """
    return tuple(zip(*batch))


class FasterRCNN(nn.Module):
    """PyTorch Lightning implementation of `Faster R-CNN: Towards Real-Time Object Detection with Region Proposal
    Networks <https://arxiv.org/abs/1506.01497>`_.
    Paper authors: Shaoqing Ren, Kaiming He, Ross Girshick, Jian Sun
    """

    def __init__(
        self,
        num_classes: int = 91,
        backbone: Optional[str] = None,
        pretrained: bool = False,
        pretrained_backbone: bool = True,
        trainable_backbone_layers: int = 3,
        **kwargs: Any,
    ):
        super().__init__()

        self.num_classes = num_classes
        self.backbone = backbone
        if backbone is None:
            self.model = fasterrcnn_resnet50_fpn(
                num_classes=self.num_classes,
                pretrained=pretrained,
                pretrained_backbone=pretrained_backbone,
                trainable_backbone_layers=trainable_backbone_layers,
            )

        else:
            backbone_model = resnet_fpn_backbone(
                backbone, pretrained=True, trainable_layers=trainable_backbone_layers, **kwargs
            )
            self.model = torchvision_FasterRCNN(backbone_model, num_classes=num_classes, **kwargs)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=26, pretrained_backbone=True, **kwargs):
        self.basic_lr_per_img = 0.02 / 16.0
        self.pretrained_backbone = pretrained_backbone
        self.eval_tasks = ("bbox",)

        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 1
        self.eval_executor_class = Det2DEvaluator

    def _configure_model(self):
        model = FasterRCNN(pretrained_backbone=self.pretrained_backbone).model
        return model

    def training_step(self, batch):
        images, targets = batch
        images = list(img.cuda() for img in images)
        targets = [{k: v.cuda() for k, v in t.items()} for t in targets]

        # fasterrcnn takes both images and targets for training, returns
        loss_dict = self.model(images, targets)
        loss = sum(loss for loss in loss_dict.values())
        return loss

    def test_step(self, batch):
        images, targets = batch
        images = list(img.cuda() for img in images)
        inputs = [{"image_id": int(t["image_id"])} for t in targets]
        outputs = self.model(images)

        # copnvert dict to instances, in order to use COCOEvaluator of cvpack2
        for image, out in zip(images, outputs):
            image_size = image.shape[-2:]
            result = Instances(image_size)
            result.pred_boxes = Boxes(out.pop("boxes"))
            result.scores = out.pop("scores")
            result.pred_classes = out.pop("labels")
            if "masks" in out.keys():
                result.pred_masks = out.pop("masks").squeeze(dim=1) > 0.5
            out["instances"] = result

        return inputs, outputs

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        transform = Compose(
            [
                ToRGB(),
                ConvertCocoPolysToMask(),
                ToTensor(),
                RandomHorizontalFlip(0.5),
            ]
        )
        with contextlib.redirect_stdout(io.StringIO()):
            train_set = COCODetection(data_split="training", transform=transform)
        train_set = _coco_remove_images_without_annotations(train_set)

        train_dataloader_kwargs = {
            "num_workers": 4,
            "pin_memory": False,
            "batch_size": self.batch_size_per_device,
            "collate_fn": trivial_batch_collator,
            "shuffle": False,
            "drop_last": True,
            "sampler": InfiniteSampler(len(train_set), seed=self.seed if self.seed else 0)
            if dist.is_distributed()
            else None,
        }
        train_loader = torch.utils.data.DataLoader(train_set, **train_dataloader_kwargs)
        return train_loader

    def _configure_val_dataloader(self):
        transform = Compose(
            [
                ToRGB(),
                ConvertCocoPolysToMask(),
                ToTensor(),
            ]
        )
        with contextlib.redirect_stdout(io.StringIO()):
            eval_set = COCODetection(data_split="validation", transform=transform)
        val_loader = torch.utils.data.DataLoader(
            eval_set,
            batch_size=1,
            shuffle=False,
            num_workers=2,
            pin_memory=False,
            drop_last=False,
            collate_fn=trivial_batch_collator,
            sampler=torch.utils.data.distributed.DistributedSampler(eval_set) if dist.is_distributed() else None,
        )
        self.evaluator = COCOEvaluator(eval_set, distributed=dist.is_distributed(), tasks=self.eval_tasks)
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        params = [p for p in self.model.parameters() if p.requires_grad]
        lr = self.basic_lr_per_img * self.batch_size_per_device * self.total_devices
        optimizer = SGD(
            params,
            lr=lr,
            momentum=0.9,
            weight_decay=1e-4,
        )
        return optimizer

    def _configure_lr_scheduler(self):

        scheduler = WarmupStepLRScheduler(
            self.optimizer,
            self.basic_lr_per_img * self.batch_size_per_device * self.total_devices,
            len(self.train_dataloader),
            self.max_epoch,
            milestones=[19, 22],
        )
        return scheduler


if __name__ == "__main__":
    BaseCli(Exp).run()
