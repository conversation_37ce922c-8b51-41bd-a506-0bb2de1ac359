# encoding: utf-8
"""
rlaunch --charged-group base_det --cpu 32 --gpu 8 --memory 150000 --preemptible no -- \
    python3 perceptron/exps/det2d/torchvision/retinanet_exp.py --no-clearml -b 2 -d 0-7

"""

from collections import OrderedDict

import torch
from perceptron.engine.cli.base_cli import BaseCli
from perceptron.engine.executors.exports import Det2DExports
from perceptron.exps.det2d.torchvision.faster_rcnn_exp import Exp as BaseExp

from torchvision.models.detection.anchor_utils import AnchorGenerator
from torchvision.models.detection.image_list import ImageList
from torchvision.models.detection.transform import GeneralizedRCNNTransform
from torchvision.ops.feature_pyramid_network import LastLevelP6P7
from torchvision.models.detection.retinanet import RetinaNet as torchvision_RetinaNet
from torchvision.models.detection.backbone_utils import resnet_fpn_backbone


class AnchorGeneratorImpl(AnchorGenerator):
    def grid_anchors(self, grid_sizes, strides):
        anchors = []
        cell_anchors = self.cell_anchors
        assert cell_anchors is not None

        if not (len(grid_sizes) == len(strides) == len(cell_anchors)):
            raise ValueError(
                "Anchors should be Tuple[Tuple[int]] because each feature "
                "map could potentially have different sizes and aspect ratios. "
                "There needs to be a match between the number of "
                "feature maps passed and the number of sizes / aspect ratios specified."
            )

        for size, stride, base_anchors in zip(grid_sizes, strides, cell_anchors):
            grid_height, grid_width = size
            stride_height, stride_width = stride
            device = base_anchors.device

            # For output anchor, compute [x_center, y_center, x_center, y_center]
            shifts_x = torch.arange(0, grid_width, dtype=torch.int32, device=device) * stride_width
            shifts_y = torch.arange(0, grid_height, dtype=torch.int32, device=device) * stride_height
            shift_y, shift_x = torch.meshgrid(shifts_y, shifts_x)
            shift_x = shift_x.reshape(-1)
            shift_y = shift_y.reshape(-1)
            shifts = torch.stack((shift_x, shift_y, shift_x, shift_y), dim=1)

            # For every (base anchor, output anchor) pair,
            # offset each zero-centered base anchor by the center of the output anchor.
            anchors.append((shifts.view(-1, 1, 4) + base_anchors.view(1, -1, 4)).reshape(-1, 4))

        return anchors

    def forward(self, image_list: ImageList, feature_maps):
        grid_sizes = [feature_map.shape[-2:] for feature_map in feature_maps]
        image_size = image_list.tensors.shape[-2:]
        dtype, device = feature_maps[0].dtype, feature_maps[0].device
        strides = [
            [
                torch.tensor(image_size[0] // g[0], dtype=torch.int64, device=device),
                torch.tensor(image_size[1] // g[1], dtype=torch.int64, device=device),
            ]
            for g in grid_sizes
        ]
        self.set_cell_anchors(dtype, device)
        anchors_over_all_feature_maps = self.grid_anchors(grid_sizes, strides)
        anchors = []
        for _ in range(len(image_list.image_sizes)):
            anchors_in_image = [anchors_per_feature_map for anchors_per_feature_map in anchors_over_all_feature_maps]
            anchors.append(anchors_in_image)
        anchors = [torch.cat(anchors_per_image) for anchors_per_image in anchors]
        return anchors


class transformImpl(GeneralizedRCNNTransform):
    def forward(self, images, targets=None):

        images = [self.normalize(img) for img in images]

        image_sizes = [img.shape[-2:] for img in images]
        images = self._onnx_batch_images(images)
        image_sizes_list = []
        for image_size in image_sizes:
            assert len(image_size) == 2
            image_sizes_list.append((image_size[0], image_size[1]))

        image_list = ImageList(images.contiguous(), image_sizes_list)
        return image_list, targets

    def _onnx_batch_images(self, images):
        max_size = list(max(s) for s in zip(*[img.shape for img in images]))
        max_size = tuple(max_size)
        padded_imgs = []
        if len(images) == 1:
            img = images[0]
            padding = [(s1 - s2) for s1, s2 in zip(max_size, tuple(img.shape))]
            if all(x == 0 for x in padding):  # https://github.com/pytorch/pytorch/issues/31734
                padded_imgs = img.unsqueeze(0)
            else:
                padded_img = torch.nn.functional.pad(img, (0, padding[2], 0, padding[1], 0, padding[0]))
                padded_imgs = padded_img.unsqueeze(0)

        return padded_imgs


def inference_single_image_helper(model, box_cls, box_delta, anchors):
    boxes_all = []
    scores_all = []

    # Iterate over every feature level
    for box_cls_i, box_reg_i, anchors_i in zip(box_cls, box_delta, anchors):
        # (HxWxK,)
        predicted_prob = torch.sigmoid(box_cls_i)
        predicted_boxes = model.box_coder.decode_single(box_reg_i, anchors_i)

        boxes_all.append(predicted_boxes)
        scores_all.append(predicted_prob)

    scores_all = torch.cat(scores_all, dim=0)
    boxes_all = torch.cat(boxes_all, dim=0)
    return scores_all, boxes_all


def forward_onnx_helper(model, images, targets=None):
    images, targets = model.transform(images, targets)

    # get the features from the backbone
    features = model.backbone(images.tensors)
    if isinstance(features, torch.Tensor):
        features = OrderedDict([("0", features)])

    features = list(features.values())

    head_outputs = model.head(features)
    anchors = model.anchor_generator(images, features)

    # recover level sizes
    num_anchors_per_level = [x.size(2) * x.size(3) for x in features]
    HW = 0
    for v in num_anchors_per_level:
        HW += v
    HWA = head_outputs["cls_logits"].size(1)
    A = HWA // HW
    num_anchors_per_level = [hw * A for hw in num_anchors_per_level]

    # split outputs per level
    split_head_outputs = {}
    for k in head_outputs:
        split_head_outputs[k] = list(head_outputs[k].split(num_anchors_per_level, dim=1))
    split_anchors = [list(a.split(num_anchors_per_level)) for a in anchors]

    class_logits = split_head_outputs["cls_logits"]
    box_regression = split_head_outputs["bbox_regression"]

    num_images = len(images.image_sizes)

    detections = []
    for index in range(num_images):
        box_delta = [br[index] for br in box_regression]
        box_cls = [cl[index] for cl in class_logits]
        anchors = split_anchors[index]
        detection = inference_single_image_helper(model, box_cls, box_delta, anchors)
        detections.append(detection)

    return detections


class RetinaNetImpl(torchvision_RetinaNet):
    def __init__(
        self,
        backbone,
        num_classes,
        min_size=800,
        max_size=1333,
        image_mean=None,
        image_std=None,
        anchor_generator=None,
        head=None,
        proposal_matcher=None,
        score_thresh=0.05,
        nms_thresh=0.5,
        detections_per_img=300,
        fg_iou_thresh=0.5,
        bg_iou_thresh=0.4,
        topk_candidates=1000,
    ):
        self.num_classes = num_classes
        self.is_onnx = False
        super().__init__(
            backbone,
            num_classes,
            min_size=min_size,
            max_size=max_size,
            image_mean=image_mean,
            image_std=image_std,
            anchor_generator=anchor_generator,
            head=head,
            proposal_matcher=proposal_matcher,
            score_thresh=score_thresh,
            nms_thresh=nms_thresh,
            detections_per_img=detections_per_img,
            fg_iou_thresh=fg_iou_thresh,
            bg_iou_thresh=bg_iou_thresh,
            topk_candidates=topk_candidates,
        )
        image_mean = [0.485, 0.456, 0.406]
        image_std = [0.229, 0.224, 0.225]
        self.transform = transformImpl(min_size=min_size, max_size=max_size, image_mean=image_mean, image_std=image_std)
        if anchor_generator is None:
            anchor_sizes = tuple((x, int(x * 2 ** (1.0 / 3)), int(x * 2 ** (2.0 / 3))) for x in [32, 64, 128, 256, 512])
            aspect_ratios = ((0.5, 1.0, 2.0),) * len(anchor_sizes)
            anchor_generator = AnchorGeneratorImpl(anchor_sizes, aspect_ratios)
        self.anchor_generator = anchor_generator

    def forward(self, images, targets=None):
        if self.is_onnx:
            return forward_onnx_helper(self, images, targets=targets)
        else:
            return super(torchvision_RetinaNet, self).forward(images, targets)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, pretrained_backbone=True, **kwargs):
        self.basic_lr_per_img = 0.02 / 16.0

        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, pretrained_backbone)
        self.dump_interval = 1
        self.num_classes = 91
        self.export_executor_class = Det2DExports

    def _configure_model(self):
        backbone = resnet_fpn_backbone(
            "resnet50",
            pretrained=self.pretrained_backbone,
            returned_layers=[2, 3, 4],
            extra_blocks=LastLevelP6P7(256, 256),
            trainable_layers=3,
        )
        model = RetinaNetImpl(backbone, num_classes=91)
        model.is_onnx = True
        return model

    def export_onnx(self, output_path):
        """transfer torch model to onnx model.
        Args:
            model: traced model.
            output_path (str): export path of onnx model.
        """
        dynamic_axes = {
            "scores": {0: "num_boxes"},
            "boxes": {0: "num_boxes"},
            "images": {0: "batch", 1: "channel", 2: "height", 3: "width"},
        }
        inputs = torch.randn(1, 3, 800, 800)

        with open(output_path, "wb") as f:
            torch.onnx.export(
                self.model,
                inputs,
                f,
                opset_version=11,
                do_constant_folding=True,
                input_names=["images"],
                output_names=["scores", "boxes"],
                dynamic_axes=dynamic_axes,
            )


if __name__ == "__main__":
    BaseCli(Exp).run()
