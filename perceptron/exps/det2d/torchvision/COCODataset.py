import io
import nori2 as nori
from PIL import Image

from types import SimpleNamespace
import torch
from data3d.datasets.coco import COCODataset


class COCODetection(COCODataset):
    def __init__(self, data_split="training", transform=None):
        super().__init__(data_split=data_split)
        self.transform = transform

        if data_split != "training":
            meta = self._get_metadata()
            self.meta = SimpleNamespace(**meta)

    def __getitem__(self, idx):
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        img_id = self.sorted_imgs[idx]
        nori_id = self.loadImgs(img_id)[-1]["nori_id"]
        image = Image.open(io.BytesIO(self.nori_fetcher.get(nori_id)))
        targets = self.sorted_anns[idx]
        targets = dict(image_id=img_id, annotations=targets)
        if self.transform is not None:
            image, targets = self.transform(image, targets)
        return image, targets

    def _get_metadata(self):
        meta = {}
        meta["thing_classes"] = None
        meta["json_file"] = self.json_file
        meta["evaluator_type"] = "coco"
        return meta


def _coco_remove_images_without_annotations(dataset, cat_list=None):
    def _has_only_empty_bbox(anno):
        return all(any(o <= 1 for o in obj["bbox"][2:]) for obj in anno)

    def _count_visible_keypoints(anno):
        return sum(sum(1 for v in ann["keypoints"][2::3] if v > 0) for ann in anno)

    min_keypoints_per_image = 10

    def _has_valid_annotation(anno):
        # if it's empty, there is no annotation
        if len(anno) == 0:
            return False
        # if all boxes have close to zero area, there is no annotation
        if _has_only_empty_bbox(anno):
            return False
        # keypoints task have a slight different critera for considering
        # if an annotation is valid
        if "keypoints" not in anno[0]:
            return True
        # for keypoint detection tasks, only consider valid images those
        # containing at least min_keypoints_per_image
        if _count_visible_keypoints(anno) >= min_keypoints_per_image:
            return True
        return False

    ids = []
    for ds_idx, img_id in enumerate(dataset.sorted_imgs):
        # anno = dataset.sorted_anns[ds_idx]
        ann_ids = dataset.getAnnIds(imgIds=img_id, iscrowd=None)
        anno = dataset.loadAnns(ann_ids)
        if cat_list:
            anno = [obj for obj in anno if obj["category_id"] in cat_list]
        if _has_valid_annotation(anno):
            ids.append(ds_idx)

    dataset = torch.utils.data.Subset(dataset, ids)
    return dataset
