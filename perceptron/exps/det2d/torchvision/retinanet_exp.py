# encoding: utf-8
"""
rlaunch --charged-group base_det --cpu 32 --gpu 8 --memory 150000 --preemptible no -- \
    python3 perceptron/exps/det2d/torchvision/retinanet_exp.py --no-clearml -b 2 -d 0-7

Results:
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.365
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.556
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.387
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.189
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.401
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.494
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.317
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.509
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.549
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.346
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.595
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.698
"""

from perceptron.exps.det2d.torchvision.faster_rcnn_exp import Exp as BaseExp
from perceptron.engine.cli import BaseCli

from torchvision.models.detection.retinanet import retinanet_resnet50_fpn


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, pretrained_backbone=True, **kwargs):
        self.basic_lr_per_img = 0.02 / 16.0

        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch, pretrained_backbone)
        self.dump_interval = 1

    def _configure_model(self):
        model = retinanet_resnet50_fpn(num_classes=91, pretrained_backbone=self.pretrained_backbone)
        return model


if __name__ == "__main__":
    BaseCli(Exp).run()
