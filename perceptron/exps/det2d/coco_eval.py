import torch

from cvpack2.evaluation import COCOEvaluator as C2COCOEvaluator
from cvpack2.evaluation.coco_evaluation import instances_to_coco_json


class COCOEvaluator(C2COCOEvaluator):
    """
    Evaluate object proposal, instance detection/segmentation, keypoint detection
    outputs using COCO's metrics and APIs.
    """

    def __init__(self, dataset, distributed, tasks=("bbox",), output_dir=None, dump=False):
        """
        Args:
            dataset (Dataset): the dataset to be evaluated.
                It must have the following corresponding metadata:
                    metadata(SimpleNamespace): it include "eval_type" and "thing class"
                and provide coco_api from pycocotools.coco
                    coco_api: it provide 'loadRes', "CreateIndex" function.

            task (tuple[str]): tasks that can be evaluated under the given configuration.
                "bbox", "segm", "keypoints" are supported.
            output_dir (str): optional, an output directory to dump all
                results predicted on the dataset. The dump contains two files:

                1. "instance_predictions.pth" a file in torch serialization
                   format that contains all the raw original predictions.
                2. "coco_instances_results.json" a json file in COCO's result
                   format.

            dump (bool): If True, after the evaluation is completed, a Markdown file
                that records the model evaluation metrics and corresponding scores
                will be generated in the working directory.
                It always set False in this repo.
        """

        self._dump = dump
        self._tasks = tasks
        self._distributed = distributed
        self._output_dir = output_dir
        self._cpu_device = torch.device("cpu")

        self._metadata = dataset.meta
        if hasattr(dataset, "coco_api"):
            self._coco_api = dataset.coco_api
        else:
            self._coco_api = dataset
        # Test set json files do not contain annotations (evaluation must be
        # performed using the COCO evaluation server).
        self._do_evaluation = "annotations" in self._coco_api.dataset

        self._kpt_oks_sigmas = None

    def process(self, inputs, outputs):
        """
        Args:
            inputs: the inputs to a COCO model (e.g., GeneralizedRCNN).
                It is a list of dict. Each dict corresponds to an image and
                contains keys like "height", "width", "file_name", "image_id".
            outputs: the outputs of a COCO model. It is a list of dicts with key
                "instances" that contains :class:`Instances` or a json of coco format.
        """
        for input, output in zip(inputs, outputs):
            prediction = {"image_id": input["image_id"]}

            # TODO this is ugly
            if "instances" in output:
                instances = output["instances"].to(self._cpu_device)
                prediction["instances"] = instances_to_coco_json(instances, input["image_id"])
            else:
                prediction["instances"] = outputs

            if "proposals" in output:
                prediction["proposals"] = output["proposals"].to(self._cpu_device)
            self._predictions.append(prediction)
