#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Copyright (c) Megvii, Inc. and its affiliates.

"""
python3 yolox_l_traffic_data3d.py -te --no-clearml --amp -b 2 -d 0-7 \
    --pretrained_model s3://wankaiyuan-hhb/Transform/share/wfy/yolox_l.pth
python3 yolox_l_traffic_data3d.py --eval --ckpt outputdir

Result:

| metric             | value   |
|--------------------|---------|
| AP                 | 0.9523  |
| gt_num             | 14237   |
| recall_num         | 13656   |
| fp_num             | 1942    |
| high_iou_fp        | 96      |
| low_iou_fp         | 1846    |
| recall             | 0.9592  |
| precision          | 0.8755  |
| fppi               | 0.4845  |
| thres              | 0.05    |
| traffic_light_mIoU | 0.8362  |

| bucket_metric              | gt_num   | recall   |
|----------------------------|----------|----------|
| traffic_light@(1500, 4500) | 562      | 0.9786   |
| traffic_light@(4500, inf)  | 166      | 0.994    |
| traffic_light@(500, 1500)  | 3728     | 0.9839   |
| traffic_light@(0, 500)     | 9781     | 0.9481   |

| metric@fpr=0.05   | value   |
|-------------------|---------|
| gt_num            | 14237   |
| dt_num            | 13874   |
| tp_num            | 13163   |
| fp_num            | 711     |
| recall            | 0.9246  |
| thres             | 0.2604  |
"""
from types import SimpleNamespace

import numpy as np
import torch
from data3d.datasets.traffic_lights_det import TrafficDataset
from data3d.transforms.t2d.traffic_light_transform import TrainTransformLarge, Traffic_ValTransform
from perceptron.engine.cli import Det2dCvpack2Cli
from perceptron.engine.executors.evaluators import TrafficDet2DEvaluator
from perceptron.exps.det2d.coco_eval import COCOEvaluator
from perceptron.exps.det2d.yolox.yolox_base import Exp as BaseExp
from perceptron.exps.det2d.yolox.yolox_base import postprocess
from perceptron.utils import torch_dist as dist
from perceptron.utils.torch_dist import get_world_size
from perceptron.utils.traffic_det2d_utils.evaluate import TrafficEvaluator

from yolox.data import (
    DataLoader,
    InfiniteSampler,
    MosaicDetection,
    YoloBatchSampler,
    worker_init_reset_seed,
)


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=5, total_devices=1, max_epoch=30, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        # ---------------- model config ---------------- #
        self.depth = 1.00
        self.width = 1.00
        self.num_classes = 1

        # ---------------- dataloader config ---------------- #
        self.input_size = (608, 1088)
        self.start_input_size = self.input_size
        self.data_num_workers = 8
        self.crop_prob = 0.5
        self.nori_list = {
            "train": [
                "s3://koala-share/self_driving/apollo/detect/train/annotations_front_single_image.json",
                "s3://traffic-light-datasets/gjzl/train/train.json",
                "s3://traffic-light-datasets/gjzl/test/test.json",
                "s3://traffic-light-datasets/gshl_01/train/train.json",
                "s3://traffic-light-datasets/gshl_01/test/test.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_03/30_degree_untrack_labeled.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_03/60_degree_untrack_labeled.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_05/30_degree.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_05/60_degree.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_05/120_degree.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_06/30_degree.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_06/60_degree.json",
                "s3://perceptor-share/traffic-light-datasets/gshl_06/120_degree.json",
                "s3://wankaiyuan-hhb/data_building/distractor/distractor.json",
            ],
            # "val": ["s3://koala-share/self_driving/apollo/detect/test/annotations_front.json"],
            # "val": ["s3://perceptor-share/traffic-light-datasets/gshl_07/120_degree.json"],
            "val": ["s3://perceptor-share/traffic-light-datasets/gshl_02/annotation_inserted_cleared.json"],
        }

        # --------------  training config --------------------- #
        self.warmup_epochs = 0
        self._max_epoch = 30
        self.num_keep_latest_ckpt = self._max_epoch
        self.warmup_lr = 0
        self.basic_lr_per_img = 0.01 / 64
        self.no_aug_epochs = 5
        self.min_lr_ratio = 0.05
        self.ema = True

        self.weight_decay = 5e-4
        self.momentum = 0.9
        self.print_interval = 10
        self.dump_interval = 1
        self.mosaic_prob = 0
        self.mixup_prob = 0

        # -----------------  testing config ------------------ #
        self.test_size = (608, 1088)
        self.test_conf = 0.01
        self.nmsthre = 0.65

        # -----------------  evaluator config ------------------ #
        self.eval_executor_class = TrafficDet2DEvaluator
        self.iou_thr = 0.4  # iou>阈值时,eval时视为对应GT的正样本
        self.score_thr = {  # score>阈值时,eval时视为该类检出
            "traffic_light": 0.05,
        }
        self.class2dim = {
            "traffic_light": 0,
        }
        self.sub_areas = {"traffic_light": [0, 500, 1500, 4500, np.float("inf")]}

    def training_step(self, batch):
        images, targets, _, _ = batch

        images = images.cuda().to(self.data_type)
        targets = targets.cuda().to(self.data_type)
        targets.requires_grad = False

        images, targets = self.preprocess(images, targets, self.input_size)
        outputs = self.model(images, targets)
        loss = outputs["total_loss"]

        return loss

    def test_step(self, batch, model=None):
        images, targets, info_imgs, ids = batch
        ids, nori_ids = ids
        inputs = [{"image_id": int(img_id)} for img_id in ids]
        if model is not None:
            outputs = model(images.cuda())
        else:
            outputs = self.model(images.cuda())

        outputs = postprocess(outputs, self.num_classes, self.test_conf, self.nmsthre)
        outputs, scale = self.convert_to_cvpack2_format(outputs, info_imgs)
        all_dt = dict()
        all_gt = dict()
        # copnvert dict to instances, in order to use COCOEvaluator of cvpack2
        new_targets = [{} for _ in nori_ids]
        for k, v in targets.items():
            for i in range(len(v)):
                new_targets[i][k] = v[i]
        for out, target, nori_id in zip(outputs, new_targets, nori_ids):
            all_dt[nori_id] = dict()
            all_gt[nori_id] = dict()
            for label, box in zip(target["labels"].numpy().tolist(), target["boxes"].numpy().tolist()):
                # 个别image有gt,无dt,因此需要在gt的遍历中实例化一个空dt结果
                if label not in all_gt[nori_id]:
                    all_gt[nori_id][int(label)] = list()
                if label not in all_dt[nori_id]:
                    all_dt[nori_id][int(label)] = list()
                all_gt[nori_id][int(label)].append(box)
            for logits, pred, score in zip(out["labels"], out["boxes"], out["scores"]):
                all_dt[nori_id][int(logits)].append(pred.tolist() + [score.tolist()])

        return inputs, outputs, all_dt, all_gt

    def _configure_train_dataloader(self, no_aug=True, cache_img=False):

        batch_size = self.batch_size_per_device

        dataset = TrafficDataset(
            data_split="train",
            img_size=self.input_size,
            preproc=TrainTransformLarge(
                target_size=self.input_size,
                max_labels=50,
                flip_prob=self.flip_prob,
                hsv_prob=self.hsv_prob,
                crop_prob=self.crop_prob,
            ),
            nori_list=self.nori_list,
        )

        dataset = MosaicDetection(
            dataset,
            mosaic=False,
            img_size=self.input_size,
            preproc=TrainTransformLarge(
                target_size=self.input_size,
                max_labels=120,
                flip_prob=self.flip_prob,
                hsv_prob=self.hsv_prob,
                crop_prob=self.crop_prob,
            ),
            degrees=self.degrees,
            translate=self.translate,
            mosaic_scale=self.mosaic_scale,
            mixup_scale=self.mixup_scale,
            shear=self.shear,
            enable_mixup=self.enable_mixup,
            mosaic_prob=self.mosaic_prob,
            mixup_prob=self.mixup_prob,
        )

        self.dataset = dataset

        sampler = InfiniteSampler(len(self.dataset), seed=self.seed if self.seed else 0)

        batch_sampler = YoloBatchSampler(
            sampler=sampler,
            batch_size=batch_size,
            drop_last=False,
            mosaic=not no_aug,
        )

        dataloader_kwargs = {"num_workers": self.data_num_workers, "pin_memory": True}
        dataloader_kwargs["batch_sampler"] = batch_sampler

        # Make sure each process has different random seed, especially for 'fork' method.
        # Check https://github.com/pytorch/pytorch/issues/63311 for more details.
        dataloader_kwargs["worker_init_fn"] = worker_init_reset_seed

        train_loader = DataLoader(self.dataset, **dataloader_kwargs)

        return train_loader

    def _configure_val_dataloader(self, testdev=False, legacy=False):

        valdataset = TrafficDataset(
            data_split="val",
            img_size=self.test_size,
            preproc=Traffic_ValTransform(legacy=legacy),
            nori_list=self.nori_list,
        )

        is_distributed = get_world_size() > 1
        if is_distributed:
            sampler = torch.utils.data.distributed.DistributedSampler(valdataset, shuffle=False)
        else:
            sampler = torch.utils.data.SequentialSampler(valdataset)

        dataloader_kwargs = {
            "num_workers": 1,
            "pin_memory": True,
            "sampler": sampler,
        }
        dataloader_kwargs["batch_size"] = 1
        val_loader = torch.utils.data.DataLoader(valdataset, **dataloader_kwargs)

        meta = {}
        meta["thing_classes"] = None
        meta["evaluator_type"] = "coco"
        valdataset.meta = SimpleNamespace(**meta)
        self.evaluator = COCOEvaluator(valdataset, distributed=is_distributed, tasks=self.eval_tasks)
        self.extra_evaluator = TrafficEvaluator(
            distributed=dist.is_distributed(),
            iou_thr=self.iou_thr,
            score_thr=self.score_thr,
            class2dim=self.class2dim,
            sub_areas=self.sub_areas,
        )

        return val_loader


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
