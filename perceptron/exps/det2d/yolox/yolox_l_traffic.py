#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Copyright (c) Megvii, Inc. and its affiliates.

"""
rlaunch --charged-group base_det --cpu 48 --gpu 8 --memory 200000 --preemptible no -- \
    python3 perceptron/exps/det2d/yolox/yolox_l_traffic.py --no-clearml -e 30 -b 8 -d 0-7 --amp \
        --pretrained_model yolox_l.pth
model path:
    s3://basedet3d/peceptron/yolox_l.pth

Result:
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.381
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.729
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.348
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.275
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.578
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.720
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.143
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.447
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.451
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.359
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.658
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.776
"""

from collections import defaultdict
import contextlib
import io
import json
from types import SimpleNamespace
import numpy as np
import cv2
from refile import smart_open

import torch
from pycocotools.coco import COCO

from perceptron.exps.det2d.coco_eval import COCOEvaluator
from perceptron.exps.det2d.yolox.yolox_base import Exp as BaseExp, postprocess
from perceptron.engine.cli import Det2dCvpack2Cli
from yolox.data import COCODataset

from perceptron.utils.torch_dist import get_world_size


class OSSCOCO(COCO):
    def __init__(self, annotation_file=None):
        """
        Constructor of Microsoft COCO helper class for reading and visualizing annotations.
        :param annotation_file (str): location of annotation file
        :param image_folder (str): location to the folder that hosts images.
        :return:
        """
        # load dataset
        self.dataset, self.anns, self.cats, self.imgs = dict(), dict(), dict(), dict()
        self.imgToAnns, self.catToImgs = defaultdict(list), defaultdict(list)
        if annotation_file is not None:
            print("loading annotations into memory...")
            with smart_open(annotation_file, "r") as f:
                dataset = json.load(f)
            assert type(dataset) == dict, "annotation file format {} not supported".format(type(dataset))
            self.dataset = dataset
            self.createIndex()


class TrafficDataset(COCODataset):
    """
    COCO dataset class.
    """

    def __init__(
        self,
        data_split="train",
        img_size=(416, 416),
        preproc=None,
        cache=False,
    ):
        self.__input_dim = img_size[:2]
        self.enable_mosaic = True

        self.nori_list = {
            "train": "s3://koala-share/self_driving/apollo/detect/train/annotations_front_single_image.json",
            "val": "s3://koala-share/self_driving/apollo/detect/test/annotations_front.json",
        }

        with contextlib.redirect_stdout(io.StringIO()):
            self.coco = OSSCOCO(self.nori_list[data_split])
        self.ids = self.coco.getImgIds()
        self.class_ids = sorted(self.coco.getCatIds())
        cats = self.coco.loadCats(self.coco.getCatIds())
        self._classes = tuple([c["name"] for c in cats])
        self.imgs = None
        self.img_size = img_size
        self.preproc = preproc
        self.annotations = self._load_coco_annotations()

        self.coco_api = self.coco

    @property
    def input_dim(self):
        if hasattr(self, "_input_dim"):
            return self._input_dim
        return self.__input_dim

    def load_image(self, index):
        nori_id = self.annotations[index][3]
        with smart_open("nori://" + nori_id, "rb") as f:
            img = cv2.imdecode(np.frombuffer(f.read(), np.uint8), cv2.IMREAD_COLOR)
        assert img is not None

        return img


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=300, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        # ---------------- model config ---------------- #
        self.depth = 1.00
        self.width = 1.00
        self.num_classes = 1

        # ---------------- dataloader config ---------------- #
        self.input_size = (672, 1216)
        self.start_input_size = self.input_size  # (height, width)
        self.data_num_workers = 4

        # --------------  training config --------------------- #
        self.warmup_epochs = 1
        self._max_epoch = 30
        self.warmup_lr = 0
        self.basic_lr_per_img = 0.001 / 64.0
        self.no_aug_epochs = 5
        self.min_lr_ratio = 0.05
        self.ema = True

        self.weight_decay = 5e-4
        self.momentum = 0.9
        self.print_interval = 10
        self.dump_interval = 1

        # -----------------  testing config ------------------ #
        self.test_size = (672, 1216)
        self.test_conf = 0.01
        self.nmsthre = 0.65

    def training_step(self, batch):
        images, targets, _, _ = batch

        images = images.cuda()
        targets = targets.cuda()
        targets.requires_grad = False

        images, targets = self.preprocess(images, targets, self.input_size)

        outputs = self.model(images, targets)
        loss = outputs["total_loss"]

        return loss

    def test_step(self, batch):
        images, targets, info_imgs, ids = batch
        inputs = [{"image_id": int(img_id)} for img_id in ids]
        outputs = self.model(images.cuda())

        outputs = postprocess(outputs, self.num_classes, self.test_conf, self.nmsthre)
        output_instances = self.convert_to_coco_format(outputs, info_imgs, ids)

        return inputs, output_instances

    def _configure_train_dataloader(self, no_aug=False, cache_img=False):

        batch_size = self.batch_size_per_device

        from yolox.data import (
            TrainTransform,
            YoloBatchSampler,
            DataLoader,
            InfiniteSampler,
            MosaicDetection,
            worker_init_reset_seed,
        )

        dataset = TrafficDataset(
            data_split="train",
            img_size=self.input_size,
            preproc=TrainTransform(max_labels=50, flip_prob=self.flip_prob, hsv_prob=self.hsv_prob),
            cache=cache_img,
        )

        dataset = MosaicDetection(
            dataset,
            mosaic=not no_aug,
            img_size=self.input_size,
            preproc=TrainTransform(max_labels=120, flip_prob=self.flip_prob, hsv_prob=self.hsv_prob),
            degrees=self.degrees,
            translate=self.translate,
            mosaic_scale=self.mosaic_scale,
            mixup_scale=self.mixup_scale,
            shear=self.shear,
            enable_mixup=self.enable_mixup,
            mosaic_prob=self.mosaic_prob,
            mixup_prob=self.mixup_prob,
        )

        self.dataset = dataset

        sampler = InfiniteSampler(len(self.dataset), seed=self.seed if self.seed else 0)

        batch_sampler = YoloBatchSampler(
            sampler=sampler,
            batch_size=batch_size,
            drop_last=False,
            mosaic=not no_aug,
        )

        dataloader_kwargs = {"num_workers": self.data_num_workers, "pin_memory": True}
        dataloader_kwargs["batch_sampler"] = batch_sampler

        # Make sure each process has different random seed, especially for 'fork' method.
        # Check https://github.com/pytorch/pytorch/issues/63311 for more details.
        dataloader_kwargs["worker_init_fn"] = worker_init_reset_seed

        train_loader = DataLoader(self.dataset, **dataloader_kwargs)

        return train_loader

    def _configure_val_dataloader(self, testdev=False, legacy=False):
        from yolox.data import ValTransform

        valdataset = TrafficDataset(
            data_split="val",
            img_size=self.test_size,
            preproc=ValTransform(legacy=legacy),
        )

        is_distributed = get_world_size() > 1
        if is_distributed:
            sampler = torch.utils.data.distributed.DistributedSampler(valdataset, shuffle=False)
        else:
            sampler = torch.utils.data.SequentialSampler(valdataset)

        dataloader_kwargs = {
            "num_workers": 2,
            "pin_memory": True,
            "sampler": sampler,
        }
        dataloader_kwargs["batch_size"] = 1
        val_loader = torch.utils.data.DataLoader(valdataset, **dataloader_kwargs)

        meta = {}
        meta["thing_classes"] = None
        meta["evaluator_type"] = "coco"
        valdataset.meta = SimpleNamespace(**meta)
        self.evaluator = COCOEvaluator(valdataset, distributed=is_distributed, tasks=self.eval_tasks)
        return val_loader


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
