#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Copyright (c) 2014-2021 Megvii Inc. All rights reserved.


import contextlib
import io
import cv2
import numpy as np
import random
import torch
import torch.nn as nn
import torch.distributed as dist
import torchvision

from types import SimpleNamespace
from refile import smart_open
from yolox.data import COCODataset as YoloXCOCODataset
from yolox.utils import all_reduce_norm
from data3d.datasets.coco import COCODataset as coco_api
from perceptron.engine.cli import Det2dCvpack2Cli
from perceptron.engine.executors import Trainer
from perceptron.engine.executors.evaluators import Det2DEvaluator
from perceptron.engine.executors.exports import Det2DExports
from perceptron.exps.base_exp import BaseExp
from perceptron.exps.det2d.coco_eval import COCOEvaluator
from perceptron.layers.ema import ModelEMA
from perceptron.utils.torch_dist import get_world_size, is_distributed, get_rank


class COCODataset(YoloXCOCODataset):
    """
    COCO dataset class.
    """

    def __init__(
        self,
        data_split="training",
        img_size=(416, 416),
        preproc=None,
        cache=False,
    ):
        self.data_split = data_split
        self.__input_dim = img_size[:2]
        self.enable_mosaic = True

        with contextlib.redirect_stdout(io.StringIO()):
            self.coco = coco_api(self.data_split)
        self.ids = self.coco.getImgIds()
        self.class_ids = sorted(self.coco.getCatIds())
        cats = self.coco.loadCats(self.coco.getCatIds())
        self._classes = tuple([c["name"] for c in cats])
        self.imgs = None
        self.img_size = img_size
        self.preproc = preproc
        self.annotations = self._load_coco_annotations()

        self.coco_api = self.coco

    @property
    def input_dim(self):
        if hasattr(self, "_input_dim"):
            return self._input_dim
        return self.__input_dim

    def load_image(self, index):
        nori_id = self.annotations[index][-1]
        with smart_open("nori://" + nori_id, "rb") as f:
            img = cv2.imdecode(np.frombuffer(f.read(), np.uint8), cv2.IMREAD_COLOR)
        assert img is not None

        return img

    def load_anno_from_ids(self, id_):
        im_ann = self.coco.loadImgs(id_)[0]
        width = im_ann["width"]
        height = im_ann["height"]
        anno_ids = self.coco.getAnnIds(imgIds=[int(id_)], iscrowd=False)
        annotations = self.coco.loadAnns(anno_ids)
        objs = []
        for obj in annotations:
            x1 = np.max((0, obj["bbox"][0]))
            y1 = np.max((0, obj["bbox"][1]))
            x2 = np.min((width, x1 + np.max((0, obj["bbox"][2]))))
            y2 = np.min((height, y1 + np.max((0, obj["bbox"][3]))))
            if obj["area"] > 0 and x2 >= x1 and y2 >= y1:
                obj["clean_bbox"] = [x1, y1, x2, y2]
                objs.append(obj)

        num_objs = len(objs)

        res = np.zeros((num_objs, 5))

        for ix, obj in enumerate(objs):
            cls = self.class_ids.index(obj["category_id"])
            res[ix, 0:4] = obj["clean_bbox"]
            res[ix, 4] = cls

        r = min(self.img_size[0] / height, self.img_size[1] / width)
        res[:, :4] *= r

        img_info = (height, width)
        resized_info = (int(height * r), int(width * r))

        nori_id = im_ann["nori_id"]

        return (res, img_info, resized_info, nori_id)

    def pull_item(self, index):
        id_ = self.ids[index]

        res, img_info, resized_info, _ = self.annotations[index]
        if self.imgs is not None:
            pad_img = self.imgs[index]
            img = pad_img[: resized_info[0], : resized_info[1], :].copy()
        else:
            img = self.load_resized_img(index)
        return img, res.copy(), img_info, np.array([id_])


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=90, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)

        # ---------------- model config ---------------- #
        self.num_classes = 80
        self.depth = 1.00
        self.width = 1.00
        self.act = "silu"
        self.fp16 = True
        self.data_type = torch.float16 if self.fp16 else torch.float32

        # ---------------- dataloader config ---------------- #
        # set worker to 4 for shorter dataloader init time
        self.data_num_workers = 4
        self.input_size = (640, 640)
        self.start_input_size = self.input_size  # (height, width)
        # Actual multiscale ranges: [640-5*32, 640+5*32].
        # To disable multiscale training, set the
        # self.multiscale_range to 0.
        self.multiscale_range = 5
        # You can uncomment this line to specify a multiscale range
        # self.random_size = (14, 26)

        # --------------- transform config ----------------- #
        self.mosaic_prob = 1.0
        self.mixup_prob = 1.0
        self.hsv_prob = 1.0
        self.flip_prob = 0.5
        self.degrees = 10.0
        self.translate = 0.1
        self.mosaic_scale = (0.1, 2)
        self.mixup_scale = (0.5, 1.5)
        self.shear = 2.0
        self.enable_mixup = True

        # --------------  training config --------------------- #
        self.warmup_epochs = 5
        self._max_epoch = 300
        self.num_keep_latest_ckpt = self._max_epoch
        self.warmup_lr = 0
        self.basic_lr_per_img = 0.01 / 64.0
        self.no_aug_epochs = 15
        self.min_lr_ratio = 0.05
        self.ema = True

        self.weight_decay = 5e-4
        self.momentum = 0.9
        self.print_interval = 100
        self.eval_interval = 10

        # -----------------  testing config ------------------ #
        self.test_size = (640, 640)
        self.test_conf = 0.001
        self.nmsthre = 0.65
        self.eval_tasks = ("bbox",)

        # -----------------  executors config ------------------ #
        self.eval_executor_class = Det2DEvaluator
        self.export_executor_class = Det2DExports

    def training_step(self, batch):
        images, targets, _, _ = batch
        images = images.cuda()
        targets = targets.cuda()
        targets.requires_grad = False

        images, targets = self.preprocess(images, targets, self.input_size)

        outputs = self.model(images, targets)
        loss = outputs["total_loss"]

        return loss

    def test_step(self, batch):
        images, targets, info_imgs, ids = batch
        inputs = [{"image_id": int(img_id)} for img_id in ids]
        outputs = self.model(images.cuda())
        outputs = postprocess(outputs, self.num_classes, self.test_conf, self.nmsthre)
        output_instances = self.convert_to_coco_format(outputs, info_imgs, ids)
        return inputs, output_instances

    def export_onnx(self, output_path):
        from yolox.models.network_blocks import SiLU
        from yolox.utils import replace_module

        """transfer torch model to onnx model.
        Args:
            model: traced model.
            output_path (str): export path of onnx model.
        """
        dynamic_axes = {
            "yolox_output": {0: "batch"},
            "images": {0: "batch", 1: "channel", 2: "height", 3: "width"},
        }
        inputs = torch.randn(1, 3, 640, 640)
        self.model = replace_module(self.model, nn.SiLU, SiLU)
        self.model.head.decode_in_inference = False

        with open(output_path, "wb") as f:
            torch.onnx.export(
                self.model,
                inputs,
                f,
                opset_version=11,
                do_constant_folding=True,
                input_names=["images"],
                output_names=["yolox_output"],
                dynamic_axes=dynamic_axes,
            )

    def export_torchscript(self, output_path):
        inputs = torch.randn(1, 3, 640, 640)
        self.model.head.decode_in_inference = False
        mod = torch.jit.trace(self.model, inputs)
        mod.save(output_path)

    def _configure_model(self):
        from yolox.models import YOLOPAFPN, YOLOX, YOLOXHead

        def init_yolo(M):
            for m in M.modules():
                if isinstance(m, nn.BatchNorm2d):
                    m.eps = 1e-3
                    m.momentum = 0.03

        in_channels = [256, 512, 1024]
        backbone = YOLOPAFPN(self.depth, self.width, in_channels=in_channels, act=self.act)
        head = YOLOXHead(self.num_classes, self.width, in_channels=in_channels, act=self.act)
        model = YOLOX(backbone, head)

        model.apply(init_yolo)
        model.head.initialize_biases(1e-2)
        return model

    def _configure_train_dataloader(self, no_aug=False, cache_img=False):

        batch_size = self.batch_size_per_device

        from yolox.data import (
            DataLoader,
            InfiniteSampler,
            MosaicDetection,
            TrainTransform,
            YoloBatchSampler,
            worker_init_reset_seed,
        )

        dataset = COCODataset(
            data_split="training",
            img_size=self.input_size,
            preproc=TrainTransform(max_labels=50, flip_prob=self.flip_prob, hsv_prob=self.hsv_prob),
            cache=cache_img,
        )

        dataset = MosaicDetection(
            dataset,
            mosaic=not no_aug,
            img_size=self.input_size,
            preproc=TrainTransform(max_labels=120, flip_prob=self.flip_prob, hsv_prob=self.hsv_prob),
            degrees=self.degrees,
            translate=self.translate,
            mosaic_scale=self.mosaic_scale,
            mixup_scale=self.mixup_scale,
            shear=self.shear,
            enable_mixup=self.enable_mixup,
            mosaic_prob=self.mosaic_prob,
            mixup_prob=self.mixup_prob,
        )

        self.dataset = dataset

        sampler = InfiniteSampler(len(self.dataset), seed=self.seed if self.seed else 0)

        batch_sampler = YoloBatchSampler(
            sampler=sampler,
            batch_size=batch_size,
            drop_last=False,
            mosaic=not no_aug,
        )

        dataloader_kwargs = {"num_workers": self.data_num_workers, "pin_memory": True}
        dataloader_kwargs["batch_sampler"] = batch_sampler

        # Make sure each process has different random seed, especially for 'fork' method.
        # Check https://github.com/pytorch/pytorch/issues/63311 for more details.
        dataloader_kwargs["worker_init_fn"] = worker_init_reset_seed

        train_loader = DataLoader(self.dataset, **dataloader_kwargs)

        return train_loader

    def _configure_val_dataloader(self, testdev=False, legacy=False):
        from yolox.data import ValTransform

        valdataset = COCODataset(
            data_split="validation",
            img_size=self.test_size,
            preproc=ValTransform(legacy=legacy),
        )

        is_distributed = get_world_size() > 1
        if is_distributed:
            sampler = torch.utils.data.distributed.DistributedSampler(valdataset, shuffle=False)
        else:
            sampler = torch.utils.data.SequentialSampler(valdataset)

        dataloader_kwargs = {
            "num_workers": self.data_num_workers,
            "pin_memory": True,
            "sampler": sampler,
        }
        dataloader_kwargs["batch_size"] = 1
        val_loader = torch.utils.data.DataLoader(valdataset, **dataloader_kwargs)

        meta = {}
        meta["thing_classes"] = None
        meta["evaluator_type"] = "coco"
        valdataset.meta = SimpleNamespace(**meta)
        self.evaluator = COCOEvaluator(valdataset, distributed=is_distributed, tasks=self.eval_tasks)
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        if self.warmup_epochs > 0:
            lr = self.warmup_lr
        else:
            lr = self.basic_lr_per_img * self.batch_size_per_device * self.total_devices

        pg0, pg1, pg2 = [], [], []  # optimizer parameter groups

        for k, v in self.model.named_modules():
            if hasattr(v, "bias") and isinstance(v.bias, nn.Parameter):
                pg2.append(v.bias)  # biases
            if isinstance(v, nn.BatchNorm2d) or "bn" in k:
                pg0.append(v.weight)  # no decay
            elif hasattr(v, "weight") and isinstance(v.weight, nn.Parameter):
                pg1.append(v.weight)  # apply decay

        optimizer = torch.optim.SGD(pg0, lr=lr, momentum=self.momentum, nesterov=True)
        optimizer.add_param_group({"params": pg1, "weight_decay": self.weight_decay})  # add pg1 with weight_decay
        optimizer.add_param_group({"params": pg2})

        return optimizer

    def _configure_lr_scheduler(self):
        from perceptron.layers.lr_scheduler import YoloxWarmCosineLRScheduler

        lr = self.basic_lr_per_img * self.batch_size_per_device * self.total_devices
        scheduler = YoloxWarmCosineLRScheduler(
            self.optimizer,
            lr,
            len(self.train_dataloader),
            self.max_epoch,
            warmup_epochs=self.warmup_epochs,
            warmup_lr_start=self.warmup_lr,
            no_aug_epochs=self.no_aug_epochs,
            min_lr_ratio=self.min_lr_ratio,
        )
        return scheduler

    def _configure_callbacks(self):
        return [ModelEMACallback(), MosaicCallback(), RandomResizeByIter(self.input_size, self.multiscale_range)]

    def preprocess(self, inputs, targets, tsize):
        scale_y = tsize[0] / self.start_input_size[0]
        scale_x = tsize[1] / self.start_input_size[1]
        if scale_x != 1 or scale_y != 1:
            inputs = nn.functional.interpolate(inputs, size=tsize, mode="bilinear", align_corners=False)
            targets[..., 1::2] = targets[..., 1::2] * scale_x
            targets[..., 2::2] = targets[..., 2::2] * scale_y
        return inputs, targets

    def convert_to_cvpack2_format(self, outputs, info_imgs):
        data_list = []
        res_dict = {}
        scale = 1
        for (output, img_h, img_w) in zip(outputs, info_imgs[0], info_imgs[1]):
            if output is None:
                data_list.append(
                    {
                        "boxes": torch.zeros((0, 4)),
                        "scores": torch.zeros((0)),
                        "labels": torch.zeros((0)),
                    }
                )
                continue
            output = output.cpu()

            # xyxy
            bboxes = output[:, 0:4]

            # preprocessing: resize
            scale = min(self.test_size[0] / float(img_h), self.test_size[1] / float(img_w))
            bboxes /= scale

            scores = output[:, 4] * output[:, 5]
            labels = output[:, -1]
            res_dict["boxes"] = bboxes
            res_dict["scores"] = scores
            res_dict["labels"] = labels
            data_list.append(res_dict)

        return data_list, scale

    def convert_to_coco_format(self, outputs, info_imgs, ids):
        data_list = []
        for (output, img_h, img_w, img_id) in zip(outputs, info_imgs[0], info_imgs[1], ids):
            if output is None:
                continue
            output = output.cpu()

            bboxes = output[:, 0:4]

            # preprocessing: resize
            scale = min(self.test_size[0] / float(img_h), self.test_size[1] / float(img_w))
            bboxes /= scale
            bboxes = xyxy2xywh(bboxes)

            cls = output[:, 6]
            scores = output[:, 4] * output[:, 5]
            for ind in range(bboxes.shape[0]):
                label = self.val_dataloader.dataset.class_ids[int(cls[ind])]
                pred_data = {
                    "image_id": int(img_id),
                    "category_id": label,
                    "bbox": bboxes[ind].numpy().tolist(),
                    "score": scores[ind].numpy().item(),
                    "segmentation": [],
                }  # COCO json format
                data_list.append(pred_data)
        return data_list


def postprocess(prediction, num_classes, conf_thre=0.7, nms_thre=0.45, class_agnostic=False):
    box_corner = prediction.new(prediction.shape)
    box_corner[:, :, 0] = prediction[:, :, 0] - prediction[:, :, 2] / 2
    box_corner[:, :, 1] = prediction[:, :, 1] - prediction[:, :, 3] / 2
    box_corner[:, :, 2] = prediction[:, :, 0] + prediction[:, :, 2] / 2
    box_corner[:, :, 3] = prediction[:, :, 1] + prediction[:, :, 3] / 2
    prediction[:, :, :4] = box_corner[:, :, :4]

    output = [None for _ in range(len(prediction))]
    for i, image_pred in enumerate(prediction):

        # If none are remaining => process next image
        if not image_pred.size(0):
            continue
        # Get score and class with highest confidence
        class_conf, class_pred = torch.max(image_pred[:, 5 : 5 + num_classes], 1, keepdim=True)

        conf_mask = (image_pred[:, 4] * class_conf.squeeze() >= conf_thre).squeeze()
        # Detections ordered as (x1, y1, x2, y2, obj_conf, class_conf, class_pred)
        detections = torch.cat((image_pred[:, :5], class_conf, class_pred.float()), 1)
        detections = detections[conf_mask]
        if not detections.size(0):
            continue

        if class_agnostic:
            nms_out_index = torchvision.ops.nms(
                detections[:, :4],
                detections[:, 4] * detections[:, 5],
                nms_thre,
            )
        else:
            nms_out_index = torchvision.ops.batched_nms(
                detections[:, :4],
                detections[:, 4] * detections[:, 5],
                detections[:, 6],
                nms_thre,
            )

        detections = detections[nms_out_index]
        if output[i] is None:
            output[i] = detections
        else:
            output[i] = torch.cat((output[i], detections))

    return output


def xyxy2xywh(bboxes):
    bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 0]
    bboxes[:, 3] = bboxes[:, 3] - bboxes[:, 1]
    return bboxes


class ModelEMACallback:

    enabled_rank = None

    def before_train(self, trainer: Trainer):
        trainer.ema_model = ModelEMA(trainer.model, 0.9999)
        trainer.ema_model.updates = len(trainer.train_dataloader) * trainer.epoch
        trainer.logger.info("ema model update {}".format(trainer.epoch))

    def after_step(self, trainer, step, data_dict, *args, **kwargs):
        trainer.ema_model.update(trainer.model)

    def before_eval(self, trainer):
        all_reduce_norm(trainer.ema_model.ema)
        trainer.temp_model = trainer.model
        trainer.model = trainer.ema_model.ema

    def after_eval(self, trainer, **kwargs):
        trainer.model = trainer.temp_model
        del trainer.temp_model


class MosaicCallback:

    enabled_rank = None

    def before_epoch(self, trainer: Trainer, epoch: int):
        if epoch >= trainer.exp.max_epoch - trainer.exp.no_aug_epochs:
            trainer.logger.info("--->No mosaic aug now!")
            trainer.train_dataloader.close_mosaic()
            trainer.model.module.head.use_l1 = True


class RandomResizeByIter:

    enabled_rank = [0]

    def __init__(self, input_size, multiscale_range, random_size=None, frequency=30) -> None:
        self.input_size = input_size
        self.multiscale_range = multiscale_range
        self.random_size = random_size
        self.frequency = frequency

    def random_resize(self):
        tensor = torch.LongTensor(2).cuda()
        if get_rank() == 0:
            size_factor = self.input_size[1] * 1.0 / self.input_size[0]
            if self.random_size is None:
                min_size = int(self.input_size[0] / 32) - self.multiscale_range
                max_size = int(self.input_size[0] / 32) + self.multiscale_range
                self.random_size = (min_size, max_size)
            size = random.randint(*self.random_size)
            size = (int(32 * size), 32 * int(size * size_factor))
            tensor[0] = size[0]
            tensor[1] = size[1]
        if is_distributed() > 1:
            dist.barrier()
            dist.broadcast(tensor, 0)

        input_size = (tensor[0].item(), tensor[1].item())
        return input_size

    def after_step(self, trainer, step, data_dict, *args, **kwargs):
        if (trainer.global_step + 1) % self.frequency == 0:
            trainer.exp.input_size = self.random_resize()


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
