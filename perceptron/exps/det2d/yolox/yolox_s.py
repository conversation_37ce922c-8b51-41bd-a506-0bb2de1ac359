#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Copyright (c) Megvii, Inc. and its affiliates.

"""
Train:
rlaunch --charged-group base_det --cpu 48 --gpu 8 --memory 200000 --preemptible no -- \
    python3 perceptron/exps/det2d/yolox/yolox_s.py --no-clearml -e 300 -b 8 -d 0-7 --amp
Export:
rlaunch --charged-group base_det --cpu 8 --gpu 1 --memory 20000 --preemptible no -- \
    python3 perceptron/exps/det2d/yolox/yolox_s.py \
    --no-clearml --export --ckpt {your model path} \
    --env local
model path:
    s3://basedet3d/peceptron/yolox_s.pth

Results:
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.397
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.592
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.430
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.238
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.442
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.523
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.320
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.527
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.572
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.392
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.633
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.706
"""

from perceptron.exps.det2d.yolox.yolox_base import Exp as BaseExp
from perceptron.engine.cli import Det2dCvpack2Cli


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=300, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.depth = 0.33
        self.width = 0.50
        self.print_interval = 100


if __name__ == "__main__":
    Det2dCvpack2Cli(Exp).run()
