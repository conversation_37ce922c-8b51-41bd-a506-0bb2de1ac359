"""
Multi-task train/test code for 2 task:
  - task1: fcos3d (perceptron/exps/det3d/monocular/mmdet3d_fcos3d_sgd_nuscenes.py)
  - task2: yolox (perceptron/exps/det2d/yolox/yolox_l_traffic.py)
1. train:
DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --preemptible no -- python3 perceptron/exps/multi_task/fcos3d_yoloxl_exp.py -d 0-7 -b 1 -e 24 --sync_bn 3 --no-clearml --find_unused_parameters --amp

2. test:
python3 perceptron/exps/multi_task/fcos3d_yoloxl_exp.py -d 0-7 -e 24 --no-clearml --amp --eval --ckpt <path-to-pth>

3. metric (epoch_24):
  - task1
    mAP: 0.2776
    mATE: 0.7986
    mASE: 0.2714
    mAOE: 0.4983
    mAVE: 1.1587
    mAAE: 0.1691
    NDS: 0.3651

    Per-class results:
    Object Class    AP      ATE     ASE     AOE     AVE     AAE
    car     0.442   0.638   0.152   0.110   1.545   0.149
    truck   0.200   0.867   0.207   0.180   1.267   0.209
    bus     0.270   0.876   0.194   0.203   2.601   0.344
    trailer 0.065   1.100   0.222   0.811   0.654   0.099
    construction_vehicle    0.047   1.050   0.474   0.951   0.143   0.298
    pedestrian      0.381   0.719   0.291   0.707   0.787   0.166
    motorcycle      0.254   0.795   0.272   0.647   1.566   0.073
    bicycle 0.231   0.725   0.285   0.724   0.706   0.014
    traffic_cone    0.479   0.585   0.329   nan     nan     nan
    barrier 0.408   0.631   0.287   0.152   nan     nan

  - task2: yolox
    Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.295
    Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.520
    Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.299
    Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.181
    Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.519
    Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.766
    Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.119
    Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.381
    Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.399
    Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.281
    Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.659
    Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.828

    |   AP   |  AP50  |  AP75  |  APs   |  APm   |  APl   |
    |:------:|:------:|:------:|:------:|:------:|:------:|
    | 29.516 | 51.973 | 29.937 | 18.143 | 51.881 | 76.583 |
"""

from collections import OrderedDict
import torch
import torch.distributed as dist
import torch.nn as nn
import math
from copy import deepcopy

from torch.utils.data import DataLoader, DistributedSampler
from perceptron.data.sampler import InfiniteSampler


from perceptron.exps.base_exp import BaseExp
from perceptron.engine.executors import Trainer

# fcos3d
from perceptron.engine.cli import MultiCli
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import load_data_to_gpu
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import Exp as Exp_fcos3d
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import ModelConfigs as Fcos3dModelConfigs
from perceptron.exps.det3d.monocular.mmdet3d_fcos3d_sgd_nuscenes import NuScenesMonoDatasetWithEval
from mmdet.datasets.pipelines import Compose, MultiScaleFlipAug, Normalize, Pad, Resize
from mmdet3d.datasets.pipelines import Collect3D, DefaultFormatBundle3D, LoadAnnotations3D, RandomFlip3D
from mmdet3d.core.bbox.structures.cam_box3d import CameraInstance3DBoxes

# yolox
from perceptron.exps.det2d.yolox.yolox_l_traffic import Exp as Exp_yolox
from perceptron.exps.det2d.yolox.yolox_base import postprocess

# multi task
from perceptron.models.multi_task.fcos3d_yolox_seperatefpn import Fcos3dYoloxModel
from perceptron.engine.executors.evaluators import Det2DEvaluatorMulti, Det3DEvaluatorMulti

from perceptron.utils.det3d_utils.common_utils import set_random_seed

set_random_seed(1234)


class YoloxModelConfigs:
    depth = 1.00
    width = 1.00
    num_classes = 1
    act = "silu"
    in_channels = [256, 512, 1024]


class NuScenesMonoDatasetWithEvalMulti(NuScenesMonoDatasetWithEval):
    def __init__(self, class_names=None, attribute_names=None, training=True, img_scale=(1600, 900)):
        super().__init__(class_names, attribute_names, training)
        self.img_scale = img_scale
        self.data_augmentor = Compose(
            [
                LoadAnnotations3D(
                    with_bbox=True,
                    with_label=True,
                    with_attr_label=True,
                    with_bbox_3d=True,
                    with_label_3d=True,
                    with_bbox_depth=True,
                ),
                Resize(img_scale=self.img_scale, keep_ratio=True),
                RandomFlip3D(flip_ratio_bev_horizontal=0.5),
                Normalize(
                    mean=[0.0, 0.0, 0.0], std=[1.0, 1.0, 1.0], to_rgb=True
                ),  # ori img is rgb, so to_rgb=True flag will transform input img to be bgr (Note: input of yolox is bgr)
                Pad(size_divisor=32),
                DefaultFormatBundle3D(class_names=self.class_names),
                Collect3D(
                    keys=[
                        "img",
                        "gt_bboxes",
                        "gt_labels",
                        "attr_labels",
                        "gt_bboxes_3d",
                        "gt_labels_3d",
                        "centers2d",
                        "depths",
                    ]
                ),
            ]
        )
        self.test_augmentor = Compose(
            [
                MultiScaleFlipAug(
                    scale_factor=1.0,
                    flip=False,
                    transforms=[
                        Normalize(
                            mean=[0.0, 0.0, 0.0], std=[1.0, 1.0, 1.0], to_rgb=True
                        ),  # ori img is rgb, so to_rgb=True flag will transform input img to be bgr (Note: input of yolox is bgr)
                        Pad(size_divisor=32),
                        DefaultFormatBundle3D(class_names=self.class_names, with_label=False),
                        Collect3D(keys=["img"]),
                    ],
                )
            ]
        )


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=2, total_devices=1, max_epoch=24, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.multi_task = True  # NOTE: must be set True when training with multi task codebase (single task in this codebase should also be set)
        self.dump_interval = 1
        self.print_interval = 20
        self.img_scale = (1600, 900)  # w, h
        self.input_size = (928, 1600)  # h, w (after padded 32)

        # Define multi task exp
        # 1. fcos3d
        self.fcos3d_batch_size_per_device = 1
        self.fcos3d_exp = Exp_fcos3d(batch_size_per_device=self.fcos3d_batch_size_per_device)
        self.fcos3d_exp.img_scale = self.img_scale

        # 2. yolox
        self.yolox_batch_size_per_device = 6
        self.yolox_exp = Exp_yolox(batch_size_per_device=self.yolox_batch_size_per_device)
        self.yolox_exp.no_aug_epochs = 0  # always with demosaic aug (avoid loss NaN bug)
        self.yolox_exp.input_size = self.input_size
        self.yolox_exp.test_size = self.input_size

        # Define multi task configurations
        self.tasks = OrderedDict()
        # 1. fcos3d
        self.tasks["fcos3d"] = {
            "loss_ratio": 1,  # NOTE: scale coefficient
            "dataset": "nuscenes",  # NOTE: training/eval dataset; A task can only be read from one dataset, but a dataset can supply different tasks at the same time
            "evaluator": Det3DEvaluatorMulti,  # NOTE: definition class of evaluator
            "exp": self.fcos3d_exp,  # NOTE: experiment instance for single task
            "eval": True,  # NOTE: whether to eval this task
            "train": True,  # NOTE: whether to train this task
            "pretrained": False,  # NOTE: whether to use pretrained model weights
            "checkpoint": "",  # NOTE: path to pretrained model weights
            "training_times_in_a_single_step": 10,  # NOTE: number of training times in a single training step
        }
        # 2. yolox
        self.tasks["yolox"] = {
            "loss_ratio": 1,
            "dataset": "traffic",
            "evaluator": Det2DEvaluatorMulti,
            "exp": self.yolox_exp,
            "eval": True,
            "train": True,
            "pretrained": True,
            "checkpoint": "yolox_l.pth",
            "training_times_in_a_single_step": 1,
        }
        (
            self.dataset_tasks,
            self.train_dataset_names,
            self.eval_dataset_names,
            self.dataset_training_times,
        ) = self._get_dataset_tasks()
        self.loss_ratios = self._get_loss_ratios()

        self._fake_img_metas = [
            {
                "cam2img": [[1, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]],
                "scale_factor": 1.0,
                "box_type_3d": CameraInstance3DBoxes,
            }
        ]  # define fake_img_metas for evaluating yolox seperately with traffic dataset (w/o fake_img_metas key)

        self.train_backbone_epoch = 18  # NOTE: when current epoch is smaller(<) than train_backbone_epoch the bachbone will be trained; else the backbone weights will be freezed
        self.total_step = self._calc_total_step(
            self._configure_train_dataloader()
        )  # NOTE: total_step must be set in multi-task exps

        # warm up lr configuration
        self.warmup_lr = 0
        self.basic_lr_per_img = 0.002 / (self.yolox_batch_size_per_device * self.total_devices)
        self.min_lr_ratio = 0.05
        self.weight_decay = 5e-4
        self.momentum = 0.9
        self.warmup_epochs = 1
        self.no_aug_epochs = 0
        self.ema = True

    def _get_loss_ratios(self):
        loss_ratios = {}
        for task, task_info in self.tasks.items():
            loss_ratios[task] = task_info["loss_ratio"]
        return loss_ratios

    def _get_dataset_tasks(self):
        dataset_tasks, train_dataset_names, eval_dataset_names, dataset_training_times = {}, [], [], {}
        for task, task_info in self.tasks.items():
            dataset_name = task_info["dataset"]
            if task_info["train"] and dataset_name not in train_dataset_names:
                train_dataset_names.append(dataset_name)
            if task_info["eval"] and dataset_name not in eval_dataset_names:
                eval_dataset_names.append(dataset_name)

            if dataset_name not in dataset_tasks:
                dataset_tasks[dataset_name] = [task]
            else:
                dataset_tasks[dataset_name].append(task)
            dataset_training_times[dataset_name] = task_info["training_times_in_a_single_step"]
        return dataset_tasks, train_dataset_names, eval_dataset_names, dataset_training_times

    def _calc_total_step(self, train_dataloader):
        if isinstance(train_dataloader, OrderedDict):
            # Multi task
            dataset_steps = {}
            for dataset_name, (dataloader, _) in train_dataloader.items():
                dataset_steps[dataset_name] = len(dataloader)
            total_step = max([v // self.dataset_training_times[k] for k, v in dataset_steps.items()])
        else:
            total_step = len(train_dataloader)
        return total_step

    def _configure_model(self):
        model = Fcos3dYoloxModel(fcos3d_config=Fcos3dModelConfigs, yolox_config=YoloxModelConfigs)
        if dist.get_rank() == 0:
            print(model)
        model.init_weights()

        def init_yolo(M):
            for m in M.modules():
                if isinstance(m, nn.BatchNorm2d):
                    m.eps = 1e-3
                    m.momentum = 0.03

        if self.tasks["yolox"]["pretrained"]:
            model.yolox_fpn.apply(init_yolo)
            model.load_yolox_pretrained_params(self.tasks["yolox"]["checkpoint"], to_cpu=True)
        else:
            model.yolox_fpn.apply(init_yolo)
            model.yolox_head.apply(init_yolo)
            model.yolox_head.initialize_biases(1e-2)
        return model

    @torch.no_grad()
    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(img=batch["img"], targets=batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        data, dataset_name, tasks = batch["data"], batch["dataset_name"], batch["tasks"]

        if dataset_name == "nuscenes":
            load_data_to_gpu(data)
            loss, ext_dict = self.model(img=data["img"], targets=data, tasks=tasks, loss_ratios=self.loss_ratios)
        if dataset_name == "traffic":
            images, targets, _, _ = data
            images = images.cuda()
            targets = targets.cuda()
            targets.requires_grad = False
            images, targets = self.preprocess(images, targets, self.input_size)
            labels = {"labels": targets}
            loss, ext_dict = self.model(img=images, targets=labels, tasks=tasks, loss_ratios=self.loss_ratios)
        return loss, ext_dict

    def preprocess(self, inputs, targets, tsize):
        scale_y = tsize[0] / self.input_size[0]
        scale_x = tsize[1] / self.input_size[1]
        if scale_x != 1 or scale_y != 1:
            inputs = nn.functional.interpolate(inputs, size=tsize, mode="bilinear", align_corners=False)
            targets[..., 1::2] = targets[..., 1::2] * scale_x
            targets[..., 2::2] = targets[..., 2::2] * scale_y
        return inputs, targets

    @torch.no_grad()
    def test_step(self, batch):
        data, dataset_name = batch["data"], batch["dataset_name"]

        if dataset_name == "nuscenes":
            load_data_to_gpu(data)
            data["img_metas"] = data["img_metas"][0]
            pred_fcos3d, pred_yolox = self.model(img=data["img"][0], targets=data)
            yolox_inputs, yolox_output_instances = None, None

        if dataset_name == "traffic":
            images, targets, info_imgs, ids = data
            yolox_inputs = [{"image_id": int(img_id)} for img_id in ids]
            labels = {"labels": targets, "img_metas": self._fake_img_metas}
            pred_fcos3d, pred_yolox = self.model(img=images, targets=labels)

            # post process of yolox
            yolox_outputs = postprocess(
                pred_yolox, self.yolox_exp.num_classes, self.yolox_exp.test_conf, self.yolox_exp.nmsthre
            )
            yolox_output_instances = self.yolox_exp.convert_to_coco_format(yolox_outputs, info_imgs, ids)

        return pred_fcos3d, (yolox_inputs, yolox_output_instances)

    def _configure_train_dataloader_nuscenes(self):
        dataset = NuScenesMonoDatasetWithEvalMulti(
            Fcos3dModelConfigs.class_names, Fcos3dModelConfigs.attribute_names, training=True, img_scale=self.img_scale
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.fcos3d_batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader_nuscenes(self):
        dataset = NuScenesMonoDatasetWithEvalMulti(
            Fcos3dModelConfigs.class_names, Fcos3dModelConfigs.attribute_names, training=False, img_scale=self.img_scale
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_train_dataloader(self):
        train_loaders = OrderedDict()
        for train_dataset_name in self.train_dataset_names:
            if train_dataset_name == "nuscenes":
                train_loader = self._configure_train_dataloader_nuscenes()
            else:
                train_loader = self.tasks[self.dataset_tasks[train_dataset_name][0]][
                    "exp"
                ]._configure_train_dataloader()
            train_loaders[train_dataset_name] = (
                train_loader,
                self.dataset_tasks[train_dataset_name],
            )  # (dataset loader, [task1, task2, ...])
        return train_loaders

    def _configure_val_dataloader(self):
        val_loaders = OrderedDict()
        for eval_dataset_name in self.eval_dataset_names:
            if eval_dataset_name == "nuscenes":
                val_loader = self._configure_val_dataloader_nuscenes()
            else:
                val_loader = self.tasks[self.dataset_tasks[eval_dataset_name][0]]["exp"]._configure_val_dataloader()
            val_loaders[eval_dataset_name] = (
                val_loader,
                self.dataset_tasks[eval_dataset_name],
            )  # (dataset loader, [task1, task2, ...])
        return val_loaders

    def _configure_test_dataloader(self):
        pass

    def freeze_backbone(self, model):
        for p in model.module.backbone.parameters():
            p.requires_grad = False

    def set_all_params_requires_grad(self, model):
        for p in model.parameters():
            p.requires_grad = True

    def _configure_optimizer(self):
        if self.warmup_epochs > 0:
            lr = self.warmup_lr
        else:
            lr = self.basic_lr_per_img * self.yolox_batch_size_per_device * self.total_devices

        pg0, pg1, pg2 = [], [], []  # optimizer parameter groups

        for k, v in self.model.named_modules():
            if hasattr(v, "bias") and isinstance(v.bias, nn.Parameter):
                if v.bias.requires_grad:
                    pg2.append(v.bias)  # biases
            if isinstance(v, nn.BatchNorm2d) or "bn" in k:
                if v.weight.requires_grad:
                    pg0.append(v.weight)  # no decay
            elif hasattr(v, "weight") and isinstance(v.weight, nn.Parameter):
                if v.weight.requires_grad:
                    pg1.append(v.weight)  # apply decay

        optimizer = torch.optim.SGD(pg0, lr=lr, momentum=self.momentum, nesterov=True)
        optimizer.add_param_group({"params": pg1, "weight_decay": self.weight_decay})  # add pg1 with weight_decay
        optimizer.add_param_group({"params": pg2})

        return optimizer

    def _configure_lr_scheduler(self):
        from perceptron.layers.lr_scheduler import YoloxWarmCosineLRScheduler

        lr = self.basic_lr_per_img * self.yolox_batch_size_per_device * self.total_devices
        scheduler = YoloxWarmCosineLRScheduler(
            self.optimizer,
            lr,
            # len(self.train_dataloader),
            self.total_step,
            self.max_epoch,
            warmup_epochs=self.warmup_epochs,
            warmup_lr_start=self.warmup_lr,
            no_aug_epochs=self.no_aug_epochs,
            min_lr_ratio=self.min_lr_ratio,
        )
        return scheduler

    def _configure_callbacks(self):
        if self.ema:
            return [ModelEMACallback(), MosaicCallback()]
        else:
            return [MosaicCallback()]


def is_parallel(model):
    """check if model is in parallel mode."""
    parallel_type = (
        nn.parallel.DataParallel,
        nn.parallel.DistributedDataParallel,
    )
    return isinstance(model, parallel_type)


class ModelEMA:
    """
    Model Exponential Moving Average from https://github.com/rwightman/pytorch-image-models
    Keep a moving average of everything in the model state_dict (parameters and buffers).
    This is intended to allow functionality like
    https://www.tensorflow.org/api_docs/python/tf/train/ExponentialMovingAverage
    A smoothed version of the weights is necessary for some training schemes to perform well.
    This class is sensitive where it is initialized in the sequence of model init,
    GPU assignment and distributed training wrappers.
    """

    def __init__(self, model, decay=0.9999, updates=0):
        """
        Args:
            model (nn.Module): model to apply EMA.
            decay (float): ema decay reate.
            updates (int): counter of EMA updates.
        """
        # Create EMA(FP32)
        if hasattr(model.module, "custom_deep_copy"):
            # To make deep copy work for multi-machine parallel model
            self.ema = model.module.custom_deep_copy(model.module if is_parallel(model) else model).eval()
        elif hasattr(model, "custom_deep_copy"):
            self.ema = model.custom_deep_copy(model.module if is_parallel(model) else model).eval()
        else:
            self.ema = deepcopy(model.module if is_parallel(model) else model).eval()
        self.updates = updates
        # decay exponential ramp (to help early epochs)
        self.decay = lambda x: decay * (1 - math.exp(-x / 2000))
        for p in self.ema.parameters():
            p.requires_grad_(False)

    def update(self, model):
        # Update EMA parameters
        with torch.no_grad():
            self.updates += 1
            d = self.decay(self.updates)

            msd = model.module.state_dict() if is_parallel(model) else model.state_dict()  # model state_dict
            for k, v in self.ema.state_dict().items():
                if v.dtype.is_floating_point:
                    v *= d
                    v += (1.0 - d) * msd[k].detach()


class ModelEMACallback:

    enabled_rank = None

    def before_train(self, trainer: Trainer):
        trainer.ema_model = ModelEMA(trainer.model, 0.9999)
        trainer.ema_model.updates = trainer.exp.total_step * trainer.epoch
        trainer.logger.info("ema model update {}".format(trainer.epoch))

    def after_step(self, trainer, step, data_dict, *args, **kwargs):
        trainer.ema_model.update(trainer.model)


class MosaicCallback:

    enabled_rank = None

    def before_epoch(self, trainer: Trainer, epoch: int):
        if epoch >= trainer.exp.max_epoch - trainer.exp.no_aug_epochs:
            trainer.logger.info("--->No mosaic aug now!")
            trainer.train_dataloader["traffic"][0].close_mosaic()
            trainer.model.module.yolox_head.use_l1 = True


if __name__ == "__main__":
    MultiCli(Exp).run()
