# encoding: utf-8
"""
@author: <PERSON><PERSON>
@contact: wang<PERSON><PERSON><PERSON>@megvii.com

# python3 perceptron/exps/seg2d/stdcnet.py -d 0-7 -b 12 -e 20 --sync_bn 8 --no-clearml --find_unused_parameters
# python3 perceptron/exps/seg2d/stdcnet.py -d 0-7 --eval --ckpt outputdir
# Experiment name: stdcnet, mIoU is: 0.4853735566139221
# | Class name     |    IoU |
# |----------------|--------|
# | road obstacles | 0.5455 |
# | car            | 0.7756 |
# | motorcycle     | 0.1966 |
# | bicycle        | 0.2412 |
# | person         | 0.0577 |
# | rider          | 0.1103 |
# | truck          | 0.5115 |
# | bus            | 0.5888 |
# | tricycle       | 0.0102 |
# | road           | 0.9235 |
# | sidewalk       | 0.3374 |
# | sky            | 0.9117 |
# | building       | 0.7435 |
# | vegetation     | 0.9033 |
# | traffic light  | 0.3345 |
# | pole           | 0.2728 |
# | traffic sign   | 0.6352 |
# | wall           | 0.2255 |
# | ego vehicle    | 0.8974 |
# | mIoU           | 0.4854 |
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
from torch.optim import SGD
from torch.utils.data.distributed import DistributedSampler

from data3d.datasets.apollo import Apollo
from data3d.transforms.t2d.transforms import ColorJitter  # noqa: E402,E261
from data3d.transforms.t2d.transforms import Compose, HorizontalFlip, RandomCrop, RandomScale
from perceptron.exps.base_exp import BaseExp
from perceptron.layers.lr_scheduler import WarmCosineLRScheduler
from perceptron.engine.cli import SegCli
from perceptron.models.seg2d.bisenet import BiSeNet
from perceptron.layers.losses.seg2d import OhemCELoss, DetailAggregateLoss
from perceptron.utils import torch_dist as dist


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=20, **kwargs):
        super(Exp, self).__init__(batch_size_per_device, total_devices, max_epoch)
        self.learning_rate = 1e-2
        self.data_loader_workers = 1
        self.backbone = "STDCNet813"
        self.n_classes = 19
        self.pretrain_model = ""
        self.use_boundary_2 = False
        self.use_boundary_4 = False
        self.use_boundary_8 = True
        self.use_boundary_16 = False
        self.use_conv_last = False
        self.grad_clip_value = 35
        self.dump_interval = 1

    def _configure_model(self):
        model = BiSeNet(
            backbone=self.backbone,
            n_classes=self.n_classes,
            pretrain_model=self.pretrain_model,
            use_boundary_2=self.use_boundary_2,
            use_boundary_4=self.use_boundary_4,
            use_boundary_8=self.use_boundary_8,
            use_boundary_16=self.use_boundary_16,
            use_conv_last=self.use_conv_last,
        )
        return model

    def training_step(self, batch):
        images, target = batch
        images = images.cuda()
        target = target.cuda()
        logits = self.model(images)
        if self.use_boundary_2 and self.use_boundary_4 and self.use_boundary_8:
            out, out16, out32, detail2, detail4, detail8 = logits
        elif not self.use_boundary_2 and self.use_boundary_4 and self.use_boundary_8:
            out, out16, out32, detail4, detail8 = logits
        elif not self.use_boundary_2 and not self.use_boundary_4 and self.use_boundary_8:
            out, out16, out32, detail8 = logits
        elif not self.use_boundary_2 and not self.use_boundary_4 and not self.use_boundary_8:
            out, out16, out32 = logits

        score_thres = 0.35
        n, c, h, w = images.shape
        n_min = n * h * w // 16
        ignore_idx = 255
        criteria_p = OhemCELoss(
            thresh=score_thres,
            n_min=n_min,
            ignore_lb=ignore_idx,
        )
        criteria_16 = OhemCELoss(
            thresh=score_thres,
            n_min=n_min,
            ignore_lb=ignore_idx,
        )
        criteria_32 = OhemCELoss(
            thresh=score_thres,
            n_min=n_min,
            ignore_lb=ignore_idx,
        )
        boundary_loss_func = DetailAggregateLoss()
        target = torch.squeeze(target, 1)
        lossp = criteria_p(out, target)
        loss2 = criteria_16(out16, target)
        loss3 = criteria_32(out32, target)

        boundery_bce_loss = 0.0
        boundery_dice_loss = 0.0
        if self.use_boundary_2:
            boundery_bce_loss2, boundery_dice_loss2 = boundary_loss_func(detail2, target)
            boundery_bce_loss += boundery_bce_loss2
            boundery_dice_loss += boundery_dice_loss2
        if self.use_boundary_4:
            boundery_bce_loss4, boundery_dice_loss4 = boundary_loss_func(detail4, target)
            boundery_bce_loss += boundery_bce_loss4
            boundery_dice_loss += boundery_dice_loss4
        if self.use_boundary_8:
            boundery_bce_loss8, boundery_dice_loss8 = boundary_loss_func(detail8, target)
            boundery_bce_loss += boundery_bce_loss8
            boundery_dice_loss += boundery_dice_loss8
        loss = lossp + loss2 + loss3 + boundery_bce_loss + boundery_dice_loss
        return loss

    def test_step(self, batch, ignore_label=255, scale=0.25):
        images, target = batch
        _, _, H, W = images.shape
        origin_size = [H, W]
        model_size = [int(H * scale), int(W * scale)]
        images = images.cuda()
        target = target.squeeze(1).cuda()
        images = F.interpolate(
            images,
            model_size,
            mode="bilinear",
            align_corners=True,
        )
        logits = self.model(images)[0]
        origin_logits = F.interpolate(
            logits,
            size=origin_size,
            mode="bilinear",
            align_corners=True,
        )
        probs = torch.softmax(origin_logits, dim=1)
        preds = torch.argmax(probs, dim=1)
        keep = target != ignore_label
        count = target[keep] * self.n_classes + preds[keep]
        hist = torch.bincount(count, minlength=self.n_classes ** 2).view(self.n_classes, self.n_classes).float()
        return hist

    def _configure_train_dataloader(self):
        from perceptron.data.sampler import InfiniteSampler

        transform = Compose(
            [
                ColorJitter(
                    brightness=0.5,
                    contrast=0.5,
                    saturation=0.5,
                ),
                HorizontalFlip(),
                RandomScale([0.125, 0.25, 0.375, 0.5, 0.675, 0.75, 0.875, 1.0, 1.25, 1.5]),
                RandomCrop([846, 677]),
            ]
        )
        totensor = transforms.Compose(
            [
                transforms.ToTensor(),
                transforms.Normalize(
                    [0.485, 0.456, 0.406],
                    [0.229, 0.224, 0.225],
                ),
            ]
        )
        train_set = Apollo(
            mode="train",
            ignore_lb=255,
            transform=transform,
            totensor=totensor,
        )
        if dist.is_distributed():
            sampler = InfiniteSampler(len(train_set), seed=self.seed if self.seed else 0)
        else:
            sampler = None
        train_loader = torch.utils.data.DataLoader(
            train_set,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=self.data_loader_workers,
            shuffle=sampler is None,
            drop_last=True,
            sampler=sampler,
        )
        return train_loader

    def _configure_val_dataloader(self):
        totensor = transforms.Compose(
            [
                transforms.ToTensor(),
                transforms.Normalize(
                    [0.485, 0.456, 0.406],
                    [0.229, 0.224, 0.225],
                ),
            ]
        )
        val_set = Apollo(
            mode="val",
            ignore_lb=255,
            transform=None,
            totensor=totensor,
        )
        if dist.is_distributed():
            sampler = DistributedSampler(val_set, shuffle=False)
        else:
            sampler = None
        val_loader = torch.utils.data.DataLoader(
            val_set,
            batch_size=2,
            pin_memory=True,
            num_workers=4,
            shuffle=False,
            drop_last=False,
            sampler=sampler,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_optimizer(self):
        lr = 0.003
        optimizer = SGD(
            self.model.parameters(),
            lr=lr,
            momentum=0.9,
            weight_decay=5e-4,
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = WarmCosineLRScheduler(
            self.optimizer,
            self.learning_rate,
            len(self.train_dataloader),
            self.max_epoch,
        )
        return scheduler


if __name__ == "__main__":
    SegCli(Exp).run()
