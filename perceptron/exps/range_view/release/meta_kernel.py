import numpy as np
import torch
import torch.nn as nn
from perceptron.exps.range_view.release.utils import im2col_indices_gpu as im2col


class MetaKernel(nn.Module):
    def __init__(
        self,
        num_batch,
        feat_height,
        feat_width,
        channel_list,
        coord_channels=3,
        use_norm=False,
        fp16=False,
        num_frame=1,
    ):
        super(MetaKernel, self).__init__()
        self.num_batch = num_batch
        self.H = feat_height
        self.W = feat_width
        self.fp16 = fp16
        self.num_frame = num_frame

        self.mlp_module = self.mlp_init(coord_channels, channel_list, use_norm)

    # mlp initial
    def mlp_init(self, coord_channels, channel_list, use_norm: bool):
        assert isinstance(channel_list, list)
        layers = []
        channel_list = [coord_channels] + channel_list

        for i in range(len(channel_list) - 1):
            conv = nn.Conv2d(
                in_channels=channel_list[i],
                out_channels=channel_list[i + 1],
                kernel_size=1,
                stride=1,
                padding=0,
                dilation=1,
                bias=False,
            )
            layers.append(conv)

            if i < len(channel_list) - 2:
                if use_norm:
                    norm = nn.BatchNorm2d(num_features=channel_list[i + 1])
                    layers.append(norm)
                relu = nn.ReLU(inplace=True)
                layers.append(relu)
        return nn.Sequential(*layers)

    # 获得栅格坐标
    def sample(self, data, kernel_size):
        """Sample kernel_size coordinate.
        Args:
            data: num_batch, num_channel_in, H, W

        Returns:
            coord_sample_data: num_batch, num_channel_in * kernel_size * kernel_size, H, W
        """
        # TODO: apply `im2col` C++  operator later

        if isinstance(kernel_size, int):
            kernel_size = (kernel_size, kernel_size)

        kernel_height, kernel_width = kernel_size
        sample_output = im2col(data, kernel_height, kernel_width, padding=1, stride=1)
        return sample_output

    # 相对坐标
    def relative_coord(self, sample_coord, center_coord, num_channel_in, kernel_size):
        """
        Args:
            sample_coord: num_batch, num_channel_in * kernel_size * kernel_size, H, W
            center_coord: num_batch, num_channel_in, H, W
            num_channel_in: int
            kernel_size: int

        Returns:
            rel_coord: num_batch, num_channel_in, kernel_size * kernel_size, H, W
        """
        sample_reshape = sample_coord.reshape(self.num_batch, num_channel_in, kernel_size * kernel_size, self.H, self.W)
        if isinstance(sample_coord, np.ndarray):
            sample_coord = torch.from_numpy(sample_coord)

        center_coord_expand = torch.unsqueeze(center_coord, dim=2)
        rel_coord = torch.sub(sample_reshape, center_coord_expand)
        return rel_coord

    # 相对坐标，过mlp，得到权重weight
    def mlp(self, data, in_channels, channel_list=None, b_mul=1):
        """
        Args:
            data: num_batch, num_channel_in * kernel_size * kernel_size, H, W
            in_channels: int
            channel_list: List[int]
            b_mul: int default=1

        Returns:
            mlp_output_reshape: num_batch, out_channels, kernel_size * kernel_size, H, W
        """
        x = data.reshape(self.num_batch * b_mul, in_channels, -1, self.W)
        x = self.mlp_module(x)
        mlp_output_reshape = x.reshape(self.num_batch * b_mul, channel_list[-1], -1, self.H, self.W)
        return mlp_output_reshape

    def forward(self, data, coord_data, data_channels, coord_channels, channel_list, conv1_filter, kernel_size=3):
        """
        Args:
            data:           num_batch, num_channel_in, H, W
            coord_data:     num_batch, 3, H, W
            data_channels:  num_channel_in
            coord_channels: 3
            channel_list:   List[int]
            conv1_filter:   int
            kernel_size:    int default=3
        Returns:
            conv1: num_batch, conv1_filter, H, W
        """

        if self.fp16:
            pass

        coord_sample_data = self.sample(coord_data, kernel_size)
        rel_coord = self.relative_coord(coord_sample_data, coord_data, coord_channels, kernel_size)
        weights = self.mlp(rel_coord, in_channels=coord_channels, channel_list=channel_list)

        data_sample = self.sample(data, kernel_size)
        data_sample_reshape = data_sample.reshape(
            self.num_batch, data_channels, kernel_size * kernel_size, self.H, self.W
        )

        output = data_sample_reshape * weights
        output_reshape = output.reshape(self.num_batch, -1, self.H, self.W)

        return output_reshape
