import torch
import torch.nn as nn
from perceptron.exps.range_view.release.meta_kernel import MetaK<PERSON>l
from perceptron.utils.det3d_utils.initialize_utils import kaiming_init


class BasicBlock(nn.Module):
    def __init__(self, model_config, data_config, in_channels, filter, stride, dilate, name: str, proj: bool):
        super(BasicBlock, self).__init__()
        self.model_config = model_config
        self.data_config = data_config

        if name not in self.model_config.meta_kernel_units:
            self.conv1 = nn.Conv2d(
                in_channels=in_channels,
                out_channels=filter,
                kernel_size=3,
                stride=1,
                padding=dilate,
                dilation=dilate,
                bias=False,
            )
            self.bn1 = nn.BatchNorm2d(num_features=filter)

        self.conv2 = nn.Conv2d(
            in_channels=filter,
            out_channels=filter,
            kernel_size=3,
            stride=stride,
            padding=dilate,
            dilation=dilate,
            bias=False,
        )
        self.bn2 = nn.BatchNorm2d(num_features=filter)

        # skip connection
        if proj:
            self.sc_conv = nn.Conv2d(
                in_channels=in_channels, out_channels=filter, kernel_size=1, stride=stride, padding=0, bias=False
            )
            self.sc_bn = nn.BatchNorm2d(num_features=filter)

        if name in model_config.meta_kernel_units:
            # meta_kernel --- init
            conv_param = model_config.meta_kernel_units[name]
            feature_stride = conv_param["stride"]
            self.meta_kernel = MetaKernel(
                num_batch=data_config.batch_size,
                feat_height=data_config.range_image_shape_hw[0],
                feat_width=data_config.range_image_shape_hw[1] // feature_stride,
                channel_list=conv_param["channel_list"],
                coord_channels=conv_param["coord_channels"],
                use_norm=False,
            )
            # meta_kernel --- aggregation layer
            meta_channels = conv_param["data_channels"]
            meta_kernel_size = conv_param["kernel_size"]
            meta_agg_in_channels = meta_channels * meta_kernel_size * meta_kernel_size
            self.agg_bn1 = nn.BatchNorm2d(num_features=meta_agg_in_channels)
            self.agg_conv = nn.Conv2d(
                in_channels=meta_agg_in_channels, out_channels=filter, kernel_size=1, stride=1, padding=0, bias=False
            )
            self.agg_bn2 = nn.BatchNorm2d(num_features=filter)

        self.relu = nn.ReLU(inplace=True)
        self.filter = filter
        self.proj = proj
        self.name = name

    def meta_kernel_conv(self, x, name, filter_num, coord_dict):
        if name in self.model_config.meta_kernel_units:
            conv_param = self.model_config.meta_kernel_units[name]

            feature_stride = conv_param["stride"]
            coord = coord_dict[f"coord_s{feature_stride}"]  # get coor, shape: num_batch, 3, H, W
            conv_mlp = self.meta_kernel(
                data=x,
                coord_data=coord,
                data_channels=conv_param["data_channels"],
                coord_channels=conv_param["coord_channels"],
                channel_list=conv_param["channel_list"],
                conv1_filter=filter_num,
                kernel_size=conv_param["kernel_size"],
            )

            # aggregation convolution
            bn_mlp = self.agg_bn1(conv_mlp)
            relu_mlp = self.relu(bn_mlp)
            conv1 = self.agg_conv(relu_mlp)
            bn1 = self.agg_bn2(conv1)
            relu1 = self.relu(bn1)
            is_in_name_list = True
        else:
            relu1 = None
            is_in_name_list = False

        return relu1, is_in_name_list

    def forward(self, x, coord_dict):
        relu1, is_in_name_list = self.meta_kernel_conv(x, self.name, self.filter, coord_dict)

        if not is_in_name_list:
            conv1 = self.conv1(x)
            bn1 = self.bn1(conv1)
            relu1 = self.relu(bn1)

        conv2 = self.conv2(relu1)
        bn2 = self.bn2(conv2)

        if self.proj:
            shortcut = self.sc_conv(x)
            shortcut = self.sc_bn(shortcut)
        else:
            shortcut = x

        eltwise = bn2 + shortcut
        out = self.relu(eltwise)

        return out


class DeConv(nn.Module):
    def __init__(self, in_channels, filter, deconv_kernel, deconv_stride, deconv_pad):
        super(DeConv, self).__init__()
        self.deconv = nn.ConvTranspose2d(
            in_channels=in_channels,
            out_channels=filter,
            kernel_size=deconv_kernel,
            stride=deconv_stride,
            padding=deconv_pad,
        )
        self.norm = nn.BatchNorm2d(num_features=filter)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        data_upsample = self.deconv(x)
        data_upsample = self.norm(data_upsample)
        data_upsample = self.relu(data_upsample)
        return data_upsample


class ResStage(nn.Module):
    def __init__(self, model_config, data_config, in_channels, name, num_block, filter, stride, dilate):
        super(ResStage, self).__init__()
        if isinstance(stride, int):
            stride = (stride, stride)

        layers = nn.ModuleList()
        basicblock1 = BasicBlock(
            model_config, data_config, in_channels, filter, stride, dilate, name=f"{name}_unit1", proj=True
        )
        layers.append(basicblock1)
        for i in range(2, num_block + 1):
            basicblock2 = BasicBlock(
                model_config, data_config, filter, filter, 1, dilate, name=f"{name}_unit{i}", proj=False
            )
            layers.append(basicblock2)

        self.res_layers = layers

    def forward(self, x, coord_dict):
        for res_layer in self.res_layers:
            x = res_layer(x, coord_dict)
        return x


class AggStage(nn.Module):
    def __init__(
        self,
        model_config,
        data_config,
        in_channels,
        name,
        num_block,
        filter,
        stride,
        dilate,
        deconv_kernel,
        deconv_stride,
        deconv_pad,
    ):
        super(AggStage, self).__init__()
        self.deconv = DeConv(in_channels, filter, deconv_kernel, deconv_stride, deconv_pad)
        self.res_layer = ResStage(model_config, data_config, filter, name, num_block, filter, stride, dilate)

    def forward(self, data_const, data_upsample, coord_dict):
        data_upsample = self.deconv(data_upsample)
        eltwise = data_const + data_upsample
        stage_out = self.res_layer(eltwise, coord_dict)
        return stage_out


class DLABackbone(nn.Module):
    def __init__(self, model_config, data_config):
        super(DLABackbone, self).__init__()
        self.model_config = model_config
        self.data_config = data_config

        num_block = model_config.num_block
        num_filter = model_config.num_filter
        self.num_bev_features = model_config.meta_out_channels[0]

        num_stride = model_config.num_stride
        num_dilate = model_config.num_dilate
        deconv_kernel = model_config.deconv_kernel
        deconv_padding = model_config.deconv_padding

        self.res1 = self.make_res_layer(
            data_config.range_image_channels,
            "res1",
            num_block["res1"],
            num_filter["res1"],
            num_stride["res1"],
            num_dilate["res1"],
        )
        self.res2a = self.make_res_layer(
            num_filter["res1"],
            "res2a",
            num_block["res2a"],
            num_filter["res2a"],
            num_stride["res2a"],
            num_dilate["res2a"],
        )
        self.res2 = self.make_res_layer(
            num_filter["res2a"], "res2", num_block["res2"], num_filter["res2"], num_stride["res2"], num_dilate["res2"]
        )
        self.res3a = self.make_res_layer(
            num_filter["res2"],
            "res3a",
            num_block["res3a"],
            num_filter["res3a"],
            num_stride["res3a"],
            num_dilate["res3a"],
        )
        self.res3 = self.make_res_layer(
            num_filter["res3a"], "res3", num_block["res3"], num_filter["res3"], num_stride["res3"], num_dilate["res3"]
        )
        self.agg2 = self.make_agg_layer(
            num_filter["res3"],
            "agg2",
            num_block["agg2"],
            num_filter["agg2"],
            1,
            dilate=num_dilate["agg2"],
            deconv_kernel=deconv_kernel["agg2"],
            # deconv_stride=(1, 4),
            deconv_stride=num_stride["agg2"],
            deconv_pad=deconv_padding["agg2"],
        )
        self.agg1 = self.make_agg_layer(
            num_filter["res2"],
            "agg1",
            num_block["agg1"],
            num_filter["agg1"],
            1,
            dilate=num_dilate["agg1"],
            deconv_kernel=deconv_kernel["agg1"],
            # deconv_stride=(1, 4),
            deconv_stride=num_stride["agg1"],
            deconv_pad=deconv_padding["agg1"],
        )
        self.agg2a = self.make_agg_layer(
            num_filter["agg2"],
            "agg2a",
            num_block["agg2a"],
            num_filter["agg2a"],
            1,
            dilate=num_dilate["agg2a"],
            deconv_kernel=deconv_kernel["agg2a"],
            # deconv_stride=(1, 2),
            deconv_stride=num_stride["agg2a"],
            deconv_pad=deconv_padding["agg2a"],
        )
        self.agg3 = self.make_agg_layer(
            num_filter["agg2a"],
            "agg3",
            num_block["agg3"],
            num_filter["agg3"],
            1,
            dilate=num_dilate["agg3"],
            deconv_kernel=deconv_kernel["agg3"],
            # deconv_stride=(1, 2),
            deconv_stride=num_stride["agg3"],
            deconv_pad=deconv_padding["agg3"],
        )

        self.__init_weights__()

    def __init_weights__(self):
        if self.model_config.model_init_func == "kaiming_init":
            print("kaiming_init for backbone.")
            for m in self.modules():
                if isinstance(
                    m, (nn.Conv2d, nn.Conv1d, nn.Conv3d, nn.ConvTranspose1d, nn.ConvTranspose2d, nn.ConvTranspose3d)
                ):
                    kaiming_init(m)
        elif self.model_config.model_init_func == "none":
            print("no init func for backbone.")
        else:
            raise NotImplementedError("unsupported model_init_func: ", self.model_config.model_init_func)

    def make_res_layer(self, in_channels, name, num_block, filter, stride, dilate):
        res_layer = ResStage(self.model_config, self.data_config, in_channels, name, num_block, filter, stride, dilate)
        return res_layer

    def make_agg_layer(
        self, upsample_inchannels, name, num_block, filter, stride, dilate, deconv_kernel, deconv_stride, deconv_pad
    ):
        agg_layer = AggStage(
            self.model_config,
            self.data_config,
            upsample_inchannels,
            name,
            num_block,
            filter,
            stride,
            dilate,
            deconv_kernel,
            deconv_stride,
            deconv_pad,
        )
        return agg_layer

    def forward(self, x, coord_dict, mode="dla"):
        """
        Args:
            mode:   - fpn: original fpn code
                    - dla: RangeDet paper code
        Returns:
            list    - output_dict[1]: torch.Size([batch, 72, height, width])
                    - output_dict[2]: torch.Size([batch, 64, height, width/2])
                    - output_dict[4]: torch.Size([batch, 128, height, width/4])
        """
        if mode == "dla":
            res1 = self.res1(x, coord_dict)
            res2a = self.res2a(res1, coord_dict)
            res2 = self.res2(res2a, coord_dict)
            res3a = self.res3a(res2, coord_dict)
            res3 = self.res3(res3a, coord_dict)
            agg2 = self.agg2(res2, res3, coord_dict)
            agg1 = self.agg1(res1, res2, coord_dict)
            agg2a = self.agg2a(res2a, agg2, coord_dict)
            agg3 = self.agg3(agg1, agg2a, coord_dict)
        elif mode == "fpn":
            # Bottom-up
            pass
            # Top-down

        if hasattr(self.model_config, "add_data_sc") and self.model_config.add_data_sc:
            agg3 = torch.cat((x, agg3), dim=1)

        output_dict = {1: agg3, 2: agg2a, 4: agg2, 16: res3}

        if hasattr(self.model_config, "fpn_strides"):
            return [output_dict[s] for s in self.model_config.fpn_strides]
        else:
            return [
                agg3,
            ]


if __name__ == "__main__":
    pass
