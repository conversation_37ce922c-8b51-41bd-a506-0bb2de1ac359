# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/range_view/release/rv_base_exp_car2.py  -b 1 -e 80 --sync_bn 3 --no-clearml
# rlaunch --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no --max-wait-time=24h -- python3 perceptron/exps/range_view/release/rv_base_exp_car2.py  -b 1 -e 80 --sync_bn 1 --no-clearml

import easydict
import numpy as np
from torch.utils.data import DataLoader, DistributedSampler
from perceptron.data.det3d.dataset.private_data.dataset_rv import PrivateRangeDatasetWithEval
from perceptron.data.sampler import InfiniteSampler
from perceptron.exps.base_exp import BaseExp
from perceptron.engine.cli import Det3DCli
from perceptron.models.det3d.base_3d_detector import Base3DDetector
from perceptron.layers.head.range_view import RVSegTopkAssigner, CenterPointGenProposals_RV, CenterHead_RV
from perceptron.layers.losses.det3d import CenterNetRegLoss, FocalLoss
import torch.nn.functional as F
from perceptron.exps.range_view.release.utils import cal_coord_in_stride
from perceptron.exps.range_view.release.dla_backbone import DLABackbone
from perceptron_ops.range_view_converter import RangeViewConverter
import torch
from perceptron.exps.det3d.release.private_dataset_path import TRAIN_CAR2_DATALIST, EVAL_CAR2_BMK_CHENGQU
from perceptron.layers.lr_scheduler import OnecycleLRScheduler
from perceptron.layers.optimizer.det3d import OptimWrapper
from perceptron.models.det3d import load_data_to_gpu
import torch.optim as optim
from functools import partial


class DataConfigs:
    point_cloud_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 5
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4

    rv_input_channel = 7

    # DLA BACKBONE
    range_image_channels = 7
    range_image_shape_hw = (80, 1808)

    batch_size = 1


class PrivateModelConfig:
    class_name = (
        "car",
        "truck",
        "construction_vehicle",
        "bus",
        "motorcycle",
        "bicycle",
        "tricycle",
        "cyclist",
        "pedestrian",
    )

    # dense head
    densehead_dataset_name = "once"
    densehead_mode = "3d"
    densehead_tasks = [
        easydict.EasyDict(
            {
                "num_class": 9,
                "class_names": [
                    "car",
                    "truck",
                    "construction_vehicle",
                    "bus",
                    "motorcycle",
                    "bicycle",
                    "tricycle",
                    "cyclist",
                    "pedestrian",
                ],
            }
        ),
    ]  # , "other", "ghost", "masked_area")

    densehead_share_conv_channel = 64
    densehead_init_bias = -2.19
    densehead_common_heads = easydict.EasyDict(
        {
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
        }
    )
    densehead_loss_code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    densehead_loss_loc_weight = 0.25
    densehead_out_size_factor = 4  # 8
    densehead_upsample_for_pedestrian = False

    # target assigner
    target_assigner_dense_reg = 1
    target_assigner_gaussian_overlap = 0.1
    target_assigner_min_radius = 2
    target_assigner_mapping = easydict.EasyDict(
        {
            "car": 1,
            "truck": 2,
            "construction_vehicle": 3,
            "bus": 4,
            "motorcycle": 5,
            "bicycle": 6,
            "tricycle": 7,
            "cyclist": 8,
            "pedestrian": 9,
        }
    )
    target_assigner_topk = 20
    target_assigner_max_objs = 5000
    target_assigner_alpha = 0.25
    target_assigner_gamma = 2
    target_assigner_no_log = False

    # proposal config
    proposal_post_center_limit_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
    proposal_max_per_img = 500
    proposal_score_threshold = 0.1
    proposal_pc_range = [-75.2, -75.2]
    proposal_voxel_size = [0.1, 0.1]

    # for nms train
    nms_iou_threshold_train = 0.8
    nms_pre_max_size_train = 1500
    nms_post_max_size_train = 80

    # for nms test
    nms_iou_threshold_test = 0.1
    nms_pre_max_size_test = 1500
    nms_post_max_size_test = 100

    # post process
    post_process_recall_thresh_list = [0.3, 0.5, 0.7]
    post_process_score_thresh = 0.1
    post_process_output_raw_score = False
    post_process_eval_metric = "once"

    post_process_multi_classes_nms = False
    post_process_nms_type = "nms_gpu"
    post_process_nms_thresh = 0.01
    post_process_nms_pre_maxsize = 4096
    post_process_nms_post_maxsize = 500

    # ===== DLA BACKBONE =====#
    # meta kernel
    meta_kernel_units = {}  # 当前默认不使用meta kernerl(太慢)
    # meta_kernel_units = {
    #     "res1_unit2": dict(
    #         stride=1,
    #         meta_func_param="meta_baseline_bias",
    #         data_channels=64,
    #         coord_channels=3,
    #         channel_list=[32, 64],
    #         kernel_size=3,
    #     )
    # }

    num_block = {
        "res1": 2,
        "res2a": 3,
        "res2": 3,
        "res3a": 5,
        "res3": 5,
        "agg1": 2,
        "agg2": 2,
        "agg2a": 1,
        "agg3": 2,
    }

    num_filter = {
        "res1": 64,
        "res2a": 64,
        "res2": 128,
        "res3a": 128,
        "res3": 128,
        "agg1": 64,
        "agg2": 128,
        "agg2a": 64,
        "agg3": 64,
    }

    num_dilate = {
        "res1": 1,
        "res2a": 1,
        "res2": 1,
        "res3a": 1,
        "res3": 1,
        "agg1": 1,
        "agg2": 1,
        "agg2a": 1,
        "agg3": 1,
    }
    num_stride = {
        "res1": (1, 1),
        "res2a": (1, 2),  # stride 1, 2
        "res2": (1, 2),  # stride 1, 4
        "res3a": (1, 2),  # stride 1, 8
        "res3": (1, 2),  # stride 2, 16
        "agg1": (1, 4),  # res1 & res2
        "agg2": (1, 4),  # res2 & res3
        "agg2a": (1, 2),  # res2a & agg2
        "agg3": (1, 2),  # agg1 & agg2a
    }

    deconv_kernel = {
        "agg1": (3, 8),
        "agg2": (3, 8),
        "agg2a": (3, 4),
        "agg3": (3, 4),
    }
    deconv_padding = {
        "agg1": (1, 2),
        "agg2": (1, 2),
        "agg2a": (1, 1),
        "agg3": (1, 1),
    }
    add_data_sc = True
    if add_data_sc:
        # 72, 64, 128
        meta_out_channels = (
            num_filter["agg3"] + DataConfigs().range_image_channels,
            num_filter["agg2a"],
            num_filter["agg2"],
        )
    else:
        # 64, 64, 128
        meta_out_channels = (num_filter["agg3"], num_filter["agg2a"], num_filter["agg2"])

    # ==== RV Details ==== #
    rv_mv_origin = [0.10730886, 1.71309435, 1.65211558]

    # must be radians. Here we use 80 beams setting, achieving comparable performance with 177 beams setting.
    incl_array = np.array(
        [
            -0.47849238,
            -0.25817516,
            -0.23978123,
            -0.22953391,
            -0.22087494,
            -0.21288204,
            -0.20535026,
            -0.1981259,
            -0.19100402,
            -0.18418956,
            -0.17752881,
            -0.17097053,
            -0.16451472,
            -0.15821263,
            -0.15206423,
            -0.14606956,
            -0.14027983,
            -0.13454133,
            -0.12880285,
            -0.12316683,
            -0.11758204,
            -0.11194602,
            -0.10631,
            -0.10062274,
            -0.09503795,
            -0.08976059,
            -0.08463693,
            -0.07956451,
            -0.07469704,
            -0.06993204,
            -0.06526951,
            -0.0607607,
            -0.05640559,
            -0.05220419,
            -0.04805403,
            -0.0439551,
            -0.03990742,
            -0.03591096,
            -0.03191451,
            -0.02791806,
            -0.02392161,
            -0.01987392,
            -0.015775,
            -0.01167607,
            -0.00752591,
            -0.00337575,
            0.00072318,
            0.00487334,
            0.00907474,
            0.01332737,
            0.01763124,
            0.02188388,
            0.02613651,
            0.03044038,
            0.03479549,
            0.0393043,
            0.04396683,
            0.0486806,
            0.05339436,
            0.05810812,
            0.06292436,
            0.06789431,
            0.07296672,
            0.07819286,
            0.08362393,
            0.08936243,
            0.09561329,
            0.10242774,
            0.10990828,
            0.1180549,
            0.12691882,
            0.13696119,
            0.14848942,
            0.16181092,
            0.17723313,
            0.19378254,
            0.21161285,
            0.23174882,
            0.25752082,
            0.3056832,
        ]
    )

    incl_array = np.sort(incl_array)[::-1]  # decreasing order

    rv_ncols = 1800
    rv_col_offset = 1350  # linely incresing with rv_ncols
    model_init_func = "none"


# CenterNet for RV
class CenterNet_RV(Base3DDetector):
    def __init__(self, model_config, data_config):
        super().__init__(num_class=len(model_config.class_name), class_names=model_config.class_name)
        self.model_config = model_config
        self.data_config = data_config

        self.backbone_2d = self.build_backbone_2d()
        self.dense_head = self.build_dense_head()
        self.before_backbone_layer = torch.nn.Sequential()

    def build_backbone_2d(self):
        backbone_2d_module = DLABackbone(self.model_config, self.data_config)
        return backbone_2d_module

    def build_dense_head(self):
        target_assigner = RVSegTopkAssigner(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )

        proposal_layer = CenterPointGenProposals_RV(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
        )

        dense_head_module = CenterHead_RV(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit", FocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma)
            )
            m.add_module("crit_reg", CenterNetRegLoss())

        _build_losses(dense_head_module)

        return dense_head_module

    def before_backbone_step(self, rv_images):
        return self.before_backbone_layer(rv_images)

    def forward(self, batch_dict):
        points = batch_dict["points"]  # list
        rv_images = []
        for point in points:
            assert point.ndim == 2, f"ndim should be 2 but {point.ndim}"
            point = point.unsqueeze(0)
            # points = torch.stack(points)  # B, N, C
            incl_array = torch.from_numpy(self.model_config.incl_array.copy()).float().to(point.device)
            rv_image = RangeViewConverter.apply(
                point, incl_array, [-np.pi, np.pi], 2 * np.pi / self.model_config.rv_ncols
            )  # BHWC
            rv_images.append(rv_image)
        rv_images = torch.cat(rv_images, dim=0)
        rv_images = rv_images.permute(0, 3, 1, 2)  # BCHW

        # 水平移动
        rv_col_offset = self.model_config.rv_col_offset
        nr_azim = self.model_config.rv_ncols

        col_indexs = torch.cat((torch.arange(rv_col_offset, nr_azim), torch.arange(0, rv_col_offset)), axis=0).long()

        rv_images = rv_images[:, :, :, col_indexs]
        rv_images = rv_images.contiguous()

        rv_coords = rv_images[:, 3:6, :, :]  # B3HW (xyz)
        rv_range = rv_images[:, 0:1, :, :]  # B1HW
        gt_boxes = batch_dict.get("gt_boxes", None)

        # padding
        pad = (self.data_config.range_image_shape_hw[1] - rv_images.shape[-1]) // 2  # 4

        incl_array = self.model_config.incl_array

        if pad > 0:
            rv_images = F.pad(rv_images, (pad, pad, 0, 0), mode="constant", value=0)
            rv_coords = F.pad(rv_coords, (pad, pad, 0, 0), mode="constant", value=0)

        # DLA backbone
        coord_dict = cal_coord_in_stride(rv_coords)

        rv_images = self.before_backbone_layer(rv_images)
        spatial_features_2d = self.backbone_2d(rv_images, coord_dict)[0]

        if pad > 0:  # delete padding part
            spatial_features_2d = spatial_features_2d[:, :, :, pad : spatial_features_2d.shape[3] - pad]
            rv_coords = rv_coords[:, :, :, pad : rv_coords.shape[3] - pad]
            rv_coords = rv_coords.contiguous()

        forward_ret_dict = self.dense_head(
            rv_coords,
            spatial_features_2d,
            gt_boxes,
            incl_array=incl_array,
            ncols=self.model_config.rv_ncols,
            rv_col_offset=self.model_config.rv_col_offset,
            rv_mv_origin=self.model_config.rv_mv_origin,
            rv_range=rv_range,
        )

        if self.training:
            loss_rpn, tb_dict = self.dense_head.get_loss(forward_ret_dict)
            tb_dict.update({"loss_rpn": loss_rpn.item()})
            ret_dict = {"loss": loss_rpn}
            return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 5
        self.lr = 0.003

    def forward(self, batch):
        load_data_to_gpu(batch)
        pred_dicts, recall_dicts = self.model(batch)
        return pred_dicts, recall_dicts

    def training_step(self, batch):
        load_data_to_gpu(batch)
        ret_dict, tb_dict, _ = self.model(batch)
        loss = ret_dict["loss"].mean()
        return loss, tb_dict

    def test_step(self, batch):
        load_data_to_gpu(batch)
        return self.model(batch)

    def _configure_train_dataloader(self):
        model_config = PrivateModelConfig()

        dataset = PrivateRangeDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=TRAIN_CAR2_DATALIST,
            class_names=model_config.class_name,
            training=True,
            lidar_key_list=["fuser_lidar"],
            rv_mv_origin=model_config.rv_mv_origin,
            nr_azim=model_config.rv_ncols,
            rv_col_offset=model_config.rv_col_offset,
            incl_array=model_config.incl_array,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader

    def _configure_val_dataloader(self):
        model_config = PrivateModelConfig()

        dataset = PrivateRangeDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=EVAL_CAR2_BMK_CHENGQU,
            class_names=PrivateModelConfig().class_name,
            training=False,
            lidar_key_list=["fuser_lidar"],
            rv_mv_origin=model_config.rv_mv_origin,
            nr_azim=model_config.rv_ncols,
            rv_col_offset=model_config.rv_col_offset,
            incl_array=model_config.incl_array,
        )
        sampler = DistributedSampler(dataset, shuffle=False, drop_last=False)
        val_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and False,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return val_loader

    def _configure_test_dataloader(self):
        pass

    def _configure_model(self):
        model = CenterNet_RV(model_config=PrivateModelConfig(), data_config=DataConfigs())
        print(model)
        return model

    def _configure_optimizer(self):
        def children(m: torch.nn.Module):
            return list(m.children())

        def num_children(m: torch.nn.Module) -> int:
            return len(children(m))

        def flatten_model(m):
            return sum(map(flatten_model, m.children()), []) if num_children(m) else [m]  # noqa

        def get_layer_groups(m):
            return [torch.nn.Sequential(*flatten_model(m))]  # noqa

        optimizer_func = partial(optim.Adam, betas=(0.9, 0.99))
        optimizer = OptimWrapper.create(
            optimizer_func, self.lr, get_layer_groups(self.model), wd=0.01, true_wd=True, bn_wd=True
        )
        return optimizer

    def _configure_lr_scheduler(self):
        scheduler = OnecycleLRScheduler(
            optimizer=self.optimizer,
            lr=self.lr,
            iters_per_epoch=len(self.train_dataloader),
            total_epochs=self.max_epoch,
            moms=[0.95, 0.85],
            div_factor=10,
            pct_start=0.4,
        )
        return scheduler


if __name__ == "__main__":
    Det3DCli(Exp).run()
