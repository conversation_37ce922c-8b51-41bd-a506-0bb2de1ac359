# rlaunch --cpu=32 --gpu=8 --memory=200000 --charged-group=transformer --preemptible no --max-wait-duration=24h --positive-tags 2080ti -- python3 perceptron/exps/range_view/release/rv_base_exp_car2_bs16_seg_invweight_3w2_car1ft40.py  -b 2 -e 40 --sync_bn 1 --no-clearml --pretrained_model s3://cxw3ddet/longterm/rv_checkpoints/car1_pretrained_model.pth
import easydict
from perceptron.exps.range_view.release.rv_base_exp_car2 import (
    CenterNet_RV as BaseDetector,
    PrivateModelConfig as BaseModelConfig,
    DataConfigs,
    Exp as BaseExp,
    Det3DCli,
)
from perceptron.layers.head.range_view.target_assigner.rv_seg_assigner_inv_weight import RVSegAssignerInvWeight
from perceptron.layers.head.range_view.generate_proposals.iou_aware_gen_proposals import IouAwareGenProposals_RV
from perceptron.layers.head.range_view.center_head_weighted_loss import WeightedCenterHeadIouAware_RV
from perceptron.layers.losses.det3d import WeightedFocalLoss, WeightedCenterNetRegLoss


class PrivateModelConfig(BaseModelConfig):

    densehead_common_heads = easydict.EasyDict(
        {
            "iou": [1, 2],
            "reg": [2, 2],
            "height": [1, 2],
            "dim": [3, 2],
            "rot": [2, 2],
        }
    )
    target_assigner_max_objs = 80000

    proposal_iou_aware_list = [0.65, 0.65]

    densehead_loss_iou_weight = 5.0

    # for wnms test
    nms_pre_max_size_test = 80000
    num_stride = {
        "res1": (1, 1),
        "res2a": (1, 2),  # stride 1, 2
        "res2": (1, 2),  # stride 1, 4
        "res3a": (2, 2),  # stride 2, 8
        "res3": (2, 2),  # stride 4, 16
        "agg1": (1, 4),  # res1 & res2
        "agg2": (4, 4),  # res2 & res3
        "agg2a": (1, 2),  # res2a & agg2
        "agg3": (1, 2),  # agg1 & agg2a
    }

    deconv_kernel = {
        "agg1": (3, 8),
        "agg2": (8, 8),
        "agg2a": (3, 4),
        "agg3": (3, 4),
    }
    deconv_padding = {
        "agg1": (1, 2),
        "agg2": (2, 2),
        "agg2a": (1, 1),
        "agg3": (1, 1),
    }


class CenterNet_RV(BaseDetector):
    def __init__(self, model_config, data_config):
        super().__init__(model_config, data_config)

    def build_dense_head(self):
        target_assigner = RVSegAssignerInvWeight(
            out_size_factor=self.model_config.densehead_out_size_factor,
            tasks=self.model_config.densehead_tasks,
            dense_reg=self.model_config.target_assigner_dense_reg,
            gaussian_overlap=self.model_config.target_assigner_gaussian_overlap,
            max_objs=self.model_config.target_assigner_max_objs,
            min_radius=self.model_config.target_assigner_min_radius,
            mapping=self.model_config.target_assigner_mapping,
            grid_size=self.data_config.grid_size,
            pc_range=self.model_config.proposal_pc_range,
            voxel_size=self.model_config.proposal_voxel_size,
            assign_topk=self.model_config.target_assigner_topk,
            no_log=self.model_config.target_assigner_no_log,
        )

        proposal_layer = IouAwareGenProposals_RV(
            dataset_name=self.model_config.densehead_dataset_name,
            class_names=[t["class_names"] for t in self.model_config.densehead_tasks],
            post_center_limit_range=self.model_config.proposal_post_center_limit_range,
            score_threshold=self.model_config.proposal_score_threshold,
            pc_range=self.model_config.proposal_pc_range,
            out_size_factor=self.model_config.densehead_out_size_factor,
            voxel_size=self.model_config.proposal_voxel_size,
            no_log=self.model_config.target_assigner_no_log,
            iou_aware_list=self.model_config.proposal_iou_aware_list,
            nms_iou_threshold_train=self.model_config.nms_iou_threshold_train,
            nms_pre_max_size_train=self.model_config.nms_pre_max_size_train,
            nms_post_max_size_train=self.model_config.nms_post_max_size_train,
            nms_iou_threshold_test=self.model_config.nms_iou_threshold_test,
            nms_pre_max_size_test=self.model_config.nms_pre_max_size_test,
            nms_post_max_size_test=self.model_config.nms_post_max_size_test,
            nms_type="multiclass_wnms",
            iou_rescore=False,
            post_process_nms_post_maxsize=self.model_config.post_process_nms_post_maxsize,
        )

        dense_head_module = WeightedCenterHeadIouAware_RV(
            dataset_name=self.model_config.densehead_dataset_name,
            tasks=self.model_config.densehead_tasks,
            target_assigner=target_assigner,
            proposal_layer=proposal_layer,
            out_size_factor=self.model_config.densehead_out_size_factor,
            input_channels=self.backbone_2d.num_bev_features,
            grid_size=self.data_config.grid_size,
            point_cloud_range=self.data_config.point_cloud_range,
            code_weights=self.model_config.densehead_loss_code_weights,
            loc_weight=self.model_config.densehead_loss_loc_weight,
            iou_weight=self.model_config.densehead_loss_iou_weight,
            share_conv_channel=self.model_config.densehead_share_conv_channel,
            common_heads=self.model_config.densehead_common_heads,
            upsample_for_pedestrian=self.model_config.densehead_upsample_for_pedestrian,
            mode=self.model_config.densehead_mode,
            init_bias=self.model_config.densehead_init_bias,
            predict_boxes_when_training=False,
        )

        def _build_losses(m):
            m.add_module(
                "crit",
                WeightedFocalLoss(self.model_config.target_assigner_alpha, self.model_config.target_assigner_gamma),
            )
            m.add_module("crit_reg", WeightedCenterNetRegLoss())
            m.add_module("crit_iou_aware", WeightedCenterNetRegLoss())

        _build_losses(dense_head_module)

        return dense_head_module


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 1
        self.lr = 0.003

    def _configure_model(self):
        model = CenterNet_RV(model_config=PrivateModelConfig(), data_config=DataConfigs())
        return model


if __name__ == "__main__":
    Det3DCli(Exp).run()
