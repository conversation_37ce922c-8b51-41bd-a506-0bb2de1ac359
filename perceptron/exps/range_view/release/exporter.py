import os
import torch
from perceptron.engine.executors.base_executor import BaseExecutor
import numpy as np


class Exporter(BaseExecutor):
    def __init__(self, exp, callbacks, logger=None, output_dir=None) -> None:
        super().__init__(exp, callbacks, logger)
        self.output_dir = output_dir
        self.output_trt_path = os.path.join(self.output_dir, f"{exp.exp_name}.engine")
        self.output_onnx_path = os.path.join(self.output_dir, f"{exp.exp_name}.onnx")

    def export_tensorrt(self):
        import tensorrt as trt
        from torch2trt import torch2trt

        self.model.eval().cuda()

        model_trt = torch2trt(
            self.model,
            (torch.randn(1, 7, 80, 1800).cuda(),),
            max_batch_size=1,
            max_workspace_size=1 << 30,
            fp16_mode=True,
            log_level=trt.Logger.INFO,
            # strict_type_constraints=True,
        )
        with open(self.output_trt_path, "wb") as fout:
            fout.write(bytes(model_trt.engine.serialize()))
        self.logger.info("export tensorrt engine into {}".format(self.output_trt_path))

        dummy_input = torch.randn(1, 7, 80, 1800).cuda()
        y = self.model(dummy_input)
        y_trt = model_trt(dummy_input)

        def to_numpy(tensor):
            return tensor.detach().cpu().numpy() if tensor.requires_grad else tensor.cpu().numpy()

        np.testing.assert_allclose(to_numpy(y), to_numpy(y_trt), rtol=1e-03, atol=1e-05)

        print("Exported model has been tested with ONNXRuntime, and the result looks good!")

    def export(self):
        self.export_tensorrt()
