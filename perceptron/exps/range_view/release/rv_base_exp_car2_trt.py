# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 3 --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no -- python3 perceptron/exps/range_view/release/rv_base_exp_car2_trt.py  -b 1 -e 80 --sync_bn 3 --no-clearml
# rlaunch --cpu=32 --gpu=8 --memory=250000 --charged-group=transformer --preemptible no --max-wait-time=24h -- python3 perceptron/exps/range_view/release/rv_base_exp_car2_trt.py  -b 1 -e 80 --sync_bn 1 --no-clearml

from perceptron.exps.range_view.release.rv_base_exp_car2 import Exp as BaseExp
from perceptron.exps.range_view.release.rv_base_exp_car2 import CenterNet_RV
from perceptron.exps.range_view.release.rv_base_exp_car2 import PrivateModelConfig, DataConfigs
from perceptron.engine.cli import Det3DCli
import torch.nn.functional as F
from perceptron.exps.range_view.release.utils import cal_coord_in_stride
import torch


# CenterNet for RV
class CenterNet_RV_TRT(CenterNet_RV):
    def __init__(self, model_config, data_config):
        super().__init__(model_config, data_config)

    @torch.no_grad()
    def forward(self, batch_dict):

        if isinstance(batch_dict, torch.Tensor):
            batch_dict = {"rv_images": batch_dict}
            rv_images = batch_dict["rv_images"]  # BCHW

        # points = batch_dict["points"]  # list
        # points = torch.stack(points)  # B, N, C
        # incl_array = torch.from_numpy(self.model_config.incl_array.copy()).float().to(points.device)
        # rv_images = RangeViewConverter.apply(
        #     points, incl_array, [-np.pi, np.pi], 2 * np.pi / self.model_config.rv_ncols
        # )  # BHWC

        rv_images = rv_images.permute(0, 3, 1, 2)  # BCHW
        # 水平移动
        rv_col_offset = self.model_config.rv_col_offset
        nr_azim = self.model_config.rv_ncols

        col_indexs = torch.cat((torch.arange(rv_col_offset, nr_azim), torch.arange(0, rv_col_offset)), axis=0).long()

        rv_images = rv_images[:, :, :, col_indexs]
        rv_images = rv_images.contiguous()

        rv_coords = rv_images[:, 3:6, :, :]  # B3HW (xyz)
        rv_range = rv_images[:, 0:1, :, :]  # B1HW
        gt_boxes = batch_dict.get("gt_boxes", None)

        # padding
        pad = (self.data_config.range_image_shape_hw[1] - rv_images.shape[-1]) // 2  # 4

        incl_array = self.model_config.incl_array

        if pad > 0:
            rv_images = F.pad(rv_images, (pad, pad, 0, 0), mode="constant", value=0)
            rv_coords = F.pad(rv_coords, (pad, pad, 0, 0), mode="constant", value=0)

        # DLA backbone
        coord_dict = cal_coord_in_stride(rv_coords)
        spatial_features_2d = self.backbone_2d(rv_images, coord_dict)[0]

        if pad > 0:  # delete padding part
            spatial_features_2d = spatial_features_2d[:, :, :, pad : spatial_features_2d.shape[3] - pad]
            rv_coords = rv_coords[:, :, :, pad : rv_coords.shape[3] - pad]

        forward_ret_dict = self.dense_head(
            rv_coords,
            spatial_features_2d,
            gt_boxes,
            incl_array=incl_array,
            ncols=self.model_config.rv_ncols,
            rv_col_offset=self.model_config.rv_col_offset,
            rv_mv_origin=self.model_config.rv_mv_origin,
            rv_range=rv_range,
        )

        if self.training:
            loss_rpn, tb_dict = self.dense_head.get_loss(forward_ret_dict)
            tb_dict.update({"loss_rpn": loss_rpn.item()})
            ret_dict = {"loss": loss_rpn}
            return ret_dict, tb_dict, {}
        else:
            return forward_ret_dict


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch, **kwargs)

    def _configure_model(self):
        model = CenterNet_RV_TRT(model_config=PrivateModelConfig(), data_config=DataConfigs())
        print(model)
        return model


if __name__ == "__main__":
    Det3DCli(Exp).run()
