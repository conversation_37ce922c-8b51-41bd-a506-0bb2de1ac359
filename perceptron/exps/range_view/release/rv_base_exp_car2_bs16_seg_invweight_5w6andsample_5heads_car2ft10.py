# DET3D_EXPID=$(cat /proc/sys/kernel/random/uuid) rlaunch -P 2 --cpu=32 --gpu=8 --memory=150000 --charged-group=transformer --preemptible no --max-wait-duration=24h --positive-tags 2080ti -- python3 perceptron/exps/range_view/release/rv_base_exp_car2_bs16_seg_invweight_5w6andsample_5heads_car2ft10.py  -b 1 -e 10 --sync_bn 1 --no-clearml --pretrained_model s3://cxw3ddet/longterm/rv_checkpoints/rv_base_xp_car2_bs16_otaignore_3w2_car1ft40.pth ## --pretrained_model outputs/car2_pretrained_model.pth
import easydict
from perceptron.exps.range_view.release.rv_base_exp_car2_bs16_seg_invweight_3w2_car1ft40 import (
    CenterNet_RV,
    PrivateModelConfig as BaseModelConfig,
    DataConfigs,
    Exp as BaseExp,
    Det3DCli,
)
from torch.utils.data import DataLoader
from perceptron.data.det3d.dataset.private_data.dataset_rv import PrivateRangeDatasetWithEval
from perceptron.exps.det3d.release.private_dataset_path import TRAIN_CAR2_DATALIST_MORE_SAMPLE
from perceptron.data.sampler import InfiniteSampler


class PrivateModelConfig(BaseModelConfig):
    densehead_tasks = [
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "car",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 3,
                "class_names": [
                    "truck",
                    "construction_vehicle",
                    "bus",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 3,
                "class_names": [
                    "motorcycle",
                    "bicycle",
                    "tricycle",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "cyclist",
                ],
            }
        ),
        easydict.EasyDict(
            {
                "num_class": 1,
                "class_names": [
                    "pedestrian",
                ],
            }
        ),
    ]  # , "other", "ghost", "masked_area")
    proposal_iou_aware_list = [0.65, 0.65, 0.65, 0.65, 0.65]
    post_process_nms_post_maxsize = 100  # 每个head最多输出100个框，5个head合计500


class Exp(BaseExp):
    def __init__(self, batch_size_per_device=32, total_devices=1, max_epoch=90, **kwargs):
        super().__init__(batch_size_per_device, total_devices, max_epoch)
        self.dump_interval = 1
        self.lr = 0.003

    def _configure_model(self):
        model = CenterNet_RV(model_config=PrivateModelConfig(), data_config=DataConfigs())
        return model

    def _configure_train_dataloader(self):

        model_config = PrivateModelConfig()

        dataset = PrivateRangeDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=TRAIN_CAR2_DATALIST_MORE_SAMPLE,
            class_names=model_config.class_name,
            training=True,
            lidar_key_list=["fuser_lidar"],
            rv_mv_origin=model_config.rv_mv_origin,
            nr_azim=model_config.rv_ncols,
            rv_col_offset=model_config.rv_col_offset,
            incl_array=model_config.incl_array,
        )
        sampler = InfiniteSampler(len(dataset), shuffle=True, drop_last=False)
        train_loader = DataLoader(
            dataset,
            batch_size=self.batch_size_per_device,
            pin_memory=True,
            num_workers=5,
            shuffle=(sampler is None) and True,
            collate_fn=dataset.collate_batch,
            drop_last=False,
            sampler=sampler,
            timeout=0,
        )
        return train_loader


if __name__ == "__main__":
    Det3DCli(Exp).run()
