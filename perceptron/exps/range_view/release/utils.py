import numpy as np
import torch
import torch.nn.functional as F


# TODO: calculate coordinate with different stride
def cal_coord_in_stride(coord_s1):
    """calculate coordinate in different stride

    Args:
        coord_s1 (torch.Tensor): torch.Size([batch, 3, 64, 2656])
    """
    coord_dict = {
        "coord_s1": coord_s1,
        "coord_s2": coord_s1[:, :, :, ::2],  # torch.Size([batch, 3, 64, 1328])
        "coord_s4": coord_s1[:, :, :, ::4],  # torch.Size([batch, 3, 64, 664])
        "coord_s8": coord_s1[:, :, :, ::8],
        "coord_s16": coord_s1[:, :, :, ::16],
    }

    return coord_dict


def get_im2col_indices(x_shape, field_height, field_width, padding=1, stride=1):
    # First figure out what the size of the output should be
    N, C, H, W = x_shape
    assert (H + 2 * padding - field_height) % stride == 0
    assert (W + 2 * padding - field_height) % stride == 0
    out_height = (H + 2 * padding - field_height) // stride + 1
    out_width = (W + 2 * padding - field_width) // stride + 1

    i0 = np.repeat(np.arange(field_height), field_width)
    i0 = np.tile(i0, C)
    i1 = stride * np.repeat(np.arange(out_height), out_width)
    j0 = np.tile(np.arange(field_width), field_height * C)
    j1 = stride * np.tile(np.arange(out_width), out_height)
    i = i0.reshape(-1, 1) + i1.reshape(1, -1)
    j = j0.reshape(-1, 1) + j1.reshape(1, -1)

    k = np.repeat(np.arange(C), field_height * field_width).reshape(-1, 1)

    return (k, i, j)


def im2col_indices(x, field_height, field_width, padding=1, stride=1):
    """ An implementation of im2col based on some fancy indexing """
    # Zero-pad the input
    p = padding
    x_padded = np.pad(x, ((0, 0), (0, 0), (p, p), (p, p)), mode="constant")

    k, i, j = get_im2col_indices(x.shape, field_height, field_width, padding, stride)

    cols = x_padded[:, k, i, j]
    B, channels, H, W = x.shape
    cols = cols.transpose(1, 2, 0).reshape(B, field_height * field_width * channels, H, W)
    return cols


# -----------------------------------------------------------------------
# GPU version: im2col_indices
# time: 2022.05.04


def get_im2col_indices_gpu(x_shape, field_height, field_width, padding=1, stride=1):
    # First figure out what the size of the output should be
    N, C, H, W = x_shape
    assert (H + 2 * padding - field_height) % stride == 0
    assert (W + 2 * padding - field_height) % stride == 0
    out_height = (H + 2 * padding - field_height) // stride + 1
    out_width = (W + 2 * padding - field_width) // stride + 1

    i0 = torch.arange(field_height).repeat_interleave(field_width)
    i0 = torch.tile(i0, dims=(C,))
    i1 = stride * torch.arange(out_height).repeat_interleave(out_width)
    j0 = torch.tile(torch.arange(field_width), dims=(field_height * C,))
    j1 = stride * torch.tile(torch.arange(out_width), dims=(out_height,))
    i = i0.reshape(-1, 1) + i1.reshape(1, -1)
    j = j0.reshape(-1, 1) + j1.reshape(1, -1)

    k = torch.arange(C).repeat_interleave(field_height * field_width).reshape(-1, 1)

    return (k, i, j)


def im2col_indices_gpu(x, field_height, field_width, padding=1, stride=1):
    """ An implementation of im2col based on some fancy indexing """
    # Zero-pad the input
    p = padding
    x_padded = F.pad(x, (p, p, p, p), mode="constant", value=0)

    k, i, j = get_im2col_indices_gpu(x.shape, field_height, field_width, padding, stride)

    cols = x_padded[:, k, i, j]
    B, channels, H, W = x.shape
    cols = torch.permute(cols, (1, 2, 0)).reshape(B, field_height * field_width * channels, H, W)
    return cols


def numpy_demo(kernel_size, batch, channels, height, width):
    np.random.seed(0)
    data = np.random.randint(1, height * width, (batch, channels, height, width))
    print("data: \n", data)
    res = im2col_indices(data, kernel_size[0], kernel_size[1])
    print("sample: \n", res[:, :, 1, 1])


def gpu_demo(kernel_size, batch, channels, height, width):
    torch.manual_seed(0)
    data = torch.randint(1, height * width, (batch, channels, height, width))
    print("data: \n", data)
    res = im2col_indices_gpu(data, kernel_size[0], kernel_size[1])
    print("sample: \n", res[:, :, 1, 1])


if __name__ == "__main__":
    kernel_size = (3, 3)
    batch = 1
    channels = 2
    height = 5
    width = 5

    numpy_demo(kernel_size, batch, channels, height, width)
    gpu_demo(kernel_size, batch, channels, height, width)
