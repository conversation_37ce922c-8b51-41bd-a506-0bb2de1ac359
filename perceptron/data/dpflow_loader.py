#!/usr/bin/env python3
# Copyright (c) 2014-2021 Megvii Inc. All rights reserved.
import os
import time
import uuid
import warnings
from collections import defaultdict
from multiprocessing import Process, Queue

import rrun
from colorama import Fore

try:
    from dpflow import Controller, InputPipe, OutputPipe, control
except ImportError:
    warnings.warn("DPFlow not installed, unable to use DPFlowDataLoader.")
from loguru import logger
from torch import distributed as dist
from torch.utils.data import DataLoader
from torch.utils.data.dataset import ConcatDataset

__all__ = ["DPFlowDataLoader"]


class DPFlowDataLoader(DataLoader):
    def __init__(
        self,
        dataset,
        batch_size,
        sampler,
        collate_fn,
        num_workers,
        num_machines,
        dpflow_buffer_size=16,
        rrun_charged_group="",
        num_memories_per_worker=10240,
        rrun_server_name=None,
        rrun_preemptible=True,
        rrun_logdir=None,
        num_iter_per_epoch=None,
    ):
        # assert num_machines > 0 and num_workers > 0
        self.sampler = sampler
        self.rank = 0
        self.world_size = 1
        if dist.is_available() and dist.is_initialized():
            self.rank = dist.get_rank()
            self.world_size = dist.get_world_size()
        self.num_iter_per_epoch = num_iter_per_epoch
        if num_iter_per_epoch is None:
            assert sampler is not None
            self.num_iter_per_epoch = len(sampler) // batch_size
        self.dpflow_buffer_size = dpflow_buffer_size

        if rrun_server_name is None:
            rrun_server_name = uuid.uuid1().hex
        self.rrun_server_name = rrun_server_name

        self.runner_state = defaultdict()
        if num_machines > 0:
            spec = rrun.RunnerSpec()
            spec.name = f"rrun-dataset-{self.rrun_server_name}"
            spec.resources.cpu = int(max(num_workers, 2) * 1.5)
            spec.resources.memory_in_mb = num_memories_per_worker * int(max(num_workers, 2) * 1.5)
            spec.priority = "Medium"
            spec.preemptible_flag = rrun.RunnerSpec.BestEffort if rrun_preemptible else rrun.RunnerSpec.Unpreemptible

            spec.charged_group = rrun_charged_group
            spec.max_wait_duration = "24h"
            spec.minimum_lifetime = 24 * 3600 * int(1e9)
            # spec.scheduling_hint.group = ""
            if rrun_logdir:
                rrun_logdir = os.path.realpath(rrun_logdir)
                spec.log_dir = rrun_logdir

            self.executor = rrun.RRunExecutor(spec, num_machines, 1)
            for i in range(num_machines):
                # rrun do not support class as its param.
                self.executor.submit(
                    producer,
                    dataset,
                    batch_size,
                    sampler,
                    collate_fn,
                    num_workers,
                    dpflow_buffer_size,
                    self.rrun_server_name,
                    self.rank * num_machines + i,
                )
        else:
            self.worker = Process(
                target=producer,
                args=(
                    dataset,
                    batch_size,
                    sampler,
                    collate_fn,
                    num_workers,
                    dpflow_buffer_size,
                    self.rrun_server_name,
                    self.rank,
                ),
                daemon=False,
            )
            self.worker.start()

    def __len__(self):
        return self.num_iter_per_epoch

    def __iter__(self):
        if getattr(self, "_input_pipe", None) is None:
            self._input_pipe = InputPipe(self.rrun_server_name, buffer_size=self.dpflow_buffer_size)
            self._input_pipe.set_policy("GROUP_ID", self.rrun_server_name)
        if getattr(self, "_input_controller", None) is None:
            self._input_controller = Controller(io=[self._input_pipe])
            self._input_controller.start()

        while True:
            qsize = self._input_pipe._queue.qsize()
            if qsize < max(self.dpflow_buffer_size - 10, 10):
                if self.rank == 0:
                    logger.info("Rank={}, DPFlow Dataloader Warmup, queue size: {}".format(self.rank, qsize))
                time.sleep(10)
            else:
                break

        for _ in range(len(self)):
            obj = self._input_pipe.get()
            now = time.time()
            runner_id = obj["runner_id"]
            self.runner_state[runner_id] = now
            pop_list = []
            for runner_id, last_time in self.runner_state.items():
                interval = now - last_time
                if interval > 120:
                    try:
                        self.executor._rrun_client.kill_runner(rrun.KillRunnerRequest(id=runner_id))
                    except Exception:
                        pass
                    pop_list.append(runner_id)
            for runner_id in pop_list:
                self.runner_state.pop(runner_id)
                logger.info("kill runner %s" % runner_id)
            if obj.get("frame_id", None) == "error":
                logger.error(Fore.RED + "Something wrong happens in dataset, please check without dpflow.")
                continue
            yield obj

    def __del__(self):
        if getattr(self, "_input_controller", None) is not None:
            self._input_controller.stop()
        if getattr(self, "_input_pipe", None) is not None:
            # release input_pipe buffer, just a work around of leaking memory.
            self._input_pipe._queue.queue.clear()
        if getattr(self, "executor", None) is not None:
            self.executor.shutdown(wait=False)
        if getattr(self, "worker", None) is not None:
            if self.worker.is_alive():
                self.worker.terminate()
            self.worker.join()


def producer(dataset, batch_size, sampler, collate_fn, num_workers, dpflow_buffer_size, rrun_server_name, machine_id=0):
    def _producer_worker(queue, worker_id=0):
        sampler.set_seed(19491001 * (machine_id * num_workers + worker_id) + int(time.time()))
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            sampler=sampler,
            num_workers=0,
            collate_fn=collate_fn,
        )
        # logger.info("after build dataloader")
        while True:
            data_iter = iter(dataloader)
            runner_id = os.environ.get("KUBEBRAIN_PROCESS_NAME")
            for i in range(len(dataloader)):
                try:
                    obj = next(data_iter)
                    obj["runner_id"] = runner_id
                    obj["worker_id"] = worker_id
                    queue.put(obj)
                except StopIteration:
                    data_iter = iter(dataloader)
                except Exception as e:
                    logger.info("Something wrong happens in dataset, please check without dpflow.")
                    logger.info(e)
                    obj = {"frame_id": "error", "runner_id": runner_id, "worker_id": worker_id}
                    queue.put(obj)
                    time.sleep(600)

    queue = Queue(maxsize=dpflow_buffer_size)
    output_pipe = OutputPipe(rrun_server_name, buffer_size=dpflow_buffer_size)

    if isinstance(dataset, ConcatDataset):
        for sub_dataset in dataset.datasets:
            if sub_dataset.image.undistort:
                sub_dataset.image._init_camera_undistort_mapping()
                sub_dataset.image.init_camera_undistort_flag = True
    else:
        if dataset.image.undistort:
            dataset.image._init_camera_undistort_mapping()
            dataset.image.init_camera_undistort_flag = True

    if num_workers > 0:
        workers = [Process(target=_producer_worker, args=(queue, i)) for i in range(num_workers)]
        for w in workers:
            w.start()

        with control(io=[output_pipe]):
            while True:
                obj = queue.get()
                output_pipe.put_pyobj(obj)
    else:
        _producer_worker(queue, 0)
