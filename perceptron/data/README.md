# 数据说明

## 总体说明

+ 为 Perceptron 提供数据 Dataset 供应。

## 规范说明
* **一级目录**：具体以 `Task` 进行划分，比如：Det3d / Lane3D / Etc。
* **二级目录**：不同 Task，具体以功能 `Functionality` 进行模块化划分，比如：modules / private / public。
* **其他**：Private 数据和 Public 能够实现最大复用，数据流 Aug 需要统一。

## 目录结构
```zsh
./perceptron/data
├── det3d
│   ├── modules   # 数据集的子模块
│   │   ├── annotation    # 标签处理
│   │   ├── pipelines     # 数据归一化、增强
│   │   ├── image         # 图像处理
│   │   ├── loader        # 原始数据载入和预处理
│   │   ├── visulization  # Public 原始数据处理
│   │   ├── utils         # 存放常用的工具
│   │   ├── evaluation.py   # 推理评测
│   ├── private   # 私有数据集
│   │   ├── base.py   # 数据集基类
│   │   └── depth.py  # depth数c集（待完善）
│   ├── public   # 公开数据集
│   │   ├── nuscenes.py
│   │   └── once.py
│   ├── source   # 原始数据的索引
│   │   ├── base.py           # 定义所使用到的基类
│   │   ├── config.py         # 数据对外接口
│   │   ├── filter.py         # 各种数据集过滤的脚本
│   │   ├── geely_car_1.py    # 吉利1号车
│   │   ├── geely_car_2.py    # 吉利2号车
│   │   ├── synthetic_vcar.py # 合成数据
│   │   ├── wm_car_1.py       # 威马1号车
│   │   ├── wm_car_2.py       # 威马2号车
│   │   ├── wm_car_3.py       # 威马3号车
│   │   ├── wm_car_4.py       # 威马4号车
│   ├── └── wm_car_5.py       # 威马5号车
├── lane3d
│   ├── ....
├── seg3d
│   ├── ....
└── README.md
```
