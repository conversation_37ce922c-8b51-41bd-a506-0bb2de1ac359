import json
from multiprocessing import Pool

from perceptron.data.det3d.utils.redis_cache_stepmind import RedisCachedData, OSSEtagHelper
import refile
from perceptron.data.det3d.source.z10 import TRAINSET_PARTIAL
import pickle
import os
import numpy as np
import tqdm
from loguru import logger


def build_redis(json_path):
    oss_etag_helper = OSSEtagHelper(check_etag=True)
    # assert str.endswith(json_path, ".json")
    if str.endswith(json_path, ".json"):
        json_files = [json_path]
    else:
        json_files = refile.smart_glob(refile.smart_path_join(json_path, "*.json"))
    for json_path in json_files:
        json_data = RedisCachedData(json_path, oss_etag_helper, rebuild=False)
        try:
            frames, idx_list = json_data["frames"], json_data["key_frame_idx"]  # noqa
        except Exception:
            print("rebuild redis: ", json_path)
            json_data = RedisCachedData(json_path, oss_etag_helper, rebuild=True)
        freespace_path_list = []
        for idx, frame in enumerate(frames):

            if "freespace_path" not in frame:
                # print(f"{json_path} idx: {idx} dont contains freespace_path, {frame.keys()}")
                continue
            freespace_path = frame["freespace_path"].replace("/freespace_prelabel/", "occupancy_prelabel/")
            freespace_path_list.append(freespace_path)
        # print("len freespace_path_list: ", len(freespace_path_list))
        pkl_to_npz(freespace_path_list)
    return None


def labels2onehot(labels):
    num_classes = np.max(labels) + 1
    assert np.min(labels) >= 0
    labels_shape = labels.shape
    one_hot = np.zeros((labels.shape) + (num_classes,), dtype=np.bool_).reshape((labels.size, num_classes))
    labels = labels.reshape(-1)
    # 使用布尔索引设置 1
    one_hot[np.arange(labels.size), labels] = True
    one_hot = one_hot.reshape(labels_shape + (num_classes,))
    return one_hot


def pkl_to_npz(freespace_path_list):
    for freespace_path in freespace_path_list:
        local_freespace_path = refile.smart_path_join("/data/all_data/occ_annos/", freespace_path[len("s3://") :])
        if refile.smart_exists(local_freespace_path):
            continue

        if not refile.smart_exists(freespace_path):
            logger.info(f"{freespace_path} not exist")
            continue

        data = pickle.load(refile.smart_open(freespace_path, "rb"))
        data_target = data["target"]
        mask = data["mask"]
        assert len(data) == 2, f"data.keys(): {data.keys()} is not only target and mask"
        # data_target = labels2onehot(data_target)
        assert data_target.max() <= 255 and data_target.min() >= 0, f"freespace_path: {freespace_path}"
        data_target = data_target.astype(np.uint8)
        data["target"] = data_target
        os.makedirs(os.path.dirname(local_freespace_path), exist_ok=True)
        np.savez_compressed(local_freespace_path, target=data_target, mask=mask)


if __name__ == "__main__":
    p_list = []
    for k in [
        # "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_train699",
        # "carz10_1358_occ_prelabels_v20250101_static_data_602_vx0p1",
        "carz10_1358_occ_labels_z10_20250222_huiliu_odlabel_data_vx0p1_restore_940_rmv1bmk_odbmk",
        "carz10_1358_occ_labels_z10_20250306_huiliu_jixie_label_data_vx0p1_restore_1078",
        "car_z10_occ_prelabel_4in1_v0307_2093jsons_vx0p1_10fps",
        "car_z10_occ_prelabel_4in1_1363jsons_vx0p1_10fps",
        # "z10_label_1230_train",
        # "Z10_label_0207_7w",
        # "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps",
    ]:
        p_list.append(TRAINSET_PARTIAL[k])
        # p_list.append(BENCHMARK_PARTIAL[k])

    def get_dirs(json_paths):
        return list(set(["/".join(json_path.split("/")[:-1]) for json_path in json_paths]))

    data_paths = []
    for p in p_list:
        with refile.smart_open(p, "r") as f:
            x = json.load(f)

        if isinstance(x, dict) and "paths" in x:
            x = x["paths"]
        # x=get_dirs(x)
        data_paths += x
    import random

    random.shuffle(data_paths)
    # data_paths = data_paths[:20]
    # data_paths = ["s3://wyh-occ/occ-prelabels/merge_jsons/yjp-oss/to_wyh_only_json/prelabeled_data/car_z12/20250208_dp-track/ppl_bag_20250208_141914_det/v0_250213_063557/splited_video_prelabels_tracking/0107.json"]
    count = 0
    total = len(data_paths)
    pbar = tqdm.tqdm(total=total)
    print(total)
    with Pool(50) as pool:
        results = []
        for result in pool.imap(build_redis, data_paths):
            count += 1
            print(count, total, f"{count/total*100}%")
            pbar.update(1)
    # for data in data_paths[:20]:
    #     build_redis(data)
