import json
from multiprocessing import Pool

from perceptron.data.det3d.utils.redis_cache_stepmind import RedisCachedData, OSSEtagHelper
from perceptron.data.occ.utils.occ_redis_cache import OccRedisCachedData
import refile
from perceptron.data.det3d.source.z10 import BENCHMARK_PARTIAL
import tqdm


def build_redis(json_path):
    oss_etag_helper = OSSEtagHelper(check_etag=False)
    # assert str.endswith(json_path, ".json")
    if str.endswith(json_path, ".json"):
        json_files = [json_path]
    else:
        json_files = refile.smart_glob(refile.smart_path_join(json_path, "*.json"))
    for json_path in json_files:
        json_data = RedisCachedData(json_path, oss_etag_helper, rebuild=False)
        try:
            frames, idx_list = json_data["frames"], json_data["key_frame_idx"]  # noqa
        except Exception:
            print("rebuild redis: ", json_path)
            json_data = RedisCachedData(json_path, oss_etag_helper, rebuild=False)
        freespace_path_list = []
        for idx, frame in enumerate(frames):

            if "freespace_path" not in frame:
                # print(f"{json_path} idx: {idx} dont contains freespace_path, {frame.keys()}")
                continue
            freespace_path = frame["freespace_path"].replace("/freespace_prelabel/", "occupancy_prelabel/")
            freespace_path = "s3://cxw-occ-annos/occ_annos/" + freespace_path[5:] + ".npz"
            freespace_path_list.append(freespace_path)
            if refile.smart_exists(freespace_path):
                occ_anno = OccRedisCachedData(freespace_path, oss_etag_helper=oss_etag_helper, rebuild=False)
                print(occ_anno["target"].shape)

    return None


if __name__ == "__main__":
    p_list = []
    for k in [
        # "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_train699",
        # "carz10_1358_occ_prelabels_v20250101_static_data_602_vx0p1",
        # "carz10_1358_occ_labels_z10_20250222_huiliu_odlabel_data_vx0p1_restore_940_rmv1bmk_odbmk",
        # "carz10_1358_occ_labels_z10_20250306_huiliu_jixie_label_data_vx0p1_restore_1078",
        # "car_z10_occ_prelabel_4in1_v0307_2093jsons_vx0p1_10fps",
        # "car_z10_occ_prelabel_4in1_1363jsons_vx0p1_10fps",
        # "z10_label_1230_train",
        # "Z10_label_0207_7w",
        "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps",
    ]:
        # p_list.append(TRAINSET_PARTIAL[k])
        p_list.append(BENCHMARK_PARTIAL[k])

    def get_dirs(json_paths):
        return list(set(["/".join(json_path.split("/")[:-1]) for json_path in json_paths]))

    data_paths = []
    for p in p_list:
        with refile.smart_open(p, "r") as f:
            x = json.load(f)

        if isinstance(x, dict) and "paths" in x:
            x = x["paths"]
        # x=get_dirs(x)
        data_paths += x
    import random

    random.shuffle(data_paths)
    # data_paths = data_paths[:50]
    # data_paths = ["s3://wyh-occ/occ-prelabels/merge_jsons/yjp-oss/to_wyh_only_json/prelabeled_data/car_z12/20250208_dp-track/ppl_bag_20250208_141914_det/v0_250213_063557/splited_video_prelabels_tracking/0107.json"]
    count = 0
    total = len(data_paths)
    pbar = tqdm.tqdm(total=total)
    print(total)
    with Pool(20) as pool:
        results = []
        for result in pool.imap(build_redis, data_paths):
            count += 1
            print(count, total, f"{count/total*100}%")
            pbar.update(1)
    # for data in data_paths[:20]:
    #     build_redis(data)
