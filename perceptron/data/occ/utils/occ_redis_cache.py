import refile

import io
import numpy as np

from perceptron.data.det3d.utils.redis_cache_stepmind import <PERSON>SEtagHelper, RedisCachedIterator


def save_npz_to_bytes(*args, **kwargs):
    with io.BytesIO() as bytes_io:
        np.savez_compressed(bytes_io, *args, **kwargs)
        bytes_data = bytes_io.getvalue()
    return bytes_data


def load_npz_from_bytes(bytes_data):
    data_dict = {}
    with io.BytesIO(bytes_data) as bytes_io:
        npz_data = np.load(bytes_io)
        for k in npz_data.files:
            data_dict[k] = npz_data[k]
    return data_dict


class OccRedisCachedIterator(RedisCachedIterator):
    _data_prefix = "docc"


class OccRedisCachedData:
    def __init__(self, path, oss_etag_helper, rebuild=False, **kwargs):
        self.path = path
        assert self.path.endswith(".npz"), f"unsupported file path: {self.path}"
        self.cache = OccRedisCachedIterator(oss_etag_helper.get_etag(self.path), **kwargs)
        if not self.cache.exist() or rebuild:
            self._init_cache()

    def _init_cache(self):
        with refile.smart_open(self.path, "rb") as rf:
            npz_bytes = rf.read()
            bytesio = io.BytesIO(npz_bytes)

        np_obj = np.load(bytesio)
        target = np_obj["target"]
        mask = np_obj["mask"]
        target_shape = target.shape[:2]
        if target.dtype == np.bool_ and target.ndim == 3:
            target = np.where(target)[-1].astype(np.uint8)
            target = target.reshape(target_shape)
        elif target.dtype == np.int64 and target.ndim == 2:
            target = target.astype(np.uint8)
        elif target.dtype == np.uint8 and target.ndim == 2:
            pass
        else:
            raise ValueError(f"unsupported target type and shape: {target.shape}, {target.dtype}")

        data_bytes = save_npz_to_bytes(target=target)
        mask_data_bytes = save_npz_to_bytes(mask=mask)
        self.cache._set("target", data_bytes)
        self.cache._set("mask", mask_data_bytes)

    def __getitem__(self, key):
        if key in ["target", "mask"]:
            bytes_data = self.cache._get(key)
            obj = load_npz_from_bytes(bytes_data)
            return obj[key]
        else:
            raise KeyError(f"No such key: {key} in OccRedisCacheData")


oss_etag_helper = OSSEtagHelper(check_etag=False)
