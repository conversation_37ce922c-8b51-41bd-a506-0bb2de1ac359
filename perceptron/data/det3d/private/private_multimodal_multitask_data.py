import bisect
import math
import warnings
from collections import abc
from functools import partial
from typing import Any, Dict, Optional

from loguru import logger
import numpy as np
import torch
from mmdet3d.core.bbox import Box3DMode, LiDARInstance3DBoxes, BaseInstance3DBoxes

from perceptron.data.det3d.modules.annotation.e2e_annos import E2EAnnotations
import copy
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset, padding_radar

from perceptron.data.det3d.modules.pipelines import ImageAffineTransGPU, ImageUndistortGPU


def _convert_to_lidar_instance_multitask(sample_queue: list, key: str) -> list:
    """
    Args:
        sample_queue: list of dict, each dict contains the data of one frame.
    Returns:
        lidar_instances: list of LiDARInstance3DBoxes, each instance contains the boxes of one frame.
    """
    lidar_instances = []
    for i in range(len(sample_queue)):
        if len(sample_queue[i][key]):
            if sample_queue[i][key].shape[1] == 7:
                sample_queue[i][key] = np.hstack(
                    (sample_queue[i][key], np.zeros((sample_queue[i][key].shape[0], 2)))
                )  # temp add velo space
        else:
            sample_queue[i][key] = np.zeros((0, 9))
        # +0.5,
        # (lidar_instances[0].gravity_center, lidar_instances[0].tensor[:, 3:])
        lidar_instances.append(
            LiDARInstance3DBoxes(
                sample_queue[i][key], box_dim=sample_queue[i][key].shape[-1], origin=(0.5, 0.5, 0.5)
            ).convert_to(Box3DMode.LIDAR)
        )
    return lidar_instances


def filter_gt(annos):
    if "occ" not in annos:
        return True

    if isinstance(annos["occ"]["semantic"], list) and len(annos["occ"]["semantic"]) == 2:
        freespace = annos["occ"]["semantic"][0].transpose(1, 0)[::-1, :]
    else:
        freespace = annos["occ"]["semantic"].transpose(1, 0)[::-1, :]

    offset = 272
    focus_range = [500, 700]
    height, width = freespace.shape
    sliced_freespace = freespace[height - (offset + focus_range[1]) : height - (offset + focus_range[0])]

    count_nonzeros = np.count_nonzero(sliced_freespace)
    total_count = sliced_freespace.size
    zero_ratio = 1 - count_nonzeros / total_count

    if zero_ratio > 0.3:
        # 过滤掉该帧
        return False

    return True


class PrivateMultitaskDataset(PrivateE2EDataset):
    def __init__(
        self,
        num_frames_per_sample=5,
        eval_cfg=None,
        gpu_aug=False,
        postcollate_tensorize=False,
        convert_fp32=True,
        seq_mode=False,
        seq_split_num=1,
        fov_boardline=None,  # 前视 fov
        sample_rate=1,
        **kwargs,
    ):
        self.gpu_aug = gpu_aug and torch.cuda.is_available()
        if self.gpu_aug and not postcollate_tensorize:
            warnings.warn(
                "gpu aug is unavailable without postcollate_tensorize, use cpu aug instead.",
            )
        for key, transform in kwargs["pipeline"].items():
            if self.gpu_aug and key in ["undistort", "ida_aug"]:
                transform.update({"gpu_aug": True})
            transform.update({"multiframe": True})

        annotations_e2e = kwargs.pop("annotation")
        # 修改kwargs中的annotation只是为了兼容旧的代码结构
        if len(annotations_e2e) == 0:
            raise NotImplementedError
        else:
            k = list(annotations_e2e.keys())[0]
            kwargs["annotation"] = annotations_e2e[k]
        super(PrivateE2EDataset, self).__init__(**kwargs)
        import os

        if os.environ.get("PERCEPTRON_OURS", "old") == "old":
            self.annotation = E2EAnnotations(self.loader_output, self.mode, annotations_e2e, sub_level_annos=False)
        else:
            self.annotation = E2EAnnotations(self.loader_output, self.mode, annotations_e2e, sub_level_annos=True)

        self.num_frames_per_sample = num_frames_per_sample
        self.sample_rate = sample_rate

        try:
            self.annotation.loader_output["calibrated_sensors"] = self.image.loader_output["calibrated_sensors"]
        except Exception:
            assert not getattr(self, "image", False)
            self.annotation.loader_output["calibrated_sensors"] = self.loader_output["calibrated_sensors"]
        self._init_scene_flag()
        self.seq_mode = seq_mode
        self.seq_split_num = seq_split_num
        if self.seq_mode:
            self._set_sequence_group_flag()

        self.postcollate_tensorize = postcollate_tensorize
        self._init_post_collate()

        self._init_preforward(kwargs["pipeline"])

        self.fov_boardline = fov_boardline

    def _init_scene_flag(self):
        """
        organize data in scene for multi gpu eval
        """
        cumulative_size = self.loader_output["cummulative_sizes"]
        scene_len = np.array([0] + cumulative_size)
        scene_len = scene_len[1:] - scene_len[:-1]
        self.scene_flag = []
        self.scene_frame_idx = []
        for i, s in enumerate(scene_len):
            self.scene_flag.extend([i] * s)
            self.scene_frame_idx.extend(list(range(s)))
        self.scene_flag = np.array(self.scene_flag)  # must be np.array
        self.scene_frame_idx = np.array(self.scene_frame_idx)
        assert len(self.scene_flag) == len(self.scene_frame_idx) == len(self)
        self.scene_order = (
            True  # default as False, need to set True when using GroupEachSampleInBatchSampler and multi gpu eval
        )

    def _set_sequence_group_flag(self):
        """Set each sequence to be a different group"""

        assert self.seq_split_num > 1, "each group must be longer than only one frame!"
        bin_counts = np.bincount(self.scene_flag)

        new_flags = []
        group_frame_idx = []

        curr_new_flag = 0
        for curr_flag in range(len(bin_counts)):
            curr_sequence_length = np.array(
                list(range(0, bin_counts[curr_flag], math.ceil(bin_counts[curr_flag] / self.seq_split_num)))
                + [bin_counts[curr_flag]]
            )
            for sub_seq_idx in curr_sequence_length[1:] - curr_sequence_length[:-1]:
                for frame_idx in range(sub_seq_idx):
                    new_flags.append(curr_new_flag)
                    group_frame_idx.append(frame_idx)
                curr_new_flag += 1

        assert len(new_flags) == len(self.scene_flag)
        assert len(group_frame_idx) == len(self.scene_frame_idx)
        assert len(np.bincount(new_flags)) == len(np.bincount(self.scene_flag)) * self.seq_split_num
        self.scene_flag = np.array(new_flags, dtype=np.int64)
        self.scene_frame_idx = np.array(group_frame_idx, dtype=np.int64)

    def _init_post_collate(self):
        """
        collate data
        """
        self.postcollate_pipeline = []
        if self.postcollate_tensorize:
            self.postcollate_pipeline.append(PrivateMultitaskDataset.convert_data2tensor)
        if torch.cuda.is_available():
            self.postcollate_pipeline.append(PrivateMultitaskDataset.load_tensor2gpu)

        def _postcollate_fn(batch_dict: Dict) -> None:
            for fn in self.postcollate_pipeline:
                fn(batch_dict)

        self.batch_postcollate_fn = _postcollate_fn

    def _init_preforward(self, pipeline):
        # 重构中：gpu的去畸变在pipeline中，和默认去畸变在loader中不一致，统一处理将默认的cpu去畸变同样后移至pipeline进行
        # 除gpu undistort/aug之外，暂时没有其他内容只能放置于preforward，暂时不进行额外配置
        """"""
        self.preforward_pipeline = []
        if self.gpu_aug:
            self.preforward_pipeline.append(
                ImageUndistortGPU(
                    mode=self.mode,
                )
            )
            if "ida_aug" in pipeline:
                self.preforward_pipeline.append(
                    ImageAffineTransGPU(
                        mode=self.mode,
                        img_norm=pipeline["ida_aug"]["img_norm"],
                        img_conf=pipeline["ida_aug"]["img_conf"],
                    )
                )

        def _preforward_fn(batch_dict: Dict) -> None:
            for fn in self.preforward_pipeline:
                fn(batch_dict)

        self.batch_preforward_fn = _preforward_fn

    @staticmethod
    def convert_data2tensor(batch_dict) -> None:
        def _cast_to_tensor(inputs: Any, dtype: Optional[np.dtype] = None) -> Any:
            """Recursively convert np.ndarray in inputs into torch.Tensor
            Args:
                inputs: Inputs that to be casted.
                dtype (numpy.dtype): dtype before conversion


            Returns:
                The same recursive structure are remained as inputs, with all contained ndarrays cast to Tensors.
            """
            if isinstance(inputs, BaseInstance3DBoxes):
                return inputs
            elif isinstance(inputs, torch.Tensor):
                return inputs
            elif isinstance(inputs, np.ndarray):
                # if isinstance(inputs, np.ndarray):
                if dtype is not None:
                    inputs = inputs.astype(dtype)
                if inputs.dtype in [np.uint16]:
                    # unsupported numpy types in torch.tensor
                    inputs = inputs.astype(np.int32)
                return torch.from_numpy(inputs).contiguous()
            elif isinstance(inputs, abc.Mapping):
                return {k: _cast_to_tensor(v, dtype) for k, v in inputs.items()}
            elif isinstance(inputs, str):
                return inputs
            elif isinstance(inputs, abc.Iterable):
                return [_cast_to_tensor(item, dtype) for item in inputs]
            else:
                return inputs

        skip_conversions = ["frame_id", "img_metas", "calib", "image_shape"]

        for key, val in batch_dict.items():
            if key in skip_conversions:
                continue
            batch_dict[key] = _cast_to_tensor(val)

    @staticmethod
    def load_tensor2gpu(batch_dict, pin_memory=False):
        # 重构问题：
        # 是否和convert_data2tensor合并？
        # Private数据在什么情况使用BaseInstance3DBoxes？
        """Recursively load cpu tensor to cuda and convert tensor from src_type to dst_type.
        Ensure that data is loaded into the GPU before type conversion
        to avoid pinned memory being converted to pageable memory.
        """

        def _cast_tensors(inputs: Any, cast_fn) -> Any:
            if isinstance(inputs, torch.Tensor):
                return cast_fn(inputs)
            elif isinstance(inputs, BaseInstance3DBoxes):
                return type(inputs)(
                    _cast_tensors(inputs.tensor, cast_fn), box_dim=inputs.box_dim, with_yaw=inputs.with_yaw
                )
            elif isinstance(inputs, abc.Mapping):
                return {k: _cast_tensors(v, cast_fn) for k, v in inputs.items()}
            elif isinstance(inputs, str):
                return inputs
            elif isinstance(inputs, abc.Iterable):
                return [_cast_tensors(item, cast_fn) for item in inputs]
            else:
                return inputs

        def _cast_tensors_to_cuda(inputs: Any, pin_memory: bool) -> Any:
            def _cast_tensor_to_cuda(tensor: torch.Tensor, pin_memory: bool):
                return (tensor.pin_memory() if pin_memory else tensor).to(device="cuda", non_blocking=True)

            return _cast_tensors(inputs, partial(_cast_tensor_to_cuda, pin_memory=pin_memory))

        # 所有不需要加载到gpu上的key，放在skip_conversions中；否则默认会加载为cuda tensor
        skip_conversions = ["frame_id", "img_metas", "calib", "image_shape"]

        for key, val in batch_dict.items():
            if key in skip_conversions:
                continue
            batch_dict[key] = _cast_tensors_to_cuda(val, pin_memory)

    def pre_check_not_skip(self, index: int) -> bool:
        """check anno / sensor valid, return True if all valid, False if some is not valid."""
        if not self.is_train:
            return True

        if isinstance(self.annotation, list):
            for task in self.annotation:
                if not task.pre_check_not_skip(index):
                    return False
        else:
            if not self.annotation.pre_check_not_skip(index):
                return False

        # if hasattr(self, "image") and "camera_names" in self.sensor_names:
        #     if not self.image.pre_check_not_skip(index):
        #         return False
        # lidar
        # if hasattr(self, "lidar") and "lidar_names" in self.sensor_names:
        #     if not self.lidar.pre_check_not_skip(index):
        #         return False

        # radar
        if hasattr(self, "radar") and "radar_names" in self.sensor_names:
            if not self.radar.pre_check_not_skip(index):
                return False

        return True

    def get_single_data(self, index: int) -> dict:
        """
        Return dataset's final outputs.
        Note:
            "frame_id":       list of frame index.
            "gt_boxes":       (num_boxes, 7) numpy array, 7 means (x,y,z,l,w,h,angle).
            "gt_labels":      (N, 1) numpy array, box's labels.
            "imgs":           list of multi-view image (in order to compatible with inputs of different shapes).
            "lidar2imgs":     (N, 4, 4) numpy array, camera intrinsic matrix.
            "points":         (N, 4) numpy array, concated lidar points.
            "lidar2ego:       (4, 4) numpy array, lidar to ego extrinsics.
            "ego2global":     (4, 4) numpy array, ego to world extrinsics.
            "bda_mat":        (N, 4, 4) numpy array, loaded by pipeline forward
            "ida_mats":       (N, 4, 4) numpy array, loaded by pipeline forward
        """
        pre_skip_flag = self.pre_check_not_skip(index)
        if not pre_skip_flag:
            logger.info("skip by pre_check_not_skip")
            return None

        frame_idx = self.loader_output["frame_index"][index]
        data_dict = {
            "frame_id": frame_idx,
        }
        # annotation

        annos = self.annotation[index]
        if annos is None and self.is_train:
            self.loader_output["frame_data_list"].cal_useless_data(index, "ann1")
            # print("ann 1")
            return None
        if self.is_train and not filter_gt(annos):  # False 过滤， True 保留 # 主要用于occ的过滤
            # print("ann 2")
            self.loader_output["frame_data_list"].cal_useless_data(index, "ann2")
            return None
        if annos is not None:  # self.is_train:
            task_list = annos.pop("task_list", None)
            if task_list is not None:
                data_dict["task_list"] = task_list

            import os

            if os.environ.get("PERCEPTRON_OURS", "old") == "old":
                data_dict.update(annos)
            else:
                data_dict["multitask_labels"] = annos
        # image
        if hasattr(self, "image") and "camera_names" in self.sensor_names:
            img_info = self.image.get_images(index, data_dict)
            if img_info is None and self.is_train:
                del data_dict
                # print("9999")
                self.loader_output["frame_data_list"].cal_useless_data(index, "imgs")
                return None

        # lidar
        if hasattr(self, "lidar") and "lidar_names" in self.sensor_names:
            lidar_info = self.lidar.get_lidars(index, data_dict)
            if lidar_info is None:  # and self.is_train:
                del data_dict
                # print("lidar")
                self.loader_output["frame_data_list"].cal_useless_data(index, "lidar")
                return None

        # radar
        if hasattr(self, "radar") and "radar_names" in self.sensor_names:
            radar_info = self.radar.get_radars(index, data_dict)
            if radar_info is None and self.is_train:
                return None

        if hasattr(self, "scene_flag"):
            group_idx = self.scene_flag[index]
            data_dict["group_idx"] = group_idx
        if hasattr(self, "scene_frame_idx"):
            scene_frame_idx = self.scene_frame_idx[index]
            data_dict["scene_frame_idx"] = scene_frame_idx
        # remove single frame pipeline for E2E dataset

        return data_dict

    def random_track_indices(self, index: int) -> list:
        """ Random sampling frames inside a same clip for training """

        # Get the frame index range of current clip
        cum_size = self.loader_output["cummulative_sizes"]
        clip_id = bisect.bisect_right(cum_size, index)
        upper = cum_size[clip_id]
        lower = cum_size[clip_id - 1] if clip_id > 0 else 0
        assert upper - lower > self.num_frames_per_sample

        # sample frames per `self.frame_sample_interval`
        # retrive back
        # sample_indices = list(
        #     range(
        #         index,
        #         max(lower, index - self.frame_sample_interval * self.num_frames_per_sample) - 1,
        #         -self.frame_sample_interval
        #     )
        # )
        sample_indices = list(
            range(
                max(lower, index - self.frame_sample_interval * (self.num_frames_per_sample - 1)),
                index + 1,
                self.frame_sample_interval,
            )
        )
        # sample_indices = sample_indices[::-1]
        # retrive front
        num_frames_more = self.num_frames_per_sample - len(sample_indices)
        sample_indices += [index + self.frame_sample_interval * (i + 1) for i in range(0, num_frames_more)]

        assert len(sample_indices) == self.num_frames_per_sample

        clip_json_path = self.loader_output["json_collection"][clip_id - 1]

        return sample_indices, clip_json_path

    def generate_track_data_indexes(self, index: int) -> list:
        """Choose the track indexes that are within the same sequence"""
        index_list = [i for i in range(index - self.num_frames_per_sample + 1, index + 1)]

        scene_tokens = [bisect.bisect_right(self.loader_output["cummulative_sizes"], i) for i in index_list]

        tgt_scene_token, earliest_index = scene_tokens[-1], index_list[-1]
        for i in range(self.num_frames_per_sample)[::-1]:
            if scene_tokens[i] == tgt_scene_token:
                earliest_index = index_list[i]
            elif self.mode != "train":
                index_list = index_list[i + 1 :]
                break
            elif self.mode == "train":
                index_list[i] = earliest_index

        # get clip json path
        clip_json_path = self.loader_output["json_collection"][tgt_scene_token]

        return index_list, clip_json_path

    def _convert_to_lidar_instance(self, sample_queue: list, key: str) -> list:
        lidar_instances = []
        for i in range(len(sample_queue)):
            if len(sample_queue[i][key]):
                if sample_queue[i][key].shape[1] == 7:
                    sample_queue[i][key] = np.hstack(
                        (sample_queue[i][key], np.zeros((sample_queue[i][key].shape[0], 2)))
                    )  # temp add velo space
            else:
                sample_queue[i][key] = np.zeros((0, 9))
            lidar_instances.append(
                LiDARInstance3DBoxes(
                    sample_queue[i][key], box_dim=sample_queue[i][key].shape[-1], origin=(0.5, 0.5, 0.5)
                ).convert_to(Box3DMode.LIDAR)
            )
        return lidar_instances

    @staticmethod
    def multiframe_to_batch_basic(sample_queue):
        batch = dict()
        # print("k1 ", sample_queue[0].keys())
        for key in sample_queue[0].keys():

            if key in ["frame_id", "radar_points", "timestamp"]:
                batch[key] = [sample_queue[i][key] for i in range(len(sample_queue))]
            elif (
                key in ["imgs", "map1s", "map2s"] or key[-5:] == "_imgs"
            ):  # [List(bs): [List(queue_len): [List(num_cameras): [tensor: H*W*C]]]]
                batch[key] = np.expand_dims(
                    np.stack(
                        [
                            np.stack(
                                sample_queue[i][key],
                                axis=0,
                            )
                            for i in range(len(sample_queue))
                        ],
                        axis=0,
                    ),
                    axis=0,
                )
            elif key in ["img_semantic_seg", "sequence_data", "valid_mask_seq", "sequence_data_noise"]:
                batch[key] = np.expand_dims(
                    np.stack(
                        [
                            np.stack(
                                sample_queue[i][key],
                                axis=0,
                            )
                            for i in range(len(sample_queue))
                        ],
                        axis=0,
                    ),
                    axis=0,
                )
            elif key in [
                "tran_mats_dict",
                "fork_idx",
                "end_idx",
                "index_in_dataset",
                "seq_attribute",
                "semantic",
                "semantic_mask",
            ]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "lidar2global":
                batch["l2g"] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "multitask_labels":
                multitask_labels = dict()
                sub_key_list = [set(sample_queue[i]["multitask_labels"].keys()) for i in range(len(sample_queue))]
                # all equal
                equal_list = [sub_key_list[0] == i for i in sub_key_list]
                assert all(
                    equal_list
                ), f"multitask_labels keys are not equal in all frames, sub_key_list: {sub_key_list}"

                for sub_key in sample_queue[0]["multitask_labels"].keys():  # box, occ, god, map, od, lidar_only
                    tmp_sub_dict = dict()
                    for sub_sub_key in sample_queue[0]["multitask_labels"][sub_key].keys():
                        # print(f"{sub_key}.{sub_sub_key}")
                        """
                        box.gt_labels
                        box.gt_boxes
                        box.instance_inds
                        god.gt_labels
                        god.gt_boxes
                        god.instance_inds
                        occ.semantic
                        occ.semantic_mask
                        """
                        key_mapping = {
                            "gt_labels": "gt_labels_3d",
                            "gt_boxes": "gt_bboxes_3d",
                        }
                        if sub_sub_key in ["gt_boxes"]:
                            tmp_sub_dict[
                                key_mapping.get(sub_sub_key, sub_sub_key)
                            ] = _convert_to_lidar_instance_multitask(
                                [sample_queue[i]["multitask_labels"][sub_key] for i in range(len(sample_queue))],
                                "gt_boxes",
                            )  # (num_frames, LidarBox)
                        elif sub_sub_key == "predict_attribute":
                            # gt_forecasting: gt_forecasting_locs/gt_forecasting_masks/gt_forecasting_angles/gt_forecasting_bboxes
                            predict_attribute = dict()
                            all_keys = [
                                set(sample_queue[i]["multitask_labels"].keys()) for i in range(len(sample_queue))
                            ]
                            equal_list = [all_keys[0] == i for i in all_keys]
                            assert all(
                                equal_list
                            ), f"predict_attribute keys are not equal in all frames, all_keys: {all_keys}"
                            # 预测属性的子键可能不一致，需处理
                            for sub_key in sample_queue[0]["multitask_labels"][sub_key][sub_sub_key].keys():
                                predict_attribute[sub_key] = [
                                    [
                                        sample_queue[0]["multitask_labels"][sub_key][sub_sub_key]
                                        for i in range(len(sample_queue))
                                    ]
                                ]
                            tmp_sub_dict[sub_sub_key] = predict_attribute
                        else:
                            tmp_sub_dict[key_mapping.get(sub_sub_key, sub_sub_key)] = [
                                sample_queue[i]["multitask_labels"][sub_key][sub_sub_key]
                                for i in range(len(sample_queue))
                            ]  # (num_frames, ...)
                    multitask_labels[sub_key] = tmp_sub_dict
                batch[key] = [multitask_labels]
        return batch

    def multiframe_to_batch_train(self, sample_queue):
        batch = dict()
        for key in sample_queue[0].keys():
            if key == "gt_labels":
                batch["gt_labels_3d"] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "gt_boxes":
                batch["gt_bboxes_3d"] = [self._convert_to_lidar_instance(sample_queue, key)]
            elif key in [
                "points",
                "lidar2ego",
                "ego2global",
                "instance_inds",
                "targets",
                "pe_embedding",
                "tran_mats_dict",
                "group_idx",
                "scene_frame_idx",
            ]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "predict_attribute":
                # gt_forecasting: gt_forecasting_locs/gt_forecasting_masks/gt_forecasting_angles/gt_forecasting_bboxes
                predict_attribute = dict()
                for sub_key in sample_queue[0][key].keys():
                    predict_attribute[sub_key] = [[sample_queue[i][key][sub_key] for i in range(len(sample_queue))]]
                batch[key] = [predict_attribute]
            elif key in [
                "sequence_pe",
                # 'sequence_data',
                # 'sequence_data_noise',
                # 'valid_mask_seq',
                "fork_idx",
                "end_idx",
                "seq_attribute",
            ]:
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]
            elif key == "non_focus_area":
                batch[key] = [[sample_queue[i][key] for i in range(len(sample_queue))]]

        return batch

    def get_img_metas(self, batch, sample_queue):
        batch["img_metas"] = [
            {
                "ida_mats": [sample_queue[i]["ida_mats"] for i in range(len(sample_queue))]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "ida_mats_detail": [sample_queue[i]["ida_mats_detail"] for i in range(len(sample_queue))]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "lidar2imgs": [sample_queue[i]["lidar2imgs"] for i in range(len(sample_queue))]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "pad_shape": [
                    [
                        tuple(sample_queue[i]["imgs"][j].shape[1:]).__add__((3,))
                        for j in range(len(sample_queue[i]["imgs"]))
                    ]
                    for i in range(len(sample_queue))
                ]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "img_shape": [
                    [
                        tuple(sample_queue[i]["imgs"][j].shape[1:]).__add__((3,))
                        for j in range(len(sample_queue[i]["imgs"]))
                    ]
                    for i in range(len(sample_queue))
                ]
                if getattr(self, "image", False)
                else [[] for i in range(len(sample_queue))],
                "box_type_3d": [LiDARInstance3DBoxes for i in range(len(sample_queue))],
                "lidar2ego": [sample_queue[i]["lidar2ego"] for i in range(len(sample_queue))],
            }
        ]

    def __getitem__(self, index: int) -> dict:
        # print("PrivateMultitaskDataset __getitem__ index: ", index)
        while True:
            index_list, clip_json_path = self.generate_track_data_indexes(index)
            if len(index_list) != self.num_frames_per_sample:
                index = self._rand_index()
                continue
            sample_queue = []
            for i in range(len(index_list)):
                if index_list[i] < 0:  # 仅有一个 json 用于训练时，会出现负数 index
                    index = self._rand_index()
                    break
                data_dict = self.get_single_data(index_list[i])
                if data_dict is None:
                    index = self._rand_index()
                    break
                else:
                    sample_queue.append(data_dict)
            if len(sample_queue) == len(
                index_list
            ):  # and sum([len(sample_queue[i]["gt_labels"]) for i in range(len(sample_queue))]) != 0:
                # 尝试运行 pipeline，如果报错，则捕获异常、记录错误，并继续循环
                valid = True

                try:
                    pipeline_out = self.pipeline(sample_queue)
                except Exception as e:
                    index = self._rand_index()
                    print(f"pipeline exception e: {e} self.pipeline continue!!!")
                    continue
                    # raise e

                for num_index, data_dict in enumerate(pipeline_out):
                    if "points" in data_dict and len(data_dict["points"]) == 0:
                        index = self._rand_index()
                        valid = False
                        break  # break for loop
                    if (
                        "points" in data_dict
                        and (
                            (data_dict["points"][:, 0] > -32)
                            & (data_dict["points"][:, 0] < 32)
                            & (data_dict["points"][:, 1] > 0)
                            & (data_dict["points"][:, 1] < 120)
                            & (data_dict["points"][:, 2] > -3)
                            & (data_dict["points"][:, 2] < 5)
                        ).sum()
                        == 0
                    ):
                        index = self._rand_index()
                        valid = False
                        break  # break for loop
                    if (
                        "occ" in data_dict["multitask_labels"]
                        and "points" in data_dict
                        and len(data_dict["points"]) < 1000
                    ):
                        self.loader_output["frame_data_list"].cal_useless_data(
                            index + num_index, "lidar points is out of ROI"
                        )
                        index = self._rand_index()
                        valid = False
                        break  # break for loop

                if valid:
                    break  # break while loop
                else:
                    del sample_queue  # continue while loop
            else:
                del sample_queue  # continue while loop

        assert len(sample_queue) == len(index_list), "len(sample_queue) != len(index_list)"
        # sample_queue = self.pipeline(sample_queue)
        sample_queue = pipeline_out
        batch = {}  # reorg multi-frame in dict
        if "task_list" in sample_queue[0]:
            batch["task_list"] = sample_queue[0]["task_list"]
        batch.update(self.multiframe_to_batch_basic(sample_queue))

        if self.mode == "train":
            batch.update(self.multiframe_to_batch_train(sample_queue))
            if "img_semantic_seg" in batch:
                batch["img_semantic_seg"] = batch["img_semantic_seg"].astype(bool)
        else:
            if "points" in sample_queue[0]:
                batch["points"] = [[sample_queue[i]["points"] for i in range(len(sample_queue))]]
        self.get_img_metas(batch, sample_queue)

        if "bda_mat" in sample_queue[0].keys():
            batch["img_metas"][0].update(bda_mat=[sample_queue[i]["bda_mat"] for i in range(len(sample_queue))])

        batch["roi_mask"] = np.asarray(self.roi_mask, dtype=np.float32)[np.newaxis]  # (1, 4)
        if self.fov_boardline is not None:
            batch["fov_boardline"] = np.array(self.fov_boardline)[np.newaxis]
        # NOTE: Method 1: 压缩语义分割数据(int64(8 Byte) -> bool(1 Byte))
        batch["clip_json_path"] = [clip_json_path]

        return batch

    def evaluate(self, det_annos: list, **kwargs):
        return self.evaluator.evaluate(det_annos, **kwargs)

    def evaluate_tracking(self, det_annos: list, **kwargs):
        return self.evaluator.evaluate_tracking(det_annos, **kwargs)

    @staticmethod
    def collate_fn(data: list, max_radar_num=200) -> dict:
        batch_collection = dict()
        all_keys = [k for i in range(len(data)) for k in data[i].keys()]
        for key in set(all_keys):
            if key in ["imgs", "map1s", "map2s"]:
                data_list = np.concatenate([iter_data[key] for iter_data in data if key in iter_data])
                batch_collection[key] = data_list
            elif key in ["img_semantic_seg", "sequence_data", "sequence_data_noise", "valid_mask_seq"]:
                data_list = np.concatenate([iter_data[key] for iter_data in data if key in iter_data])
                batch_collection[key] = data_list
            elif key == "radar_points":
                data_list = np.concatenate(
                    [
                        np.concatenate(
                            [
                                padding_radar(iter_data[key][fidx], max_radar_num)[np.newaxis, ...]
                                for fidx in range(len(iter_data[key]))
                                if key in iter_data
                            ]
                        )[np.newaxis, ...]
                        for iter_data in data
                    ]
                )
                batch_collection[key] = data_list  # (2, 200, 11)
            elif key in ["roi_mask", "fov_boardline"]:
                data_list = np.concatenate(
                    [iter_data[key] for iter_data in data if key in iter_data], axis=0
                )  # (bs, 4)
                batch_collection[key] = data_list
            else:
                data_list = []
                for iter_data in data:
                    if key in iter_data:
                        data_list += iter_data[key]
                batch_collection[key] = data_list

        # NOTE: Method 2: 转化为Tensor数据(pin_memory需要设置为False)
        PrivateMultitaskDataset.convert_data2tensor(batch_collection)

        return batch_collection


if __name__ == "__main__":
    import mmcv
    from perceptron.exps.end2end.private.multitask.data_cfg.multitask_data_cfg import base_dataset_cfg as DATA_TRAIN_CFG

    data_train_od = mmcv.Config(DATA_TRAIN_CFG)
    data_train_od["annotation"]["box"]["label_key"] = "pre_labels"
    data_train_od["loader"]["only_key_frame"] = True
    data_train_od["annotation"]["box"]["occlusion_threshold"] = 1
    data_train_od["lidar"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
    data_train_od["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar", "fuser_lidar"]
    data_train_god = copy.deepcopy(data_train_od)
    # data_train_god["annotation"].pop("box")
    data_train_god["annotation"].pop("occ")

    data_train_god["num_frames_per_sample"] = 1
    data_train_god["loader"]["datasets_names"] = ["god_train_352keyclips"]
    data_train_god["loader"]["only_key_frame"] = True
    data_train_god["sensor_names"]["lidar_names"] = ["rfu_front_2_lidar"]
    data_train_god["lidar"]["lidar_names"] = ["rfu_front_2_lidar"]
    pipeline = data_train_god["pipeline"]
    object_range_filter = pipeline.pop("object_range_filter")

    point_cloud_range = object_range_filter["point_cloud_range"]
    from perceptron.data.det3d.modules.pipelines.transformation_multitask import MultiTaskObjectRangeFilter

    object_range_filter_od = dict(
        type=MultiTaskObjectRangeFilter,
        task="box",
        filter_pc=False,
        point_cloud_range=point_cloud_range,
        box_range=point_cloud_range,
        mode="train",
    )
    pipeline["object_range_filter_od"] = object_range_filter_od

    object_range_filter_god = dict(
        type=MultiTaskObjectRangeFilter,
        task="god",
        filter_pc=False,
        point_cloud_range=point_cloud_range,
        box_range=point_cloud_range,
        mode="train",
    )
    dataset = PrivateMultitaskDataset(**data_train_god)

    length = len(dataset)
    print("length", length, len(dataset.loader_output["frame_index"]))
    for idx in range(len(dataset)):
        item = dataset.__getitem__(idx)
        print(item["multitask_labels"][0]["god"]["gt_labels_3d"])
        print(item["multitask_labels"][0]["god"]["gt_bboxes_3d"])
        break
        """
        item['multitask_labels'][0]['box']['gt_labels_3d'] = [[array([0, 1])]]
        item['multitask_labels'][0]['box']['gt_bboxes_3d'] = [[LiDARInstance3DBoxes(
            tensor([[-2.7536e+01,  2.9990e+01, -4.9023e-01,  4.5264e+00,  1.8324e+00,
                1.5363e+00,  5.7032e-02,  0.0000e+00,  0.0000e+00],
                [-1.6969e+01,  8.2526e+01, -1.6416e+00,  6.4187e+00,  2.3139e+00,
                2.8808e+00,  1.7097e+00,  0.0000e+00,  0.0000e+00]]))]]
        item['multitask_labels'][0]['god']['gt_bboxes_3d'] = [[LiDARInstance3DBoxes(
            tensor([[-16.2739,  54.4368,  -0.8618,   0.1427,   0.1618,   0.7815,  -3.0679,
                0.0000,   0.0000],
                [-14.9149,  55.8507,  -0.8235,   0.1523,   0.1618,   0.7815,  -3.0679,
                0.0000,   0.0000],
                [-13.9205,  57.0772,  -0.8488,   0.1031,   0.1618,   0.8302,  -3.0679,
                0.0000,   0.0000],
                [-21.6584,  50.1702,  -0.8142,   0.1277,   0.1618,   0.7815,  -3.0679,
                0.0000,   0.0000],
                [-22.9808,  49.5016,  -0.8075,   0.1154,   0.1618,   0.7815,  -3.0679,
                0.0000,   0.0000],
                [-25.0585,  32.2122,  -0.3910,   0.1523,   0.1570,   0.7447,  -3.0679,
                0.0000,   0.0000],
                [-25.0539,  32.5099,  -0.3972,   0.1523,   0.1394,   0.7447,  -3.0679,
                0.0000,   0.0000],
                [-25.4395,  32.1987,  -0.3947,   0.1523,   0.1345,   1.0304,  -3.0679,
                0.0000,   0.0000],
                [-25.4551,  32.4844,  -0.4238,   0.1523,   0.1345,   1.0304,  -3.0679,
                0.0000,   0.0000],
                [-37.2539,  31.5645,  -0.3750,   0.1523,   0.1345,   1.0304,  -3.0679,
                0.0000,   0.0000],
                [-37.2618,  31.8679,  -0.3904,   0.1523,   0.1345,   1.0304,  -3.0679,
                0.0000,   0.0000],
                [-37.6769,  31.5188,  -0.3906,   0.1282,   0.1345,   0.7524,  -3.0679,
                0.0000,   0.0000],
                [-37.7009,  31.8658,  -0.3936,   0.1282,   0.1345,   0.7584,  -3.0679,
                0.0000,   0.0000],
                [-32.0567,  49.7446,  -0.9143,   0.3281,   0.3293,   0.7470,  -3.1416,
                0.0000,   0.0000]]))]]
        """
