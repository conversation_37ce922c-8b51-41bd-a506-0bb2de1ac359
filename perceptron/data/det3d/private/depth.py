import numpy as np
import torch
from base import DatasetBase
from loguru import logger

# TODO[@wuxiaolei]: 需要根据新的代码重构


class DatasetWithDepth(DatasetBase):
    def __init__(self, cars, cameras, mode, loader, image, annotation, augment, extention, depth):
        super(DatasetWithDepth, self).__init__(cars, cameras, mode, loader, image, annotation, augment, extention)
        self.depth = depth[0](cameras=cameras, loader=self.loader, **depth[1])
        self.get_2d_masks = False
        if "get_2d_masks" in depth[2].keys() and depth[2]["get_2d_masks"]:
            self.get_2d_masks = True

    def __getitem__(self, index):
        # image
        while True:
            images, lidar2imgs, img_metas = self.image.get_images(index)
            if images is not None:
                break
            logger.warning("index {} images is None.".format(index))
            index = self._rand_index()
        # annotation
        if self.mode == "train":
            annos = self.annotation.get_annos(index)
            if self.annotation.filter_outlier_frames and annos["has_outlier"]:
                logger.warning("index {} annotations have outlier bbox.".format(index))
                self.__getitem__(self._rand_index())
            gt_labels, gt_boxes = annos["gt_labels"], annos["gt_boxes"]
        else:
            gt_labels = np.zeros((0,), dtype=np.float32)
            gt_boxes = np.zeros((0, 7), dtype=np.float32)

        depths = self.depth._get_depth(index)
        if depths is None:
            return self.__getitem__(self._rand_index())
        if self.get_2d_masks:
            masks_2d = self.annotation.get_2d_masks(index)
        else:
            masks_2d = None
        # augment
        images, ida_mats, gt_boxes, bda_mat, depths, masks_2d = self.augment(images, gt_boxes, depths, masks_2d)
        return (index, images, lidar2imgs, ida_mats, bda_mat, img_metas, gt_boxes, gt_labels, depths, masks_2d)

    @staticmethod
    def collate_fn(data: tuple):
        """merges a list of samples to form a mini-batch of Tensor(s).

        Args:
            data (list): samples contain all elements about inputs of network

        Returns:
            dict: mini-batch of network input tensors
        """
        frame_id_batch = list()
        imgs_batch = list()
        lidar2imgs_batch = list()
        ida_mats_batch = list()
        bda_mat_batch = list()
        gt_boxes_batch = list()
        gt_labels_batch = list()
        img_metas_batch = list()
        depth_batch = list()
        masks_2d_batch = list()
        for (
            frame_id,
            imgs,
            lidar2imgs,
            ida_mats,
            bda_mat,
            img_metas,
            gt_boxes,
            gt_labels,
            depths,
            masks_2d,
        ) in data:
            frame_id_batch.append(frame_id)
            imgs_batch.append(imgs)
            lidar2imgs_batch.append(lidar2imgs)
            ida_mats_batch.append(ida_mats)
            bda_mat_batch.append(bda_mat)
            img_metas_batch.append(img_metas)
            gt_boxes_batch.append(torch.from_numpy(gt_boxes))
            gt_labels_batch.append(torch.from_numpy(gt_labels))
            depth_batch.append(torch.from_numpy(depths))
            if masks_2d is not None:
                masks_2d_batch.append(torch.from_numpy(masks_2d))

        mats_dict = dict()
        mats_dict["lidar2imgs"] = torch.from_numpy(np.stack(lidar2imgs_batch))
        mats_dict["ida_mats"] = torch.from_numpy(np.stack(ida_mats_batch))
        mats_dict["bda_mat"] = torch.from_numpy(np.stack(bda_mat_batch))

        data_dict = dict(
            frame_id=frame_id_batch,
            imgs=torch.from_numpy(np.stack(imgs_batch)),
            mats_dict=mats_dict,
            img_metas=img_metas_batch,
            gt_boxes=gt_boxes_batch,
            gt_labels=gt_labels_batch,
            depths=torch.from_numpy(np.stack(depth_batch)),
        )
        if len(masks_2d_batch) > 0:
            data_dict["masks_2d"] = torch.from_numpy(np.stack(masks_2d_batch))
        return data_dict

    @staticmethod
    def collate_fn_numpy(data: tuple):
        """merges a list of samples to form a mini-batch of Tensor(s).

        Args:
            data (list): samples contain all elements about inputs of network

        Returns:
            dict: mini-batch of network input tensors
        """
        frame_id_batch = list()
        imgs_batch = list()
        lidar2imgs_batch = list()
        ida_mats_batch = list()
        bda_mat_batch = list()
        gt_boxes_batch = list()
        gt_labels_batch = list()
        img_metas_batch = list()
        depth_batch = list()
        masks_2d_batch = list()
        for (
            frame_id,
            imgs,
            lidar2imgs,
            ida_mats,
            bda_mat,
            img_metas,
            gt_boxes,
            gt_labels,
            depths,
            masks_2d,
        ) in data:
            frame_id_batch.append(frame_id)
            imgs_batch.append(imgs)
            lidar2imgs_batch.append(lidar2imgs)
            ida_mats_batch.append(ida_mats)
            bda_mat_batch.append(bda_mat)
            img_metas_batch.append(img_metas)
            gt_boxes_batch.append(gt_boxes)
            gt_labels_batch.append(gt_labels)
            depth_batch.append(torch.from_numpy(depths))
            if masks_2d is not None:
                masks_2d_batch.append(torch.from_numpy(masks_2d))

        mats_dict = dict()
        mats_dict["lidar2imgs"] = np.stack(lidar2imgs_batch)
        mats_dict["ida_mats"] = np.stack(ida_mats_batch)
        mats_dict["bda_mat"] = np.stack(bda_mat_batch)

        data_dict = dict(
            frame_id=frame_id_batch,
            imgs=np.stack(imgs_batch),
            mats_dict=mats_dict,
            img_metas=img_metas_batch,
            gt_boxes=gt_boxes_batch,
            gt_labels=gt_labels_batch,
            depths=np.stack(depth_batch),
        )
        if masks_2d is None and len(masks_2d_batch) > 0:
            data_dict["masks_2d"] = np.stack(masks_2d_batch)
        return data_dict
