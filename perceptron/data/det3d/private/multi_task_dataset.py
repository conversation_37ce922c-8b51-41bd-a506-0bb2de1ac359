import torch
import math
import random
import itertools
import numpy as np
from typing import List, Optional
from torch.utils.data.sampler import <PERSON><PERSON>
from torch.utils.data.distributed import DistributedSampler


class MultiTaskDistributedSampler(DistributedSampler):
    def __init__(self, dataset, dataset_boundaries, batch_size, shuffle=False, drop_last=True):
        # 检测是否启用分布式环境
        self.is_distributed = torch.distributed.is_available() and torch.distributed.is_initialized()
        self.epoch = 0

        if self.is_distributed:
            super().__init__(dataset, shuffle=shuffle, drop_last=drop_last)
        else:
            self.num_replicas = 1
            self.rank = 0
            self.shuffle = shuffle

        self.dataset_boundaries = dataset_boundaries
        self.num_datasets = len(dataset_boundaries) - 1
        self.batch_size = batch_size
        assert self.batch_size >= self.num_datasets, "为避免异常, 当前只支持batch_size >= num_datasets"
        self.dataset_lengths = [dataset_boundaries[i + 1] - dataset_boundaries[i] for i in range(self.num_datasets)]
        print("self.dataset_lengths ", self.dataset_lengths)
        self.num_samples = max(self.dataset_lengths) * self.num_datasets  # 确保所有数据集都能被采样
        self.total_size = self.num_samples * self.num_replicas
        # if self.drop_last and len(self.dataset) % self.num_replicas != 0:  # type: ignore[arg-type]
        #     # Split to nearest available length that is evenly divisible.
        #     # This is to ensure each rank receives the same amount of data when
        #     # using this Sampler.
        #     self.num_samples = math.ceil(
        #         (len(self.dataset) - self.num_replicas) / self.num_replicas  # type: ignore[arg-type]
        #     )
        self.num_samples_per_replica = math.ceil(self.num_samples / self.num_replicas)

    def __iter__(self):
        # 生成每个数据集的索引序列
        indices = []
        # 按固定顺序排列样本
        for i in range(self.num_datasets):
            start, end = self.dataset_boundaries[i], self.dataset_boundaries[i + 1]
            # 循环取样，确保每个数据集的索引足够多
            dataset_indices = list(range(start, end)) * (self.num_samples // (end - start) + 1)
            indices.append(dataset_indices)

        # 打乱顺序(如果需要)
        if self.shuffle:
            g = torch.Generator()
            g.manual_seed(self.epoch)
            new_indices = []
            for dataset_indices in indices:
                random_indices = torch.randperm(len(dataset_indices), generator=g).tolist()
                dataset_indices = [dataset_indices[i] for i in random_indices]
                new_indices.append(dataset_indices)
            indices = new_indices

        batch_indices = []
        for i in range(0, self.num_samples):
            batch_indices.append(indices[i % self.num_datasets][i // self.num_datasets])

        # 分配给每个副本
        if self.is_distributed:
            # 按照不同的rank来排序
            # rank0_bs0, rank0_bs1, rank0_bs0,..., rank0_bs1, rank1_bs0,..., rank1_bs1,..., rankn_bsx
            # indices = batch_indices[self.rank::self.num_replicas]
            indices = []
            for i in range(self.num_samples_per_replica):
                if (i + 1) * self.batch_size * self.num_replicas <= len(batch_indices):
                    # 按照每个rank的batch来排序
                    # rank0_bs0,..., rank0_bsn, rank1_bs0,..., rank1_bsn,....,rank0_bs0,...

                    # rank0, rank1, ..., rank0, rank1
                    # bs,,,, bs,,,
                    # 0,1,2, 3,4,5
                    indices += batch_indices[
                        (self.num_replicas * i + self.rank)
                        * self.batch_size : (self.num_replicas * i + self.rank + 1)
                        * self.batch_size
                    ]
                else:
                    # 最后一个部分均分
                    # rank0, rank1, rank0
                    # 6,     7,     8
                    for j in range(i * self.num_replicas * self.batch_size, len(batch_indices)):
                        if j % self.batch_size == self.rank:
                            indices.append(batch_indices[j])
            # indices = batch_indices[self.rank::self.num_replicas]
            # # if self.rank == 0:
        else:
            indices = batch_indices
        return iter(indices)

    def __len__(self):
        if self.is_distributed:
            return self.num_samples_per_replica
        else:
            return self.num_samples

    def set_epoch(self, epoch):
        # self.epoch = epoch
        pass


def get_dataset_boundaries(dataset_list):
    assert len(dataset_list) > 1
    dataset_lengths = [len(i) for i in dataset_list]
    dataset_boundaries = [0] + np.cumsum(dataset_lengths).tolist()
    return dataset_boundaries


def get_dataset_boundariesv2(dataset_list):
    # assert len(dataset_list) > 1
    dataset_lengths = [len(i) for i in dataset_list]
    dataset_boundaries = [0] + np.cumsum(dataset_lengths).tolist()
    return dataset_boundaries


def collate_fn_wrapper(collate_fn_func, is_training=True):
    def inner(data: list, *args, **kwargs):
        task_set_list = []

        add_task_list_flag = False
        if not is_training:
            task_list_mask = ["task_list" in data_i for data_i in data]
            if any(task_list_mask):
                add_task_list_flag = True
        for data_i in data:
            if "task_list" in data_i:
                task_set_list.append(data_i.pop("task_list"))
            elif add_task_list_flag:
                task_set_list.append(["box", "occ", "map"])
        result = collate_fn_func(data, *args, **kwargs)
        if len(task_set_list) > 0:
            result["task_list"] = task_set_list
        return result

    return inner


class FlexibleMultiTaskIntervalSamplerV2(DistributedSampler):
    def __init__(
        self,
        dataset,
        dataset_boundaries: List[int],
        per_gpu_dataset_batch_sizes: List[int],
        shuffle: bool = False,
        seed: int = 0,
        rank: Optional[int] = None,
        world_size: Optional[int] = None,
        drop_last: bool = False,
        intervals: Optional[List[int]] = None,
        sample_len_type: str = "max",
    ):
        """
        Args:
            dataset: 组合数据集对象
            dataset_boundaries: 数据集边界列表，如[0, 100, 200]表示两个数据集各100样本
            per_gpu_dataset_batch_sizes: 每个GPU从各数据集采样的数量，如[4, 2]
            shuffle: 是否打乱数据顺序
            seed: 随机种子
            rank: 当前进程的rank
            world_size: 总进程数
            drop_last: 是否丢弃不完整批次
            intervals: 各数据集的采样间隔（默认1）
            sample_len_type: 采样长度类型，"max"表示最大长度，"min"表示最小长度
        """
        self.is_distributed = torch.distributed.is_available() and torch.distributed.is_initialized()
        if self.is_distributed:
            super().__init__(dataset, shuffle=shuffle, drop_last=drop_last)
        else:
            self.num_replicas = 1
            self.rank = 0
            self.shuffle = shuffle
            self.drop_last = drop_last

        # 参数验证
        assert len(dataset_boundaries) >= 2, "至少需要两个数据集边界"
        self.num_datasets = len(dataset_boundaries) - 1
        assert len(per_gpu_dataset_batch_sizes) == self.num_datasets, "批次大小设置与数据集数量不匹配"

        # 初始化核心参数
        self.dataset_boundaries = dataset_boundaries
        self.per_gpu_batch_sizes = per_gpu_dataset_batch_sizes
        self.total_per_gpu_batch = sum(per_gpu_dataset_batch_sizes)
        self._base_seed = seed
        self.intervals = intervals if intervals else [1] * self.num_datasets

        # 计算数据集元信息
        self.original_sizes = [dataset_boundaries[i + 1] - dataset_boundaries[i] for i in range(self.num_datasets)]
        self.adjusted_sizes = [size // interval for size, interval in zip(self.original_sizes, self.intervals)]
        assert isinstance(dataset, torch.utils.data.ConcatDataset), "多合一任务使用ConcatDataset"
        assert [len(i) for i in dataset.datasets] == self.original_sizes, "ConcatDataset中的数据集数量与边界不匹配"
        print("self.original_sizes ", self.original_sizes)
        print("self.adjusted_sizes ", self.adjusted_sizes)

        # 创建分片采样器
        self.dataset_samplers = [self._create_dataset_sampler(dataset_idx) for dataset_idx in range(self.num_datasets)]
        assert sample_len_type in ["max", "min"], "sample_len_type must be 'max' or 'min'"
        self.sample_len_type = sample_len_type

    def _create_dataset_sampler(self, dataset_idx: int):
        """创建单个数据集的分片采样器"""
        start = self.dataset_boundaries[dataset_idx]
        interval = self.intervals[dataset_idx]
        size = self.adjusted_sizes[dataset_idx]

        return _DatasetShardSampler(
            base_offset=start,
            size=size,
            interval=interval,
            shuffle=self.shuffle,
            seed=self._base_seed + dataset_idx * 1000,
            rank=self.rank,
            num_replicas=self.num_replicas,
            drop_last=self.drop_last,
        )

    def __iter__(self):
        # 创建各数据集的迭代器
        iterators = [iter(sampler) for sampler in self.dataset_samplers]

        while True:
            # 按比例收集样本
            batch = []
            for dataset_idx, count in enumerate(self.per_gpu_batch_sizes):
                for _ in range(count):
                    batch.append(next(iterators[dataset_idx]))

            yield from batch

    def set_epoch(self, epoch: int):
        """设置epoch影响所有子采样器"""
        self.epoch = epoch
        for sampler in self.dataset_samplers:
            sampler.set_epoch(epoch)

    def __len__(self):
        if self.sample_len_type == "min":
            # 最小长度：取所有数据集的调整后大小的最小值
            return math.ceil(min(self.adjusted_sizes) * self.num_datasets / self.num_replicas)
        elif self.sample_len_type == "max":
            return math.ceil(max(self.adjusted_sizes) * self.num_datasets / self.num_replicas)


class _DatasetShardSampler(Sampler):
    """处理单个数据集的分片采样"""

    def __init__(
        self,
        base_offset: int,
        size: int,
        interval: int,
        shuffle: bool,
        seed: int,
        rank: int,
        num_replicas: int,
        drop_last: bool,
    ):
        super().__init__(None)
        self.base_offset = base_offset
        self.size = size
        self.interval = interval
        self.shuffle = shuffle
        self.seed = seed
        self.rank = rank
        self.num_replicas = num_replicas
        self.drop_last = drop_last
        self.generator = torch.Generator()
        self.generator.manual_seed(seed)

    def __iter__(self):
        # 生成全局索引流
        indices = self._infinite_indices()
        # 应用分片逻辑
        indices = itertools.islice(indices, self.rank, None, self.num_replicas)
        # 添加间隔和随机偏移
        for idx in indices:
            yield self.base_offset + idx * self.interval + torch.randint(
                0, self.interval, (), generator=self.generator
            ).item()

    def _infinite_indices(self):
        """无限索引生成"""
        while True:
            if self.shuffle:
                yield from torch.randperm(self.size, generator=self.generator).tolist()
            else:
                yield from range(self.size)

    def set_epoch(self, epoch: int):
        """通过改变种子实现epoch切换"""
        self.generator.manual_seed(self.seed + epoch * 1000)

    def __len__(self):
        if self.drop_last:
            return self.size // self.num_replicas
        else:
            return (self.size + self.num_replicas - 1) // self.num_replicas


class FlexibleMultiTaskMultiDataIntervalSampler(DistributedSampler):
    def __init__(
        self,
        dataset,
        dataset_boundaries: List[List[int]],
        per_gpu_dataset_batch_sizes: List[int],
        shuffle: bool = False,
        seed: int = 0,
        rank: Optional[int] = None,
        world_size: Optional[int] = None,
        drop_last: bool = False,
        intervals: Optional[List[List[int]]] = None,
        task_dataset_ratios: Optional[List[List[float]]] = None,
    ):
        """
        优化版支持多任务内多数据集采样，支持指定每个任务内数据集的采样比例

        Args:
            dataset: 组合数据集对象
            dataset_boundaries: 二维列表，每个任务的数据集边界
                [[任务1数据集1起点, 任务1数据集1终点, 任务1数据集2终点, ...],
                 [任务2数据集1起点, 任务2数据集1终点, ...], ...]
            per_gpu_dataset_batch_sizes: 一维列表，每个GPU从各任务采样的总数量，如[4, 2]
            shuffle: 是否打乱数据顺序
            seed: 随机种子
            rank: 当前进程的rank
            world_size: 总进程数
            drop_last: 是否丢弃不完整批次
            intervals: 二维列表，各任务中各数据集的采样间隔
                [[任务1数据集1间隔, 任务1数据集2间隔, ...],
                 [任务2数据集1间隔, ...], ...]
            task_dataset_ratios: 二维列表，各任务内数据集的采样比例
                [[任务1数据集1比例, 任务1数据集2比例, ...],
                 [任务2数据集1比例, ...], ...]
        """
        # 分布式初始化
        self.is_distributed = torch.distributed.is_available() and torch.distributed.is_initialized()
        if self.is_distributed:
            super().__init__(dataset, shuffle=shuffle, drop_last=drop_last)
        else:
            self.num_replicas = 1
            self.rank = 0
            self.shuffle = shuffle
            self.drop_last = drop_last

        # 参数验证
        # assert len(dataset_boundaries) > 0, "至少需要一个任务"
        self.num_tasks = len(dataset_boundaries)
        assert len(per_gpu_dataset_batch_sizes) == self.num_tasks, "任务数量不匹配"

        # 计算每个任务的数据集数量
        self.per_task_num_datasets = [len(boundaries) - 1 for boundaries in dataset_boundaries]

        # 初始化核心参数
        self.dataset_boundaries = dataset_boundaries
        self.per_gpu_batch_sizes = per_gpu_dataset_batch_sizes
        self.total_per_gpu_batch = sum(per_gpu_dataset_batch_sizes)
        self._base_seed = seed

        # 设置默认采样间隔
        if intervals is None:
            self.intervals = [[1] * num_ds for num_ds in self.per_task_num_datasets]
        else:
            assert len(intervals) == self.num_tasks, "间隔任务数量不匹配"
            assert all(len(iv) == num_ds for iv, num_ds in zip(intervals, self.per_task_num_datasets)), "任务内间隔数量不匹配"
            self.intervals = intervals

        # 处理数据集比例参数
        if task_dataset_ratios is None:
            # 默认使用数据集调整后大小的比例
            self.task_dataset_ratios = []
            for task_idx in range(self.num_tasks):
                adjusted_sizes = []
                for ds_idx in range(self.per_task_num_datasets[task_idx]):
                    ds_start = self.dataset_boundaries[task_idx][ds_idx]
                    ds_end = self.dataset_boundaries[task_idx][ds_idx + 1]
                    interval = self.intervals[task_idx][ds_idx]
                    adjusted_sizes.append((ds_end - ds_start) // interval)
                total_size = sum(adjusted_sizes)
                self.task_dataset_ratios.append([size / total_size for size in adjusted_sizes])
        else:
            # 验证自定义比例
            assert len(task_dataset_ratios) == self.num_tasks, "比例任务数量不匹配"
            assert all(
                len(ratios) == num_ds for ratios, num_ds in zip(task_dataset_ratios, self.per_task_num_datasets)
            ), "任务内比例数量不匹配"
            # 归一化比例
            self.task_dataset_ratios = []
            for task_ratios in task_dataset_ratios:
                total_ratio = sum(task_ratios)
                self.task_dataset_ratios.append([ratio / total_ratio for ratio in task_ratios])

        # 计算全局任务偏移量
        self.task_global_offsets = [0]
        for i in range(self.num_tasks - 1):
            total_task_size = self.dataset_boundaries[i][-1]  # 任务总长度
            self.task_global_offsets.append(self.task_global_offsets[-1] + total_task_size)

        # 创建每个任务内每个数据集的采样器
        self.dataset_samplers = []
        for task_idx in range(self.num_tasks):
            task_samplers = []
            for ds_idx in range(self.per_task_num_datasets[task_idx]):
                # 计算全局偏移 = 任务全局偏移 + 任务内数据集偏移
                base_offset = self.task_global_offsets[task_idx] + self.dataset_boundaries[task_idx][ds_idx]

                # 计算数据集原始大小
                ds_start = self.dataset_boundaries[task_idx][ds_idx]
                ds_end = self.dataset_boundaries[task_idx][ds_idx + 1]
                original_size = ds_end - ds_start

                # 计算调整后大小（考虑采样间隔）
                interval = self.intervals[task_idx][ds_idx]
                adjusted_size = original_size // interval

                # 创建数据集采样器
                sampler = _DatasetShardSampler(
                    base_offset=base_offset,
                    size=adjusted_size,
                    interval=interval,
                    shuffle=self.shuffle,
                    seed=self._base_seed + task_idx * 1000 + ds_idx,
                    rank=self.rank,
                    num_replicas=self.num_replicas,
                    drop_last=self.drop_last,
                )
                task_samplers.append(sampler)
            self.dataset_samplers.append(task_samplers)

        # 为每个任务创建任务级采样器
        self.task_samplers = []
        for task_idx in range(self.num_tasks):
            sampler = _TaskLevelSampler(
                num_datasets=self.per_task_num_datasets[task_idx],
                ratios=self.task_dataset_ratios[task_idx],
                seed=self._base_seed + task_idx * 2000,
            )
            self.task_samplers.append(sampler)

        # 打印调试信息
        self._print_debug_info()

    def __iter__(self):
        # 创建二维迭代器列表 [任务][数据集]
        dataset_iterators = [[iter(sampler) for sampler in task_samplers] for task_samplers in self.dataset_samplers]

        # 创建任务级迭代器
        task_iterators = [iter(sampler) for sampler in self.task_samplers]

        # 为每个任务创建计数器
        task_counters = [0] * self.num_tasks

        while True:
            batch = []
            # 为每个任务采样指定数量的样本
            for task_idx in range(self.num_tasks):
                target_count = self.per_gpu_batch_sizes[task_idx]

                # 从当前任务采样直到达到目标数量
                while task_counters[task_idx] < target_count:
                    # 获取下一个要采样的数据集
                    ds_idx = next(task_iterators[task_idx])

                    # 从该数据集采样一个样本
                    try:
                        batch.append(next(dataset_iterators[task_idx][ds_idx]))
                        task_counters[task_idx] += 1
                    except StopIteration:
                        # 如果数据集耗尽，重新初始化迭代器
                        dataset_iterators[task_idx][ds_idx] = iter(self.dataset_samplers[task_idx][ds_idx])
                        batch.append(next(dataset_iterators[task_idx][ds_idx]))
                        task_counters[task_idx] += 1

                # 重置任务计数器
                task_counters[task_idx] = 0

            yield from batch

    def set_epoch(self, epoch: int):
        """设置epoch影响所有子采样器"""
        self.epoch = epoch
        for task_samplers in self.dataset_samplers:
            for sampler in task_samplers:
                sampler.set_epoch(epoch)
        for sampler in self.task_samplers:
            sampler.set_epoch(epoch)

    def __len__(self):
        """估计采样器长度（最大数据集长度）"""
        max_adjusted_size = 0
        for task_samplers in self.dataset_samplers:
            task_num = 0
            for sampler in task_samplers:
                task_num += sampler.size
                # if sampler.size > max_adjusted_size:
                #     max_adjusted_size = sampler.size
            if task_num > max_adjusted_size:
                max_adjusted_size = task_num

        # total_datasets = sum(self.per_task_num_datasets)
        total_datasets = len(self.per_task_num_datasets)
        return math.ceil(max_adjusted_size * total_datasets / self.num_replicas)

    def _print_debug_info(self):
        """打印调试信息"""
        if self.rank == 0:
            print("\n===== FlexibleMultiTaskIntervalSampler Configuration =====")
            print(f"Number of tasks: {self.num_tasks}")
            print(f"Per GPU batch sizes: {self.per_gpu_batch_sizes}")
            print(f"Total per GPU batch size: {self.total_per_gpu_batch}")
            print(f"Shuffle: {self.shuffle}, Seed: {self._base_seed}")

            for task_idx in range(self.num_tasks):
                print(f"\nTask {task_idx} Configuration:")
                print(f"  Global offset: {self.task_global_offsets[task_idx]}")
                print(f"  Task batch size: {self.per_gpu_batch_sizes[task_idx]}")
                print(f"  Dataset ratios: {self.task_dataset_ratios[task_idx]}")

                for ds_idx in range(self.per_task_num_datasets[task_idx]):
                    ds_start = self.dataset_boundaries[task_idx][ds_idx]
                    ds_end = self.dataset_boundaries[task_idx][ds_idx + 1]
                    interval = self.intervals[task_idx][ds_idx]
                    print(f"  Dataset {ds_idx}:")
                    print(f"    Range: {ds_start}-{ds_end} (size: {ds_end - ds_start})")
                    print(f"    Interval: {interval}")
                    print(f"    Adjusted size: {(ds_end - ds_start) // interval}")
                    print(f"    Ratio: {self.task_dataset_ratios[task_idx][ds_idx]:.4f}")

            print("=========================================================\n")


class _TaskLevelSampler:
    """任务级数据集选择器，按指定比例选择数据集"""

    def __init__(self, num_datasets: int, ratios: List[float], seed: int):
        self.num_datasets = num_datasets
        self.ratios = ratios
        self.seed = seed
        self.generator = random.Random(seed)

        # 验证比例
        assert len(ratios) == num_datasets, "比例数量与数据集数量不匹配"
        assert abs(sum(ratios) - 1.0) < 1e-5, f"比例总和不为1: {sum(ratios)}"

        # 创建累积分布
        self.cum_ratios = np.cumsum(ratios).tolist()

    def __iter__(self):
        while True:
            # 生成随机数
            r = self.generator.random()

            # 找到对应的数据集索引
            for i in range(self.num_datasets):
                if r <= self.cum_ratios[i]:
                    yield i
                    break

    def set_epoch(self, epoch: int):
        """通过改变种子实现epoch切换"""
        self.generator = random.Random(self.seed + epoch * 1000)
