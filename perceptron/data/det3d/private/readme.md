以下是本次重构的主要变动，已经合并到 master 分支

1. **庆阳集群适配**

   - 通过 `utils.get_cluster()` 判断集群类型（brain / qy），并在数据读取时同时适配 nori 以及挂载于 `/mnt` 的 GPFS 等多种方式。

2. **去畸变与图像预处理流程重构**

   舍弃 `postpone_undistort`，`img_warp_maps_dict` 和 `two_stage_warp`，添加`target_extrinsic`。主要变更是将 resize first 固定为 True，并在读取图像后进行 resize 和 crop 操作以获得统一尺寸的图像，确保所有 sensor 的图像都会被调整到相同的目标尺寸，同时保持原始图像的长宽比。具体说明如下：

   - 移除了 resize_first 的条件判断，固定为 True
   - 在图像处理流程中添加了保持长宽比的 resize 和 crop操作
   - 确保所有图像最终都具有相同的 target_resolution 尺寸
   - 去畸变操作放在 resize 和 crop 之后进行

3. **新增 UndistortWarp 类（继承自 UndistortSimFov）**

   之前标准相机使用的是 `UndistortStandard`，模拟 fov 视角使用的是 `UndistortSimFov`，现在统一使用 `UndistortWarp` 接口进行处理，根据相机类型选择不同的畸变校正方法。

   1. 根据传感器名称和畸变模型（fisheye 或 pinhole），调用基类的方法（`UndistortSimFov` 或 `UndistortStandard`）进行去畸变处理。
   2. 获取 `lidar2pix`，用于计算 LiDAR 点在相机图像中的投影坐标，计算新的内参和外参投影矩阵，将相机图像与 LiDAR 数据对齐。
   3. 通过 RANSAC 拟合单应矩阵，计算图像透视变换矩阵 `img_warp_matrix`。
   4. 更新去畸变内参矩阵和目标外参的投影矩阵到 `camera_calib`。
   5. 通过 `combine_remap` 将「去畸变映射」与「透视变换映射」合并成一次性的计算图像映射表 `(map1_new, map2_new)`。

4. **ImageSimFov 缓存机制调整**

   - 同一物理相机的不同命名（如 `cam_front_30_sim_fov` 与 `cam_front_30`）可共享标定参数。
   - 仅当 camera_name 中带有 `_sim_fov` 后缀时才触发参数复用逻辑，`hidden_name` 则是去除 `_sim_fov` 后缀后的名称。
   - 通过 `id(sensors_info[hidden_name])` 判断是否为同一内存地址，进而实现参数复用。

5. **后处理部分**

   - 后处理部分包括 `postcollate_pipeline` 和 `preforward_pipeline`
     - `postcollate_pipeline` 中包括 `convert_data2tensor` 和 `load_tensor2gpu` 两部分，把后续处理挪到 GPU 上来做。
     - `preforward_pipeline` 中包括 `ImageUndistortGPU` 和 `ImageAffineTransGPU` 两部分，通过 `gpu_aug` 参数来选择开启，`ImageUndistortGPU` 使用 `torch.nn.functional.grid_sample` 来代替 `cv2.remap` 操作，把 Undistort 操作搬移到 GPU 上来处理。同时利用`ImageAffineTransGPU` 在 GPU 上进行数据增强操作。

6. **配置简化**

   - 移除 `perceptron/data/det3d/source/config.py` 中部分冗余配置。
   - 针对 Z10 车辆的配置文件和预处理逻辑，做了 `camera_name` 的特定适配。

   - 分辨率 `resolution` 不再根据 camera 的 `hidden_name` 匹配固定的 IMAGE_RESOLUTION（如“200w”/“800w”），而改为动态读取相机内参。

7. **添加 testcase**

   - 在 `test/data/sensor_modules` 添加了不同模态数据（image, lidar, radar），以及 pipeline 和 multimodal 的测试数据，来保证重构前后，输入到 model 中的数据一致性，这是对比的[旧分支链接](https://git-core.megvii-inc.com/e2ead/perceptron/-/tree/personal/zyk/compare_old_branch)

8. **ImageBase 传入参数调整**

   - `target_resolution` 从原本的字符串（如 `"200w"`）改为元组（如 `(1920, 1080)`），与内参的传参方式保持一致。
   - 新增 `target_extrinsic` 参数

9. **删除 `get_latest_version` 方法**

10. **值得注意的点**

    - 从 brainpp 迁移到 qy 过程中，有部分数据 json 无法使用，从 json list 中被剔除，如 `z10_label_1230_train`。
