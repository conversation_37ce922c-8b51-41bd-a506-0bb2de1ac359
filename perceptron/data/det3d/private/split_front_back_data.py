import numpy as np

from perceptron.data.det3d.private.base import DatasetBase


class DatasetSplitFrontBack(DatasetBase):
    @staticmethod
    def collate_fn(data: tuple):
        """merges a list of samples to form a mini-batch of Tensor(s).

        Args:
            data (list): samples contain all elements about inputs of network

        Returns:
            dict: mini-batch of network input tensors
        """
        batch_collection = dict()
        batch_collection["mats_dict"] = dict()
        mats_shape_list = [(3, 3), (4, 3), (4, 4)]

        for key, value in data[0].items():
            if key == "imgs":
                side_imgs = list()
                front_back_imgs = list()
                for iter_data in data:
                    side_imgs.append(np.stack(iter_data[key][:4]))
                    front_back_imgs.append(np.stack(iter_data[key][4:]))
                batch_collection["front_back_imgs"] = np.stack(front_back_imgs)
                batch_collection["side_imgs"] = np.stack(side_imgs)
            else:
                data_list = [iter_data[key] for iter_data in data]
                if isinstance(value, (list, int)):
                    batch_collection[key] = np.stack(data_list)
                elif isinstance(value, np.ndarray) and value.shape[-2:] in mats_shape_list:
                    batch_collection["mats_dict"][key] = np.stack(data_list)
                else:
                    batch_collection[key] = data_list

        return batch_collection
