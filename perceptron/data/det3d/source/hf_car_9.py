# TODO: 数据情况需要更新


# ----- 训练数据全集，定期增量回流 -----
TRAINSET = {}


# ----- 训练数据子集，人工设计规则 -----
TRAINSET_PARTIAL = {
    "0929": "s3://peppa-meg-detect/tmpdata/datasets/car9_train_0929.json",
    "1023": "s3://peppa-meg-detect/tmpdata/datasets/car9_train_1023_more.json",
    "prelabels_radar": "s3://gongjiahao-share/e2e/test-file/HF9_RADAR_prelabel_path_e2e.json",
    "prelabels_radar_all": "/home/<USER>/HF9_RADAR_prelabelALL_path_e2e.json",
    "HF_e2e_labeled": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled.json",
    "HF_e2e_labeled_861": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_861.json",
    "HF_e2e_labeled_861_with_occ": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_with_occ.json",
    "HF_e2e_labeled_bad_radar": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_bad_radar.json",
    "HF_e2e_labeled_bad_radar_with_occ": "s3://gongjiahao-share/e2e/test-file/HF_e2e_labeled_bad_radar_with_occ.json",
    "0720_0831": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0720_0831.json",
    "0901_0926_filter_bmk": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0901_0926_filted_bmk.json",
    "0720_0831_add_map_prelabels": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0720_0831_add_map_prelabels.json",
    "0901_0926_filter_bmk_add_map_prelabels": "s3://end2end/data/paths_list/det/hf/hf_car9_train_0901_0926_filted_bmk_add_map_prelabels.json",
    "debug_sample_map": "s3://wangningzi-data/dataset/e2e_map_dataset/reorg_sample/sample_trainset_det_map.json",
    "debug_sample_track": "s3://wangningzi-data/dataset/e2e_map_dataset/reorg_sample/sample_trainset_track.json",
    "quick": "/home/<USER>/bmk_quick.json",
    "map_share_prelabels_bad_radar": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel_bad_radar.json",  # 带了map
    "map_share_prelabels": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel.json",  # 带了map
    "mapbmk_align": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_list.json",  # prelabel
    "CAR9_BMK_OCC_DAY": "s3://peppa-meg-detect/tmpdata/datasets/car9_bmk_occ_day.json",
    "bmk_new_withOCC": "s3://end2end/data/paths_list/track/HF_e2e_bmk_107_with_occ.json",
    "map_share_prelabels_for_e2e": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel_for_e2e.json",
    "map_lane_mf": "s3://mj-share/admap_e2e/data_e2eformat_18-6w/train_mf_list.json",  # 18.6w_lane_mf
    "hf_label_det_cq_good_radar_1246json": "s3://gxt-share/e2e-data/hf_label_0717_det_2184_goodradar.json",
    "hf_label_det_cq_bad_radar_938json": "s3://gxt-share/e2e-data/hf_label_0717_det_2184_badradar.json",
    "hf_label_det_cq_good_radar_1246json_filter": "s3://yrn-share/tmp/hf_label_0717_det_2184_goodradar_filter.json",  # 过滤高速bmk
    "hf_label_det_cq_bad_radar_938json_filter": "s3://yrn-share/tmp/hf_label_0717_det_2184_badradar_filter.json",
    # cq track
    # hf prelabel 4441 json
    "hf_prelabel_track_4441json": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_4441.json",
    "hf_prelabel_track_good_2738json": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_4441_goodradar.json",
    "hf_prelabel_track_bad_1703json": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_4441_badradar.json",
    # hf prelabel 4441 json
    "hf_prelabel_track_good_2738json_tracklength": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_2738_goodradar_tracklength.json",
    "hf_prelabel_track_bad_1703json_tracklength": "s3://gxt-share/e2e-data/hf_prelabel_0717_track_1703_badradar_tracklength.json",
    # hf label cq
    "hf_label_track_1410json": "s3://gxt-share/e2e-data/hf_label_1410_jsons.json",
    "hf_label_track_good_radar_97json": "s3://gxt-share/e2e-data/hf_label_1410_jsons_goodradar.json",
    "hf_label_track_bad_radar_608json": "s3://gxt-share/e2e-data/hf_label_1410_jsons_badradar.json",
    # cq filter gs bmk
    "hf_label_1410_jsons_goodradar_filter": "s3://yrn-share/tmp/hf_label_1410_jsons_goodradar_filter.json",
    "hf_label_1410_jsons_badradar_filter": "s3://yrn-share/tmp/hf_label_1410_jsons_badradar_filter.json",
    "hf_label_1410_jsons_badradar_filter_fix": "s3://gxt-share/e2e-data/hf-2024-fix/hf_label_1410_jsons_badradar_filter.json",
    "hf_prelabel_4441_jsons_goodradar_filter": "s3://yrn-share/tmp/hf_prelabel_0717_track_4441_goodradar_tracklength_filter.json",
    "hf_prelabel_4441_jsons_badradar_filter": "s3://yrn-share/tmp/hf_prelabel_0717_track_4441_badradar_tracklength_filter.json",
    "HF15_1002": "s3://gongjiahao-share/e2e-data-list/HF15_all_prelabels_tracking_1002_GS.json",
    # prelabel过滤完后 24年回流label数据
    "hf_prelabel_0717_track_4441_goodradar_filter_rm20241121label": "s3://wuyuhang-datasets/HFdata/det/hf_prelabel_0717_track_4441_goodradar_filter_rmlabelpplbag20241121_1441.json",
    "hf_prelabel_0717_track_4441_badradar_filter_rm20241121label": "s3://wuyuhang-datasets/HFdata/det/hf_prelabel_0717_track_4441_badradar_filter_rmlabelpplbag20241121_333.json",
    "hf_prelabel_0718_det_2103_goodradar_filter_rm20241121label": "s3://wuyuhang-datasets/HFdata/det/hf_prelabel_0718_det_2103_goodradar_filter_rmlabelpplbag20241121_628.json",
    "hf_prelabel_0718_det_2103_badradar_filter_rm20241121label": "s3://wuyuhang-datasets/HFdata/det/hf_prelabel_0718_det_2103_badradar_filter_rmlabelpplbag20241121_62.json",
    # 24年回流的 23年数据
    "hf_newlabel_20241105_jiushujuhuiliu_good_radar": "s3://wuyuhang-datasets/HFdata/det/tf-rhea-data-e2e_e2e_labeled_data_hf_label_1105_track_goodradar_773.json",
    "hf_newlabel_20241105_jiushujuhuiliu_bad_radar": "s3://wuyuhang-datasets/HFdata/det/tf-rhea-data-e2e_e2e_labeled_data_hf_label_1105_track_badradar_443.json",
    "hf_newlabel_20241121-20241105_jiushujuhuiliu_bad_radar": "s3://yrn-share/data/wyh_olddata_badradar_610.json",
    "hf_newlabel_20241121-20241105_jiushujuhuiliu_good_radar": "s3://yrn-share/data/wyh_olddata_goodradar_292.json",
    # 过滤hf2024_chengqu_bmk的 24年回流数据
    "labeled_cq_20241126_car_11_2039": "s3://gxt-share/e2e-data/hf-2024-labeled/labeled_cq_1126_car_11_2039.json",
    "labeled_cq_20241126_car_12_748": "s3://gxt-share/e2e-data/hf-2024-labeled/labeled_cq_1126_car_12_748.json",
    "labeled_cq_20241126_car_15_346": "s3://gxt-share/e2e-data/hf-2024-labeled/labeled_cq_1126_car_15_346.json",
    "labeled_gs_20241126_car_15_204": "s3://gxt-share/e2e-data/hf-2024-labeled/labeled_gs_1126_car_15_204.json",
    "specialTask": "s3://gongjiahao-share/e2e/test-file/HF_specialTask.json",
    "labeled_gs_hf15_1126": "s3://gxt-share-qy/hf-old-data/label_check/labeled_gs_1126_car_15_204_filter_filter.json",
    "labeled_gs_hf15_1206": "s3://gxt-share-qy/hf-old-data/label_check/labeled_gs_1206_car_15_106-noricheck_filterbmk_filter_96_filter.json",
}


# ----- 测试数据全集，定期增量回流 -----
BENCHMARK = []


# ----- 测试数据子集，人工设计规则 -----
BENCHMARK_PARTIAL = {
    "CAR12_BMK_LIST_LABEL_OCC_GS": "s3://hangzhou-tf/tf_labels/动态感知2dbmk/export_labels/100874/car_12/20230920_dp-det_yueying_checked/ppl_bag_20230920_163417_det/v0_230926_152943/",
    # "CAR9_BMK_OCC": '/data/tmpdata/car9_bmk_radar.json',
    "CAR9_BMK_OCC_DAY": "s3://peppa-meg-detect/tmpdata/datasets/car9_bmk_occ_day.json",
    "CAR9_BMK_OCC_DAY_e2e": "s3://end2end/data/paths_list/track/hf9_track_bmk119.json",
    "CAR9_BMK_OCC_DAY_e2e_filter": "/home/<USER>/hf9_track_bmk119.json",
    "bmk_new": "s3://end2end/data/paths_list/track/HF_e2e_bmk_107.json",
    "bmk_new_withOCC": "s3://end2end/data/paths_list/track/HF_e2e_bmk_107_with_occ.json",
    "trt_eval_sample_300f": "s3://wangningzi-data/dataset/e2e_map_dataset/data_lists_v5/trt_eval_300f_sample_hf.json",
    "trt_eval_sample_14scene": "s3://end2end/data/paths_list/track/hf9_track_bmk15_trt_eval.json",
    "quick": "/home/<USER>/bmk_quick.json",
    "mapbmk_align": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_list.json",  # prelabel
    "map_share_prelabels": "s3://gongjiahao-share/e2e/test-file/HF_map_share_prelabel.json",  # 带了map
    "map20w_val": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_BMK_6k.json",
    "map20w_val_down100": "s3://dwj-share/tmp/admap_bmk/base_align_prelabel_BMK_6k_downsample100.json",
    "map20w_val_mf": "s3://mj-share/admap_e2e/data_e2eformat_bmk_mf/6k_bmk_mf_list.json",  # bmk_6k_mf
    "map-test": "/home/<USER>/6k_bmk_mf_list.json",
    "testOnTrain": "s3://wuyuhang-datasets/HF9/HF_e2e_labeled_861_GS_566.json",
    "val_hf_data_urban_occlusion_bmk": "s3://peppa-meg-detect/tmpdata/bmk_cq/dis_and_class_v2_test_demo_v2_test_for_occ_bugfix_depth_comp_rm_self_thres_test_vru_p6_thres3.json",
    # 2024 HF new bmk
    "hf2024_chengqu_bmk": "s3://wuyuhang-datasets/HFdata/det/HF_e2e_2024bmk_300_with_occ.json",
    "z10_sample": "s3://wangningzi-data-qy/z10_transfer_data/241231_json_list.json",
}
