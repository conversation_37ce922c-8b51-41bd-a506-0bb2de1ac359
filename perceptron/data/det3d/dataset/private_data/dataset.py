import copy
import hashlib
from collections import defaultdict

import numpy as np

from data3d.datasets.private import PrivateDataset
from data3d.transforms import transforms3d
from perceptron.utils.det3d_utils import common_utils
from perceptron_eval.tracking.utils.file_utils import dump_pickle
import refile


class PrivateEvalMixin:
    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels - 1]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def add_json_path_to_annos(self, annos):
        for anno_idx, anno in enumerate(annos):
            cur_idx = self.dataset.key_frame_index[anno_idx]
            json_path = self.dataset.get_scene_name(cur_idx)
            frame_id = self.dataset.frame_data_list[cur_idx]["frame_id"]
            anno["json_path"] = json_path
            anno["json_frame_id"] = frame_id
        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        from perceptron_eval.evaluation.det3d.main import Evaluation

        # from perceptron_eval.evaluation.det3d.configs.center_distance import EVAL_CONFIGS

        # 报点常用config
        from perceptron_eval.evaluation.det3d.configs.dynamic_thres_distance_with_merge_cls import EVAL_CONFIGS

        gt_annos = [
            copy.deepcopy(self.dataset._get_annos(self.dataset.key_frame_index[idx]))
            for idx in range(len(self.dataset.key_frame_index))
        ]

        det_annos = self.add_json_path_to_annos(det_annos)
        gt_annos = self.add_json_path_to_annos(gt_annos)
        # 将gt 和 det 结果dump，供perceptron_eval使用
        dump_pickle(det_annos, refile.smart_path_join(kwargs["output_dir"], "det_annos.pkl"))
        dump_pickle(gt_annos, refile.smart_path_join(kwargs["output_dir"], "ground_truth.pkl"))
        self.eval_cfg = EVAL_CONFIGS

        # TODO: 当模型支持预测速度后，需要修改此处逻辑，将vel相关评测根据不同设置加进来，当前先简单的将vel相关评测pop掉。
        if "vel_err" in self.eval_cfg["tp_metrics"]:
            pop_idx = self.eval_cfg["tp_metrics"].index("vel_err")
            self.eval_cfg["tp_metrics"].pop(pop_idx)
        self.eval_cfg["tp_metrics_err_ratio"].pop("vel_err")

        assert isinstance(self.eval_cfg["roi"]["expected_recall"], (list, tuple))
        eval_output_dir = refile.smart_path_join(
            kwargs["output_dir"],
            "eval_results",
        )
        eval_obj = Evaluation(
            eval_cfg=self.eval_cfg,
            det_annos=det_annos,
            gt_annos=gt_annos,
            output_dir=eval_output_dir,
            use_cache=False,
        )
        prefix = hashlib.sha1(str(self.dataset.data_paths).encode("utf-8")).hexdigest()
        kwargs["prefix"] = str(prefix)[:10]

        metrics_summary = eval_obj.eval(**kwargs)
        ap_dict = metrics_summary

        return None, ap_dict


class PrivateDatasetWithEval(PrivateEvalMixin):
    def __init__(
        self,
        data_configs,
        data_paths,
        lidar_key_list=["middle_lidar"],
        img_key_list=[],
        class_names=None,
        training=True,
        only_key_frame=True,
        pc_fields=["x", "y", "z", "i"],
        used_echo_id=[1],
        use_occluded=False,
    ):
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.data_configs = data_configs
        self.class_names = class_names
        self.training = training
        self.dataset = PrivateDataset(
            class_names=self.class_names,
            data_paths=data_paths,
            img_key_list=img_key_list,  # ["camera_10", "camera_11", "camera_15", "camera_2", "camera_5"],
            lidar_key_list=lidar_key_list,
            only_key_frame=only_key_frame,
            pc_fields=pc_fields,
            used_echo_id=used_echo_id,
            use_occluded=use_occluded,
        )
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 4, np.pi / 4]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features

    def __len__(self):
        return len(self.dataset.key_frame_index)

    def _point_process(self, item):
        if "fuser_lidar" in item:
            return item["fuser_lidar"]
        elif "middle_lidar" in item:
            return item["middle_lidar"]
        else:
            raise ValueError("No valid lidar('fuser_lidar', 'middle_lidar') can be used!")

    def _run_aug_and_process(self, data_dict):
        if self.training:
            assert "gt_boxes" in data_dict, "gt_boxes should be provided for training"
            if len(data_dict["gt_boxes"]) == 0:
                new_index = np.random.randint(self.__len__())
                return self.__getitem__(new_index)

            gt_boxes_mask = np.array([n in self.class_names for n in data_dict["gt_names"]], dtype=np.bool_)
            data_dict = self.data_augmentor.forward(data_dict={**data_dict, "gt_boxes_mask": gt_boxes_mask})
            if len(data_dict["gt_boxes"]) == 0:
                new_index = np.random.randint(self.__len__())
                return self.__getitem__(new_index)
        elif hasattr(self, "test_augmentor"):
            data_dict = self.test_augmentor.forward(data_dict=data_dict)

        if len(data_dict.get("gt_boxes", [])) > 0:
            selected = common_utils.keep_arrays_by_name(data_dict["gt_names"], self.class_names)
            data_dict["gt_boxes"] = data_dict["gt_boxes"][selected]
            data_dict["gt_names"] = data_dict["gt_names"][selected]
            gt_classes = np.array([self.class_names.index(n) + 1 for n in data_dict["gt_names"]], dtype=np.int32)
            gt_boxes = np.concatenate((data_dict["gt_boxes"], gt_classes.reshape(-1, 1).astype(np.float32)), axis=1)
            data_dict["gt_boxes"] = gt_boxes
        else:
            gt_boxes = np.zeros((0, 8), dtype=np.float32)
            data_dict["gt_boxes"] = gt_boxes

        data_dict = self.processor.forward(data_dict=data_dict)
        data_dict.pop("gt_names", None)
        data_dict.pop("num_points_in_gt", None)
        return data_dict

    def __getitem__(self, idx: int):
        idx = self.dataset.key_frame_index[idx]
        item = self.dataset[idx]
        data_dict = {
            "points": self._point_process(item)[:, : self.data_configs.use_num_point_features],
            "frame_id": item["frame_id"],
            "use_lead_xyz": self.use_lead_xyz,
        }

        if "labels" in item:
            data_dict.update(
                {
                    "gt_names": copy.deepcopy(item["labels"]),
                    "gt_boxes": copy.deepcopy(item["gt_boxes"]),
                    "num_points_in_gt": copy.deepcopy(item["num_lidar_points"]),
                }
            )

        data_dict = self._run_aug_and_process(data_dict)
        return data_dict

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points"]:
                    ret[key] = val
                elif key in ["gt_boxes"]:
                    max_gt = max([len(x) for x in val])
                    batch_gt_boxes3d = np.zeros((batch_size, max_gt, val[0].shape[-1]), dtype=np.float32)
                    for k in range(batch_size):
                        batch_gt_boxes3d[k, : val[k].__len__(), :] = val[k]
                    ret[key] = batch_gt_boxes3d
                elif key == "gt_boxes_mask":
                    continue
                else:
                    ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class PrivateDatasetMultiFramesWithEval(PrivateDatasetWithEval):
    def __init__(
        self,
        data_configs,
        data_paths,
        lidar_key_list=["middle_lidar"],
        img_key_list=[],
        use_adjacent_frames=[-1, 0],
        class_names=None,
        training=True,
        only_key_frame=False,
        pc_fields=["x", "y", "z", "i"],
        used_echo_id=[1],
        use_occluded=False,
    ):
        super(PrivateDatasetMultiFramesWithEval, self).__init__(
            data_configs=data_configs,
            data_paths=data_paths,
            lidar_key_list=lidar_key_list,
            class_names=class_names,
            training=training,
            only_key_frame=only_key_frame,
            img_key_list=img_key_list,
            pc_fields=pc_fields,
            used_echo_id=used_echo_id,
            use_occluded=use_occluded,
        )
        self.use_adjacent_frames = use_adjacent_frames

    def _fuse_multi_frames(self, points, current_idx):
        current_points = copy.deepcopy(points)
        scene_name = self.dataset.get_scene_name(current_idx)
        adjacent_tags = np.zeros((current_points.shape[0], 1))
        for adjacent_i in self.use_adjacent_frames:
            if adjacent_i != 0:
                adjacent_idx = current_idx + adjacent_i
                if adjacent_idx >= 0 and adjacent_idx < len(self.dataset.frame_data_list):
                    if scene_name == self.dataset.get_scene_name(adjacent_idx):
                        adj_point = self._point_process(
                            self.dataset._get_point_cloud(adjacent_idx, self.dataset.lidar_key_list)
                        )
                        adj_cur_points = self.dataset.project_point_to_reference_frame(
                            adj_point[:, :3], adjacent_idx, reference_idx=current_idx
                        )
                        adj_cur_points = np.concatenate([adj_cur_points, adj_point[:, 3:]], axis=1)
                        adj_tags = np.zeros((adj_cur_points.shape[0], 1)) + adjacent_i
                        current_points = np.concatenate([current_points, adj_cur_points], axis=0)
                        adjacent_tags = np.concatenate([adjacent_tags, adj_tags], axis=0)
        assert current_points.shape[0] == adjacent_tags.shape[0]
        current_points = np.concatenate([current_points, adjacent_tags], axis=1)
        return current_points

    def __getitem__(self, idx: int):
        current_key_frame_idx = self.dataset.key_frame_index[idx]
        key_item = self.dataset[current_key_frame_idx]  # key frame points and labels
        multi_fuse_points = self._fuse_multi_frames(self._point_process(key_item), current_key_frame_idx)
        data_dict = {
            "points": multi_fuse_points[:, : self.data_configs.use_num_point_features],
            "frame_id": key_item["frame_id"],
            "use_lead_xyz": self.use_lead_xyz,
        }

        if "labels" in key_item:
            data_dict.update(
                {
                    "gt_names": copy.deepcopy(key_item["labels"]),
                    "gt_boxes": copy.deepcopy(key_item["gt_boxes"]),
                    "num_points_in_gt": copy.deepcopy(key_item["num_lidar_points"]),
                }
            )

        data_dict = self._run_aug_and_process(data_dict)
        return data_dict
