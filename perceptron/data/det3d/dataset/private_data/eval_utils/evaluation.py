# nuScenes dev-kit.
# Code written by <PERSON><PERSON><PERSON> & <PERSON>, 2018.

from collections import defaultdict
import os
import time
from typing import Any, Dict, <PERSON><PERSON>

import numpy as np
import refile


from perceptron.data.det3d.dataset.private_data.eval_utils.common.data_classes import EvalBoxes
from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import (
    PrintStr,
    JsonEncoder,
    dump_json,
    dump_pkl,
)
from perceptron.data.det3d.dataset.private_data.eval_utils.common.loaders import (
    add_center_dist,
    filter_eval_boxes,
    format_private_gt_annotations,
    format_private_pred_annotations,
    get_valid_boxes,
    load_gt,
    load_pkl,
    load_prediction,
    mapping_boxes_categories,
    set_cipo,
    get_dynamic_roi_info,
    set_dynamic_roi_flag,
    filter_pred_ego_car,
)
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.algo import (
    accumulate,
    calc_ap,
    calc_tp,
    calc_tp_metric_ratio,
)
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.data_classes import (
    DetectionBox,
    DetectionConfig,
    DetectionMetricDataList,
    DetectionMetrics,
    SampleInfoDict,
)
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.utils import count_samples_by_classname
from perceptron.utils.log_utils import setup_logger


class DetectionEval:
    """
    This is the official nuScenes detection evaluation code.
    Results are written to the provided output_dir.

    nuScenes uses the following detection metrics:
    - Mean Average Precision (mAP): Uses center-distance as matching criterion; averaged over distance thresholds.
    - True Positive (TP) metrics: Average of translation, velocity, scale, orientation and attribute errors.
    - nuScenes Detection Score (NDS): The weighted sum of the above.

    Here is an overview of the functions in this method:
    - init: Loads GT annotations and predictions stored in JSON format and filters the boxes.
    - run: Performs evaluation and dumps the metric data to disk.
    - render: Renders various plots and dumps to disk.

    We assume that:
    - Every sample_token is given in the results, although there may be not predictions for that sample.

    Please see https://www.nuscenes.org/object-detection for more details.
    """

    def __init__(
        self,
        config: DetectionConfig,
        pred_annos: list,
        gt_annos: list,
        output_dir: str = None,
        verbose: bool = True,
        use_cache: bool = True,
    ):
        """
        Initialize a DetectionEval object.
        :param config: A DetectionConfig object.
        :param output_dir: Folder to save plots and results to.
        :param verbose: Whether to print to stdout.
        """
        self.output_dir = output_dir
        self.verbose = verbose
        self.cfg = config
        if self.verbose:
            self.logger = setup_logger(output_dir, filename="eval_log.txt")
        else:
            self.logger = None

        # Make dirs.
        if not refile.smart_isdir(self.output_dir):
            refile.smart_makedirs(self.output_dir)

        # format annos
        if verbose:
            self.logger.info("Formating gt annos and pred annos")
        if not use_cache:
            token2gt_boxes = format_private_gt_annotations(gt_annos)
            token2pred_boxes = format_private_pred_annotations(pred_annos)

        # Load data.
        if verbose:
            self.logger.info("Initializing nuScenes detection evaluation")

        if not use_cache:
            self.pred_boxes = load_prediction(
                token2pred_boxes,
                self.cfg.max_boxes_per_sample,
                DetectionBox,
                verbose=verbose,
                logger=self.logger,
            )
            self.gt_boxes = load_gt(token2gt_boxes, DetectionBox, verbose=verbose, logger=self.logger)

        # Add center distances.
        if not use_cache:
            self.pred_boxes = add_center_dist(self.pred_boxes)
            self.gt_boxes = add_center_dist(self.gt_boxes)

            assert set(self.pred_boxes.sample_tokens) == set(
                self.gt_boxes.sample_tokens
            ), "Samples in split doesn't match samples in predictions."
            if verbose:
                self.logger.info("gt boxes:", self.gt_boxes)
                self.logger.info("pred boxes:", self.pred_boxes)

        if not use_cache:
            self.gt_boxes, self.ignored_areas = get_valid_boxes(
                self.gt_boxes,
                valid_keys=self.cfg.class_names,
                ignored_keys=self.cfg.ignored_names,
                verbose=verbose,
                logger=self.logger,
            )
            self.pred_boxes, _ = get_valid_boxes(
                self.pred_boxes,
                valid_keys=self.cfg.class_names,
                ignored_keys=self.cfg.ignored_names,
                verbose=verbose,
                logger=self.logger,
            )

            # Filter boxes (distance, points per b  ox, etc.).
            if verbose:
                self.logger.info("Filtering predictions")

            self.pred_boxes = filter_eval_boxes(
                self.pred_boxes,
                self.cfg.roi_mode,
                self.cfg.class_range,
                is_gt=False,
                ignored_areas=self.ignored_areas,
                verbose=verbose,
                logger=self.logger,
            )
            if verbose:
                self.logger.info("Filtering predictions ego car boxes.")
            self.pred_boxes = filter_pred_ego_car(self.pred_boxes, verbose=verbose, logger=self.logger)

            if verbose:
                self.logger.info("Filtering ground truth annotations")
            self.gt_boxes = filter_eval_boxes(
                self.gt_boxes,
                self.cfg.roi_mode,
                self.cfg.class_range,
                is_gt=True,
                ignored_areas=self.ignored_areas,
                verbose=verbose,
                logger=self.logger,
            )

            # set CIPO flag
            if verbose:
                self.logger.info("Filtering cipo boxes")
            self.gt_boxes = set_cipo(self.gt_boxes, verbose=verbose, logger=self.logger)

            # get dynamic roi info
            if verbose:
                self.logger.info("Get dynamic roi information")
            self.sample_info_dict = get_dynamic_roi_info(
                self.gt_boxes, gt_annos, self.cfg, verbose=verbose, logger=self.logger
            )

            # set dynamic roi flag
            if verbose:
                self.logger.info("Set ground truth annotations in_dynamic_roi flag.")
            self.gt_boxes = set_dynamic_roi_flag(
                self.sample_info_dict, self.gt_boxes, verbose=verbose, logger=self.logger
            )
            if verbose:
                self.logger.info("Set prediction boxes in_dynamic_roi flag.")
            self.pred_boxes = set_dynamic_roi_flag(
                self.sample_info_dict, self.pred_boxes, verbose=verbose, logger=self.logger
            )

            # dump gt and pred boxes
            if verbose:
                self.logger.info("Dump gt and pred boxes at {}".format(self.output_dir))
            dump_pkl(self.gt_boxes.serialize(), refile.smart_path_join(self.output_dir, "filtered_gt_boxes.pkl"))
            dump_pkl(self.pred_boxes.serialize(), refile.smart_path_join(self.output_dir, "filtered_pred_boxes.pkl"))
            dump_pkl(self.sample_info_dict.serialize(), refile.smart_path_join(self.output_dir, "dynamic_rois.pkl"))
        else:
            self.gt_boxes = EvalBoxes.deserialize(
                load_pkl(refile.smart_path_join(self.output_dir, "filtered_gt_boxes.pkl")), DetectionBox
            )
            self.pred_boxes = EvalBoxes.deserialize(
                load_pkl(refile.smart_path_join(self.output_dir, "filtered_pred_boxes.pkl")), DetectionBox
            )
            self.sample_info_dict = SampleInfoDict.deserialize(
                load_pkl(refile.smart_path_join(self.output_dir, "dynamic_rois.pkl"))
            )

        self.sample_tokens = self.gt_boxes.sample_tokens

    def evaluate(self, eval_class_names) -> Tuple[DetectionMetrics, DetectionMetricDataList, Dict]:
        """
        Performs the actual evaluation.
        :return: A tuple of high-level and the raw metric data.
        """
        start_time = time.time()

        # -----------------------------------
        # Step 1: Accumulate metric data for all classes and distance thresholds.
        # -----------------------------------
        if self.verbose:
            self.logger.info("Accumulating metric data...")

        metric_data_list = DetectionMetricDataList()
        dyn_roi_metric_data_list = DetectionMetricDataList()
        metric_details_dict = {}
        metric_details_dict_dyn_roi = {}
        fp_boxes_records = {}
        matched_gt_records = defaultdict(list)
        for class_name in eval_class_names:
            for dist_th in self.cfg.dist_ths:
                md, md_dyn_roi, md_details, md_details_dyn_roi, fp_boxes, matched_gt = accumulate(
                    self.gt_boxes,
                    self.pred_boxes,
                    class_name,
                    self.cfg.dist_fcn_callable,
                    dist_th,
                    expected_recall=max(self.cfg.roi_expected_recall),
                )
                metric_data_list.set(class_name, dist_th, md)
                dyn_roi_metric_data_list.set(class_name, dist_th, md_dyn_roi)
                metric_details_dict[(class_name, dist_th)] = md_details
                metric_details_dict_dyn_roi[(class_name, dist_th)] = md_details_dyn_roi
                if fp_boxes is not None:
                    fp_boxes_records[(class_name, dist_th)] = fp_boxes
                if matched_gt is not None:
                    for k in matched_gt:
                        matched_gt_records[k[0]].append(k[1])

        # -----------------------------------
        # Step 2: Calculate metrics from the data.
        # -----------------------------------
        if self.verbose:
            self.logger.info("Calculating metrics...")
        metrics = DetectionMetrics(self.cfg)
        metrics_dyn_roi = DetectionMetrics(self.cfg)
        # self.cfg.class_names: ['car', 'truck', 'bus', 'trailer', 'construction_vehicle', 'pedestrian', 'motorcycle', 'bicycle', 'traffic_cone', 'barrier']
        # self.cfg.dist_ths: [0.5, 1.0, 2.0, 4.0]

        for class_name in eval_class_names:
            # Compute APs.
            for dist_th in self.cfg.dist_ths:
                # calc fix roi
                metric_data = metric_data_list[(class_name, dist_th)]
                ap = calc_ap(metric_data)
                metrics.add_label_ap(class_name, dist_th, ap)
                # calc dynamic roi
                metric_data_dyn = dyn_roi_metric_data_list[(class_name, dist_th)]
                dyn_ap = calc_ap(metric_data_dyn)
                metrics_dyn_roi.add_label_ap(class_name, dist_th, dyn_ap)

            # Compute TP metrics.
            for metric_name in self.cfg.tp_metrics:
                metric_data = metric_data_list[(class_name, self.cfg.dist_th_tp)]
                metric_data_dyn = dyn_roi_metric_data_list[(class_name, self.cfg.dist_th_tp)]
                if class_name in ["traffic_cone"] and metric_name in ["attr_err", "vel_err", "orient_err"]:
                    tp = np.nan
                    tp_dyn = np.nan
                elif class_name in ["barrier"] and metric_name in ["attr_err", "vel_err"]:
                    tp = np.nan
                    tp_dyn = np.nan
                else:
                    tp = calc_tp(metric_data, metric_name)
                    tp_dyn = calc_tp(metric_data_dyn, metric_name)
                metrics.add_label_tp(class_name, metric_name, tp)
                metrics_dyn_roi.add_label_tp(class_name, metric_name, tp_dyn)

        metrics_extra = self.get_metrics_extra(metric_details_dict, eval_class_names)
        metrics_extra_dyn_roi = self.get_metrics_extra(metric_details_dict_dyn_roi, eval_class_names)

        # Compute evaluation time.
        metrics.add_runtime(time.time() - start_time)
        metrics_dyn_roi.add_runtime(time.time() - start_time)

        # Calculate fn boxes
        fn_boxes_records = []
        for sample_token in self.gt_boxes.sample_tokens:
            for box_idx, box in enumerate(self.gt_boxes[sample_token]):
                if box_idx not in matched_gt_records[sample_token] and not box.is_ignore:
                    fn_boxes_records.append(box.serialize())

        return (
            metrics,
            metric_data_list,
            metrics_dyn_roi,
            dyn_roi_metric_data_list,
            metrics_extra,
            metrics_extra_dyn_roi,
            fp_boxes_records,
            fn_boxes_records,
        )

    def get_metrics_extra(self, metric_details_dict, eval_class_names):
        # detail metrics for spec recall
        # 整个计算过程没有涉及插值，均为真实值统计。
        total_frames = len(self.gt_boxes.sample_tokens)
        metrics_extra = {}
        for expected_recall in self.cfg.roi_expected_recall:
            metrics_extra_sub = {dist_th: {} for dist_th in self.cfg.dist_ths}
            for class_name in eval_class_names:
                for dist_th in self.cfg.dist_ths:
                    metric_details = metric_details_dict[(class_name, dist_th)]

                    # class without ground truth => continue
                    if metric_details is None:
                        continue

                    above_recall_mask = metric_details["rec_roi"] >= expected_recall
                    if above_recall_mask.any():
                        conf_idx = np.where(above_recall_mask)[0][0]
                    else:
                        conf_idx = -1

                    # select confidence threshold
                    # tp metrics
                    if len(metric_details["conf_roi"]) == 0:
                        continue

                    conf_th = metric_details["conf_roi"][conf_idx]

                    rec_cipo = None
                    # this is no cipo belongs to this class
                    # 计算过程中没有插值计算，依据 conf_th 卡出 rec_cipo
                    if len(metric_details["conf_cipo"]) == 0:
                        rec_cipo = np.nan
                        cipo_conf = np.nan
                    elif len(np.where(metric_details["conf_cipo"] >= conf_th)[0]) == 0:
                        rec_cipo = 0
                        cipo_conf = np.max(metric_details["conf_cipo"])  # 当rec_cipo==0时，cipo_conf取当前conf_list中的最大值。
                    else:
                        conf_idx_cipo = np.where(metric_details["conf_cipo"] >= conf_th)[0][-1]
                        rec_cipo = metric_details["rec_cipo"][conf_idx_cipo]
                        cipo_conf = metric_details["conf_cipo"][conf_idx_cipo]

                    metrics_extra_sub[dist_th][class_name] = dict(
                        conf_th=conf_th,
                        # roi metric
                        rec_roi=metric_details["rec_roi"][conf_idx],
                        prec_roi=metric_details["prec_roi"][conf_idx],
                        fp_roi=metric_details["fp_roi"][conf_idx],
                        fppie=metric_details["fp_roi"][conf_idx] / max(metric_details["existed_sample_nums"], 1),
                        fppi=metric_details["fp_roi"][conf_idx] / total_frames,
                        npos_roi=metric_details["npos"],
                        existed_sample_nums=metric_details["existed_sample_nums"],
                        existed_sample_tokens=metric_details["existed_sample_tokens"],
                        # cipo metric
                        rec_cipo=rec_cipo,
                        cipo_conf=cipo_conf,
                        npos_cipo=metric_details["npos_cipo"],  # cipo total number
                        matched_cipo=metric_details["matched_cipo"],  # cipo matched bumber
                    )

                    # tp metric
                    tp_valid_idx = np.where(metric_details["conf_tp"] >= conf_th)
                    if len(tp_valid_idx[0]) == 0:
                        conf_idx_tp = -1
                    else:
                        conf_idx_tp = tp_valid_idx[0][-1]

                    for tp_metric in self.cfg.tp_metrics:
                        if conf_idx_tp != -1:
                            metrics_extra_sub[dist_th][class_name].update(
                                {tp_metric: metric_details[tp_metric][conf_idx_tp]}
                            )
                            if tp_metric in self.cfg.tp_metrics_err_ratio:
                                metrics_extra_sub[dist_th][class_name].update(
                                    {
                                        f"{tp_metric}_error_ratio": calc_tp_metric_ratio(
                                            metric_details["match_data"][tp_metric][: conf_idx_tp + 1],
                                            self.cfg.tp_metrics_err_ratio[tp_metric],
                                        ),
                                        f"{tp_metric}_total_num": len(
                                            metric_details["match_data"][tp_metric][: conf_idx_tp + 1]
                                        ),
                                    }
                                )
                        else:
                            metrics_extra_sub[dist_th][class_name].update({tp_metric: 1.0})
                            if tp_metric in self.cfg.tp_metrics_err_ratio:
                                metrics_extra_sub[dist_th][class_name].update(
                                    {
                                        f"{tp_metric}_error_ratio": calc_tp_metric_ratio(
                                            [], self.cfg.tp_metrics_err_ratio[tp_metric]
                                        ),
                                        f"{tp_metric}_total_num": -1,
                                    }
                                )

            metrics_extra[expected_recall] = metrics_extra_sub
        return metrics_extra

    def main(self, plot_examples: int = 0, render_curves: bool = True) -> Dict[str, Any]:
        """
        Main function that loads the evaluation code, visualizes samples, runs the evaluation and renders stat plots.
        :param plot_examples: How many example visualizations to write to disk.
        :param render_curves: Whether to render PR and TP curves to disk.
        :return: A dict that stores the high-level metrics and meta data.
        """
        if plot_examples > 0:
            raise NotImplementedError("Ploting examples not supported now.")

        # # Run evaluation.
        (
            metrics,
            metric_data_list,
            metrics_dyn_roi,
            metric_data_list_dyn_roi,
            metrics_extra,
            metrics_extra_dyn_roi,
            fp_boxes_records,
            fn_boxes_records,
        ) = self.evaluate(self.cfg.class_names)

        if self.verbose:
            self.logger.info("Saving metrics to: %s" % self.output_dir)

        metrics_summary = metrics.serialize()
        # metrics_summary["meta"] = self.meta.copy()
        dump_json(metrics_summary, refile.smart_path_join(self.output_dir, "metrics_summary.json"))
        dump_json(metric_data_list.serialize(), refile.smart_path_join(self.output_dir, "metrics_details.json"))
        dump_json(metrics_extra, refile.smart_path_join(self.output_dir, "metrics_extra.json"), encoder=JsonEncoder)

        # save dynamic roi results
        metrics_summary_dyn_roi = metrics_dyn_roi.serialize()
        dump_json(metrics_summary_dyn_roi, refile.smart_path_join(self.output_dir, "metrics_summary_dynamic_roi.json"))
        dump_json(
            metric_data_list_dyn_roi.serialize(),
            refile.smart_path_join(self.output_dir, "metrics_details_dynamic_roi.json"),
        )
        dump_json(
            metrics_extra_dyn_roi,
            refile.smart_path_join(self.output_dir, "metrics_extra_dynamic_roi.json"),
            encoder=JsonEncoder,
        )

        dump_pkl(fp_boxes_records, refile.smart_path_join(self.output_dir, "fp_boxes_records.pkl"))
        dump_pkl(fn_boxes_records, refile.smart_path_join(self.output_dir, "fn_boxes_records.pkl"))

        self.print_output(metrics_summary, metrics_extra, render_curves=render_curves)
        self.logger.info("In dynamic roi results:")
        self.print_output(
            metrics_summary_dyn_roi, metrics_extra_dyn_roi, render_curves=render_curves, use_dynamic_roi=True
        )

        # Run merge category evaluation.
        if self.cfg.class_mapping_rules is not None:
            self.logger.info(os.linesep)
            self.logger.info("merging class result:")
            self.logger.info(f"merging rules ==> {self.cfg.class_mapping_rules}")
            mapping_class = sorted(set(list(self.cfg.class_mapping_rules.values())))
            self.gt_boxes = mapping_boxes_categories(self.gt_boxes, self.cfg.class_mapping_rules)
            self.pred_boxes = mapping_boxes_categories(self.pred_boxes, self.cfg.class_mapping_rules)
            (
                merged_class_metrics,
                merged_class_metric_data_list,
                merged_class_metrics_dyn_roi,
                merged_class_metric_data_list_dyn_roi,
                merged_metrics_extra,
                merged_metrics_extra_dyn_roi,
                _,
                _,
            ) = self.evaluate(mapping_class)
            if self.verbose:
                self.logger.info("Saving merged class metrics to: %s" % self.output_dir)
            merged_metrics_summary = merged_class_metrics.serialize()
            dump_json(merged_metrics_summary, refile.smart_path_join(self.output_dir, "merged_metrics_summary.json"))
            dump_json(
                merged_class_metric_data_list.serialize(),
                refile.smart_path_join(self.output_dir, "merged_metrics_details.json"),
            )
            dump_json(
                merged_metrics_extra,
                refile.smart_path_join(self.output_dir, "merged_metrics_extra.json"),
                encoder=JsonEncoder,
            )

            merged_metrics_summary_dyn_roi = merged_class_metrics_dyn_roi.serialize()
            dump_json(
                merged_metrics_summary_dyn_roi,
                refile.smart_path_join(self.output_dir, "merged_metrics_summary_dynamic_roi.json"),
            )
            dump_json(
                merged_class_metric_data_list_dyn_roi.serialize(),
                refile.smart_path_join(self.output_dir, "merged_metrics_details_dynamic_roi.json"),
            )
            dump_json(
                merged_metrics_extra_dyn_roi,
                refile.smart_path_join(self.output_dir, "merged_metrics_extra_dynamic_roi.json"),
                encoder=JsonEncoder,
            )

            self.print_output(
                merged_metrics_summary,
                merged_metrics_extra,
                render_curves=render_curves,
                class_key_name="mapping_detection_name",
            )
            self.logger.info("After merge class, in dynamic roi results:")
            self.print_output(
                merged_metrics_summary_dyn_roi,
                merged_metrics_extra_dyn_roi,
                render_curves=render_curves,
                class_key_name="mapping_detection_name",
                use_dynamic_roi=True,
            )
        else:
            merged_metrics_summary = None
            merged_metrics_extra = None

        metrics_summary["merged_class_metrics_summary"] = merged_metrics_summary
        metrics_extra["merged_class_metrics_extra"] = merged_metrics_extra
        return metrics_summary, metrics_extra

    def print_output(
        self,
        metrics_summary: Dict,
        metrics_extra: Dict,
        render_curves: bool = True,
        class_key_name: str = "detection_name",
        use_dynamic_roi: bool = False,
    ):
        # Render PR and TP curves.
        if render_curves:
            raise NotImplementedError("Rendering curves not supported now.")

        # Print high-level metrics.
        print_str = PrintStr()
        print_str += "mAP: {:.4f}".format(metrics_summary["mean_ap"])
        err_name_mapping = {
            "trans_err": "mATE",
            "scale_err": "mASE",
            "orient_err": "mAOE",
            "vel_err": "mAVE",
            "attr_err": "mAAE",
        }

        for tp_name, tp_val in metrics_summary["tp_errors"].items():
            print_str += "{}: {:.4f}".format(err_name_mapping[tp_name], tp_val)
        print_str += "NDS: {:.4f}".format(metrics_summary["nd_score"])
        print_str += "Eval time: {:.1f}s".format(metrics_summary["eval_time"])
        print_str += os.linesep

        # Print per-class metrics.
        tp_metric_title_mapping = {
            "trans_err": "ATE",
            "scale_err": "ASE",
            "orient_err": "AOE",
            "vel_err": "AVE",
            "attr_err": "AAE",
        }

        print_str += "Per-class results:"
        class_aps = metrics_summary["mean_dist_aps"]
        class_tps = metrics_summary["label_tp_errors"]
        tp_metric_tiles = tuple([tp_metric_title_mapping[tp_metric] for tp_metric in self.cfg.tp_metrics])
        print_str += ("|%-25s\t|%s\t|" + "%s\t|" * len(tp_metric_tiles)) % (("Object Class", "AP") + tp_metric_tiles)

        # count samples for every class
        if use_dynamic_roi:

            def filter_func(box):
                return not box.is_ignore and box.is_in_dynamic_roi  # noqa

        else:

            def filter_func(box):
                return not box.is_ignore  # noqa

        classname2count = count_samples_by_classname(self.gt_boxes, key_name=class_key_name, filter_func=filter_func)

        for class_name in class_aps.keys():
            metrics = []
            for tp_metric in self.cfg.tp_metrics:
                if tp_metric in class_tps[class_name]:
                    metrics.append(class_tps[class_name][tp_metric])
            metrics = tuple(metrics)
            print_str += ("|%-25s\t|%.3f\t|" + "%.3f\t|" * len(tp_metric_tiles)) % (
                (
                    class_name + f"({classname2count.get(class_name, 0)})",
                    # + f"({0 if class_name not in metrics_extra[list(metrics_extra.keys())[0]] else metrics_extra[list(metrics_extra.keys())[0]][class_name]['npos_roi']})",
                    class_aps[class_name],
                )
                + metrics
            )
        print_str += os.linesep
        # Print per-class metrics for specific confidence threshold.
        for expected_recall in metrics_extra:
            print_str += os.linesep
            print_str += "Per-class metrics for specific confidence threshold:"
            print_str += f"Expected recall = {expected_recall}"
            metrics_extra_sub = metrics_extra[expected_recall]
            for dist_th in metrics_extra_sub.keys():
                print_str += f"dist_th = {dist_th}"
                print_str += "RoI metrics:"
                print_str += "** Notice: fppie = fp numbers / objects existed frame numbers **"
                print_str += "** Notice: fppi = fp numbers / total frame numbers in benchmark **"
                print_str += (
                    "|%-15s|" + "%-10s|" * 4 + "%-15s|" + "%-15s|" * 2 + "%-10s|" * len(self.cfg.tp_metrics)
                ) % (
                    ("Oject Class", "conf_th", "rec_roi", "prec_roi", "fp_roi", "exist frames", "fppie", "fppi")
                    + tuple(self.cfg.tp_metrics)
                )

                for class_name, metric_item in metrics_extra_sub[dist_th].items():
                    print_str += (
                        "|{:<15}|" + "{:<10}|" * 4 + "{:<15}|" + "{:<15.5e}|" * 2 + "{:<10}|" * len(self.cfg.tp_metrics)
                    ).format(
                        *(
                            class_name,
                            round(metric_item["conf_th"], 3),
                            round(metric_item["rec_roi"], 3),
                            round(metric_item["prec_roi"], 3),
                            round(metric_item["fp_roi"], 3),
                            metric_item["existed_sample_nums"],
                            metric_item["fppie"],
                            metric_item["fppi"],
                        )
                        + tuple([round(metric_item[tp_metric], 3) for tp_metric in self.cfg.tp_metrics])
                    )
                print_str += "CIPO metrics:"
                print_str += ("|%-20s|" + "%-10s|" * 4 + "%-15s|") % (
                    ("Oject Class", "conf_th", "cipo_conf", "rec_cipo", "total_cipo", "matched_cipo")
                )
                for class_name, metric_item in metrics_extra_sub[dist_th].items():
                    print_str += ("|{:<20}|" + "{:<10}|" * 4 + "{:<15}|").format(
                        *(
                            class_name,
                            round(metric_item["conf_th"], 3),
                            round(metric_item["cipo_conf"], 3),
                            round(metric_item["rec_cipo"], 3),
                            metric_item["npos_cipo"],
                            metric_item["matched_cipo"],
                        )
                    )
                # tp metrics error ratios
                print_str += "tp metrics ratios:"
                all_class_names = list(metrics_extra_sub[dist_th].keys())
                for tp_metric_name in self.cfg.tp_metrics_err_ratio.keys():
                    range_list = list(
                        metrics_extra_sub[dist_th][all_class_names[0]][f"{tp_metric_name}_error_ratio"].keys()
                    )
                    print_str += tp_metric_name
                    print_str += ("|%-15s|" + "%-15s|" + "%-15s|" * len(range_list)) % (
                        tuple(["Oject Class", "total number"] + sorted(range_list))
                    )
                    for class_name, metric_item in metrics_extra_sub[dist_th].items():
                        show_data = [class_name, metric_item[f"{tp_metric_name}_total_num"]]
                        for rn in range_list:
                            show_data.append(metric_item[f"{tp_metric_name}_error_ratio"][rn])
                        print_str += ("|{:<15}|" + "{:<15}|" + "{:<15.5}|" * len(range_list)).format(*show_data)
        self.logger.info(str(print_str))
        return metrics_summary
