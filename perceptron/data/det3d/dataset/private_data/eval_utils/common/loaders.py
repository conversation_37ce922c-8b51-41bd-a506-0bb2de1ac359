import math
import pickle
from typing import Dict, <PERSON><PERSON>

import numpy as np
import refile
import tqdm
from pyquaternion import Quaternion

from perceptron.data.det3d.dataset.private_data.eval_utils.common.data_classes import Box, EvalBoxes
from perceptron.data.det3d.dataset.private_data.eval_utils.common.geometry_utils import points_in_box
from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import PrintStr
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.data_classes import (
    DetectionBox,
    SampleInfo,
    SampleInfoDict,
)


def load_pkl(file_path):
    with refile.smart_open(file_path, "rb") as f:
        return pickle.load(f)


def format_private_gt_annotations(annos):
    format_annos = {}
    for sample_id, anno in enumerate(annos):
        sample_token = "sample_" + str(sample_id)
        boxes = []
        for box_id, box in enumerate(anno["gt_boxes"]):
            box_dict = {
                "sample_token": sample_token,
                "translation": box[:3],
                "size": box[3:6],
                "rotation": Quaternion(math.cos(box[6] / 2), 0, 0, math.sin(box[6] / 2)),
                "velocity": (0, 0),
                "num_pts": anno["num_lidar_points"][box_id],
                "detection_name": anno["labels"][box_id],
                "detection_score": -1.0,
                "attribute_name": "",  # NOTE: set default
                "json_path": anno.get("json_path", ""),
                "json_frame_id": anno.get("json_frame_id", -1),
            }
            boxes.append(box_dict)
        format_annos[sample_token] = boxes
    return format_annos


def format_private_pred_annotations(annos):
    format_annos = {}
    for sample_id, anno in enumerate(annos):
        sample_token = "sample_" + str(sample_id)
        boxes = []
        for box_id, box in enumerate(anno["boxes_3d"]):
            box_dict = {
                "sample_token": sample_token,
                "translation": box[:3],
                "size": box[3:6],
                "rotation": Quaternion(math.cos(box[6] / 2), 0, 0, math.sin(box[6] / 2)),
                "velocity": box[7:9] if box.shape[0] == 9 else np.array([0, 0]),
                "num_pts": -1,
                "detection_name": anno["name"][box_id],
                "detection_score": anno["score"][box_id],
                "attribute_name": "",
                "json_path": anno.get("json_path", ""),
                "json_frame_id": anno.get("json_frame_id", -1),
            }
            boxes.append(box_dict)
        format_annos[sample_token] = boxes
    return format_annos


def format_nuscenes_gt_annotations(annos):
    # 按照nuscenes格式
    format_annos = {}
    for sample_id, anno in enumerate(annos):
        sample_token = "sample_" + str(sample_id)
        boxes = []
        for box_id, box in enumerate(anno["gt_boxes"]):
            box_dict = {
                "sample_token": sample_token,
                "translation": box[:3],
                "size": box[3:6],
                "rotation": Quaternion(math.cos(box[6] / 2), 0, 0, math.sin(box[6] / 2)),
                "velocity": anno["gt_boxes_velocity"][box_id][:2],
                "num_pts": anno["num_lidar_pts"][box_id] + anno["num_radar_pts"][box_id],
                "detection_name": anno["gt_names"][box_id],
                "detection_score": -1.0,
                "attribute_name": "vehicle.moving",  # NOTE: set default
            }
            boxes.append(box_dict)
        format_annos[sample_token] = boxes
    return format_annos


def format_nuscenes_pred_annotations(annos):
    # 按照nuscenes格式
    format_annos = {}
    for sample_id, anno in enumerate(annos):
        sample_token = "sample_" + str(sample_id)
        boxes = []
        for box_id, box in enumerate(anno["boxes_3d"]):
            box_dict = {
                "sample_token": sample_token,
                "translation": box[:3],
                "size": box[3:6],
                "rotation": Quaternion(math.cos(box[6] / 2), 0, 0, math.sin(box[6] / 2)),
                "velocity": box[7:9] if box.shape[0] == 9 else np.array([0, 0]),
                "num_pts": -1,
                "detection_name": anno["name"][box_id],
                "detection_score": anno["score"][box_id],
                "attribute_name": "vehicle.moving"
                if "attribute_name" not in anno
                else anno["attribute_name"],  # NOTE: set default
            }
            boxes.append(box_dict)
        format_annos[sample_token] = boxes
    return format_annos


def load_prediction(
    token2pred_boxes: dict,
    max_boxes_per_sample: int,
    box_cls,
    verbose: bool = False,
    logger=None,
) -> Tuple[EvalBoxes, Dict]:
    """
    Loads object predictions from dt_annos.
    :param dt_annos: list contains pred annos.
    :param max_boxes_per_sample: Maximim number of boxes allowed per sample.
    :param box_cls: Type of box to load, e.g. DetectionBox or TrackingBox.
    :param verbose: Whether to print messages to stdout.
    :return: The deserialized results.
    """
    # Deserialize results
    all_results = EvalBoxes.deserialize(token2pred_boxes, box_cls)
    if verbose:
        logger.info("Found detections for {} samples.".format(len(all_results.sample_tokens)))

    # Check that each sample has no more than x predicted boxes.
    for sample_token in all_results.sample_tokens:
        assert len(all_results.boxes[sample_token]) <= max_boxes_per_sample, (
            "Error: Only <= %d boxes per sample allowed!" % max_boxes_per_sample
        )
    return all_results


def load_gt(token2gt_boxes: dict, box_cls, verbose: bool = False, logger=None) -> EvalBoxes:
    """
    Loads ground truth boxes from DB.
    :param gt_annos: list contains ground truth annos.
    :param box_cls: Type of box to load, e.g. DetectionBox or TrackingBox.
    :param verbose: Whether to print messages to stdout.
    :return: The GT boxes.
    """
    all_annotations = EvalBoxes()
    for sample_token, boxes in tqdm.tqdm(token2gt_boxes.items(), leave=verbose):
        sample_boxes = []
        for box in boxes:
            sample_boxes.append(box_cls.deserialize(box))
        all_annotations.add_boxes(sample_token, sample_boxes)

    if verbose:
        logger.info("Loaded ground truth annotations for {} samples.".format(len(all_annotations.sample_tokens)))

    return all_annotations


def add_center_dist(eval_boxes: EvalBoxes):
    """
    Adds the cylindrical (xy) center distance from ego vehicle to each box.
    :param eval_boxes: A set of boxes, either GT or predictions.
    :return: eval_boxes augmented with center distances.
    """
    for sample_token in eval_boxes.sample_tokens:
        # sample_rec = nusc.get('sample', sample_token)
        # sd_record = nusc.get('sample_data', sample_rec['data']['LIDAR_TOP'])
        # pose_record = nusc.get('ego_pose', sd_record['ego_pose_token'])
        pose_record = {"translation": (0, 0, 0)}

        for box in eval_boxes[sample_token]:
            # Both boxes and ego pose are given in global coord system, so distance can be calculated directly.
            # Note that the z component of the ego pose is 0.
            ego_translation = (
                box.translation[0] - pose_record["translation"][0],
                box.translation[1] - pose_record["translation"][1],
                box.translation[2] - pose_record["translation"][2],
            )
            if isinstance(box, DetectionBox):
                box.ego_translation = ego_translation
            else:
                raise NotImplementedError

    return eval_boxes


def mapping_boxes_categories(eval_boxes: EvalBoxes, category_mapping: Dict[str, str]) -> EvalBoxes:
    for sample_token in eval_boxes.sample_tokens:
        for box in eval_boxes[sample_token]:
            mapping_name = category_mapping.get(box.detection_name, None)
            if mapping_name:
                if isinstance(box, DetectionBox):
                    box.mapping_detection_name = mapping_name
                else:
                    raise NotImplementedError
            else:
                raise ValueError(
                    f"sample_token = {sample_token}, box.detection_name = <{box.detection_name}> not in category_mapping dict {category_mapping}"
                )
    return eval_boxes


def get_valid_boxes(gt_boxes: EvalBoxes, valid_keys: list, ignored_keys: list, verbose: bool = False, logger=None):
    """Split valid boxes and ignored boxes"""
    class_field = _get_box_class_field(gt_boxes)

    token2ignored_areas = dict()
    num_ignored_areas = 0
    for sample_token in gt_boxes.sample_tokens:
        valid_boxes, ignored_areas = [], []
        for box in gt_boxes[sample_token]:
            if box.__getattribute__(class_field) in ignored_keys:
                ignored_areas.append(box)
                num_ignored_areas += 1
            elif box.__getattribute__(class_field) in valid_keys:
                valid_boxes.append(box)

        gt_boxes.boxes[sample_token] = valid_boxes
        token2ignored_areas[sample_token] = ignored_areas

    if verbose:
        logger.info("boxes have {} ignored areas".format(num_ignored_areas))
    return gt_boxes, token2ignored_areas


def filter_eval_boxes(
    eval_boxes: EvalBoxes,
    roi_mode: str,
    max_dist: Dict,
    ignored_areas: Dict,
    is_gt: bool = False,
    verbose: bool = False,
    logger=None,
) -> EvalBoxes:
    """
    Applies filtering to boxes. Distance, bike-racks and points per box.
    :param eval_boxes: An instance of the EvalBoxes class.
    :param max_dist: Maps the detection name to the eval distance threshold for that class.
    :param verbose: Whether to print to stdout.
    """
    # Retrieve box type for detectipn/tracking boxes.
    class_field = _get_box_class_field(eval_boxes)

    # Accumulators for number of filtered boxes.
    total, dist_filter, point_filter, ignore_area_filter = 0, 0, 0, 0
    for ind, sample_token in enumerate(eval_boxes.sample_tokens):
        # Filter on distance first.
        total += len(eval_boxes[sample_token])
        if roi_mode == "circle":
            boxes = []
            for box in eval_boxes[sample_token]:
                if box.ego_dist < max_dist[box.__getattribute__(class_field)]:
                    boxes.append(box)
                elif is_gt:
                    box.is_ignore = True
                    boxes.append(box)
            eval_boxes.boxes[sample_token] = boxes
        elif roi_mode == "rectangle":
            boxes = []
            for box in eval_boxes[sample_token]:
                limit_range = np.array(max_dist[box.__getattribute__(class_field)])
                box_center = np.array(box.translation)[:2]
                if (np.concatenate([box_center - limit_range[:2], limit_range[2:4] - box_center]) > 0).all():
                    boxes.append(box)
                elif is_gt:
                    box.is_ignore = True
                    boxes.append(box)
            eval_boxes.boxes[sample_token] = boxes
        else:
            raise NotImplementedError

        dist_filter += len(eval_boxes[sample_token])

        # # Then remove boxes with zero points in them. Eval boxes have -1 points by default.
        eval_boxes.boxes[sample_token] = [box for box in eval_boxes[sample_token] if not box.num_pts == 0]
        point_filter += len(eval_boxes[sample_token])

        # Remove boxes whose centers are in ignore areas
        filtered_boxes = []
        if len(ignored_areas[sample_token]) > 0:
            ignored_boxes = [
                Box(area.translation, area.size, Quaternion(area.rotation)) for area in ignored_areas[sample_token]
            ]
            for box in eval_boxes[sample_token]:
                in_ignore_area = False
                for ignore_box in ignored_boxes:
                    if np.sum(points_in_box(ignore_box, np.expand_dims(np.array(box.translation), axis=1))) > 0:
                        in_ignore_area = True
                if in_ignore_area is False:
                    filtered_boxes.append(box)

            eval_boxes.boxes[sample_token] = filtered_boxes
        ignore_area_filter += len(eval_boxes.boxes[sample_token])

        # # Perform bike-rack filtering.
        # sample_anns = nusc.get('sample', sample_token)['anns']
        # bikerack_recs = [nusc.get('sample_annotation', ann) for ann in sample_anns if
        #                  nusc.get('sample_annotation', ann)['category_name'] == 'static_object.bicycle_rack']
        # bikerack_boxes = [Box(rec['translation'], rec['size'], Quaternion(rec['rotation'])) for rec in bikerack_recs]
        # filtered_boxes = []
        # for box in eval_boxes[sample_token]:
        #     if box.__getattribute__(class_field) in ['bicycle', 'motorcycle']:
        #         in_a_bikerack = False
        #         for bikerack_box in bikerack_boxes:
        #             if np.sum(points_in_box(bikerack_box, np.expand_dims(np.array(box.translation), axis=1))) > 0:
        #                 in_a_bikerack = True
        #         if not in_a_bikerack:
        #             filtered_boxes.append(box)
        #     else:
        #         filtered_boxes.append(box)

        # eval_boxes.boxes[sample_token] = filtered_boxes
        # bike_rack_filter += len(eval_boxes.boxes[sample_token])

    if verbose:
        print_str = PrintStr()
        print_str += "=> Original number of boxes: %d" % total
        print_str += "=> After distance based filtering: %d" % dist_filter
        print_str += "=> After LIDAR points based filtering: %d" % point_filter
        # print("=> After bike rack filtering: %d" % bike_rack_filter)
        print_str += "=> After ignore area based filtering: %d" % ignore_area_filter
        logger.info(str(print_str))

    return eval_boxes


def filter_pred_ego_car(eval_boxes: EvalBoxes, verbose: bool = False, logger=None):
    ego_filter = 0
    for sample_token in eval_boxes.sample_tokens:
        # Remove prediction ego car boxes
        filtered_boxes = []
        ego_range = np.array([-0.5, -0.5, 0.5, 0.5])
        for box in eval_boxes[sample_token]:
            box_center = box.translation[:2]
            if not (np.concatenate([box_center - ego_range[:2], ego_range[2:] - box_center]) > 0).all():
                filtered_boxes.append(box)
        ego_filter += len(filtered_boxes)
        eval_boxes.boxes[sample_token] = filtered_boxes
    if verbose:
        logger.info("=> After prediction ego car based filtering: %d" % ego_filter)
    return eval_boxes


def set_cipo(gt_boxes: EvalBoxes, verbose: bool = False, logger=None) -> EvalBoxes:
    cipo_filter = 0
    for sample_token in gt_boxes.sample_tokens:
        if len(gt_boxes[sample_token]) == 0:
            continue
        cipo_index = 0
        for box_id, box in enumerate(gt_boxes.boxes[sample_token]):
            if not box.is_ignore and box.ego_dist < gt_boxes.boxes[sample_token][cipo_index].ego_dist:
                cipo_index = box_id
        gt_boxes.boxes[sample_token][cipo_index].is_cipo = True
        cipo_filter += 1

    if verbose:
        logger.info("=> After cipo based filtering: %d" % cipo_filter)
    return gt_boxes


def get_dynamic_roi_info(
    gt_boxes: EvalBoxes, gt_annos: list, eval_cfg, verbose: bool = False, logger=None
) -> SampleInfoDict:
    sample_info_dict = SampleInfoDict()
    for sample_token in gt_boxes.sample_tokens:
        cipo_box = None
        sample_idx = int(sample_token.split("_")[-1])
        sample_info = SampleInfo(
            sample_token=sample_token,
            dynamic_roi_range=eval_cfg.dynamic_roi_range["range"],
            min_front_dist=eval_cfg.dynamic_roi_range["min_front_dist"],
            json_path=gt_annos[sample_idx].get("json_path", ""),
            json_frame_id=gt_annos[sample_idx].get("json_frame_id", -1),
        )

        for box in gt_boxes.boxes[sample_token]:
            if not box.is_ignore and sample_info.box_in_dynamic_roi(box):
                if cipo_box is None:
                    cipo_box = box
                elif box.ego_dist < cipo_box.ego_dist:
                    cipo_box = box

        sample_info.update_dynamic_roi(cipo_box)
        sample_info_dict.set_sample_info(sample_token, sample_info)
    if verbose:
        logger.info("=> Total dynamic roi sample info = %d." % len(sample_info_dict))
    return sample_info_dict


def set_dynamic_roi_flag(
    dynamic_roi_info: SampleInfoDict, eval_boxes: EvalBoxes, verbose: bool = False, logger=None
) -> EvalBoxes:
    in_dynamic_roi_cnt = 0
    for sample_token in eval_boxes.sample_tokens:
        for box in eval_boxes.boxes[sample_token]:
            if dynamic_roi_info.get_sample_info(sample_token).box_in_dynamic_roi(box):
                box.is_in_dynamic_roi = True
                in_dynamic_roi_cnt += 1
    if verbose:
        logger.info("=> Set dynamic roi box number = %d" % in_dynamic_roi_cnt)
    return eval_boxes


def _get_box_class_field(eval_boxes: EvalBoxes) -> str:
    """
    Retrieve the name of the class field in the boxes.
    This parses through all boxes until it finds a valid box.
    If there are no valid boxes, this function throws an exception.
    :param eval_boxes: The EvalBoxes used for evaluation.
    :return: The name of the class field in the boxes, e.g. detection_name or tracking_name.
    """
    assert len(eval_boxes.boxes) > 0
    box = None
    for val in eval_boxes.boxes.values():
        if len(val) > 0:
            box = val[0]
            break
    if isinstance(box, DetectionBox):
        class_field = "detection_name"
    else:
        raise Exception("Error: Invalid box type: %s" % box)

    return class_field
