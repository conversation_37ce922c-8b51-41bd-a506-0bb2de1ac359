# nuScenes dev-kit.
# Code written by <PERSON>, 2019.

import copy
import os
from typing import Callable, Dict, List, Tuple

import numpy as np

from perceptron.data.det3d.dataset.private_data.eval_utils.common.data_classes import EvalBoxes
from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import (
    attr_acc,
    center_distance,
    cummean,
    scale_iou,
    velocity_l2,
    yaw_diff,
)
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.data_classes import DetectionMetricData


def accumulate(
    gt_boxes: EvalBoxes,
    pred_boxes: EvalBoxes,
    class_name: str,
    dist_fcn: Callable,
    dist_th: float,
    expected_recall: float,
    verbose: bool = False,
    logger=None,
) -> DetectionMetricData:
    """
    Average Precision over predefined different recall thresholds for a single distance threshold.
    The recall/conf thresholds and other raw metrics will be used in secondary metrics.
    :param gt_boxes: Maps every sample_token to a list of its sample_annotations.
    :param pred_boxes: Maps every sample_token to a list of its sample_results.
    :param class_name: Class to compute AP on.
    :param dist_fcn: Distance function used to match detections and ground truths.
    :param dist_th: Distance threshold for a match.
    :param verbose: If true, print debug messages.
    :return: (average_prec, metrics). The average precision value and raw data for a number of metrics.
    """
    # ---------------------------------------------
    # Organize input and initialize accumulators.
    # ---------------------------------------------

    # Count the positives.
    npos = 0
    npos_cipo = 0
    npos_dyn_roi = 0
    existed_sample_tokens = []
    existed_sample_tokens_dyn_roi = []
    for gt_box in gt_boxes.all:
        if gt_box.mapping_detection_name == class_name and not gt_box.is_ignore:
            existed_sample_tokens.append(gt_box.sample_token)
            npos += 1
            if gt_box.is_cipo:
                npos_cipo += 1
            if gt_box.is_in_dynamic_roi:
                npos_dyn_roi += 1
                existed_sample_tokens_dyn_roi.append(gt_box.sample_token)
    existed_sample_tokens = np.array(sorted(set(existed_sample_tokens)))
    existed_sample_nums = len(existed_sample_tokens)
    existed_sample_tokens_dyn_roi = np.array(sorted(set(existed_sample_tokens_dyn_roi)))
    existed_sample_nums_dyn_roi = len(existed_sample_tokens_dyn_roi)

    if verbose:
        logger.info(
            "{} Found {} GT ({} GT in dynamic roi) of class {} out of {} total across {} samples.".format(
                os.linesep, npos, npos_dyn_roi, class_name, len(gt_boxes.all), len(gt_boxes.sample_tokens)
            )
        )

    # For missing classes in the GT, return a data structure corresponding to no predictions.
    if npos == 0:
        return (
            DetectionMetricData.no_predictions(expected_recall),
            DetectionMetricData.no_predictions(expected_recall),
            None,
            None,
            None,
            None,
        )

    # Organize the predictions in a single list.
    pred_boxes_list = [box for box in pred_boxes.all if box.mapping_detection_name == class_name]
    pred_confs = [box.detection_score for box in pred_boxes_list]

    if verbose:
        logger.info(
            "{} Found {} PRED of class {} out of {} total across {} samples.".format(
                os.linesep, len(pred_confs), class_name, len(pred_boxes.all), len(pred_boxes.sample_tokens)
            )
        )

    # Sort by confidence.
    sortind = [i for (v, i) in sorted((v, i) for (i, v) in enumerate(pred_confs))][::-1]

    # tp, fp, conf, tp_cipo, conf_cipo, match_data, fp_boxes, matched_gt = calc_match(
    #     sortind, pred_boxes_list, gt_boxes, class_name, dist_fcn, dist_th
    # )
    roi_data, cipo_data, dyn_roi_data, fp_boxes, matched_gt = calc_match(
        sortind, pred_boxes_list, gt_boxes, class_name, dist_fcn, dist_th
    )

    # Check if we have any matches. If not, just return a "no predictions" array.
    if len(roi_data["match_data"]["trans_err"]) == 0:
        return (
            DetectionMetricData.no_predictions(expected_recall),
            DetectionMetricData.no_predictions(expected_recall),
            None,
            None,
            None,
            None,
        )

    roi_result, dyn_roi_result, md_details, md_details_dyn_roi = calc_info(
        roi_data,
        cipo_data,
        dyn_roi_data,
        npos,
        npos_cipo,
        npos_dyn_roi,
        expected_recall=expected_recall,
    )
    md_details["existed_sample_tokens"] = existed_sample_tokens
    md_details["existed_sample_nums"] = existed_sample_nums
    if md_details_dyn_roi:
        md_details_dyn_roi["existed_sample_tokens"] = existed_sample_tokens_dyn_roi
        md_details_dyn_roi["existed_sample_nums"] = existed_sample_nums_dyn_roi

    # ---------------------------------------------
    # Done. Instantiate MetricData and return
    # ---------------------------------------------
    md = DetectionMetricData(
        expected_recall=expected_recall,
        recall=roi_result["recall"],
        precision=roi_result["precision"],
        confidence=roi_result["confidence"],
        trans_err=roi_result["match_data"]["trans_err"],  # size = [1, nelem]
        vel_err=roi_result["match_data"]["vel_err"],
        scale_err=roi_result["match_data"]["scale_err"],
        orient_err=roi_result["match_data"]["orient_err"],
        attr_err=roi_result["match_data"]["attr_err"],
    )

    dyn_roi_md = DetectionMetricData(
        expected_recall=expected_recall,
        recall=dyn_roi_result["recall"],
        precision=dyn_roi_result["precision"],
        confidence=dyn_roi_result["confidence"],
        trans_err=dyn_roi_result["match_data"]["trans_err"],  # size = [1, nelem]
        vel_err=dyn_roi_result["match_data"]["vel_err"],
        scale_err=dyn_roi_result["match_data"]["scale_err"],
        orient_err=dyn_roi_result["match_data"]["orient_err"],
        attr_err=dyn_roi_result["match_data"]["attr_err"],
    )

    return md, dyn_roi_md, md_details, md_details_dyn_roi, fp_boxes, matched_gt


def init_match_data():
    return {
        "trans_err": [],
        "vel_err": [],
        "scale_err": [],
        "orient_err": [],
        "attr_err": [],
        "conf": [],
    }


def update_match_data(match_data, trans_err, vel_err, scale_err, orient_err, attr_err, conf):
    match_data["trans_err"].append(trans_err)
    match_data["vel_err"].append(vel_err)
    match_data["scale_err"].append(scale_err)
    match_data["orient_err"].append(orient_err)
    match_data["attr_err"].append(attr_err)
    match_data["conf"].append(conf)
    return match_data


def calc_match(sortind, pred_boxes_list, gt_boxes, class_name, dist_fcn, dist_th):
    # Do the actual matching.
    tp = []  # Accumulator of true positives.
    fp = []  # Accumulator of false positives.
    conf = []  # Accumulator of confidences.
    fp_boxes = []  # record fp boxes for visualizing.

    tp_dyn_roi = []  # Accumulator of true positives.
    fp_dyn_roi = []  # Accumulator of false positives.
    conf_dyn_roi = []  # Accumulator of confidences.

    tp_cipo = []
    conf_cipo = []

    # match_data holds the extra metrics we calculate for each match.
    match_data = init_match_data()

    match_data_dyn_roi = init_match_data()

    # ---------------------------------------------
    # Match and accumulate match data.
    # ---------------------------------------------

    taken = set()  # Initially no gt bounding box is matched.
    for ind in sortind:
        pred_box = pred_boxes_list[ind]
        min_dist = np.inf
        match_gt_idx = None

        for gt_idx, gt_box in enumerate(gt_boxes[pred_box.sample_token]):

            # Find closest match among ground truth boxes
            if (
                gt_box.mapping_detection_name == class_name
                and not (pred_box.sample_token, gt_idx) in taken
                and pred_box.mapping_detection_name == gt_box.mapping_detection_name
            ):
                this_distance = dist_fcn(gt_box, pred_box)
                if this_distance < min_dist:
                    min_dist = this_distance
                    match_gt_idx = gt_idx

        # If the closest match is close enough according to threshold we have a match!
        is_match = min_dist < dist_th

        # 对于ignore的gt，匹配上的 pred 不算到tp中，未匹配到的不算在fp中。
        if is_match and (match_gt_idx is not None) and (not gt_boxes[pred_box.sample_token][match_gt_idx].is_ignore):
            taken.add((pred_box.sample_token, match_gt_idx))

            gt_box_match = gt_boxes[pred_box.sample_token][match_gt_idx]

            # calc errors
            trans_err_tmp = center_distance(gt_box_match, pred_box)
            vel_err_tmp = velocity_l2(gt_box_match, pred_box)
            scale_err_tmp = 1 - scale_iou(gt_box_match, pred_box)
            # Barrier orientation is only determined up to 180 degree. (For cones orientation is discarded later)
            period = np.pi if class_name == "barrier" else 2 * np.pi
            orient_err_tmp = yaw_diff(gt_box_match, pred_box, period=period)
            attr_err_tmp = 1 - attr_acc(gt_box_match, pred_box)

            #  Update tp, fp and confs.
            tp.append(1)
            fp.append(0)
            conf.append(pred_box.detection_score)

            # Since it is a match, update match data also.
            match_data = update_match_data(
                match_data,
                trans_err_tmp,
                vel_err_tmp,
                scale_err_tmp,
                orient_err_tmp,
                attr_err_tmp,
                pred_box.detection_score,
            )

            # cipo
            if gt_box_match.is_cipo:
                tp_cipo.append(1)
                conf_cipo.append(pred_box.detection_score)

            # in dynamic roi
            if gt_box_match.is_in_dynamic_roi:
                tp_dyn_roi.append(1)
                fp_dyn_roi.append(0)
                conf_dyn_roi.append(pred_box.detection_score)
                # update metric data also.
                match_data_dyn_roi = update_match_data(
                    match_data_dyn_roi,
                    trans_err_tmp,
                    vel_err_tmp,
                    scale_err_tmp,
                    orient_err_tmp,
                    attr_err_tmp,
                    pred_box.detection_score,
                )

        elif (match_gt_idx is not None) and (gt_boxes[pred_box.sample_token][match_gt_idx].is_ignore):
            # 对于ignore的gt，未匹配到 pred 不算在fp中。
            continue
        elif not is_match:
            # No match. Mark this as a false positive.
            tp.append(0)
            fp.append(1)
            conf.append(pred_box.detection_score)
            fp_boxes.append(pred_box.serialize())
            if pred_box.is_in_dynamic_roi:
                tp_dyn_roi.append(0)
                fp_dyn_roi.append(1)
                conf_dyn_roi.append(pred_box.detection_score)
        else:
            raise ValueError("Bad match in calc_match function, please check!!")
    roi_data = {
        "tp": tp,
        "fp": fp,
        "conf": conf,
        "match_data": match_data,
    }

    cipo_data = {
        "tp": tp_cipo,
        "fp": [],
        "conf": conf_cipo,
        "match_data": {},
    }

    dyn_roi_data = {"tp": tp_dyn_roi, "fp": fp_dyn_roi, "conf": conf_dyn_roi, "match_data": match_data_dyn_roi}

    return roi_data, cipo_data, dyn_roi_data, fp_boxes, taken


def get_prec_rec_conf(roi_data, npos):
    tp = roi_data["tp"]
    fp = roi_data["fp"]
    conf = np.array(roi_data["conf"])
    if len(tp) > 0:
        # Accumulate.
        tp = np.cumsum(tp).astype(float)
        fp = np.cumsum(fp).astype(float)

        # Calculate precision and recall.
        prec = tp / (fp + tp)
        rec = tp / max(float(npos), 1)
    else:
        prec = np.array([])
        rec = np.array([])
        conf = np.array([])
    return prec, rec, conf


def get_interp_data(prec, rec, conf, match_data, expected_recall):
    rec_interp = np.linspace(
        0, 1, DetectionMetricData.get_nelem(expected_recall)
    )  # nelem steps, from 0% to 100% recall.
    # 此处判断 match_data 中 err 数据长度的原因：
    # 存在 prec 长度不为 0，但是全部没有命中，即prec全为 0，因此err 数据为空list导致np.interp 报错。
    if len(match_data[list(match_data.keys())[0]]) > 0:
        prec = np.interp(rec_interp, rec, prec, right=0)
        conf = np.interp(rec_interp, rec, conf, right=0)
        # ---------------------------------------------
        # Re-sample the match-data to match, prec, recall and conf.
        # ---------------------------------------------
        for key in match_data.keys():
            if key == "conf":
                continue  # Confidence is used as reference to align with fp and tp. So skip in this step.
            else:
                # For each match_data, we first calculate the accumulated mean.
                tmp = cummean(np.array(match_data[key]))

                # Then interpolate based on the confidences. (Note reversing since np.interp needs increasing arrays)
                match_data[key] = np.interp(conf[::-1], match_data["conf"][::-1], tmp[::-1])[::-1]

    else:
        # no prediction
        prec = np.zeros_like(rec_interp)
        conf = np.zeros_like(rec_interp)
        for key in match_data.keys():
            if key == "conf":
                match_data["conf"] = np.zeros_like(rec_interp)
            else:
                match_data[key] = np.ones_like(rec_interp)

    rec = rec_interp
    return {
        "precision": prec,
        "recall": rec,
        "confidence": conf,
        "match_data": match_data,
    }


def calc_info(
    roi_data,
    cipo_data,
    dyn_roi_data,
    npos,
    npos_cipo,
    npos_dyn_roi,
    expected_recall,
):
    # parse data
    # roi match data
    match_data = roi_data["match_data"]

    # cipo
    tp_cipo = cipo_data["tp"]
    conf_cipo = cipo_data["conf"]

    # dynamic roi match data
    match_data_dyn_roi = dyn_roi_data["match_data"]

    # Accumulate.
    prec, rec, conf = get_prec_rec_conf(roi_data, npos)
    prec_dyn_roi, rec_dyn_roi, conf_dyn_roi = get_prec_rec_conf(dyn_roi_data, npos_dyn_roi)

    # NOTE: Save info for caculating precision/recall by specific threshold
    rec_cipo = np.cumsum(tp_cipo).astype(float) / max(npos_cipo, 1)
    conf_cipo = np.array(conf_cipo)
    md_details = {
        # roi
        "prec_roi": copy.deepcopy(prec),
        "rec_roi": copy.deepcopy(rec),
        "fp_roi": np.cumsum(copy.deepcopy(roi_data["fp"])).astype(float),
        "conf_roi": copy.deepcopy(conf),
        "npos": npos,  # roi gt nums
        # cipo
        "rec_cipo": rec_cipo,
        "conf_cipo": conf_cipo,
        "npos_cipo": npos_cipo,  # cipo gt nums
        "matched_cipo": np.sum(tp_cipo),  # matched cipo nums
        # roi tp
        "trans_err": cummean(np.array(match_data["trans_err"])),
        "scale_err": cummean(np.array(match_data["scale_err"])),
        "orient_err": cummean(np.array(match_data["orient_err"])),
        "vel_err": cummean(np.array(match_data["vel_err"])),
        "attr_err": cummean(np.array(match_data["attr_err"])),
        "conf_tp": np.array(match_data["conf"]),
        "match_data": copy.deepcopy(match_data),
    }

    if npos_dyn_roi > 0:
        md_details_dyn_roi = {
            # dyn roi
            "prec_roi": copy.deepcopy(prec_dyn_roi),
            "rec_roi": copy.deepcopy(rec_dyn_roi),
            "fp_roi": np.cumsum(copy.deepcopy(dyn_roi_data["fp"])).astype(float),
            "conf_roi": copy.deepcopy(conf_dyn_roi),
            "npos": npos_dyn_roi,  # dynamic roi gt nums
            # cipo
            "rec_cipo": np.array([]),
            "conf_cipo": np.array([]),
            "npos_cipo": -1,  # cipo gt nums
            "matched_cipo": -1,  # matched cipo nums
            # dyn roi tp
            "trans_err": cummean(np.array(match_data_dyn_roi["trans_err"])),
            "scale_err": cummean(np.array(match_data_dyn_roi["scale_err"])),
            "orient_err": cummean(np.array(match_data_dyn_roi["orient_err"])),
            "vel_err": cummean(np.array(match_data_dyn_roi["vel_err"])),
            "attr_err": cummean(np.array(match_data_dyn_roi["attr_err"])),
            "conf_tp": np.array(match_data_dyn_roi["conf"]),
            "match_data": copy.deepcopy(match_data_dyn_roi),
        }
    else:
        md_details_dyn_roi = None

    roi_result = get_interp_data(prec, rec, conf, match_data, expected_recall)
    dyn_roi_result = get_interp_data(prec_dyn_roi, rec_dyn_roi, conf_dyn_roi, match_data_dyn_roi, expected_recall)

    return roi_result, dyn_roi_result, md_details, md_details_dyn_roi


def calc_ap(md: DetectionMetricData) -> float:
    """ Calculated average precision. """
    prec = np.copy(md.precision)
    ap = float(np.mean(prec))
    return ap


def calc_tp(md: DetectionMetricData, metric_name: str) -> float:
    """ Calculates true positive errors. """
    last_ind = md.max_recall_ind  # First instance of confidence = 0 is index of max achieved recall.
    if last_ind == 0:
        return 1.0  # Assign 1 here. If this happens for all classes, the score for that TP metric will be 0.
    else:
        return float(np.mean(getattr(md, metric_name)[: last_ind + 1]))  # +1 to include error at max recall.


def calc_tp_metric_ratio(data: List, tp_metric_error_ratio: List) -> Dict[Tuple, float]:
    tp_metric_error_ratio = sorted(tp_metric_error_ratio)
    if not tp_metric_error_ratio[0] == 0.0:
        tp_metric_error_ratio.insert(0, 0.0)
    tp_metric_error_ratio.append(float("inf"))

    data = np.array(data)
    total_match = len(data)
    err_ratio = {}
    error_interval = [(a, b) for a, b in zip(tp_metric_error_ratio[:-1], tp_metric_error_ratio[1:])]
    if total_match == 0:
        for err_inter in error_interval:
            err_ratio[str(err_inter)] = -1.0
    else:
        for err_inter in error_interval:
            if err_inter[0] == 0:
                ratio_tmp = np.sum((err_inter[0] <= data) * (data <= err_inter[1])) / total_match
            else:
                ratio_tmp = np.sum((err_inter[0] < data) * (data <= err_inter[1])) / total_match
            err_ratio[str(err_inter)] = ratio_tmp
    return err_ratio
