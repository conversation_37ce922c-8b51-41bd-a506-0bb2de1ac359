# nuScenes dev-kit.
# Code written by <PERSON>, 2019.

from collections import defaultdict
from typing import Dict, List, <PERSON><PERSON>

import numpy as np

from perceptron.data.det3d.dataset.private_data.eval_utils.common.data_classes import EvalBox, MetricData
from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import center_distance, LinearThres2D
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.constants import ATTRIBUTE_NAMES


class DetectionConfig:
    """ Data class that specifies the detection evaluation settings. """

    def __init__(
        self,
        class_names: List[str],
        ignored_names: List[str],
        roi_mode: str,
        class_range: Dict[str, List],
        dynamic_roi_range: Dict,
        roi_expected_recall: List[float],
        dist_fcn: str,
        dist_ths: List[float],
        dist_th_tp: float,
        tp_metrics: list,
        tp_metrics_err_ratio: Dict[str, list],
        # min_recall: float,
        # min_precision: float,
        max_boxes_per_sample: int,
        mean_ap_weight: int,
        class_mapping_rules: Dict[str, str] = None,
    ):

        # assert set(class_range.keys()) == set(DETECTION_NAMES), "Class count mismatch."
        assert dist_th_tp in dist_ths, "dist_th_tp must be in set of dist_ths."
        assert np.all(
            [x in tp_metrics for x in tp_metrics_err_ratio.keys()]
        ), "tp_metrics_err_ratio keys must in tp_metrics"

        self.roi_mode = roi_mode
        self.class_range = class_range
        self.dynamic_roi_range = dynamic_roi_range
        self.roi_expected_recall = roi_expected_recall
        self.dist_fcn = dist_fcn
        self.dist_ths = dist_ths
        self.dist_th_tp = dist_th_tp
        self.tp_metrics = tp_metrics
        self.tp_metrics_err_ratio = tp_metrics_err_ratio
        # self.min_recall = min_recall
        # self.min_precision = min_precision
        self.max_boxes_per_sample = max_boxes_per_sample
        self.mean_ap_weight = mean_ap_weight

        # self.class_names = self.class_range.keys()
        self.class_names = class_names
        self.ignored_names = ignored_names
        if class_mapping_rules:
            assert list(class_mapping_rules.keys()) == self.class_names
        self.class_mapping_rules = class_mapping_rules

    def __eq__(self, other):
        eq = True
        for key in self.serialize().keys():
            eq = eq and np.array_equal(getattr(self, key), getattr(other, key))
        return eq

    def serialize(self) -> dict:
        """ Serialize instance into json-friendly format. """
        return {
            "class_names": self.class_names,
            "ignored_names": self.ignored_names,
            "roi_mode": self.roi_mode,
            "class_range": self.class_range,
            "dynamic_roi_range": self.dynamic_roi_range,
            "roi_expected_recall": self.roi_expected_recall,
            "dist_fcn": self.dist_fcn,
            "dist_ths": self.dist_ths,
            "dist_th_tp": self.dist_th_tp,
            "tp_metrics": self.tp_metrics,
            "tp_metrics_err_ratio": self.tp_metrics_err_ratio,
            # "min_recall": self.min_recall,
            # "min_precision": self.min_precision,
            "max_boxes_per_sample": self.max_boxes_per_sample,
            "mean_ap_weight": self.mean_ap_weight,
            "class_mapping_rules": self.class_mapping_rules,
        }

    @classmethod
    def deserialize(cls, content: dict):
        """ Initialize from serialized dictionary. """
        return cls(
            content["class_names"],
            content["ignored_names"],
            content["roi"]["mode"],
            content["roi"]["range"],
            content["roi"]["dynamic_range"],
            content["roi"]["expected_recall"],
            content["dist_fcn"],
            content["dist_ths"],
            content["dist_th_tp"],
            content["tp_metrics"],
            content["tp_metrics_err_ratio"],
            # content["min_recall"],
            # content["min_precision"],
            content["max_boxes_per_sample"],
            content["mean_ap_weight"],
            content["class_mapping_rules"],
        )

    @property
    def dist_fcn_callable(self):
        """ Return the distance function corresponding to the dist_fcn string. """
        if self.dist_fcn == "center_distance":
            return center_distance
        elif self.dist_fcn == "center_distance_linear_threshold":
            return LinearThres2D().distance_calc
        elif isinstance(self.dist_fcn, dict):
            if self.dist_fcn["dist_name"] == "center_distance":
                return center_distance
            elif self.dist_fcn["dist_name"] == "center_distance_linear_threshold":
                kwargs = {key: value for key, value in self.dist_fcn.items() if key != "dist_name"}
                return LinearThres2D(**kwargs).distance_calc
        else:
            raise Exception("Error: Unknown distance function %s!" % self.dist_fcn)


class DetectionMetricData(MetricData):
    """ This class holds accumulated and interpolated data required to calculate the detection metrics. """

    def __init__(
        self,
        expected_recall: float,
        recall: np.array,
        precision: np.array,
        confidence: np.array,
        trans_err: np.array,
        vel_err: np.array,
        scale_err: np.array,
        orient_err: np.array,
        attr_err: np.array,
    ):

        self.nelem = self.get_nelem(expected_recall)

        # Assert lengths.
        assert len(recall) == self.nelem
        assert len(precision) == self.nelem
        assert len(confidence) == self.nelem
        assert len(trans_err) == self.nelem
        assert len(vel_err) == self.nelem
        assert len(scale_err) == self.nelem
        assert len(orient_err) == self.nelem
        assert len(attr_err) == self.nelem

        # Assert ordering.
        assert all(confidence == sorted(confidence, reverse=True))  # Confidences should be descending.
        assert all(recall == sorted(recall))  # Recalls should be ascending.

        # Set attributes explicitly to help IDEs figure out what is going on.
        self.recall = recall
        self.precision = precision
        self.confidence = confidence
        self.trans_err = trans_err
        self.vel_err = vel_err
        self.scale_err = scale_err
        self.orient_err = orient_err
        self.attr_err = attr_err

    def __eq__(self, other):
        eq = True
        for key in self.serialize().keys():
            eq = eq and np.array_equal(getattr(self, key), getattr(other, key))
        return eq

    @property
    def max_recall_ind(self):
        """ Returns index of max recall achieved. """

        # Last instance of confidence > 0 is index of max achieved recall.
        non_zero = np.nonzero(self.confidence)[0]
        if len(non_zero) == 0:  # If there are no matches, all the confidence values will be zero.
            max_recall_ind = 0
        else:
            max_recall_ind = non_zero[-1]

        return max_recall_ind

    @property
    def max_recall(self):
        """ Returns max recall achieved. """

        return self.recall[self.max_recall_ind]

    def serialize(self):
        """ Serialize instance into json-friendly format. """
        return {
            "recall": self.recall.tolist(),
            "precision": self.precision.tolist(),
            "confidence": self.confidence.tolist(),
            "trans_err": self.trans_err.tolist(),
            "vel_err": self.vel_err.tolist(),
            "scale_err": self.scale_err.tolist(),
            "orient_err": self.orient_err.tolist(),
            "attr_err": self.attr_err.tolist(),
        }

    @classmethod
    def get_nelem(cls, expected_recall: float) -> int:
        # 根据expected_recall，设置合理的 recall 插值数目
        if expected_recall == 1.0:
            return 1001
        recall_str = str(expected_recall).split(".", 2)[-1]
        nelem = int(10 ** max(len(recall_str), 2)) + 1  # 最小插值数目为 101
        return nelem

    @classmethod
    def deserialize(cls, content: dict):
        """ Initialize from serialized content. """
        return cls(
            expected_recall=float(content["expected_recall"]),
            recall=np.array(content["recall"]),
            precision=np.array(content["precision"]),
            confidence=np.array(content["confidence"]),
            trans_err=np.array(content["trans_err"]),
            vel_err=np.array(content["vel_err"]),
            scale_err=np.array(content["scale_err"]),
            orient_err=np.array(content["orient_err"]),
            attr_err=np.array(content["attr_err"]),
        )

    @classmethod
    def no_predictions(cls, expected_recall):
        """ Returns a md instance corresponding to having no predictions. """
        nelem = cls.get_nelem(expected_recall)
        return cls(
            expected_recall=expected_recall,
            recall=np.linspace(0, 1, nelem),
            precision=np.zeros(nelem),
            confidence=np.zeros(nelem),
            trans_err=np.ones(nelem),
            vel_err=np.ones(nelem),
            scale_err=np.ones(nelem),
            orient_err=np.ones(nelem),
            attr_err=np.ones(nelem),
        )

    @classmethod
    def random_md(cls, expected_recall):
        """ Returns an md instance corresponding to a random results. """
        nelem = cls.get_nelem(expected_recall)
        return cls(
            expected_recall=expected_recall,
            recall=np.linspace(0, 1, nelem),
            precision=np.random.random(nelem),
            confidence=np.linspace(0, 1, nelem)[::-1],
            trans_err=np.random.random(nelem),
            vel_err=np.random.random(nelem),
            scale_err=np.random.random(nelem),
            orient_err=np.random.random(nelem),
            attr_err=np.random.random(nelem),
        )


class DetectionMetrics:
    """ Stores average precision and true positive metric results. Provides properties to summarize. """

    def __init__(self, cfg: DetectionConfig):

        self.cfg = cfg
        self._label_aps = defaultdict(lambda: defaultdict(float))
        self._label_tp_errors = defaultdict(lambda: defaultdict(float))
        self.eval_time = None

    def add_label_ap(self, detection_name: str, dist_th: float, ap: float) -> None:
        self._label_aps[detection_name][dist_th] = ap

    def get_label_ap(self, detection_name: str, dist_th: float) -> float:
        return self._label_aps[detection_name][dist_th]

    def add_label_tp(self, detection_name: str, metric_name: str, tp: float):
        self._label_tp_errors[detection_name][metric_name] = tp

    def get_label_tp(self, detection_name: str, metric_name: str) -> float:
        return self._label_tp_errors[detection_name][metric_name]

    def add_runtime(self, eval_time: float) -> None:
        self.eval_time = eval_time

    @property
    def mean_dist_aps(self) -> Dict[str, float]:
        """ Calculates the mean over distance thresholds for each label. """
        return {class_name: np.mean(list(d.values())) for class_name, d in self._label_aps.items()}

    @property
    def mean_ap(self) -> float:
        """ Calculates the mean AP by averaging over distance thresholds and classes. """
        return float(np.mean(list(self.mean_dist_aps.values())))

    @property
    def tp_errors(self) -> Dict[str, float]:
        """ Calculates the mean true positive error across all classes for each metric. """
        errors = {}
        for metric_name in self.cfg.tp_metrics:
            class_errors = []
            for detection_name in self._label_tp_errors:
                class_errors.append(self.get_label_tp(detection_name, metric_name))
            errors[metric_name] = float(np.nanmean(class_errors))

        return errors

    @property
    def tp_scores(self) -> Dict[str, float]:
        scores = {}
        tp_errors = self.tp_errors
        for metric_name in self.cfg.tp_metrics:

            # We convert the true positive errors to "scores" by 1-error.
            score = 1.0 - tp_errors[metric_name]

            # Some of the true positive errors are unbounded, so we bound the scores to min 0.
            score = max(0.0, score)

            scores[metric_name] = score

        return scores

    @property
    def nd_score(self) -> float:
        """
        Compute the nuScenes detection score (NDS, weighted sum of the individual scores).
        :return: The NDS.
        """
        # Summarize.
        total = float(self.cfg.mean_ap_weight * self.mean_ap + np.sum(list(self.tp_scores.values())))

        # Normalize.
        total = total / float(self.cfg.mean_ap_weight + len(self.tp_scores.keys()))

        return total

    def serialize(self):
        return {
            "label_aps": self._label_aps,
            "mean_dist_aps": self.mean_dist_aps,
            "mean_ap": self.mean_ap,
            "label_tp_errors": self._label_tp_errors,
            "tp_errors": self.tp_errors,
            "tp_scores": self.tp_scores,
            "nd_score": self.nd_score,
            "eval_time": self.eval_time,
            "cfg": self.cfg.serialize(),
        }

    @classmethod
    def deserialize(cls, content: dict):
        """ Initialize from serialized dictionary. """

        cfg = DetectionConfig.deserialize(content["cfg"])

        metrics = cls(cfg=cfg)
        metrics.add_runtime(content["eval_time"])

        for detection_name, label_aps in content["label_aps"].items():
            for dist_th, ap in label_aps.items():
                metrics.add_label_ap(detection_name=detection_name, dist_th=float(dist_th), ap=float(ap))

        for detection_name, label_tps in content["label_tp_errors"].items():
            for metric_name, tp in label_tps.items():
                metrics.add_label_tp(detection_name=detection_name, metric_name=metric_name, tp=float(tp))

        return metrics

    def __eq__(self, other):
        eq = True
        eq = eq and self._label_aps == other._label_aps
        eq = eq and self._label_tp_errors == other._label_tp_errors
        eq = eq and self.eval_time == other.eval_time
        eq = eq and self.cfg == other.cfg

        return eq


class DetectionBox(EvalBox):
    """ Data class used during detection evaluation. Can be a prediction or ground truth."""

    def __init__(
        self,
        sample_token: str = "",
        translation: Tuple[float, float, float] = (0, 0, 0),
        size: Tuple[float, float, float] = (0, 0, 0),
        rotation: Tuple[float, float, float, float] = (0, 0, 0, 0),
        velocity: Tuple[float, float] = (0, 0),
        ego_translation: Tuple[float, float, float] = (0, 0, 0),  # Translation to ego vehicle in meters.
        num_pts: int = -1,  # Nbr. LIDAR or RADAR inside the box. Only for gt boxes.
        detection_name: str = "car",  # The class name used in the detection challenge.
        detection_score: float = -1.0,  # GT samples do not have a score.
        attribute_name: str = "",
        is_cipo: bool = False,
        is_ignore: bool = False,
        is_in_dynamic_roi: bool = False,
        json_path: str = "",
        json_frame_id: int = -1,
    ):  # Box attribute. Each box can have at most 1 attribute.

        super().__init__(sample_token, translation, size, rotation, velocity, ego_translation, num_pts)

        assert detection_name is not None, "Error: detection_name cannot be empty!"
        # assert detection_name in DETECTION_NAMES, 'Error: Unknown detection_name %s' % detection_name

        assert attribute_name in ATTRIBUTE_NAMES or attribute_name == "", (
            "Error: Unknown attribute_name %s" % attribute_name
        )

        assert type(detection_score) == float, "Error: detection_score must be a float!"
        assert not np.any(np.isnan(detection_score)), "Error: detection_score may not be NaN!"

        # Assign.
        self.detection_name = detection_name
        self.mapping_detection_name = detection_name
        self.detection_score = detection_score
        self.attribute_name = attribute_name
        self.is_cipo = is_cipo
        self.is_ignore = is_ignore
        self.is_in_dynamic_roi = is_in_dynamic_roi
        self.json_path = json_path
        self.json_frame_id = json_frame_id

    def __eq__(self, other):
        return (
            self.sample_token == other.sample_token
            and self.translation == other.translation
            and self.size == other.size
            and self.rotation == other.rotation
            and self.velocity == other.velocity
            and self.ego_translation == other.ego_translation
            and self.num_pts == other.num_pts
            and self.detection_name == other.detection_name
            and self.mapping_detection_name == other.mapping_detection_name
            and self.detection_score == other.detection_score
            and self.attribute_name == other.attribute_name
            and self.json_path == other.json_path
            and self.json_path == other.json_frame_id
        )

    def serialize(self) -> dict:
        """ Serialize instance into json-friendly format. """
        return {
            "sample_token": self.sample_token,
            "translation": self.translation,
            "size": self.size,
            "rotation": self.rotation,
            "velocity": self.velocity,
            "ego_translation": self.ego_translation,
            "num_pts": self.num_pts,
            "detection_name": self.detection_name,
            "mapping_detection_name": self.mapping_detection_name,
            "detection_score": self.detection_score,
            "attribute_name": self.attribute_name,
            "is_cipo": self.is_cipo,
            "is_ignore": self.is_ignore,
            "is_in_dynamic_roi": self.is_in_dynamic_roi,
            "json_path": self.json_path,
            "json_frame_id": self.json_frame_id,
        }

    @classmethod
    def deserialize(cls, content: dict):
        """ Initialize from serialized content. """
        return cls(
            sample_token=content["sample_token"],
            translation=tuple(content["translation"]),
            size=tuple(content["size"]),
            rotation=tuple(content["rotation"]),
            velocity=tuple(content["velocity"]),
            ego_translation=(0.0, 0.0, 0.0) if "ego_translation" not in content else tuple(content["ego_translation"]),
            num_pts=-1 if "num_pts" not in content else int(content["num_pts"]),
            detection_name=content["detection_name"],
            detection_score=-1.0 if "detection_score" not in content else float(content["detection_score"]),
            attribute_name=content["attribute_name"],
            is_cipo=content.get("is_cipo", False),
            is_ignore=content.get("is_ignore", False),
            is_in_dynamic_roi=content.get("is_in_dynamic_roi", False),
            json_path=content.get("json_path", ""),
            json_frame_id=content.get("json_frame_id", -1),
        )


class DetectionMetricDataList:
    """ This stores a set of MetricData in a dict indexed by (name, match-distance). """

    def __init__(self):
        self.md = {}

    def __getitem__(self, key):
        return self.md[key]

    def __eq__(self, other):
        eq = True
        for key in self.md.keys():
            eq = eq and self[key] == other[key]
        return eq

    def get_class_data(self, detection_name: str) -> List[Tuple[DetectionMetricData, float]]:
        """ Get all the MetricData entries for a certain detection_name. """
        return [(md, dist_th) for (name, dist_th), md in self.md.items() if name == detection_name]

    def get_dist_data(self, dist_th: float) -> List[Tuple[DetectionMetricData, str]]:
        """ Get all the MetricData entries for a certain match_distance. """
        return [(md, detection_name) for (detection_name, dist), md in self.md.items() if dist == dist_th]

    def set(self, detection_name: str, match_distance: float, data: DetectionMetricData):
        """ Sets the MetricData entry for a certain detection_name and match_distance. """
        self.md[(detection_name, match_distance)] = data

    def serialize(self) -> dict:
        return {key[0] + ":" + str(key[1]): value.serialize() for key, value in self.md.items()}

    @classmethod
    def deserialize(cls, content: dict):
        mdl = cls()
        for key, md in content.items():
            name, distance = key.split(":")
            mdl.set(name, float(distance), DetectionMetricData.deserialize(md))
        return mdl


class SampleInfo:
    """
    This class save each frame dynamic information, also provide some useful functions.
    """

    def __init__(
        self,
        sample_token: str = "",
        dynamic_roi_range: Tuple[float, float, float, float] = (
            0,
            0,
            0,
            0,
        ),  # (left_bottom_x, left_bottom_y, right_top_x, right_top_y)
        min_front_dist: float = 0.0,  # dynamic roi min front distance
        json_path: str = "",
        json_frame_id: int = -1,
    ):

        self.sample_token = sample_token
        self.dynamic_roi_range = dynamic_roi_range
        self.min_front_dist = min_front_dist
        self.json_path = json_path
        self.json_frame_id = json_frame_id

    def update_dynamic_roi(self, box: DetectionBox = None) -> None:
        # 当box 为 None 时，设置dynamic 前向最小值为 self.min_front_dist
        box_trans_y = box.translation[1] if box else 0.0
        dynamic_roi_front_range = max(self.min_front_dist, min(self.dynamic_roi_range[3], box_trans_y))
        tmp_range = list(self.dynamic_roi_range)
        tmp_range[3] = dynamic_roi_front_range
        self.dynamic_roi_range = tuple(tmp_range)

    def box_in_dynamic_roi(self, box: DetectionBox) -> bool:
        box_center_xy = np.array(box.translation)[:2]
        return (
            np.concatenate([box_center_xy - self.dynamic_roi_range[:2], self.dynamic_roi_range[2:4] - box_center_xy])
            >= 0
        ).all()

    def serialize(self) -> dict:
        return {
            "sample_token": self.sample_token,
            "dynamic_roi_range": self.dynamic_roi_range,
            "min_front_dist": self.min_front_dist,
            "json_path": self.json_path,
            "json_frame_id": self.json_frame_id,
        }

    @classmethod
    def deserialize(cls, content: dict):
        return cls(
            sample_token=content["sample_token"],
            dynamic_roi_range=content["dynamic_roi_range"],
            min_front_dist=content["min_front_dist"],
            json_path=content["json_path"],
            json_frame_id=content["json_frame_id"],
        )


class SampleInfoDict:
    def __init__(self):
        self.sample_info_dict = {}

    def __len__(self):
        return len(self.sample_info_dict)

    def set_sample_info(self, key, value) -> None:
        self.sample_info_dict[key] = value

    def get_sample_info(self, key) -> SampleInfo:
        return self.sample_info_dict[key]

    def serialize(self) -> dict:
        sample_info_dict = {}
        for k, v in self.sample_info_dict.items():
            sample_info_dict[k] = v.serialize()
        return sample_info_dict

    @classmethod
    def deserialize(cls, content: Dict[str, dict]):
        sample_info_dict = SampleInfoDict()
        for k, v in content.items():
            sample_info_dict.sample_info_dict[k] = SampleInfo.deserialize(v)
        return sample_info_dict
