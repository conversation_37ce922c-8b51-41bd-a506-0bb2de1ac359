import os
import copy
import refile
import mmcv
import torch
import numpy as np
from PIL import Image
from collections import defaultdict
from mmdet.datasets.pipelines import Compose
from mmdet3d.core.bbox.structures.lidar_box3d import LiDARInstance3DBoxes
from .dataset import PrivateDatasetWithEval
from data3d.datasets.private import PreLabelPrivateDataset
from perceptron.data.detbev.dataset.nuscenes import NuscBevDetData, img_transform, bev_transform


class PrivatePreLabelVisionDataset(PrivateDatasetWithEval):
    def __init__(
        self,
        data_configs,
        data_paths,
        eval_configs=None,
        lidar_key_list=["middle_lidar"],
        img_key_list=[],
        pipeline=[],
        class_names=None,
        training=True,
        only_key_frame=True,
        pc_fileds=["x", "y", "z", "i"],
        used_echo_id=[1],
        filter_empty_gt=True,
        pre_only=False,
        occlusion_filter=1,
        undistort=True,
        undistort_alpha=0.0,
    ):
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"] or lidar_key_list == []
        self.data_configs = data_configs
        self.class_names = class_names
        self.training = training
        self.dataset = PreLabelPrivateDataset(
            class_names=self.class_names,
            data_paths=data_paths,
            img_key_list=img_key_list,
            lidar_key_list=lidar_key_list,
            only_key_frame=only_key_frame,
            pc_fields=pc_fileds,
            used_echo_id=used_echo_id,
            pre_only=pre_only,
            occlusion_filter=occlusion_filter,
            undistort=undistort,
            undistort_alpha=undistort_alpha,
        )
        self.point_cloud_range = np.array(self.data_configs["point_cloud_range"])

        self.use_lead_xyz = True
        self.num_point_features = self.data_configs["use_num_point_features"]
        self.filter_empty_gt = filter_empty_gt

        if pipeline is not None:
            self.pipeline = Compose(pipeline)

    def _get_frame(self, idx: int):
        idx = self.dataset.key_frame_index[idx]
        item = self.dataset[idx]
        if self.dataset.lidar_key_list:
            data_dict = {
                "points": self._point_process(item)[:, : self.num_point_features],
                "frame_id": item["frame_id"],
                "use_lead_xyz": self.use_lead_xyz,
            }
        else:
            data_dict = {
                "frame_id": item["frame_id"],
                "use_lead_xyz": self.use_lead_xyz,
            }
        if self.dataset.img_key_list:
            img = []
            img_shape = []
            ori_shape = []
            lidar2img = []
            for cam in self.dataset.img_key_list:
                img.append(item[cam].astype(np.float32))
                img_shape.append(item[cam].shape)
                ori_shape.append(item[cam].shape)
                lidar2img_cam = None
                for info in item["img_info"]:
                    if info["channel"] == cam:
                        lidar2img_cam = np.concatenate(
                            [np.array(info["T_lidar_to_pixel"]), np.array([[0.0, 0.0, 0.0, 1.0]])]
                        )
                assert lidar2img_cam is not None, "Incorrect camera name in img info"
                lidar2img.append(lidar2img_cam)
            data_dict.update(
                {
                    "img": img,
                    "img_shape": img_shape,
                    "ori_shape": ori_shape,
                    "img_info": item["img_info"],
                    "frame_id": item["frame_id"],
                    "lidar2img": lidar2img,
                }
            )
        if "labels" in item:
            gt_bboxes_3d = copy.deepcopy(item["gt_boxes"])
            ann_info = dict(
                gt_bboxes_3d=gt_bboxes_3d,
                gt_names=item["labels"].tolist() if isinstance(item["labels"], np.ndarray) else [],
                gt_labels_3d=np.array(
                    [self.class_names.index(i) if i in self.class_names else -1 for i in item["labels"]]
                ),
                gt_track_id=np.arange(len(item["labels"])),
            )
            data_dict.update(
                {
                    "ann_info": ann_info,
                    "bbox3d_fields": [],
                }
            )

        data_dict = self.pipeline(data_dict)
        data_dict["frame_id"] = item["frame_id"]
        return data_dict

    def __getitem__(self, idx: int):
        data_dict = self._get_frame(idx)
        if self.training and self.filter_empty_gt:

            gt_labels = data_dict["gt_labels_3d"].data.detach()
            pos_labels = gt_labels[gt_labels >= 0]
            while pos_labels.numel() == 0:
                idx = np.random.choice(len(self.dataset))
                data_dict = self._get_frame(idx)
                gt_labels = data_dict["gt_labels_3d"].data.detach()
                pos_labels = gt_labels[gt_labels >= 0]
        return data_dict

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points"]:
                    ret[key] = val
                elif key in ["gt_boxes"]:
                    max_gt = max([len(x) for x in val])
                    batch_gt_boxes3d = np.zeros((batch_size, max_gt, val[0].shape[-1]), dtype=np.float32)
                    for k in range(batch_size):
                        batch_gt_boxes3d[k, : val[k].__len__(), :] = val[k]
                    ret[key] = batch_gt_boxes3d
                else:
                    ret[key] = val
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class PrivateBevDetData(PrivatePreLabelVisionDataset, NuscBevDetData):
    def __init__(
        self,
        data_configs,
        ida_aug_conf,
        bda_aug_conf,
        data_paths,
        lidar_key_list=["middle_lidar"],
        img_key_list=[],
        pipeline=[],
        eval_configs=None,
        class_names=None,
        training=True,
        only_key_frame=True,
        pc_fileds=["x", "y", "z", "i"],
        used_echo_id=[1],
        filter_empty_gt=True,
        pre_only=False,
        occlusion_filter=1,
        img_conf=dict(img_mean=[123.675, 116.28, 103.53], img_std=[58.395, 57.12, 57.375], to_rgb=True),
        undistort=True,
        undistort_alpha=0.0,
        dump_det_results=True,
    ):
        super(PrivateBevDetData, self).__init__(
            data_configs=data_configs,
            data_paths=data_paths,
            eval_configs=eval_configs,
            lidar_key_list=lidar_key_list,
            img_key_list=img_key_list,
            pipeline=pipeline,
            class_names=class_names,
            training=training,
            only_key_frame=only_key_frame,
            pc_fileds=pc_fileds,
            used_echo_id=used_echo_id,
            filter_empty_gt=filter_empty_gt,
            pre_only=pre_only,
            occlusion_filter=occlusion_filter,
            undistort=undistort,
            undistort_alpha=undistort_alpha,
        )
        self.ida_aug_conf = ida_aug_conf
        self.bda_aug_conf = bda_aug_conf
        self.classes = class_names
        self.is_train = training
        self.only_key_frame = only_key_frame
        self.img_mean = np.array(img_conf["img_mean"], np.float32)
        self.img_std = np.array(img_conf["img_std"], np.float32)
        self.to_rgb = img_conf["to_rgb"]
        self.dump_det_results = dump_det_results

        self.CATEGORY_MAPPING = {
            "car": "汽车",
            "truck": "货车",
            "construction_vehicle": "工程车",
            "bus": "巴士",
            "motorcycle": "摩托车",
            "bicycle": "自行车",
            "tricycle": "三轮车",
            "cyclist": "骑车人",
            "pedestrian": "人",
            "other": "其它",
            "ghost": "残影",
            "masked_area": "蒙版",
        }

    def _get_image(self, item, idx):
        imgs = []
        ida_mats = []
        lidar2imgs = []

        for cam in self.dataset.img_key_list:
            img = item[cam]

            for info in item["img_info"]:
                if info["channel"] == cam:
                    lidar2img = np.concatenate([np.array(info["T_lidar_to_pixel"]), np.array([[0.0, 0.0, 0.0, 1.0]])])
            assert lidar2img is not None, "Incorrect camera name in img info"
            img = Image.fromarray(img)

            resize, resize_dims, crop, flip, rotate_ida = self.sample_ida_augmentation()
            img, ida_mat = img_transform(
                img,
                resize=resize,
                resize_dims=resize_dims,
                crop=crop,
                flip=flip,
                rotate=rotate_ida,
            )
            img = mmcv.imnormalize(np.array(img), self.img_mean, self.img_std, self.to_rgb)
            img = torch.from_numpy(img).permute(2, 0, 1)
            ida_mats.append(ida_mat)
            imgs.append(img)
            lidar2imgs.append(torch.tensor(lidar2img, dtype=torch.float))

        img_metas = dict(
            box_type_3d=LiDARInstance3DBoxes,
            token=item["frame_id"],
        )
        return (
            torch.stack(imgs),
            torch.stack(lidar2imgs),
            torch.stack(ida_mats),
            img_metas,
        )

    def _get_gt(self, item):
        box = copy.deepcopy(item["gt_boxes"])
        gt_labels = torch.tensor([self.class_names.index(i) if i in self.class_names else -1 for i in item["labels"]])

        return torch.Tensor(box), gt_labels

    def __getitem__(self, idx):
        if self.only_key_frame:
            cam_idx = self.dataset.key_frame_index[idx]
        else:
            cam_idx = idx
        item = self.dataset[cam_idx]

        imgs, lidar2imgs, ida_mats, img_metas = self._get_image(item, idx)
        if self.training:
            gt_boxes, gt_labels = self._get_gt(item)
        else:
            gt_boxes = imgs.new_zeros(0, 7)
            gt_labels = imgs.new_zeros(
                0,
            )

        rotate_bda, scale_bda, flip_dx, flip_dy = self.sample_bda_augmentation()
        gt_boxes, post_rot_bda = bev_transform(gt_boxes, rotate_bda, scale_bda, flip_dx, flip_dy)

        return (
            item["frame_id"],
            imgs,
            lidar2imgs,
            ida_mats,
            post_rot_bda,
            img_metas,
            gt_boxes,
            gt_labels,
        )

    def __len__(self):
        if self.only_key_frame:
            return len(self.dataset.key_frame_index)
        else:
            return len(self.dataset.frame_data_list)

    def evaluation(self, det_annos, class_names, **kwargs):
        if self.dump_det_results:
            self.dump_inference_results(det_annos, **kwargs)
        return super(PrivateBevDetData, self).evaluation(det_annos, class_names, **kwargs)

    def dump_inference_results(self, det_annos, **kwargs):
        from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import dump_json, JsonEncoder

        def dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index):
            ori_det_data = {
                "frames": ori_det_annos,
                "calibrated_sensors": calibrated_sensors,
                "key_frame_idx": key_frame_index,
            }
            json_path = self.dataset.data_paths[current_scene]
            json_file = os.path.basename(json_path).split(".")[0]
            dump_json(
                ori_det_data,
                refile.smart_path_join(data_root, "%s_dt.json" % str(json_file)),
                encoder=JsonEncoder,
            )

        output_dir = kwargs["output_dir"]
        ori_gt_annos = self.dataset.frame_data_list
        ori_det_annos = []
        key_frame_index = []
        current_idx = 0
        current_scene = None
        calibrated_sensors = self.dataset.calibrated_sensors_dict[0]
        data_root = refile.smart_path_join(output_dir, "dump_vis")
        for i in range(len(ori_gt_annos)):
            item = copy.deepcopy(ori_gt_annos[i])
            item["labels"] = self._reverse_format(det_annos[i])
            scene = self.dataset.calibrated_sensors_id[i]
            if current_scene is None:
                current_scene = scene
            elif current_scene != scene:
                dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index)
                current_scene = scene
                ori_det_annos = []
                key_frame_index = []
                current_idx = 0
            ori_det_annos.append(item)
            if item["is_key_frame"]:
                key_frame_index.append(current_idx)
            current_idx += 1
        # dump last scene
        dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index)

    def _reverse_format(self, det_annos):
        from scipy.spatial.transform import Rotation as R

        annos = []
        for i, box_anno in enumerate(det_annos["boxes_3d"]):
            anno = {"xyz_lidar": {}, "lwh": {}, "angle_lidar": {}}
            anno["xyz_lidar"]["x"] = box_anno[0]
            anno["xyz_lidar"]["y"] = box_anno[1]
            anno["xyz_lidar"]["z"] = box_anno[2]
            anno["lwh"]["l"] = box_anno[3]
            anno["lwh"]["w"] = box_anno[4]
            anno["lwh"]["h"] = box_anno[5]

            quat = R.from_euler("xyz", (0.0, 0.0, box_anno[6])).as_quat()
            anno["angle_lidar"]["x"] = quat[0]
            anno["angle_lidar"]["y"] = quat[1]
            anno["angle_lidar"]["z"] = quat[2]
            anno["angle_lidar"]["w"] = quat[3]
            anno["score"] = det_annos["score"][i]
            anno["category"] = self.CATEGORY_MAPPING[det_annos["name"][i]]
            anno["track_id"] = -1
            annos.append(anno)
        return annos


def collate_fn(data):
    frame_id_batch = list()
    imgs_batch = list()
    lidar2imgs_batch = list()
    ida_mats_batch = list()
    bda_mat_batch = list()
    gt_boxes_batch = list()
    gt_labels_batch = list()
    img_metas_batch = list()
    for (
        frame_id,
        imgs,
        lidar2imgs,
        ida_mats,
        bda_mat,
        img_metas,
        gt_boxes,
        gt_labels,
    ) in data:
        frame_id_batch.append(frame_id)
        imgs_batch.append(imgs)
        lidar2imgs_batch.append(lidar2imgs)
        ida_mats_batch.append(ida_mats)
        bda_mat_batch.append(bda_mat)
        img_metas_batch.append(img_metas)
        gt_boxes_batch.append(gt_boxes)
        gt_labels_batch.append(gt_labels)

    mats_dict = dict()
    mats_dict["lidar2imgs"] = torch.stack(lidar2imgs_batch)
    mats_dict["ida_mats"] = torch.stack(ida_mats_batch)
    mats_dict["bda_mat"] = torch.stack(bda_mat_batch)

    data_dict = dict(
        frame_id=frame_id_batch,
        imgs=torch.stack(imgs_batch),
        mats_dict=mats_dict,
        img_metas=img_metas_batch,
        gt_boxes=gt_boxes_batch,
        gt_labels=gt_labels_batch,
    )
    return data_dict
