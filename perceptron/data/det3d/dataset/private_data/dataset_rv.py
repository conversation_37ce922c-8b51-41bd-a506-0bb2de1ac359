from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval
from data3d.datasets.private import PrivateDataset
import numpy as np
import copy
from data3d.transforms import transforms3d


class PrivateRangeDatasetWithEval(PrivateDatasetWithEval):
    def __init__(
        self,
        data_configs,
        data_paths,
        lidar_key_list=["middle_lidar"],
        img_key_list=[],
        class_names=None,
        training=True,
        only_key_frame=True,
        pc_fields=["x", "y", "z", "i", "r", "t"],
        used_echo_id=[1],
        use_rv_valid_mask=False,
        use_occluded=False,
        rv_mv_origin=[0, 0, 0],
        nr_azim=1800,
        rv_col_offset=0,
        incl_array=None,
    ):
        self.data_configs = data_configs
        self.class_names = class_names
        self.training = training

        self.rv_mv_origin = rv_mv_origin
        self.nr_azim = nr_azim
        self.rv_col_offset = rv_col_offset
        self.incl_array = incl_array

        self.dataset = PrivateDataset(
            class_names=self.class_names,
            data_paths=data_paths,
            img_key_list=img_key_list,  # ["camera_10", "camera_11", "camera_15", "camera_2", "camera_5"],
            lidar_key_list=lidar_key_list,
            only_key_frame=only_key_frame,
            pc_fields=pc_fields,
            used_echo_id=used_echo_id,
            use_occluded=use_occluded,
        )

        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 4, np.pi / 4]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
            ]
        )
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features
        self.use_rv_valid_mask = use_rv_valid_mask

    def __len__(self):
        return len(self.dataset.key_frame_index)

    def __getitem__(self, idx: int):
        idx = self.dataset.key_frame_index[idx]
        item = self.dataset[idx]

        # lidar 和 gt_boxes修正
        rv_mv_origin = np.array(self.rv_mv_origin).reshape(-1, 3)
        lidar_pc = self._point_process(item)[:, : self.data_configs.use_num_point_features]
        lidar_pc[:, :3] = lidar_pc[:, :3] - rv_mv_origin

        gt_boxes = copy.deepcopy(item["gt_boxes"])  # xyz
        gt_boxes[:, :3] = gt_boxes[:, :3] - rv_mv_origin

        data_dict = {
            "points": lidar_pc,
            "frame_id": item["frame_id"],
            "use_lead_xyz": self.use_lead_xyz,
        }

        if "labels" in item:
            data_dict.update(
                {
                    "gt_names": copy.deepcopy(item["labels"]),
                    "gt_boxes": gt_boxes,
                    "num_points_in_gt": copy.deepcopy(item["num_lidar_points"]),
                }
            )

        data_dict = self._run_aug_and_process(data_dict)

        return data_dict
