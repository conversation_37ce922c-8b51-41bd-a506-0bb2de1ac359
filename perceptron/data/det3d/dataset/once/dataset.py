import numpy as np
import copy
from collections import defaultdict
from data3d.datasets.once import OnceDataset
from data3d.transforms import transforms3d
from perceptron.utils.det3d_utils import common_utils


class OnceEvalMixin:
    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels - 1]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        from once_eval.evaluation import get_evaluation_results

        eval_det_annos = copy.deepcopy(det_annos)
        eval_gt_annos = [copy.deepcopy(info["annos"]) for info in self.dataset.infos]
        ap_result_str, ap_dict = get_evaluation_results(eval_gt_annos, eval_det_annos, class_names)

        return ap_result_str, ap_dict


class OnceDatasetWithEval(OnceEvalMixin):
    def __init__(self, data_configs, class_names=None, training=True):
        self.data_configs = data_configs
        self.class_names = class_names
        self.training = training
        self.dataset = OnceDataset(
            class_names=class_names,
            data_split="training" if training else "validation",
            root_path="s3://generalDetection/3DDatasets/HUAWEI_ONCE/",
            is_filter_empty_sample=True,
            img_key_list=[],
        )
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.GTSampling(
                    root_path="s3://generalDetection/3DDatasets/HUAWEI_ONCE/",
                    data_name="once",
                    data_split="training",
                    class_names=class_names,
                    sampler_groups=["Car:14", "Bus:5", "Truck:4", "Pedestrian:5", "Cyclist:13"],
                    num_point_feature=self.data_configs.src_num_point_features,
                    use_road_plane=False,
                    filter_by_min_points_cfg=["Car:5", "Bus:5", "Truck:5", "Pedestrian:5", "Cyclist:5"],
                    remove_extra_width=[0.0, 0.0, 0.0],
                    limit_whole_scene=True,
                ),
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 4, np.pi / 4]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features

    def __len__(self):
        return self.dataset.__len__()

    def __getitem__(self, idx: int):
        item = self.dataset[idx]
        info = copy.deepcopy(item["info"])

        data_dict = {
            "points": item["lidar_roof"][:, : self.data_configs.use_num_point_features],
            "frame_id": info["frame_id"],
            "use_lead_xyz": self.use_lead_xyz,
        }

        if "annos" in info:
            annos = info["annos"]
            data_dict.update(
                {
                    "gt_names": annos["name"],
                    "gt_boxes": annos["boxes_3d"],
                    "num_points_in_gt": annos.get("num_points_in_gt", None),
                }
            )

        if self.training:
            assert "gt_boxes" in data_dict, "gt_boxes should be provided for training"
            gt_boxes_mask = np.array([n in self.class_names for n in data_dict["gt_names"]], dtype=np.bool_)

            data_dict = self.data_augmentor.forward(data_dict={**data_dict, "gt_boxes_mask": gt_boxes_mask})
            if len(data_dict["gt_boxes"]) == 0:
                new_index = np.random.randint(self.__len__())
                return self.__getitem__(new_index)
        elif hasattr(self, "test_augmentor"):
            data_dict = self.test_augmentor.forward(data_dict=data_dict)

        if data_dict.get("gt_boxes", None) is not None:
            selected = common_utils.keep_arrays_by_name(data_dict["gt_names"], self.class_names)
            data_dict["gt_boxes"] = data_dict["gt_boxes"][selected]
            data_dict["gt_names"] = data_dict["gt_names"][selected]
            gt_classes = np.array([self.class_names.index(n) + 1 for n in data_dict["gt_names"]], dtype=np.int32)
            gt_boxes = np.concatenate((data_dict["gt_boxes"], gt_classes.reshape(-1, 1).astype(np.float32)), axis=1)
            data_dict["gt_boxes"] = gt_boxes

        data_dict = self.processor.forward(data_dict=data_dict)
        data_dict.pop("gt_names", None)
        data_dict.pop("num_points_in_gt", None)
        return data_dict

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points"]:
                    ret[key] = val
                elif key in ["gt_boxes"]:
                    max_gt = max([len(x) for x in val])
                    batch_gt_boxes3d = np.zeros((batch_size, max_gt, val[0].shape[-1]), dtype=np.float32)
                    for k in range(batch_size):
                        batch_gt_boxes3d[k, : val[k].__len__(), :] = val[k]
                    ret[key] = batch_gt_boxes3d
                else:
                    ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret
