import copy
import os
import pickle
from collections import defaultdict
from functools import partial

import nori2 as nori
import numpy as np
import pandas as pd
import refile
import torch.distributed as dist

from data3d.datasets.nuScenes import NuscenesDataset
from data3d.transforms import transforms3d
from perceptron.utils.det3d_utils import common_utils


class NuScenesEvalMixin:
    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels - 1]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        from once_eval.evaluation import get_evaluation_results

        eval_gt_annos = []
        for anno in self.dataset.infos:
            item = copy.deepcopy(anno)
            item["name"] = item["gt_names"]
            item["boxes_3d"] = item["gt_boxes"][:, :7]
            eval_gt_annos.append(item)

        eval_det_annos = copy.deepcopy(det_annos)
        iou_threshold_dict = {
            "car": 0.7,
            "truck": 0.7,
            "construction_vehicle": 0.7,
            "bus": 0.7,
            "trailer": 0.7,
            "barrier": 0.7,
            "motorcycle": 0.5,
            "bicycle": 0.5,
            "pedestrian": 0.3,
            "traffic_cone": 0.3,
        }
        ap_result_str, ap_dict = get_evaluation_results(
            eval_gt_annos,
            eval_det_annos,
            class_names,
            use_superclass=False,
            iou_thresholds=iou_threshold_dict,
            ap_with_heading=False,
        )
        return ap_result_str, ap_dict


class NuScenesDatasetWithEval(NuScenesEvalMixin):
    def __init__(self, data_configs, class_names=None, training=True):
        self.data_configs = data_configs
        self.class_names = class_names
        self.training = training
        self.dataset = NuscenesDataset(
            class_names=class_names,
            data_split="training" if training else "validation",
            root_path="s3://generalDetection/3DDatasets/nuScenes/",
            img_key_list=[],
        )
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 4, np.pi / 4]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features

        if not training and dist.get_rank() == 0:
            nuscenes_meta_path = "s3://yzy-share/nuscenes_tmp/nuscenes_v1.0-trainval_meta.pkl"
            if refile.is_s3(nuscenes_meta_path):
                meta_file = "./tmp/{}".format(os.path.basename(nuscenes_meta_path))
                if not os.path.exists(meta_file):
                    refile.s3_download(nuscenes_meta_path, meta_file)
            else:
                meta_file = nuscenes_meta_path
            # dist.barrier()
            from perceptron.data.det3d.dataset.nuscenes.eval_utils.eval_utils import load_pkl

            self.meta_info = load_pkl(meta_file)

    def __len__(self):
        return self.dataset.__len__()

    def __getitem__(self, idx: int):
        item = self.dataset[idx]
        info = copy.deepcopy(item["info"])

        data_dict = {
            "points": item["LIDAR_TOP"][:, : self.data_configs.use_num_point_features],
            "frame_id": -1,  # info["frame_id"],
            "use_lead_xyz": self.use_lead_xyz,
        }

        # dict_keys(['lidar_path', 'cam_front_path', 'cam_intrinsic', 'token', 'sweeps', 'ref_from_car', 'car_from_global', 'timestamp', 'gt_boxes', 'gt_boxes_velocity', 'gt_names', 'gt_boxes_token', 'num_lidar_pts', 'num_radar_pts'])
        data_dict.update(
            {
                "gt_names": info["gt_names"],
                "gt_boxes": info["gt_boxes"],
                "num_points_in_gt": info.get("num_points_in_gt", None),
            }
        )

        if self.training:
            assert "gt_boxes" in data_dict, "gt_boxes should be provided for training"
            gt_boxes_mask = np.array([n in self.class_names for n in data_dict["gt_names"]], dtype=np.bool_)

            data_dict = self.data_augmentor.forward(data_dict={**data_dict, "gt_boxes_mask": gt_boxes_mask})
            if len(data_dict["gt_boxes"]) == 0:
                new_index = np.random.randint(self.__len__())
                return self.__getitem__(new_index)
        elif hasattr(self, "test_augmentor"):
            data_dict = self.test_augmentor.forward(data_dict=data_dict)

        if data_dict.get("gt_boxes", None) is not None:
            selected = common_utils.keep_arrays_by_name(data_dict["gt_names"], self.class_names)
            data_dict["gt_boxes"] = data_dict["gt_boxes"][selected]
            data_dict["gt_names"] = data_dict["gt_names"][selected]
            gt_classes = np.array([self.class_names.index(n) + 1 for n in data_dict["gt_names"]], dtype=np.int32)
            gt_boxes = np.concatenate((data_dict["gt_boxes"], gt_classes.reshape(-1, 1).astype(np.float32)), axis=1)
            data_dict["gt_boxes"] = gt_boxes

        data_dict = self.processor.forward(data_dict=data_dict)
        data_dict.pop("gt_names", None)
        data_dict.pop("num_points_in_gt", None)
        return data_dict

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points"]:
                    ret[key] = val
                elif key in ["gt_boxes"]:
                    max_gt = max([len(x) for x in val])
                    batch_gt_boxes3d = np.zeros((batch_size, max_gt, val[0].shape[-1]), dtype=np.float32)
                    for k in range(batch_size):
                        batch_gt_boxes3d[k, : val[k].__len__(), :] = val[k]
                    ret[key] = batch_gt_boxes3d
                else:
                    ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret

    def evaluation(self, det_annos, class_names, **kwargs):
        from perceptron.data.det3d.dataset.nuscenes.eval_utils.evaluation import (
            generate_submission_results,
            get_evaluation_results,
        )

        output_dir = os.path.join(str(kwargs["output_dir"]), "nuscenes")
        generate_submission_results(
            meta_info=self.meta_info,
            gt=self.dataset.infos,
            dt=det_annos,
            result_dir=output_dir,
            meta_type_list=["use_lidar"],
        )

        ap_dict = get_evaluation_results(
            nusc_meta_info=self.meta_info,
            result_path=os.path.join(output_dir, "nuscenes_results.json"),
            output_dir=output_dir,
            eval_set="val",
            verbose=False,
            plot_examples=0,
            render_curves=False,
        )
        ap_result_str = None
        return ap_result_str, ap_dict


class NuScenesMonoDataset(NuscenesDataset):
    def __init__(
        self,
        class_names,
        data_split="training",
        logger=None,
        root_path=None,
        is_nori_read=True,
        transforms=None,
        img_key_list=["CAM_BACK", "CAM_BACK_LEFT", "CAM_BACK_RIGHT", "CAM_FRONT", "CAM_FRONT_LEFT", "CAM_FRONT_RIGHT"],
        lidar_key_list=["LIDAR_TOP"],
    ):
        assert set(img_key_list).issubset(
            ["CAM_BACK", "CAM_BACK_LEFT", "CAM_BACK_RIGHT", "CAM_FRONT", "CAM_FRONT_LEFT", "CAM_FRONT_RIGHT"]
        ), "Illegal image keys"
        assert set(lidar_key_list).issubset(["LIDAR_TOP"]), "Illegal lidar keys"

        super(NuscenesDataset, self).__init__(
            class_names=class_names,
            data_split=data_split,
            logger=logger,
            root_path=root_path,
            is_nori_read=is_nori_read,
            transforms=transforms,
        )
        self.img_key_list = img_key_list
        self.lidar_key_list = lidar_key_list

        if is_nori_read:
            from data3d.datasets.data_path_manager import NoriListPathManager

            nori_list_path = NoriListPathManager.get_nori_list_path_by_name("nuScenes")[self.data_split]
            self.nori_list = self._smart_load(
                nori_list_path, open_func=partial(refile.smart_open, mode="r"), load_func=partial(pd.read_csv, sep="\t")
            )
            self.nori_fetcher = None

        if data_split == "training":
            info_file = (
                "s3://yzy-share/nuscenes_tmp/nuscenes_infos_train_with_transform_matrix_and_lidar_original_info.pkl"
            )
        elif data_split == "validation":
            info_file = (
                "s3://yzy-share/nuscenes_tmp/nuscenes_infos_val_with_transform_matrix_and_lidar_original_info.pkl"
            )
        else:
            raise ValueError
        self.infos = self._smart_load(info_file, open_func=partial(refile.smart_open, mode="rb"), load_func=pickle.load)

        assert len(self.infos) == len(self.nori_list)

    def _smart_load(self, file, open_func, load_func):
        if refile.is_s3(file):
            local_file = "./tmp/{}".format(os.path.basename(file))
            if not os.path.exists(local_file):
                refile.s3_download(file, local_file)
        else:
            local_file = file
        return load_func(open_func(local_file))

    def __getitem__(self, idx: int):
        """
        loading data-of-all-sensors with given index

        Args:
            idx: int, Sampled index
        Returns:
            dict
        """

        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        item = {}
        item.update(self._get_images(idx, keys=self.img_key_list))
        if self.data_split in ("training", "validation"):
            item["info"] = self.infos[idx]
        return item


class NuScenesMonoEvalMixin:
    """Refer to: mmdet3d.datasets.nuscenes_dataset.NuscenesDataset"""

    def evaluation(self, det_annos, class_names, **kwargs):
        from nuscenes_eval.evaluation import get_evaluation_results

        self.CLASSES = class_names
        # select specific key for formating results
        data_infos = []
        for info in self.dataset.infos:
            ele = {"token": copy.deepcopy(info["token"])}
            ele.update(copy.deepcopy(info["sensor_transform_matrix"]["LIDAR_TOP"]))
            data_infos.append(ele)
        self.data_infos = data_infos

        output_dir = os.path.join(str(kwargs["output_dir"]), "nuscenes")
        # format resutls
        _, __ = self.format_results(det_annos, output_dir)
        # evaluation
        ap_dict = get_evaluation_results(
            nusc_meta_info=self.meta_info,
            result_path=os.path.join(output_dir, "nuscenes_results.json"),
            output_dir=output_dir,
            eval_set="val",
            verbose=False,
            plot_examples=0,
            render_curves=False,
        )
        ap_result_str = None
        return ap_result_str, ap_dict

    def format_results(self, results, jsonfile_prefix=None):
        """Format the results to json (standard format for COCO evaluation).

        Args:
            results (list[dict]): Testing results of the dataset.
            jsonfile_prefix (str | None): The prefix of json files. It includes
                the file path and the prefix of filename, e.g., "a/b/prefix".
                If not specified, a temp file will be created. Default: None.

        Returns:
            tuple: Returns (result_files, tmp_dir), where `result_files` is a \
                dict containing the json filepaths, `tmp_dir` is the temporal \
                directory created for saving json files when \
                `jsonfile_prefix` is not specified.
        """
        assert isinstance(results, list), "results must be a list"
        assert len(results) == len(self), "The length of results is not equal to the dataset len: {} != {}".format(
            len(results), len(self)
        )

        if jsonfile_prefix is None:
            import tempfile

            tmp_dir = tempfile.TemporaryDirectory()
            jsonfile_prefix = os.path.join(tmp_dir.name, "results")
        else:
            tmp_dir = None

        # currently the output prediction results could be in two formats
        # 1. list of dict('boxes_3d': ..., 'scores_3d': ..., 'labels_3d': ...)
        # 2. list of dict('pts_bbox' or 'img_bbox':
        #     dict('boxes_3d': ..., 'scores_3d': ..., 'labels_3d': ...))
        # this is a workaround to enable evaluation of both formats on nuScenes
        # refer to https://github.com/open-mmlab/mmdetection3d/issues/449
        if not ("pts_bbox" in results[0] or "img_bbox" in results[0]):
            result_files = self._format_bbox(results, jsonfile_prefix)
        else:
            # should take the inner dict out of 'pts_bbox' or 'img_bbox' dict
            result_files = dict()
            for name in results[0]:
                print(f"\nFormating bboxes of {name}")
                results_ = [out[name] for out in results]
                tmp_file_ = os.path.join(jsonfile_prefix, name)
                result_files.update({name: self._format_bbox(results_, tmp_file_)})
        return result_files, tmp_dir

    def _format_bbox(self, results, jsonfile_prefix=None):
        """Convert the results to the standard format.

        Args:
            results (list[dict]): Testing results of the dataset.
            jsonfile_prefix (str): The prefix of the output jsonfile.
                You can specify the output directory/filename by
                modifying the jsonfile_prefix. Default: None.

        Returns:
            str: Path of the output json file.
        """
        import mmcv
        from mmdet3d.datasets.nuscenes_dataset import lidar_nusc_box_to_global

        nusc_annos = {}
        mapped_class_names = self.CLASSES

        print("Start to convert detection format...")
        for sample_id, det in enumerate(mmcv.track_iter_progress(results)):
            annos = []

            # NOTE: mmdet3d会把rot转化为左手系，data3d不会
            boxes = self._output_to_nusc_box(det, with_dir_offsets=False)

            sample_token = self.data_infos[sample_id]["token"]
            boxes = lidar_nusc_box_to_global(
                self.data_infos[sample_id], boxes, mapped_class_names, self.eval_detection_configs, self.eval_version
            )
            for i, box in enumerate(boxes):
                name = mapped_class_names[box.label]
                if np.sqrt(box.velocity[0] ** 2 + box.velocity[1] ** 2) > 0.2:
                    if name in [
                        "car",
                        "construction_vehicle",
                        "bus",
                        "truck",
                        "trailer",
                    ]:
                        attr = "vehicle.moving"
                    elif name in ["bicycle", "motorcycle"]:
                        attr = "cycle.with_rider"
                    else:
                        attr = self.DefaultAttribute[name]
                else:
                    if name in ["pedestrian"]:
                        attr = "pedestrian.standing"
                    elif name in ["bus"]:
                        attr = "vehicle.stopped"
                    else:
                        attr = self.DefaultAttribute[name]

                nusc_anno = dict(
                    sample_token=sample_token,
                    translation=box.center.tolist(),
                    size=box.wlh.tolist(),
                    rotation=box.orientation.elements.tolist(),
                    velocity=box.velocity[:2].tolist(),
                    detection_name=name,
                    detection_score=box.score,
                    attribute_name=attr,
                )
                annos.append(nusc_anno)
            nusc_annos[sample_token] = annos
        nusc_submissions = {
            "meta": self.modality,
            "results": nusc_annos,
        }

        jsonfile_prefix = jsonfile_prefix.replace("pts_bbox", "")
        mmcv.mkdir_or_exist(jsonfile_prefix)
        res_path = os.path.join(jsonfile_prefix, "nuscenes_results.json")
        print("Results writes to", res_path)
        mmcv.dump(nusc_submissions, res_path)
        return res_path

    def _output_to_nusc_box(self, detection, with_dir_offsets=False):
        """Convert the output to the box class in the nuScenes.

        Args:
            detection (dict): Detection results.

                - boxes_3d (:obj:`BaseInstance3DBoxes`): Detection bbox.
                - scores_3d (torch.Tensor): Detection scores.
                - labels_3d (torch.Tensor): Predicted box labels.

        Returns:
            list[:obj:`NuScenesBox`]: List of standard NuScenesBoxes.
        """
        import pyquaternion
        from nuscenes.utils.data_classes import Box as NuScenesBox

        box3d = detection["boxes_3d"]
        scores = detection["scores_3d"].numpy()
        labels = detection["labels_3d"].numpy()

        box_gravity_center = box3d.gravity_center.numpy()
        box_dims = box3d.dims.numpy()
        box_yaw = box3d.yaw.numpy()
        # TODO: check whether this is necessary
        # with dir_offset & dir_limit in the head
        if with_dir_offsets:
            box_yaw = -box_yaw - np.pi / 2

        box_list = []
        for i in range(len(box3d)):
            quat = pyquaternion.Quaternion(axis=[0, 0, 1], radians=box_yaw[i])
            velocity = (*box3d.tensor[i, 7:9], 0.0)
            # velo_val = np.linalg.norm(box3d[i, 7:9])
            # velo_ori = box3d[i, 6]
            # velocity = (
            # velo_val * np.cos(velo_ori), velo_val * np.sin(velo_ori), 0.0)
            box = NuScenesBox(
                box_gravity_center[i], box_dims[i], quat, label=labels[i], score=scores[i], velocity=velocity
            )
            box_list.append(box)
        return box_list
