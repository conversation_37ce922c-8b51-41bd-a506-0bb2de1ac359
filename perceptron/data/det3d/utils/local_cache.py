import os
import refile
import hashlib
import msgpack
import msgpack_numpy
from tqdm import tqdm
from multiprocessing import Pool
from perceptron.utils.file_io import smart_load_json
from .redis_cache_stepmind import OSSEtagHelper
from loguru import logger


msgpack_numpy.patch()
oss_etag_helper = OSSEtagHelper(check_etag=False)


def recursive_dict_sort(d):
    if not isinstance(d, dict):
        return d

    sorted_dict = {}
    for key in sorted(d.keys()):
        value = d[key]
        if isinstance(value, dict):
            sorted_dict[key] = recursive_dict_sort(value)
        else:
            sorted_dict[key] = value

    return sorted_dict


def hash_dict(d):
    d = recursive_dict_sort(d)
    return hashlib.sha256(msgpack.dumps(d)).hexdigest()


def get_size(path):
    # 存在redis cache内容和gpfs文件不一致的情况，因此统一使用acceldata.open，避免冲突

    data = smart_load_json(path)

    idx = [i for i, x in enumerate(data["frames"]) if x["is_key_frame"]]
    return {
        "num_frames": len(data["frames"]),
        "num_key_frames": len(idx),
        "calib": data["calibrated_sensors"],
        "md5": refile.smart_getmd5(path),
    }


def create_cache(paths):
    with Pool(processes=30) as pool:
        results = list(tqdm(pool.imap(get_size, paths), total=len(paths), desc="Processing paths", unit="file"))
    return dict(zip(paths, results))


class LocalCache:
    def __init__(self, local_path="/mnt/local0", key=None, init=True, rebuild=False):
        self.rebuild = rebuild
        # 默认路径使用juicefs挂载盘 /local/cache/ 下的key目录，会检查cache目录是否有文件
        # 建立新cache时需传入init=True，表示cache目录为空时仍然使用juicefs挂载盘，或手动创建目录并在目录中写入一个文件
        if init:
            self.local_path = f"/mnt/cache/{key}"
            os.makedirs(self.local_path, exist_ok=True)
        elif refile.smart_exists(f"/mnt/cache/{key}") and refile.smart_listdir(f"/mnt/cache/{key}"):
            self.local_path = f"/mnt/cache/{key}"
        else:
            self.local_path = local_path
            logger.warning(f"cache file use {self.local_path} instead of juicefs, please check.")
        os.makedirs(self.local_path, exist_ok=True)

    def __getitem__(self, key):
        with open(os.path.join(self.local_path, key), "rb") as f:
            data = f.read()
        return msgpack.loads(data, raw=False)

    def __setitem__(self, key, value):
        encoded_v = msgpack.dumps(value)
        with open(os.path.join(self.local_path, key), "wb") as f:
            f.write(encoded_v)

    def __contains__(self, key):
        if os.path.exists(os.path.join(self.local_path, key)):
            if self.rebuild:
                try:
                    with open(os.path.join(self.local_path, key), "rb") as f:
                        data = f.read()
                    _ = msgpack.loads(data, raw=False)
                    return True
                except Exception:
                    os.remove(os.path.join(self.local_path, key))
                    return False
            else:
                return True
        else:
            return False

    def __delitem__(self, key):
        try:
            os.remove(os.path.join(self.local_path, key))
        except FileNotFoundError:
            pass


class LocalCachedDict(LocalCache):
    def __setitem__(self, key, value):
        if isinstance(value, dict):
            for k, v in value.items():
                new_key = f"{key}.{k}"
                encoded_v = msgpack.dumps(v)
                with open(os.path.join(self.local_path, new_key), "wb") as f:
                    f.write(encoded_v)
        encoded_v = msgpack.dumps(value)
        with open(os.path.join(self.local_path, key), "wb") as f:
            f.write(encoded_v)


class LocalCacheCalib(LocalCache):
    def __init__(self, local_path="/mnt/local0", key=None, init=False, rebuild=False):
        super().__init__(local_path, key, init)
        self.calib_cache = LocalCache("/data/cache/calib", key="calib", rebuild=rebuild)

    def __setitem__(self, key, value):
        # value is dict(zip(paths, results))
        for path, result in value.items():
            result["calib"].pop("odom_data", None)
            calib_key = hash_dict(result["calib"])
            if calib_key not in self.calib_cache:
                self.calib_cache[calib_key] = result["calib"]
            result["calib"] = calib_key
        encoded_v = msgpack.dumps(value)
        with open(os.path.join(self.local_path, key), "wb") as f:
            f.write(encoded_v)
