# Replace OCC_Z10_10W_BSL with any data list to create v3 loader cache with cpu workers
# rlaunch -P 10 --cpu=30 --memory=100000 -n mach-generator --group=generator_gpu --mount=gpfs://gpfs1/acceldata:/mnt/acceldata --mount=gpfs://gpfs2/perceptron-cache:/mnt/cache --mount=gpfs://gpfs2/dynamic2:/mnt/dynamic2 --max-wait-duration=20m --preemptible=yes -- python3 perceptron/data/det3d/utils/build_cache_for_sample_data.py

import os

from perceptron.utils.file_io import smart_load_json
from perceptron.data.det3d.source.z10 import TRAINSET_PARTIAL
from perceptron.data.det3d.utils.local_cache import LocalCacheCalib, create_cache, hash_dict
from perceptron.exps.end2end.private.data_name.release_10w_bsl_data_name import OCC_Z10_10W_BSL


def _load_primary_paths(dataset_name):

    json_path = TRAINSET_PARTIAL[dataset_name]

    if isinstance(json_path, str):
        json_data = smart_load_json(json_path)
        if isinstance(json_data, list):
            json_paths = json_data
        elif isinstance(json_data, dict) and "paths" not in json_data.keys():
            json_paths = []
            for key in json_data.keys():
                json_paths.append(key)
        else:
            json_paths = json_data["paths"]
    elif isinstance(json_path, list):
        json_paths = json_path
    else:
        raise TypeError("Only json file (str type) and json list (list type) are supported!")

    assert len(json_paths) > 0, f"There should be more than one json in {json_path}"
    return json_paths


if __name__ == "__main__":
    idx = int(os.environ.get("RLAUNCH_REPLICA", 0))
    total = int(os.environ.get("RLAUNCH_REPLICA_TOTAL", 1))
    for name in OCC_Z10_10W_BSL[idx::total]:
        path_sizes_cache = LocalCacheCalib("/data/cache/loader_v3", key="loader_cache_v3", init=True)
        primary_paths = _load_primary_paths(name)

        local_key = hash_dict(primary_paths)
        if local_key not in path_sizes_cache:
            path_sizes_cache[local_key] = create_cache(primary_paths)
