import concurrent.futures
import functools
import itertools
import os
import time
import json

import msgpack
import msgpack_numpy
import refile
from loguru import logger


from redis.cluster import RedisCluster, ClusterNode

msgpack_numpy.patch()


cluster_ip_list = [
    "************",
    "************",
    "************",
    "************",
    "************",
    "************",
    "************",
    "************",
    "************",
    "************",
]
REDIS_CLUSTER = RedisCluster(
    startup_nodes=[ClusterNode(host=ip, port=6479) for ip in cluster_ip_list],
    decode_responses=False,
    password="As123456",
)


REDIS_COLLECTION = {
    "https://qy.machdrive.cn": "redis://************:6666",
    "https://hh-d.brainpp.cn": "redis://mcd-mc-redis-web-main-d-r-p.mcd-mc:6666/0",
}
default_url = REDIS_COLLECTION[os.environ.get("KUBEBRAIN_CLUSTER_ENTRY")]
_REDIS_URL = os.environ.get(
    "REDIS_URL",
    default_url,
)
# _REDIS_CONNECTION_POOL = redis.ConnectionPool.from_url(_REDIS_URL)


def retry(max_retry_times=10, retry_interval=10):
    def decorator(func):
        @functools.wraps(func)
        def wrap(*args, **kwargs):
            retry = 0
            while True:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.warning("retry#{} exception:\n{}".format(retry, e))
                    logger.warning(args)
                    if hasattr(args[0], "prefix"):
                        logger.warning(args[0].prefix)
                    retry += 1
                    if retry > max_retry_times:
                        raise e
                logger.info("retry {}/{} after {} seconds".format(retry, max_retry_times, retry_interval))
                time.sleep(retry_interval)

        return wrap

    return decorator


class RedisCachedIterator:

    _data_prefix = "d3dc"
    _ttl = 60 * 60 * 24 * 7  # 7 days

    def __init__(self, prefix, redis_url=None, **kwargs):
        self.prefix = ".".join([self._data_prefix, prefix])
        self._last_check_time = 0
        self.redis_url = redis_url

    @functools.lru_cache(maxsize=1)
    def get_client(self):
        return REDIS_CLUSTER
        # return redis.Redis(connection_pool=_REDIS_CONNECTION_POOL)

    @property
    def _client(self):
        return self.get_client()

    @property
    def ttl(self):
        return self._client.ttl(self.prefix)

    def _update_ttl(self):
        now = time.time()
        if self._ttl and (now - self._last_check_time > self._ttl * 0.05):
            self._last_check_time = now
            current_ttl = self._client.ttl(self.prefix)
            current_ttl = current_ttl if current_ttl else 0
            # only update expire when current_ttl is less than 0.5 * target_ttl
            # in order to reduce the load of matser redis server
            result = current_ttl < self._ttl * 0.5
            if result:
                logger.debug("[TTL] prefix: {} update ttl {} -> {}".format(self.prefix, current_ttl, self._ttl))
                self._client.expire(self.prefix, self._ttl)

    def _set(self, key, val):
        self._client.hset(self.prefix, str(key), msgpack.dumps(val))

    @functools.lru_cache(maxsize=20)
    @retry()
    def _get(self, key):
        self._update_ttl()
        return msgpack.loads(self._client.hget(self.prefix, str(key)), raw=False)

    def __len__(self):
        return self._get("__len")

    def __getitem__(self, idx):
        if isinstance(idx, slice):
            items = []
            for i in itertools.islice(range(len(self)), idx.start, idx.stop, idx.step):
                items.append(self._get(i))
            return items
        else:
            return self._get(idx)

    def __iter__(self):
        for i in range(len(self)):
            yield self.__getitem__(i)

    def exist(self):
        return self._client.hexists(self.prefix, "__len")

    def _insert_iterable(self, iterable, chunk_size=1000, **kwargs):
        pipe = self._client.pipeline()
        total = 0
        for idx, item in enumerate(iterable):
            pipe.hset(self.prefix, idx, msgpack.dumps(item))
            total += 1
            if idx % chunk_size == 0:
                pipe.expire(self.prefix, self._ttl)
                pipe.execute()
                pipe = self._client.pipeline()
        pipe.hset(self.prefix, "__len", msgpack.dumps(total))
        pipe.expire(self.prefix, self._ttl)
        pipe.execute()


class OSSEtagHelper(RedisCachedIterator):
    _ttl = None

    def __init__(self, check_etag=True):
        super().__init__("oss_etag")
        self.check_etag = check_etag
        if self.check_etag:
            self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=10)
            self.futures = []

    def _check_etag(self, path, etag):
        file_info = refile.smart_stat(path).extra
        if isinstance(file_info, dict):
            expected_etag = refile.smart_stat(path).extra["ETag"][1:-1]
        else:
            expected_etag = path + str(refile.smart_stat(path).extra.st_mtime)
        if expected_etag != etag:
            self._set(path, expected_etag)
            raise RuntimeError(f"ETag mismatch! {path} has been changed, please reload")

    def get_etag(self, path):
        if self._client.hexists(self.prefix, path):
            etag = self._get(path)
        else:
            if refile.smart_exists(path):
                file_info = refile.smart_stat(path).extra
            elif path.startswith("/mnt/acceldata/dynamic/"):
                file_info = refile.smart_stat(path.replace("/mnt/acceldata/dynamic/", "s3://")).extra
            elif path.startswith("/mnt/dynamic2/"):
                file_info = refile.smart_stat(path.replace("/mnt/dynamic2/", "s3://")).extra
            elif path.startswith("/mnt/acceldata/static/"):
                file_info = refile.smart_stat(path.replace("/mnt/acceldata/static/", "s3://")).extra
            else:
                raise FileNotFoundError(f"File {path} not found")
            if isinstance(file_info, dict):
                etag = file_info["ETag"][1:-1]
            else:
                etag = path + str(file_info.st_mtime)

            self._set(path, etag)
        if self.check_etag:
            future = self.executor.submit(self._check_etag, path, etag)
            self.futures.append(future)
        return etag

    def join(self):
        if not self.check_etag:
            return
        for f in self.futures:
            f.result()


class RedisCachedData:
    def __init__(self, path, oss_etag_helper, rebuild=False, **kwargs):
        self.path = path
        self.cache = RedisCachedIterator(oss_etag_helper.get_etag(self.path), **kwargs)
        build_cache = False
        if not self.cache.exist():
            for prefix in ["/mnt/acceldata/dynamic/", "/mnt/dynamic2/", "/mnt/acceldata/static/"]:
                if self.path.startswith(prefix):
                    osspath = self.path.replace(prefix, "s3://")
                    self.cache = RedisCachedIterator(oss_etag_helper.get_etag(osspath), **kwargs)
                    if not self.cache.exist():
                        logger.info("cannot find cache {}, building...".format(path))
                        build_cache = True
                    break
            else:
                logger.info("cannot find cache {}, building...".format(path))
                build_cache = True
        if rebuild or build_cache:
            logger.info("cache key: {}".format(self.cache.prefix))
            self._init_cache()
        self.meta = self.cache._get("meta")

    def _init_cache(self):
        try:
            with refile.smart_open(self.path, "r") as rf:
                json_data = json.load(rf)
        except Exception as e:
            logger.error(f"Failed to load JSON data from {self.path}: {e}")
            if self.path.startswith("/mnt/dynamic2/"):
                with refile.smart_open(self.path.replace("/mnt/dynamic2/", "s3://"), "r") as rf:
                    json_data = json.load(rf)
            elif self.path.startswith("/mnt/static2/"):
                with refile.smart_open(self.path.replace("/mnt/static2/", "s3://"), "r") as rf:
                    json_data = json.load(rf)
            elif self.path.startswith("/mnt/acceldata/static/"):
                with refile.smart_open(self.path.replace("/mnt/acceldata/static/", "s3://"), "r") as rf:
                    json_data = json.load(rf)
            else:
                raise e

        frames = json_data.pop("frames")
        json_data["key_frame_idx"] = [i for i, x in enumerate(frames) if x["is_key_frame"]]
        self.cache._set("meta", json_data)
        self.cache._insert_iterable(frames)

    def get(self, key, default=None):
        if key == "frames":
            return self.cache
        else:
            return self.meta.get(key, default)

    def __getitem__(self, key):
        if key == "frames":
            return self.cache
        else:
            return self.meta[key]


class RedisCachedDict:
    _data_prefix = "b1bc"
    # _data_prefix = str(os.getenv("REDIS_DATA_PREFIX", None))
    _ttl = 60 * 60 * 24 * 7

    def __init__(self, prefix, redis_url=None, ttl=None, use_local_cache=True):
        self.prefix = ".".join([self._data_prefix, prefix])
        self._last_check_time = 0
        self.redis_url = _REDIS_URL if redis_url is None else redis_url
        self._ttl = ttl if ttl is not None else self._ttl
        self.use_local_cache = use_local_cache
        self._local_cache = {}

    @functools.lru_cache(maxsize=1)
    def get_client(self):
        return REDIS_CLUSTER

    @property
    def _client(self):
        return self.get_client()

    def _update_ttl(self):
        now = time.time()
        if self._ttl and (now - self._last_check_time > self._ttl * 0.05):
            self._last_check_time = now
            current_ttl = self._client.ttl(self.prefix)
            current_ttl = current_ttl if current_ttl else 0
            if current_ttl < self._ttl * 0.5:
                self._client.expire(self.prefix, self._ttl)

    def __getitem__(self, key):
        if self.use_local_cache and key in self._local_cache:
            return self._local_cache[key]
        self._update_ttl()
        data = self._client.hget(self.prefix, str(key))
        if data is None:
            raise KeyError(f"{key} not found in RedisCachedDict({self.prefix})")
        value = msgpack.loads(data, raw=False)
        if self.use_local_cache:
            self._local_cache[key] = value

    def __setitem__(self, key, value):
        if isinstance(value, dict):
            with self._client.pipeline() as pipe:
                for k, v in value.items():
                    new_key = f"{key}.{k}"
                    encoded_v = msgpack.dumps(v)
                    pipe.hset(self.prefix, new_key, encoded_v)
                    if self.use_local_cache:
                        self._local_cache[new_key] = v
                pipe.execute()
        else:
            encoded_v = msgpack.dumps(value)
            self._client.hset(self.prefix, str(key), msgpack.dumps(value))
            if self.use_local_cache:
                self._local_cache[key] = value

    def __contains__(self, key):
        if self.use_local_cache and key in self._local_cache:
            return True
        return self._client.hexists(self.prefix, str(key))


class LocalCachedDict:
    def __init__(self, local_path="/mnt/local0"):
        self.local_path = local_path
        os.makedirs(self.local_path, exist_ok=True)

    def __getitem__(self, key):
        with open(os.path.join(self.local_path, key), "rb") as f:
            data = f.read()
        return msgpack.loads(data, raw=False)

    def __setitem__(self, key, value):
        if isinstance(value, dict):
            for k, v in value.items():
                new_key = f"{key}.{k}"
                encoded_v = msgpack.dumps(v)
                with open(os.path.join(self.local_path, new_key), "wb") as f:
                    f.write(encoded_v)
        encoded_v = msgpack.dumps(value)
        with open(os.path.join(self.local_path, key), "wb") as f:
            f.write(encoded_v)

    def __contains__(self, key):
        return os.path.exists(os.path.join(self.local_path, key))
