import os

import numpy as np
import refile
import pickle
from .base_dataset import BaseDataset


class NuScenesDataset(BaseDataset):
    def __init__(
        self,
        loader=None,
        pipeline=None,
        class_names=None,
        use_cbgs=False,
        filter_empty=True,
        mode="train",
    ):
        super().__init__(
            loader=loader,
            pipeline=pipeline,
            class_names=class_names,
            use_cbgs=use_cbgs,
            filter_empty=filter_empty,
            mode=mode,
        )

        if not self.is_train:
            nuscenes_meta_path = "s3://e2emodel-data/nuScenes_fusion/nuscenes_v1.0-trainval_meta.pkl"
            with refile.smart_open(nuscenes_meta_path, "rb") as f:
                meta_info = pickle.load(f)
            self.meta_info = meta_info

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = -1  # Currently set up to -1 as CenterPoint
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        from perceptron.data.det3d.dataset.nuscenes.eval_utils.evaluation import (
            generate_submission_results,
            get_evaluation_results,
        )

        output_dir = os.path.join(str(kwargs["output_dir"]), "nuscenes")
        for frame in self.infos:
            frame["token"] = frame["sample_token"]
        generate_submission_results(
            meta_info=self.meta_info,
            gt=self.infos,
            dt=det_annos,
            result_dir=output_dir,
            meta_type_list=["use_camera", "use_lidar"],
        )

        metrics_summary = get_evaluation_results(
            nusc_meta_info=self.meta_info,
            result_path=os.path.join(output_dir, "nuscenes_results.json"),
            output_dir=output_dir,
            eval_set="val",
            verbose=False,
            plot_examples=0,
            render_curves=False,
        )
        ap_dict = metrics_summary
        # Print high-level metrics.
        ap_str = ""
        ap_str += "mAP: %.4f\n" % (metrics_summary["mean_ap"])
        err_name_mapping = {
            "trans_err": "mATE",
            "scale_err": "mASE",
            "orient_err": "mAOE",
            "vel_err": "mAVE",
            "attr_err": "mAAE",
        }
        for tp_name, tp_val in metrics_summary["tp_errors"].items():
            ap_str += "%s: %.4f\n" % (err_name_mapping[tp_name], tp_val)
        ap_str += "NDS: %.4f\n" % (metrics_summary["nd_score"])
        ap_str += "Eval time: %.1fs\n" % metrics_summary["eval_time"]

        # Print per-class metrics.

        ap_str += "\nPer-class results:\n"
        ap_str += "{:<24}\tAP\tATE\tASE\tAOE\tAVE\tAAE\n".format("Object Class")
        class_aps = metrics_summary["mean_dist_aps"]
        class_tps = metrics_summary["label_tp_errors"]
        for class_name in class_aps.keys():
            ap_str += "{:<24}\t{:.4f}\t{:.4f}\t{:.4f}\t{:.4f}\t{:.4f}\t{:.4f}\n".format(
                class_name,
                class_aps[class_name],
                class_tps[class_name]["trans_err"],
                class_tps[class_name]["scale_err"],
                class_tps[class_name]["orient_err"],
                class_tps[class_name]["vel_err"],
                class_tps[class_name]["attr_err"],
            )

        return ap_str, ap_dict
