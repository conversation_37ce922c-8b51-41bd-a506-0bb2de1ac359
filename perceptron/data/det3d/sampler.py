import itertools
from typing import Optional, Sequence

import numpy as np
import torch
from loguru import logger
from torch import distributed as dist
from torch.utils.data.sampler import Sampler


class InfiniteSampler(Sampler):
    """
    In training, we only care about the "infinite stream" of training data.
    So this sampler produces an infinite stream of indices and
    all workers cooperate to correctly shuffle the indices and sample different indices.
    The samplers in each worker effectively produces `indices[worker_id::num_workers]`
    where `indices` is an infinite stream of indices consisting of
    `shuffle(range(size)) + shuffle(range(size)) + ...` (if shuffle is True)
    or `range(size) + range(size) + ...` (if shuffle is False)
    """

    def __init__(self, size: int, shuffle: bool = True, seed: Optional[int] = 0, rank=0, world_size=1, drop_last=False):
        """
        Args:
            size (int): the total number of data of the underlying dataset to sample from
            shuffle (bool): whether to shuffle the indices or not
            seed (int): the initial seed of the shuffle. Must be the same
                across all workers. If None, will use a random seed shared
                among workers (require synchronization among all workers).
        """
        self._size = size
        assert size > 0
        self._shuffle = shuffle
        self._seed = int(seed)
        self.drop_last = drop_last

        if dist.is_available() and dist.is_initialized():
            self._rank = dist.get_rank()
            self._world_size = dist.get_world_size()
        else:
            self._rank = rank
            self._world_size = world_size

    def set_epoch(self, epoch):
        pass

    def __iter__(self):
        start = self._rank
        yield from itertools.islice(self._infinite_indices(), start, None, self._world_size)

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                yield from torch.randperm(self._size, generator=g).tolist()
            else:
                # yield from torch.arange(self._size)
                yield from list(range(self._size))

    def __len__(self):
        if self.drop_last:
            return self._size // self._world_size
        else:
            return (self._size + self._world_size - 1) // self._world_size


class InfiniteIntervalSampler(InfiniteSampler):
    def __init__(
        self,
        size: int,
        shuffle: bool = True,
        seed: Optional[int] = 0,
        rank=0,
        world_size=1,
        drop_last=False,
        interval=1,
    ):
        super().__init__(size, shuffle, seed, rank, world_size, drop_last)
        self.interval = interval
        self._size //= self.interval

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                list_temp = (torch.randperm(self._size, generator=g) * self.interval).tolist()
            else:
                list_temp = list(range(0, self._size * self.interval, self.interval))
            yield from [idx + np.random.randint(0, self.interval - 1) for idx in list_temp]


class DPFlowInfiniteSampler(InfiniteSampler):
    def set_seed(self, seed):
        self._seed = int(seed)

    def __iter__(self):
        yield from self._infinite_indices()

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        logger.info(self._seed)
        while True:
            if self._shuffle:
                yield from torch.randperm(self._size, generator=g).tolist()
            else:
                # yield from torch.arange(self._size)
                yield from list(range(self._size))


class WeightedInfiniteSampler(Sampler):
    def __init__(
        self,
        weights: Sequence[float],
        size: int,
        replacement: bool = True,
        generator=None,
        rank=0,
        world_size=1,
        drop_last=False,
    ) -> None:
        self._size = size
        assert size > 0, "The dataset size should larger than 0"
        self.drop_last = drop_last
        self.weights = torch.as_tensor(weights, dtype=torch.double)
        self.replacement = replacement
        self.generator = generator

        if dist.is_available() and dist.is_initialized():
            self._rank = dist.get_rank()
            self._world_size = dist.get_world_size()
        else:
            self._rank = rank
            self._world_size = world_size

    def __iter__(self):
        start = self._rank
        yield from itertools.islice(self._infinite_indices(), start, None, self._world_size)

    def _infinite_indices(self):
        while True:
            rand_tensor = torch.multinomial(self.weights, self._size, self.replacement, generator=self.generator)
            yield from rand_tensor.tolist()

    def __len__(self):
        if self.drop_last:
            return self._size // self._world_size
        else:
            return (self._size + self._world_size - 1) // self._world_size


class DPFlowWeightedInfiniteSampler(WeightedInfiniteSampler):
    def set_seed(self, seed):
        self._seed = int(seed)

    def __iter__(self):
        yield from self._infinite_indices()

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        logger.info(self._seed)
        while True:
            rand_tensor = torch.multinomial(self.weights, self._size, self.replacement, generator=g)
            yield from rand_tensor.tolist()
