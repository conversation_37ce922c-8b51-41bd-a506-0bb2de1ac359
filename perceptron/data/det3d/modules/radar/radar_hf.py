import io
import pickle
import random
import traceback

import nori2 as nori
import numpy as np

from .base import RadarBase
from refile import smart_open
from perceptron.utils.file_io import load_pkl
from loguru import logger


class HFRadar(RadarBase):
    def get_radar_name(self, sensor_data_name):
        for name in sensor_data_name:
            if "radar" in name:
                target_radar_name = name
                break
            else:
                target_radar_name = "none"

        if "_" in target_radar_name:
            radar_name_list = [f"radar_{x}" for x in self.radar_key_list]
        elif target_radar_name == "none":
            radar_name_list = []
        else:
            radar_name_list = [f"radar{x}" for x in self.radar_key_list]

        return radar_name_list

    def collect_single_radar_points_center_query(self, radar_info, adj_idx, mode, radar_name):

        single_radar_points = list()
        objects = radar_info["objects"]

        for obj in objects:
            # jeely 数据msg解释见： https://wiki.megvii-inc.com/pages/viewpage.action?pageId=422957145
            xyz = [-obj["position"]["pose"]["position"]["y"], obj["position"]["pose"]["position"]["x"], 0.5]
            if mode == "mlp":
                # mlp_points = xyz
                theta = obj["orientation_angle"] / 180.0 * np.pi + np.pi / 2
                length = obj["length"]
                width = obj["width"]

                if length < 5.0:
                    radarbox_length = 4.5
                else:
                    radarbox_length = length
                if ("3" not in radar_name) and ("4" not in radar_name):
                    center_coord_x = xyz[0]
                    center_coord_y = xyz[1] + radarbox_length / 2 - 0.6
                else:
                    center_coord_x = xyz[0]
                    center_coord_y = xyz[1] - radarbox_length / 2 + 0.6

                mlp_points = [center_coord_x, center_coord_y, 0.5]
                mlp_points.append(theta)
                mlp_points.extend([width, length])

                velocity_x = -obj["relative_velocity"]["twist"]["linear"]["y"]
                velocity_y = obj["relative_velocity"]["twist"]["linear"]["x"]
                mlp_points.extend([velocity_x, velocity_y])  # mlp_points[6:8]

                prob_of_exist = obj["prob_of_exist"]
                class_type = obj["class_type"]
                class_type = 7
                mlp_points.extend([prob_of_exist, class_type])
                mlp_points.extend([int(radar_name[-1])])  # timestamp 占位符

                single_radar_points.append(mlp_points)  # len(mlp_points) = 11

            else:
                single_radar_points.append(xyz)

        return single_radar_points

    def collect_single_radar_points(self, radar_info, adj_idx, mode, radar_name):

        single_radar_points = list()
        objects = radar_info["objects"]

        for obj in objects:
            # jeely 数据msg解释见： https://wiki.megvii-inc.com/pages/viewpage.action?pageId=422957145
            xyz = [-obj["position"]["pose"]["position"]["y"], obj["position"]["pose"]["position"]["x"], 0.5]
            if mode == "mlp":
                mlp_points = xyz
                theta = obj["orientation_angle"] / 180.0 * np.pi + np.pi / 2
                mlp_points.append(theta)

                length = obj["length"]
                width = obj["width"]
                mlp_points.extend([width, length])

                velocity_x = -obj["relative_velocity"]["twist"]["linear"]["y"]
                velocity_y = obj["relative_velocity"]["twist"]["linear"]["x"]
                mlp_points.extend([velocity_x, velocity_y])  # mlp_points[6:8]

                prob_of_exist = obj["prob_of_exist"]
                class_type = 7
                mlp_points.extend([prob_of_exist, class_type])
                mlp_points.extend([int(radar_name[-1])])  # timestamp 占位符

                single_radar_points.append(mlp_points)  # len(mlp_points) = 11

            else:
                single_radar_points.append(xyz)

        return single_radar_points

    def read_radar_data(self, radar_oriinfo):
        if "file_path" in radar_oriinfo:
            with smart_open(radar_oriinfo["file_path"].replace("s3://", "/mnt/acceldata/dynamic/"), "rb") as f:
                file_bytes = f.read()
            data = pickle.load(io.BytesIO(file_bytes))
            if not isinstance(data, dict):
                data = pickle.loads(data)
        elif "nori_id" in radar_oriinfo:
            nori_id = radar_oriinfo["nori_id"]
            if self.nori_available:
                data = pickle.loads(self.nori_fetcher.get(radar_oriinfo["nori_id"]))
            else:
                try:
                    vid = int(nori_id.split(",")[0])
                    nori_path = radar_oriinfo["nori_path"].replace("s3://", "/mnt/acceldata/dynamic/")
                    vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                    data = pickle.loads(vreader.get(radar_oriinfo["nori_id"]))
                except Exception:
                    print(radar_oriinfo, "not found")
                    return None
        elif "file_path" in radar_oriinfo:
            if "s3://" in radar_oriinfo["file_path"]:
                logger.info(f'file_path starts with s3:// {radar_oriinfo["file_path"]}')
            with smart_open(radar_oriinfo["file_path"], "rb") as f:
                file_bytes = f.read()
            data = pickle.load(io.BytesIO(file_bytes))
        else:
            data = load_pkl(radar_oriinfo["s3_path"])
        return data

    def _get_radars(self, idx, keys):
        """
        Loading radar info with given sample index

        Args:
            idx (int):, Sampled index
        Returns:
            radar (list[np.ndarray]): (N, 3),point ver
        """
        radar_points = list()

        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        radar_name_list = self.get_radar_name(sensor_data.keys())
        for radar_name in radar_name_list:
            radar_info = dict()
            if radar_name in sensor_data:
                try:
                    radar_oriinfo = sensor_data[radar_name]
                    if radar_oriinfo is not None:
                        data = self.read_radar_data(radar_oriinfo)
                        if data is not None:
                            radar_info.update(data)

                        # add right radar points into query
                        if self.radar_mv2center:
                            single_radar_points = self.collect_single_radar_points_center_query(
                                radar_info, 0, self.radar_mode, radar_name
                            )
                        else:
                            single_radar_points = self.collect_single_radar_points(
                                radar_info, 0, self.radar_mode, radar_name
                            )

                        if len(single_radar_points) > 0:
                            radar_points.append(single_radar_points)
                except Exception:
                    traceback.print_exc()
                    continue

        return radar_points, radar_name_list

    def gen_virtual_radar_points(self, data_dict):
        """
        Func: generate virtual radar points according to gt_annos
        Args:
        data_dict:
            gt_boxes: (n, 7)
            gt_labels: (n,)
        Returns:
            vitrual_radar_points_list(list[np.ndarray]): (N, 3), point ver
        """
        vitrual_radar_points_list = list()
        gt_boxes_list = data_dict["gt_boxes"].copy()
        labels_list = data_dict["gt_labels"]
        repeat_time = random.randint(1, 4)  # 模拟不同雷达可能扫到同一个目标的场景
        for _ in range(repeat_time):
            for idx, gt_boxes in enumerate(gt_boxes_list):
                save_seed = np.random.randint(10, size=1)  # 6/10 的概率依据改点生成 虚拟框
                if save_seed > 4:
                    labels = labels_list[idx]
                    if labels in [0, 1, 2, 3, 4, 5, 6, 7, 8]:
                        class_type = 7
                    else:
                        continue
                    lw_seed = np.random.randint(low=-10, high=10, size=2) * 0.1  # 模拟radar点长宽缩放，缩放范围[0,1]
                    lw = gt_boxes[3:5][::-1] * np.abs(lw_seed)
                    center_xy = gt_boxes[:2] - gt_boxes[3:5][::-1] * lw_seed  # 中心点diff
                    vitrual_radar_points = center_xy.tolist()
                    vitrual_radar_points.append(0.5)  # z
                    vitrual_radar_points.append(gt_boxes[-1])  # theta
                    vitrual_radar_points.extend(lw.tolist())
                    vitrual_radar_points.extend([0, 0])  # velocity_x/y，默认 0，0
                    prob_of_exist = np.random.randint(low=3, high=8, size=1)
                    vitrual_radar_points.extend(prob_of_exist.tolist())  # z
                    vitrual_radar_points.append(class_type)
                    vitrual_radar_points.extend([0])  # timestamp
                    vitrual_radar_points_list.append(vitrual_radar_points)

        return vitrual_radar_points_list

    # only support load key frame
    def get_radars(self, idx, data_dict):
        """
        Loading lidar with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        # load current key frame
        res = self._get_radars(idx, keys=self.radar_key_list)
        if res is not None:
            radar_points, radar_name_list = res
        else:
            return None
        if len(radar_points) > 0:
            radar_points = np.concatenate(radar_points, axis=0)  # np.array[n*3] 当前没过滤边界
        else:
            # 初始化全零适配1.radar 被全过滤掉的情况，2. 无radar
            if self.radar_mode == "mlp":
                radar_points = np.zeros((100, 11))

        if self.with_virtual_radar and (len(radar_name_list) == 0):
            virtual_radar_points_list = self.gen_virtual_radar_points(data_dict)

            if len(virtual_radar_points_list) > 0:
                radar_points = np.concatenate([virtual_radar_points_list], axis=0)  # np.array[n*3] 当前没过滤边界
            else:
                # 初始化全零适配1.radar 被全过滤掉的情况，2. 无radar
                radar_points = np.zeros((100, 11))

        # load to data_dict
        data_dict.update(
            {
                "radar_points": radar_points,
                "radar_mode": self.radar_mode,
            }
        )
        return data_dict
