import nori2 as nori
import numpy as np

import pickle
from .base import RadarBase


class GeelyRadar(RadarBase):
    def get_radar_name(self, sensor_data_name):
        for name in sensor_data_name:
            if "radar" in name:
                target_radar_name = name
                break
            else:
                target_radar_name = "none"

        if "_" in target_radar_name:
            radar_name_list = [f"radar_{x}" for x in self.radar_key_list]
        elif target_radar_name == "none":
            radar_name_list = []
        else:
            radar_name_list = [f"radar{x}" for x in self.radar_key_list]

        return radar_name_list

    def collect_single_radar_points(self, radar_info, adj_idx, mode):

        single_radar_points = list()
        objects = radar_info["objects"]

        for obj in objects:
            # filter
            if (obj["class_type"] == 5) or (obj["class_type"] == 8):
                if obj["prob_of_exist"] >= 3:
                    # jeely 数据msg解释见： https://wiki.megvii-inc.com/pages/viewpage.action?pageId=422957145
                    xyz = [-obj["position"]["pose"]["position"]["y"], obj["position"]["pose"]["position"]["x"], 0.5]
                    if mode == "mlp":
                        mlp_points = xyz
                        theta = obj["orientation_angle"] / 180.0 * np.pi + np.pi / 2
                        mlp_points.append(theta)

                        length = obj["length"]
                        width = obj["width"]
                        mlp_points.extend([width, length])

                        velocity_x = -obj["relative_velocity"]["twist"]["linear"]["y"]
                        velocity_y = obj["relative_velocity"]["twist"]["linear"]["x"]
                        mlp_points.extend([velocity_x, velocity_y])  # mlp_points[6:8]

                        prob_of_exist = obj["prob_of_exist"]
                        class_type = obj["class_type"]
                        mlp_points.extend([prob_of_exist, class_type])
                        mlp_points.extend([-adj_idx])  # timestamp 占位符

                        single_radar_points.append(mlp_points)  # len(mlp_points) = 11

                    else:
                        single_radar_points.append(xyz)

        return single_radar_points

    def _get_radars(self, idx, keys):
        """
        Loading point cloud with given sample index

        Refer to https://wiki.megvii-inc.com/pages/viewpage.action?pageId=316705342 for details about the point cloud format.

        Args:
            idx (int): Sampled index
        Returns:
            point cloud (Dict[str, np.ndarray]): (N, 4), (x-y-z-reflection)
        """
        radar_points = list()

        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        radar_name_list = self.get_radar_name(sensor_data.keys())
        for radar_name in radar_name_list:
            radar_info = dict()
            if radar_name in sensor_data:
                radar_oriinfo = sensor_data[radar_name]
                if radar_oriinfo is not None:
                    radar_info.update(pickle.loads(self.nori_fetcher.get(radar_oriinfo["nori_id"])))

                    # add right radar points into query
                    single_radar_points = self.collect_single_radar_points(radar_info, 0, self.radar_mode)
                    if len(single_radar_points) > 0:
                        radar_points.append(single_radar_points)

        return radar_points, radar_name_list

    # only support load key frame
    def get_radars(self, idx, data_dict):
        """
        Loading lidar with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        # load current key frame
        radar_points, radar_name_list = self._get_radars(idx, keys=self.radar_key_list)
        if len(radar_points) > 0:
            radar_points = np.concatenate(radar_points, axis=0)  # np.array[n*3] 当前没过滤边界
        else:
            # 初始化全零适配1.radar 被全过滤掉的情况，2. 无radar
            if self.radar_mode == "mlp":
                radar_points = np.zeros((100, 11))

        # 暂时先不支持 没想好gt_annos 怎么传
        # with_virtual_radar = True
        # if self.with_virtual_radar and (len(radar_name_list) == 0):
        #     virtual_radar_points_list = self.gen_virtual_radar_points(gt_annos)

        #     if len(virtual_radar_points_list) > 0:
        #         radar_points = np.concatenate([virtual_radar_points_list], axis=0)  # np.array[n*3] 当前没过滤边界
        #     else:
        #         # 初始化全零适配1.radar 被全过滤掉的情况，2. 无radar
        #         radar_points = np.zeros((100, 11))

        # load to data_dict
        data_dict.update(
            {
                "radar_points": radar_points,
                "radar_mode": self.radar_mode,
            }
        )
        return data_dict
