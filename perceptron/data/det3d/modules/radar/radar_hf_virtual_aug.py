import numpy as np

from .radar_hf import HFRadar

distribution = """car
(tensor([0, 1, 2, 3, 4, 5]), tensor([46462, 13876, 10507, 4733, 123, 5]))
(tensor([0, 1, 2, 3, 4, 5]), tensor([40122, 15364, 13169, 6854, 185, 12]))
truck
(tensor([ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]), tensor([15005, 7650, 6517, 6222, 2208, 1117, 611, 190, 50, 5, 2]))
(tensor([ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]), tensor([14084, 7456, 6564, 6673, 2457, 1250, 775, 236, 66, 13, 3]))
construction_vehicle
(tensor([0, 1, 2, 3]), tensor([219, 5, 4, 1]))
(tensor([0, 1, 2, 3]), tensor([218, 6, 4, 1]))
bus
(tensor([0, 1, 2, 3, 4, 5, 6, 7]), tensor([563, 288, 282, 221, 68, 29, 6, 2]))
(tensor([0, 1, 2, 3, 4, 5, 6, 7]), tensor([530, 284, 276, 245, 83, 29, 9, 3]))
motorcycle
(tensor([0, 1]), tensor([833, 1]))
(tensor([0, 1]), tensor([832, 2]))
bicycle
(tensor([0, 1]), tensor([238, 1]))
(tensor([0, 1]), tensor([238, 1]))
tricycle
(tensor([0, 1, 2, 3]), tensor([891, 66, 24, 22]))
(tensor([0, 1, 2, 3]), tensor([873, 59, 43, 28]))
cyclist
(tensor([0, 1, 2, 3]), tensor([2359, 324, 98, 12]))
(tensor([0, 1, 2, 3]), tensor([2195, 382, 188, 28]))
pedestrian
(tensor([0, 1, 2]), tensor([3058, 9, 2]))
(tensor([0, 1, 2]), tensor([3048, 19, 2]))
背景radar点
(tensor([0, 1, 2, 3, 4, 5, 6, 7]), tensor([9681, 491, 89, 29, 9, 5, 1, 2]))
(tensor([ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]), tensor([18231, 9285, 6591, 3931, 2090, 989, 395, 157, 62, 18, 10]))"""


class HFRadarVirtualAug(HFRadar):
    """For the unusable radar data(before 20230831), only use Virtual radar or zeros,"""

    def __init__(
        self,
        car=None,
        radar_key_list=...,
        loader_output=None,
        radar_mode="mlp",
        mode="train",
        with_virtual_radar=False,
        radar_mv2center=False,
    ):
        super().__init__(car, radar_key_list, loader_output, radar_mode, mode, with_virtual_radar, radar_mv2center)

        self.probabilitiy = {
            "car": [[0, 1, 2, 3, 4, 5], [40122, 15364, 13169, 6854, 185, 12]],
            "truck": [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [14084, 7456, 6564, 6673, 2457, 1250, 775, 236, 66, 13, 3]],
            "construction_vehicle": [[0, 1, 2, 3], [218, 6, 4, 1]],
            "bus": [[0, 1, 2, 3, 4, 5, 6, 7], [530, 284, 276, 245, 83, 29, 9, 3]],
            "motorcycle": [[0, 1], [832, 2]],
            "bicycle": [[0, 1], [238, 1]],
            "tricycle": [[0, 1, 2, 3], [873, 59, 43, 28]],
            "cyclist": [[0, 1, 2, 3], [2195, 382, 188, 28]],
            "pedestrian": [[0, 1, 2], [3048, 19, 2]],
            "bg_radar": [
                [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                [18231, 9285, 6591, 3931, 2090, 989, 395, 157, 62, 18, 10],
            ],
        }

        for k, v in self.probabilitiy.items():
            total = sum(v[1])
            percent = [i / total for i in v[1]]
            v[1] = percent
            self.probabilitiy[k] = v

        class_names = [
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "motorcycle",
            "bicycle",
            "tricycle",
            "cyclist",
            "pedestrian",
        ]
        for i, k in enumerate(class_names):
            self.probabilitiy[i] = self.probabilitiy[k]

    # only support load key frame
    def get_radars(self, idx, data_dict):
        """
        Loading lidar with given sample index
        """
        radar_points = None
        if self.with_virtual_radar:
            virtual_radar_points_list = self.gen_virtual_radar_points(data_dict)
            if len(virtual_radar_points_list) > 0:
                radar_points = np.concatenate([virtual_radar_points_list], axis=0)  # np.array[n*3] 当前没过滤边界
        if radar_points is None:
            if self.radar_mode == "mlp":
                radar_points = np.zeros((100, 11))
            else:
                radar_points = np.zeros((100, 3))

        # load to data_dict
        data_dict.update(
            {
                "radar_points": radar_points,
                "radar_mode": self.radar_mode,
            }
        )
        return data_dict

    def gen_virtual_radar_points(self, data_dict):
        """
        Func: generate virtual radar points according to gt_annos
        Args:
        data_dict:
            gt_boxes: (n, 7)
            gt_labels: (n,)
        Returns:
            vitrual_radar_points_list(list[np.ndarray]): (N, 3), point ver
        """
        vitrual_radar_points_list = list()
        gt_boxes_list = data_dict["gt_boxes"].copy()
        labels_list = data_dict["gt_labels"]

        for idx, gt_boxes in enumerate(gt_boxes_list):

            labels = int(labels_list[idx])
            if labels in [0, 1, 2, 3, 4, 5, 6, 7, 8]:
                class_type = 7
            else:
                continue
            distribution = self.probabilitiy[labels]
            point_num = np.random.choice(distribution[0], size=1, p=distribution[1]).item()  # 模拟一个物体上出现多个radar或者没有radar

            lw_seed = np.random.rand(point_num) * 2.0 - 1.0  # 模拟radar点长宽缩放，缩放范围[-1, 1]
            for i in range(point_num):
                # 边框外扩0.5m的范围内随机选点
                lw = (gt_boxes[3:5][::-1] / 2.0 + 0.5) * lw_seed[i]
                center_xy = gt_boxes[:2] + lw  # 中心点diff
                vitrual_radar_points = center_xy.tolist()
                vitrual_radar_points.append(0.5)  # z
                vitrual_radar_points.append(gt_boxes[-1])  # theta
                vitrual_radar_points.extend(lw.tolist())
                vitrual_radar_points.extend([0, 0])  # velocity_x/y，默认 0，0
                prob_of_exist = np.random.randint(low=3, high=8, size=1)
                vitrual_radar_points.extend(prob_of_exist.tolist())  # z
                vitrual_radar_points.append(class_type)
                vitrual_radar_points.extend([0])  # timestamp
                vitrual_radar_points_list.append(vitrual_radar_points)

        return vitrual_radar_points_list
