from abc import ABC
from dataclasses import dataclass
from functools import lru_cache
from mc_dnn import CudaUnifiedImageProcessing
import numpy as np
import torch
from torchvision.transforms import functional as F
from typing import Dict, List, Optional, Tuple
import warnings

from perceptron.utils.misc import EnvironWrapper


@lru_cache(maxsize=1)
def _use_unified_impl() -> bool:
    envWrapper = EnvironWrapper()
    useUnifiedImpl = envWrapper.getBool("E2E_USE_UNIFIED_CUDA_IMAGE_PROCESSOR", defaultOrErr=False)
    return useUnifiedImpl


class ImageAffineTransGPU(ABC):
    def __init__(
        self,
        mode: str = "train",
        img_norm: bool = False,
        img_conf: Dict = {"img_mean": [0, 0, 0], "img_std": [1, 1, 1]},
    ) -> None:
        super().__init__()
        self.mode = mode
        self.img_norm = img_norm
        self.img_conf = img_conf

    def _multisize_img_transform(self, batch_dict: Dict) -> None:
        warnings.warn(
            "Multi size img input is deprecated, consider use multiple dataset with different config instead.",
            DeprecationWarning,
        )
        img_list = batch_dict["imgs"]
        img_metas = batch_dict["img_metas"]

        item = img_list
        outer_dims = []
        while isinstance(item, list):
            outer_dims.append(len(item))
            item = item[0]
        # Batch x Time x Cameras x 3 x H x W
        N, T, C = (*outer_dims,)
        K, H, W = item.shape

        img_batch_aug = []

        for batch_idx in range(N):
            for time_idx in range(T):
                for camera_idx in range(C):
                    _ida_paras = img_metas[batch_idx]["ida_mats_detail"][0][camera_idx]
                    img_batch_aug.append(
                        self._img_transform_torch(
                            img_list[batch_idx][time_idx][camera_idx].unsqueeze(0),
                            _ida_paras["resize"],
                            _ida_paras["resize_dims"][::-1] if not _use_unified_impl() else None,
                            _ida_paras["crop"],
                            _ida_paras["flip"],
                            _ida_paras["rotate"],
                        ).squeeze(0)
                    )
        H_new, W_new = img_batch_aug[0].shape[-2:]
        img_batch_aug = torch.stack(img_batch_aug).view(N, T, C, K, H_new, W_new).contiguous()
        batch_dict["imgs"] = img_batch_aug

    def __call__(self, batch_dict: Dict) -> None:
        # 重构：移除side image相关功能，仅暂时保留list形式输入，如需side image可考虑通过增加额外dataset实现
        r"""
        Description:  preprocess image to tensor, and compute by GPU.
        Batch["imgs"]: Option 1 -> List[Tensor] with shape 1 x 3 x H x W .
        batch["imgs"]: Option 2 -> Tensor with shape N x Time x Cameras X Channel x W x H.
        """
        assert set(("imgs", "img_metas")).issubset(batch_dict.keys()), "batch_dict should have img and img_metas"

        if isinstance(batch_dict["imgs"], list):  # !NOTE: List[Tensor] will be deprecated in the future.
            # batch_dict_aug = [self._img_transform_torch(img) for img in batch_dict["imgs"]]
            self._multisize_img_transform(batch_dict)
        else:
            img_batch = batch_dict["imgs"]
            img_metas = batch_dict["img_metas"]

            # Batch x Time x Cameras x 3 x H x W
            N, T, C, K, H, W = img_batch.shape
            img_batch_aug = []

            for batch_idx in range(N):
                for camera_idx in range(C):
                    _ida_paras = img_metas[batch_idx]["ida_mats_detail"][0][camera_idx]
                    img_batch_aug.append(
                        self._img_transform_torch(
                            img_batch[batch_idx, :, camera_idx, ...],
                            _ida_paras["resize"],
                            _ida_paras["resize_dims"][::-1] if not _use_unified_impl() else None,
                            _ida_paras["crop"],
                            _ida_paras["flip"],
                            _ida_paras["rotate"],
                            img_norm=True,
                        )  # [1, 3, h, w]
                    )
            C_tmp = len(img_metas[0]["ida_mats_detail"][0])
            H_new, W_new = img_batch_aug[0].shape[-2:]
            img_batch_aug = (
                torch.stack(img_batch_aug).view(N, C_tmp, T, K, H_new, W_new).permute(0, 2, 1, 3, 4, 5).contiguous()
            )
            batch_dict["imgs"] = img_batch_aug

            # 这里最好要和 cam name 对齐，由于这里没有传入 cam_name, 只能按顺序对齐
            if False:  # "img_semantic_seg" in batch_dict:
                rv_seg = batch_dict["img_semantic_seg"]  # batch x time x cameras(1) x 1 x H x W
                _, _, num_cam, num_channel, h, w = rv_seg.shape
                rv_seg_batch_aug = []
                for batch_idx in range(N):
                    for camera_idx in range(num_cam):
                        rv_seg_batch_aug.append(
                            self._img_transform_torch(
                                rv_seg[batch_idx, :, camera_idx, ...],
                                _ida_paras["resize"],
                                _ida_paras["resize_dims"][::-1],
                                _ida_paras["crop"],
                                _ida_paras["flip"],
                                _ida_paras["rotate"],
                                img_norm=False,
                            )
                        )
                rv_seg_batch_aug = (
                    torch.stack(rv_seg_batch_aug)
                    .view(N, num_cam, T, num_channel, H_new, W_new)
                    .permute(0, 2, 1, 3, 4, 5)
                    .contiguous()
                )
                batch_dict["img_semantic_seg"] = rv_seg_batch_aug

            # tmp = img_batch_aug[0, 0, 0].cpu().permute(1, 2, 0).numpy()
            # tmp[:, :, 1] += (rv_seg_batch_aug[0, 0, 0, 0].cpu().numpy()>0)*100

    @torch.no_grad()
    def _img_transform_torch(
        self,
        img: torch.Tensor,
        resize: float = 1.0,
        resize_dims: Optional[List[int]] = None,
        crop: Tuple = (0, 0, 0, 0),
        flip: bool = False,
        rotate: float = 0,
        img_norm=True,
    ) -> torch.Tensor:
        r"""
        Description:  image augmentation by torh, and compute by GPU.

        Args:
            img  [Torch.tensor] : N * C * H * W.
            resize      [float] : default 1.0.
            resize_dims [Tuple] : (0, 0).
            Crop        [Tuple] : (x1, y1, x2, y2).
            flip        [bool]  : defalut `False`

        Return:
            return torch.Tensor : N * C * H * W.

        Example::
            >>> TODO @tanfeiyang
        """

        def _robust_crop(img: torch.Tensor, crop: Tuple) -> torch.Tensor:
            r"""img shape is N x C x H x W"""
            x1, y1, x2, y2 = crop
            img_h, img_w = img.shape[2], img.shape[3]
            pad_up = max(0, -y1)
            pad_down = max(0, y2 - img_h)
            pad_left = max(0, -x1)
            pad_right = max(0, x2 - img_w)

            # left, top, right and bottom
            img = F.pad(img, (pad_left, pad_up, pad_right, pad_down), 0, "constant")

            y2 += -min(0, y1)
            y1 += -min(0, y1)
            x2 += -min(0, x1)
            x1 += -min(0, x1)

            return img[..., y1:y2, x1:x2]

        if self.mode == "train":
            interpolate_candidate = [
                F.InterpolationMode.NEAREST,
                F.InterpolationMode.BILINEAR,
                F.InterpolationMode.BICUBIC,
            ]
            index = np.random.randint(0, len(interpolate_candidate))
            interpolation = interpolate_candidate[index]
        else:
            interpolation = F.InterpolationMode.BILINEAR

        if resize_dims is not None:
            img = F.resize(img, resize_dims, interpolation=interpolation, antialias=True)

        img = _robust_crop(img, crop)
        if flip:
            img = F.hflip(img)
        img = F.rotate(img, rotate, interpolation=F.InterpolationMode.BILINEAR)
        if self.img_norm and img_norm:
            img = F.normalize(img, self.img_conf["img_mean"], self.img_conf["img_std"])
        return img


@dataclass(frozen=True)
class _Resolution2D:
    height: int
    width: int


class ImageUndistortGPU(ABC):
    _IMAGE_KEY: str = "imgs"
    _IMAGE_META_LIST_KEY: str = "img_metas"
    _IMAGE_TRANSFORMATION_KEY: str = "ida_mats_detail"
    _IMAGE_RESIZE_TARGET_RESOLUTION_KEY: str = "resize_dims"

    _IMAGE_INPUT_DTYPE: torch.dtype = torch.uint8
    _IMAGE_OUTPUT_DTYPE: torch.dtype = torch.float32
    _IMAGE_CHANNELS: int = 3
    _IMAGE_MEMORY_FORMAT: torch.memory_format = torch.contiguous_format

    _COORD_MAP_KEY: str = "map1s"
    _COORD_MAP_DTYPE: torch.dtype = torch.float32
    _COORD_MAP_MEMORY_FORMAT: torch.memory_format = torch.contiguous_format
    _COORD_DIM: int = 2

    def __init__(self, mode: str = "train") -> None:
        super().__init__()
        self.mode = mode

    def _remap_pytorch(self, img: torch.Tensor, map1: torch.Tensor, map2: torch.Tensor) -> torch.Tensor:
        r"""
        Description: Replace cv2.remap with torch.nn.functional.grid_sample

        Args:
            img  [torch.Tensor] : Input image tensor with shape [B, C, H, W]
            map1 [torch.Tensor] : Input map1 tensor with shape  [B, H, W, 1]
            map2 [torch.Tensor] : Input map2 tensor with shape  [B, H, W, 1]

        Return:
            undistort image : torch.Tensor

        Example::
            >>> img = torch.randn(1, 3, 1080, 1920)
            >>> map1 = torch.randn(1, 1080, 1920, 1)
            >>> map2 = torch.randn(1, 1080, 1920, 1)
            >>> img_undistort = remap_pytorch(img, map1, map2)
        """
        # Define height and width
        height, width = img.shape[-2:]

        # Normalize map and permute dimensions
        map1 = 2.0 * map1 / (width - 1) - 1.0
        map2 = 2.0 * map2 / (height - 1) - 1.0

        grid = torch.stack([map1, map2], dim=-1)

        return torch.nn.functional.grid_sample(img.float(), grid, align_corners=False)

    def _call_torch_impl(self, batch_dict: Dict) -> None:
        imgs = batch_dict[self._IMAGE_KEY]
        if isinstance(imgs, list):  # !NOTE: List[Tensor] will be deprecated in the future.
            img_list = imgs
            map1_list = batch_dict[self._COORD_MAP_KEY]

            item = img_list
            outer_dims = []
            while isinstance(item, list):
                outer_dims.append(len(item))
                item = item[0]
            # Batch x Time x Cameras x 3 x H x W
            N, T, C = (*outer_dims,)
            K, H, W = item.shape

            for batch_idx in range(N):
                for time_idx in range(T):
                    for camera_idx in range(C):
                        img = img_list[batch_idx][time_idx][camera_idx].unsqueeze(0)
                        map1 = map1_list[batch_idx][time_idx][camera_idx].unsqueeze(0)
                        # map2 = map2_list[batch_idx][time_idx][camera_idx].unsqueeze(0)
                        img_remapped = self._remap_pytorch(img, map1[..., 0], map1[..., 1])
                        img_list[batch_idx][time_idx][camera_idx] = img_remapped.squeeze(0)
        else:
            img_batch = imgs
            map1_batch = batch_dict[self._COORD_MAP_KEY]

            N, T, C, K, H, W = img_batch.shape
            img_batch = img_batch.view(N * T * C, K, H, W)
            map1_batch = map1_batch.view(N * T * C, H, W, 2)
            img_batch_remapped = (
                self._remap_pytorch(img_batch, map1_batch[..., 0], map1_batch[..., 1])
                .view(N, T, C, K, H, W)
                .contiguous()
            )

            batch_dict[self._IMAGE_KEY] = img_batch_remapped

    def _call_unified_impl(self, batchDict: Dict) -> None:
        imageOrList = batchDict[self._IMAGE_KEY]
        if isinstance(imageOrList, list):
            self._process_image_list(batchDict, imageOrList)
        else:
            self._process_batched_image(batchDict, imageOrList)

    def _process_batched_image(self, batchDict: Dict, batchedImage: torch.Tensor) -> None:
        # `batchedImage` should be of shape [N, T, Cam, Ch, H, W]
        self._check_image(batchedImage, dim=6, channelDim=3)
        batchSize, numTimeSeq, numCam, _, height, width = batchedImage.shape
        batchedCoordMap = batchDict[self._COORD_MAP_KEY]
        # `batchedCoordMap` should be of shape [N, T, Cam, H, W, D]
        self._check_coordinate_map(batchedCoordMap, (batchSize, numTimeSeq, numCam, height, width, self._COORD_DIM))
        # `tensor.view()` does not change the memory layout of a tensor
        combinedBatch = batchSize * numTimeSeq * numCam
        # `inputImage` has shape [N * T * Cam, Ch, H, W]
        inputImage = batchedImage.view(combinedBatch, self._IMAGE_CHANNELS, height, width)
        # `inputCoordMap` has shape [D, N * T * Cam, H, W]
        inputCoordMap = (
            batchedCoordMap.view(combinedBatch, height, width, self._COORD_DIM)
            .permute(3, 0, 1, 2)
            .to(dtype=self._COORD_MAP_DTYPE, memory_format=self._COORD_MAP_MEMORY_FORMAT)
        )
        # Assume output resolutions of images from all cameras are same.
        outputResolution = self._get_output_resolution(batchDict, batchIdx=0, cameraIdx=0)
        cudaProcessor = CudaUnifiedImageProcessing()
        # `outputImage` has shape [N * T * Cam, Ch, H, W]
        outputImage = self._unified_cuda_process(
            inputImage,
            inputCoordMap[0, ...],
            inputCoordMap[1, ...],
            cudaProcessor,
            outputResolution.height,
            outputResolution.width,
        )
        self._check_image(
            outputImage,
            shape=(
                combinedBatch,
                self._IMAGE_CHANNELS,
                outputResolution.height,
                outputResolution.width,
            ),
        )
        batchDict[self._IMAGE_KEY] = outputImage.view(
            batchSize, numTimeSeq, numCam, self._IMAGE_CHANNELS, outputResolution.height, outputResolution.width
        ).to(dtype=self._IMAGE_OUTPUT_DTYPE, memory_format=self._IMAGE_MEMORY_FORMAT)

    def _check_coordinate_map(
        self,
        coordMap: torch.Tensor,
        shape: torch.Size | Tuple[int, ...],
    ) -> None:
        assert isinstance(coordMap, torch.Tensor)
        assert coordMap.is_cuda
        assert coordMap.shape == shape

    def _check_image(
        self,
        image: torch.Tensor,
        *,
        dim: Optional[int] = None,
        channelDim: Optional[int] = None,
        shape: torch.Size | Tuple[int, ...] | None = None,
        contiguous: Optional[bool] = None,
    ) -> None:
        assert isinstance(image, torch.Tensor)
        assert image.is_cuda
        assert image.dtype == self._IMAGE_INPUT_DTYPE

        assert dim is None or image.dim() == dim
        assert channelDim is None or image.shape[channelDim] == self._IMAGE_CHANNELS
        assert shape is None or image.shape == shape
        assert contiguous is None or image.is_contiguous() == contiguous

    @classmethod
    def _get_output_resolution(
        cls,
        batchDict: Dict,
        *,
        batchIdx: int,
        cameraIdx: int,
    ) -> _Resolution2D:
        imgMeta = batchDict[cls._IMAGE_META_LIST_KEY][batchIdx]
        transSpec = imgMeta[cls._IMAGE_TRANSFORMATION_KEY][0][cameraIdx]
        resizeTarget = transSpec[cls._IMAGE_RESIZE_TARGET_RESOLUTION_KEY]
        return _Resolution2D(height=resizeTarget[1], width=resizeTarget[0])

    def _process_image_list(
        self,
        batchDict: Dict,
        imageList: List[List[List[torch.Tensor]]],
    ) -> None:
        assert isinstance(imageList, list)
        batchSize = len(imageList)
        assert isinstance(imageList[0], list)
        numTimeSeq = len(imageList[0])
        assert isinstance(imageList[0][0], list)
        numCam = len(imageList[0][0])

        imageSample = imageList[0][0][0]
        self._check_image(imageSample, dim=3, channelDim=0)
        imageShape = imageSample.shape
        _, height, width = imageShape

        coordMap: torch.Tensor = batchDict[self._COORD_MAP_KEY]
        # `coordMap` should be of shape [H, W, D]
        self._check_coordinate_map(coordMap, (height, width, self._COORD_DIM))
        # `inputCoordMap` has shape [D, 1, H, W]
        inputCoordMap = (
            coordMap.permute(2, 0, 1)
            .to(dtype=self._COORD_MAP_DTYPE, memory_format=self._COORD_MAP_MEMORY_FORMAT)
            .unsqueeze(1)
        )

        cudaProcessor = CudaUnifiedImageProcessing()
        for batchIdx in range(batchSize):
            for timeIdx in range(numTimeSeq):
                for camIdx in range(numCam):
                    outputResolution = self._get_output_resolution(batchDict, batchIdx=batchIdx, cameraIdx=camIdx)
                    # `inputImage` has shape [Ch, H, W]
                    inputImage = imageList[batchIdx][timeIdx][camIdx]
                    self._check_image(inputImage, shape=imageShape)
                    # `ouutputImage` has shape [Ch, H, W]
                    outputImage = self._unified_cuda_process(
                        inputImage.unsqueeze(0),
                        inputCoordMap[0, ...],
                        inputCoordMap[1, ...],
                        cudaProcessor,
                        outputResolution.height,
                        outputResolution.width,
                    ).squeeze(0)
                    self._check_image(outputImage, shape=(outputResolution.height, outputResolution.width))
                    imageList[batchIdx][timeIdx][camIdx] = outputImage.to(
                        dtype=self._IMAGE_OUTPUT_DTYPE, memory_format=self._IMAGE_MEMORY_FORMAT
                    )

    def _unified_cuda_process(
        self,
        image: torch.Tensor,
        xMap: torch.Tensor,
        yMap: torch.Tensor,
        cudaProcessor: CudaUnifiedImageProcessing,
        outputHeight: int,
        outputWidth: int,
    ) -> torch.Tensor:
        r"""Undistort images using an implementation deployed on end devices.

        Args:
            image [torch.Tensor]: batched intput images, with shape
                                  [N, Ch, H, W] and dtype `uint8`, stored in a
                                  CUDA device
            xMap [torch.Tensor]: batched coordinate maps containing
                                 `x`-coordinates of input `image` which pixels
                                 of the output image map to, being of shape
                                 [N, H, W], dtype `float32`, and stored in a
                                 CUDA device contiguously
            yMap [torch.Tensor]: batched coordinate maps containing
                                 `y`-coordinates of input `image` which pixels
                                 of the output image map to, being of shape
                                 [N, H, W], dtype `float32`, and stored in a
                                 CUDA device contiguously
            cudaProcessor [CudaUnifiedImageProcessing]: image processor
            outputHeight [int]: height of the output image
            outputWidth [int]: width of the output image

        Returns:
            output [torch.Tensor]: batched output images, with shape
                                   [N, Ch, outputHeight, outputWidth] and dtype
                                   `uint8`, stored in a CUDA device
        """
        # Method `CudaUnifiedImageProcessing.CudaProcess` requires the shapes of
        # the image tensor and the two mapping tensors to be [N, H, W, Ch] and
        # [N, H, W] respectively, and they needs to have contiguous memory
        # formats. See https://git-core.megvii-inc.com/e2e/mc_dnn_python/-/blob/cd1b63c80a18bee0874208b6e2ed7bdccf37bc81/mc_dnn/src/image_cuda.cpp#L34-68.
        #
        # `inputImage` has shape [N, H, W, Ch], and it is contiguous
        inputImage = image.permute(0, 2, 3, 1).contiguous()
        # `inputMap` has shape [D, N, H, W], and it is contiguous
        # Shape of `output` is [N, H, W, Ch]
        output = cudaProcessor.CudaProcess(
            inputImage,
            outputWidth,  # width of output
            outputHeight,  # height of output
            xMap,
            yMap,
        )
        return output.permute(0, 3, 1, 2)

    @torch.no_grad()
    def __call__(self, batch_dict: Dict) -> None:
        if _use_unified_impl():
            self._call_unified_impl(batch_dict)
        else:
            self._call_torch_impl(batch_dict)
