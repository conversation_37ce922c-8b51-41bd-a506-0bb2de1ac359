import numpy as np

from matplotlib.path import Path

from perceptron.utils.box_np_ops import center_to_corner_box3d
from perceptron_ops.roiaware_pool3d.roiaware_pool3d_utils import points_in_boxes_cpu
from perceptron.data.det3d.modules.pipelines.transformation import BaseAugmentation


class MultiTaskObjectRangeFilter(BaseAugmentation):
    """Filter objects by the range.
    Args:
        point_cloud_range (list[float]): Point cloud range.
    """

    def __init__(
        self,
        task: str,
        filter_pc: bool,
        point_cloud_range,
        box_range,
        mode,
        multiframe=False,
        fov_convexthull=None,
        min_points=None,
    ):
        super().__init__()
        self.task = task
        self.filter_pc = filter_pc
        self.point_cloud_range = np.array(point_cloud_range, dtype=np.float32)
        assert len(self.point_cloud_range) == 6, "point_cloud_range should be a list of 6 floats"
        self.mode = mode
        self.multiframe = multiframe
        self.fov_convexthull = fov_convexthull
        self.box_range = np.array(box_range, dtype=np.float32)
        assert len(self.box_range) == 6, "box_range should be a list of 6 floats"
        self.min_points = min_points

    @staticmethod
    def mask_points_by_range(points, limit_range):
        mask = (
            (points[:, 0] >= limit_range[0])
            & (points[:, 0] <= limit_range[3])
            & (points[:, 1] >= limit_range[1])
            & (points[:, 1] <= limit_range[4])
        )
        return mask

    @staticmethod
    def mask_boxes_outside_range_numpy(task_annos, limit_range, min_num_corners=1):
        boxes = task_annos["gt_boxes"]
        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        corners = center_to_corner_box3d(boxes[:, :3], boxes[:, 3:6], boxes[:, 6], origin=(0.5, 0.5, 0.5), axis=2)
        mask = ((corners >= limit_range[0:3]) & (corners <= limit_range[3:6])).all(axis=2)
        mask = mask.sum(axis=1) >= min_num_corners  # (N)

        return mask

    @staticmethod
    def mask_boxes_outside_fovconvexthull_numpy(task_annos, fov_convexthull):
        boxes = task_annos["gt_boxes"]
        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        points = boxes[:, :2]
        fov_convexthull = np.array(fov_convexthull)
        path = Path(fov_convexthull)
        mask = np.ones(len(boxes)).astype(np.bool)
        for idx, point in enumerate(points):
            mask[idx] = path.contains_point(point)
        return mask

    @staticmethod
    def mask_boxes_by_min_points(boxes, points, min_points):
        mask = points_in_boxes_cpu(points[:, :3], boxes[:, :7])
        points_in_boxes_num = mask.sum(-1)
        return points_in_boxes_num > min_points

    def camera_aug(self, data_dict):
        pass

    def lidar_aug(self, data_dict):
        if not self.filter_pc:
            return
        mask = self.mask_points_by_range(data_dict["points"], self.point_cloud_range)
        data_dict["points"] = data_dict["points"][mask]

    def radar_aug(self, data_dict):
        if not self.filter_pc:
            return
        mask = self.mask_points_by_range(data_dict["radar_points"], self.point_cloud_range)
        data_dict["radar_points"] = data_dict["radar_points"][mask]

    def forward_single(self, data_dict):

        if "points" in data_dict.keys():
            self.lidar_aug(data_dict)
        if "radar_points" in data_dict.keys():
            self.radar_aug(data_dict)
        if data_dict.get("multitask_labels", None):
            if self.task not in data_dict["multitask_labels"]:
                return data_dict
            task_annos = data_dict["multitask_labels"][self.task]
            mask = self.mask_boxes_outside_range_numpy(task_annos, self.box_range)
            if self.fov_convexthull is not None:
                mask &= self.mask_boxes_outside_fovconvexthull_numpy(task_annos, self.fov_convexthull)
            if self.min_points is not None:
                mask &= self.mask_boxes_by_min_points(
                    task_annos["gt_boxes"], task_annos["points"], self.min_points
                )  # lidar训练丢掉稀疏点的GT
            task_annos["gt_boxes"] = task_annos["gt_boxes"][mask]
            if task_annos.get("gt_names", None) is not None:
                task_annos["gt_names"] = task_annos["gt_names"][mask]
            if task_annos.get("gt_labels", None) is not None:
                task_annos["gt_labels"] = task_annos["gt_labels"][mask]
            if task_annos.get("instance_inds", None) is not None:
                task_annos["instance_inds"] = task_annos["instance_inds"][mask]
            if task_annos.get("predict_attribute", None) is not None:
                for key in list(task_annos["predict_attribute"].keys()):
                    task_annos["predict_attribute"][key] = task_annos["predict_attribute"][key][mask]
        return data_dict

    def forward(self, data_dict):

        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class MultiTaskObjectRangeWOMaskFilter(MultiTaskObjectRangeFilter):
    """Filter objects by the range.
    Args:
        point_cloud_range (list[float]): Point cloud range.
    """

    def __init__(
        self,
        class_names: list[str],
        task: str,
        filter_pc: bool,
        point_cloud_range,
        box_range,
        mode,
        multiframe=False,
        fov_convexthull=None,
        min_points=None,
    ):
        super().__init__(task, filter_pc, point_cloud_range, box_range, mode, multiframe, fov_convexthull, min_points)

        self.mask_area_label = class_names.index("masked_area")

    def mask_boxes_outside_range_numpy(self, task_annos, limit_range, min_num_corners=1):
        boxes = task_annos["gt_boxes"]
        labels = task_annos["gt_labels"]
        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        corners = center_to_corner_box3d(boxes[:, :3], boxes[:, 3:6], boxes[:, 6], origin=(0.5, 0.5, 0.5), axis=2)
        mask = ((corners >= limit_range[0:3]) & (corners <= limit_range[3:6])).all(axis=2)
        mask = mask.sum(axis=1) >= min_num_corners  # (N)
        mask = mask | (labels == self.mask_area_label)
        return mask

    def mask_boxes_outside_fovconvexthull_numpy(self, task_annos, fov_convexthull):
        boxes = task_annos["gt_boxes"]
        labels = task_annos["gt_labels"]
        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        points = boxes[:, :2]
        fov_convexthull = np.array(fov_convexthull)
        path = Path(fov_convexthull)
        mask = np.ones(len(boxes)).astype(np.bool)
        for idx, point in enumerate(points):
            mask[idx] = path.contains_point(point)

        mask = mask | (labels == self.mask_area_label)
        return mask
