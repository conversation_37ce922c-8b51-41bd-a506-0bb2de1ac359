# -------------------------------------------------------------------
# *Author       : tanfeiyang <EMAIL>
# *Date         : 2023-02-16 18:28:07
# *LastEditors  : tanfeiyang <EMAIL>
# *LastEditTime : 2023-02-23 18:18:55
# *FilePath     : /Perceptron/perceptron/data/multimodal/pipelines/transformation.py
# *Description  : TODO
# *Copyright (c) 2023 by <EMAIL>, All Rights Reserved.
# -------------------------------------------------------------------

from perceptron_ops.roiaware_pool3d.roiaware_pool3d_utils import points_in_boxes_cpu
from perceptron.data.det3d.modules.pipelines.database_sampler import UnifiedDataBaseSampler
from perceptron.utils.det3d_utils.box_utils import boxes_to_corners_3d
from perceptron.data.det3d.modules.pipelines.database_sampler import DataBaseSampler
from perceptron.utils.box_np_ops import center_to_corner_box3d
from perceptron.data.det3d.utils.functional import robust_crop_img
from mmdet3d.core.bbox import box_np_ops
from refile import smart_open
from matplotlib.path import Path
from torch.nn import Module
import random
import copy
import torch
import numpy as np
import mmcv
from abc import ABC, abstractmethod
from typing import Any, Dict, NewType, Optional

import cv2

cv2.setNumThreads(0)
cv2.ocl.setUseOpenCL(False)


_Affine_Matrix = NewType("_Affine_Matrix", np.array)


def get_geometric_trans(theta_x, theta_y, theta_z, tx, ty, tz):
    """
    get random ego --> ego' rotation, translation matrix
    theta_x: pitch
    theta_y: roll
    theta_z: yaw
    """
    T_e2e = np.zeros(3)

    theta_x = theta_x / 180.0 * np.pi
    theta_y = theta_y / 180.0 * np.pi
    theta_z = theta_z / 180.0 * np.pi
    T_e2e[0] = tx
    T_e2e[1] = ty
    T_e2e[2] = tz

    trans_e2e = np.eye(4)

    # trans_e2e[:3, :3] = R_e2e
    trans_e2e[:3, :3] = rul2rot((theta_x, theta_y, theta_z))
    trans_e2e[:3, 3] = T_e2e

    return trans_e2e


def rul2rot(theta):
    R = np.array(
        [
            [
                np.cos(theta[1]) * np.cos(theta[2]),
                np.sin(theta[0]) * np.sin(theta[1]) * np.cos(theta[2]) - np.sin(theta[2]) * np.cos(theta[0]),
                np.sin(theta[1]) * np.cos(theta[0]) * np.cos(theta[2]) + np.sin(theta[0]) * np.sin(theta[2]),
            ],
            [
                np.sin(theta[2]) * np.cos(theta[1]),
                np.sin(theta[0]) * np.sin(theta[1]) * np.sin(theta[2]) + np.cos(theta[0]) * np.cos(theta[2]),
                np.sin(theta[1]) * np.sin(theta[2]) * np.cos(theta[0]) - np.sin(theta[0]) * np.cos(theta[2]),
            ],
            [-np.sin(theta[1]), np.sin(theta[0]) * np.cos(theta[1]), np.cos(theta[0]) * np.cos(theta[1])],
        ]
    )

    return R


class BaseAugmentation(ABC, Module):
    r"""This Base Augmentation class is used to define the interface for all.

    Example:
        >>> class CustomObject(BaseAugmentation):
        ...     def __init__(self):
        ...         self.x = torch.rand(2, 2)
        ...     def camera_aug(self, data_dict : Dict, *args : Any, **kwargs : Any):
        ...         ...
        ...     def lidar_aug(self, data_dict : Dict, *args : Any, **kwargs : Any):
        ...         ...
        ...     def radar_aug(self, data_dict : Dict, *args : Any, **kwargs : Any):
        ...         ...
        ...     def forward(self, data_dict : Dict, *args : Any, **kwargs : Any):
        ...         ....
        >>> custom_object = CustomObject()
    """

    def __init__(self, **kwargs: Any):
        ABC.__init__(self)
        Module.__init__(self)
        self.aug_conf: Optional[Dict] = kwargs

    @abstractmethod
    def camera_aug(self, *args: Any, **kwargs: Any) -> Any:
        raise NotImplementedError("You must rewrite this method!")

    @abstractmethod
    def lidar_aug(self, *args: Any, **kwargs: Any) -> Any:
        raise NotImplementedError("You must rewrite this method!")

    @abstractmethod
    def radar_aug(self, *args: Any, **kwargs: Any) -> Any:
        raise NotImplementedError("You must rewrite this method!")

    def gt_aug(self, *args: Any, **kwargs: Any) -> Any:
        # TODO: @tanfeiyang  -> For Multi-Task.
        raise NotImplementedError("TODO!")

    def forward(self, data_dict: Optional[Dict], *args: Any, **kwargs: Any) -> Any:
        if "imgs" in data_dict.keys():
            self.camera_aug(data_dict)
        if "points" in data_dict.keys():
            self.lidar_aug(data_dict)
        if "radar_points" in data_dict.keys():
            self.radar_aug(data_dict)

    def __repr__(self):
        return "Class name is : {}. ".format(self._get_name()) + "Its aug method paras are : {}.".format(self.aug_conf)


class BevAffineTransformation(BaseAugmentation):
    r"""This class is used to bird view augmentation only for camera.

    Example:

        >>> bda_aug_cfg=dict(
        ...     rot_lim=(-22.5 * 2, 22.5 * 2), # Bev Ratation
        ...     scale_lim=(0.90, 1.10),        # Bev Scale
        ...     trans_lim=(-4, 4),             # Bev Translation
        ...     flip_dx_ratio=0.5,             # Bev Flip of X
        ...     flip_dy_ratio=0.5,             # Bev Flip of Y
        ... )
        >>> bda_aug = BevAffineTransformation(aug_conf=bda_aug_cfg)
    """

    def __init__(
        self,
        aug_conf: Dict[str, float],
        mode: str,
        with_trans_z: bool = False,
        multiframe=False,
    ):
        r"""This method is used to initialize the class.

        Args:
            aug_conf {Dict}:  Augmentation configuration.
            mode {str}: "train" or "val"
            with_trans_z {bool}: use z-trans aug.

        Return:
            None
        """
        super().__init__(aug_conf=aug_conf)
        self.aug_conf = aug_conf
        self.mode = mode
        self.with_trans_z = with_trans_z
        self.multiframe = multiframe

    def sample_augs(self):
        rotate_bda, scale_bda = 0, 1.0
        trans_x, trans_y = 0, 0
        if self.with_trans_z:
            trans_z = 0
        flip_dx, flip_dy = False, False

        if self.mode == "train":
            try:
                rotate_bda = np.random.uniform(*self.aug_conf["rot_lim"])
                scale_bda = np.random.uniform(*self.aug_conf["scale_lim"])
                flip_dx = np.random.uniform() < self.aug_conf["flip_dx_ratio"]
                flip_dy = np.random.uniform() < self.aug_conf["flip_dy_ratio"]
                if "trans_lim" in self.aug_conf:
                    low, high = self.aug_conf["trans_lim"]
                    trans_x = np.random.rand() * (high - low) + low
                    trans_y = np.random.rand() * (high - low) + low
                    if self.with_trans_z:
                        trans_z = np.random.rand() * (high - low) + low
            except KeyError:
                raise KeyError(
                    f"You must set the aug_conf for {self._get_name()} correctly! \
                    Current keys are : {self.aug_conf.keys()}"
                )
        if self.with_trans_z:
            return rotate_bda, scale_bda, (trans_x, trans_y, trans_z), flip_dx, flip_dy
        return rotate_bda, scale_bda, (trans_x, trans_y), flip_dx, flip_dy

    def _bev_transform(self, gt_boxes, rotate_angle, scale_ratio, trans_bda, flip_dx, flip_dy):
        rotate_angle = np.array(rotate_angle / 180 * np.pi, dtype=np.float32)
        rot_sin = np.sin(rotate_angle)
        rot_cos = np.cos(rotate_angle)
        rot_mat = np.array([[rot_cos, -rot_sin, 0], [rot_sin, rot_cos, 0], [0, 0, 1]], dtype=np.float32)
        # scale_mat = np.eye((3)) * scale_ratio
        scale_mat = np.array([[scale_ratio, 0, 0], [0, scale_ratio, 0], [0, 0, scale_ratio]])
        flip_mat = np.eye((3), dtype=np.float32)
        if flip_dx:
            flip_mat = flip_mat @ np.array([[-1, 0, 0], [0, 1, 0], [0, 0, 1]], dtype=np.float32)
        if flip_dy:
            flip_mat = flip_mat @ np.array([[1, 0, 0], [0, -1, 0], [0, 0, 1]], dtype=np.float32)
        rot_mat = flip_mat @ (scale_mat @ rot_mat)

        if gt_boxes.shape[0] > 0:
            gt_boxes[:, :3] = (rot_mat @ np.expand_dims(gt_boxes[:, :3], -1)).squeeze(-1)
            gt_boxes[:, 0] += trans_bda[0]
            gt_boxes[:, 1] += trans_bda[1]
            if self.with_trans_z:
                gt_boxes[:, 2] += trans_bda[2]
            gt_boxes[:, 3:6] *= scale_ratio
            gt_boxes[:, 6] += rotate_angle
            if flip_dx:
                gt_boxes[:, 6] = 2 * np.arcsin(np.array(1.0, dtype=np.float32)) - gt_boxes[:, 6]
            if flip_dy:
                gt_boxes[:, 6] = -gt_boxes[:, 6]
            if gt_boxes.shape[1] > 7:
                gt_boxes[:, 7:] = (rot_mat[:2, :2] @ np.expand_dims(gt_boxes[:, 7:], -1)).squeeze(-1)
        bda_mat = np.zeros((4, 4), dtype=np.float32)
        bda_mat[3, 3] = 1
        bda_mat[0, 3] = trans_bda[0]
        bda_mat[1, 3] = trans_bda[1]
        if self.with_trans_z:
            bda_mat[2, 3] = trans_bda[2]
        bda_mat[:3, :3] = rot_mat

        return gt_boxes, bda_mat

    def camera_aug(self, data_dict: Dict, transform_mat: _Affine_Matrix) -> Any:
        pass

    def lidar_aug(self, data_dict: Dict, transform_mat: _Affine_Matrix) -> Any:
        homogeneous_point = np.ones((data_dict["points"].shape[0], 4))
        homogeneous_point[:, :3] = data_dict["points"][:, :3]
        homogeneous_point_transform = (transform_mat @ homogeneous_point.T).T
        data_dict["points"][:, :3] = homogeneous_point_transform[:, :3]

    def radar_aug(self, data_dict: Dict, transform_mat: _Affine_Matrix) -> Any:
        if data_dict["radar_mode"] == "mlp":
            homogeneous_radar_point = np.ones((data_dict["radar_points"].shape[0], 4))
            homogeneous_radar_point[:, :3] = data_dict["radar_points"][:, :3]
            homogeneous_radar_point_transform = (transform_mat @ homogeneous_radar_point.T).T
            data_dict["radar_points"][:, :3] = homogeneous_radar_point_transform[:, :3]
            # torch.nn.Moduleel trans
            data_dict["radar_points"][:, 6:8] = (transform_mat[:2, :2] @ data_dict["radar_points"][:, 6:8].T).T
        else:
            radar_points = data_dict["radar_points"]
            radar_points[:, :3] = (transform_mat[:3, :3] @ radar_points[:, :3].T).T
            radar_points[:, 6:8] = (transform_mat[:2, :2] @ radar_points[:, 6:8].T).T
            radar_points[:, 8:10] = (transform_mat[:2, :2] @ radar_points[:, 8:10].T).T
            data_dict["radar_points"] = radar_points

    def forward_single(self, data_dict: Dict) -> Dict:
        rotate_bda, scale_bda, trans_bda, flip_dx, flip_dy = self.sample_augs()
        gt_boxes = data_dict["gt_boxes"]
        gt_boxes, transform_mat = self._bev_transform(gt_boxes, rotate_bda, scale_bda, trans_bda, flip_dx, flip_dy)
        if data_dict.get("imgs", None) is not None:
            self.camera_aug(data_dict, transform_mat)
        if data_dict.get("points", None) is not None:
            self.lidar_aug(data_dict, transform_mat)
        if data_dict.get("radar_points", None) is not None:
            self.radar_aug(data_dict, transform_mat)
        data_dict["bda_mat"] = transform_mat
        return data_dict

    def forward(self, data_dict: Dict) -> Dict:
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class ImageAffineTransformation(BaseAugmentation):
    r"""This class is used to bird view augmentation for different modal.

    Example:

        >>> ida_aug_conf = {
        ...    "final_dim": final_dim,         # Final image shape
        ...    "resize_lim": (0.772, 1.10),    # Image resize scale
        ...    "H": H,                         # Original image height
        ...    "W": W,                         # Original image width
        ...    "rand_flip": True,              # Image flip
        ...    "bot_pct_lim": (0.0, 0.0),      # Image crop
        ...    "rot_lim": (-5.4, 5.4),         # Image Rotation
        >>> }
        ida_aug = ImageAffineTransformation(aug_conf=ida_aug_cfg)
    """

    def __init__(
        self,
        aug_conf: Dict[str, float],
        camera_names: list,
        mode: str,
        img_norm=False,
        img_conf={},
        gpu_aug: bool = False,
        multiframe=False,
    ):
        r"""This method is used to initialize the class.

        Args:
            aug_conf {Dict}:  Augmentation configuration.

        Return:
            None
        """
        super().__init__(aug_conf=aug_conf)
        self.ida_aug_conf_dict = self._init_ida_aug_conf(aug_conf, camera_names)
        self.mode = mode
        self.img_norm = img_norm
        self.camera_names = camera_names
        self.gpu_aug = gpu_aug
        self.multiframe = multiframe
        if self.img_norm:
            assert (
                img_conf and "img_mean" in img_conf and "img_std" in img_conf and "to_rgb" in img_conf
            ), "invalid image conf input format."
            self.img_norm = img_norm
            self.img_mean = np.array(img_conf["img_mean"], np.float32)
            self.img_std = np.array(img_conf["img_std"], np.float32)
            self.to_rgb = img_conf["to_rgb"]

    def _init_ida_aug_conf(self, aug_conf, camera_names):
        """This function designed for different camera equipped with different aug setting.

        Args:
            >>> camera_key = ["camera_front_120", "camera_front_left_120"]

            >>> ida_aug_conf = {
            ...    "final_dim": (512, 1408),                        # Final image shape
            ...    "resize_lim": [(0.772, 1.10), (0.386, 0.55)],    # Image resize scale: index 0 for camera_key[0]...
            ...    "H": 1080,                                       # Original image height
            ...    "W": 1960,                                       # Original image width
            ...    "rand_flip": True,                               # Image flip
            ...    "bot_pct_lim": [(0.0, 0.0), (0.0, 0.2)],         # Image crop: index 0 for camera_key[0]...
            ...    "rot_lim": (-5.4, 5.4),                          # Image Rotation
            >>> }
        Return:
            ida_aug_conf = {
                "camera_front_120" :{
                    "final_dim": (512, 1408),
                    "resize_lim": (0.772, 1.10),
                    ...
                }
                "camera_front_left_120":{
                    "final_dim": (512, 1408),
                    "resize_lim": (0.386, 0.55),
                }
            }
        """
        ida_aug_conf_dict = {}
        for camera_idx, camera_name in enumerate(camera_names):
            cur_camera_aug = {}
            for aug_key in aug_conf:
                if isinstance(aug_conf[aug_key], list):
                    print(aug_conf[aug_key], camera_names)
                    assert len(aug_conf[aug_key]) == len(
                        camera_names
                    ), f"The lenth of {aug_key} should be equal with camera_names'! "
                    cur_camera_aug[aug_key] = aug_conf[aug_key][camera_idx]
                elif isinstance(aug_conf[aug_key], (tuple, int, float, bool)):
                    cur_camera_aug[aug_key] = aug_conf[aug_key]
                else:
                    raise TypeError(
                        "{} in ida_aug_conf should be list or tuple! But got {} instead!".format(
                            aug_key, type(aug_conf[aug_key])
                        )
                    )
            ida_aug_conf_dict[camera_name] = cur_camera_aug
        return ida_aug_conf_dict

    def _get_crop_hw(self, newH, fH, newW, fW, bot_pct_lim):
        if self.mode == "train":
            crop_h = int((1 - np.random.uniform(*bot_pct_lim)) * newH) - fH
            crop_w = int(np.random.uniform(0, max(0, newW - fW)))
        else:
            crop_h = int((1 - np.mean(bot_pct_lim)) * newH) - fH
            crop_w = int(max(0, newW - fW) / 2)

        return crop_h, crop_w

    def sample_augs(self, camera_name):
        """Generate ida augmentation values based on ida_config."""
        H, W = self.ida_aug_conf_dict[camera_name]["H"], self.ida_aug_conf_dict[camera_name]["W"]
        fH, fW = self.ida_aug_conf_dict[camera_name]["final_dim"]
        resize_lim = self.ida_aug_conf_dict[camera_name]["resize_lim"]
        bot_pct_lim = self.ida_aug_conf_dict[camera_name]["bot_pct_lim"]
        if self.mode == "train":
            resize = np.random.uniform(*resize_lim)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h, crop_w = self._get_crop_hw(newH, fH, newW, fW, bot_pct_lim)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = True if self.ida_aug_conf_dict[camera_name]["rand_flip"] and np.random.choice([0, 1]) else False
            rotate_ida = np.random.uniform(*self.ida_aug_conf_dict[camera_name]["rot_lim"])
        else:
            resize = max(fH / H, fW / W)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h, crop_w = self._get_crop_hw(newH, fH, newW, fW, bot_pct_lim)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate_ida = 0
        return resize, resize_dims, crop, flip, rotate_ida

    def sample_augs_hw(self, camera_name):  # 处理 h w 方向 resize ratio 不一样的情况
        """Generate ida augmentation values based on ida_config."""
        H, W = self.ida_aug_conf_dict[camera_name]["H"], self.ida_aug_conf_dict[camera_name]["W"]
        fH, fW = self.ida_aug_conf_dict[camera_name]["final_dim"]
        resize_lim_h, resize_lim_w = self.ida_aug_conf_dict[camera_name]["resize_lim"]

        bot_pct_lim = self.ida_aug_conf_dict[camera_name]["bot_pct_lim"]
        if self.mode == "train":
            resize_h = np.random.uniform(*resize_lim_h)
            resize_w = np.random.uniform(*resize_lim_w)
            resize = (resize_h, resize_w)
            resize_dims = (int(W * resize_w), int(H * resize_h))
            newW, newH = resize_dims
            crop_h, crop_w = self._get_crop_hw(newH, fH, newW, fW, bot_pct_lim)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = True if self.ida_aug_conf_dict[camera_name]["rand_flip"] and np.random.choice([0, 1]) else False
            rotate_ida = np.random.uniform(*self.ida_aug_conf_dict[camera_name]["rot_lim"])
        else:
            # resize = max(fH / H, fW / W)
            # resize_dims = (int(W * resize), int(H * resize))
            resize_h = (resize_lim_h[0] + resize_lim_h[1]) / 2
            resize_w = (resize_lim_w[0] + resize_lim_w[1]) / 2
            resize = (resize_h, resize_w)
            resize_dims = (int(W * resize_w), int(H * resize_h))
            newW, newH = resize_dims
            crop_h, crop_w = self._get_crop_hw(newH, fH, newW, fW, bot_pct_lim)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate_ida = 0
        return resize, resize_dims, crop, flip, rotate_ida

    def get_ida_mat(self, resize, resize_dims, crop, flip, rotate, img_warp_matrix=None):
        ida_rot = np.eye(2, dtype=np.float32)
        ida_tran = np.zeros(2, dtype=np.float32)
        # post-homography transformation

        ida_rot *= resize
        # if not (isinstance(resize, list) or isinstance(resize, tuple)):
        #     # print('!'*10, resize)
        #     ida_rot *= resize
        # else:
        #     h_ratio, w_ratio = resize
        #     new_resize = (w_ratio, h_ratio)
        #     ida_rot *= new_resize
        ida_tran -= np.array(crop[:2], dtype=np.float32)

        if flip:
            A = np.array([[-1, 0], [0, 1]], dtype=np.float32)
            b = np.array([crop[2] - crop[0], 0], dtype=np.float32)
            ida_rot = np.matmul(A, ida_rot)
            ida_tran = np.matmul(A, ida_tran) + b

        def _get_rot(h):
            return np.array(
                [
                    [np.cos(h), np.sin(h)],
                    [-np.sin(h), np.cos(h)],
                ],
                dtype=np.float32,
            )

        A = _get_rot(rotate / 180 * np.pi)
        b = np.array([crop[2] - crop[0], crop[3] - crop[1]], dtype=np.float32) / 2
        b = np.matmul(A, -b) + b
        ida_rot = np.matmul(A, ida_rot)
        ida_tran = np.matmul(A, ida_tran) + b
        ida_mat = np.zeros((4, 4), dtype=np.float32)
        ida_mat[3, 3] = 1
        ida_mat[2, 2] = 1
        ida_mat[:2, :2] = ida_rot
        # ida_mat[:2, 3] = ida_tran
        ida_mat[:2, 2] = ida_tran

        if img_warp_matrix is not None:  # not None
            # 3维ida和4维ida的定义不同, ida_mat的第3维是depth
            ida_3dim = np.zeros((3, 3), dtype=np.float32)
            ida_3dim[2, 2] = 1
            ida_3dim[:2, :2] = ida_rot
            ida_3dim[:2, 2] = ida_tran
            ida_3dim = ida_3dim @ img_warp_matrix
            ida_mat[:2, :2] = ida_3dim[:2, :2]
            ida_mat[:2, 3] = ida_3dim[:2, 2]

        return ida_mat

    def _img_transform(self, img, resize, resize_dims, crop, flip, rotate):
        if self.mode == "train":
            interpolate_candidate = [
                cv2.INTER_LINEAR,
                cv2.INTER_NEAREST,
                cv2.INTER_AREA,
                cv2.INTER_CUBIC,
                cv2.INTER_LANCZOS4,
            ]
            index = np.random.randint(0, 5)
            interpolation = interpolate_candidate[index]
        else:
            interpolation = cv2.INTER_LINEAR
        img = cv2.resize(img, resize_dims, interpolation=interpolation)
        img = robust_crop_img(img, crop)
        if flip:
            img = cv2.flip(img, 1)
        height, width, _ = img.shape
        center = (width // 2, height // 2)
        M = cv2.getRotationMatrix2D(center, rotate, 1.0)
        img = cv2.warpAffine(img, M, (width, height))
        if self.img_norm:
            img = mmcv.imnormalize(img, self.img_mean, self.img_std, self.to_rgb)
            return img.transpose(2, 0, 1).astype(np.float32)
        else:
            return img.transpose(2, 0, 1)

    def camera_aug(self, data_dict):
        aug_imgs, ida_mats = [], []
        ida_mats_detail = []
        assert len(data_dict["imgs"]) == len(self.camera_names)
        for img, camera_name in zip(data_dict["imgs"], self.camera_names):
            # resize, resize_dims, crop, flip, rotate_ida = self.sample_augs(camera_name)
            resize, resize_dims, crop, flip, rotate_ida = self.sample_augs_hw(camera_name)
            if not self.gpu_aug:
                img = self._img_transform(
                    img, resize=resize, resize_dims=resize_dims, crop=crop, flip=flip, rotate=rotate_ida
                )
            ida_mat = self.get_ida_mat(resize=resize, resize_dims=resize_dims, crop=crop, flip=flip, rotate=rotate_ida)
            aug_imgs.append(img)
            ida_mats.append(ida_mat)
            ida_mats_detail.append(
                {
                    "resize": resize,
                    "resize_dims": resize_dims,
                    "crop": crop,
                    "flip": flip,
                    "rotate": rotate_ida,
                }
            )
        return aug_imgs, np.stack(ida_mats), ida_mats_detail

    def lidar_aug(self):
        pass

    def radar_aug(self):
        pass

    def forward_single(self, data_dict):
        if "imgs" in data_dict:
            aug_imgs, ida_mats, ida_mats_detail = self.camera_aug(data_dict)
            data_dict["imgs"] = aug_imgs
            data_dict["ida_mats"] = ida_mats
            data_dict["ida_mats_detail"] = ida_mats_detail
        return data_dict

    def forward(self, data_dict):
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class MultiFrameImageAffineTransformation(ImageAffineTransformation):
    """
    This class is implemented for multi frame camera view augmentation.
    Augmentation is the same for one camera across different frames.
    """

    def camera_aug(self, sample_queue):
        queue_len = len(sample_queue)

        ida_mats = [[] for i in range(queue_len)]
        ida_mats_detail = []
        assert len(sample_queue[0]["imgs"]) == len(self.camera_names)
        for i in range(len(self.camera_names)):
            resize_lim = self.ida_aug_conf_dict[self.camera_names[i]]["resize_lim"]
            if isinstance(resize_lim[0], float):
                resize, resize_dims, crop, flip, rotate_ida = self.sample_augs(self.camera_names[i])
            elif isinstance(resize_lim[0], tuple):
                resize, resize_dims, crop, flip, rotate_ida = self.sample_augs_hw(self.camera_names[i])
            else:
                raise NotImplementedError
            ida_mats_detail.append(
                {
                    "resize": resize,
                    "resize_dims": resize_dims,
                    "crop": crop,
                    "flip": flip,
                    "rotate": rotate_ida,
                }
            )
            ida_mat = self.get_ida_mat(resize=resize, resize_dims=resize_dims, crop=crop, flip=flip, rotate=rotate_ida)
            for j in range(queue_len):
                if not self.gpu_aug:
                    sample_queue[j]["imgs"][i] = self._img_transform(
                        sample_queue[j]["imgs"][i],
                        resize=resize,
                        resize_dims=resize_dims,
                        crop=crop,
                        flip=flip,
                        rotate=rotate_ida,
                    )
                else:
                    sample_queue[j]["imgs"][i] = sample_queue[j]["imgs"][i].transpose(2, 0, 1)
                ida_mats[j].append(ida_mat)

        for j in range(queue_len):
            sample_queue[j]["ida_mats"] = np.stack(ida_mats[j])
            sample_queue[j]["ida_mats_detail"] = ida_mats_detail
        return sample_queue

    def forward(self, sample_queue):
        if not self.multiframe:
            raise NotImplementedError
        if "imgs" in sample_queue[0]:
            sample_queue = self.camera_aug(sample_queue)
        return sample_queue


class MultiSizeImageAffineTransformation(MultiFrameImageAffineTransformation):
    def __init__(
        self, aug_conf: list, camera_names: list, mode: str, img_norm=False, img_conf={}, map_conf=None, multiframe=True
    ):
        """This method is used to initialize the class.

        Args:
            aug_conf: List[Dict]:  Augmentation configuration of multi size images.
            camera_names: List[List]: Camera lists of applying different augmentation.
            mode: 'train' or any other mode as test.
            img_norm: image normalize mode.
            img_conf: image normalize config.
            map_conf: dict(
                ego_region, distance in ego coordinate, [front, back, left, right, down, up], in unit m
                mask_shape_xyz,
                mask_shape_uv,
                bezier_param
            ) config to translate map label to fit image augmentation

        Return:
            None
        """
        super(ImageAffineTransformation, self).__init__(aug_conf=aug_conf)
        self.multiframe = multiframe
        self.ida_aug_conf_dict = self._init_ida_aug_conf(aug_conf[0], camera_names[0])
        self.mode = mode
        self.img_norm = img_norm
        self.camera_names = camera_names[0]
        self.multi_size_keys = []
        self.image_id_matching = dict()
        for idx, key in enumerate(self.camera_names):
            self.image_id_matching[key] = dict(img_id=idx, multisize_id=0)
            # camera order should be the same with raw image for first set of augmentation
        for idx, camera_extra_keys in enumerate(camera_names[1:]):
            extra_name = aug_conf[idx + 1].pop("extra_name")
            self.multi_size_keys.append(extra_name)
            extra_ida_conf = self._init_ida_aug_conf(aug_conf[idx + 1], camera_extra_keys)
            for cam_name in camera_extra_keys:
                extra_cam_name = f"{extra_name}_{cam_name}"
                if cam_name in self.camera_names:
                    self.image_id_matching[extra_cam_name] = dict(
                        img_id=self.camera_names.index(cam_name), multisize_id=idx + 1, key=extra_name
                    )
                else:
                    self.image_id_matching[extra_cam_name] = dict(img_id=len(self.camera_names), multisize_id=idx + 1)
                    # camera order should continue as raw image input if camera name not in first augmentation set
                self.camera_names.append(extra_cam_name)
                self.ida_aug_conf_dict[extra_cam_name] = extra_ida_conf[cam_name]
        if self.img_norm:
            assert (
                img_conf and "img_mean" in img_conf and "img_std" in img_conf and "to_rgb" in img_conf
            ), "invalid image conf input format."
            self.img_norm = img_norm
            self.img_mean = np.array(img_conf["img_mean"], np.float32)
            self.img_std = np.array(img_conf["img_std"], np.float32)
            self.to_rgb = img_conf["to_rgb"]

    def camera_aug(self, sample_queue: list) -> list:
        """
        transform from original image to different sizes with extra prefix.
        """

        queue_len = len(sample_queue)

        imgs = [[] for i in range(queue_len)]
        ida_mats = [[] for i in range(queue_len)]

        num_multi_size = len(self.multi_size_keys)
        extra_imgs = [[[] for i in range(queue_len)] for i in range(num_multi_size)]
        extra_ida_mats = [[[] for i in range(queue_len)] for i in range(num_multi_size)]
        for i in range(len(self.camera_names)):
            resize, resize_dims, crop, flip, rotate_ida = self.sample_augs(self.camera_names[i])
            image_id = self.image_id_matching[self.camera_names[i]]["img_id"]
            multisize_id = self.image_id_matching[self.camera_names[i]]["multisize_id"]
            for j in range(queue_len):
                img_aug, ida_mat = self._img_transform(
                    sample_queue[j]["imgs"][image_id],
                    resize=resize,
                    resize_dims=resize_dims,
                    crop=crop,
                    flip=flip,
                    rotate=rotate_ida,
                )
                if multisize_id == 0:
                    imgs[j].append(img_aug)
                    ida_mats[j].append(ida_mat)
                else:
                    extra_imgs[multisize_id - 1][j].append(img_aug)
                    extra_ida_mats[multisize_id - 1][j].append(ida_mat)
        for j in range(queue_len):
            sample_queue[j]["imgs"] = imgs[j]
            sample_queue[j]["ida_mats"] = np.stack(ida_mats[j])
            for i in range(num_multi_size):
                sample_queue[j][f"{self.multi_size_keys[i]}_imgs"] = extra_imgs[i][j]
                sample_queue[j][f"{self.multi_size_keys[i]}_idas"] = np.stack(extra_ida_mats[i][j])
        return sample_queue

    def forward(self, sample_queue: list):
        if not self.multiframe:
            raise NotImplementedError
        if "imgs" in sample_queue[0]:
            sample_queue = self.camera_aug(sample_queue)
        return sample_queue


class MultiFrameImageAffineTransformationWarp(ImageAffineTransformation):
    """
    This class is implemented for multi frame camera view augmentation.
    Augmentation is the same for one camera across different frames.
    """

    def __init__(
        self,
        target_extrinsic: str = None,
        one_stage=True,
        map_aug: dict = None,
        map_lidar_range: list = None,
        **kwargs,
    ):
        r"""This method is used to initialize the class.

        Args:
            aug_conf {Dict}:  Augmentation configuration.

        Return:
            None
        """
        super().__init__(**kwargs)
        if target_extrinsic is not None:
            self.target_extrinsic = dict(np.load(smart_open(target_extrinsic, "rb")))
        else:
            self.target_extrinsic = None
        self.one_stage = one_stage  # multi car use two stage
        self.map_aug = map_aug
        self.map_lidar_range = map_lidar_range

    def get_aug_geometric_trans(self):
        aug_dict = {}
        for k in self.map_aug:
            aug_dict[k] = np.random.uniform(*self.map_aug[k])
        aug_geom_trans_matrix = get_geometric_trans(
            *[aug_dict[k] for k in ["theta_x", "theta_y", "theta_z", "px", "py", "pz"]]
        )
        return aug_geom_trans_matrix

    def aug_map_gt(self, map_gt, aug_geom_trans_matrix):
        # import matplotlib.pyplot as plt
        # plt.figure(figsize=(3, 10))  # 设置图像大小
        # # 设置x轴和y轴的范围
        # plt.axis([-0, 1, 0, 1])

        # aug_geom_trans_matrix = np.eye(4)
        min_lidar = np.array(self.map_lidar_range[:3]).reshape(1, 1, 1, 3)
        max_lidar = np.array(self.map_lidar_range[3:]).reshape(1, 1, 1, 3)
        lidar_range = max_lidar - min_lidar
        aug_geom_trans_matrix = aug_geom_trans_matrix.reshape(1, 1, 1, 4, 4)

        # for cls in map_gt:
        #     for line in map_gt[cls]:
        #         line = np.array(line)
        #         x = line[0, :, 0]
        #         y = line[0, :, 1]
        # plt.plot(x, y)

        for idx in map_gt:
            if not map_gt[idx]:
                continue
            cls_gt = np.array(map_gt[idx])
            cls_gt = cls_gt * lidar_range + min_lidar

            pad = np.ones((*cls_gt.shape[:-1], 1))
            cls_gt = np.concatenate([cls_gt, pad], axis=-1)
            cls_gt = np.expand_dims(cls_gt, axis=-1)
            cls_gt = aug_geom_trans_matrix @ cls_gt
            cls_gt = cls_gt.squeeze(-1)[..., :-1]

            cls_gt = (cls_gt - min_lidar) / lidar_range

            # plt.plot(x, y)

            map_gt[idx] = cls_gt.tolist()
        return map_gt

    def camera_aug(self, sample_queue):
        queue_len = len(sample_queue)

        ida_mats = [[] for i in range(queue_len)]
        ida_mats_detail = []
        assert len(sample_queue[0]["imgs"]) == len(self.camera_names)
        # img_warp_matrix_dict = {}
        aug_flag = np.random.uniform(0, 1) > 0.0
        if self.mode == "train" and self.map_aug is not None and aug_flag:
            aug_geom_trans_matrix = self.get_aug_geometric_trans()

        for i in range(len(self.camera_names)):
            resize, resize_dims, crop, flip, rotate_ida = self.sample_augs(self.camera_names[i])
            # resize, resize_dims, crop, flip, rotate_ida = self.sample_augs_hw(self.camera_names[i])
            ida_mats_detail.append(
                {
                    "resize": resize,
                    "resize_dims": resize_dims,
                    "crop": crop,
                    "flip": flip,
                    "rotate": rotate_ida,
                }
            )
            img_warp_matrix = None
            if self.target_extrinsic is not None:
                campose_geom_trans_matrix = self.target_extrinsic[self.camera_names[i]] @ np.linalg.inv(
                    sample_queue[0]["lidar2imgs"][i]
                )
                img_warp_matrix = calc_warp_between_imgs(
                    campose_geom_trans_matrix,
                    np.eye(3),  # sample_queue[0]["tran_mats_dict"]["cam2img"][i],
                    sample_queue[0]["lidar2imgs"][i],  # trans_ego2cam[-1],
                    sample_queue[0]["imgs"][i].shape[1],
                    sample_queue[0]["imgs"][i].shape[0],
                )
                # img_warp_matrix_dict[self.camera_names[i]] = img_warp_matrix

            # tmp dwj
            ida_mat = self.get_ida_mat(
                resize=resize,
                resize_dims=resize_dims,
                crop=crop,
                flip=flip,
                rotate=rotate_ida,
                img_warp_matrix=img_warp_matrix,
            )

            for j in range(queue_len):
                if self.mode == "train" and self.map_aug is not None and aug_flag:
                    aug_compose_geom_trans_matrix = (
                        self.target_extrinsic[self.camera_names[i]] @ aug_geom_trans_matrix
                    ) @ np.linalg.inv(sample_queue[0]["lidar2imgs"][i])
                    aug_img_warp_matrix = calc_warp_between_imgs(
                        aug_compose_geom_trans_matrix,
                        np.eye(3),  # sample_queue[0]["tran_mats_dict"]["cam2img"][i],
                        sample_queue[0]["lidar2imgs"][i],  # trans_ego2cam[-1],
                        sample_queue[0]["imgs"][i].shape[1],
                        sample_queue[0]["imgs"][i].shape[0],
                    )
                    img_warp_matrix = aug_img_warp_matrix
                    if i == 0:  # 多 cam, gt 只变一次
                        map_gt = sample_queue[j]["map_gt"]
                        aug_map_gt = self.aug_map_gt(map_gt, aug_geom_trans_matrix)
                        sample_queue[j]["map_gt"] = aug_map_gt

                if not self.one_stage:
                    if self.target_extrinsic is not None:
                        im_h, im_w, _ = sample_queue[j]["imgs"][i].shape
                        sample_queue[j]["imgs"][i] = cv2.warpPerspective(
                            sample_queue[j]["imgs"][i], img_warp_matrix, (im_w, im_h)
                        )
                if not self.gpu_aug:
                    sample_queue[j]["imgs"][i] = self._img_transform(
                        sample_queue[j]["imgs"][i],
                        resize=resize,
                        resize_dims=resize_dims,
                        crop=crop,
                        flip=flip,
                        rotate=rotate_ida,
                    )
                else:
                    sample_queue[j]["imgs"][i] = sample_queue[j]["imgs"][i].transpose(2, 0, 1)
                ida_mats[j].append(ida_mat)

        # np.savez("/home/<USER>/perceptron/Perceptron/img_warp_matrix_dict.npz", **img_warp_matrix_dict)

        for j in range(queue_len):
            sample_queue[j]["ida_mats"] = np.stack(ida_mats[j])
            sample_queue[j]["ida_mats_detail"] = ida_mats_detail
        return sample_queue

    def forward(self, sample_queue):
        if not self.multiframe:
            raise NotImplementedError
        if "imgs" in sample_queue[0]:
            sample_queue = self.camera_aug(sample_queue)
        return sample_queue


class ObjectRangeFilter(BaseAugmentation):
    """Filter objects by the range.
    Args:
        point_cloud_range (list[float]): Point cloud range.
    """

    def __init__(
        self, point_cloud_range, mode, multiframe=False, fov_convexthull=None, box_range=None, min_points=None
    ):
        super().__init__()
        self.point_cloud_range = np.array(point_cloud_range, dtype=np.float32)
        self.mode = mode
        self.multiframe = multiframe
        self.fov_convexthull = fov_convexthull
        self.box_range = np.array(box_range, dtype=np.float32) if box_range is not None else self.point_cloud_range
        self.min_points = min_points

    @staticmethod
    def mask_points_by_range(points, limit_range):
        mask = (
            (points[:, 0] >= limit_range[0])
            & (points[:, 0] <= limit_range[3])
            & (points[:, 1] >= limit_range[1])
            & (points[:, 1] <= limit_range[4])
        )
        return mask

    @staticmethod
    def mask_boxes_outside_range_numpy(boxes, limit_range, min_num_corners=1):
        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        corners = center_to_corner_box3d(boxes[:, :3], boxes[:, 3:6], boxes[:, 6], origin=(0.5, 0.5, 0.5), axis=2)
        mask = ((corners >= limit_range[0:3]) & (corners <= limit_range[3:6])).all(axis=2)
        mask = mask.sum(axis=1) >= min_num_corners  # (N)

        return mask

    @staticmethod
    def mask_boxes_outside_fovconvexthull_numpy(boxes, fov_convexthull):

        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        points = boxes[:, :2]
        fov_convexthull = np.array(fov_convexthull)
        path = Path(fov_convexthull)
        mask = np.ones(len(boxes)).astype(np.bool)
        for idx, point in enumerate(points):
            mask[idx] = path.contains_point(point)
        return mask

    @staticmethod
    def mask_boxes_by_min_points(boxes, points, min_points):
        mask = points_in_boxes_cpu(points[:, :3], boxes[:, :7])
        points_in_boxes_num = mask.sum(-1)
        return points_in_boxes_num > min_points

    def camera_aug(self, data_dict):
        pass

    def lidar_aug(self, data_dict):
        mask = self.mask_points_by_range(data_dict["points"], self.point_cloud_range)
        data_dict["points"] = data_dict["points"][mask]
        if len(data_dict["points"]) == 0:
            data_dict["points"] = np.array([[0.1, 0.1, 0.1, 0], [0.2, 0.2, 0.2, 0]], dtype=np.float32)
        else:
            data_dict["points"] = np.concatenate(
                [data_dict["points"], np.array([[0.1, 0.1, 0.1, 0], [0.2, 0.2, 0.2, 0]], dtype=np.float32)], axis=0
            )

    def radar_aug(self, data_dict):
        mask = self.mask_points_by_range(data_dict["radar_points"], self.point_cloud_range)
        data_dict["radar_points"] = data_dict["radar_points"][mask]

    def forward_single(self, data_dict):
        if "points" in data_dict.keys():
            self.lidar_aug(data_dict)
        if "radar_points" in data_dict.keys():
            self.radar_aug(data_dict)
        if len(data_dict.get("gt_boxes", [])) > 0:
            mask = self.mask_boxes_outside_range_numpy(data_dict["gt_boxes"], self.box_range)
            if self.fov_convexthull is not None:
                mask &= self.mask_boxes_outside_fovconvexthull_numpy(data_dict["gt_boxes"], self.fov_convexthull)
            if self.min_points is not None:
                mask &= self.mask_boxes_by_min_points(
                    data_dict["gt_boxes"], data_dict["points"], self.min_points
                )  # lidar训练丢掉稀疏点的GT
            data_dict["gt_boxes"] = data_dict["gt_boxes"][mask]
            if data_dict.get("gt_names", None) is not None:
                data_dict["gt_names"] = data_dict["gt_names"][mask]
            if data_dict.get("gt_labels", None) is not None:
                data_dict["gt_labels"] = data_dict["gt_labels"][mask]
            if data_dict.get("instance_inds", None) is not None:
                data_dict["instance_inds"] = data_dict["instance_inds"][mask]
            if data_dict.get("predict_attribute", None) is not None:
                for key in list(data_dict["predict_attribute"].keys()):
                    data_dict["predict_attribute"][key] = data_dict["predict_attribute"][key][mask]
        return data_dict

    def forward(self, data_dict):
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class PointShuffle(Module):
    """Shuffle input points"""

    def __init__(self, mode, multiframe=False):
        super().__init__()
        self.mode = mode
        self.multiframe = multiframe

    def camera_aug(self, data_dict):
        pass

    def lidar_aug(self, data_dict):
        points = data_dict["points"]
        np.random.shuffle(points)
        data_dict["points"] = points

    def radar_aug(self, data_dict):
        points = data_dict["radar_points"]
        np.random.shuffle(points)
        data_dict["radar_points"] = points

    def forward_single(self, data_dict):
        if "points" in data_dict.keys():
            self.lidar_aug(data_dict)
        if "radar_points" in data_dict.keys():
            self.radar_aug(data_dict)
        return data_dict

    def forward(self, data_dict):
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class GTSampling(Module):
    """GT Sampling"""

    def __init__(
        self,
        root_path,
        data_name,
        data_split,
        class_names,
        sampler_groups,
        num_point_feature,
        remove_extra_width=(0, 0, 0),
        use_road_plane=False,
        database_with_fakelidar=False,
        filter_by_min_points_cfg=None,
        removed_difficulty=None,
        limit_whole_scene=False,
        logger=None,
        mode="train",
    ):
        """
        :param root_path: db_info parent directory
        :param db_info_paths: db info file list
        :param class_names: object class
        :param sampler_groups: gt sampling number per category
        :param remove_extra_width: remove point near gt box
        :param use_road_plane: using road plane info
        :param database_with_fakelidar: fake lidar
        :param filter_by_min_points_cfg: filter min points object
        :param limit_whole_scene: limit object per category in one scene
        """
        super().__init__()
        self.epoch = -1
        self.db_sampler = DataBaseSampler(
            root_path=root_path,
            data_name=data_name,
            data_split=data_split,
            sampler_groups=sampler_groups,
            num_point_feature=num_point_feature,
            remove_extra_width=remove_extra_width,
            use_road_plane=use_road_plane,
            database_with_fakelidar=database_with_fakelidar,
            filter_by_min_points_cfg=filter_by_min_points_cfg,
            removed_difficulty=removed_difficulty,
            limit_whole_scene=limit_whole_scene,
            class_names=class_names,
            logger=logger,
        )

    def forward(self, data_dict):
        if data_dict.get("points", None) is not None:
            data_dict = self.db_sampler(data_dict)
        return data_dict


class PubicImageAffineTransformation(ImageAffineTransformation):
    def __init__(self, aug_conf, mode, img_norm, img_conf):
        super().__init__(aug_conf=aug_conf, camera_names=None, mode=mode, img_norm=img_norm, img_conf=img_conf)

    def _init_ida_aug_conf(self, aug_conf, camera_names=None):
        return aug_conf

    def sample_augs(self):
        """Generate ida augmentation values based on ida_config."""
        H, W = self.ida_aug_conf_dict["H"], self.ida_aug_conf_dict["W"]
        fH, fW = self.ida_aug_conf_dict["final_dim"]
        resize_lim = self.ida_aug_conf_dict["resize_lim"]
        bot_pct_lim = self.ida_aug_conf_dict["bot_pct_lim"]
        if self.mode == "train":
            resize = np.random.uniform(*resize_lim)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h, crop_w = self._get_crop_hw(newH, fH, newW, fW, bot_pct_lim)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = True if self.ida_aug_conf_dict["rand_flip"] and np.random.choice([0, 1]) else False
            rotate_ida = np.random.uniform(*self.ida_aug_conf_dict["rot_lim"])
        else:
            resize = max(fH / H, fW / W)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h, crop_w = self._get_crop_hw(newH, fH, newW, fW, bot_pct_lim)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate_ida = 0
        return resize, resize_dims, crop, flip, rotate_ida

    def camera_aug(self, data_dict):
        aug_imgs, ida_mats = [], []
        for img in data_dict["imgs"]:
            resize, resize_dims, crop, flip, rotate_ida = self.sample_augs()
            img, ida_mat = self._img_transform(
                img, resize=resize, resize_dims=resize_dims, crop=crop, flip=flip, rotate=rotate_ida
            )
            aug_imgs.append(img)
            ida_mats.append(ida_mat)
        return aug_imgs, np.stack(ida_mats)


class CameraUndistortCPU(Module):
    def __init__(self, mode="train", multiframe=True, gpu_aug=False):
        super().__init__()
        self.mode = mode
        self.multiframe = multiframe
        self.gpu_aug = gpu_aug

    def forward_single(self, data_dict):
        if self.mode == "train":
            interpolation = random.choice(
                [cv2.INTER_AREA, cv2.INTER_LINEAR, cv2.INTER_NEAREST, cv2.INTER_CUBIC, cv2.INTER_LANCZOS4]
            )
        else:
            interpolation = cv2.INTER_LINEAR
        undistort_imgs = []
        for img, map1, map2 in zip(data_dict["imgs"], data_dict["map1s"], data_dict["map2s"]):
            img = cv2.remap(img, map1, map2, interpolation)
            undistort_imgs.append(img)
        data_dict["imgs"] = undistort_imgs
        return data_dict

    def forward(self, data_dict):
        if self.gpu_aug:
            return data_dict
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class UnifiedObjectSample(torch.nn.Module):
    """GT Sampling"""

    def __init__(
        self,
        db_sampler,
        sample_2d=False,
        sample_method="depth",
        modify_points=False,
        mixup_rate=-1,
        stop_epoch=None,
        mode="train",
    ):
        super().__init__()
        self.sampler_cfg = db_sampler
        self.sample_2d = sample_2d
        self.sample_method = sample_method
        self.modify_points = modify_points
        self.mixup_rate = mixup_rate
        self.stop_epoch = stop_epoch
        self.db_sampler = UnifiedDataBaseSampler(**db_sampler)

    def set_epoch(self, epoch):
        self.epoch = epoch

    @staticmethod
    def remove_points_in_boxes(points, boxes):
        """Remove the points in the sampled bounding boxes.

        Args:
            points (:obj:`BasePoints`): Input point cloud array.
            boxes (np.ndarray): Sampled ground truth boxes. Boxes are defined in mmdet3d coord system.

        Returns:
            np.ndarray: Points with those in the boxes removed.
        """
        masks = box_np_ops.points_in_rbbox(points, boxes)
        points = points[np.logical_not(masks.any(-1))]
        return points

    def forward(self, input_dict):
        """Call function to sample ground truth objects to the data.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after object sampling augmentation, \
                'points', 'gt_bboxes_3d', 'gt_labels_3d' keys are updated \
                in the result dict.
        """

        if self.stop_epoch is not None and (self.epoch + 1) >= self.stop_epoch:  # self.epoch starts from 0
            return input_dict
        gt_bboxes_3d = copy.deepcopy(input_dict["gt_boxes"])
        gt_labels_3d = copy.deepcopy(input_dict["gt_labels"])

        # change to float for blending operation
        points = input_dict["points"]
        if self.sample_2d:
            # Assume for now 3D & 2D bboxes are the same
            sampled_dict = self.db_sampler.sample_all(gt_bboxes_3d, gt_labels_3d, with_img=True)
        else:
            sampled_dict = self.db_sampler.sample_all(gt_bboxes_3d.tensor.numpy(), gt_labels_3d, with_img=False)

        if sampled_dict is not None:
            sampled_gt_bboxes_3d = sampled_dict["gt_bboxes_3d"]
            sampled_points = sampled_dict["points"].tensor.numpy()
            sampled_points_idx = sampled_dict["points_idx"]
            sampled_gt_labels = sampled_dict["gt_labels_3d"]

            # sampled_gt_bboxes_3d in db_info are defined in mmdet3d coords system (version 0.18.0).
            # diff:
            # sampled_gt_bboxes_3d = [bottom_x, bottom_y, bottom_z, x_size(w), y_size(l), z_size(z), theta_1]
            # gt_bboxes_3d = [center_x, center_y, center_z, l, w, z, theta_2]
            # where theta_1 = - theta_2 + pi/2
            gt_bboxes_3d[..., 2] -= gt_bboxes_3d[..., 5] / 2
            gt_bboxes_3d[..., [3, 4]] = gt_bboxes_3d[..., [4, 3]]
            gt_bboxes_3d[..., 6] = -gt_bboxes_3d[..., 6] + np.pi / 2

            gt_labels_3d = np.concatenate([gt_labels_3d, sampled_gt_labels], axis=0)
            gt_bboxes_3d = np.concatenate([gt_bboxes_3d, sampled_gt_bboxes_3d], axis=0)

            points = self.remove_points_in_boxes(points, sampled_gt_bboxes_3d)
            points_idx = -1 * np.ones(len(points), dtype=np.int)
            points = np.concatenate([points, sampled_points])
            points_idx = np.concatenate([points_idx, sampled_points_idx], axis=0)

            if self.sample_2d:
                imgs = input_dict["imgs"]
                sampled_img = sampled_dict["images"]
                sampled_num = len(sampled_gt_bboxes_3d)
                img_list = [np.array(v) for v in imgs]
                lidar2img_list = [mat for mat in input_dict["lidar2imgs"]]
                corners = boxes_to_corners_3d(gt_bboxes_3d)
                imgs, points_keep = self.unified_sample(
                    img_list, lidar2img_list, points, points_idx, corners, sampled_img, sampled_num
                )
                input_dict["imgs"] = imgs
                if self.modify_points:
                    points = points[points_keep]

            gt_bboxes_3d[..., 2] += gt_bboxes_3d[..., 5] / 2
            gt_bboxes_3d[..., [3, 4]] = gt_bboxes_3d[..., [4, 3]]
            gt_bboxes_3d[..., 6] = -gt_bboxes_3d[..., 6] + np.pi / 2

        input_dict["gt_boxes"] = gt_bboxes_3d
        input_dict["gt_labels"] = gt_labels_3d.astype(np.long)
        input_dict["points"] = points

        return input_dict

    def unified_sample(self, imgs, lidar2img, points, points_idx, bboxes_3d, sampled_img, sampled_num):
        # for boxes
        bboxes_3d = np.concatenate([bboxes_3d, np.ones_like(bboxes_3d[..., :1])], -1)
        is_raw = np.ones(len(bboxes_3d))
        is_raw[-sampled_num:] = 0
        is_raw = is_raw.astype(bool)
        raw_num = len(is_raw) - sampled_num
        # for point cloud
        points_3d = points[:, :4].copy()
        points_3d[:, -1] = 1
        points_keep = np.ones(len(points_3d)).astype(np.bool)
        new_imgs = imgs

        assert len(imgs) == len(lidar2img) and len(sampled_img) == sampled_num
        for _idx, (_img, _lidar2img) in enumerate(zip(imgs, lidar2img)):
            coord_img = bboxes_3d @ _lidar2img.T
            coord_img[..., :2] /= coord_img[..., 2, None]
            depth = coord_img[..., 2]
            img_mask = (depth > 0).all(axis=-1)
            img_count = img_mask.nonzero()[0]
            if img_mask.sum() == 0:
                continue
            depth = depth.mean(1)[img_mask]
            coord_img = coord_img[..., :2][img_mask]
            minxy = np.min(coord_img, axis=-2)
            maxxy = np.max(coord_img, axis=-2)
            bbox = np.concatenate([minxy, maxxy], axis=-1).astype(int)
            bbox[:, 0::2] = np.clip(bbox[:, 0::2], a_min=0, a_max=_img.shape[1] - 1)
            bbox[:, 1::2] = np.clip(bbox[:, 1::2], a_min=0, a_max=_img.shape[0] - 1)
            img_mask = ((bbox[:, 2:] - bbox[:, :2]) > 1).all(axis=-1)
            if img_mask.sum() == 0:
                continue
            depth = depth[img_mask]
            if "depth" in self.sample_method:
                paste_order = depth.argsort()
                paste_order = paste_order[::-1]
            else:
                paste_order = np.arange(len(depth), dtype=np.int64)
            img_count = img_count[img_mask][paste_order]
            bbox = bbox[img_mask][paste_order]

            paste_mask = -255 * np.ones(_img.shape[:2], dtype=np.int)
            fg_mask = np.zeros(_img.shape[:2], dtype=np.int)
            # first crop image from raw image
            raw_img = []
            for _count, _box in zip(img_count, bbox):
                if is_raw[_count]:
                    raw_img.append(_img[_box[1] : _box[3], _box[0] : _box[2]])

            # then stitch the crops to raw image
            for _count, _box in zip(img_count, bbox):
                if is_raw[_count]:
                    if self.mixup_rate < 0:
                        _img[_box[1] : _box[3], _box[0] : _box[2]] = raw_img.pop(0)
                    else:
                        _img[_box[1] : _box[3], _box[0] : _box[2]] = (
                            _img[_box[1] : _box[3], _box[0] : _box[2]] * (1 - self.mixup_rate)
                            + raw_img.pop(0) * self.mixup_rate
                        )
                    fg_mask[_box[1] : _box[3], _box[0] : _box[2]] = 1
                else:
                    img_crop = sampled_img[_count - raw_num]
                    if len(img_crop) == 0:
                        continue
                    img_crop = cv2.resize(img_crop, tuple(_box[[2, 3]] - _box[[0, 1]]))
                    if self.mixup_rate < 0:
                        _img[_box[1] : _box[3], _box[0] : _box[2]] = img_crop
                    else:
                        _img[_box[1] : _box[3], _box[0] : _box[2]] = (
                            _img[_box[1] : _box[3], _box[0] : _box[2]] * (1 - self.mixup_rate)
                            + img_crop * self.mixup_rate
                        )

                paste_mask[_box[1] : _box[3], _box[0] : _box[2]] = _count

            new_imgs[_idx] = _img

            # calculate modify mask
            if self.modify_points:
                points_img = points_3d @ _lidar2img.T
                points_img[:, :2] /= points_img[:, 2, None]
                depth = points_img[:, 2]
                img_mask = depth > 0
                if img_mask.sum() == 0:
                    continue
                img_mask = (
                    (points_img[:, 0] > 0)
                    & (points_img[:, 0] < _img.shape[1])
                    & (points_img[:, 1] > 0)
                    & (points_img[:, 1] < _img.shape[0])
                    & img_mask
                )
                points_img = points_img[img_mask].astype(int)
                new_mask = paste_mask[points_img[:, 1], points_img[:, 0]] == (points_idx[img_mask] + raw_num)
                raw_fg = (fg_mask == 1) & (paste_mask >= 0) & (paste_mask < raw_num)
                raw_bg = (fg_mask == 0) & (paste_mask < 0)
                raw_mask = raw_fg[points_img[:, 1], points_img[:, 0]] | raw_bg[points_img[:, 1], points_img[:, 0]]
                keep_mask = new_mask | raw_mask
                points_keep[img_mask] = points_keep[img_mask] & keep_mask

        return new_imgs, points_keep

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f" sample_2d={self.sample_2d},"
        repr_str += f" data_root={self.sampler_cfg.data_root},"
        repr_str += f" info_path={self.sampler_cfg.info_path},"
        repr_str += f" rate={self.sampler_cfg.rate},"
        repr_str += f" prepare={self.sampler_cfg.prepare},"
        repr_str += f" classes={self.sampler_cfg.classes},"
        repr_str += f" sample_groups={self.sampler_cfg.sample_groups}"
        return repr_str


class ModalMask3D(BaseAugmentation):
    def __init__(self, mode="train", mask_modal="image", drop_ratio=[0.75, 0.5], multiframe=True, **kwargs):
        """
        Args:
            mode (str): "train" will mask random modal with probability; "test" will mask all data for a specified modal
            mask_modal (str): "image" or "points" or "none"
            drop_ratio (list): drop ratio during training, (1 - drop_ratio[0]) for img ,(drop_ratio[0] - drop_ratio[1]) for lidar

        Returns:
            list[dict]: Valid samples after collision test.
        """
        super(ModalMask3D, self).__init__(**kwargs)
        self.mode = mode
        self.mask_modal = mask_modal
        assert drop_ratio[0] <= 1 and drop_ratio[1] <= drop_ratio[0] and drop_ratio[0] >= 0
        assert self.mask_modal in ["image", "points", "none"]
        self.drop_ratio = drop_ratio
        self.multiframe = multiframe

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        return repr_str

    def lidar_aug(self, data_dict):

        dummy_point = np.ones([1, data_dict["points"].shape[1]])
        dummy_point[0, 2:] = 0
        data_dict["points"] = np.concatenate([data_dict["points"] * 0, dummy_point], axis=0)
        data_dict["mask_modal"] += "L"

    def camera_aug(self, data_dict):
        data_dict["imgs"] = [img * 0 for img in data_dict["imgs"]]
        data_dict["mask_modal"] += "C"

    def radar_aug(self, data_dict):
        dummy_point = np.ones([1, data_dict["radar_points"].shape[1]])
        dummy_point[0, 2:] = 0
        data_dict["radar_points"] = np.concatenate([data_dict["radar_points"] * 0, dummy_point], axis=0)
        data_dict["mask_modal"] += "R"

    def forward_single(self, data_dict):
        data_dict["mask_modal"] = ""
        if self.mode == "train":
            seed = np.random.rand()
            if seed > self.drop_ratio[0]:
                self.camera_aug(data_dict)
                self.radar_aug(data_dict)  # 默认 camera radar 绑定
            elif seed > self.drop_ratio[1]:
                self.lidar_aug(data_dict)
        elif self.mode == "val":
            if self.mask_modal == "points":
                self.lidar_aug(data_dict)
            elif self.mask_modal == "image":
                self.camera_aug(data_dict)
                self.radar_aug(data_dict)

        return data_dict

    def forward(self, data_dict):
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


class FrontLidarBoxPostFilter(BaseAugmentation):
    def __init__(self, mode="train", multiframe=True, fov_convexthull=None, **kwargs):
        """
        适用 环视camera + 前视lidar的配置下 使用modal mask的情况
        """
        super(FrontLidarBoxPostFilter, self).__init__(**kwargs)
        self.mode = mode
        self.multiframe = multiframe
        self.fov_convexthull = fov_convexthull
        assert self.fov_convexthull is not None

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        return repr_str

    def lidar_aug(
        self,
    ):
        pass

    def camera_aug(self):
        pass

    def radar_aug(self):
        pass

    @staticmethod
    def mask_boxes_outside_fovconvexthull_numpy(boxes, fov_convexthull):

        if boxes.shape[1] > 7:
            boxes = boxes[:, 0:7]
        points = boxes[:, :2]
        fov_convexthull = np.array(fov_convexthull)
        path = Path(fov_convexthull)
        mask = np.ones(len(boxes)).astype(np.bool)
        for idx, point in enumerate(points):
            mask[idx] = path.contains_point(point)
        return mask

    def forward_single(self, data_dict):
        if self.mode == "train":
            if "imgs" in data_dict and np.all(data_dict["imgs"][0] == 0):
                mask = self.mask_boxes_outside_fovconvexthull_numpy(data_dict["gt_boxes"], self.fov_convexthull)
                data_dict["gt_boxes"] = data_dict["gt_boxes"][mask]
                if data_dict.get("gt_names", None) is not None:
                    data_dict["gt_names"] = data_dict["gt_names"][mask]
                if data_dict.get("gt_labels", None) is not None:
                    data_dict["gt_labels"] = data_dict["gt_labels"][mask]
                if data_dict.get("instance_inds", None) is not None:
                    data_dict["instance_inds"] = data_dict["instance_inds"][mask]
                if data_dict.get("predict_attribute", None) is not None:
                    for key in list(data_dict["predict_attribute"].keys()):
                        data_dict["predict_attribute"][key] = data_dict["predict_attribute"][key][mask]
        return data_dict

    def forward(self, data_dict):
        if not self.multiframe:
            return self.forward_single(data_dict)
        elif isinstance(data_dict, list):
            data_seq = []
            for frame in data_dict:
                data_seq.append(self.forward_single(frame))
            return data_seq
        else:
            raise NotImplementedError


def calc_warp_between_imgs(campose_geom_trans_matrix, K, ego2cam_proj, W, H, ego_voxels=None):
    try:
        if ego_voxels is None:
            x_coord, y_coord, z_coord = np.mgrid[-15:15, 0:100, -10:10]
            ego_voxels = np.stack((x_coord, y_coord, z_coord), axis=-1).reshape(-1, 3)
            ego_voxels = np.c_[ego_voxels, np.ones(len(ego_voxels))]
            ego_voxels = ego_voxels.astype(np.float32)  # fix: mix-accuracy bug on h20

        cam_voxels = (ego2cam_proj @ ego_voxels.T).T  # (n, 4)
        tgt_cam_voxels = (campose_geom_trans_matrix @ cam_voxels.T).T

        src_pixels = (K @ cam_voxels[:, :3].T).T
        dst_pixels = (K @ tgt_cam_voxels[:, :3].T).T

        src_pixels[:, :2] = src_pixels[:, :2] / src_pixels[:, 2:3]
        dst_pixels[:, :2] = dst_pixels[:, :2] / dst_pixels[:, 2:3]

        depth_filter = (src_pixels[:, 2] > 0) & (dst_pixels[:, 2] > 0)
        width_filter = ((src_pixels[:, 0] > 0) & (src_pixels[:, 0] < W)) & (
            (dst_pixels[:, 0] > 0) & (dst_pixels[:, 0] < W)
        )
        height_filter = ((src_pixels[:, 1] > 0) & (src_pixels[:, 1] < H)) & (
            (dst_pixels[:, 1] > 0) & (dst_pixels[:, 1] < H)
        )
        filters = depth_filter & width_filter & height_filter

        src_pixels = src_pixels[filters].astype(np.float32)[:, :2]
        dst_pixels = dst_pixels[filters].astype(np.float32)[:, :2]

        warp_matrix, valid_pts = cv2.findHomography(src_pixels, dst_pixels, cv2.RANSAC, 2.0)
        warp_matrix_inv = np.linalg.inv(warp_matrix)  # 通过求逆来判断是否合格
        if np.count_nonzero(np.isnan(warp_matrix_inv)):
            return np.eye(3)
        # print(sum(valid_pts), dst_pixels.shape)
        return warp_matrix
    except Exception:
        return np.eye(3)
