import io
import nori2 as nori
import numpy as np
import bisect
from numpy.lib import recfunctions as rfn
from scipy.spatial.transform import Rotation as R
import refile
from loguru import logger
from perceptron.utils import torch_dist
from perceptron.utils.file_io import load_pkl
from perceptron.utils.env import get_cluster
from perceptron.data.det3d.utils.functional import trans_oss_to_gpfs_path

# 当前文件里原有的 ego 全部改为 gnss, 另新增真实的 gnss


class LidarBase:
    def __init__(
        self,
        car,
        lidar_names,
        loader_output,
        mode,
        referen_lidar="middle_lidar",
        pc_fields=["x", "y", "z", "i"],
        used_echo_id=[1],
        lidar_sweeps_idx=[-2, -1],
        lidar_with_timestamp=False,
        lidar_ids=[],
    ) -> None:
        self.load_points = True
        if not lidar_names:
            logger.warning("Empty lidar_names. lidar points will not be loaded")
            self.load_points = False
        self.car = car
        self.lidar_names = lidar_names
        self.loader_output = loader_output
        self.mode = mode
        self.nori_fetcher = None
        self.referen_lidar = referen_lidar
        self.pc_fields = pc_fields
        self.used_echo_id = used_echo_id
        self.lidar_sweeps_idx = lidar_sweeps_idx
        self.lidar_with_timestamp = lidar_with_timestamp
        self.lidar_ids = lidar_ids
        self.nori_available = get_cluster() == "https://hh-d.brainpp.cn"
        if lidar_ids:
            assert self.pc_fields[-1] == "lidar_id", "when use lidar_ids, lidar_id should in pc_fields"

    @staticmethod
    def _able_to_get_lidar(names, sensor_data):
        is_distributed = torch_dist.is_distributed()
        local_rank = 0
        if is_distributed:
            local_rank = torch_dist.get_rank()

        valid_lidars = []
        for name in names:
            if name not in sensor_data:
                if local_rank == 0:
                    logger.debug(f"{name} is not in {sensor_data.keys()}")
                continue
            elif sensor_data[name] is None:
                if local_rank == 0:
                    logger.debug(f"{name} in {sensor_data.keys()}, but has no info")
                continue
            elif (
                "file_path" not in sensor_data[name]
                and "s3_path" not in sensor_data[name]
                # and ("nori_id" not in sensor_data[name] or sensor_data[name]["nori_id"] is None)
            ):
                # map update 04.17 for qy-lidar
                if "nori_id" not in sensor_data[name]:
                    sensor_data[name]["nori_id"] = "{}.npy".format(sensor_data[name]["timestamp"])

                if "nori_id" not in sensor_data[name] or sensor_data[name]["nori_id"] is None:
                    if local_rank == 0:
                        logger.debug(f"{name} in {sensor_data.keys()}, but has no sensor path")
                    continue
            valid_lidars.append(name)
            break

        # if local_rank == 0:
        # logger.warning(f"The file path of {name} is None")

        return valid_lidars

    def _get_point_cloud(self, idx, lidar_names):
        """
        Loading point cloud with given sample index

        Refer to https://wiki.megvii-inc.com/pages/viewpage.action?pageId=316705342 for details about the point cloud format.

        Args:
            idx (int): Sampled index
        Returns:
            point cloud (Dict[str, np.ndarray]): (N, 4), (x-y-z-reflection)
        """
        lidars = []
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        use_qy_nori = getattr(self.loader_output["frame_data_list"], "has_nori", False)
        valid_lidars = self._able_to_get_lidar(lidar_names, sensor_data)
        if not valid_lidars:
            # logger.warning(f"{lidar_names} All lidars are invalid")
            self.loader_output["frame_data_list"].cal_useless_data(idx, "All lidars are invalid", "lidar")
            return None
        for lidar_name in valid_lidars:
            if use_qy_nori:
                try:
                    nori_id = sensor_data[lidar_name]["nori_id"]
                    vid = int(nori_id.split(",")[0])
                    nori_path = trans_oss_to_gpfs_path(sensor_data[lidar_name]["nori_path"])
                    vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                    data = vreader.get(sensor_data[lidar_name]["nori_id"])
                    pc_data_raw = np.load(io.BytesIO(data)).copy()
                    pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
                    pc_data = pc_data[np.isin(pc_data_raw["echo_id"], self.used_echo_id)]
                except ValueError:
                    # logger.error(f"Failed to load point cloud from {s3_path}: {e}")
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "Failed to load point cloud from use_qy_nori", "lidar"
                    )
                    continue
            elif "file_path" in sensor_data[lidar_name]:
                s3_path = trans_oss_to_gpfs_path(sensor_data[lidar_name]["file_path"])
                # if not os.path.exists(s3_path):  # accelerdata
                #     s3_path = sensor_data[lidar_name]["file_path"]
                # if "s3://" in s3_path:
                #     logger.info("file_path starts with s3://:, {}".format(s3_path))
                if refile.smart_exists(s3_path):
                    with refile.smart_open(s3_path, "rb") as f:
                        file_bytes = f.read()
                    try:
                        pc_data_raw = np.load(io.BytesIO(file_bytes))
                        pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
                    except ValueError:
                        # logger.error(f"Failed to load point cloud from {s3_path}: {e}")
                        self.loader_output["frame_data_list"].cal_useless_data(
                            idx, "Failed to load point cloud from {}".format(s3_path), "lidar"
                        )
                        continue
                else:
                    # print(s3_path, " file_path not found")
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "file_path not found {}".format(s3_path), "lidar"
                    )
                    continue
            elif "nori_id" in sensor_data[lidar_name]:
                nori_id = sensor_data[lidar_name]["nori_id"]
                if self.nori_available:
                    data = self.nori_fetcher.get(nori_id)
                else:
                    try:
                        vid = int(nori_id.split(",")[0])
                        nori_path = trans_oss_to_gpfs_path(sensor_data[lidar_name]["nori_path"])
                        # if not os.path.exists(nori_path):  # accelerdata
                        #     nori_path = sensor_data[lidar_name]["nori_path"]
                        vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                        data = vreader.get(sensor_data[lidar_name]["nori_id"])
                        pc_data_raw = np.load(io.BytesIO(data)).copy()
                        pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
                        pc_data = pc_data[np.isin(pc_data_raw["echo_id"], self.used_echo_id)]
                    except Exception:
                        # logger.error(e)
                        # logger.error(f"{sensor_data[lidar_name]} nori_id not found")
                        self.loader_output["frame_data_list"].cal_useless_data(
                            idx, "nori_id not found {}".format(sensor_data[lidar_name]), "lidar"
                        )
                        continue
            else:
                s3_path = sensor_data[lidar_name]["s3_path"]
                try:
                    pc_data_raw = load_pkl(s3_path)
                    pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
                except Exception:
                    # FIXME: 这里考虑针对mcap推理数据，设置不同的pc_fields，去掉lidar_id
                    self.pc_fields = ["x", "y", "z", "i"]
                    self.lidar_ids = None
                    pc_data_raw = load_pkl(s3_path)
                    pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
                    # logger.error(f"Failed to load point cloud from {s3_path}: {e}")
                    # self.loader_output["frame_data_list"].cal_useless_data(
                    #     idx,
                    #     "Failed to load point cloud from s3_path, path: {}".format(sensor_data[lidar_name]),
                    #     "lidar",
                    # )
                    # continue

            if self.lidar_ids:
                pc_data_list = []
                for lidar_id in self.lidar_ids:
                    pc_data_list.append(pc_data[pc_data[:, -1] == lidar_id][:, :-1])

                pc_data = np.concatenate(pc_data_list)
            if len(pc_data) < 10:
                self.loader_output["frame_data_list"].cal_useless_data(idx, "len(pc_data) < 10", "lidar")
                continue

            if self.lidar_with_timestamp:
                timestamp = self._get_timestamp(idx, lidar_name)
                time = np.ones((pc_data.shape[0], 1)) * timestamp
                pc_data = np.concatenate((pc_data, time), axis=1)
            lidars.append(pc_data)
        return lidars

    @staticmethod
    def _get_lidar_to_gnss_rotate_and_translation(lidar_to_gnss_info):
        # NOTE：数据json需要批量修正
        if "lidar_gnss" in lidar_to_gnss_info.keys():
            trans = lidar_to_gnss_info["lidar_gnss"]["transform"]["translation"]
            quan = lidar_to_gnss_info["lidar_gnss"]["transform"]["rotation"]
        if "extrinsic" in lidar_to_gnss_info.keys():
            trans = lidar_to_gnss_info["extrinsic"]["transform"]["translation"]
            quan = lidar_to_gnss_info["extrinsic"]["transform"]["rotation"]
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    @staticmethod
    def _get_lidar_to_ego_rotate_and_translation(lidar_to_ego_info):
        trans = lidar_to_ego_info["lidar_ego"]["extrinsic"]["transform"]["translation"]
        quan = lidar_to_ego_info["lidar_ego"]["extrinsic"]["transform"]["rotation"]
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    @staticmethod
    def _get_lidar_pose_rotate_and_translation(lidar_pose_info):
        trans = lidar_pose_info["lidar_pose"]["translation"]
        quan = lidar_pose_info["lidar_pose"]["rotation"]
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    @staticmethod
    def _get_gnss_to_world_rotate_and_translation(sensor_data):
        # 之前private data初期ins 数据有误，因此存的是odom 数据，之后 ins 数据正确后，改存ins 数据，更多信息见：
        # https://wiki.megvii-inc.com/pages/viewpage.action?pageId=312323474
        if "odom_data" in sensor_data:
            odom_data = sensor_data["odom_data"]
            trans = odom_data["pose"]["pose"]["position"]  # dict
            quan = odom_data["pose"]["pose"]["orientation"]  # dict
        elif "ins_data" in sensor_data:
            ins_data = sensor_data["ins_data"]
            trans = ins_data["localization"]["position"]  # dict
            quan = ins_data["localization"]["orientation"]  # dict
        elif "ins_pose" in sensor_data:
            ins_data = sensor_data["ins_pose"]
            trans = ins_data["localization"]["position"]  # dict
            quan = ins_data["localization"]["orientation"]  # dict
        else:
            raise ValueError(f"{sensor_data['frame_id']} lack odom/ins data")
        trans = np.array([trans["x"], trans["y"], trans["z"]], dtype=np.float64)
        quan = np.array([quan["x"], quan["y"], quan["z"], quan["w"]], dtype=np.float64)
        rot_mat = R.from_quat(quan).as_matrix()
        return rot_mat, trans

    def get_lidar_info(self, idx):
        # current lidar to gnss extrinsics
        lidar2gnss = np.eye(4)
        scene_id = bisect.bisect_right(self.loader_output["frame_data_list"].cumulative_sizes, idx)
        if (
            "front_lidar" in self.loader_output["frame_data_list"][idx]["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["front_lidar"]
        ):
            sensor_key = "front_lidar"
        elif (
            "middle_lidar" in self.loader_output["frame_data_list"][idx]["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["middle_lidar"]
        ):
            sensor_key = "middle_lidar"
        elif "front_lidar" in self.loader_output["calibrated_sensors"][scene_id].keys():
            sensor_key = "front_lidar"
        elif (
            "fuser_lidar"
            in self.loader_output["frame_data_list"][idx]["sensor_data"].keys()
            # and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["fuser_lidar"]
        ):
            sensor_key = self.referen_lidar  # map lidar旧数据适配
        else:
            return None
        cur_lidar2gnss_rot, cur_lidar2gnss_trans = self._get_lidar_to_gnss_rotate_and_translation(
            self.loader_output["calibrated_sensors"][scene_id][sensor_key]
        )
        lidar2gnss[:3, :3] = cur_lidar2gnss_rot
        lidar2gnss[:3, 3] = cur_lidar2gnss_trans

        # current gnss to world extrinsics
        gnss2global = np.eye(4)
        cur_gnss2world_rot, cur_gnss2world_trans = self._get_gnss_to_world_rotate_and_translation(
            self.loader_output["frame_data_list"][idx]
        )
        gnss2global[:3, :3] = cur_gnss2world_rot
        gnss2global[:3, 3] = cur_gnss2world_trans

        # middle lidar to ego extrinsics
        lidar2ego = np.eye(4)
        cur_lidar2ego_rot, cur_lidar2ego_trans = self._get_lidar_to_ego_rotate_and_translation(
            self.loader_output["calibrated_sensors"][scene_id]
        )
        lidar2ego[:3, :3] = cur_lidar2ego_rot
        lidar2ego[:3, 3] = cur_lidar2ego_trans

        # lidar_pose for multi frame
        # T_lidar_pose = np.eye(4).astype(np.float64)
        # cur_lidar_pose_rot, cur_lidar_pose_trans = self._get_lidar_pose_rotate_and_translation(
        #     self.loader_output["frame_data_list"][scene_id]
        # )
        # T_lidar_pose[:3, :3] = cur_lidar_pose_rot
        # T_lidar_pose[:3, 3] = cur_lidar_pose_trans

        return lidar2gnss, gnss2global, lidar2ego, gnss2global @ lidar2gnss  # T_lidar_pose

    def _get_timestamp(self, idx, lidar_name):
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        if lidar_name in sensor_data.keys():
            timestamp = sensor_data[lidar_name]["timestamp"]
        else:
            potential_lidar_names = ["middle_lidar", "front_lidar", "cam_front_120"]
            valid_lidar_names = [name for name in potential_lidar_names if name in sensor_data.keys()]
            if len(valid_lidar_names) > 0:
                timestamp = sensor_data[valid_lidar_names[0]]["timestamp"]
            else:
                raise RuntimeError(f"invalid lidar name in :{lidar_name}")
        return float(timestamp) * 1e6

    def _load_single_sweep(self, lidar_name, idx):
        sw_lidar_infos = dict()
        load_dim = 5 if self.lidar_with_timestamp else 4
        lidar_data = self._get_point_cloud(idx, self.lidar_names)
        sw_lidar = lidar_data[0][:, :load_dim]
        sw_lidar_infos["sweep_lidar_to_gnss"], sw_lidar_infos["sweep_gnss_to_global"] = self.get_lidar_info(idx)
        if self.lidar_with_timestamp:
            sw_lidar_infos["sweep_lidar_timestamp"] = self._get_timestamp(idx, lidar_name)
        return sw_lidar, sw_lidar_infos

    def _load_lidar_sweeps(self, lidar_name, current_idx):
        sweeps, sweep_points, sweep_points_infos = dict(), list(), list()
        for sw_idx in self.lidar_sweeps_idx:
            # calcurate current index
            lidar_sw_idx = sw_idx + current_idx
            if lidar_sw_idx < 0 and lidar_sw_idx >= len(self.loader_output["frame_data_list"]):
                lidar_sw_idx = current_idx

            sw_lidar, sw_lidar_infos = self._load_single_sweep(lidar_name, lidar_sw_idx)
            sweep_points.append(sw_lidar)
            sweep_points_infos.append(sw_lidar_infos)

        sweeps["sweep_points"] = sweep_points
        sweeps["sweep_lidar_infos"] = sweep_points_infos
        return sweeps

    def _concat_sweeps(self, idx, lidars, lidar_infos, lidar_sweeps):
        assert len(lidars) == len(lidar_sweeps), "Incorrect lidar information"
        concat_points = []
        for lidar_id, points in enumerate(lidars):
            lidar_name, lidar_sweep = self.lidar_names[lidar_id], lidar_sweeps[lidar_id]
            sweep_point, sweep_info = lidar_sweep["sweep_points"], lidar_sweep["sweep_lidar_infos"]
            key_gnss_to_global = lidar_infos["gnss2global"]
            key_lidar_to_gnss = lidar_infos["lidar2gnss"]
            all_points = points.copy()
            if all_points.shape[-1] == 5:
                all_points[:, -1] = 0.0
            for swid, frame in enumerate(sweep_point):
                sweep_gnss_to_global = sweep_info[swid]["sweep_gnss_to_global"]
                homogeneous_point = np.ones((frame.shape[0], 4))
                homogeneous_point[:, :3] = frame[:, :3]
                sweep_on_key = (
                    np.linalg.inv(key_lidar_to_gnss)
                    @ np.linalg.inv(key_gnss_to_global)
                    @ sweep_gnss_to_global
                    @ key_lidar_to_gnss
                    @ homogeneous_point.T
                ).T
                frame[:, :3] = sweep_on_key[:, :3]
                if all_points.shape[-1] == 5:
                    cur_timestamp = self._get_timestamp(idx, lidar_name)
                    frame[:, -1] = (cur_timestamp - sweep_info[swid]["sweep_lidar_timestamp"]) / 1e6
                all_points = np.concatenate([all_points, frame])
            concat_points.append(all_points)
        return concat_points

    def get_lidars(self, idx, data_dict):
        """
        Loading lidar with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        lidar_infos = dict()
        (
            lidar_infos["lidar2gnss"],
            lidar_infos["gnss2global"],
            lidar_infos["lidar2ego"],
            lidar_infos["T_lidar_pose"],
        ) = self.get_lidar_info(idx)
        ego2lidar = np.linalg.inv(lidar_infos["lidar2ego"])  # [4, 4]

        lidar_infos["ego2global"] = lidar_infos["T_lidar_pose"] @ ego2lidar  # NOTE
        try:
            if self.load_points:
                # load current key frame
                lidars = self._get_point_cloud(idx, self.lidar_names)
                # load lidar sweeps
                if self.lidar_sweeps_idx:
                    lidar_sweeps = []
                    for lidar_name in self.lidar_names:
                        lidar_sweeps.append(self._load_lidar_sweeps(lidar_name, idx))
                    lidars = self._concat_sweeps(idx, lidars, lidar_infos, lidar_sweeps)
                if lidars == [] or isinstance(lidars, None.__class__):
                    return None
                lidars = np.concatenate(lidars)
                data_dict.update(
                    {
                        "points": lidars,
                    }
                )
        except Exception:
            # print(e, "get_lidar")
            self.loader_output["frame_data_list"].cal_useless_data(idx, "get_lidar failed", "lidar")
            return None

        try:
            timestamp = self._get_timestamp(idx, self.referen_lidar)
        except Exception:
            timestamp = 000000.000000
            Warning("No lidar timestamp, use 00000.00000 instead")
        # load to data_dict
        data_dict.update(
            {
                "lidar2ego": lidar_infos["lidar2ego"],
                "lidar2gnss": lidar_infos["lidar2gnss"],
                "gnss2global": lidar_infos["gnss2global"],
                "lidar2global": lidar_infos["gnss2global"] @ lidar_infos["lidar2gnss"],
                "timestamp": timestamp,
                "ego2global": lidar_infos["ego2global"],
            }
        )
        return data_dict
