import os
import h5py
import io
import nori2 as nori
import numpy as np
from refile import smart_open
from perceptron.data.det3d.modules.annotation.base import AnnotationBase
from perceptron.data.det3d.utils.functional import trans_oss_to_gpfs_path


class AnnotationFreespace(AnnotationBase):
    def __init__(
        self, loader_output, mode, label_mapping, cross_mask=False, use_oss_data=True, log_bad_data=False
    ) -> None:
        super().__init__(loader_output, mode)
        self.label_mapping = label_mapping
        self.vfunc = np.vectorize(self.map_labels)
        self.cross_mask = cross_mask
        self.use_oss_data = use_oss_data
        self.log_bad_data = log_bad_data

    # 创建一个函数，该函数返回给定键在字典中对应的值
    def map_labels(self, x):
        return self.label_mapping[x]

    def load_h5_file(self, path):
        """从 H5 文件中读取所需字段。"""
        with smart_open(path, "rb") as rf:
            with h5py.File(rf, "r") as h5f:
                return (h5f["target"][...], h5f["cam_mask"][...], h5f["lidar_mask"][...], h5f["vis_2d_mask"][...])

    def load_h5_nori_file(self, data):
        with h5py.File(io.BytesIO(data), "r") as f2:
            return (f2["target"][...], f2["cam_mask"][...], f2["lidar_mask"][...], f2["vis_2d_mask"][...])

    def __getitem__(self, idx):
        res_dict = {}
        if self.mode == "eval":
            res_dict["semantic"] = np.zeros((0, 0), dtype=np.uint8)
            res_dict["semantic_mask"] = np.zeros((0, 0), dtype=np.bool_)
            return res_dict
        frame_data_list = self.loader_output["frame_data_list"]
        clip_info = frame_data_list[idx]
        freespace_path = clip_info["freespace_path"]

        if "freespace" in clip_info:
            nori_info = clip_info["freespace"]
            nori_id = nori_info["nori_id"]
            vid = int(nori_id.split(",")[0])
            nori_path = trans_oss_to_gpfs_path(nori_info["nori_path"])
            if not os.path.exists(nori_path) and self.use_oss_data:  # accelerdata
                # logger.debug(f"{nori_path} not exist in gpfs.")
                nori_path = nori_info["nori_path"]
                if self.log_bad_data:
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "Freespace Nori File Not Accelerated", "occ"
                    )
            try:
                vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                data = vreader.get(nori_id)
                freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_nori_file(data)
            except Exception:
                if self.log_bad_data:
                    self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace Nori File Read Error")
                # logger.warning(f"reading freespace nori file {nori_path}: {e}")
                return None

        else:
            if not freespace_path.endswith(".h5"):
                if self.log_bad_data:
                    self.loader_output["frame_data_list"].cal_useless_data(idx, "No h5 Freespace File Found", "occ")
                    # print(freespace_path, "No h5 Freespace File Found")
                    return None
            # 判断 gpfs 路径优先
            gpfs_path = trans_oss_to_gpfs_path(freespace_path)
            if os.path.exists(gpfs_path):
                try:
                    freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_file(gpfs_path)
                except Exception:
                    if self.log_bad_data:
                        self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace Nori File Read Error")
                    # logger.warning(f"reading freespace nori file {nori_path}: {e}")
                    return None
            else:
                if self.log_bad_data:
                    self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace File Not in GPFS")
                if self.use_oss_data:
                    freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_file(freespace_path)
                else:
                    return None

        # Vis Mask
        freespace_modal_mask = np.logical_or(cam_mask_2d, lidar_mask_2d)
        freespace_dynamic = freespace_gt == 2  # 只适合动态单分类
        freespace_target = np.logical_or(freespace_dynamic, freespace_modal_mask)
        freespace_mask = np.logical_or(freespace_target, vis_2d_mask)

        freespace_gt = np.where(freespace_gt < 29, freespace_gt, 255)
        freespace_gt = self.vfunc(freespace_gt)
        if self.cross_mask and clip_info.get("freespace_ignore", False):
            freespace_gt[freespace_gt == 0] = 255
        res_dict["semantic"] = freespace_gt.astype(np.uint8)
        res_dict["semantic_mask"] = freespace_mask.astype(np.bool_)
        return res_dict


CLS_MAP = {
    "unknow": 0,
    "freespace": 1,
    "dynamic": 2,
    "static": 3,
    "car": 5,  # 类别5 car，黄色
    "larger_vehicle": 6,  # 类别6 larger_vehicle，品红色
    "bicycle": 7,  # 类别7 bicycle，青色
    "pedestrain": 8,  # 类别8 pedestrain，蓝紫色
}


def process_3d_to_2p5(arr_ori):
    arr = arr_ori[..., ::-1]  # 翻转Z轴
    non_empty_dummy = (arr != CLS_MAP["unknow"]).astype(np.uint8)
    z_idx_dummy = np.argmax(non_empty_dummy, axis=-1, keepdims=True)
    freespace = np.take_along_axis(arr, z_idx_dummy, axis=-1).squeeze(-1)
    return freespace


class AnnotationFreespaceHeightLayer(AnnotationBase):
    def __init__(
        self,
        loader_output,
        mode,
        label_mapping,
        cross_mask=False,
        z_voxel_size=None,
        z_range=None,
        use_oss_data=True,
        log_bad_data=False,
    ) -> None:
        super().__init__(loader_output, mode)
        self.label_mapping = label_mapping
        self.vfunc = np.vectorize(self.map_labels)
        self.cross_mask = cross_mask
        self.z_voxel_size = z_voxel_size
        self.z_range = z_range
        self.use_oss_data = use_oss_data
        self.log_bad_data = log_bad_data

    # 创建一个函数，该函数返回给定键在字典中对应的值
    def map_labels(self, x):
        return self.label_mapping[x]

    def load_h5_file(self, path):
        """从 H5 文件中读取所需字段。"""
        with smart_open(path, "rb") as rf:
            with h5py.File(rf, "r") as h5f:
                return (
                    h5f["target"][...],
                    h5f["cam_mask"][...],
                    h5f["lidar_mask"][...],
                    h5f["vis_2d_mask"][...] if "vis_2d_mask" in h5f else None,
                )

    def load_h5_nori_file(self, data):
        with h5py.File(io.BytesIO(data), "r") as f2:
            return (
                f2["target"][...],
                f2["cam_mask"][...],
                f2["lidar_mask"][...],
                f2["vis_2d_mask"][...] if "vis_2d_mask" in f2 else None,
            )

    def process_label(
        self, occ_gt_layer, cam_mask_2d, lidar_mask_2d, vis_2d_mask, clip_info, with_freespace_mask=False
    ):
        # Vis Mask
        freespace_modal_mask = np.logical_or(cam_mask_2d, lidar_mask_2d)
        freespace_dynamic = occ_gt_layer == 2  # 只适合动态单分类
        freespace_target = np.logical_or(freespace_dynamic, freespace_modal_mask)
        freespace_mask = np.logical_or(freespace_target, vis_2d_mask)

        occ_gt_layer = np.where(occ_gt_layer < 29, occ_gt_layer, 255)

        occ_gt_layer = self.vfunc(occ_gt_layer)
        if self.cross_mask and clip_info.get("freespace_ignore", False):
            occ_gt_layer[occ_gt_layer == 0] = 255
        if with_freespace_mask:
            return occ_gt_layer, freespace_mask
        return occ_gt_layer

    def __getitem__(self, idx):
        res_dict = {}
        if self.mode == "eval":
            res_dict["semantic"] = np.zeros((0, 0), dtype=np.uint8)
            res_dict["semantic_mask"] = np.zeros((0, 0), dtype=np.bool_)
            return res_dict
        frame_data_list = self.loader_output["frame_data_list"]
        clip_info = frame_data_list[idx]
        freespace_path = clip_info["freespace_path"]

        if "freespace" in clip_info:
            nori_info = clip_info["freespace"]
            nori_id = nori_info["nori_id"]
            vid = int(nori_id.split(",")[0])
            nori_path = trans_oss_to_gpfs_path(nori_info["nori_path"])
            if not os.path.exists(nori_path) and self.use_oss_data:  # accelerdata
                nori_path = nori_info["nori_path"]
                self._fun_log_bad_data(idx, "Freespace Nori File Not Accelerated")
            try:
                vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                data = vreader.get(nori_id)
                freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_nori_file(data)
            except Exception:
                self._fun_log_bad_data(idx, "Freespace Nori File Read Error")
                return None

        else:
            if not freespace_path.endswith(".h5"):
                self._fun_log_bad_data(idx, "No h5 Freespace File Found", "occ")
                return None
            # 判断 gpfs 路径优先
            gpfs_path = trans_oss_to_gpfs_path(freespace_path)
            if os.path.exists(gpfs_path):
                try:
                    freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_file(gpfs_path)
                except Exception:
                    self._fun_log_bad_data(idx, "Freespace Nori File Read Error")
                    return None
            else:
                self._fun_log_bad_data(idx, "Freespace File Not in GPFS")
                if self.use_oss_data:
                    try:
                        freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_file(freespace_path)
                    except Exception:
                        self._fun_log_bad_data(idx, "H5 Freespace Filepath Read Error")
                        return None
                else:
                    return None

        # Vis Mask
        freespace_gt, freespace_mask = self.process_label(
            freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask, clip_info, with_freespace_mask=True
        )
        # ------------------- load occ 3d -----------------------
        occ_path = clip_info["occ_path"]

        if "occ" in clip_info:
            nori_info = clip_info["occ"]
            nori_id = nori_info["nori_id"]
            vid = int(nori_id.split(",")[0])
            nori_path = trans_oss_to_gpfs_path(nori_info["nori_path"])
            if not os.path.exists(nori_path):  # accelerdata
                self._fun_log_bad_data(idx, "OCC Nori File Not Accelerated")
                if self.use_oss_data:
                    nori_path = nori_info["nori_path"]
                else:
                    return None
            try:
                vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                data = vreader.get(nori_id)
                occ_gt, cam_mask_3d, lidar_mask_3d, _ = self.load_h5_nori_file(data)
            except Exception:
                self._fun_log_bad_data(idx, "OCC Nori File Read Error")
                return None

        else:
            # 替换 occ_dump 版本
            if not occ_path.endswith(".h5"):
                self._fun_log_bad_data(idx, "Freespace File Not H5")
                return None
            # 判断 gpfs 路径优先
            gpfs_path = trans_oss_to_gpfs_path(occ_path)
            if os.path.exists(gpfs_path):
                try:
                    occ_gt, cam_mask_3d, lidar_mask_3d, _ = self.load_h5_file(gpfs_path)
                except Exception:
                    self._fun_log_bad_data(idx, "H5 Freespace GPFS Filepath Read Error")
                    return None
            else:
                self._fun_log_bad_data(idx, "H5 Freespace File Not Accelerated")
                if self.use_oss_data:
                    try:
                        occ_gt, cam_mask_3d, lidar_mask_3d, _ = self.load_h5_nori_file(occ_path)
                    except Exception:
                        self._fun_log_bad_data(idx, "H5 Freespace Filepath Read Error")
                        return None
                else:
                    return None

        semantic_list = []
        semantic_mask_list = []
        for z_idx, cur_z_range in enumerate(self.z_range):
            if z_idx == 0:  # 第一层是否需要特殊判断，最低点的范围是否可控
                height_filted_data = occ_gt[..., : cur_z_range[1]]
            else:
                height_filted_data = occ_gt[..., cur_z_range[0] : cur_z_range[1]]

            occ_2p5_layer = process_3d_to_2p5(height_filted_data)
            occ_2p5_layer = self.process_label(occ_2p5_layer, cam_mask_2d, lidar_mask_2d, vis_2d_mask, clip_info)
            semantic_list.append(occ_2p5_layer.astype(np.uint8))
            semantic_mask_list.append(freespace_mask.astype(np.bool_))
        if len(semantic_list) == 1:
            semantic_list = semantic_list[0]
            semantic_mask_list = semantic_mask_list[0]
        res_dict["semantic"] = semantic_list
        res_dict["semantic_mask"] = semantic_mask_list

        return res_dict

    def _fun_log_bad_data(
        self,
        idx,
        msg,
    ):
        if self.log_bad_data:
            self.loader_output["frame_data_list"].cal_useless_data(idx, msg, "occ")
