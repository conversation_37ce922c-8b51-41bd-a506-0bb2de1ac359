from .base import AnnotationBase
from perceptron.data.det3d.utils.functional import initialize_object


class E2EAnnotations(AnnotationBase):
    def __init__(
        self,
        loader_output,
        mode,
        annotation_cfgs,
        sub_level_annos=False,
    ) -> None:
        super(E2EAnnotations, self).__init__(loader_output, mode)
        self.tasks = dict()
        self.sub_level_annos = sub_level_annos

        assert all(
            issubclass(ann_cfg["type"], AnnotationBase) for ann_cfg in annotation_cfgs.values()
        ), "Annotation type should be derived class of AnnotationBase"

        for task, config in annotation_cfgs.items():
            config.update({"loader_output": loader_output, "mode": mode})
            self.tasks[task] = initialize_object(config)

    def __getitem__(self, index):

        data_dict = dict()
        task_list = []
        for task, ann_reader in self.tasks.items():
            result = ann_reader[index]
            if result is None:
                return None
            if self.sub_level_annos:
                data_dict[task] = result
            else:
                data_dict.update(result)
            task_list.append(task)
        data_dict["task_list"] = task_list
        return data_dict

    def pre_check_not_skip(self, index: int) -> bool:
        """check anno / sensor valid, return True if all valid, False if some is not valid."""
        for task, ann_reader in self.tasks.items():
            if not ann_reader.pre_check_not_skip(index):
                return False
        return True
