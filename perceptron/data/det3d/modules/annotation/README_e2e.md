
## Readme for E2E Annotations

Currently the predefined tasks contain 5 forms of labels, including detection box and Bezier map, reserving occlusion/plan/rv for additional tasks

## Box Annotation Configurations

### class AnnotationDet
* filter_outlier_boxes: Filtering of specific categories of boxes based on preset dimensions, with oversized boxes replaced by masks, used to mitigate the effects of labelling errors.
* filter_outlier_frames: Deprecated, retained only as a non-e2e exp adaptation.
* filter_empty_2d_bboxes: Treat gt boxes without the 2d occlusion property as 2d occlusion invisible.
* filter_empty_frames: When there is no non-masked class gt frame inside the roi, the whole frame is discarded.

### class AnnotationTrack


## Map Annotation Configurations
