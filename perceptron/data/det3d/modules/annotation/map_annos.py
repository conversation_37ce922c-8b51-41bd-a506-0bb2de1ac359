import bisect
import torch
import cv2
import math
import copy
import traceback
import numpy as np
import random
import os
from copy import deepcopy
import matplotlib.pyplot as plt
from pyquaternion import Quaternion
from scipy.stats import multivariate_normal
from perceptron.data.det3d.modules.annotation.base import AnnotationBase
from shapely.geometry import MultiLineString, Polygon, LineString, LinearRing, Point, box

from perceptron.utils.map_utils.line_merge_utils import (
    find_connected_components,
    build_graph,
    merge_line_attr,
    cut_new_info,
    unordered_merge_line_attr,
)
from perceptron.utils.map_utils.line_merge_utils import build_ordered_graph, build_ordered_graph_laneline  # 有向图
from perceptron.utils.map_utils.double_line_utils import merge_double_line_all
import json
import refile
from scipy.spatial import distance
from scipy.spatial import KDTree


def inter_lane(lane, interp_dist=0.1):
    if len(lane) < 2:
        return lane
    lane = lane[lane[:, 0].argsort()]
    lane_x, lane_y, lane_z = lane[:, 0], lane[:, 1], lane[:, 2]
    lane_x_min, lane_x_max = min(lane_x), max(lane_x)
    lane_x_inter = np.append(np.arange(lane_x_min, lane_x_max, interp_dist), lane_x_max)

    lane_x_inter_np = np.array(lane_x_inter)
    lane_x, lane_y, lane_z = np.array(lane_x), np.array(lane_y), np.array(lane_z)
    lane_y_inter = np.interp(lane_x_inter_np, lane_x, lane_y)
    lane_z_inter = np.interp(lane_x_inter_np, lane_x, lane_z)
    lane_x_inter, lane_y_inter, lane_z_inter = lane_x_inter, lane_y_inter, lane_z_inter
    lane_inter = np.stack([lane_x_inter, lane_y_inter, lane_z_inter], axis=1)
    return lane_inter


def inter_lane_uniform(tmp_points, interp_dist=0.2):
    if len(tmp_points) < 2:
        return tmp_points

    points = np.array(tmp_points)

    # Step 1: 计算累计距离作为参数 u
    distances = np.zeros(len(points))
    for i in range(1, len(points)):
        distances[i] = distances[i - 1] + np.linalg.norm(points[i] - points[i - 1])

    total_length = distances[-1]
    num_samples = int(np.ceil(total_length / interp_dist)) + 1

    # Step 2: 创建等距采样点的位置
    sample_distances = np.linspace(0, total_length, num_samples)

    # Step 3: 分段线性插值
    lane_inter = []
    current_idx = 0
    for d in sample_distances:
        # 找到当前处于哪一段线段上
        while current_idx < len(distances) - 1 and d > distances[current_idx + 1]:
            current_idx += 1

        start_point = points[current_idx]
        end_point = points[current_idx + 1]
        segment_start_dist = distances[current_idx]
        segment_end_dist = distances[current_idx + 1]
        t = (d - segment_start_dist) / max(segment_end_dist - segment_start_dist, 1e-8)  # 防止除零

        interpolated_point = (1 - t) * start_point + t * end_point
        lane_inter.append(interpolated_point)

    lane_inter = np.array(lane_inter)

    # Step 4: 确保首尾点与原始数据一致
    lane_inter[0] = points[0]
    lane_inter[-1] = points[-1]

    return lane_inter


class AnnotationMap(AnnotationBase):
    def __init__(
        self,
        loader_output,
        mode,
        label_key="bev",  # 在 json 中对应的字段
        map_class_dict={},
        map_cam_dict={},
        arrow_name_id_map={},
        map_point_cloud_range=[0, -15, -6, 100, 15, 6],
        map_lidar_range=[],
        seg_canvas_size=[64, 120],
        seg_thickness=2,
        scene_weight=False,
        warp_tgt_json=None,
        bev_h=100,
        bev_w=60,
        attr_num=3,
        filter_start=60,
        filter_length=20,
        except_dist=30,
        bda_aug_conf=None,
        use_manual_mask=False,  # 历史数据均不使用valid_mask
        remove_reverse=False,  # 过滤掉逆向车道
        curb_only=False,  # 是否只训练curb
        use_interp=True,  # 保险起见还是插值
        data_source="manual",  # 高精需要特殊处理
    ) -> None:
        super(AnnotationMap, self).__init__(loader_output, mode)
        self.label_key = label_key
        self.map_class_dict = map_class_dict
        self.map_cam_dict = map_cam_dict
        self.map_point_cloud_range = map_point_cloud_range
        self.map_lidar_range = map_lidar_range
        self.class_names = list(self.map_class_dict.keys())
        self.cam_names = list(self.map_cam_dict.keys())
        self.arrow_name_id_map = arrow_name_id_map
        self.seg_canvas_size = seg_canvas_size
        self.seg_thickness = seg_thickness
        self.scene_weight = scene_weight
        self.bev_h = bev_h
        self.bev_w = bev_w
        self.attr_num = attr_num
        self.mode = mode
        self.warp_tgt_json = warp_tgt_json
        self.filter_start = filter_start
        self.filter_length = filter_length
        self.except_dist = except_dist
        self.calibrated_sensors = None
        self.bda_aug_conf = bda_aug_conf
        self.use_manual_mask = use_manual_mask  # 历史数据使用lidar-pts作为依据, manual_mask
        self.remove_reverse = remove_reverse  # 是否过滤对向车道线/路沿
        self.curb_only = curb_only  # 是否只训练路沿
        self.use_interp = use_interp  # 是否对标注插值
        if self.warp_tgt_json is not None:
            self.calibrated_sensors = json.load(refile.smart_open(self.warp_tgt_json))[
                "calibrated_sensors"
            ]  # 只用来生产 semantic rv gt

        # update 05.31, 高精特殊处理
        self.data_source = data_source

    def __getitem__(self, idx):
        frame_data_list = self.loader_output["frame_data_list"]
        cumulative_sizes = self.loader_output["frame_data_list"].cumulative_sizes
        scene_id = bisect.bisect_right(cumulative_sizes, idx)
        # lidar_pose = frame_data_list[idx]['lidar_pose']
        calibrated_sensors = self.loader_output["calibrated_sensors"][scene_id]
        timestamp = frame_data_list[idx].get("timestamp", -1)
        # fpath = frame_data_list[idx]['fpath']

        # add bda-aug
        rotate_bda, scale_bda, flip_dx, flip_dy = self.sample_bda_augmentation()
        self.bda_aug_param_dict = {
            "rotate_bda": rotate_bda,
            "scale_bda": scale_bda,
            "flip_dx": flip_dx,
            "flip_dy": flip_dy,
        }

        tran_mats_dict = self.get_tran_mats_dict(calibrated_sensors, self.map_cam_dict)
        item = dict(
            index_in_dataset=idx,
            map_tran_mats_dict=tran_mats_dict,
            timestamp=timestamp,
            map_gt={},
            map_attr={},
            map_label_mask={},
        )

        if self.label_key not in frame_data_list[idx]:
            if "train" in self.mode:
                return None
            else:
                return item
        """
        1. laneline curb 分离;

        2. 分段车道线属性融合(Y 型重构)；

        3. line gt 按照预定点数重构；
        """
        bev_info = frame_data_list[idx][self.label_key]
        # fix format bugs
        for key in bev_info:
            if bev_info[key] is None:
                bev_info[key] = {}
        try:
            # import time
            # t1 = time.time()
            # remove centerline! update 05.24
            if "center_lane" in bev_info:
                bev_info["center_lane"] = {}

            # bda-auged bev_info
            bda_bev_info, post_rot_bda = self._bev_transform_allinfo(bev_info, rotate_bda, scale_bda, flip_dx, flip_dy)

            map_scene_weight, map_gt, label_mask, map_gauss, map_attr, valid_region_norm = self.get_map_gt(
                bev_info, tran_mats_dict
            )
            (
                map_scene_weight,
                bda_map_gt,
                bda_label_mask,
                bda_map_gauss,
                bda_map_attr,
                bda_valid_region_norm,
            ) = self.get_map_gt(bda_bev_info, tran_mats_dict)

            # aux rv seg supervision
            if self.calibrated_sensors is not None:
                calibrated_sensors = self.calibrated_sensors  # 为了适配虚拟相机，但是在虚拟相机 + aug 时又会出现新的问题
            semantic_map, img_ins_semantic_map = self.prepare_img_segment_map(
                map_gt, calibrated_sensors, cam_names=self.cam_names, idx=idx
            )

            # 根据规则过滤 map gt, 注意过滤完后和 semantic 不是 instance level 的一致了，如果要完全一致，下面这句需要搬到 semantic 生成的前面
            bda_map_gt, bda_map_attr = self.filter_map_gt(
                bda_map_gt,
                bda_map_attr,
                filter_start=self.filter_start,
                filter_length=self.filter_length,
                except_dist=self.except_dist,
            )

            lane_bev_segment_map = self.get_lane_bev_segment_map(map_gt)  # line, (h, w)

            # add manual-mask, 使用逻辑: 75%的预测点落在mask内则不监督该pred，认为当前gt存在局限性。
            bev_manual_mask = self.get_manual_mask(bda_bev_info, tran_mats_dict)  # line, (h, w)

            # t2 = time.time()
            # print("maptracker: {}".format(t2-t1))
        except BaseException:
            print("bad!!! {}".format(idx))
            return None

        item.update(
            dict(
                map_gt=bda_map_gt,
                map_attr=bda_map_attr,
                map_gauss=bda_map_gauss,
                map_label_mask=bda_label_mask,
                map_scene_weight=map_scene_weight,
                valid_region=bda_valid_region_norm,
                img_semantic_seg=semantic_map,
                bda_aug_param_dict=self.bda_aug_param_dict,
                post_rot_bda=post_rot_bda,
                lane_bev_segment_map=lane_bev_segment_map,  # add seg
                bev_manual_mask=bev_manual_mask,  # add manual mask
            )
        )

        # check if gt is valid
        flag = False
        for cls in bda_map_gt:
            if len(bda_map_gt[cls]) > 0:
                flag = True
        if flag or "val" in self.mode:
            return item
        else:
            # print(f'no map gt, scene id: {scene_id}, idx: {idx}')
            return None

    def get_manual_mask(self, bev_info, tran_mats_dict):
        """
        根据lidar系归一化坐标, 生成lane的map, 包括了laneline和curb
        """
        target_h, target_w = self.bev_h, self.bev_w
        bev_manual_mask = np.zeros((target_h, target_w), dtype=np.int32)
        ego2lidar = tran_mats_dict["ego2lidar"]

        if not self.use_manual_mask:
            bev_manual_mask -= 1
        else:
            for mask_id, mask_polygon in bev_info["mask"].items():
                mask_polygon_np = np.array(mask_polygon["points"])
                homo_mask_polygon_np = np.concatenate(
                    [mask_polygon_np, np.ones((mask_polygon_np.shape[0], 1))], axis=-1
                )
                lidar_pts = (ego2lidar @ homo_mask_polygon_np.T).T[:, :3]  # (N, 3)
                norm_lidar_pts = self.norm_line(lidar_pts)
                poly_lidar_pts = norm_lidar_pts
                poly_lidar_pts[:, :1] = norm_lidar_pts[:, :1] * target_w
                poly_lidar_pts[:, 1:2] = norm_lidar_pts[:, 1:2] * target_h
                poly_lidar = poly_lidar_pts[:, :2]

                # 绘制
                cv2.fillPoly(bev_manual_mask, [poly_lidar.astype(np.int32)], (1))
        # cv2.imwrite("bev.jpg", np.uint8(bev_manual_mask*255))
        return bev_manual_mask

    def get_lane_bev_segment_map(self, map_gt):
        """
        根据lidar系归一化坐标, 生成lane的map, 包括了laneline和curb
        """
        target_h, target_w = self.bev_h, self.bev_w
        bev_segment_map = np.zeros((target_h, target_w), dtype=np.uint8)
        for cls_id, lanes in map_gt.items():
            if cls_id not in [0, 1]:
                continue
            for lane_id, permute_lane_points in enumerate(lanes):
                lane_points = permute_lane_points[0]  # lidar坐标系, 第二维是前后, 第一维是左右
                for point_idx in range(len(lane_points) - 1):
                    start_point = (
                        round(target_w * lane_points[point_idx][0]),
                        round(target_h * lane_points[point_idx][1]),
                    )
                    end_point = (
                        round(target_w * lane_points[point_idx + 1][0]),
                        round(target_h * lane_points[point_idx + 1][1]),
                    )
                    cv2.line(bev_segment_map, start_point, end_point, 1, 1)
        # cv2.imwrite("bev.jpg", np.uint8(bev_segment_map*255))
        return bev_segment_map

    def sample_bda_augmentation(self):
        """Generate bda augmentation values based on bda_config."""
        if "train" in self.mode:
            rotate_bda = np.random.uniform(*self.bda_aug_conf["rot_lim"])
            scale_bda = np.random.uniform(*self.bda_aug_conf["scale_lim"])
            flip_dx = np.random.uniform() < self.bda_aug_conf["flip_dx_ratio"]
            flip_dy = np.random.uniform() < self.bda_aug_conf["flip_dy_ratio"]
        else:
            rotate_bda = 0
            scale_bda = 1.0
            flip_dx = False
            flip_dy = False
        return rotate_bda, scale_bda, flip_dx, flip_dy

    def _bev_transform_allinfo(self, bev_info, rotate_angle, scale_ratio, flip_dx, flip_dy):
        # 对所有散点进行ego下的bda
        rotate_angle = torch.tensor(rotate_angle / 180 * np.pi)
        rot_sin = torch.sin(rotate_angle)
        rot_cos = torch.cos(rotate_angle)
        rot_mat = torch.tensor([[rot_cos, -rot_sin, 0], [rot_sin, rot_cos, 0], [0, 0, 1]]).float()
        scale_mat = torch.tensor([[scale_ratio, 0, 0], [0, scale_ratio, 0], [0, 0, scale_ratio]]).float()
        flip_mat = torch.tensor([[1, 0, 0], [0, 1, 0], [0, 0, 1]]).float()
        if flip_dx:
            flip_mat = flip_mat @ torch.tensor([[-1, 0, 0], [0, 1, 0], [0, 0, 1]]).float()
        if flip_dy:
            flip_mat = flip_mat @ torch.tensor([[1, 0, 0], [0, -1, 0], [0, 0, 1]]).float()
        rot_mat = flip_mat @ (scale_mat @ rot_mat)

        bda_bev_info = copy.deepcopy(bev_info)

        for cls_key, cls_dict in bev_info.items():
            for ins_id, ins_item in cls_dict.items():
                if "points" not in ins_item:
                    continue
                tmp_points = np.array(ins_item["points"])
                if len(tmp_points) < 2:
                    continue
                # print("cls:", cls_key, len(tmp_points))
                point_ordered_tensor = torch.from_numpy(tmp_points).float()
                bda_point_ordered_tensor = (rot_mat @ point_ordered_tensor.unsqueeze(-1)).squeeze(-1)
                bda_bev_info[cls_key][ins_id]["points"] = bda_point_ordered_tensor.numpy().tolist()
                # 对应bev_info[cls_key][ins_id]

        # 将rot_mat转换为homo-mat
        post_rot_bda_expand = torch.cat([rot_mat, torch.tensor([[0.0, 0.0, 0.0]]).transpose(1, 0)], 1)
        post_rot_bda_expand = torch.cat([post_rot_bda_expand, torch.tensor([[0.0, 0.0, 0.0, 1.0]])], 0)
        post_rot_bda_expand = post_rot_bda_expand.unsqueeze(0).numpy()  # (4, 4) -> (1, 4, 4)

        return bda_bev_info, post_rot_bda_expand

    def filter_map_gt(self, map_gt, map_attr, filter_start=60, filter_length=20, except_dist=10):
        # 按各种规则过滤 map_gt, 只过滤车道线和路沿 int(cls) < 2
        # rule 1. 过滤掉起点在 filter_start 之外的
        # rule 2. 过滤短线（长度小于 filter_length)，起点小于 except_dist 的除外
        for cls in map_gt:
            new_value = []
            new_attr = []
            for i, item in enumerate(map_gt[cls]):  # [n_ins, permute_n, 20, 3]
                if len(item) == 0:
                    continue
                if int(cls) < 2:
                    line = np.array(item[0])  # [20, 3]
                    recover_line = self.recover_pts(line)

                    # rule 1
                    min_y = np.min(recover_line[:, 1])
                    if min_y > filter_start:
                        continue

                    # rule 2
                    length = LineString(recover_line).length
                    if (length < filter_length) and (min_y > except_dist):
                        continue

                new_value.append(item)
                new_attr.append(map_attr[cls][i])

            map_gt[cls] = new_value
            map_attr[cls] = new_attr
        return map_gt, map_attr

    def prepare_img_segment_map(self, map_gt_ori, calibrated_sensors, cam_names, idx=0):
        # if env.is_volcano_platform():
        #     input_shape = [calibrated_sensors[cam]["intrinsic"]["resolution"][::-1] for cam in cam_names]  # 原始图像大小和 tgt 一致
        if "target_resolution" in calibrated_sensors[cam_names[0]]["intrinsic"]:
            input_shape = [
                calibrated_sensors[cam]["intrinsic"]["target_resolution"][::-1] for cam in cam_names
            ]  # 200w 对应 [[2160, 3840]]
        else:
            input_shape = [calibrated_sensors[cam]["intrinsic"]["resolution"][::-1] for cam in cam_names]  # 使用目标车辆的参数

        img_ins_semantic_map = (
            []
        )  # [num_cam, num_cls, h, w]  background 为 -1， 前景对应 ins id, cls 顺序对应 self.class_names 顺序
        for cam_id in range(len(input_shape)):
            # get footprint2img
            cam = cam_names[cam_id]
            K = calibrated_sensors[cam]["intrinsic"]["K"]
            ext_R = calibrated_sensors[cam]["extrinsic"]["transform"]["rotation"]
            ext_t = calibrated_sensors[cam]["extrinsic"]["transform"]["translation"]
            lidar2cam = self.transform_matrix(
                rotation=Quaternion([ext_R["w"], ext_R["x"], ext_R["y"], ext_R["z"]]),
                translation=[ext_t["x"], ext_t["y"], ext_t["z"]],
                inverse=False,
            )
            K_44 = np.eye(4).astype(np.float32)
            K_44[:3, :3] = K

            img_h, img_w = input_shape[cam_id]  # [2160, 3840]
            canvas_h, canvas_w = self.seg_canvas_size
            h_ratio = float(canvas_h) / img_h
            w_ratio = float(canvas_w) / img_w
            seg = np.ones((len(self.class_names) - 1, canvas_h, canvas_w)) * -1  # cam-level

            map_gt = copy.deepcopy(map_gt_ori)
            map_gt = self.recover_line(map_gt)
            for cls in map_gt:
                cls = int(cls)
                if cls in [2, 6]:
                    continue  # TODO stopline、entrance new gt seg 支持
                points = map_gt[cls]  # [n_ins, n_permute, n_pts, 3]
                for ins_id in range(len(points)):
                    line = np.array(points[ins_id][0])
                    if not len(line) > 1:
                        continue
                    new_lane = np.ones((np.array(line).shape[0], 4))
                    new_lane[:, :3] = line
                    cam_line = (lidar2cam @ new_lane.T).T  # [N, 4]
                    cam_line = cam_line[cam_line[:, 2] > 0]
                    uv_line = (K @ cam_line[:, :3].T).T
                    if not len(uv_line) > 1:
                        continue
                    uv_line = uv_line / uv_line[:, 2:3]
                    uv_pts = uv_line[:, :2].round().astype(np.int32)

                    uv_pts_scaled = copy.deepcopy(uv_pts)
                    uv_pts_scaled[:, 0] = uv_pts_scaled[:, 0] * w_ratio
                    uv_pts_scaled[:, 1] = uv_pts_scaled[:, 1] * h_ratio
                    seg[cls] = cv2.polylines(  # TODO: 区分闭合元素和非闭合元素
                        seg[cls],
                        [uv_pts_scaled],
                        isClosed=False,
                        thickness=self.seg_thickness,
                        color=ins_id + 1,
                    )
                # tmp = cv2.resize(seg[cls], (600, 300))
                # cv2.imwrite(f'vis_seg_ori_laneline_curb_{idx}_{cls}_{ins_id}.jpg', tmp*100)
                # from IPython import embed; embed()

            img_ins_semantic_map.append(seg)
        semantic_map = np.array(img_ins_semantic_map) > 0
        # cls_seg_rgb = np.zeros((semantic_map[0][0].shape[0], semantic_map[0][0].shape[1], 3), dtype=np.uint8)
        # cls_seg_rgb[semantic_map[0][0]] = (0, 255, 100)
        # cls_seg_rgb[semantic_map[0][1]] = (0, 0, 250)
        # cv2.imwrite(f'./tmp/vis_seg_ori_laneline_curb_{idx}.jpg', cls_seg_rgb)
        return semantic_map, img_ins_semantic_map

    def filter_lane_curb(self, info, min_thre=0.01):
        """过滤掉 1. 只有一个点的线 2. 长度小于 0.5m 的线
        并去掉重复点, 相邻点 xyz 距离均小于 min_thre 认为是重复点
        Args:
            info (_type_): _description_
        """
        bev_info = copy.deepcopy(info)
        lanes = bev_info["lane"]
        out_lanes = {}
        for idx in lanes:
            line = lanes[idx]["points"]
            new_line = []  # 去掉重复点, xyz 间隔都小于 1cm 认为是重复点
            for i, pt in enumerate(line):
                if i == 0:
                    new_line.append(pt)
                else:
                    last_pt = new_line[-1]
                    if (
                        abs(pt[0] - last_pt[0]) < min_thre
                        and abs(pt[1] - last_pt[1]) < min_thre
                        and abs(pt[2] - last_pt[2]) < min_thre
                    ):
                        continue
                    new_line.append(pt)
            if len(new_line) < 2:
                continue
            out_lanes[idx] = copy.deepcopy(lanes[idx])
            out_lanes[idx]["points"] = new_line
        bev_info["lane"] = out_lanes
        return bev_info

    def filter_lane_curb_by_range(self, info):
        """
        过滤bev范围外的点
        """
        bev_info = copy.deepcopy(info)
        lanes = bev_info["lane"]
        new_lanes = copy.deepcopy(lanes)
        ego_xmin = self.map_lidar_range[1]
        ego_xmax = self.map_lidar_range[4]
        ego_ymin = self.map_lidar_range[0]
        ego_ymax = self.map_lidar_range[3]
        pc_range = [ego_xmin, ego_ymin, -6.0, ego_xmax, ego_ymax, 6.0]  # todo: hardcode

        xmin, ymin, xmax, ymax = pc_range[0], pc_range[1], pc_range[3], pc_range[4]
        all_lane_id = list(lanes.keys())
        for _lane_id in all_lane_id:
            points = lanes[_lane_id]["points"]
            for i in range(len(points) - 1, -1, -1):
                p0_ego = np.array(points[i])
                x, y, z = p0_ego
                if x > xmin and x < xmax and y > ymin and y < ymax:
                    continue
                else:
                    del new_lanes[_lane_id]["points"][i]
        bev_info["lane"] = new_lanes
        return bev_info

    def interp_bev_info(self, bev_info):
        """
        对吉利数据做插值
        """
        bev_info = copy.deepcopy(bev_info)
        lanes = bev_info["lane"]
        new_lanes_gt = copy.deepcopy(lanes)
        for lane_id, lane_item in lanes.items():
            tmp_points = lane_item["points"]
            tmp_points_interp = inter_lane_uniform(np.array(tmp_points), interp_dist=0.2)
            new_lanes_gt[lane_id]["points"] = tmp_points_interp.tolist()
        bev_info["lane"] = new_lanes_gt
        return bev_info

    def filter_reverse(self, bev_info):
        """
        过滤掉逆向车道, 通常为不可见的车道线
        """
        bev_info = copy.deepcopy(bev_info)
        lanes = bev_info["lane"]
        new_lanes_gt = copy.deepcopy(lanes)
        for lane_id, lane_item in lanes.items():
            if lane_item["attribute"]["direction"] == "Reverse":
                new_lanes_gt.pop(lane_id)
        bev_info["lane"] = new_lanes_gt
        return bev_info

    def get_map_gt(self, bev_info, tran_mats_dict):
        map_scene_weight, map_gt, box_gauss, map_attr, label_mask = 1, dict(), dict(), dict(), dict()
        if self.use_interp:
            # 插值
            bev_info = self.interp_bev_info(bev_info)
        if self.remove_reverse:
            bev_info = self.filter_reverse(bev_info)  # update 04.11, 过滤对向车道
        bev_info = self.filter_lane_curb_by_range(bev_info)  # 对bev range外的点做过滤
        bev_info = self.filter_lane_curb(bev_info)
        bev_info_lane_curb = self.split_lane_to_laneline_curb(bev_info)
        if self.curb_only:
            bev_info_lane_curb["laneline"] = {}
        map_scene_weight = self.get_map_scene_weight(bev_info_lane_curb)
        valid_region_norm = self.get_valid_region(bev_info, tran_mats_dict["ego2lidar"])
        for class_name, class_info in self.map_class_dict.items():
            if class_name == "mask":
                if class_name in bev_info and bev_info[class_name] is not None:
                    label_mask["invalid_mask"], _, _ = self.get_box_gt(
                        bev_info[class_name], class_name, tran_mats_dict, class_info
                    )
                    continue
            map_gt[class_info["id"]] = []
            map_attr[class_info["id"]] = []

            if class_name in ["laneline", "curb"]:
                # laneline 和 curb 做线 merge 的逻辑是一致的，生成最后 attr tensor 的做法不同
                if class_name == "laneline":
                    cls_info = copy.deepcopy(bev_info_lane_curb[class_name])
                    try:
                        bev_info_lane_curb[class_name] = merge_double_line_all(
                            cls_info
                        )  # 这里输入的还是 ego 坐标系，所以黄线处理内的逻辑是 ego 坐标系来做的
                    except BaseException:
                        bev_info_lane_curb[class_name] = cls_info

                bev_info_rebuild = self.get_line_merged_info(bev_info_lane_curb[class_name], class_name=class_name)
                # bev_info_rebuild
                # merge 过的 {'id': {'points': [], 'attribute': []}}
                # 没 merge 过的 {'id': {'points': [], 'attribute': dict()}}
                pts_gt, pts_attrs = self.get_line_gt(bev_info_rebuild, class_name, tran_mats_dict, class_info)
                map_gt[class_info["id"]] = pts_gt
                map_attr[class_info["id"]] = pts_attrs

            elif class_name in ["stopline", "entrance"]:
                if class_name in bev_info:
                    pts_gt, pts_attrs = self.get_point_vector_gt(
                        bev_info[class_name], class_name, tran_mats_dict, class_info, other_info=bev_info["lane"]
                    )
                    map_gt[class_info["id"]] = pts_gt
                    map_attr[class_info["id"]] = pts_attrs
            elif class_name in ["crosswalk", "noparking", "arrow"]:
                if class_name in bev_info:
                    map_gt[class_info["id"]], box_gauss[class_info["id"]], map_attr[class_info["id"]] = self.get_box_gt(
                        bev_info[class_name], class_name, tran_mats_dict, class_info, other_info=bev_info["lane"]
                    )

        return map_scene_weight, map_gt, label_mask, box_gauss, map_attr, valid_region_norm

    def get_valid_region(self, bev_info, ego2lidar):

        valid_region_list = []
        for class_name, info in bev_info.items():
            if class_name == "mask":
                continue  # mask 比较零散，暂时还不考虑加入mask
            for idx, info_one in info.items():
                if "points" not in info_one:
                    # print("can not find points in info_one: {info_one}")
                    continue
                ego_pts = np.array(info_one["points"])
                if len(ego_pts.shape) < 3:
                    continue  # fix: 2025.04.16, few pts..
                lidar_pts = self.ego2lidar_and_intersection(ego_pts, ego2lidar, class_name)
                lidar_pts = np.array(lidar_pts)
                if len(lidar_pts.shape) < 3:
                    continue
                valid_region_list.append(self.get_min_max(lidar_pts[:, 0], lidar_pts[:, 1]))

        if len(valid_region_list) > 0:
            valid_region_list = np.array(valid_region_list)
            valid_region = self.get_min_max(valid_region_list[:, :, 0], valid_region_list[:, :, 1])
        else:
            # 注意region坐标系
            valid_region = [
                [self.map_lidar_range[0], self.map_lidar_range[1], 0],
                [self.map_lidar_range[3], self.map_lidar_range[4], 0],
            ]
        valid_region_norm = self.norm_line(np.array(valid_region)).tolist()
        return valid_region_norm

    def get_min_max(self, x_coords, y_coords):
        # 计算 minx, miny, maxx, maxy
        minx = np.min(x_coords)
        miny = np.min(y_coords)
        maxx = np.max(x_coords)
        maxy = np.max(y_coords)

        return [[minx, miny, 0], [maxx, maxy, 0]]

    def convert_attr(self, input_all_attrs, cls_name, direction):
        # 转换接口定义见：https://wiki.megvii-inc.com/pages/viewpage.action?pageId=589476516
        all_attrs = []
        # attr_num = 6 # 统一使用6种属性 虚实 / 颜色 / 形状 / 是否栏杆 / 箭头属性 / 元素方向

        attr_num = self.attr_num  # 统一使用3种属性 虚实 / 颜色 / 是否栏杆

        line_num = len(input_all_attrs)
        if line_num == 0:
            return []
        pt_num = len(input_all_attrs[0])
        if cls_name == "laneline":
            for i in range(line_num):
                converted_line = []
                for j in range(pt_num):
                    pt_attr = input_all_attrs[i][j]

                    new_attr = [0 for _ in range(attr_num)]  # 属性 init
                    lane_type = pt_attr["lane_type"]

                    shape = lane_type.get("shape", "NAN")

                    # dashed 实虚线  0 -> 未知  1 -> 实线  2 -> 虚线
                    dashed = lane_type.get("dashed", "NAN")
                    if dashed == "solid" and shape == "single":  # 单实线
                        new_attr[0] = 1
                    elif dashed == "dotted" and shape == "single":  # 单虚线
                        new_attr[0] = 2
                    elif dashed == "solid+dotted" and shape == "double":  # 实虚
                        new_attr[0] = 3
                    elif dashed == "dotted+solid" and shape == "double":  # 虚实
                        new_attr[0] = 4
                    elif dashed == "solid" and shape == "double":  # 双实线
                        new_attr[0] = 5
                    elif dashed == "dotted" and shape == "double":  # 双虚线
                        new_attr[0] = 6
                    else:
                        new_attr[0] = 0

                    # color 颜色: 0 -> 未知 1 -> 白色线 2 -> 黄色线
                    color = lane_type.get("color", "NAN")
                    if color == "white":
                        new_attr[1] = 1
                    elif color == "yellow":
                        new_attr[1] = 2
                    else:
                        new_attr[1] = 0

                    # shape 形状: 0 -> 未知  1 -> 单线  2 -> 双线
                    # shape = lane_type.get('shape', 'NAN')
                    # if shape == 'double': # 双线
                    #     new_attr[2] = 2
                    # elif shape == 'single':
                    #     new_attr[2] = 1  # 单线
                    # else:
                    #     new_attr[2] = 0

                    # 是否栏杆：车道线没有该属性，按照unknown处理
                    if pt_attr["diversion_line"] is True:
                        new_attr[2] = 3  # update 04.08, 导流线
                    else:
                        new_attr[2] = 0

                    # # 元素方向 0 -> 未知 1 -> 正向 2 -> 反向 3 -> 双向
                    # new_attr[5] = direction
                    converted_line.append(new_attr)
                all_attrs.append(converted_line)

        elif cls_name == "curb":
            for i in range(line_num):
                converted_line = []
                for j in range(pt_num):
                    pt_attr = input_all_attrs[i][j]
                    new_attr = [0 for _ in range(attr_num)]
                    guardrail = pt_attr.get("guardrail", "-999")
                    guardrail = str(guardrail)
                    if guardrail.isdigit():
                        if int(guardrail) == 1:  # 栏杆
                            new_attr[2] = 2
                        elif int(guardrail) == "-999":  # 没这个字段，代表标注或者预标没标
                            new_attr[2] = 0
                        else:
                            new_attr[2] = 0
                    else:
                        if guardrail == "unknown":
                            new_attr[2] = 0
                        elif guardrail == "NAN":
                            new_attr[2] = 1

                    # 虚实 / 颜色 / 形状：路沿没有该属性，按照unknown处理
                    new_attr[0] = 0
                    new_attr[1] = 0
                    # new_attr[2] = 0

                    # # 元素方向 0 -> 未知 1 -> 正向 2 -> 反向 3 -> 双向
                    # new_attr[5] = direction
                    converted_line.append(new_attr)
                all_attrs.append(converted_line)

        elif cls_name == "arrow":
            for i in range(line_num):
                converted_line = []
                for j in range(pt_num):
                    pt_attr = input_all_attrs[i][j]
                    new_attr = [0 for _ in range(attr_num)]

                    # arrow_attr_list = []
                    # for name, value in pt_attr.items():
                    #     if value in [1, '1']:
                    #         arrow_attr_list.append(name)

                    # all_permutations = list(permutations(arrow_attr_list))
                    # for attr_i, attr_one in enumerate(all_permutations):
                    #     arrow_attr_str = "+".join(attr_one)

                    #     if arrow_attr_str in self.arrow_name_id_map and arrow_attr_str:
                    #         # print(f"find arrow attr: {arrow_attr_str}-{arrow_name_id_map[arrow_attr_str]}")
                    #         new_attr[4] = self.arrow_name_id_map[arrow_attr_str]
                    #         break
                    #     # elif attr_i == len(all_permutations)-1 and arrow_attr_str:
                    #     #     print(f"find unknown arrow attr: {arrow_attr_str}")

                    # # 元素方向 0 -> 未知 1 -> 正向 2 -> 反向 3 -> 双向
                    # new_attr[5] = direction
                    converted_line.append(new_attr)
                all_attrs.append(converted_line)
        else:
            for i in range(line_num):
                converted_line = []
                for j in range(pt_num):
                    new_attr = [0 for _ in range(attr_num)]
                    # 虚实 / 颜色 / 形状 / 是否栏杆：停止线/人行横道等没有该属性，按照unknown处理
                    converted_line.append(new_attr)
                all_attrs.append(converted_line)

        return all_attrs

    def get_line_merged_info(self, bev_info, merge_thre=0.5, class_name="default"):  # 为了兼容分岔合并点噪声
        """线型和属性merge, 支持 V 型线 / 倒 V 型 / X 型 / Y 型 / 多分叉

        Args:
            bev_info (_type_): _description_
            merge_thre (float, optional): _description_. Defaults to 0.05.

        Returns:
            _type_: _description_
        """
        id_list = list(bev_info.keys())
        lines = [bev_info[ins_id]["points"] for ins_id in id_list]

        if self.data_source == "hd":
            # 高精需要按老方式走，不保证点序。。。
            graph = build_graph(lines, thre=merge_thre)
            components = find_connected_components(graph)
            new_info = {}
            for comp in components:
                if len(comp) == 1:  # 不用 merge 的 instance
                    id_ = id_list[comp[0]]
                    new_info[id_] = copy.deepcopy(bev_info[id_])  # 一个 instance 一个 attr， attr 为 dict
                else:
                    merge_ids = [id_list[i] for i in comp]
                    to_merge_info = {id_: copy.deepcopy(bev_info[id_]) for id_ in merge_ids}
                    merged_info = unordered_merge_line_attr(
                        to_merge_info, merge_thre
                    )  # merge 过的 attr 为 list, 没 merge 的 attr 为 dict
                    new_info.update(merged_info)
        else:
            # 1. 区分需要 merge 的线和不需要 merge 的线
            # update 2025.04.13, curb需要按照有向图合并.
            if class_name == "curb":
                # 按照老一套逻辑找到PRE/POST,
                # 按照老一套逻辑找到PRE/POST,
                # 按照老一套逻辑找到PRE/POST,
                graph = build_graph(lines, thre=merge_thre)
                components = find_connected_components(graph)
                new_info = {}
                for comp in components:
                    if len(comp) == 1:  # 不用 merge 的 instance
                        id_ = id_list[comp[0]]
                        new_info[id_] = copy.deepcopy(bev_info[id_])  # 一个 instance 一个 attr， attr 为 dict
                    else:
                        merge_ids = [id_list[i] for i in comp]
                        to_merge_info = {id_: copy.deepcopy(bev_info[id_]) for id_ in merge_ids}
                        merged_info = merge_line_attr(
                            to_merge_info, merge_thre
                        )  # merge 过的 attr 为 list, 没 merge 的 attr 为 dict
                        new_info.update(merged_info)

                # update 05.24, 打断切向向量变化幅度在90度以上的分量
                new_info = cut_new_info(new_info, class_name=class_name)
            else:
                # # fix: 0429, 两点距离小于0.05才认为共点; 有向连通,优化导流线
                # graph = build_ordered_graph_laneline(lines, thre=merge_thre) # 两点距离小于 thre 认为共点
                graph = build_graph(lines, thre=merge_thre)  # update 05.24, revert to unordered-graph
                components = find_connected_components(graph)

                # 2. 处理 merge 和不 merge 的情况
                # V 型线 / 倒 V 型：角度为锐角就不 merge, 钝角就 merge
                # X 型： merge 成两条 '\' 和 '/'
                # Y 型 / 倒 Y 型: 两条一长一短
                # 多分叉：也兼容
                new_info = {}
                for comp in components:
                    if len(comp) == 1:  # 不用 merge 的 instance
                        id_ = id_list[comp[0]]
                        new_info[id_] = copy.deepcopy(bev_info[id_])  # 一个 instance 一个 attr， attr 为 dict
                    else:
                        merge_ids = [id_list[i] for i in comp]
                        to_merge_info = {id_: copy.deepcopy(bev_info[id_]) for id_ in merge_ids}
                        merged_info = merge_line_attr(
                            to_merge_info, merge_thre
                        )  # merge 过的 attr 为 list, 没 merge 的 attr 为 dict
                        new_info.update(merged_info)
        return new_info

    def get_line_gt(
        self,
        bev_info_cls,
        class_name,
        tran_mats_dict,
        class_info,
        other_info=None,
    ):
        ego2lidar = tran_mats_dict["ego2lidar"]
        key_list = list(bev_info_cls.keys())
        all_lines = []
        all_attrs = []
        for i, idx in enumerate(key_list):
            idx_dict = bev_info_cls[idx]
            ego_pts = idx_dict["points"]
            # attrs = idx_dict['attribute']
            attrs = idx_dict.get(
                "attribute",
                {
                    "unknow": True,
                },
            )
            lidar_pts = self.ego2lidar_and_intersection(ego_pts, ego2lidar, class_name)
            if not lidar_pts:
                continue

            rebuild_pts = self.rebuild_maptracker(lidar_pts, class_info["pt_num"], type="line")
            permute_pts = self.permute_line(rebuild_pts)

            if self.line_is_in_range(rebuild_pts, class_info):  # 特殊类别判断是否在有效range范围内
                all_lines.append(permute_pts.tolist())
                direction = self.get_direction(idx_dict, class_name, other_info)
                if isinstance(attrs, dict):  # 没做过 merge
                    attrs = [attrs] * int(class_info["pt_num"])
                    attrs = self.convert_attr([attrs], class_name, direction)[0]
                    attrs = self.permute_attrs(attrs)
                else:
                    # 计算原始点在 lidar 坐标系的坐标, 用于给 点属性
                    ego_pts = [list(pt) + [1] for pt in ego_pts]
                    raw_lidar_pts = (np.array(ego_pts) @ ego2lidar.T)[:, :3].tolist()
                    attrs = self.rebuild_attrs(raw_lidar_pts, rebuild_pts, attrs)
                    attrs = self.convert_attr([attrs], class_name, direction)[0]
                    attrs = self.permute_attrs(attrs)

                all_attrs.append(attrs)
        return all_lines, all_attrs

    def rebuild_attrs(self, raw_lidar_pts, rebuild_pts, attrs):
        # 对插值后的每个点， 去原始的线上找到最近的点位置，按这个位置给对应点属性
        assert len(raw_lidar_pts) == len(attrs)
        rebuid_attrs = []
        for pt in rebuild_pts:
            dist = np.linalg.norm(raw_lidar_pts - pt, axis=1)
            nearest_index = np.argmin(dist)
            rebuid_attrs.append(attrs[nearest_index])
        return rebuid_attrs

    def norm_line(self, pts):
        # normalize pts to 0-1
        pts_norm = (pts - np.array(self.map_lidar_range[:3])) / (
            np.array(self.map_lidar_range[3:6]) - np.array(self.map_lidar_range[:3])
        )
        return pts_norm

    def norm_width(self, width):
        # normalize pts to 0-1
        width_norm = (width) / (self.map_lidar_range[3] - self.map_lidar_range[0])
        return width_norm

    def recover_line(self, map_gt):
        for cls_id, cls_dict in map_gt.items():
            for idx, line_pts in enumerate(cls_dict):
                pts = np.array(line_pts[0])
                pts_recover = self.recover_pts(pts, cls_id)
                line_pts[0] = pts_recover.tolist()
        return map_gt

    def recover_pts(self, pts, cls_id=0):
        if len(pts) > 0:
            if cls_id in [2, 6]:  # stopline, entrance new gt
                pts_norm = np.array([pts[0], pts[2]])
                pts_rec = pts_norm * (
                    np.array(self.map_lidar_range[3:6]) - np.array(self.map_lidar_range[:3])
                ) + np.array(self.map_lidar_range[:3])
                width = self.recover_width(pts[1, 2])
                pts_recover = np.array(
                    [
                        [pts_rec[0, 0], pts_rec[0, 1], pts_rec[0, 2]],
                        [pts[1, 0], pts[1, 1], width],
                        [pts_rec[1, 0], pts_rec[1, 1], pts_rec[1, 2]],
                        [pts[3, 0], pts[3, 1], width],
                    ]
                )
            else:
                pts_recover = pts * (
                    np.array(self.map_lidar_range[3:6]) - np.array(self.map_lidar_range[:3])
                ) + np.array(self.map_lidar_range[:3])
        else:
            pts_recover = np.array([])
        return pts_recover

    def recover_width(self, width_norm):
        width = (width_norm) * (self.map_lidar_range[3] - self.map_lidar_range[0])
        return width

    def get_box_gt(
        self,
        bev_info_cls,
        class_name,
        tran_mats_dict,
        class_info,
        other_info=None,
    ):
        key_list = list(bev_info_cls.keys())
        all_lines, all_gauss, all_attrs = [], [], []
        for i, idx in enumerate(key_list):
            try:
                idx_dict = bev_info_cls[idx]
                ego_pts = idx_dict["points"]
                # attrs = idx_dict['attribute']
                attrs = idx_dict.get("attribute", {"unknow", True})
                lidar_pts = self.ego2lidar_and_intersection(ego_pts, tran_mats_dict["ego2lidar"], class_name)
                if not lidar_pts and len(lidar_pts) != 5:
                    continue
                gauss_value = self.build_3d_gauss_from_box(lidar_pts[:4], vis=False)

                permute_pts = self.permute_line(np.array(lidar_pts[:4]), is_closed=True)

                if self.pt_is_in_range(gauss_value[0], class_info):
                    all_gauss.append(gauss_value)
                    all_lines.append(permute_pts.tolist())
                    direction = self.get_direction(idx_dict, class_name, other_info)
                    attrs = [attrs] * int(class_info["pt_num"])  # box 类别不会存在merge问题
                    attrs = self.convert_attr([attrs], class_name, direction)[0]
                    attrs = self.permute_attrs(attrs, cls_type="box")
                    all_attrs.append(attrs)
            except BaseException:
                continue
            # 插值前后可视化
            # self.plot_2d_pt(lidar_pts, rebuild_pts, name="box_rebuild_check")
            # self.plot_box_check(ego_pts, lidar_pts, rebuild_pts, permute_pts, tran_mats_dict["ego2lidar"], name="box_rebuild_check")

        return all_lines, all_gauss, all_attrs

    def line_is_in_range(self, line, cls_info):
        if "filter_range" not in cls_info:
            return True
        range = cls_info["filter_range"]

        pt = np.mean(line, axis=0)
        in_range = False
        if range[0] <= pt[0] <= range[3] and range[1] <= pt[1] <= range[4] and range[2] <= pt[2] <= range[5]:
            in_range = True
        return in_range

    def pt_is_in_range(self, pt, cls_info):
        if "filter_range" not in cls_info:
            return True
        range = cls_info["filter_range"]
        in_range = False
        if range[0] <= pt[0] <= range[3] and range[1] <= pt[1] <= range[4] and range[2] <= pt[2] <= range[5]:
            in_range = True
        return in_range

    def plot_2d_pt(self, ori_pts, reb_pts, name="unknow"):

        save_path = "./tmp_check/"  # TODO 后期存储在output路径下
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        ori_pts = np.array(ori_pts)
        reb_pts = np.array(reb_pts)
        plt.figure()
        plt.scatter(ori_pts[:, 0], ori_pts[:, 1], color="red", label="Points")
        plt.scatter(reb_pts[:, 0], reb_pts[:, 1], color="blue", label="Points")

        plt.savefig(save_path + f"{name}_{random.randint(0, 99999)}.png")

    def plot_box_check(self, ego_pts, rfu_pts, reb_pts, permute_pts, ego2lidar, name="unknow"):
        save_path = "./tmp_check/"  # TODO 后期存储在output路径下
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        ego_pts = np.array(ego_pts)
        rfu_pts = np.array(rfu_pts)
        reb_pts = np.array(reb_pts)
        rec_pts = self.recover_pts(permute_pts[0])

        # check ego 2 rfu
        x_min, y_min, z_min, x_max, y_max, z_max = self.map_point_cloud_range
        patch_bev = box(x_min, y_min, x_max, y_max)
        print("ori ego pts:", ego_pts)
        line_geom_before = Polygon(np.array(ego_pts))
        if not line_geom_before.is_valid:
            return []
        line_geom_after = line_geom_before.intersection(patch_bev)
        if line_geom_after.geom_type == "Polygon":
            ego_pts = np.array(line_geom_after.exterior.coords)
        else:
            return []
        ego_pts = ego_pts.tolist()

        if ego_pts:
            try:
                ego_pts = [list(pt) + [1] for pt in ego_pts]
                print("inter ego pts:", ego_pts)
                # lidar_pts = (np.array(ego_pts) @ ego2lidar.T)[:, :3].tolist()
                print("ego2rfu pts:", ego_pts)
            except BaseException:
                print(f"Find ERROR in {traceback.format_stack()}")
                # from IPython import embed;embed()
        else:
            pass
        ego_pts = np.array(ego_pts)[:, :3]
        plt.figure()

        plt.subplot(2, 2, 1)
        plt.scatter(rfu_pts[:, 0], rfu_pts[:, 1], color="red", label="rfu_pts")
        plt.scatter(reb_pts[:, 0], reb_pts[:, 1], color="blue", label="reb_pts")
        plt.scatter(rec_pts[:, 0], rec_pts[:, 1], color="black", label="rec_pts")
        plt.title("rfu, reb, rec x-y")
        plt.legend()

        plt.subplot(2, 2, 2)
        plt.scatter(rfu_pts[:, 0], rfu_pts[:, 2], color="red", label="rfu_pts")
        plt.scatter(reb_pts[:, 0], reb_pts[:, 2], color="blue", label="reb_pts")
        plt.scatter(rec_pts[:, 0], rec_pts[:, 2], color="black", label="rec_pts")
        plt.title("rfu, reb, rec x-z")
        plt.legend()

        plt.subplot(2, 2, 3)
        plt.scatter(ego_pts[:, 1], ego_pts[:, 0], color="red", label="ego_pts")
        plt.title("ego y-x")
        plt.legend()

        plt.subplot(2, 2, 4)
        plt.scatter(ego_pts[:, 1], ego_pts[:, 2], color="red", label="ego_pts")
        plt.title("ego y-z")
        plt.legend()
        plt.savefig(save_path + f"{random.randint(0, 99999)}_{name}.png")

    # 感谢 gpt4o 给出的box的3D高斯GT生成方法(>.<)
    def build_3d_gauss_from_box(self, lidar_pts, vis=False):
        lidar_pts = np.array(lidar_pts)

        mean = np.mean(lidar_pts, axis=0)
        cov_matrix = np.cov(lidar_pts.T)

        if vis:
            # self.plot_guass_new(lidar_pts, mean, cov_matrix) # TODO plot_guass_new 存在BUG，暂不可用

            # 绘制原始pts 以及拟合gauss
            fig = plt.figure(figsize=(12, 6))
            ax3d = fig.add_subplot(121, projection="3d")
            ax2d = fig.add_subplot(122)
            self.plot_guass_3d(ax3d, lidar_pts, mean, cov_matrix)
            self.plot_guass_2d(ax2d, lidar_pts, mean, cov_matrix)
            save_path = "./tmp_check_guass/"  # TODO 后期存储在output路径下
            if not os.path.exists(save_path):
                os.makedirs(save_path)
            plt.savefig(f"./tmp_check_guass/3d_gaussian_box_{random.randint(0, 99999)}.png", dpi=300)

        return [mean, cov_matrix]

    @staticmethod
    def plot_guass_3d(ax3d, lidar_pts, mean, cov_matrix, n_std=2.0):
        # 特征值和特征向量
        eigvals, eigvecs = np.linalg.eigh(cov_matrix)

        # 设置椭球的轴长度 (特征值)
        radii = np.sqrt(eigvals) * n_std

        # 生成球的参数
        u = np.linspace(0, 2 * np.pi, 100)
        v = np.linspace(0, np.pi, 100)
        x = np.outer(np.cos(u), np.sin(v))
        y = np.outer(np.sin(u), np.sin(v))
        z = np.outer(np.ones_like(u), np.cos(v))

        # 按特征向量和特征值对球进行缩放和旋转
        ellipsoid = np.array([x.ravel(), y.ravel(), z.ravel()])
        ellipsoid = np.dot(eigvecs, ellipsoid).T * radii

        # 将椭球体平移到高斯分布的均值位置
        ellipsoid[:, 0] += mean[0]
        ellipsoid[:, 1] += mean[1]
        ellipsoid[:, 2] += mean[2]

        ax3d.scatter(lidar_pts[:, 0], lidar_pts[:, 1], lidar_pts[:, 2], color="r", label="Original Points")
        # 绘制椭球体表面
        ax3d.plot_surface(
            ellipsoid[:, 0].reshape(x.shape),
            ellipsoid[:, 1].reshape(y.shape),
            ellipsoid[:, 2].reshape(z.shape),
            color="c",
            alpha=0.3,
            rstride=4,
            cstride=4,
        )
        # 添加图例和标题
        ax3d.legend()
        ax3d.set_title("3D Box and Corresponding Gaussian Distribution")
        ax3d.set_xlabel("X")
        ax3d.set_ylabel("Y")
        ax3d.set_zlabel("Z")

    @staticmethod
    def plot_guass_2d(ax2d, lidar_pts, mean, cov_matrix):
        # 2D XY 平面子图 (绘制高斯分布的等高线图)

        # 提取 XY 平面的协方差矩阵 (2D)
        cov_matrix_xy = cov_matrix[:2, :2]
        mean_xy = mean[:2]  # XY 平面的均值

        # 创建 2D 高斯分布
        gaussian_xy = multivariate_normal(mean=mean_xy, cov=cov_matrix_xy, allow_singular=True)

        # 在 XY 平面上生成网格点
        x, y = np.mgrid[
            min(lidar_pts[:, 0]) - 1 : max(lidar_pts[:, 0]) + 1 : 0.01,
            min(lidar_pts[:, 1]) - 1 : max(lidar_pts[:, 1]) + 1 : 0.01,
        ]
        pos = np.dstack((x, y))

        # 计算每个网格点的高斯概率密度值
        z = gaussian_xy.pdf(pos)

        # 绘制高斯分布的等高线图 (2D)
        contour = ax2d.contourf(x, y, z, cmap="viridis")

        # 绘制原始点集的 XY 平面投影 (2D)
        ax2d.scatter(lidar_pts[:, 0], lidar_pts[:, 1], color="r", label="Original Points")

        # 添加颜色条，展示概率密度
        plt.colorbar(contour, ax=ax2d)

        # 设置 2D 图像的标题和标签
        ax2d.legend()
        ax2d.set_title("XY Gaussian Distribution Contour")
        ax2d.set_xlabel("X")
        ax2d.set_ylabel("Y")

    @staticmethod
    def split_lane_to_laneline_curb(bev_info):
        bev_split = {
            "laneline": deepcopy(bev_info["lane"]),
            "curb": deepcopy(bev_info["lane"]),
        }
        all_lane_id = list(bev_info["lane"].keys())
        for _lane_id in all_lane_id:
            attr = bev_info["lane"][_lane_id]["attribute"]
            if (
                attr["road_curb"] in ["NAN", 0]
                and attr["guardrail"] in ["NAN", 0]
                and attr["water_barrier"] in ["NAN", 0]
            ):
                del bev_split["curb"][_lane_id]

            if (
                "color" not in attr["lane_type"].keys()
                or "dashed" not in attr["lane_type"].keys()
                or "shape" not in attr["lane_type"].keys()
                or attr["lane_type"]["color"] == "NAN"
                or attr["lane_type"]["dashed"] == "NAN"
                or attr["lane_type"]["shape"] == "NAN"
            ):
                del bev_split["laneline"][_lane_id]

        return bev_split

    def get_tran_mats_dict(self, calibrated_sensors, map_cam_dict):
        tran_mats_dict, resolution_list, cam2img_list, cam_undistort_list, lidar2cam_list, ego2cam_list = (
            {},
            [],
            [],
            [],
            [],
            [],
        )

        lidar_ego_ext_R = calibrated_sensors["lidar_ego"]["extrinsic"]["transform"]["rotation"]
        lidar_ego_ext_t = calibrated_sensors["lidar_ego"]["extrinsic"]["transform"]["translation"]
        ego2lidar = self.transform_matrix(
            rotation=Quaternion(
                [lidar_ego_ext_R["w"], lidar_ego_ext_R["x"], lidar_ego_ext_R["y"], lidar_ego_ext_R["z"]]
            ),
            translation=[lidar_ego_ext_t["x"], lidar_ego_ext_t["y"], lidar_ego_ext_t["z"]],
            inverse=True,
        )

        for cam_id, cam_info in map_cam_dict.items():
            # if env.is_volcano_platform():
            #     W, H = calibrated_sensors[cam_id]["intrinsic"]["resolution"]   # 原始图像大小和 tgt 一致
            # else:  # brainpp
            W, H = calibrated_sensors[cam_id]["intrinsic"]["target_resolution"]  # 200w 对应 [[2160, 3840]]

            K = calibrated_sensors[cam_id]["intrinsic"]["K"]
            D = calibrated_sensors[cam_id]["intrinsic"]["D"]
            resolution_list.append([W, H])
            cam2img_list.append(K)
            cam_undistort_list.append(D)
            ext_R = calibrated_sensors[cam_id]["extrinsic"]["transform"]["rotation"]
            ext_t = calibrated_sensors[cam_id]["extrinsic"]["transform"]["translation"]
            lidar2cam = self.transform_matrix(
                rotation=Quaternion([ext_R["w"], ext_R["x"], ext_R["y"], ext_R["z"]]),
                translation=[ext_t["x"], ext_t["y"], ext_t["z"]],
                inverse=False,
            )
            lidar2cam_list.append(lidar2cam)
            ego2cam_list.append(lidar2cam @ ego2lidar)

        tran_mats_dict["cam_resolution"] = resolution_list
        tran_mats_dict["cam2img"] = cam2img_list
        tran_mats_dict["ego2cam"] = ego2cam_list
        tran_mats_dict["cam_undistort_list"] = cam_undistort_list
        tran_mats_dict["lidar2cam"] = lidar2cam_list
        tran_mats_dict["ego2lidar"] = ego2lidar
        tran_mats_dict["map_lidar_range"] = self.map_lidar_range
        return tran_mats_dict

    @staticmethod
    def transform_matrix(
        translation: np.ndarray = np.array([0, 0, 0]),
        rotation: Quaternion = Quaternion([1, 0, 0, 0]),
        inverse: bool = False,
    ) -> np.ndarray:
        tm = np.eye(4)
        if inverse:
            rot_inv = rotation.rotation_matrix.T
            trans = np.transpose(-np.array(translation))
            tm[:3, :3] = rot_inv
            tm[:3, 3] = rot_inv.dot(trans)
        else:
            tm[:3, :3] = rotation.rotation_matrix
            tm[:3, 3] = np.transpose(np.array(translation))
        return tm

    def rebuild_maptracker(self, points, inter_num, type="line"):
        if type == "line":
            line = LineString(points)
        elif type == "box":
            line = LinearRing(points)
        distances = np.linspace(0, line.length, inter_num)
        sampled_points = np.array([list(line.interpolate(distance).coords) for distance in distances]).squeeze()

        return sampled_points

    @staticmethod
    def get_interpolate_number(points):
        """
        累加弦长计算插值点数目
        """
        length = 0
        for i in range(len(points) - 1):
            # utm_x1, utm_y1 = utm.from_latlon(nodes[i].lat, nodes[i].lon)[:2]
            # utm_x2, utm_y2 = utm.from_latlon(nodes[i + 1].lat, nodes[i + 1].lon)[:2]
            utm_x1, utm_y1 = float(points[i][0]), float(points[i][1])
            utm_x2, utm_y2 = float(points[i + 1][0]), float(points[i + 1][1])
            length += math.sqrt((utm_x1 - utm_x2) ** 2 + (utm_y1 - utm_y2) ** 2)
        return math.ceil(length / 1.0)

    def merge_attr(self, bev_lines):
        bev_forkpoints, bev_sectionpoints = fixed_forkpoint(bev_lines)
        bev_lanes = bev_lines
        bev_lanes_merge = {}
        bev_lanes_pts = []
        merge_lanes_index = []
        for k, v in bev_lanes.items():
            bev_lanes_pts.append(v["points"])
            merge_lanes_index.append([k])

        sorted_bev = sorted(
            enumerate(bev_lanes_pts), key=lambda x: x[1][0][0] - x[1][0][1], reverse=False
        )  # 从左到右，从后到前排序
        bev_lanes_pts = [value for index, value in sorted_bev]
        merge_lanes_index_sort = [merge_lanes_index[index] for index, value in sorted_bev]
        merge_lanes_index = merge_lanes_index_sort

        stpt_flags = [1 for _ in range(len(bev_lanes_pts))]

        def lane_merge(i, lane, bev_lanes_pts, stpt_flags, forkpts_pool):
            for j, lane_next in enumerate(bev_lanes_pts):
                if dist_func(lane_next, lane) and stpt_flags[j] == 1 and lane_next[0] not in forkpts_pool:  # 分叉车道线
                    same_attr = True  # 该版 merge 对于不同属性车道线也进行融合
                    return j, lane_next, same_attr
            return -1, None, False

        # 找起始线段
        forkpts_pool = []
        for k, v in bev_forkpoints.items():
            if v["points"][0] not in forkpts_pool:
                forkpts_pool.append(v["points"][0])
        done_num = 0
        for i, lane in enumerate(bev_lanes_pts):
            if stpt_flags[i] != -1:
                next_index, lane_next, same_attr = lane_merge(i, lane, bev_lanes_pts, stpt_flags, forkpts_pool)
                while next_index != -1 and stpt_flags[i] == 1 and same_attr:
                    # print(f"done_num {done_num}->lane_id {i}: {merge_lanes_index[i]}->{merge_lanes_index[next_index][0]}" )
                    done_num += 1
                    stpt_flags[next_index] = 0
                    lane.extend(lane_next[1:])
                    bev_lanes_pts[i] = lane
                    merge_lanes_index[i].extend(merge_lanes_index[next_index])
                    next_index, lane_next, same_attr = lane_merge(i, lane, bev_lanes_pts, stpt_flags, forkpts_pool)

        for n, flag in enumerate(stpt_flags):
            if flag:
                bev_lanes_pts_round = [[round(pt[0], 3), round(pt[1], 3), round(pt[2], 3)] for pt in bev_lanes_pts[n]]
                bev_lanes_merge[merge_lanes_index[n][0]] = {
                    "points": bev_lanes_pts_round,
                }

        return bev_lanes_merge

    def rebuild_y(self, bev_lines):
        bev_forkpoints, bev_sectionpoints = fixed_forkpoint(bev_lines)
        bev_lanes = bev_lines
        bev_lanes_merge = {}
        bev_lanes_pts = []
        merge_lanes_index = []
        for k, v in bev_lanes.items():
            bev_lanes_pts.append(v["points"])
            merge_lanes_index.append([k])

        sorted_bev = sorted(
            enumerate(bev_lanes_pts), key=lambda x: x[1][0][0] - x[1][0][1], reverse=False
        )  # 从左到右，从后到前排序
        bev_lanes_pts = [value for index, value in sorted_bev]
        merge_lanes_index_sort = [merge_lanes_index[index] for index, value in sorted_bev]
        merge_lanes_index = merge_lanes_index_sort

        stpt_flags = [1 for _ in range(len(bev_lanes_pts))]

        def lane_merge(i, lane, bev_lanes_pts, stpt_flags, forkpts_line_idx):
            for j, lane_next in enumerate(bev_lanes_pts):
                if (
                    dist_func(lane_next, lane) and stpt_flags[j] == 1 and merge_lanes_index[i][0] in forkpts_line_idx
                ):  # 分叉车道线
                    if merge_lanes_index[j][0] in forkpts_line_idx[merge_lanes_index[i][0]]["match_id"]:
                        same_attr = True  # 该版merge对于不同属性车道线也进行融合
                        return j, lane_next, same_attr
            return -1, None, False

        # 找起始线段
        """
        bev_forkpoints[str(forkpt_num)] = {
            "attribute": {"match_lane": ept_match,
                          "start_lane": es_tmp_dpt_pool,
                          "end_lane": [bev_lanes_index[i]],
                          },
            "points": [lane_ept],
          }
        """
        forkpts_pool, forkpts_line_idx = [], {}
        for k, v in bev_forkpoints.items():
            if v["points"][0] not in forkpts_pool:
                forkpts_pool.append(v["points"][0])
            match_line_list = []
            for s_idx in v["attribute"]["start_lane"]:
                for e_idx in v["attribute"]["end_lane"]:
                    match_line_list.append(
                        [[s_idx, e_idx], calculating_curvature(bev_lines[s_idx]["points"] + bev_lines[e_idx]["points"])]
                    )
            match_line_list = sorted(match_line_list, key=lambda x: x[1], reverse=True)

            forkpts_line_idx[match_line_list[0][0]] = {
                "match_id": [match_line_list[0]],
            }

        for i, lane in enumerate(bev_lanes_pts):
            if stpt_flags[i] != -1:
                next_index, lane_next, same_attr = lane_merge(i, lane, bev_lanes_pts, stpt_flags, forkpts_line_idx)
                while next_index != -1 and stpt_flags[i] == 1 and same_attr:
                    stpt_flags[next_index] = 0
                    lane.extend(lane_next[1:])
                    bev_lanes_pts[i] = lane
                    merge_lanes_index[i].extend(merge_lanes_index[next_index])
                    next_index, lane_next, same_attr = lane_merge(i, lane, bev_lanes_pts, stpt_flags, forkpts_line_idx)

        for n, flag in enumerate(stpt_flags):
            if flag:
                bev_lanes_pts_round = [[round(pt[0], 3), round(pt[1], 3), round(pt[2], 3)] for pt in bev_lanes_pts[n]]
                bev_lanes_merge[merge_lanes_index[n][0]] = {
                    "points": bev_lanes_pts_round,
                }

        return bev_lanes_merge

    def ego2lidar_and_intersection(self, ego_pts, ego2lidar, cls_name):
        if len(ego_pts) < 2:
            return []
        x_min, y_min, z_min, x_max, y_max, z_max = self.map_point_cloud_range
        patch_bev = box(x_min, y_min, x_max, y_max)
        if cls_name in ["lane", "laneline", "curb", "stopline"]:
            line_geom_before = LineString(np.array(ego_pts))
            if not line_geom_before.is_valid:
                return []
            line_geom_after = line_geom_before.intersection(patch_bev)
            if not line_geom_after.is_empty:
                if line_geom_after.geom_type == "LineString":
                    line_geom_after = MultiLineString([line_geom_after])
                elif line_geom_after.geom_type == "Point":
                    return []
                coords_after = []
                try:
                    for new_line in line_geom_after.geoms:
                        coords_after.extend(new_line.coords)
                    ego_pts = np.array(list(coords_after)).tolist()
                except BaseException:
                    print(f"Find ERROR in {traceback.format_stack()}")
                    ego_pts = []
            else:
                ego_pts = []
        elif cls_name in ["crosswalk", "arrow", "noparking", "entrance", "mask"]:
            line_geom_before = Polygon(np.array(ego_pts))
            if not line_geom_before.is_valid:
                return []
            line_geom_after = line_geom_before.intersection(patch_bev)
            if line_geom_after.geom_type == "Polygon":
                ego_pts = np.array(line_geom_after.exterior.coords)
            else:
                return []
            ego_pts = ego_pts.tolist()
        else:
            # print(f"unknow cls_name: {cls_name}")
            pass

        if len(ego_pts) > 1:
            try:
                ego_pts = [list(pt) + [1] for pt in ego_pts]
                lidar_pts = (np.array(ego_pts) @ ego2lidar.T)[:, :3].tolist()
            except BaseException:
                print(f"Find ERROR in {traceback.format_stack()}")
                # from IPython import embed;embed()
                lidar_pts = []
        else:
            lidar_pts = []
        return lidar_pts

    def get_map_scene_weight(self, bev_info):
        scene_weight = 1
        if self.scene_weight:
            # find lane change and car turn
            lane_change_flag, car_turn_flag = False, False
            for cls_name in ["laneline", "curb"]:
                for k, v in bev_info[cls_name].items():
                    if v["points"]:
                        if v["points"][0][0] <= 20 and 0.9 <= v["points"][0][1] <= 0.9:
                            lane_change_flag = True
                        x_dis = v["points"][-1][0] - v["points"][0][0]
                        y_dis = v["points"][-1][1] - v["points"][0][1]
                        if abs(x_dis) / max(1, abs(y_dis)) <= 1 and (x_dis >= 5 or y_dis >= 5):
                            car_turn_flag = True
            if lane_change_flag or car_turn_flag:
                scene_weight = 10
                # print(f"find lane_change_flag {lane_change_flag} or car_turn_flag {car_turn_flag}")

        return scene_weight

    def get_direction(self, idx_dict, class_name, other_info):
        ego_pts = idx_dict["points"]
        # attrs = idx_dict.get(
        #     "attribute",
        #     {
        #         "unknow": True,
        #     },
        # )
        direction = 0
        # if "direction" in attrs:
        #     if attrs["direction"] in ['Forward', 'forward']:
        #         direction = 1
        #     elif attrs["direction"] in ['Reverse', 'reverse']:
        #         direction = 2
        #     elif attrs["direction"] in ['Bidirectional', 'bidirectional']:
        #         direction = 3
        #     else:
        #         direction = 0
        # elif class_name in ['laneline', 'curb']:
        #     direction = 1
        # elif class_name in ['stopline']:
        #     angle_x, angle_y = self.calculate_angles(ego_pts[0], ego_pts[-1])
        #     # TODO 截止20241206，对箭头起点不参照箭头方向，暂无法使用； 标注文档 v1.6.2 之后标注起点能反映本向对向
        #     if 135 <= angle_y <= 225:
        #         direction = 1
        #     else:
        #         direction = 2
        # elif class_name in ['crosswalk', 'noparking', 'entrance', 'mask']:
        #     angle_x, angle_y = self.calculate_angles(ego_pts[-2], ego_pts[-1])
        #     # TODO 截止20241206，对箭头起点不参照箭头方向，暂无法使用；标注文档 v1.6.2 之后标注起点能反映本向对向
        #     if -45 <= angle_x <= 45: # 路口左右转时，横向箭头方向如何定义？
        #         direction = 1
        #     else:
        #         direction = 2

        if class_name in ["arrow"]:
            angle_x, angle_y = self.calculate_angles(ego_pts[-2], ego_pts[-1])
            arrow_center_pt = np.mean(ego_pts[:-1], axis=0)
            arrow_geom = LineString([[0, 0], arrow_center_pt[:-1]])

            if abs(angle_x) <= 30 and arrow_center_pt[0] > 0:
                direction = 1  # 当 箭头方向与车辆方向垂直时，且箭头中心点在自车ego右侧时，arrow 默认为本向，避免城区路中出现路沿导致判断错误；
                return direction
            if other_info:  # Lidar 方案 存在 arrow箭头标注起点在右上角的情况, 无法通过角度判断是否横向
                direction = 1  # arrow 默认为本向
                if other_info:
                    for line_idx, line_dict in other_info.items():
                        attr = line_dict.get(
                            "attribute",
                            {
                                "unknow": True,
                            },
                        )
                        if (
                            attr["road_curb"] in ["NAN", 0]
                            and attr["guardrail"] in ["NAN", 0]
                            and attr["water_barrier"] in ["NAN", 0]
                            and attr["lane_type"]["color"] not in ["yellow"]
                        ):
                            continue  # 跳过所有非分隔道路线

                        line_pts = line_dict["points"]
                        line_geom = LineString([[x, y] for x, y, z in line_pts])
                        if arrow_geom.intersects(line_geom):  # 如果arrow 中心点到原点的连线与某个curb相交，则认为arrow 朝向为对向
                            direction = 2
                            break
        return direction

    def calculate_angles(self, point1, point2):
        # 计算矢量
        vector = [p2 - p1 for p1, p2 in zip(point1, point2)]
        # 计算矢量的模
        magnitude = math.sqrt(sum(v ** 2 for v in vector))
        if magnitude == 0:
            return None
        # 计算单位矢量
        unit_vector = [v / magnitude for v in vector]
        # 计算与X轴和Y轴正方向的夹角（度数）
        angle_x = math.degrees(math.acos(unit_vector[0]))
        angle_y = math.degrees(math.acos(unit_vector[1]))
        return angle_x, angle_y

    def permute_line(self, line: np.ndarray, is_closed=False, padding=1e5):
        """
        (num_pts, 3) -> (num_permute, num_pts, 3)
        where num_permute = 2 * (num_pts - 1)
        """
        self.coords_dim = 3
        num_points = len(line)
        permute_num = num_points
        permute_lines_list = []
        if is_closed:
            pts_to_permute = line  # throw away replicate start end pts
            for shift_i in range(permute_num):
                permute_lines_list.append(np.roll(pts_to_permute, shift_i, axis=0))
            flip_pts_to_permute = np.flip(pts_to_permute, axis=0)
            for shift_i in range(permute_num):
                permute_lines_list.append(np.roll(flip_pts_to_permute, shift_i, axis=0))
        else:
            permute_lines_list.append(line)
            permute_lines_list.append(np.flip(line, axis=0))

        permute_lines_array = np.stack(permute_lines_list, axis=0)
        permute_lines_array = self.norm_line(permute_lines_array)

        return permute_lines_array

    def permute_attrs(self, rebuid_attrs, padding=1e5, cls_type="line"):
        # 针对lane attr进行premute+padding
        num_points = len(rebuid_attrs)
        permute_num = num_points
        if cls_type == "line":
            permute_attrs = copy.deepcopy(rebuid_attrs[::-1])
            padding_attrs = [rebuid_attrs, permute_attrs]
        elif cls_type == "box":
            padding_attrs = [rebuid_attrs for i in range(permute_num * 2)]
        return padding_attrs

    def build_point_vector_gt_new_1216(self, ego_pts, ego2lidar, cls_name, other_info):
        # 特别注意：shapely insect 操作会改变gt的标注顺序！！！ 在数据预处理过程中，box 类别点序已经被更改，需要重新恢复道路入口线点序
        try:
            if cls_name in ["entrance"]:
                if len(ego_pts) < 5:  # 原始 bbox 异常
                    print(f"Error Entrance: {ego_pts}")
                    return []
                ego_pts = self.recover_entrance_box_order(ego_pts, other_info)
                target_line = self.compute_entrance_line_new_1216(ego_pts)

                # 道路入口线复用 stopline 处理逻辑
                combination_gt = self.compute_point_vector_width_for_stopline(target_line, ego2lidar)  # RFU

            else:
                target_line = ego_pts

                combination_gt = self.compute_point_vector_width_for_stopline(target_line, ego2lidar)  # RFU
        except BaseException:
            combination_gt = []
        return combination_gt

    def recover_entrance_box_order(self, ego_pts, bev_lane):
        order_good = False
        KD_tree_list = []
        for idx, info in bev_lane.items():
            KD_tree_list.append(KDTree(info["points"]))

        tolerance_th = 1.5
        for kd_tree in KD_tree_list:
            distance1, index1 = kd_tree.query(ego_pts[0])
            distance2, index2 = kd_tree.query(ego_pts[1])
            distance3, index3 = kd_tree.query(ego_pts[2])
            distance4, index4 = kd_tree.query(ego_pts[3])
            # print(distance1, distance2, distance3, distance4)
            # 顺序正确时，box 第一、第二点均应在同一条线上 or 第三、第四点均应在同一条线上
            if (distance1 <= tolerance_th and distance2 <= tolerance_th) or (
                distance3 <= tolerance_th and distance4 <= tolerance_th
            ):
                order_good = True
                break

        if not order_good:
            ego_pts = ego_pts[:-1]
            ego_pts = [ego_pts[-1]] + ego_pts[:-1]
            ego_pts = ego_pts + [ego_pts[0]]
        return ego_pts

    @staticmethod
    def compute_entrance_line_new_1216(ego_pt):
        def distance_point_to_segment(pt, seg_start, seg_end):
            # 计算点到线段的距离
            seg_vec = np.array(seg_end) - np.array(seg_start)
            pt_vec = np.array(pt) - np.array(seg_start)
            proj_len = np.dot(pt_vec, seg_vec) / max(np.linalg.norm(seg_vec) ** 2, 1)
            proj_len = np.clip(proj_len, 0, 1)
            closest_point = seg_start + proj_len * seg_vec
            return np.linalg.norm(pt - closest_point)

        # 1. 找出距离原点更近的长边
        origin = np.array([0, 0, 0])
        d1 = distance_point_to_segment(origin, ego_pt[1], ego_pt[2])
        d2 = distance_point_to_segment(origin, ego_pt[3], ego_pt[4])
        closer_segment = (1, 2) if d1 < d2 else (3, 4)

        # 2. 找出该长边中更靠近自车ego原点的端点
        if np.linalg.norm(ego_pt[closer_segment[0]]) < np.linalg.norm(ego_pt[closer_segment[1]]):
            closer_point_index = closer_segment[0]
        else:
            closer_point_index = closer_segment[1]

        closer_point = ego_pt[closer_point_index]

        # 3. 找到包含该端点的短边并计算法向量
        short_segments = [(0, 1), (2, 3), (4, 1)]  # (4,1) index 4 与 0重合， (4, 1) == (0, 1)
        short_segment, tar_short_segment = None, None
        for i, seg in enumerate(short_segments):
            if closer_point_index in seg:
                short_segment = seg
                tar_short_segment = (2, 3) if i == 0 or i == 2 else (0, 1)
                break
        if short_segment is None or tar_short_segment is None:
            return []
        short_start, short_end = ego_pt[short_segment[0]], ego_pt[short_segment[1]]
        short_vec = np.array(short_end) - np.array(short_start)
        normal_vec = np.array([-short_vec[1], short_vec[0], 0])  # 法向量（仅考虑XY平面）

        tar_short_start = ego_pt[tar_short_segment[0]]
        tar_short_vec = np.array(ego_pt[tar_short_segment[0]]) - np.array(ego_pt[tar_short_segment[1]])
        # 4. 计算交点
        # 短边延长线： closer_point + t * normal_vec
        # 另一条短边延长线： tar_short_start + u * tar_short_vec
        p = np.array(closer_point)
        q = np.array(tar_short_start)
        r = normal_vec
        s = tar_short_vec

        # 求解t和u：p + t*r = q + u*s
        cross_rs = np.cross(r[:2], s[:2])  # Z轴分量
        if np.isclose(cross_rs, 0):
            print("短边平行，无法找到交点")
            return []

        t = np.cross((q - p)[:2], s[:2]) / cross_rs
        intersection = p + t * r

        # 5. 输出结果
        return np.array([closer_point, intersection]).tolist()

    def compute_point_vector_width_for_entrance(self, target_line, find_lines, ego2lidar):
        # 计算 target_line 的长度
        target_width = self.compute_length(target_line)

        # 计算 find_lines 的方向向量和交点
        results = []
        for line in find_lines:
            direction = self.compute_direction(line)
            if len(direction) < 2:
                continue
            if np.sqrt(direction[0] ** 2 + direction[1] ** 2) == 0:
                continue
            # intersection = self.closest_point_on_line(target_line, line[0])
            # 直接使用车道线起始点作为道路入口gt 起点
            results.append((line[0], direction))

        # 按方向向量的 x 分量排序（假设从左到右即按 x 坐标排序）
        results.sort(key=lambda x: x[1][0])  # x[1][0] 是方向向量的 x 分量

        # 组合 GT为[[norm_pt1_x, norm_pt1_y, norm_pt1_z,],
        #           [sin(direct1), cos(direct1), target_width,],
        #          [norm_pt2_x, norm_pt2_y, norm_pt1_z,],
        #           [sin(direct2), cos(direct2), target_width,],
        # ]
        combination_gt = []
        norm_width = self.norm_width(target_width)
        for res_one in results:
            ego_pt = [list(res_one[0]) + [1]]
            lidar_pt = (np.array(ego_pt) @ ego2lidar.T)[:, :3].tolist()
            norm_pt = self.norm_line(np.array(lidar_pt))[0]

            ego_direct = [list(res_one[1]) + [1]]
            lidar_direct = (np.array(ego_direct) @ ego2lidar.T)[:, :3].tolist()[0]
            sin_xy, cos_xy = self.compute_sin_cos(lidar_direct)
            combination_gt.extend([[norm_pt[0], norm_pt[1], norm_pt[2]], [sin_xy, cos_xy, norm_width]])
        if np.isnan(np.array(combination_gt)).any():
            return []
        assert len(combination_gt) == 4, print(f"Find Error Combination_gt: {combination_gt}")
        return combination_gt

    @staticmethod
    def is_close_to_curb(target_line, line):
        point1 = Point(target_line[0])
        point2 = Point(target_line[-1])
        # 定义线（由一组坐标点构成的线段）
        line_geom = LineString(line)

        distance1 = point1.distance(line_geom)
        distance2 = point2.distance(line_geom)

        if distance1 < 1.5 or distance2 < 1.5:
            return True
        else:
            return False

    # 计算线段长度
    @staticmethod
    def compute_length(line):
        start, end = line[0], line[1]
        return np.linalg.norm(np.array(end) - np.array(start))

    # 计算方向向量
    @staticmethod
    def compute_direction(line):
        if len(line) < 10:
            start, end = line[0], line[-1]
        else:
            start, end = line[0], line[9]
        direction = np.array(end) - np.array(start)

        return np.nan_to_num(
            np.divide(direction, np.linalg.norm(direction), where=np.linalg.norm(direction) != 0),
            nan=0,
            posinf=0,
            neginf=0,
        )

    @staticmethod
    def compute_normal_direction(line):
        if len(line) < 10:
            start, end = line[0], line[-1]
        else:
            start, end = line[0], line[9]

        if start[1] > end[1]:  # 旧标注规则中 stopline 应该从左到右排序-> 起点 ego_y 应大于 终点 ego_y
            start, end = end, start
        direction = np.array(end) - np.array(start)
        norm_direction = [direction[1], -direction[0], 0]  # 法向量（仅考虑XY平面）选择X轴正侧的法向量

        return np.nan_to_num(
            np.divide(norm_direction, np.linalg.norm(norm_direction), where=np.linalg.norm(norm_direction) != 0),
            nan=0,
            posinf=0,
            neginf=0,
        )

    # 计算点到线段最近点 (交点)
    @staticmethod
    def closest_point_on_line(target_line, point):
        start, end = target_line[0], target_line[1]
        line_vec = np.array(end) - np.array(start)
        point_vec = np.array(point) - np.array(start)
        t = np.dot(point_vec, line_vec) / np.dot(line_vec, line_vec)
        t = np.clip(t, 0, 1)  # 限制 t 在 [0, 1] 范围内
        closest_point = start + t * line_vec
        return closest_point

    @staticmethod
    def compute_sin_cos(vector):
        """
        计算3D矢量在XY平面上的sin和cos值。

        参数:
            vector: 3D矢量 (vx, vy, vz)

        返回:
            (cos(θ), sin(θ)) 的值
        """
        vx, vy, _ = vector  # 投影到XY平面，忽略z分量
        r = np.sqrt(vx ** 2 + vy ** 2)  # 计算投影长度

        if r == 0:  # 若长度为0，无法计算
            raise ValueError(f"Vector {vector} projection on XY plane has zero length, undefined direction.")

        cos_theta = vx / r
        sin_theta = vy / r

        return sin_theta, cos_theta

    @staticmethod
    def line_center(line):
        return np.mean(np.array(line), axis=0)

    @staticmethod
    def compute_line_direction(line):
        # Compute the direction vector of the line
        if len(line) < 2:
            return None
        start = np.array(line[0])
        end = np.array(line[-1])
        direction = end - start
        norm = np.linalg.norm(direction)
        if norm == 0:
            return None
        return direction / norm

    def angle_between_lines(self, line1, line2):
        # Compute the angle between two lines in degrees
        dir1 = self.compute_line_direction(line1)
        dir2 = self.compute_line_direction(line2)
        if dir1 is None or dir2 is None:
            return None
        cos_angle = np.dot(dir1, dir2)
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle)
        angle_degrees = np.degrees(angle)
        return angle_degrees

    @staticmethod
    def min_distance_between_lines(line1, line2):
        # Compute the minimal distance between two lines
        pts1 = np.array(line1)
        pts2 = np.array(line2)
        dists = distance.cdist(pts1, pts2)
        min_dist = np.min(dists)
        return min_dist

    """
    调整道路入口生成流程 from https://git-core.megvii-inc.com/transformer/Perceptron/-/commit/d14fb7e512699a7eab0808edede8a3a1f7db995b
    """

    def get_point_vector_gt(
        self,
        bev_info_cls,
        class_name,
        tran_mats_dict,
        class_info,
        other_info=None,
    ):
        ego2lidar = tran_mats_dict["ego2lidar"]
        key_list = list(bev_info_cls.keys())
        all_lines, all_attrs = [], []
        for i, idx in enumerate(key_list):
            idx_dict = bev_info_cls[idx]
            ego_pts = idx_dict["points"]
            attrs = idx_dict["attribute"]
            direction = self.get_direction(idx_dict, class_name, other_info)

            if class_name in ["stopline"]:
                lidar_pts = self.ego2lidar_and_intersection(ego_pts, ego2lidar, class_name)
                if len(lidar_pts) < 2:
                    continue
                elif self.line_is_in_range(lidar_pts, class_info):
                    point_vector_width = self.build_point_vector_gt_new_1216(ego_pts, ego2lidar, class_name, other_info)
                    if not point_vector_width:
                        continue
                    all_lines.append([point_vector_width])

                    attrs = [attrs] * int(class_info["pt_num"])
                    attrs = self.convert_attr([attrs], class_name, direction)[0]
                    all_attrs.append([attrs])  # point-vector gt no permute
            elif class_name in ["entrance"]:
                point_vector_width = self.build_point_vector_gt(ego_pts, ego2lidar, class_name, other_info)
                if not point_vector_width:
                    continue

                # 若生成道路入口GT中心点不在指定范围内，则跳过该GT
                lidar_pts_norm = np.array([point_vector_width[0], point_vector_width[2]])
                lidar_pts_rec = lidar_pts_norm * (
                    np.array(self.map_lidar_range[3:6]) - np.array(self.map_lidar_range[:3])
                ) + np.array(self.map_lidar_range[:3])
                if not self.pt_is_in_range(np.mean(lidar_pts_rec, axis=0), class_info):
                    continue
                attrs = [attrs] * int(class_info["pt_num"])
                attrs = self.convert_attr([attrs], class_name, direction)[0]

                all_lines.append([point_vector_width])
                all_attrs.append([attrs])  # point-vector gt no permute

        return all_lines, all_attrs

    def build_point_vector_gt(self, ego_pts, ego2lidar, cls_name, other_info):
        if cls_name in ["entrance"]:
            # try:
            combination_gt = self.compute_entrance_line(ego_pts, lines_gt=other_info, ego2lidar=ego2lidar)
            # except:
            #     combination_gt = []
        else:
            print("ERROR CLS Name: {cls_name}, Pls check")
            combination_gt = []

        return combination_gt

    def plot_2d_pt_for_entrance(
        self, reference_line, ego_pts_ori, ego_pts, half_length, valid_lane_index, name="entrance check"
    ):
        save_path = "./tmp_check/"  # TODO 后期存储在output路径下
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        ori_pts = np.array(ego_pts_ori)
        reb_pts = np.array(list(reference_line.coords))
        plt.figure()
        plt.scatter(ori_pts[:, 0], ori_pts[:, 1], color="red", label="Points")
        for i, (xi, yi) in enumerate(zip(ori_pts[:, 0], ori_pts[:, 1])):
            plt.text(xi, yi, str(i + 1), fontsize=12, ha="right", va="bottom")
        plt.scatter(reb_pts[:, 0], reb_pts[:, 1], color="blue", label="Points", s=10)

        plt.savefig(save_path + f"{name}_{random.randint(0, 99999)}.png")

    def compute_entrance_line(self, ego_pts_ori, lines_gt, ego2lidar):
        return []
        if len(ego_pts_ori) < 5:  # 原始 bbox 异常
            # print(f'Error Entrance: {ego_pts_ori}')
            return []
        # 特别注意：shapely insect 操作会改变gt的标注顺序！！！ 在数据预处理过程中，box 类别点序已经被更改，需要重新找长短边
        lines_gt_merge = self.get_line_merged_info(lines_gt)
        ego_pts = self.recover_entrance_box_order(ego_pts_ori, lines_gt_merge)
        SAMPLED_DISTANCE = 2
        START_POINT_DISTANCE_THRESHOLD = 20

        reference_line = self.compute_entrance_line_new_1216(ego_pts)
        reference_line = np.array(reference_line)[:, :2]
        rotation_matrix, translation = get_rotate_matrix(reference_line)
        reference_line = LineString(reference_line)
        half_length = reference_line.length / 2.0

        if half_length > 30:  # 较宽参考异常，不输出GT
            return []

        valid_lane_index = []
        for key, line_ in lines_gt_merge.items():
            line_points = np.array(line_["points"])[:, :2] - translation
            line_attr = line_["attribute"]
            continue_flag = False
            if isinstance(line_attr, dict):
                if (
                    line_attr["road_curb"] not in ["NAN", 0]
                    and line_attr["guardrail"] not in ["NAN", 0]
                    and line_attr["water_barrier"] not in ["NAN", 0]
                    and line_attr["lane_type"]["color"] not in ["yellow"]
                ):
                    continue_flag = True  # 跳过所有非分隔道路线
            elif isinstance:
                for line_attr_one in line_attr:
                    if (
                        line_attr_one["road_curb"] not in ["NAN", 0]
                        and line_attr_one["guardrail"] not in ["NAN", 0]
                        and line_attr_one["water_barrier"] not in ["NAN", 0]
                        and line_attr_one["lane_type"]["color"] not in ["yellow"]
                    ):
                        continue_flag = True  # 跳过所有非分隔道路线
                        break

            if continue_flag:
                continue

            # Rotate coordinates
            rotated_points = line_points.dot(rotation_matrix.T)

            if abs(rotated_points[0, 0]) > START_POINT_DISTANCE_THRESHOLD:
                continue

            # line_vector = self.compute_direction(rotated_points)
            line_vector = (
                np.array(LineString(rotated_points).interpolate(SAMPLED_DISTANCE).coords) - rotated_points[0]
            )[0]

            # Check if the line is vertical (parallel to the y-axis)
            if abs(line_vector[0]) < 1e-3:
                continue

            y_intercept = rotated_points[0, 1] - line_vector[1] / line_vector[0] * rotated_points[0, 0]
            # Check if the line is out of the GT range
            if abs(y_intercept) > half_length:  # - 0.5: # 不考虑处于道路边沿的车道线
                continue

            valid_lane_index.append((key, y_intercept))

        valid_lane_index = sorted(valid_lane_index, key=lambda item: abs(item[1]), reverse=False)
        # print('\n', reference_line, '\n', ego_pts_ori, '\n', ego_pts, '\n', half_length, '\n', valid_lane_index)
        # self.plot_2d_pt_for_entrance(reference_line, ego_pts_ori, ego_pts, half_length,  valid_lane_index)

        lane_number = round(2 * half_length / 3.5)

        # valid_lane_index 结合 lane_number 选取中间top1 或者top2的道路线，生成GT
        if lane_number % 2 == 0 and len(valid_lane_index) >= 1:  # 两车道宽，选择中间一条车道线就行；
            target_lane_ids = [valid_lane_index[0], valid_lane_index[0]]
        elif lane_number % 2 == 1 and len(valid_lane_index) >= 2:
            target_lane_ids = [valid_lane_index[0], valid_lane_index[1]]
        else:
            return []

        # compute gt line
        combination_gt = []
        norm_width = self.norm_width(2 * half_length)
        for lane_id, y_dis in target_lane_ids:
            line_pts = np.array(lines_gt[lane_id]["points"])
            direction = self.compute_direction(line_pts)
            if len(direction) < 2:
                continue
            if np.sqrt(direction[0] ** 2 + direction[1] ** 2) == 0:
                return []
            ego_pt = [list(line_pts[0]) + [1]]
            lidar_pt = (np.array(ego_pt) @ ego2lidar.T)[:, :3].tolist()
            norm_pt = self.norm_line(np.array(lidar_pt))[0]

            ego_direct = [list(direction) + [1]]
            lidar_direct = (np.array(ego_direct) @ ego2lidar.T)[:, :3].tolist()[0]
            sin_xy, cos_xy = self.compute_sin_cos(lidar_direct)
            if sin_xy <= -0.2:  # sikp reverse entrance
                return []
            combination_gt.extend(
                [
                    [norm_pt[0], norm_pt[1], norm_pt[2]],
                    [self.normalize_trig(sin_xy), self.normalize_trig(cos_xy), norm_width],
                ]
            )
        if np.isnan(np.array(combination_gt)).any():
            return []
        assert len(combination_gt) == 4, print(f"Find Error Combination_gt: {combination_gt}")

        return combination_gt

    def compute_point_vector_width_for_stopline(self, target_line, ego2lidar):
        # 计算 target_line 的长度
        target_width = self.compute_length(target_line)

        # 组合 GT为[[norm_pt1_x, norm_pt1_y, norm_pt1_z,],
        #           [sin(direct1), cos(direct1), target_width,],
        #          [norm_pt2_x, norm_pt2_y, norm_pt1_z,],
        #           [sin(direct2), cos(direct2), target_width,],
        # ]
        norm_width = self.norm_width(target_width)

        target_center = np.mean(target_line, axis=0).tolist()
        ego_pt = [target_center + [1]]
        lidar_pt = (np.array(ego_pt) @ ego2lidar.T)[:, :3].tolist()
        norm_pt = self.norm_line(np.array(lidar_pt))[0]

        ego_direct = self.compute_normal_direction(target_line)
        ego_direct = ego_direct.tolist()
        if np.sqrt(ego_direct[0] ** 2 + ego_direct[1] ** 2) == 0:
            return []
        ego_direct = [ego_direct + [1]]
        lidar_direct = (np.array(ego_direct) @ ego2lidar.T)[:, :3].tolist()[0]
        sin_xy, cos_xy = self.compute_sin_cos(lidar_direct)
        combination_gt = [
            [norm_pt[0], norm_pt[1], norm_pt[2]],
            [self.normalize_trig(sin_xy), self.normalize_trig(cos_xy), norm_width],
            [norm_pt[0], norm_pt[1], norm_pt[2]],
            [self.normalize_trig(sin_xy), self.normalize_trig(cos_xy), norm_width],
        ]
        if np.isnan(np.array(combination_gt)).any():
            return []
        assert len(combination_gt) == 4, print(f"Find Error Combination_gt: {combination_gt}")
        return combination_gt

    @staticmethod
    def normalize_trig(value):
        """
        将 sin 或 cos 值从 [-1, 1] 归一化到 [0, 1]
        """
        return (value + 1) / 2


def fixed_forkpoint(bev_lanes):
    bev_forkpoints = {}
    bev_sectionpoints = {}
    bev_lanes_index = []
    bev_lanes_pts = []
    for k, v in bev_lanes.items():
        if len(v) >= 2:
            bev_lanes_index.append(k)
            bev_lanes_pts.append(v["points"])

    dpts_pools = []
    same_pt_dist = 0.1
    forkpt_num = 0
    sectpt_num = 0
    for i, lane in enumerate(bev_lanes_pts):
        lane_spt = lane[0]
        lane_ept = lane[-1]
        spt_match = []
        ept_match = []
        ss_tmp_dpt_pool, se_tmp_dpt_pool, ee_tmp_dpt_pool, es_tmp_dpt_pool = [], [], [], []
        for k, lane2 in enumerate(bev_lanes_pts):
            if k <= i:
                continue
            lane2_spt = lane2[0]
            lane2_ept = lane2[-1]
            dist_ss, dist_se, dist_es, dist_ee = 999999999, 999999999, 999999999, 999999999
            if lane_spt not in dpts_pools:
                if lane2_spt not in dpts_pools:
                    dist_ss = dist_func_new(lane_spt, lane2_spt)
                if lane2_ept not in dpts_pools:
                    dist_se = dist_func_new(lane_spt, lane2_ept)
            if lane_ept not in dpts_pools:
                if lane2_ept not in dpts_pools:
                    dist_ee = dist_func_new(lane_ept, lane2_ept)
                if lane2_spt not in dpts_pools:
                    dist_es = dist_func_new(lane_ept, lane2_spt)

            if dist_ss <= same_pt_dist:
                spt_match.append(bev_lanes_index[k])
                ss_tmp_dpt_pool.append(lane2_spt)
            if dist_se <= same_pt_dist:
                spt_match.append(bev_lanes_index[k])
                se_tmp_dpt_pool.append(lane2_ept)
            if dist_ee <= same_pt_dist:
                ept_match.append(bev_lanes_index[k])
                ee_tmp_dpt_pool.append(lane2_ept)
            if dist_es <= same_pt_dist:
                ept_match.append(bev_lanes_index[k])
                es_tmp_dpt_pool.append(lane2_spt)
        if spt_match:
            dpts_pools.append(lane_spt)
            if len(se_tmp_dpt_pool) == 1 and len(spt_match) == 1:
                spt_match.append(bev_lanes_index[i])
                bev_sectionpoints[str(sectpt_num)] = {
                    "attribute": {
                        "match_lane": spt_match,
                        "start_lane": [bev_lanes_index[i]],
                        "end_lane": se_tmp_dpt_pool,
                    },
                    "points": [lane_spt],
                }
                sectpt_num += 1

            elif len(se_tmp_dpt_pool) > 1 and len(spt_match) > 1:
                spt_match.append(bev_lanes_index[i])
                bev_forkpoints[str(forkpt_num)] = {
                    "attribute": {
                        "match_lane": spt_match,
                        "start_lane": [bev_lanes_index[i]],
                        "end_lane": se_tmp_dpt_pool,
                    },
                    "points": [lane_spt],
                }
                forkpt_num += 1

            if len(ss_tmp_dpt_pool) >= 1 and len(spt_match) >= 1:
                spt_match.append(bev_lanes_index[i])
                bev_forkpoints[str(forkpt_num)] = {
                    "attribute": {
                        "match_lane": spt_match,
                        "start_lane": ss_tmp_dpt_pool + [bev_lanes_index[i]],
                        "end_lane": [],
                    },
                    "points": [lane_spt],
                }
                forkpt_num += 1

        if ept_match:
            dpts_pools.append(lane_ept)
            if len(es_tmp_dpt_pool) == 1 and len(ept_match) == 1:
                ept_match.append(bev_lanes_index[i])
                bev_sectionpoints[str(sectpt_num)] = {
                    "attribute": {
                        "match_lane": ept_match,
                        "start_lane": es_tmp_dpt_pool,
                        "end_lane": [bev_lanes_index[i]],
                    },
                    "points": [lane_ept],
                }
                sectpt_num += 1

            elif len(es_tmp_dpt_pool) > 1 and len(ept_match) > 1:
                ept_match.append(bev_lanes_index[i])
                bev_forkpoints[str(forkpt_num)] = {
                    "attribute": {
                        "match_lane": ept_match,
                        "start_lane": es_tmp_dpt_pool,
                        "end_lane": [bev_lanes_index[i]],
                    },
                    "points": [lane_ept],
                }
                forkpt_num += 1

            if len(ee_tmp_dpt_pool) >= 1 and len(spt_match) >= 1:
                ept_match.append(bev_lanes_index[i])
                bev_forkpoints[str(forkpt_num)] = {
                    "attribute": {
                        "match_lane": spt_match,
                        "start_lane": [],
                        "end_lane": ee_tmp_dpt_pool + [bev_lanes_index[i]],
                    },
                    "points": [lane_spt],
                }
                forkpt_num += 1

        for pt in se_tmp_dpt_pool + ss_tmp_dpt_pool + es_tmp_dpt_pool + ee_tmp_dpt_pool:
            if pt not in dpts_pools:
                dpts_pools.append(pt)

    return bev_forkpoints, bev_sectionpoints


def dist_func_new(pt1, pt2):
    dist = 0
    for i in range(len(pt1)):
        dist += (pt1[i] - pt2[i]) ** 2
    dist = dist ** 0.5
    return dist


def dist_func(l_1, l_2):
    nums_1 = l_1[0]
    nums_2 = l_2[-1]
    flag = True
    for i in range(len(nums_1)):
        if abs(nums_1[i] - nums_2[i]) < 1e-1:
            pass
        else:
            flag = False
            return flag
    return flag


class Function:
    def __init__(self, func_type, coefs):
        assert func_type in ["poly", "arc"]
        self.func_type = func_type
        self.coefs = coefs

    def __call__(self, x):
        if self.func_type == "poly":
            return np.poly1d(self.coefs)(x)
        if self.func_type == "arc":
            return self.coefs[3] * np.sqrt(self.coefs[2] - (x - self.coefs[0]) ** 2) + self.coefs[1]

    def radius(self, x):
        if self.func_type == "arc":
            return self.coefs[2] ** 0.5
        if self.func_type == "poly":
            y1 = self.coefs[0] * 3 * x ** 2 + self.coefs[1] * 2 * x + self.coefs[2]
            y2 = self.coefs[0] * 6 * x + self.coefs[1] * 2
            return np.abs((1 + y1 ** 2) ** (1.5) / y2)

    def tangent(self, x):
        if self.func_type == "poly":
            return 3 * self.coefs[0] * x ** 2 + 2 * self.coefs[1] * x + self.coefs[2]


def calculating_curvature(pts):
    all_radius_y = []
    pts = np.array(pts)
    func = Function("poly", constraint_polyfit_curve(pts[:, 0], pts[:, 1]))
    all_radius_y.append(np.mean(np.array([func.radius(i) for i in pts[:, 0]])))
    return np.mean(np.array(all_radius_y))


def constraint_polyfit_curve(x, y, w=None, start=True, end=True, eps=1e-5):
    x_max = np.abs(x).max()
    y_max = np.abs(y).max()
    norm_x = x / x_max
    norm_y = y / y_max
    n = norm_x.shape[0]
    if w is None:
        w = np.ones(n)
    tmp = norm_x.copy()
    h = [w.sum()]
    for i in range(6):
        h.append((tmp * w).sum())
        tmp *= norm_x

    g = []
    tmp = norm_y.copy()
    for i in range(4):
        g.append((tmp * w).sum())
        tmp *= norm_x

    mat = np.array(
        [
            [h[6] + eps * 18 * n * end, h[5] + eps * 6 * n * end, h[4], h[3]],
            [h[5] + eps * 6 * n * end, h[4] + eps * n * (2 * start + 2 * end), h[3], h[2]],
            [h[4], h[3], h[2], h[1]],
            [h[3], h[2], h[1], n],
        ]
    )
    coefs = np.linalg.inv(mat).dot(np.array(g[::-1])) * y_max
    return (coefs[0] / x_max ** 3, coefs[1] / x_max ** 2, coefs[2] / x_max, coefs[3])


def get_rotate_matrix(reference_line):
    # 1. Calculate the midpoint of LineString A
    midpoint = [(reference_line[0][0] + reference_line[1][0]) / 2, (reference_line[0][1] + reference_line[1][1]) / 2]
    mid_x, mid_y = midpoint[0], midpoint[1]

    reference_line = LineString(reference_line)
    # 2. Calculate the direction vector of LineString A
    direction = np.array(reference_line.coords[-1]) - np.array(reference_line.coords[0])
    direction /= np.linalg.norm(direction)
    if np.dot(direction, [0, 1]) < 0:
        direction = -direction
    # 3. Calculate the angle of rotation (atan2 gives angle w.r.t. x-axis)
    theta = np.arctan2(direction[0], direction[1])

    # 3. Create the rotation matrix
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    rotation_matrix = np.array([[cos_theta, -sin_theta], [sin_theta, cos_theta]])
    return rotation_matrix, (mid_x, mid_y)
