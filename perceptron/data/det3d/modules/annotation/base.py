import numpy as np
import bisect
import warnings
from typing import Dict
from abc import abstractmethod
from perceptron.data.det3d.utils.functional import load_angle_anno, non_gt_filter, outlier_filter, outlier_filter_god
from perceptron.data.det3d.modules.lidar import LidarBase
from scipy.spatial.transform import Rotation as R


class AnnotationBase:
    """
    loader_output: reformat data output , provided by class LoaderBase
    mode: train or val
    """

    def __init__(self, loader_output, mode, label_key="labels") -> None:
        self.loader_output = loader_output
        self.mode = mode
        self.label_key = label_key

    @abstractmethod
    def __getitem__(self, idx) -> Dict:
        raise NotImplementedError

    def pre_check_not_skip(self, index: int) -> bool:
        """check anno / sensor valid, return True if all valid, False if some is not valid."""
        return True

    def get_scene_name(self, idx):
        # 定位当前idx数据存在于哪个json文件，json文件地址作为 scene_name。
        if "cummulative_sizes" in self.loader_output and "json_collection" in self.loader_output:
            json_idx = bisect.bisect_right(self.loader_output["cummulative_sizes"], idx)
            json_path = self.loader_output["json_collection"][json_idx]
            return json_path
        else:
            raise NotImplementedError


class AnnotationDet(AnnotationBase):
    """
    遮挡定义参考wiki: https://wiki.megvii-inc.com/pages/viewpage.action?pageId=307327004
    对应遮挡阈值：1/2/3/4
    对应遮挡程度：完全可见/轻微遮挡/中度遮挡/严重遮挡
    对应可见比例：100% / 50%～100% / 20%～100% / 0～100%
    """

    def __init__(
        self,
        loader_output,
        mode,
        category_map,
        class_names,
        occlusion_threshold=4,
        filter_outlier_boxes=True,
        filter_outlier_frames=False,
        filter_empty_2d_bboxes=True,
        filter_empty_frames=False,
        filter_short_track=False,
        roi_range=None,
        label_key="labels",
        HF=False,
        with_plain_velocity=False,
        with_occlusion=False,
        soft_occ_threshold=False,  # 自定义调整遮挡比例
        cam_vis_state=[0, 1],
    ) -> None:
        super(AnnotationDet, self).__init__(loader_output, mode)

        self.category_map = category_map
        self.class_names = class_names

        self.occlusion_threshold = occlusion_threshold
        self.filter_outlier_frames = filter_outlier_frames
        self.filter_outlier_boxes = filter_outlier_boxes
        self.filter_empty_2d_bboxes = filter_empty_2d_bboxes
        self.filter_empty_frames = filter_empty_frames
        self.filter_short_track = filter_short_track
        self.roi_range = roi_range
        self.label_key = label_key
        self.HF = HF
        self.with_plain_velocity = with_plain_velocity
        self.with_occlusion = with_occlusion
        if self.with_occlusion:
            assert "遮挡" in self.category_map
        self.soft_occ_threshold = soft_occ_threshold
        self.cam_vis_state = cam_vis_state

    @staticmethod
    def _load_single_box(label):
        coor = [
            label["xyz_lidar"]["x"],
            label["xyz_lidar"]["y"],
            label["xyz_lidar"]["z"],
            label["lwh"]["l"],
            label["lwh"]["w"],
            label["lwh"]["h"],
            load_angle_anno(label),
        ]
        return np.array(coor, dtype=np.float32)

    def _judge_whether_outlier_box(self, box_anno, cat_anno):
        if cat_anno not in self.category_map:
            return False
        cur_anno = self.category_map[cat_anno]
        is_outlier = False
        if cur_anno in ["pedestrian"] and (box_anno[3:6] > np.array([3, 3, 3])).any():
            is_outlier = True
        elif cur_anno in ["car", "bus", "bicycle"] and (box_anno[3:6] > np.array([30, 6, 10])).any():
            is_outlier = True
        return is_outlier

    def _get_occlusion_attr(self, anno, camera_keys, around_occluded_mode=False):
        if "2d_bboxes" not in anno and "cam_vis_dict" not in anno:
            if_visible = True
            return if_visible
        mapping = {
            "严重遮挡": 1,
            "不可见": 2,
            "正常": 0,
            0: 0,
            1: 1,
            2: 2,
            "0": 0,
            "1": 1,
            "2": 2,
            3: 3,
            "0%": 0,
            "0%-30%": 1,
            "30%-60%": 2,
            "60%-90%": 3,
            "90%-100%": 4,
        }
        if self.HF:
            mapping = {"严重遮挡": 1, "不可见": 2, "正常": 0, 0: 0, 1: 1, 2: 2, 3: 3, "0": 3, "1": 0, "2": 1, "3": 2}
        if_visible = anno["2d_visibility"] if "2d_visibility" in anno else False
        if "occluded_fusion" in anno and "cam_vis_dict" not in anno:
            if not self.soft_occ_threshold:
                if anno["occluded_fusion"] < self.occlusion_threshold:
                    if_visible = True
            else:
                if anno["category"] in ["汽车", "car", "Car", "小汽车"]:
                    if anno["occluded_fusion"] == -1:  # 范围外
                        if_visible = True
                    elif -5 < anno["xyz_lidar"]["y"] < 5 and -8 < anno["xyz_lidar"]["x"] < 8:  # cipv范围保持可见
                        if_visible = True
                    elif anno["occlude_cam_percent"] > self.soft_occ_threshold:  # TODO 读一个是否是前视字段。
                        if_visible = True
                elif anno["category"] in ["大货车", "小货车", "tuogua", "拖挂", "货车", "工程车", "巴士"]:  # 大车暂时不设置
                    if anno["occluded_fusion"] == -1:
                        if_visible = True
                    elif -20 < anno["xyz_lidar"]["y"] < 20 and -8 < anno["xyz_lidar"]["x"] < 8:
                        if_visible = True
                    elif anno["occlude_cam_percent"] > self.soft_occ_threshold:
                        if_visible = True
                else:  # vru近距离
                    if -15 < anno["xyz_lidar"]["y"] < 15:
                        if_visible = True
                    elif anno["occluded_fusion"] < self.occlusion_threshold:
                        if_visible = True
        elif "cam_vis_dict" in anno:
            if_visible = False
            for cam_key, cam_vis in anno["cam_vis_dict"].items():
                if "state" in cam_vis:
                    if cam_vis["state"] in self.cam_vis_state:
                        if_visible = True
                        break
                elif "occlusion" in cam_vis:
                    if cam_vis["occlusion"] in self.cam_vis_state and cam_vis["occlusion"] < 4:
                        if_visible = True
                        break
            return if_visible
        else:
            assert "2d_bboxes" in anno, "Illegal anno for occlusion attributes"
            if not self.filter_empty_2d_bboxes and len(anno["2d_bboxes"]) == 0:
                if_visible = True
            for cam_anno in anno["2d_bboxes"]:
                if cam_anno["occluded"] is None or cam_anno["occluded"] not in mapping:
                    # print("cam_anno_occlusion is invalid!")
                    continue
                if (around_occluded_mode or cam_anno["sensor_name"] in camera_keys) and int(
                    mapping[cam_anno["occluded"]]
                ) < self.occlusion_threshold:
                    if_visible = True
                    break

        return if_visible

    def _get_occlusion_attr_val(self, anno, camera_keys, around_occluded_mode=False):

        if anno.get("interpolate", False):  # 插值的 直接变成不可见
            return False
        occlusion_threshold = 4
        try:
            if (
                "camera_occlusion" in anno
                and len(anno["camera_occlusion"]) > 0
                and min(anno["camera_occlusion"].values()) < occlusion_threshold
            ):
                is_visible = True
            else:
                is_visible = False
        except Exception:
            return False
        return is_visible

    @property
    def camera_keys(self):
        if "_camera_keys" not in self.__dict__:
            _camera_keys = []
            for item in self.loader_output["camera_name_mapping"]["standard"].values():
                _camera_keys.append(item["hidden_name"])
            self._camera_keys = _camera_keys
        return self._camera_keys

    def get_velocity(self, anno):
        if self.label_key == "labels":
            velocity = np.array(list(anno["velocity_lidar"].values()))
        elif self.label_key == "pre_labels":
            if isinstance(anno["velocity_global"], dict):
                velocity = np.array(list(anno["velocity_global"].values()))
            elif isinstance(anno["velocity_global"], list):
                velocity = np.array(anno["velocity_global"])
        return velocity

    def points_trans(self, points, trans_mat):
        homogeneous_point = np.ones((1, 4), dtype=np.float64)
        homogeneous_point[0, :3] = points[:3]
        return ((trans_mat @ homogeneous_point.T).T)[0, :3]

    def _get_single_anno(self, anno, trans_mat, trans_mat_mask):
        # load 3d box annotation
        box_anno = self._load_single_box(anno)
        if self.with_plain_velocity:
            velocity = self.get_velocity(anno)
            velocity = (
                self.points_trans(velocity, np.linalg.inv(trans_mat) * trans_mat_mask)
                if self.label_key == "pre_labels"
                else velocity
            )
            box_anno = np.concatenate((box_anno, velocity[:2]))
        # load category annotation, considering occlusion or outliers
        cat_anno = anno["category"]
        track_id = anno.get("track_id", -2)
        if track_id == -2:
            return None

        if self.occlusion_threshold > 0 and not self._get_occlusion_attr(anno, self.camera_keys):
            if self.with_occlusion and cat_anno in self.category_map:
                cat_anno = "遮挡"
            else:
                cat_anno = "蒙版"
        if self.filter_outlier_boxes and self._judge_whether_outlier_box(box_anno, cat_anno):
            cat_anno = "蒙版"
        if self.filter_short_track:
            if "track_length" in anno and anno["track_length"] < 2:
                cat_anno = "蒙版"
        category = self.category_map[cat_anno] if cat_anno in self.category_map else "other"
        # load num_lidar_pt
        num_lidar_info = anno["num_lidar_pts"] if "num_lidar_pts" in anno else 20
        return box_anno, category, num_lidar_info, track_id

    def get_lidar_to_world(self, scene_id, sensor_key, frame):
        def fuse_rot_trans(rot, trans, if_inv=False):
            res = np.eye(4, dtype=np.float64)
            res[:3, :3] = rot
            res[:3, 3] = trans
            return res

        (cur_lidar2gnss_rot, cur_lidar2gnss_trans,) = LidarBase._get_lidar_to_gnss_rotate_and_translation(
            self.loader_output["calibrated_sensors"][scene_id][sensor_key]
        )
        ref_lidar_to_gnss = fuse_rot_trans(cur_lidar2gnss_rot, cur_lidar2gnss_trans)
        # current gnss to world extrinsics
        (
            cur_gnss2world_rot,
            cur_gnss2world_trans,
        ) = LidarBase._get_gnss_to_world_rotate_and_translation(frame)
        ref_gnss_to_world = fuse_rot_trans(cur_gnss2world_rot, cur_gnss2world_trans)

        trans_mat = ref_gnss_to_world @ ref_lidar_to_gnss

        return trans_mat

    def __getitem__(self, idx):
        data_dict = dict()
        if self.mode != "train":
            gt_labels = np.zeros((0,), dtype=np.float32)
            gt_boxes = np.zeros((0, 7), dtype=np.float32)
            data_dict["gt_labels"] = gt_labels
            data_dict["gt_boxes"] = gt_boxes

        result = {}
        boxes = []
        cats = []
        num_lidar_pts = []
        track_ids = []
        frame_data_list = self.loader_output["frame_data_list"]
        frame = self.loader_output["frame_data_list"][idx]
        cummulative_sizes = self.loader_output["frame_data_list"].cummulative_sizes
        scene_id = bisect.bisect_right(cummulative_sizes, idx)
        if self.label_key not in frame_data_list[idx]:
            return None

        if (
            "front_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["front_lidar"]
        ):
            sensor_key = "front_lidar"
        elif (
            "middle_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["middle_lidar"]
        ):
            sensor_key = "middle_lidar"
        else:
            return None
        trans_mat = self.get_lidar_to_world(scene_id, sensor_key, frame).astype(np.float64)
        trans_mat_mask = np.ones_like(trans_mat, dtype=np.float64)
        trans_mat_mask[:3, 3] = 0

        annos = frame_data_list[idx][self.label_key]
        for anno in annos:
            # filter ego bbox
            if abs(anno["xyz_lidar"]["x"]) <= 0.4 and abs(anno["xyz_lidar"]["y"]) <= 2.0 and anno["category"] == "汽车":
                continue
            flags = self._get_single_anno(anno, trans_mat, trans_mat_mask)
            if flags is None:
                return None
            box_anno, category, num_lidar_info, track_id = flags
            boxes.append(box_anno)
            cats.append(category)
            num_lidar_pts.append(num_lidar_info)
            track_ids.append(track_id)

        gt_boxes = np.stack(boxes) if len(boxes) > 0 else boxes
        result["gt_boxes"] = np.array(gt_boxes, dtype=np.float32)
        gt_labels = np.stack(cats) if len(cats) > 0 else cats

        new_gt_labels = []
        for i in gt_labels:
            if i in self.class_names:
                new_gt_labels.append(self.class_names.index(i))
            elif i == "occlusion":
                new_gt_labels.append(-2)
            else:
                new_gt_labels.append(-1)
        result["gt_labels"] = np.array(new_gt_labels, dtype=np.int64)

        # result["gt_labels"] = np.array(
        #     [self.class_names.index(i) if i in self.class_names else -1 for i in gt_labels], dtype=np.int64
        # )
        result["labels"] = np.stack(cats) if len(cats) > 0 else cats  # for evaluation
        num_lidar_points = np.stack(num_lidar_pts) if len(boxes) > 0 else num_lidar_pts
        result["num_lidar_points"] = np.array(num_lidar_points, dtype=np.float32)
        result["instance_inds"] = np.array(track_ids, dtype=np.int64)

        if self.filter_outlier_boxes and outlier_filter(
            gt_boxes=result["gt_boxes"], gt_labels=result["gt_labels"], class_names=self.class_names
        ):
            print("ffffffff")
            return None

        if self.filter_empty_frames and non_gt_filter(
            gt_boxes=result["gt_boxes"], gt_labels=result["gt_labels"], roi_range=self.roi_range
        ):
            return None

        data_dict["gt_labels"] = result["gt_labels"]
        data_dict["gt_boxes"] = result["gt_boxes"]
        data_dict["instance_inds"] = result["instance_inds"]

        return data_dict

    def get_annos(self, idx, data_dict=None):
        warnings.warn("get_annos is deprecated, replaced by direct index as a sequence.", DeprecationWarning)
        return self.__getitem__(idx)

    # def pre_check_not_skip(self, index: int) -> bool:
    #     """check anno / sensor valid, return True if all valid, False if some is not valid.
    #     """
    #     if self.label_key not in self.loader_output["frame_data_list"][index]:
    #         return False
    #     return True


class AnnotationTrack(AnnotationDet):
    def __init__(self, with_predict=False, hist_traj_len=20, fut_traj_len=13, with_ego_motion=False, **kwargs) -> None:
        self.with_predict = with_predict
        self.hist_traj_len = hist_traj_len
        self.fut_traj_len = fut_traj_len  # Max frame number for forecasting. Default: 13 (6 seconds + current frame)
        self.with_ego_motion = with_ego_motion
        super().__init__(**kwargs)

    def _get_ego_status(self, idx):
        """
        velocity info:
        """
        frame = self.loader_output["frame_data_list"][idx]
        cummulative_sizes = self.loader_output["frame_data_list"].cummulative_sizes
        scene_id = bisect.bisect_right(cummulative_sizes, idx)
        if (
            "front_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["front_lidar"]
        ):
            sensor_key = "front_lidar"
        elif (
            "middle_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["middle_lidar"]
        ):
            sensor_key = "middle_lidar"
        else:
            return None
        trans_mat = self.get_lidar_to_world(scene_id, sensor_key, frame).astype(np.float64)
        trans_mat_mask = np.ones_like(trans_mat, dtype=np.float64)
        trans_mat_mask[:3, 3] = 0

        ins_data = self.loader_output["frame_data_list"][idx]["ins_data"]
        ego_velocity = np.array(list(ins_data["linear_velocity"].values()))
        ego_velocity = self.points_trans(ego_velocity, np.linalg.inv(trans_mat) * trans_mat_mask)
        return ego_velocity

    def _get_timestamp(self, idx, lidar_name="front_lidar"):
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        if (
            lidar_name in sensor_data.keys()
            and sensor_data[lidar_name] is not None
            and "timestamp" in sensor_data[lidar_name]
        ):
            timestamp = sensor_data[lidar_name]["timestamp"]
        else:
            potential_lidar_names = [
                "middle_lidar",
                "fuser_lidar",
                "front_lidar",
                "left_lidar",
                "right_lidar",
                "back_lidar",
                "cam_front_120",
                "radar0",
            ]
            valid_lidar_names = [
                name
                for name in potential_lidar_names
                if name in sensor_data.keys() and sensor_data[name] is not None and "timestamp" in sensor_data[name]
            ]
            if len(valid_lidar_names) > 0:
                timestamp = sensor_data[valid_lidar_names[0]]["timestamp"]
            else:
                return 0
                # raise RuntimeError(f"invalid lidar name in :{lidar_name}")
        return float(timestamp) * 1e6

    def __getitem__(self, idx):
        data_dict = super().__getitem__(idx)
        if data_dict:
            data_dict["predict_attribute"] = self._get_traj_anno(data_dict, idx)
        if self.with_ego_motion:
            data_dict["ego_velocity"] = self._get_ego_status(idx)
        return data_dict

    def _get_traj_anno(self, data_dict, idx):
        cumulative_sizes = self.loader_output["frame_data_list"].cumulative_sizes
        scene_id = bisect.bisect_right(cumulative_sizes, idx)
        frame = self.loader_output["frame_data_list"][idx]
        track_ids = data_dict["instance_inds"].tolist()
        boxes = data_dict["gt_boxes"]
        if (
            "front_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["front_lidar"]
        ):
            sensor_key = "front_lidar"
        elif (
            "middle_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["middle_lidar"]
        ):
            sensor_key = "middle_lidar"
        else:
            return None
        trans_mat = self.get_lidar_to_world(scene_id, sensor_key, frame).astype(np.float64)
        trans_mat_mask = np.ones_like(trans_mat, dtype=np.float64)
        trans_mat_mask[:3, 3] = 0
        traj_info = {
            "gt_forecasting_ego_locs": np.zeros((len(track_ids), self.fut_traj_len, 3), dtype=np.float64),
            "gt_forecasting_locs": np.zeros((len(track_ids), self.fut_traj_len, 3), dtype=np.float64),
            "gt_forecasting_angles": np.zeros((len(track_ids), self.fut_traj_len), dtype=np.float32),
            "gt_forecasting_masks": np.zeros((len(track_ids), self.fut_traj_len), dtype=np.float32),
            "gt_forecasting_bboxes": np.zeros((len(track_ids), 3), dtype=np.float32),
            "gt_forecasting_velocity": np.zeros((len(track_ids), self.fut_traj_len, 3), dtype=np.float32),
            "gt_forecasting_l2g": np.zeros((len(track_ids), self.fut_traj_len, 4, 4), dtype=np.float64),
            "gt_history_ego_locs": np.zeros((len(track_ids), self.hist_traj_len, 3), dtype=np.float64),
            "gt_history_locs": np.zeros((len(track_ids), self.hist_traj_len, 3), dtype=np.float64),
            "gt_history_angles": np.zeros((len(track_ids), self.hist_traj_len), dtype=np.float32),
            "gt_history_masks": np.zeros((len(track_ids), self.hist_traj_len), dtype=np.float32),
            "gt_history_l2g": np.zeros((len(track_ids), self.hist_traj_len, 4, 4), dtype=np.float64),
            "gt_history_timestamp": np.zeros((len(track_ids), self.hist_traj_len), dtype=np.float32),
        }
        for i in range(len(track_ids)):
            traj_info["gt_forecasting_locs"][i][0, :] = boxes[i][:3]
            traj_info["gt_forecasting_angles"][i][0] = boxes[i][6]
            traj_info["gt_forecasting_masks"][i][0] = 1
            traj_info["gt_forecasting_bboxes"][i][:] = boxes[i][3:6]
            velocity = self.get_velocity(self.loader_output["frame_data_list"][idx][self.label_key][i])
            traj_info["gt_forecasting_velocity"][i][0, :] = (
                self.points_trans(velocity, np.linalg.inv(trans_mat) * trans_mat_mask)
                if self.label_key == "pre_labels"
                else velocity
            )

        if (
            "front_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["front_lidar"]
        ):
            sensor_key = "front_lidar"
        elif (
            "middle_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["middle_lidar"]
        ):
            sensor_key = "middle_lidar"
        else:
            return None
        # ------------- generate traj info --------------
        trans_mat = self.get_lidar_to_world(scene_id, sensor_key, frame)
        for i in range(1, self.fut_traj_len):
            scene_id_i = bisect.bisect_right(cumulative_sizes, idx + i)
            if scene_id_i != scene_id:
                break
            frame_i = self.loader_output["frame_data_list"][idx + i]

            trans_mat_i = self.get_lidar_to_world(scene_id_i, sensor_key, frame_i).astype(np.float64)
            # ----------------------------------------------------
            homogeneous_point = np.array([[0, 0, 0, 1]], dtype=np.float64)
            ego_center = ((np.linalg.inv(trans_mat) @ trans_mat_i @ homogeneous_point.T).T)[0, :3]
            # ----------------------------------------------------
            for label in frame_i[self.label_key]:
                if "track_id" in label and label["track_id"] in track_ids:
                    center = np.array(list(label["xyz_lidar"].values())).astype(np.float64)
                    # ----------------------------------------------------
                    homogeneous_point = np.ones((1, 4), dtype=np.float64)
                    homogeneous_point[0, :3] = center[:3]
                    center = ((np.linalg.inv(trans_mat) @ trans_mat_i @ homogeneous_point.T).T)[0, :3]
                    # ----------------------------------------------------
                    index_i = track_ids.index(label["track_id"])
                    traj_info["gt_forecasting_ego_locs"][index_i][i, :] = ego_center
                    traj_info["gt_forecasting_l2g"][index_i][i] = trans_mat_i
                    traj_info["gt_forecasting_locs"][index_i][i, :] = center
                    traj_info["gt_forecasting_angles"][index_i][i] = load_angle_anno(
                        label
                    )  # need trans to current coord
                    traj_info["gt_forecasting_masks"][index_i][i] = 1
                    # traj_info["gt_forecasting_bboxes"][index_i][:] = boxes[index_i][3:6]

                    velocity = self.get_velocity(label)
                    if self.label_key == "pre_labels":
                        traj_info["gt_forecasting_velocity"][index_i][i, :] = self.points_trans(
                            velocity, np.linalg.inv(trans_mat) * trans_mat_mask
                        )
                    elif self.label_key == "labels":
                        traj_info["gt_forecasting_velocity"][index_i][i, :] = self.points_trans(
                            velocity, (np.linalg.inv(trans_mat) @ trans_mat_i) * trans_mat_mask
                        )

        for i in range(1, self.hist_traj_len + 1):
            scene_id_i = bisect.bisect_right(cumulative_sizes, idx - i)
            if scene_id_i != scene_id or idx - i < 0:
                break
            hist_ts = self._get_timestamp(idx - i)
            frame_i = self.loader_output["frame_data_list"][idx - i]

            trans_mat_i = self.get_lidar_to_world(scene_id_i, sensor_key, frame_i).astype(np.float64)
            # ----------------------------------------------------
            homogeneous_point = np.array([[0, 0, 0, 1]], dtype=np.float64)
            ego_center = ((np.linalg.inv(trans_mat) @ trans_mat_i @ homogeneous_point.T).T)[0, :3]
            # ----------------------------------------------------
            for label in frame_i[self.label_key]:
                if "track_id" in label and label["track_id"] in track_ids:
                    center = np.array(list(label["xyz_lidar"].values())).astype(np.float64)
                    # ----------------------------------------------------
                    homogeneous_point = np.ones((1, 4), dtype=np.float64)
                    homogeneous_point[0, :3] = center[:3]
                    center = ((np.linalg.inv(trans_mat) @ trans_mat_i @ homogeneous_point.T).T)[0, :3]
                    # ----------------------------------------------------
                    index_i = track_ids.index(label["track_id"])
                    traj_info["gt_history_ego_locs"][index_i][self.hist_traj_len - i, :] = ego_center
                    traj_info["gt_history_l2g"][index_i][self.hist_traj_len - i] = trans_mat_i
                    traj_info["gt_history_locs"][index_i][self.hist_traj_len - i, :] = center
                    traj_info["gt_history_angles"][index_i][self.hist_traj_len - i] = load_angle_anno(label)
                    traj_info["gt_history_masks"][index_i][self.hist_traj_len - i] = 1
                    traj_info["gt_history_timestamp"][index_i][self.hist_traj_len - i] = hist_ts
        return traj_info


def load_zero_angle_anno():
    """
    四元数转欧拉角
    """
    quat = np.zeros((4,), dtype=np.float32)
    quat[0] = 1.0
    quat[1] = 0.0
    quat[2] = 0.0
    quat[3] = 0.0
    return R.from_quat(quat).as_euler("xyz")[-1]


class AnnotationGOD(AnnotationDet):
    def __init__(self, fixyaw=False, **kwargs) -> None:
        super().__init__(**kwargs)
        self.fixyaw = fixyaw
        self.cuboid = [
            "waterhorse",
            "triangle_warning",
            "construction_sign",
            "no_parking_sign",
            "parking_limiter",
            "opened_parking_lock",
            "closed_parking_lock",
            "animal_big",
            "animal_small",
            "charging_pile",
            "水马",
        ]

    def _load_single_box(self, label):
        angle = 0
        if not self.fixyaw:
            angle = load_angle_anno(label)
        else:
            if label["category"] in self.cuboid:
                angle = load_angle_anno(label)
            else:
                angle = load_zero_angle_anno()
        coor = [
            label["xyz_lidar"]["x"],
            label["xyz_lidar"]["y"],
            label["xyz_lidar"]["z"],
            label["lwh"]["l"],
            label["lwh"]["w"],
            label["lwh"]["h"],
            angle,
        ]
        return np.array(coor, dtype=np.float32)

    def _judge_whether_outlier_box(self, box_anno, cat_anno):
        if cat_anno not in self.category_map:
            return False
        cur_anno = self.category_map[cat_anno]
        is_outlier = False
        if cur_anno in ["cone", "bar", "bucket", "闪光柱", "锥桶", "防撞桶"] and (box_anno[3:6] > np.array([3, 3, 3])).any():
            is_outlier = True
        elif cur_anno in ["waterhorse", "board", "水马"] and (box_anno[3:6] > np.array([30, 30, 10])).any():
            is_outlier = True

        # near filter
        if self.roi_range is not None:
            mask = (box_anno[:3] >= self.roi_range[:3]).all(0)
            mask &= (box_anno[:3] <= self.roi_range[3:]).all(0)
            is_outlier = not mask
        return is_outlier

    def _get_occlusion_attr(self, anno, camera_keys, around_occluded_mode=False):
        if "2d_bboxes" not in anno and "cam_vis_dict" not in anno:
            if_visible = True
            return if_visible
        mapping = {
            "严重遮挡": 1,
            "不可见": 2,
            "正常": 0,
            0: 0,
            1: 1,
            2: 2,
            "0": 0,
            "1": 1,
            "2": 2,
            3: 3,
            "90%-100%": 1,
            "tiaoguo": 2,
            "0%-30%": 0,
            "0%": 0,
            "30%-60%": 0,
            "60%-90%": 2,
        }
        if self.HF:
            mapping = {"严重遮挡": 1, "不可见": 2, "正常": 0, 0: 0, 1: 1, 2: 2, 3: 3, "0": 3, "1": 0, "2": 1, "3": 2}
        # god
        if "2d_visibility" in anno:
            if_visible = anno["2d_visibility"]
        elif "is_2d_visible" in anno:
            if_visible = anno["is_2d_visible"] == "可见"
            if if_visible:
                return if_visible
        else:
            if_visible = False

        if "occluded_fusion" in anno and "cam_vis_dict" not in anno:
            if not self.soft_occ_threshold:
                if anno["occluded_fusion"] < self.occlusion_threshold:
                    if_visible = True
            else:
                if anno["category"] in ["汽车", "car", "Car", "小汽车"]:
                    if anno["occluded_fusion"] == -1:  # 范围外
                        if_visible = True
                    elif -5 < anno["xyz_lidar"]["y"] < 5 and -8 < anno["xyz_lidar"]["x"] < 8:  # cipv范围保持可见
                        if_visible = True
                    elif anno["occlude_cam_percent"] > self.soft_occ_threshold:  # TODO 读一个是否是前视字段。
                        if_visible = True
                elif anno["category"] in ["大货车", "小货车", "tuogua", "拖挂", "货车", "工程车", "巴士"]:  # 大车暂时不设置
                    if anno["occluded_fusion"] == -1:
                        if_visible = True
                    elif -20 < anno["xyz_lidar"]["y"] < 20 and -8 < anno["xyz_lidar"]["x"] < 8:
                        if_visible = True
                    elif anno["occlude_cam_percent"] > self.soft_occ_threshold:
                        if_visible = True
                else:  # vru近距离
                    if -15 < anno["xyz_lidar"]["y"] < 15:
                        if_visible = True
                    elif anno["occluded_fusion"] < self.occlusion_threshold:
                        if_visible = True
        elif "cam_vis_dict" in anno:
            for cam_key, cam_vis in anno["cam_vis_dict"].items():
                if "state" in cam_vis and cam_vis["state"] in self.cam_vis_state:
                    if_visible = True
                    break
                elif "occlusion" in cam_vis:
                    if cam_vis["occlusion"] in self.cam_vis_state and cam_vis["occlusion"] < 4:
                        if_visible = True
                        break
        else:
            assert "2d_bboxes" in anno, "Illegal anno for occlusion attributes"
            if not self.filter_empty_2d_bboxes and len(anno["2d_bboxes"]) == 0:
                if_visible = True
            for cam_anno in anno["2d_bboxes"]:
                if around_occluded_mode or cam_anno["sensor_name"] in camera_keys:
                    if cam_anno.get("occluded", None) is not None and mapping.get(cam_anno["occluded"]):
                        try:
                            occlude_value = int(mapping[cam_anno["occluded"]])
                            if occlude_value < self.occlusion_threshold:
                                if_visible = True
                                break
                        except BaseException:
                            continue

        return if_visible

    def _get_single_anno(self, anno, trans_mat, trans_mat_mask):
        # load 3d box annotation
        box_anno = self._load_single_box(anno)
        if self.with_plain_velocity:
            velocity = self.get_velocity(anno)
            velocity = (
                self.points_trans(velocity, np.linalg.inv(trans_mat) * trans_mat_mask)
                if self.label_key == "pre_labels"
                else velocity
            )
            box_anno = np.concatenate((box_anno, velocity[:2]))
        # load category annotation, considering occlusion or outliers
        cat_anno = anno["category"]
        track_id = anno.get("track_id", -2)
        if track_id == -2:
            return None
        # if cat_anno in ["闪光柱", "锥桶", "防撞桶"]:
        #     print(cat_anno)
        if self.occlusion_threshold > 0 and not self._get_occlusion_attr(anno, self.camera_keys):
            if self.with_occlusion and cat_anno in self.category_map:
                cat_anno = "遮挡"
            else:
                cat_anno = "蒙版"
        if self.filter_outlier_boxes and self._judge_whether_outlier_box(box_anno, cat_anno):
            cat_anno = "蒙版"
        if self.filter_short_track:
            if "track_length" in anno and anno["track_length"] < 2:
                cat_anno = "蒙版"
        category = self.category_map[cat_anno] if cat_anno in self.category_map else "other"
        # load num_lidar_pt
        num_lidar_info = anno["num_lidar_pts"] if "num_lidar_pts" in anno else 20
        return box_anno, category, num_lidar_info, track_id

    def __getitem__(self, idx):
        data_dict = dict()
        if self.mode != "train":
            gt_labels = np.zeros((0,), dtype=np.float32)
            gt_boxes = np.zeros((0, 7), dtype=np.float32)
            data_dict["gt_labels"] = gt_labels
            data_dict["gt_boxes"] = gt_boxes

        result = {}
        boxes = []
        cats = []
        num_lidar_pts = []
        track_ids = []
        frame_data_list = self.loader_output["frame_data_list"]
        frame = self.loader_output["frame_data_list"][idx]
        cummulative_sizes = self.loader_output["frame_data_list"].cummulative_sizes
        scene_id = bisect.bisect_right(cummulative_sizes, idx)
        if self.label_key not in frame_data_list[idx]:
            return None

        if (
            "front_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["front_lidar"]
        ):
            sensor_key = "front_lidar"
        elif (
            "middle_lidar" in frame["sensor_data"].keys()
            and "lidar_gnss" in self.loader_output["calibrated_sensors"][scene_id]["middle_lidar"]
        ):
            sensor_key = "middle_lidar"
        else:
            return None
        trans_mat = self.get_lidar_to_world(scene_id, sensor_key, frame).astype(np.float64)
        trans_mat_mask = np.ones_like(trans_mat, dtype=np.float64)
        trans_mat_mask[:3, 3] = 0

        annos = frame_data_list[idx][self.label_key]
        for anno in annos:
            flags = self._get_single_anno(anno, trans_mat, trans_mat_mask)
            if flags is None:
                return None
            box_anno, category, num_lidar_info, track_id = flags
            boxes.append(box_anno)
            cats.append(category)
            num_lidar_pts.append(num_lidar_info)
            track_ids.append(track_id)

        gt_boxes = np.stack(boxes) if len(boxes) > 0 else boxes
        result["gt_boxes"] = np.array(gt_boxes, dtype=np.float32)
        gt_labels = np.stack(cats) if len(cats) > 0 else cats

        new_gt_labels = []
        for i in gt_labels:
            if i in self.class_names:
                new_gt_labels.append(self.class_names.index(i))
            elif i == "occlusion":
                new_gt_labels.append(-2)
            else:
                new_gt_labels.append(-1)
        if np.all(np.isin(new_gt_labels, [-2, -1])):
            # print("empty gt")
            return None
        result["gt_labels"] = np.array(new_gt_labels, dtype=np.int64)

        # result["gt_labels"] = np.array(
        #     [self.class_names.index(i) if i in self.class_names else -1 for i in gt_labels], dtype=np.int64
        # )
        result["labels"] = np.stack(cats) if len(cats) > 0 else cats  # for evaluation
        num_lidar_points = np.stack(num_lidar_pts) if len(boxes) > 0 else num_lidar_pts
        result["num_lidar_points"] = np.array(num_lidar_points, dtype=np.float32)
        result["instance_inds"] = np.array(track_ids, dtype=np.int64)

        if self.filter_outlier_boxes and outlier_filter_god(  # TODO
            gt_boxes=result["gt_boxes"], gt_labels=result["gt_labels"], class_names=self.class_names
        ):
            print("ffffffff")
            return None

        if self.filter_empty_frames and non_gt_filter(
            gt_boxes=result["gt_boxes"], gt_labels=result["gt_labels"], roi_range=self.roi_range
        ):
            return None

        data_dict["gt_labels"] = result["gt_labels"]
        data_dict["gt_boxes"] = result["gt_boxes"]
        data_dict["instance_inds"] = result["instance_inds"]

        return data_dict
