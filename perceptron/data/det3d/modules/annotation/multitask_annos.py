import matplotlib
import numpy as np


from perceptron.data.det3d.modules.annotation.base import AnnotationDet


class AnnotationDetLidarOnly(AnnotationDet):
    def __init__(self, fov_convexthull, **kwargs) -> None:
        super(AnnotationDetLidarOnly, self).__init__(**kwargs)
        self.fov_convexthull = np.array(fov_convexthull)
        self.path = matplotlib.path.Path(fov_convexthull)
        # FIXME: 暂未解决多机中某个task确实导致的问题，不可使用
        raise NotImplementedError(
            "AnnotationDetLidarOnly is not implemented for multi-machine training. " "Please use AnnotationDet instead."
        )

    def filter_fov_convexthull(self, task_annos):
        """
        Returns:
            True if any of the boxes in task_annos are within the fov convex hull.
            False if no boxes are within the fov convex hull.
        """
        boxes = task_annos["gt_boxes"]
        labels = task_annos["gt_labels"]
        boxes = boxes[labels != -1]
        if len(boxes) == 0:
            return False
        points = boxes[:, :2]

        mask = np.ones(len(boxes)).astype(np.bool)
        for idx, point in enumerate(points):
            mask[idx] = self.path.contains_point(point)
        if mask.any():
            return True
        else:
            return False

    def __getitem__(self, idx):
        data_dict = super().__getitem__(idx)
        if data_dict is None:
            return None
        if not self.filter_fov_convexthull(data_dict):
            return None
        return data_dict


# "MultitaskAnnotations is not implemented for multi-machine training. "
# "Please use E2EAnnotations instead."

# class MultitaskAnnotations(E2EAnnotations):

#     def __getitem__(self, index):

#         data_dict = dict()
#         task_list = []
#         for task, ann_reader in self.tasks.items():
#             result = ann_reader[index]
#             if result is None:
#                 continue
#             if self.sub_level_annos:
#                 data_dict[task] = result
#             else:
#                 data_dict.update(result)
#             task_list.append(task)
#         data_dict["task_list"] = task_list
#         if len(task_list) == 0:
#             return None
#         return data_dict

#     def pre_check_not_skip(self, index: int) -> bool:
#         """check anno / sensor valid, return True if all valid, False if some is not valid."""
#         for task, ann_reader in self.tasks.items():
#             if not ann_reader.pre_check_not_skip(index):
#                 return False
#         return True
