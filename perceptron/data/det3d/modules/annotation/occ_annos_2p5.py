import numpy as np
from perceptron.data.det3d.modules.annotation.base import AnnotationBase
from refile import smart_open
import os
import h5py
from loguru import logger
import nori2 as nori
import io

CLS_MAP = {
    "unknow": 0,
    "freespace": 1,
    "dynamic": 2,
    "static": 3,
    "car": 5,  # 类别5 car，黄色
    "larger_vehicle": 6,  # 类别6 larger_vehicle，品红色
    "bicycle": 7,  # 类别7 bicycle，青色
    "pedestrain": 8,  # 类别8 pedestrain，蓝紫色
}


def process_3d_to_2p5(arr_ori):
    arr = arr_ori[..., ::-1]  # 翻转Z轴
    non_empty_dummy = (arr != CLS_MAP["unknow"]).astype(np.uint8)
    z_idx_dummy = np.argmax(non_empty_dummy, axis=-1, keepdims=True)
    freespace = np.take_along_axis(arr, z_idx_dummy, axis=-1).squeeze(-1)
    return freespace


class AnnotationFreespace(AnnotationBase):
    def __init__(self, loader_output, mode, label_mapping, cross_mask=False) -> None:
        super().__init__(loader_output, mode)
        self.label_mapping = label_mapping
        self.vfunc = np.vectorize(self.map_labels)
        self.cross_mask = cross_mask
        # print(f'cross_mask {cross_mask}')

    # 创建一个函数，该函数返回给定键在字典中对应的值
    def map_labels(self, x):
        return self.label_mapping[x]

    def load_h5_file(self, path):
        """从 H5 文件中读取所需字段。"""
        with smart_open(path, "rb") as rf:
            with h5py.File(rf, "r") as h5f:
                return (h5f["target"][...], h5f["cam_mask"][...], h5f["lidar_mask"][...], h5f["vis_2d_mask"][...])

    def load_h5_file_occ(self, path):
        """从 H5 文件中读取所需字段。"""
        with smart_open(path, "rb") as rf:
            with h5py.File(rf, "r") as h5f:
                return (h5f["target"][...], h5f["cam_mask"][...], h5f["lidar_mask"][...])

    def load_h5_nori_file(self, data):
        with h5py.File(io.BytesIO(data), "r") as f2:
            return (f2["target"][...], f2["cam_mask"][...], f2["lidar_mask"][...], f2["vis_2d_mask"][...])

    def load_h5_nori_file_occ(self, data):
        with h5py.File(io.BytesIO(data), "r") as f2:
            return (f2["target"][...], f2["cam_mask"][...], f2["lidar_mask"][...])

    def process_label(self, occ_gt_layer, cam_mask_2d, lidar_mask_2d, vis_2d_mask, clip_info):
        # Vis Mask
        freespace_modal_mask = np.logical_or(cam_mask_2d, lidar_mask_2d)
        freespace_dynamic = occ_gt_layer == 2  # 只适合动态单分类
        freespace_target = np.logical_or(freespace_dynamic, freespace_modal_mask)
        freespace_mask = np.logical_or(freespace_target, vis_2d_mask)
        if os.environ.get("TRUNCATED_MASK", False):
            freespace_mask[:, :680] = True

        occ_gt_layer = np.where(occ_gt_layer < 29, occ_gt_layer, 255)

        occ_gt_layer = self.vfunc(occ_gt_layer)
        if self.cross_mask and clip_info.get("freespace_ignore", False):
            occ_gt_layer[occ_gt_layer == 0] = 255
        return occ_gt_layer

    def __getitem__(self, idx):
        res_dict = {}
        # if self.mode == "infer":
        if os.environ.get("INFER", False):
            res_dict["semantic"] = np.zeros((0, 0), dtype=np.int64)
            res_dict["semantic_mask"] = np.zeros((0, 0), dtype=np.int64)
            return res_dict

        frame_data_list = self.loader_output["frame_data_list"]
        clip_info = frame_data_list[idx]
        freespace_path = clip_info["freespace_path"]

        if "freespace" in clip_info:
            nori_info = clip_info["freespace"]
            nori_id = nori_info["nori_id"]
            vid = int(nori_id.split(",")[0])
            nori_path = nori_info["nori_path"].replace("s3://", "/mnt/acceldata/dynamic/")
            if not os.path.exists(nori_path):  # accelerdata
                self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace Nori File Not Accelerated")
                logger.warning(f"{nori_path} not exist in gpfs.")
                if os.getenv("OSS", "False") == "True":
                    nori_path = nori_info["nori_path"]
                else:
                    return None
            try:
                vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                data = vreader.get(nori_id)
                freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_nori_file(data)
            except Exception as e:
                self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace Nori File Read Error")
                logger.warning(f"Error reading freespace nori file {nori_path}: {e}")
                return None

        else:
            # 替换 occ_dump 版本
            if not freespace_path.endswith(".h5"):
                self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace File Not H5")
                logger.warning(f"No h5 Freespace File Found: {freespace_path}")
                return None
            # 判断 gpfs 路径优先
            gpfs_path = freespace_path.replace("s3://", "/mnt/acceldata/dynamic/")
            if os.path.exists(gpfs_path):
                try:
                    freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_file(gpfs_path)
                except Exception as e:
                    self.loader_output["frame_data_list"].cal_useless_data(idx, "H5 Freespace GPFS Filepath Read Error")
                    logger.warning(f"Error reading freespace h5 file {gpfs_path}: {e}")
                    return None
            else:
                self.loader_output["frame_data_list"].cal_useless_data(idx, "H5 Freespace File Not Accelerated")
                if os.getenv("OSS", "False") == "True":
                    try:
                        freespace_gt, cam_mask_2d, lidar_mask_2d, vis_2d_mask = self.load_h5_file(freespace_path)
                    except Exception as e:
                        self.loader_output["frame_data_list"].cal_useless_data(idx, "H5 Freespace Filepath Read Error")
                        logger.warning(f"Error reading freespace h5 file {freespace_path}: {e}")
                        return None
                else:
                    return None

        # Vis Mask
        freespace_modal_mask = np.logical_or(cam_mask_2d, lidar_mask_2d)
        freespace_dynamic = freespace_gt == 2  # 只适合动态单分类
        freespace_target = np.logical_or(freespace_dynamic, freespace_modal_mask)
        freespace_mask = np.logical_or(freespace_target, vis_2d_mask)
        if os.environ.get("TRUNCATED_MASK", False):
            freespace_mask[:, :680] = True

        freespace_gt = np.where(freespace_gt < 29, freespace_gt, 255)

        freespace_gt = self.vfunc(freespace_gt)
        if self.cross_mask and clip_info.get("freespace_ignore", False):
            freespace_gt[freespace_gt == 0] = 255

        # res_dict["semantic"] = freespace_gt
        # res_dict["semantic_mask"] = freespace_mask

        # ------------------- load occ 3d -----------------------
        occ_path = clip_info["occ_path"]

        if "occ" in clip_info:
            nori_info = clip_info["occ"]
            nori_id = nori_info["nori_id"]
            vid = int(nori_id.split(",")[0])
            nori_path = nori_info["nori_path"].replace("s3://", "/mnt/acceldata/dynamic/")
            if not os.path.exists(nori_path):  # accelerdata
                self.loader_output["frame_data_list"].cal_useless_data(idx, "OCC Nori File Not Accelerated")
                logger.warning(f"{nori_path} not exist in gpfs.")
                if os.getenv("OSS", "False") == "True":
                    nori_path = nori_info["nori_path"]
                else:
                    return None
            try:
                vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                data = vreader.get(nori_id)
                occ_gt, cam_mask_3d, lidar_mask_3d = self.load_h5_nori_file_occ(data)
            except Exception as e:
                self.loader_output["frame_data_list"].cal_useless_data(idx, "OCC Nori File Read Error")
                logger.warning(f"Error reading OCC nori file {nori_path}: {e}")
                return None

        else:
            # 替换 occ_dump 版本
            if not occ_path.endswith(".h5"):
                self.loader_output["frame_data_list"].cal_useless_data(idx, "Freespace File Not H5")
                logger.warning(f"No h5 Freespace File Found: {occ_path}")
                return None
            # 判断 gpfs 路径优先
            gpfs_path = occ_path.replace("s3://", "/mnt/acceldata/dynamic/")
            if os.path.exists(gpfs_path):
                try:
                    occ_gt, cam_mask_3d, lidar_mask_3d = self.load_h5_file_occ(gpfs_path)
                except Exception as e:
                    self.loader_output["frame_data_list"].cal_useless_data(idx, "H5 Freespace GPFS Filepath Read Error")
                    logger.warning(f"Error reading freespace h5 file {gpfs_path}: {e}")
                    return None
            else:
                self.loader_output["frame_data_list"].cal_useless_data(idx, "H5 Freespace File Not Accelerated")
                if os.getenv("OSS", "False") == "True":
                    try:
                        occ_gt, cam_mask_3d, lidar_mask_3d = self.load_h5_file_occ(occ_path)
                    except Exception as e:
                        self.loader_output["frame_data_list"].cal_useless_data(idx, "H5 Freespace Filepath Read Error")
                        logger.warning(f"Error reading freespace h5 file {occ_path}: {e}")
                        return None
                else:
                    return None

        height_limit_idx_1 = int((0.8 - (-3.4)) / 0.1)  # ori: 1 -xxxx
        height_filted_data_1 = occ_gt[..., :height_limit_idx_1]
        occ_2p5_layer_1 = process_3d_to_2p5(height_filted_data_1)

        occ_2p5_layer_1 = self.process_label(occ_2p5_layer_1, cam_mask_2d, lidar_mask_2d, vis_2d_mask, clip_info)

        res_dict["semantic"] = occ_2p5_layer_1
        res_dict["semantic_mask"] = freespace_mask

        return res_dict
