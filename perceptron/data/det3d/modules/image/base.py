import random
from collections import defaultdict
import cv2
import nori2 as nori
import numpy as np
import hashlib
import msgpack
from loguru import logger
from tqdm import tqdm
import refile
from refile import smart_open
from perceptron.data.det3d.utils.local_cache import LocalCachedDict
from perceptron.data.det3d.utils.functional import trans_oss_to_gpfs_path
from perceptron.data.det3d.modules.utils.distributed import is_master
from perceptron.data.det3d.utils.functional import camera_filter, get_lidar_to_pixel
from perceptron.utils import torch_dist
from perceptron.utils.env import get_cluster

from .undistort import UndistortBase, UndistortStandard, UndistortWarp


class ImageBase:
    """
    The transformation for image undistort is generate by calibration parameters in initialisation, and decode the original image from the nori address, which is used as the input for the subsequent pipeline.
    The image output in this stage is a list sorted by camera_names, regardless of whether the image sizes of different cameras are the same or not.

    Options:
        undistort: Switch of all undistortion related functions

        target_resolution: Determine the size of the undistorted image

        undistort_func: Calculate the transformation relations from the calibration parameters. SimFov is used to project the physical camera to a hypothetical standard camera with the same intrinsics

    """

    def __init__(
        self,
        car,
        camera_names,
        loader_output,
        mode,
        undistort=True,
        undistort_func=UndistortStandard,
        target_resolution=(1920, 1080),
        target_extrinsic=None,
        cam120_scale=1.0,  # must be set for new data after 20250303
    ) -> None:
        self.car = car
        self.camera_names = camera_names
        self.loader_output = loader_output
        self.mode = mode
        self.nori_fetcher = None
        self.undistort = undistort
        self.target_resolution = target_resolution  # W, H
        # add scale for map
        self.cam120_scale = cam120_scale

        self.calibrated_sensors = self.loader_output["calibrated_sensors"]
        self.camera_name_mapping_s2h = self.loader_output["camera_name_mapping"]["standard"]
        self.raw_names = [self.camera_name_mapping_s2h[k]["hidden_name"] for k in self.camera_names]

        self.local_map_cache = LocalCachedDict("/data/cache/undistort_cache", key="undistort_cache")
        if self.undistort:
            if isinstance(undistort_func, dict):
                assert set(undistort_func.keys()) == set(camera_names), "existing camera without undistortion function."
                for _, func_ in undistort_func.items():
                    assert issubclass(func_[0], UndistortBase), "invalid undistort function input."
                self.undistort_func = undistort_func
            elif issubclass(undistort_func[0], UndistortBase):
                self.undistort_func = {camera_name: undistort_func for camera_name in self.camera_names}
            else:
                raise ("invalid undistort function format.")

            self._init_camera_undistort_mapping()
            self.init_camera_undistort_flag = True
        else:
            self._init_camera_translation_matrix()

        self.nori_available = get_cluster() == "https://hh-d.brainpp.cn"

        if target_extrinsic is not None:
            self.target_extrinsic = dict(np.load(smart_open(target_extrinsic, "rb")))

        else:
            self.target_extrinsic = None

    def _init_sensors_info(self):
        pass

    def _init_camera_translation_matrix(self):
        """用于预处理了去畸变的图像"""
        camera_info_cache = {
            "calib_param_to_index": defaultdict(int),
            "new_k": [],
            "lidar_to_pix": [],
        }
        self._init_sensors_info()
        for json_idx, sensors_info in tqdm(
            self.loader_output["calibrated_sensors"].items(),
            disable=(not is_master()),
            desc="[Init lidar to pixel translation]",
        ):
            for camera_name in self.camera_names:
                hidden_name = self._get_hidden_name(camera_name)
                sensor_info = sensors_info[hidden_name]

                if str(sensor_info) in camera_info_cache["calib_param_to_index"]:
                    map_id = camera_info_cache["calib_param_to_index"][str(sensor_info)]
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    cur_cam_info["intrinsic"]["K"] = camera_info_cache["new_k"][map_id]
                    cur_cam_info["T_lidar_to_pixel"] = camera_info_cache["lidar_to_pix"][map_id]
                else:
                    cache_idx = len(camera_info_cache["lidar_to_pix"])
                    camera_info_cache["calib_param_to_index"][str(sensor_info)] = cache_idx
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]

                    intrinsic_k = np.array(cur_cam_info["intrinsic"]["K"]).reshape(3, 3)
                    lidar2pix = get_lidar_to_pixel(sensor_info, intrinsic_k)

                    cur_cam_info["T_lidar_to_pixel"] = lidar2pix

                    camera_info_cache["new_k"].append(cur_cam_info["intrinsic"]["K"])
                    camera_info_cache["lidar_to_pix"].append(lidar2pix)

    def _get_hidden_name(self, camera_name):
        hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
        return hidden_name

    def resize_and_crop(self, img, target_w, target_h, interpolation=cv2.INTER_LINEAR):
        ori_h, ori_w, channels = img.shape

        scale = target_w / ori_w
        new_h = int(ori_h * scale)

        img_resized = cv2.resize(img, (target_w, new_h), interpolation=interpolation)

        if new_h > target_h:
            img_cropped = img_resized[:target_h, :]
        else:
            padding = target_h - new_h
            img_cropped = np.concatenate((img_resized, np.zeros((padding, target_w, channels), dtype=np.uint8)), axis=0)

        return img_cropped

    def _init_camera_undistort_mapping(self):
        camera_info_cache = {
            "calib_param_to_index": defaultdict(int),
            "new_k": [],
            "undistort_map": [],
            "lidar_to_pix": [],
        }

        self._init_sensors_info()

        for json_idx, sensors_info in tqdm(
            self.loader_output["calibrated_sensors"].items(), disable=(not is_master()), desc="[Init Undistort Maps]"
        ):

            for camera_name in self.camera_names:
                hidden_name = self._get_hidden_name(camera_name)
                sensor_info = sensors_info[hidden_name]

                # if str(sensor_info) in camera_info_cache["calib_param_to_index"]:
                # 因为所有的value在新的数据去重机制下，都是同一个内存地址，因此选择改用id来判重
                if id(sensor_info) in camera_info_cache["calib_param_to_index"]:
                    map_id = camera_info_cache["calib_param_to_index"][id(sensor_info)]
                    cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                    cur_cam_info["intrinsic"]["distortion_model"] = "undistorted"
                    cur_cam_info["intrinsic"]["K"] = camera_info_cache["new_k"][map_id]
                    cur_cam_info["T_lidar_to_pixel"] = camera_info_cache["lidar_to_pix"][map_id]
                    cur_cam_info["distortion_maps"] = camera_info_cache["undistort_map"][map_id]
                    cur_cam_info["intrinsic"]["target_resolution"] = self.target_resolution
                else:
                    local_key = hashlib.md5(msgpack.dumps(sensor_info)).hexdigest()
                    if issubclass(self.undistort_func[camera_name][0], UndistortWarp):
                        target_extrinsic = self.undistort_func[camera_name][1]["target_extrinsic"][hidden_name]
                        local_key += hashlib.md5(msgpack.dumps(target_extrinsic)).hexdigest()
                        # 跨车warp时去畸变map与target_extrinsic有关，用于不同车型的模型可能为dataset设置不同的target_extrinsic，
                        # 因此需为local_key做出可区分的hash作为标识

                    if local_key not in self.local_map_cache:
                        func_ = self.undistort_func[camera_name]
                        if len(func_) > 1:
                            undistort_instance = func_[0](**func_[1])
                        else:
                            undistort_instance = func_[0]()
                        if camera_name == "cam_front_120":
                            new_k, undistort_map = undistort_instance(
                                sensor_name=camera_name,
                                sensor=self.camera_name_mapping_s2h[camera_name],
                                camera_calib=sensor_info,
                                camera_dim_target=self.target_resolution,
                                cam_scale=self.cam120_scale,
                            )
                        else:
                            new_k, undistort_map = undistort_instance(
                                sensor_name=camera_name,
                                sensor=self.camera_name_mapping_s2h[camera_name],
                                camera_calib=sensor_info,
                                camera_dim_target=self.target_resolution,
                            )

                        cache_idx = len(camera_info_cache["undistort_map"])
                        camera_info_cache["calib_param_to_index"][id(sensor_info)] = cache_idx
                        cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]

                        # TODO：应该取消 if-else 判断，在 UndistortWarp 给出正确的外参
                        if isinstance(undistort_instance, UndistortWarp):
                            lidar2pix = undistort_instance.target_extrinsic[camera_name][:-1, :].tolist()
                        else:
                            lidar2pix = get_lidar_to_pixel(sensor_info, new_k)
                        cur_cam_info["T_lidar_to_pixel"] = lidar2pix
                        new_k = new_k.tolist()  # new_k.flatten().tolist()
                        cur_cam_info["intrinsic"]["distortion_model"] = "undistorted"
                        cur_cam_info["intrinsic"]["K"] = new_k
                        camera_info_cache["new_k"].append(new_k)

                        cur_cam_info["intrinsic"]["target_resolution"] = self.target_resolution
                        camera_info_cache["lidar_to_pix"].append(lidar2pix)

                        self.local_map_cache[local_key] = undistort_map

                        cur_cam_info["distortion_maps"] = local_key
                    else:
                        func_ = self.undistort_func[camera_name]
                        if len(func_) > 1:
                            undistort_instance = func_[0](**func_[1])
                        else:
                            undistort_instance = func_[0]()
                        if camera_name == "cam_front_120":
                            new_k = undistort_instance.get_new_extrinsic(
                                sensor_name=camera_name,
                                camera_calib=sensor_info,
                                camera_dim_target=self.target_resolution,
                                cam_scale=self.cam120_scale,
                            )
                        else:
                            new_k = undistort_instance.get_new_extrinsic(
                                sensor_name=camera_name,
                                camera_calib=sensor_info,
                                camera_dim_target=self.target_resolution,
                            )
                        cur_cam_info = self.calibrated_sensors[json_idx][hidden_name]
                        if isinstance(undistort_instance, UndistortWarp):
                            lidar2pix = undistort_instance.target_extrinsic[camera_name][:-1, :].tolist()
                        else:
                            lidar2pix = get_lidar_to_pixel(sensor_info, new_k)
                        cur_cam_info["T_lidar_to_pixel"] = lidar2pix
                        cur_cam_info["intrinsic"]["target_resolution"] = self.target_resolution

                        cache_idx = len(camera_info_cache["undistort_map"])
                        camera_info_cache["calib_param_to_index"][id(sensor_info)] = cache_idx
                        camera_info_cache["new_k"].append(new_k)
                        camera_info_cache["lidar_to_pix"].append(lidar2pix)
                        cur_cam_info["distortion_maps"] = local_key
                    camera_info_cache["undistort_map"].append(local_key)

    @staticmethod
    def _able_to_get_image(name, sensor_data):
        is_distributed = torch_dist.is_distributed()
        local_rank = 0
        if is_distributed:
            local_rank = torch_dist.get_rank()

        Flag = True
        if name not in sensor_data:
            if local_rank == 0:
                logger.warning(f"{name} is not in {sensor_data.keys()}")
            Flag = False
        elif sensor_data[name] is None:
            if local_rank == 0:
                logger.warning(f"{name} in {sensor_data.keys()}, but has no info")
            Flag = False
        elif sensor_data[name]["nori_id"] is None:
            if local_rank == 0:
                logger.warning(f"The Nori id of {name} is None")
            Flag = False
        return Flag

    def get_images(self, idx, data_dict):
        """
        Loading image with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()

        imgs, lidar2imgs = [], []
        sensors_info = self.calibrated_sensors[self.loader_output["calibrated_sensors_id"][idx]]
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]

        use_qy_nori = getattr(self.loader_output["frame_data_list"], "has_nori", False)
        if use_qy_nori:
            # 新的nori生成环节，所有sensor共用同一个vid
            hidden_name = self._get_hidden_name(self.camera_names[0])
            nori_img_id = sensor_data[hidden_name]["nori_id"]
            vid = int(nori_img_id.split(",")[0])
            nori_path = trans_oss_to_gpfs_path(sensor_data[hidden_name]["nori_path"])
            vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)

        if camera_filter(self.raw_names, sensor_data):
            # self.loader_output["frame_data_list"].cal_useless_data(idx, "camera_filter", "imagebase") # TODO: +map后卡住！
            return None

        map1s, map2s = [], []
        for camera_name in self.camera_names:
            hidden_name = self._get_hidden_name(camera_name)
            if use_qy_nori:
                nori_img_id = sensor_data[hidden_name]["nori_id"]
                img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
            elif "file_path" in sensor_data[hidden_name]:
                try:
                    with smart_open(trans_oss_to_gpfs_path(sensor_data[hidden_name]["file_path"]), "rb") as f:
                        img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                except Exception:
                    print(hidden_name, sensor_data[hidden_name]["file_path"], "img not found")
                    return None
            elif "nori_id" in sensor_data[hidden_name]:
                nori_img_id = sensor_data[hidden_name]["nori_id"]
                try:
                    if self.nori_available:
                        img = cv2.imdecode(np.frombuffer(self.nori_fetcher.get(nori_img_id), dtype=np.uint8), 1)
                    else:
                        vid = int(nori_img_id.split(",")[0])
                        nori_path = trans_oss_to_gpfs_path(sensor_data[hidden_name]["nori_path"])
                        # if not os.path.exists(nori_path):
                        #     nori_path = sensor_data[hidden_name]["nori_path"]
                        vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                        img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
                except Exception:
                    print(sensor_data[hidden_name], hidden_name, "not found")
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "use img nori-id failed in {}".format(hidden_name), "imagebase"
                    )
                    return None
            else:
                with smart_open(sensor_data[hidden_name]["s3_path"], "rb") as f:
                    img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)

            if self.mode == "train":
                interpolation = random.choice(
                    [cv2.INTER_AREA, cv2.INTER_LINEAR, cv2.INTER_NEAREST, cv2.INTER_CUBIC, cv2.INTER_LANCZOS4]
                )
            else:
                interpolation = cv2.INTER_LINEAR

            if self.target_resolution[0] is not None:
                target_w, target_h = self.target_resolution
                img = self.resize_and_crop(img, target_w, target_h, interpolation)

            if self.undistort:
                image_resolution = self.target_resolution
                maps_key = f"{sensors_info[hidden_name]['distortion_maps']}.{image_resolution}"
                map1, map2 = self.local_map_cache[maps_key]
                map1s.append(map1)
                map2s.append(map2)

            T_lidar_to_pixel = sensors_info[hidden_name]["T_lidar_to_pixel"]
            lidar2img = np.concatenate([np.array(T_lidar_to_pixel), np.array([[0.0, 0.0, 0.0, 1.0]])])
            assert lidar2img is not None, "Incorrect camera name in image info"

            imgs.append(img)
            lidar2imgs.append(np.array(lidar2img, dtype=np.float32))

        data_dict["imgs"] = imgs
        data_dict["lidar2imgs"] = np.stack(lidar2imgs)
        if self.undistort:
            data_dict["map1s"] = map1s
            data_dict["map2s"] = map2s

        return data_dict

    def pre_check_not_skip(self, index: int) -> bool:
        """check anno / sensor valid, return True if all valid, False if some is not valid."""
        sensor_data = self.loader_output["frame_data_list"][index]["sensor_data"]
        # FIXME: 增加use_qy_nori的判断逻辑
        use_qy_nori = getattr(self.loader_output["frame_data_list"], "has_nori", False)  # noqa

        if camera_filter(self.raw_names, sensor_data):
            return False
        for camera_name in self.camera_names:
            hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
            tmp_filepath = None
            if "file_path" in sensor_data[hidden_name]:
                tmp_filepath = trans_oss_to_gpfs_path(sensor_data[hidden_name]["file_path"])
            elif "nori_id" in sensor_data[hidden_name]:

                if self.nori_available:
                    continue
                else:
                    nori_path = trans_oss_to_gpfs_path(sensor_data[hidden_name]["nori_path"])
                    tmp_filepath = nori_path
            else:
                tmp_filepath = sensor_data[hidden_name]["s3_path"]
            if not refile.smart_exists(tmp_filepath):
                return False
        return True
