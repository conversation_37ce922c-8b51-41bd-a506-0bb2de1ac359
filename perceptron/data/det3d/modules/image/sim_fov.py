import random
from copy import deepcopy
import cv2
import nori2 as nori
import numpy as np
import torch
from pyquaternion import Quaternion
from refile import smart_open


from perceptron.data.det3d.utils.functional import camera_filter
from perceptron.data.det3d.utils.functional import trans_oss_to_gpfs_path

from .base import ImageBase
from loguru import logger


class SensorCalibrationInterface(object):
    """
    copied from perceptron.data.map3d.bezier file
    """

    def __init__(self, calibrated_sensors):
        self.calibrated_sensors = calibrated_sensors

    def get_lidar2ego_trans(self, inverse=False):
        if "lidar2ego" in self.calibrated_sensors:
            lidar2ego_params = self.calibrated_sensors["lidar2ego"].get(
                "transform", self.calibrated_sensors["lidar2ego"]
            )
        else:
            lidar2ego_params = self.calibrated_sensors["lidar_ego"]["extrinsic"]["transform"]
        translation = np.array(list(lidar2ego_params["translation"].values()))
        rotation = Quaternion(list(lidar2ego_params["rotation"].values()))
        trans = self.transform_matrix(translation, rotation, inverse=inverse)
        return trans

    def get_lidar2cam_trans(self, img_key, inverse=False):
        lidar2cam_params = self.calibrated_sensors[img_key]["extrinsic"].get(
            "transform", self.calibrated_sensors[img_key]["extrinsic"]
        )
        translation = np.array(list(lidar2cam_params["translation"].values()))
        rotation = Quaternion(list(lidar2cam_params["rotation"].values()))
        trans_lidar2cam = self.transform_matrix(translation, rotation, inverse=inverse)
        return trans_lidar2cam

    def get_ego2cam_trans(self, img_key, inverse=False):
        if not inverse:
            trans_ego2lidar = self.get_lidar2ego_trans(inverse=True)
            trans_lidar2cam = self.get_lidar2cam_trans(img_key, inverse=False)
            return trans_lidar2cam @ trans_ego2lidar  # 可以把 ego 坐标系下的点转换到 cam 坐标系下
        else:
            trans_lidar2ego = self.get_lidar2ego_trans(inverse=False)
            trans_cam2lidar = self.get_lidar2cam_trans(img_key, inverse=True)
            return trans_lidar2ego @ trans_cam2lidar

    def get_distortion_status(self, img_key):
        return self.calibrated_sensors[img_key]["intrinsic"]["distortion_model"]

    def get_camera_intrinsic(self, img_key):
        cam_intrinsic_params = self.calibrated_sensors[img_key]["intrinsic"]
        _K = np.array(cam_intrinsic_params["K"]).reshape(3, 3)
        _D = np.array(cam_intrinsic_params["D"])
        mode = np.array(cam_intrinsic_params["distortion_model"])
        return _K, _D, mode

    def get_cam2img_trans(self, img_key):
        return self.get_camera_intrinsic(img_key)[0]

    @staticmethod
    def transform_matrix(translation, rotation, inverse=False):
        trans_mat = np.eye(4)
        if not inverse:
            trans_mat[:3, :3] = rotation.rotation_matrix
            trans_mat[:3, 3] = np.transpose(np.array(translation))
        else:
            trans_mat[:3, :3] = rotation.rotation_matrix.T
            trans_mat[:3, 3] = trans_mat[:3, :3].dot(np.transpose(-np.array(translation)))
        return trans_mat

    def get_cam_resolution(self, img_key):
        w, h = self.calibrated_sensors[img_key]["intrinsic"]["resolution"]  # resolution一定要是w,h
        return np.array([w, h])  # (2, )

    def project_xyz2uv(self, xyz_pts, img_key, return_format="tensor"):
        """
        :param xyz_pts: (N, 3)
        :param img_key: str
        :param return_format:
        :return:
        """
        assert isinstance(xyz_pts, (np.ndarray, torch.Tensor))
        assert len(xyz_pts.shape) == 2 and xyz_pts.shape[-1] == 3
        assert return_format in ["numpy", "tensor"]
        # 通过内外参计算xyz投影到uv平面上的坐标
        xyz_pts = xyz_pts if isinstance(xyz_pts, np.ndarray) else xyz_pts.cpu().data.numpy()
        xyz_pts = np.concatenate([xyz_pts, np.ones(xyz_pts.shape[0])[:, None]], axis=-1)  # (N, 4)
        ego2cam_trans = self.get_ego2cam_trans(img_key, inverse=False)
        cam_pts = ego2cam_trans @ xyz_pts.T
        cam_pts = cam_pts[:3]
        cam2img_trans = self.get_cam2img_trans(img_key)
        uv_pts = cam2img_trans @ cam_pts
        uv_pts = uv_pts[:2, :] / uv_pts[2, :]
        uv_pts = uv_pts if return_format == "numpy" else torch.from_numpy(uv_pts)
        # 保证xyz投影到图像上的有效性
        w, h = self.calibrated_sensors[img_key]["intrinsic"]["resolution"]  # resolution一定要是w,h
        valid_indices_u = np.bitwise_and(uv_pts[0] >= 0, uv_pts[0] < w)
        valid_indices_v = np.bitwise_and(uv_pts[1] >= 0, uv_pts[1] < h)
        valid_indices_d = (cam_pts[2] > 0).astype(bool)
        valid_indices_uv = np.bitwise_and(valid_indices_u, valid_indices_v)
        valid_indices = np.bitwise_and(valid_indices_uv, valid_indices_d)
        return uv_pts.T, valid_indices


class ImageSimFov(ImageBase):
    def _init_sensors_info(self):
        """
        1. 相同的物理相机具有相同的标定参数，即 cam_front_30_sim_fov 和 cam_front_30 是同一物理相机，标定参数相同
        2. 相同的标定参数可以复用，所以只有 camera_name 中有 _sim_fov 后缀，才进入判断复用逻辑
        3. hidden_name 就是去掉 camera_name 字符串中的 _sim_fov 后缀
        4. 用 id(sensors_info[hidden_name]) 来判断内存地址是否一致
        """
        # 一方面，相同calibrated_sensors产生的sim_fov相同，可以复用，因此对sim_fov_cache作缓存，可以节省内存
        # 另一方面，perceptron/data/det3d/modules/image/base.py中会对calibrated_sensors下的key-value进行判重，因此需要保证相同的key-value对应的value是同一个内存地址
        sim_fov_cache = {}
        for json_idx, sensors_info in self.loader_output["calibrated_sensors"].items():
            try:
                for camera_name in self.camera_names:
                    hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
                    if "_sim_fov" in camera_name:
                        hidden_name_new = hidden_name + "_sim_fov" + camera_name.split("_sim_fov")[1]
                        if id(sensors_info[hidden_name]) not in sim_fov_cache:
                            sim_fov_cache[id(sensors_info[hidden_name])] = deepcopy(sensors_info[hidden_name])
                            sim_fov_cache[id(sensors_info[hidden_name])]["sensor_name"] = hidden_name_new
                        sensors_info[hidden_name_new] = sim_fov_cache[id(sensors_info[hidden_name])]
                    else:
                        sensors_info[hidden_name]["sensor_name"] = camera_name
            except Exception:
                continue

    def _get_hidden_name(self, camera_name):
        hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
        if "_sim_fov" in camera_name:
            hidden_name = hidden_name + "_sim_fov" + camera_name.split("_sim_fov")[1]
        return hidden_name

    def get_images(self, idx, data_dict):
        """
        Loading image and intrinsic mats with given sample index
        """
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        if self.undistort:
            if self.init_camera_undistort_flag is False:
                self._init_camera_undistort_mapping()
                self.init_camera_undistort_flag = True

        imgs, lidar2imgs, intrin_mats = [], [], []
        sensors_info = self.calibrated_sensors[self.loader_output["calibrated_sensors_id"][idx]]
        sensor_data = self.loader_output["frame_data_list"][idx]["sensor_data"]
        use_qy_nori = getattr(self.loader_output["frame_data_list"], "has_nori", False)
        if camera_filter(self.raw_names, sensor_data):
            # self.loader_output["frame_data_list"].cal_useless_data(idx, "camera_filter", "sim_fov")
            return None

        calibrator = SensorCalibrationInterface(sensors_info)
        trans_ego2cam, trans_cam2ego, trans_cam2img, trans_lidar2ego, resolution = [], [], [], [], []
        map1s, map2s = [], []
        for camera_name in self.camera_names:
            hidden_name = self.camera_name_mapping_s2h[camera_name]["hidden_name"]
            data_mode = data_dict["data_mode"]  # nori or jpg
            if data_mode == "nori" or use_qy_nori:
                if "cam_front_120" in sensor_data and "nori_id" in sensor_data["cam_front_120"].keys():
                    nori_id = sensor_data["cam_front_120"]["nori_id"]
                else:
                    nori_id = None

            if hidden_name not in sensor_data:
                logger.warning(f"hidden_name {hidden_name} not in sensors_info, camera_name: {camera_name}, idx: {idx}")
                return None
            if use_qy_nori and "nori_id" in sensor_data[hidden_name]:
                try:
                    nori_img_id = sensor_data[hidden_name]["nori_id"]
                    vid = int(nori_img_id.split(",")[0])
                    nori_path = trans_oss_to_gpfs_path(sensor_data[hidden_name]["nori_path"])
                    vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                    img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
                except Exception:
                    # print(sensor_data[hidden_name].keys(), " use_qy_nori failed")
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "use_qy_nori failed in {}".format(hidden_name), "sim_fov"
                    )
                    # raise e
                    return None
                if img is None:
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "Invalid Image File in use_qy_nori!", "sim_fov"
                    )
                    # logger.warning(f"{nori_path} Invalid Image File")
                    return None
            elif "file_path" in sensor_data[hidden_name]:
                nori_id = None
                file_path = trans_oss_to_gpfs_path(sensor_data[hidden_name]["file_path"])
                # if not os.path.exists(file_path):  # accelerdata
                #     file_path = sensor_data[hidden_name]["file_path"]
                try:
                    with smart_open(file_path, "rb") as f:
                        img = cv2.imdecode(np.frombuffer(f.read(), dtype=np.uint8), 1)
                except Exception:
                    # print(hidden_name, sensor_data[hidden_name]["file_path"], "img not found")
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "use file_path failed in {}".format(hidden_name), "sim_fov"
                    )
                    return None
                if img is None:
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "Invalid Image File in file_path!", "sim_fov"
                    )
                    # logger.warning(f"{nori_path} Invalid Image File")
                    return None
            elif "nori_id" in sensor_data[hidden_name]:
                nori_img_id = sensor_data[hidden_name]["nori_id"]
                nori_id = sensor_data[hidden_name]["nori_id"]
                try:
                    if self.nori_available:
                        img = cv2.imdecode(np.frombuffer(self.nori_fetcher.get(nori_img_id), dtype=np.uint8), 1)
                    else:
                        vid = int(nori_img_id.split(",")[0])
                        nori_path = trans_oss_to_gpfs_path(sensor_data[hidden_name]["nori_path"])
                        # if not os.path.exists(nori_path):  # accelerdata
                        #     nori_path = sensor_data[hidden_name]["nori_path"]
                        vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
                        img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
                except Exception:
                    # logger.debug(str(sensor_data[hidden_name]) + "not found")
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "use img nori-id failed in {}".format(hidden_name), "sim_fov"
                    )
                    return None
                if img is None:
                    self.loader_output["frame_data_list"].cal_useless_data(
                        idx, "Invalid Image File in nori_path!", "sim_fov"
                    )
                    # logger.warning(f"{nori_path} Invalid Image File")
                    return None
            else:
                with smart_open(sensor_data[hidden_name]["s3_path"], "rb") as f:
                    img_data = np.frombuffer(f.read(), dtype=np.uint8)
                img = cv2.imdecode(img_data, 1)
            hidden_name = self._get_hidden_name(camera_name)
            if self.mode == "train":
                interpolation = random.choice(
                    [cv2.INTER_AREA, cv2.INTER_LINEAR, cv2.INTER_NEAREST, cv2.INTER_CUBIC, cv2.INTER_LANCZOS4]
                )
            else:
                interpolation = cv2.INTER_LINEAR
            # FIXME:
            try:
                if self.undistort:
                    # print(camera_name, img.shape, sensors_info[hidden_name]["intrinsic"]["resolution"])
                    if img.shape[0] / img.shape[1] == self.target_resolution[1] / self.target_resolution[0]:
                        img = cv2.resize(img, self.target_resolution, interpolation=interpolation)
                    else:
                        target_w, target_h = self.target_resolution
                        img = self.resize_and_crop(img, target_w, target_h, interpolation)
                    image_resolution = self.target_resolution

                    maps_key = f"{sensors_info[hidden_name]['distortion_maps']}.{image_resolution}"
                    map1, map2 = self.local_map_cache[maps_key]
                    map1s.append(map1)
                    map2s.append(map2)
                else:
                    image_resolution = self.target_resolution
            except Exception:
                # print(e)
                self.loader_output["frame_data_list"].cal_useless_data(
                    idx, "undistort failed in {}".format(hidden_name), "sim_fov"
                )
                return None

            T_lidar_to_pixel = sensors_info[hidden_name]["T_lidar_to_pixel"]
            lidar2img = np.concatenate([np.array(T_lidar_to_pixel), np.array([[0.0, 0.0, 0.0, 1.0]])])
            assert lidar2img is not None, "Incorrect camera name in image info"
            intrin_mat = np.array(sensors_info[hidden_name]["intrinsic"]["K"]).reshape(3, 3)
            imgs.append(img)

            lidar2imgs.append(np.array(lidar2img, dtype=np.float32))
            intrin_mats.append(np.array(intrin_mat, dtype=np.float32))

            trans_ego2cam.append(calibrator.get_ego2cam_trans(hidden_name, inverse=False))
            trans_cam2ego.append(calibrator.get_ego2cam_trans(hidden_name, inverse=True))
            trans_cam2img.append(calibrator.get_cam2img_trans(hidden_name))
            trans_lidar2ego.append(calibrator.get_lidar2ego_trans(inverse=False))
            resolution.append(image_resolution)

        tran_mats_dict = dict(
            cam2img=np.stack(trans_cam2img),
            cam2ego=np.stack(trans_cam2ego),
            ego2cam=np.stack(trans_ego2cam),
            lidar2ego=np.stack(trans_lidar2ego),
            cam_resolution=np.stack(resolution),
        )
        data_dict["imgs"] = imgs
        data_dict["lidar2imgs"] = np.stack(lidar2imgs)
        data_dict["intrin_mats"] = np.stack(intrin_mats)
        data_dict["tran_mats_dict"] = tran_mats_dict
        if self.undistort:
            data_dict["map1s"] = map1s
            data_dict["map2s"] = map2s
        data_dict["nori_id"] = nori_id
        return data_dict
