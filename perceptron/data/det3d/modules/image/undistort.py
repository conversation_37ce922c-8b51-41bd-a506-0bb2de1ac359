import cv2
import numpy as np

from perceptron.data.det3d.source.base import Sensor
from perceptron.utils.file_io import load_pkl
from perceptron.utils.env import get_cluster
from perceptron.data.det3d.utils.functional import get_lidar_to_pixel


class UndistortBase:
    def __init__(self, use_new_intrisic=False, undistort_para={}):
        self.use_new_intrisic = use_new_intrisic
        # for sim fov
        site = get_cluster()
        if site == "https://hh-d.brainpp.cn":
            self.fovs_intrinsic_k_dict = load_pkl("s3://wangningzi-data/perceptron_files/fovs_intrinsic_k_dict.pkl")
        elif site == "https://qy.machdrive.cn":
            self.fovs_intrinsic_k_dict = load_pkl("s3://wangningzi-data-qy/perceptron_files/fovs_intrinsic_k_dict.pkl")
        else:
            raise NotImplementedError
        self.undistort_para = undistort_para

    @staticmethod
    def _check_camera_model(name):
        assert name in [
            "fisheye",
            "pinhole",
            "undistorted",
        ], "distortion model {} not supported".format(name)

    @staticmethod
    def _fisheye_intrinsic_K(K, D, dim, balance, fov_scale):
        return cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(
            K, D, dim, np.eye(3), balance=balance, fov_scale=fov_scale
        )

    @staticmethod
    def _fisheye_map(K, D, new_K, dim):
        return cv2.fisheye.initUndistortRectifyMap(
            K,
            D,
            np.eye(3),
            new_K,
            dim,
            cv2.CV_16SC2,
        )

    @staticmethod
    def _pinhole_intrinsic_K(K, D, dim, new_dim):
        return cv2.getOptimalNewCameraMatrix(
            K,
            D,
            dim,
            alpha=0.0,
            newImgSize=new_dim,
        )

    @staticmethod
    def _pinhole_map(K, D, new_K, dim):
        return cv2.initUndistortRectifyMap(
            K,
            D,
            np.eye(3),
            new_K,
            dim,
            cv2.CV_16SC2,
        )

    def _fisheye_undistort(self, K, D, dim, balance=0.0, fov_scale=1.0):
        if self.use_new_intrisic:
            new_K = self._fisheye_intrinsic_K(K, D, dim, balance, fov_scale)
        else:
            new_K = K
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)

    def _my_fisheye_undistort(self, new_K, K, D, dim, balance=0.0, fov_scale=1.0):
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)

    def _pinhole_undistort(self, K, D, dim):
        if self.use_new_intrisic:
            new_K, _ = self._pinhole_intrinsic_K(K, D, dim, dim)
        else:
            new_K = K
        map1, map2 = self._pinhole_map(K, D, new_K, dim)
        return new_K, (map1, map2)

    def _fisheye_undistort_simfov(self, K, D, dim, fov_tag, scale):
        new_K = self.fovs_intrinsic_k_dict[fov_tag].copy()
        new_K[:2] = new_K[:2] / scale
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)

    def _my_fisheye_undistort_simfov(self, new_K, K, D, dim, fov_tag, scale, cam_scale):
        new_K = self.fovs_intrinsic_k_dict[fov_tag].copy()
        new_K[0][0] = new_K[0][0] * cam_scale
        new_K[1][1] = new_K[1][1] * cam_scale
        new_K[:2] = new_K[:2] / scale
        map1, map2 = self._fisheye_map(
            K,
            D,
            new_K,
            dim,
        )
        return new_K, (map1, map2)

    def _pinhole_undistort_simfov(self, K, D, dim, fov_tag, scale):
        new_K = self.fovs_intrinsic_k_dict[fov_tag].copy()
        new_K[:2] = new_K[:2] / scale
        map1, map2 = self._pinhole_map(K, D, new_K, dim)
        return new_K, (map1, map2)

    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple):
        raise NotImplementedError


class UndistortStandard(UndistortBase):
    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        self._check_camera_model(camera_calib["intrinsic"]["distortion_model"])

        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
        camera_dim = tuple(camera_calib["intrinsic"]["resolution"])

        scale = 1  # undistort to same size as input TODO: fit resize_first as default or remove

        undistort_map = {}
        if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
            new_intrinsic_K = intrinsic_K
            map = (None, None)
        elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
            if cam_scale != 1:
                new_intrinsic_K = intrinsic_K.copy()
                new_intrinsic_K[0][0] = new_intrinsic_K[0][0] * cam_scale
                new_intrinsic_K[1][1] = new_intrinsic_K[1][1] * cam_scale
                new_intrinsic_K, map = self._my_fisheye_undistort(
                    new_intrinsic_K,
                    intrinsic_K,
                    intrinsic_D,
                    camera_dim,
                    balance=self.undistort_para.get("balance", 0),
                    fov_scale=self.undistort_para.get("fov_scale", 1.0),
                )
            else:
                new_intrinsic_K, map = self._fisheye_undistort(
                    intrinsic_K,
                    intrinsic_D,
                    camera_dim,
                    balance=self.undistort_para.get("balance", 0),
                    fov_scale=self.undistort_para.get("fov_scale", 1.0),
                )
        else:
            new_intrinsic_K, map = self._pinhole_undistort(intrinsic_K, intrinsic_D, camera_dim)
        undistort_map[camera_dim] = map

        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            scale = camera_dim[0] / camera_dim_target[0]
            new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
            if cam_scale != 1:
                intrinsic_K[:2] = intrinsic_K[:2] / scale
            if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
                map = (None, None)
            elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
                map = self._fisheye_map(intrinsic_K, intrinsic_D, new_intrinsic_K, camera_dim_target)
            else:
                map = self._pinhole_map(intrinsic_K, intrinsic_D, new_intrinsic_K, camera_dim_target)
            undistort_map[camera_dim_target] = map
        return new_intrinsic_K, undistort_map

    def get_new_extrinsic(self, sensor_name: str, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
        camera_dim = tuple(camera_calib["intrinsic"]["resolution"])
        if self.use_new_intrisic:
            balance = (self.undistort_para.get("balance", 0),)
            fov_scale = (self.undistort_para.get("fov_scale", 1.0),)
            if camera_calib["intrinsic"]["distortion_model"] == "fisheye":
                new_intrinsic_K = self._fisheye_intrinsic_K(intrinsic_K, intrinsic_D, camera_dim, balance, fov_scale)
            elif camera_calib["intrinsic"]["distortion_model"] == "pinhole":
                new_intrinsic_K = self._pinhole_undistort(intrinsic_K, intrinsic_D, camera_dim, camera_dim)[0]
        else:
            if cam_scale != 1:
                new_intrinsic_K = intrinsic_K.copy()
                new_intrinsic_K[0][0] = new_intrinsic_K[0][0] * cam_scale
                new_intrinsic_K[1][1] = new_intrinsic_K[1][1] * cam_scale
            else:
                new_intrinsic_K = intrinsic_K
        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            scale = camera_dim[0] / camera_dim_target[0]
            new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
        return new_intrinsic_K


class UndistortSimFov(UndistortBase):
    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        """
        Calculate new_intrinsic_K and undistort_map for sim fov.
        """
        self._check_camera_model(camera_calib["intrinsic"]["distortion_model"])

        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])

        camera_dim = tuple(camera_calib["intrinsic"]["resolution"])  # W, H

        fov_tag = sensor_name.split("_")[-1]
        assert fov_tag in self.fovs_intrinsic_k_dict, f"{fov_tag} not in {self.fovs_intrinsic_k_dict.keys()}"

        sensor_scale = 1

        undistort_map = {}
        if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
            new_intrinsic_K = intrinsic_K
            map = (None, None)
        elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
            if cam_scale != 1:
                new_intrinsic_K, map = self._my_fisheye_undistort_simfov(
                    intrinsic_K,
                    intrinsic_D,
                    camera_dim,
                    fov_tag,
                    scale=sensor_scale,
                    cam_scale=cam_scale,
                )
            else:
                new_intrinsic_K, map = self._fisheye_undistort_simfov(
                    intrinsic_K, intrinsic_D, camera_dim, fov_tag, scale=sensor_scale
                )
        else:
            new_intrinsic_K, map = self._pinhole_undistort_simfov(
                intrinsic_K, intrinsic_D, camera_dim, fov_tag, scale=sensor_scale
            )
        undistort_map[camera_dim] = map

        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            resolution_scale = camera_dim[0] / camera_dim_target[0]

            intrinsic_K[:2] = intrinsic_K[:2] / resolution_scale
            if camera_calib["intrinsic"]["distortion_model"] == "undistorted":
                new_intrinsic_K = intrinsic_K
                map = (None, None)
            elif camera_calib["intrinsic"]["distortion_model"] == "fisheye":
                new_intrinsic_K, map = self._fisheye_undistort_simfov(
                    intrinsic_K, intrinsic_D, camera_dim_target, fov_tag, scale=resolution_scale
                )
            else:
                new_intrinsic_K, map = self._pinhole_undistort_simfov(
                    intrinsic_K, intrinsic_D, camera_dim_target, fov_tag, scale=resolution_scale
                )
            undistort_map[camera_dim_target] = map

        return new_intrinsic_K, undistort_map

    def get_new_extrinsic(self, sensor_name: str, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        camera_dim = tuple(camera_calib["intrinsic"]["resolution"])  # W, H
        fov_tag = sensor_name.split("_")[-1]
        assert fov_tag in self.fovs_intrinsic_k_dict, f"{fov_tag} not in {self.fovs_intrinsic_k_dict.keys()}"
        new_K = self.fovs_intrinsic_k_dict[fov_tag].copy()
        if cam_scale != 1:
            new_K = new_K.copy()
            new_K[0][0] = new_K[0][0] * cam_scale
            new_K[1][1] = new_K[1][1] * cam_scale
        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            resolution_scale = camera_dim[0] / camera_dim_target[0]
            new_K[:2] = new_K[:2] / resolution_scale
        return new_K


class UndistortWarp(UndistortSimFov):
    def __init__(self, target_extrinsic, **kwargs):
        super().__init__(**kwargs)
        self.target_extrinsic = target_extrinsic

    def __call__(self, sensor_name: str, sensor: Sensor, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        """
        Calculate new_intrinsic_K and undistort_map for car extrinsic unification.
        """

        self._check_camera_model(camera_calib["intrinsic"]["distortion_model"])
        if "sim_fov" in sensor_name:
            new_intrinsic_K_orig, undistort_map_orig = UndistortSimFov.__call__(
                self, sensor_name, sensor, camera_calib, camera_dim_target, cam_scale
            )
        else:
            new_intrinsic_K_orig, undistort_map_orig = UndistortStandard.__call__(
                self, sensor_name, sensor, camera_calib, camera_dim_target, cam_scale
            )
        undistort_result = undistort_map_orig[camera_dim_target]
        lidar2pix = get_lidar_to_pixel(camera_calib, new_intrinsic_K_orig)
        new_k = new_intrinsic_K_orig.tolist()  # new_k.flatten().tolist()
        camera_calib["intrinsic"]["distortion_model"] = "undistorted"
        camera_calib["intrinsic"]["K"] = new_k
        camera_calib["intrinsic"]["resolution"] = list(camera_dim_target)
        lidar2img = np.concatenate([np.array(lidar2pix), np.array([[0.0, 0.0, 0.0, 1.0]])])
        lidar2img_tar = self.target_extrinsic[sensor_name]
        camera_calib["T_lidar_to_pixel"] = lidar2img_tar[:-1, :]

        campose_geom_trans_matrix = lidar2img_tar @ np.linalg.inv(lidar2img)
        img_warp_matrix = calc_warp_between_imgs(
            campose_geom_trans_matrix,
            np.eye(3),
            lidar2img,
            camera_dim_target[1],
            camera_dim_target[0],
        )
        map1, map2 = compute_perspective_remap(img_warp_matrix, size=camera_dim_target)
        map1_new, map2_new = combine_remap(
            map2,
            map1,
            undistort_result[0][:, :, 1],
            undistort_result[0][:, :, 0],
        )

        undistort_map_warp = (
            np.stack((map1_new, map2_new), axis=-1),
            undistort_result[1],
        )

        undistort_map_orig[camera_dim_target] = undistort_map_warp
        return new_intrinsic_K_orig, undistort_map_orig

    def get_new_extrinsic(self, sensor_name: str, camera_calib: dict, camera_dim_target: tuple, cam_scale=1):
        if "sim_fov" in sensor_name:
            new_intrinsic_K_orig = UndistortSimFov.get_new_extrinsic(
                self, sensor_name, camera_calib, camera_dim_target, cam_scale
            )
            camera_calib["T_lidar_to_pixel"] = self.target_extrinsic[sensor_name][:-1, :]
            camera_calib["intrinsic"]["distortion_model"] = "undistorted"
            camera_calib["intrinsic"]["K"] = new_intrinsic_K_orig
            camera_calib["intrinsic"]["resolution"] = list(camera_dim_target)
            return new_intrinsic_K_orig
        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
        camera_dim = tuple(camera_calib["intrinsic"]["resolution"])
        if self.use_new_intrisic:
            balance = (self.undistort_para.get("balance", 0),)
            fov_scale = (self.undistort_para.get("fov_scale", 1.0),)
            if camera_calib["intrinsic"]["distortion_model"] == "fisheye":
                new_intrinsic_K = self._fisheye_intrinsic_K(intrinsic_K, intrinsic_D, camera_dim, balance, fov_scale)
            elif camera_calib["intrinsic"]["distortion_model"] == "pinhole":
                new_intrinsic_K = self._pinhole_undistort(intrinsic_K, intrinsic_D, camera_dim, camera_dim)[0]
            else:
                new_intrinsic_K = intrinsic_K
        else:
            if cam_scale != 1:
                new_intrinsic_K = intrinsic_K.copy()
                new_intrinsic_K[0][0] = new_intrinsic_K[0][0] * cam_scale
                new_intrinsic_K[1][1] = new_intrinsic_K[1][1] * cam_scale
            else:
                new_intrinsic_K = intrinsic_K
        if camera_dim_target[0] is not None and camera_dim != camera_dim_target:
            scale = camera_dim[0] / camera_dim_target[0]
            new_intrinsic_K[:2] = new_intrinsic_K[:2] / scale
        camera_calib["intrinsic"]["K"] = new_intrinsic_K
        camera_calib["intrinsic"]["resolution"] = list(camera_dim_target)
        return new_intrinsic_K


def combine_remap(mapx1, mapy1, mapx2, mapy2):
    # mapx1 and mapy1 are the mapping matrices for the first remap
    # mapx2 and mapy2 are the mapping matrices for the second remap

    # Get the shape of the mappings
    h, w = mapx1.shape

    # Create empty matrices for the combined map
    mapy_combined = np.zeros((h, w), dtype=np.float32)
    mapx_combined = np.zeros((h, w), dtype=np.float32)

    # Compute the combined map
    # mapy_combined = mapy2[mapy1.astype(np.int32), mapx1.astype(np.int32)]
    # mapx_combined = mapx2[mapy1.astype(np.int32), mapx1.astype(np.int32)]
    #
    mapy_combined = cv2.remap(mapy2, mapx1.astype(np.float32), mapy1.astype(np.float32), interpolation=cv2.INTER_LINEAR)
    mapx_combined = cv2.remap(mapx2, mapx1.astype(np.float32), mapy1.astype(np.float32), interpolation=cv2.INTER_LINEAR)

    return mapy_combined, mapx_combined


def compute_perspective_remap(M, size):
    """
    计算用于透视变换的映射表 map1 和 map2。

    :param M: 透视变换矩阵 (3x3)
    :param size: 目标图像的尺寸 (宽度, 高度)
    :return: (map1, map2) 映射表
    """
    width, height = size
    map1 = np.zeros((height, width), dtype=np.float32)
    map2 = np.zeros((height, width), dtype=np.float32)

    # 创建坐标网格
    x_indices, y_indices = np.meshgrid(np.arange(width), np.arange(height))

    # 计算透视变换
    pts = np.stack((x_indices.ravel(), y_indices.ravel(), np.ones_like(x_indices.ravel())), axis=1)
    src_pts = np.dot(np.linalg.inv(M), pts.T).T
    src_pts /= src_pts[:, 2, np.newaxis]  # 归一化

    map2 = src_pts[:, 0].reshape(height, width)
    map1 = src_pts[:, 1].reshape(height, width)

    return map1, map2


def calc_warp_between_imgs(campose_geom_trans_matrix, K, ego2cam_proj, W, H, ego_voxels=None):
    try:
        if ego_voxels is None:
            x_coord, y_coord, z_coord = np.mgrid[-15:15, -100:100, -10:10]
            ego_voxels = np.stack((x_coord, y_coord, z_coord), axis=-1).reshape(-1, 3)
            ego_voxels = np.c_[ego_voxels, np.ones(len(ego_voxels))]

        cam_voxels = (ego2cam_proj.astype(np.float32) @ ego_voxels.T.astype(np.float32)).T  # (n, 4)
        tgt_cam_voxels = (campose_geom_trans_matrix.astype(np.float32) @ cam_voxels.T).T

        src_pixels = (K.astype(np.float32) @ cam_voxels[:, :3].T).T
        dst_pixels = (K.astype(np.float32) @ tgt_cam_voxels[:, :3].T).T

        src_pixels[:, :2] = src_pixels[:, :2] / src_pixels[:, 2:3]
        dst_pixels[:, :2] = dst_pixels[:, :2] / dst_pixels[:, 2:3]

        depth_filter = (src_pixels[:, 2] > 0) & (dst_pixels[:, 2] > 0)
        width_filter = ((src_pixels[:, 0] > 0) & (src_pixels[:, 0] < W)) & (
            (dst_pixels[:, 0] > 0) & (dst_pixels[:, 0] < W)
        )
        height_filter = ((src_pixels[:, 1] > 0) & (src_pixels[:, 1] < H)) & (
            (dst_pixels[:, 1] > 0) & (dst_pixels[:, 1] < H)
        )
        filters = depth_filter & width_filter & height_filter

        src_pixels = src_pixels[filters].astype(np.float32)[:, :2]
        dst_pixels = dst_pixels[filters].astype(np.float32)[:, :2]

        warp_matrix, valid_pts = cv2.findHomography(src_pixels, dst_pixels, cv2.RANSAC, 2.0)
        warp_matrix_inv = np.linalg.inv(warp_matrix)  # 通过求逆来判断是否合格
        if np.count_nonzero(np.isnan(warp_matrix_inv)):
            return np.eye(3)
        # print(sum(valid_pts), dst_pixels.shape)
        return warp_matrix
    except Exception:
        return np.eye(3)
