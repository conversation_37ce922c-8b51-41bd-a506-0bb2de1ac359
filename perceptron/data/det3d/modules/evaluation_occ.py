import copy
import refile
import bisect
import torch
import numpy as np
from tqdm import tqdm
from perceptron_eval.occupancy.metric import Metric_mIoU, Metric_Border


class OCCEvalPostprocess:
    @staticmethod
    def get_metrics(num_classes, class_names):
        # metrics
        occ_eval_metrics = Metric_mIoU(
            num_classes=num_classes,
            class_names=class_names,  # free, freespace, dynamic, static, water
            use_lidar_mask=False,
            use_image_mask=False,
        )
        IoU_eval_metrics = Metric_mIoU(
            num_classes=2,
            class_names=[
                "empty",
                "occupied",
            ],
            use_lidar_mask=False,
            use_image_mask=False,
        )
        vismask_eval_metrics = Metric_mIoU(  # evaluate visible mask output
            num_classes=2,
            class_names=[
                "non_visible",
                "visible",
            ],
            use_lidar_mask=False,
            use_image_mask=False,
        )
        border_eval_metric = Metric_Border(
            VOXEL_SIZE=[[0.1, 0.1, 8]],
            GROUND_LABEL=1,
            DYNAMIC_LABEL=[2, 5, 6, 7, 8],
            SEP_DISTANCE=[100, 200, 300, 400, 500, 600, 700, 800],
            EGO_GRID_CENTER=[152, 0],  # 自车中心点在BEVgrid的坐标，用于作为极坐标原点
            tp_thred=20,
            dis_norm_type="relative",
        )
        return occ_eval_metrics, IoU_eval_metrics, vismask_eval_metrics, border_eval_metric


class EvaluationBase:
    def __init__(
        self,
        loader_output,
        annotation,
        output_mode="freespace",
        label_mapping=None,
        num_classes=-1,
        class_names=None,
        annotaion_cls=None,
    ) -> None:
        self.annotation = annotation  # EVAL
        self.output_mode = output_mode
        self.label_mapping = label_mapping
        self.num_classes = num_classes
        self.class_names = class_names
        self.annotaion_cls = annotaion_cls

        self.frame_data_list = loader_output["frame_data_list"]
        self.frame_index = loader_output["frame_index"]
        self.calibrated_sensors = loader_output["calibrated_sensors"]
        self.calibrated_sensors_id = loader_output["calibrated_sensors_id"]
        self.data_paths = loader_output["json_collection"]
        # copy sensor dict before undistort
        self.calibrated_sensors_dict_org = copy.deepcopy(self.calibrated_sensors)

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None, output_mode="freespace"):
        def generate_single_sample_dict(pred_dict):
            if output_mode == "freespace":
                perception_result = (
                    pred_dict["pred_seg"][..., None].cpu().numpy().astype(np.uint8)
                )  # 扩展到与occ同维度 ([816, 304]) --> (816, 304, 1)
            else:
                perception_result = pred_dict["pred_seg"].cpu().numpy().astype(np.uint8)
            pred_dict["perception_result"] = perception_result  # (816, 304, 1)
            pred_dict.pop("pred_seg")
            return pred_dict

        annos = []
        for index, pred_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]  # batch id
            single_pred_dict = generate_single_sample_dict(pred_dict)
            single_pred_dict["frame_id"] = frame_id  # frame_id = 0
            annos.append(single_pred_dict)  # 有几个 batch 就加几次
            if output_path is not None:
                raise NotImplementedError
        return annos  # 'perceptron_result' & 'frame_id'

    def evaluation_init(self):
        self._init_annos()
        self._init_metrics()

    def _init_annos(self):
        self.all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]
        self.annotaion_obj = self.annotaion_cls(  # CHANGE
            {"frame_data_list": self.frame_data_list},
            mode="train",
            label_mapping=self.label_mapping,
            z_voxel_size=self.annotation.z_voxel_size,
            z_range=self.annotation.z_range,
        )

    def _init_metrics(self):
        self.occ_eval_metrics, self.IoU_eval_metrics, _, self.border_eval_metric = OCCEvalPostprocess.get_metrics(
            self.num_classes, self.class_names
        )
        self.frames_boarder = []

    def _get_scene_name(self, idx):
        # 定位当前idx数据存在于哪个json文件中
        json_idx = bisect.bisect_right(self.frame_data_list.cumulative_sizes, idx)
        json_path = self.data_paths[json_idx]
        return json_path

    def _move_to_cpu(self, data):
        if isinstance(data, torch.Tensor):
            return data.cpu()
        elif isinstance(data, dict):
            return {k: self._move_to_cpu(v) for k, v in data.items()}
        elif isinstance(data, (list, tuple)):
            return type(data)(self._move_to_cpu(item) for item in data)
        else:
            return data

    def evaluation_process(self, occ_results):
        for i, frame in enumerate(occ_results):
            occ_result_cpu = self._move_to_cpu(occ_results[i])
            args = [
                i,
                occ_result_cpu,  # 使用CPU版本的数据
                self.frame_index,
                None,
                self.calibrated_sensors,
                self.calibrated_sensors_id,
                self.annotaion_obj,
            ]
            i, pred, frame_index, _, _, _, _ = args
            _frame_id = pred["frame_id"]
            cur_idx = frame_index[_frame_id]
            if not self.all_annos[cur_idx]["is_key_frame"]:
                continue
            frame_id = self.all_annos[cur_idx]["frame_id"]
            args[3] = self.all_annos[i]
            args.append(frame_id)
            args.append(cur_idx)
            perception_result = frame["perception_result"].transpose(1, 0, 2, 3)
            semantic, semantic_mask = occ_result_cpu["semantic"], occ_result_cpu["semantic_mask"]
            if semantic.ndim == 4:
                semantic = semantic.squeeze(0)
                semantic_mask = semantic_mask.squeeze(0)
            semantic = np.stack(semantic, axis=0)
            semantic_mask = np.stack(semantic_mask, axis=0)
            semantic = semantic[:, :, (semantic.shape[2] - perception_result.shape[1]) :]
            semantic_mask = semantic_mask[:, :, (semantic.shape[2] - perception_result.shape[1]) :]
            if self.output_mode == "freepspace":
                semantic = semantic[..., None]  # (304, 816)
                semantic_mask = semantic_mask[..., None]
            gt_semantics, mask_camera, mask_lidar = (
                semantic[0],
                semantic_mask[0],
                semantic_mask[0],  # mask lidar not accessible
            )
            occ_pred = perception_result[:, :, 0, :]
            mask_lidar = mask_lidar.astype(bool)  # not used
            mask_camera = mask_camera.astype(bool)
            self.occ_eval_metrics.add_batch(occ_pred, gt_semantics, mask_lidar, mask_camera)
            occ_pred_ = np.where(occ_pred >= self.num_classes - 1, 1, 0)
            gt_semantics_ = np.where(gt_semantics >= self.num_classes - 1, 1, 0)
            self.IoU_eval_metrics.add_batch(occ_pred_, gt_semantics_, mask_lidar, mask_camera)
            self.border_eval_metric.add_batch(occ_pred, gt_semantics, mask_camera)
        return self

    def evaluation(self, occ_results, class_names):
        return self

    def save_eval_results(self, res_dict, path):
        str_head = "mIoU"  # mIoU前置
        str_content = "| " + str(np.round(res_dict["mIoU"], 4))
        str_line = "| ---"
        for k, v in res_dict.items():
            if k == "mIoU":
                continue
            v = np.round(v, 4)
            str_head += "| " + k.split("IoU_")[-1]
            str_content += f"| {v}"
            str_line += "| ---"
        print_str = "\n".join([str_head, str_line, str_content]) + "\n"
        with refile.smart_open(path, mode="a") as f:
            f.writelines(print_str)


class EvaluationOcc(EvaluationBase):
    def __init__(
        self,
        loader_output,
        annotation,
        output_mode="freespace",
        label_mapping=None,
        num_classes=-1,
        class_names=None,
        annotaion_cls=None,
        use_image_mask=False,
        use_lidar_mask=False,
        surround=False,
        only_front=True,
        use_visible_head=False,
    ) -> None:
        super().__init__(
            loader_output,
            annotation,
            None,
            False,
        )
        assert output_mode in ["freespace", "occupancy"]
        self.output_mode = output_mode
        self.label_mapping = label_mapping
        self.num_classes = num_classes
        self.class_names = class_names
        self.annotaion_cls = annotaion_cls
        self.use_image_mask = use_image_mask
        self.use_lidar_mask = use_lidar_mask
        self.surround = surround
        self.only_front = only_front

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None, output_mode="freespace"):
        def generate_single_sample_dict(pred_dict, use_visible_head=False):
            if output_mode == "freespace":
                perception_result = (
                    pred_dict["pred_seg"][..., None].cpu().numpy().astype(np.uint8)
                )  # 扩展到与occ同维度 ([816, 304]) --> (816, 304, 1)
                vismask_pred = (
                    pred_dict["vismask_pred"][..., None].cpu().numpy().astype(np.uint8)
                )  # 扩展到与occ同维度 ([816, 304]) --> (816, 304, 1)
            else:
                perception_result = pred_dict["pred_seg"].cpu().numpy().astype(np.uint8)
                vismask_pred = pred_dict["vismask_pred"].cpu().numpy().astype(np.uint8)
            pred_dict["perception_result"] = perception_result  # (816, 304, 1)
            pred_dict["vismask_pred"] = vismask_pred
            pred_dict.pop("pred_seg")
            return pred_dict

        annos = []
        for index, pred_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]  # batch id
            single_pred_dict = generate_single_sample_dict(pred_dict)
            single_pred_dict["frame_id"] = frame_id  # frame_id = 0
            annos.append(single_pred_dict)  # 有几个 batch 就加几次

            if output_path is not None:
                raise NotImplementedError
        return annos  # 'perceptron_result' & 'frame_id'

    def _init_metrics(self):
        (
            self.occ_eval_metrics_layer0,
            self.IoU_eval_metrics_layer0,
            self.vismask_eval_metrics_layer0,
            self.border_eval_metric_layer0,
        ) = OCCEvalPostprocess.get_metrics(self.num_classes, self.class_names)
        self.frames_boarder_layer0 = []
        (
            self.occ_eval_metrics_layer1,
            self.IoU_eval_metrics_layer1,
            _,
            self.border_eval_metric_layer1,
        ) = OCCEvalPostprocess.get_metrics(self.num_classes, self.class_names)
        self.frames_boarder_layer1 = []

    def evaluation_process(self, occ_results):
        for i, frame in enumerate(occ_results):
            occ_result_cpu = self._move_to_cpu(occ_results[i])
            _frame_id = occ_result_cpu["frame_id"]
            cur_idx = self.frame_index[_frame_id]
            if not self.all_annos[cur_idx]["is_key_frame"]:
                continue
            perception_result = occ_result_cpu["perception_result"].transpose(1, 0, 2, 3)
            vismask_pred = occ_result_cpu["vismask_pred"].transpose(1, 0, 2)
            semantic, semantic_mask = occ_result_cpu["semantic"], occ_result_cpu["semantic_mask"]
            if semantic.ndim == 4:
                semantic = semantic.squeeze(0)
                semantic_mask = semantic_mask.squeeze(0)
            semantic = np.stack(semantic, axis=0)
            semantic_mask = np.stack(semantic_mask, axis=0)
            semantic = semantic[:, :, (semantic.shape[2] - perception_result.shape[1]) :]
            semantic_mask = semantic_mask[:, :, (semantic.shape[2] - perception_result.shape[1]) :]
            if self.output_mode == "freepspace":
                semantic = semantic[..., None]  # (304, 816)
                semantic_mask = semantic_mask[..., None]
            gt_vismask = np.zeros_like(semantic_mask[0], dtype=np.int64)
            gt_vismask[semantic_mask[0]] = 1
            gt_vismask = gt_vismask[:, (gt_vismask.shape[1] - vismask_pred.shape[1]) :]
            for layer_id in [0, 1]:
                gt_semantics, mask_camera, mask_lidar = (
                    semantic[layer_id],
                    semantic_mask[layer_id],
                    semantic_mask[layer_id],  # mask lidar not accessible
                )
                occ_pred = perception_result[:, :, layer_id, :]
                mask_lidar = mask_lidar.astype(bool)  # not used
                mask_camera = mask_camera.astype(bool)
                if layer_id == 0:
                    self.occ_eval_metrics_layer0.add_batch(occ_pred, gt_semantics, mask_lidar, mask_camera)
                    occ_pred_ = np.where(occ_pred >= self.num_classes - 1, 1, 0)
                    gt_semantics_ = np.where(gt_semantics >= self.num_classes - 1, 1, 0)
                    self.IoU_eval_metrics_layer0.add_batch(occ_pred_, gt_semantics_, mask_lidar, mask_camera)
                    self.border_eval_metric_layer0.add_batch(occ_pred, gt_semantics, mask_camera)
                    self.vismask_eval_metrics_layer0.add_batch(vismask_pred, gt_vismask, mask_lidar, mask_camera)
                if layer_id == 1:
                    self.occ_eval_metrics_layer1.add_batch(occ_pred, gt_semantics, mask_lidar, mask_camera)
                    occ_pred_ = np.where(occ_pred >= self.num_classes - 1, 1, 0)
                    gt_semantics_ = np.where(gt_semantics >= self.num_classes - 1, 1, 0)
                    self.IoU_eval_metrics_layer1.add_batch(occ_pred_, gt_semantics_, mask_lidar, mask_camera)
                    self.border_eval_metric_layer1.add_batch(occ_pred, gt_semantics, mask_camera)
