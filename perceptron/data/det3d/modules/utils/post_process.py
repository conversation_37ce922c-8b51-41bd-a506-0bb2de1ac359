import numpy as np
import torch
from collections import defaultdict

try:
    from perceptron_ops.iou3d_nms.iou3d_nms_utils import boxes_iou3d_gpu, iou3d_nms_cuda, boxes_bev_iou_cpu
except ImportWarning:
    print("Radar postprocess unavailable, cannot use radar input.")

name2label = {
    "car": 1,
    "truck": 2,
    "construction_vehicle": 3,
    "bus": 4,
    "motorcycle": 5,
    "bicycle": 6,
    "tricycle": 7,
    "cyclist": 8,
    "pedestrian": 9,
    "masked_area": 10,
}

label2name = {
    1: "car",
    2: "truck",
    3: "construction_vehicle",
    4: "bus",
    5: "motorcycle",
    6: "bicycle",
    7: "tricycle",
    8: "cyclist",
    9: "pedestrian",
}


class PostProcess:
    def __init__(self, iou_thr=0.1, nms_pre_max_size_use=1500, nms_post_max_size_use=100, use_gpu=True):
        self.iou_thr = iou_thr
        self.nms_pre_max_size_use = nms_pre_max_size_use
        self.nms_post_max_size_use = nms_post_max_size_use
        self.use_gpu = use_gpu

    def convert_result_to_numpy(self, det_anno):
        boxes_3d, scores, nms_scores = det_anno["boxes_3d"], det_anno["score"], det_anno["score"]
        labels = np.array([name2label[name] for name in det_anno["name"]])
        if self.use_gpu is True:
            boxes_3d = torch.from_numpy(boxes_3d).cuda()
            labels = torch.from_numpy(labels).cuda()
            scores = torch.from_numpy(scores).cuda()
            nms_scores = torch.from_numpy(nms_scores).cuda()
        else:
            boxes_3d = torch.from_numpy(boxes_3d)
            labels = torch.from_numpy(labels)
            scores = torch.from_numpy(scores)
            nms_scores = torch.from_numpy(nms_scores)
        return boxes_3d, labels, scores, nms_scores

    def nms_options(self, det_anno_1):
        raise NotImplementedError("{} not implement this func".format(self.__class__))

    def rep_nms(self, det_anno, rep_class_list):
        mask = []
        for name in det_anno["name"]:
            if name in rep_class_list:
                mask.append(1)
            else:
                mask.append(0)
        mask = np.array(mask).astype("bool")
        det_anno_1 = {
            "boxes_3d": det_anno["boxes_3d"][mask],
            "score": det_anno["score"][mask],
            "name": det_anno["name"][mask],
        }
        if mask.sum() == 0:
            return det_anno

        boxes, labels, scores, selected = self.nms_options(det_anno_1)
        new_labels = np.array([label2name[l] for l in labels])
        return {
            "boxes_3d": np.concatenate((boxes, det_anno["boxes_3d"][~mask])),
            "score": np.concatenate((scores, det_anno["score"][~mask])),
            "name": np.concatenate((new_labels, det_anno["name"][~mask])),
            "selected": selected,
        }


class StandardNMSPostProcess(PostProcess):  # nms算iou时算角度
    def nms_options(self, det_anno_1):
        boxes_3d, labels, scores, nms_scores = self.convert_result_to_numpy(det_anno_1)
        if nms_scores.shape[0] > 0:
            selected = self._nms_gpu_3d(
                boxes_3d[:, :7],
                nms_scores,
                thresh=self.iou_thr,  # train0.8 test0.1
                pre_maxsize=self.nms_pre_max_size_use,
                post_max_size=self.nms_post_max_size_use,
            )
        else:
            selected = []
        if isinstance(selected, torch.tensor):
            selected = selected.cpu().numpy()
        return (
            boxes_3d[selected].cpu().numpy(),
            labels[selected].cpu().numpy(),
            scores[selected].cpu().numpy(),
            selected,
        )

    @staticmethod
    def _nms_gpu_3d(boxes, scores, thresh, pre_maxsize=None, post_max_size=None):
        """
        iou3d nms gpu: oriented nms gpu
        :param boxes: (N, 7) [x, y, z, dx, dy, dz, heading]
        :param scores: (N)
        :param thresh:
        :return:
        """
        assert boxes.shape[1] == 7
        order = scores.sort(0, descending=True)[1]
        if pre_maxsize is not None:
            order = order[:pre_maxsize]

        boxes = boxes[order].contiguous()
        keep = torch.LongTensor(boxes.size(0))
        num_out = iou3d_nms_cuda.nms_gpu(boxes, keep, thresh)
        selected = order[keep[:num_out].cuda()].contiguous()

        if post_max_size is not None:
            selected = selected[:post_max_size]

        return selected

    @staticmethod
    def _ray_nms(
        boxes,
        scores,
        thresh,
        pre_maxsize=None,
        post_max_size=None,
        with_ray_nms=True,
        dis_thr=0.4 / 1.414,
        dis_scale=0.01,
    ):
        """
        :param boxes: (N, 7) [x, y, z, dx, dy, dz, heading]
        :param scores: (N)
        :param thresh:
        :return:
        """
        import numpy as np

        assert boxes.shape[1] == 7
        order = scores.argsort()[::-1].astype(np.int32)
        x1 = boxes[:, 0]
        y1 = boxes[:, 1]

        ndets = boxes.shape[0]
        suppressed = np.zeros((ndets), dtype=np.int32)
        keep = []
        for _i in range(ndets):
            i = order[_i]  # start with highest score box
            if suppressed[i] == 1:  # if any box have enough iou with this, remove it
                continue
            keep.append(i)
            for _j in range(_i + 1, ndets):
                j = order[_j]
                if suppressed[j] == 1:
                    continue
                # calculate center distance between i and j box
                dist_filter = ((x1[i] - x1[j]) ** 2 + (y1[i] - y1[j]) ** 2) <= thresh

                # # ovr = inter / areas[j]
                # if dist <= thresh:
                # suppressed[j] = 1
                # continue

                # calculate theta < 90°
                if with_ray_nms:
                    theta_filter = (x1[i] * x1[j] + y1[i] * y1[j]) > 0

                    # calculate ray distance
                    l_a = y1[i] / (x1[i] + 1e-6)
                    l_b = -1
                    l_c = 0

                    ray_dist = abs(l_a * x1[j] + l_b * y1[j] + l_c) / np.sqrt(l_a ** 2 + l_b ** 2 + 1e-6)
                    ray_dist_filter = ray_dist < (dis_thr + dis_scale * np.sqrt((boxes[j, :3] ** 2).sum(-1)))
                else:
                    theta_filter = ray_dist_filter = False

                if dist_filter or (theta_filter and ray_dist_filter):
                    suppressed[j] = 1

        if post_max_size < len(keep):
            return torch.LongTensor(keep[:post_max_size])

        return torch.LongTensor(keep)


class StandardNormalNMSPostProcess(PostProcess):  # nms算iou时不算角度
    def _nms_gpu_3d(self, boxes, scores, thresh, pre_maxsize=None, post_max_size=None):
        """
        iou3d nms gpu: normal nms gpu
        :param boxes: (N, 7) [x, y, z, dx, dy, dz, heading]
        :param scores: (N)
        :param thresh:
        :return:
        """
        assert boxes.shape[1] == 7
        order = scores.sort(0, descending=True)[1]
        if pre_maxsize is not None:
            order = order[:pre_maxsize]

        boxes = boxes[order].contiguous()
        keep = torch.LongTensor(boxes.size(0))
        num_out = iou3d_nms_cuda.nms_normal_gpu(boxes, keep, thresh)
        selected = order[keep[:num_out].cuda()].contiguous()

        if post_max_size is not None:
            selected = selected[:post_max_size]

        return selected


class WeightedNMSPostProcess(PostProcess):  # python版（其中计算3diou用的gpu）
    def get_weighted_box_v2(self, boxes):
        """
        Create weighted box for set of boxes

        Parameter:
        @ boxes: set of boxes to fuse. 9-dim, (x, y, z, l, w, h, theta, score, cat)

        Return
        @ weighted box: (x, y, z, l, w, h, theta, score, cat)
        """

        box = np.zeros(10, dtype=np.float32)  # xyz, lwh, sin, cos, score, cat

        cat_cnt = defaultdict(list)
        for b in boxes:
            box[:6] += b[7] * b[:6]
            box[6] += b[7] * np.sin(b[6])
            box[7] += b[7] * np.cos(b[6])
            box[8] += b[7]  # score
            cat_cnt[b[8]].append(b[7])  # cat counter

        box[:8] /= box[8]
        box[6] = (np.arctan2(box[6], box[7]) + np.pi * 2) % (2 * np.pi)  # angle
        box[7] = box[8] / len(boxes)  # mean_score

        cat_scores = dict()
        for cat in cat_cnt.keys():
            cat_scores[cat] = np.sum(cat_cnt[cat]) / len(boxes)  # 各类别加权分数和 / 总框数
        max_cat_cnt, max_cat = -1, -1
        for k, v in cat_scores.items():
            if v > max_cat_cnt:
                max_cat_cnt = v
                max_cat = k

        box[8] = max_cat  # category

        return box[:9]  # xyz, lwh, sin, cos, score, cat

    def nms_options(self, det_anno_1):
        boxes_3d, labels, scores, nms_scores = self.convert_result_to_numpy(det_anno_1)
        if nms_scores.shape[0] > 0:
            selected, boxes, scores, labels = self._nms_gpu_3d(
                boxes_3d[:, :7],
                nms_scores,
                labels,
                thresh=self.iou_thr,  # train0.8 test0.1
                pre_maxsize=self.nms_pre_max_size_use,
                post_max_size=self.nms_post_max_size_use,
            )

            return boxes, labels, scores, selected
        else:
            selected = np.array([])
            return (
                boxes_3d[selected].cpu().numpy(),
                labels[selected].cpu().numpy(),
                scores[selected].cpu().numpy(),
                selected,
            )

    def _nms_gpu_3d(self, boxes, scores, labels, thresh, pre_maxsize=None, post_max_size=None):
        assert boxes.shape[1] == 7
        order = scores.sort(0, descending=True)[1]
        if pre_maxsize is not None:
            order = order[:pre_maxsize]

        boxes = boxes[order].contiguous()
        scores = scores[order].view(-1, 1).contiguous()
        labels = labels[order].view(-1, 1).contiguous()

        res_boxes, res_scores, res_labels = [], [], []
        selected = []
        while order.shape[0] > 0:
            i = order[0]
            selected.append(i.cpu().numpy())
            if self.use_gpu is True:
                iou = boxes_iou3d_gpu(boxes[i].view(1, -1).contiguous(), boxes[order].contiguous())[0]  # 1 x num_order
            else:
                iou = boxes_bev_iou_cpu(boxes[i].view(1, -1).contiguous(), boxes[order].contiguous())[0]

            in_inds = torch.where(iou >= thresh)[0]
            in_boxes, in_scores, in_labels = (
                boxes[order[in_inds], :],
                scores[order[in_inds], :],
                labels[order[in_inds], :],
            )

            fuser_in_boxes_array = torch.cat([in_boxes, in_scores, in_labels], axis=1).cpu().numpy()
            fuser_inbox = self.get_weighted_box_v2(fuser_in_boxes_array)  # 跨类别box也可以fuser
            res_boxes.append(fuser_inbox[:7])
            res_scores.append(fuser_inbox[7])
            res_labels.append(fuser_inbox[8])

            out_inds = torch.where(iou < thresh)[0]
            order = order[out_inds]

        return np.array(selected), np.array(res_boxes), np.array(res_scores), np.array(res_labels)
