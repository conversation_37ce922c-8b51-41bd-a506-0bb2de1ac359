class GtDatabasePathManager:

    # Data format
    # {'name': 'Car',
    # 'path': 'gt_database/000003_Car_0.bin',
    # 'nori_id': '29300758,3000a60a9672',
    # 'image_idx': '000003',
    # 'gt_idx': 0,
    # 'box3d_lidar': array([13.51070309, -0.98177999, -0.90948981,  4.15,  1.73,
    #         1.57      , -3.19079633]),
    # 'num_points_in_gt': 677,
    # 'difficulty': 0,
    # 'bbox': array([614.24, 181.78, 727.31, 284.77], dtype=float32),
    # 'score': -1.0}

    gt_database_paths = {
        "kitti": {
            "training": "s3://generalDetection/3DDatasets/Kitti/info/kitti_dbinfos_train_with_nori.pkl",
        },
        "once": {
            "training": "s3://generalDetection/3DDatasets/HUAWEI_ONCE/once_dbinfos_train_with_nori.pkl",
            "trainval": "s3://generalDetection/3DDatasets/HUAWEI_ONCE/once_dbinfos_trainval_with_nori.pkl",
        },
        "nuScenes": {
            "training": "s3://generalDetection/3DDatasets/nuScenes/info/nuscenes_dbinfos_10sweeps_withvelo_with_nori.pkl",
        },
        "nuScenes_multimodal": {
            "training": "s3://e2emodel-data/e2emodel_dataset/nuscenes/nusc_dbinfos_train_nori.pkl",
        },
    }

    def __init__(self):
        pass

    @classmethod
    def get_gt_database_path_by_name(cls, name):
        return cls.gt_database_paths[name]
