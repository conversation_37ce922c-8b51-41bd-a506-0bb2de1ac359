from typing import Any, Dict
from functools import partial
from perceptron.data.det3d.utils.functional import initialize_object


def initialize_common_sensor(cfg: Dict[str, Any]) -> Any:
    """Initialize Object from a configuration.

    Example:
        >>> cfg = Dict(
        ...     type = InitObject,
        ...     args = args
        ...     ...)
        >>> test = initialize_object(cfg=cfg) # Initialize Object
    """
    func = cfg.pop("sensor_func")
    common_sensor = initialize_object(cfg)
    common_sensor.get_sensor = partial(func, common_sensor)
    return common_sensor
