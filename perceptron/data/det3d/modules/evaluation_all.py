import refile
from tqdm import tqdm
from loguru import logger
from scipy.spatial.transform import Rotation as R
import os

from perceptron.utils.file_io import dump_pkl

from perceptron.data.det3d.modules.evaluation import EvaluationBase

try:
    from perceptron_eval.occupancy.metric import Metric_Border  # noqa
except ImportError as e:
    logger.error(f"{e}: To use occ Metric_Border, please checkout the Perceptron-eval to branch: e2e_master")


class InferenceMultitask(EvaluationBase):
    def __init__(
        self,
        loader_output,
        annotation,
        category_map=None,
        dump_det_results=True,
        around_occluded_mode=False,
        class_names=[
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "motorcycle",
            "bicycle",
            "tricycle",
            "cyclist",
            "pedestrian",
            # "masked_area",
        ],
    ) -> None:
        super().__init__(
            loader_output,
            annotation,
            category_map,
            dump_det_results,
            around_occluded_mode,
        )
        self.class_names = class_names
        self.dump_each = True

    def inference(self, results, class_names, **kwargs):
        # results = self.reformater_and_dump(results, **kwargs) # 三个pkl输出

        # 三合一输出
        results = self.reformater_all_and_dump(results, **kwargs)

        return results

    def reformater_all_and_dump(self, det_annos, **kwargs):
        results = self.reformater_all(det_annos, self.calibrated_sensors, self.calibrated_sensors_id)

        # dump pkl
        eval_output_dir = refile.smart_path_join(
            kwargs["jsonfile_prefix"],
            "eval_results",
        )
        if self.dump_each:
            dump_each_clip(results, kwargs)
        else:
            results["occ_range"] = kwargs["occ_range"]
            dump_pkl(results, refile.smart_path_join(eval_output_dir, "eval_occ_god_e2e.pkl"))

        return results

    def reformater_and_dump(self, det_annos, **kwargs):
        # dir
        eval_output_dir = refile.smart_path_join(
            kwargs["jsonfile_prefix"],
            "eval_results",
        )
        results = {}

        if "pred_occ" in det_annos:
            pred_occ = det_annos["pred_occ"]
            results_occ = self.reformater_occ_split(
                pred_occ, self.calibrated_sensors, self.calibrated_sensors_id
            )  # occ
            dump_pkl(results_occ, refile.smart_path_join(eval_output_dir, "eval_occ.pkl"))
            results["occ"] = results_occ

        if "pred_god" in det_annos:
            pred_god = det_annos["pred_god"]
            results_god = self.reformater_god(pred_god, self.calibrated_sensors, self.calibrated_sensors_id)  # god
            dump_pkl(results_god, refile.smart_path_join(eval_output_dir, "eval_god.pkl"))
            results["god"] = results_god

        if "pred_e2e" in det_annos:
            pred_e2e = det_annos["pred_e2e"]
            results_e2e = self.reformater_e2e(pred_e2e, self.calibrated_sensors, self.calibrated_sensors_id)  # e2e
            dump_pkl(results_e2e, refile.smart_path_join(eval_output_dir, "eval_e2e.pkl"))
            results["e2e"] = results_e2e

        return results

    def refomater_occ(self, preds):
        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]

        results = []
        for i in tqdm(range(len(preds)), desc="occ reformating"):
            cur_idx = self.frame_index[i]
            # if not all_annos[cur_idx]["is_key_frame"]:
            #     continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]
            # 是否用all_annos?
            all_annos[i]["perception_result"] = preds[i]["perception_result"].transpose(1, 0, 2)
            all_annos[i]["json_path"] = json_path
            all_annos[i]["frame_id"] = frame_id

            results.append(all_annos[i])

        return results

    def reformater_god(self, det_annos, calibrated_sensors_dict, calibrated_sensors_id):
        all_annos = []
        for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]"):
            frame = self.frame_data_list[i].copy()
            frame.pop("occ_path", None)
            frame.pop("freespace_path", None)
            all_annos.append(frame)
        results = [
            {"frames": [], "calibrated_sensors": {}, "json_path": None} for i in range(len(set(calibrated_sensors_id)))
        ]

        for i in tqdm(range(len(det_annos)), desc="god reformating"):
            cur_idx = self.frame_index[i]
            # if not all_annos[cur_idx]["is_key_frame"]:
            #     continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]
            preds = []
            for j in range(len(det_annos[i]["boxes_3d"])):
                d = {}
                # print(det_annos[i].keys())
                d["det_score"] = det_annos[i]["score"][j]
                d["xyz_lidar"] = {}
                d["xyz_lidar"]["x"] = det_annos[i]["boxes_3d"][j][0]
                d["xyz_lidar"]["y"] = det_annos[i]["boxes_3d"][j][1]
                d["xyz_lidar"]["z"] = det_annos[i]["boxes_3d"][j][2]
                d["lwh"] = {}
                d["lwh"]["l"] = det_annos[i]["boxes_3d"][j][3]
                d["lwh"]["w"] = det_annos[i]["boxes_3d"][j][4]
                d["lwh"]["h"] = det_annos[i]["boxes_3d"][j][5]
                angle = R.from_euler(seq="z", angles=det_annos[i]["boxes_3d"][j][6]).as_quat()
                d["angle_lidar"] = {}
                d["angle_lidar"]["x"] = angle[0]
                d["angle_lidar"]["y"] = angle[1]
                d["angle_lidar"]["z"] = angle[2]
                d["angle_lidar"]["w"] = angle[3]
                d["category"] = det_annos[i]["name"][j]
                d["json_path"] = json_path
                d["json_frame_id"] = frame_id
                preds.append(d)

            all_annos[i]["preds"] = preds
            results[calibrated_sensors_id[i]]["frames"].append(all_annos[i])
            results[calibrated_sensors_id[i]]["json_path"] = json_path

        for j in range(len(results)):
            results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results

    def reformater_e2e(self, det_annos, calibrated_sensors_dict, calibrated_sensors_id):
        all_annos = []
        for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]"):
            frame = self.frame_data_list[i].copy()
            frame.pop("occ_path", None)
            frame.pop("freespace_path", None)
            all_annos.append(frame)
        results = [
            {"frames": [], "calibrated_sensors": {}, "json_path": None} for i in range(len(set(calibrated_sensors_id)))
        ]

        for i in tqdm(range(len(det_annos)), desc="e2e reformating"):
            cur_idx = self.frame_index[i]
            # if not all_annos[cur_idx]["is_key_frame"]: ## 是否去除  labels未作判断
            #     continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]
            preds = []
            boxes_3d = det_annos[i]["boxes_3d"].tensor
            for j in range(len(det_annos[i]["boxes_3d"])):
                d = {}
                d["det_score"] = det_annos[i]["scores_3d"][j].tolist()
                d["xyz_lidar"] = {}
                d["xyz_lidar"]["x"] = boxes_3d[j][0].tolist()
                d["xyz_lidar"]["y"] = boxes_3d[j][1].tolist()
                d["xyz_lidar"]["z"] = (boxes_3d[j][2] + boxes_3d[j][5] / 2.0).tolist()
                d["lwh"] = {}
                d["lwh"]["l"] = boxes_3d[j][3].tolist()
                d["lwh"]["w"] = boxes_3d[j][4].tolist()
                d["lwh"]["h"] = boxes_3d[j][5].tolist()
                angle = R.from_euler(seq="z", angles=boxes_3d[j][6]).as_quat()
                d["angle_lidar"] = {}
                d["angle_lidar"]["x"] = angle[0]
                d["angle_lidar"]["y"] = angle[1]
                d["angle_lidar"]["z"] = angle[2]
                d["angle_lidar"]["w"] = angle[3]
                d["velocity_lidar"] = {}
                if "velocity" not in det_annos[i].keys():
                    d["velocity_lidar"]["x"] = boxes_3d[j][7].tolist()
                    d["velocity_lidar"]["y"] = boxes_3d[j][8].tolist()
                    d["velocity_lidar"]["z"] = 0
                else:
                    d["velocity_lidar"]["x"] = det_annos[i]["velocity"][j][0][0].tolist()
                    d["velocity_lidar"]["y"] = det_annos[i]["velocity"][j][0][1].tolist()
                    d["velocity_lidar"]["z"] = 0
                d["category"] = self.class_names[det_annos[i]["labels_3d"][j]]

                # overwrite
                if "track_scores" in det_annos[i].keys():
                    d["det_score"] = det_annos[i]["track_scores"][j].tolist()

                # (T，2) ： offset relative to the obstacle position in current frame
                if "forecasting" in det_annos[i].keys():
                    d["forecasting"] = det_annos[i]["forecasting"][j].tolist()
                if "velocity" in det_annos[i].keys():
                    d["velocity_forecasting"] = det_annos[i]["velocity"][j].tolist()  # all future frames
                d["track_id"] = det_annos[i]["track_ids"][j].tolist() if "track_scores" in det_annos[i].keys() else -1

                d["json_path"] = json_path
                d["json_frame_id"] = frame_id
                preds.append(d)

                # 可以增加label的判断

            all_annos[i]["preds"] = preds
            results[calibrated_sensors_id[i]]["frames"].append(all_annos[i])
            results[calibrated_sensors_id[i]]["json_path"] = json_path

        for j in range(len(results)):
            results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results

    def reformater_occ_split(self, det_annos, calibrated_sensors_dict, calibrated_sensors_id):
        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]
        results = [
            {"frames": [], "calibrated_sensors": {}, "json_path": None} for i in range(len(set(calibrated_sensors_id)))
        ]

        for i in tqdm(range(len(det_annos)), desc="occ reformating"):
            cur_idx = self.frame_index[i]
            # if not all_annos[cur_idx]["is_key_frame"]:
            #     continue
            json_path = self._get_scene_name(cur_idx)
            all_annos[i]["perception_result"] = det_annos[i]["perception_result"].transpose(1, 0, 2)
            # all_annos[i]["json_path"] = json_path
            # all_annos[i]["frame_id"] = frame_id
            results[calibrated_sensors_id[i]]["frames"].append(all_annos[i])
            results[calibrated_sensors_id[i]]["json_path"] = json_path

        for j in range(len(results)):
            results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results

    def reformater_all(self, det_annos, calibrated_sensors_dict, calibrated_sensors_id):
        # 增加判断
        if "pred_occ" in det_annos:
            det_annos_occ = det_annos["pred_occ"]
        else:
            det_annos_occ = None

        if "pred_god" in det_annos:
            det_annos_god = det_annos["pred_god"]
        else:
            det_annos_god = None

        if "pred_e2e" in det_annos:
            det_annos_e2e = det_annos["pred_e2e"]
        else:
            det_annos_e2e = None

        det_annos_list = [det_annos_occ, det_annos_god, det_annos_e2e]
        length = check_length_equal(det_annos_list)

        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]
        results = [
            {"frames": [], "calibrated_sensors": {}, "json_path": None} for i in range(len(set(calibrated_sensors_id)))
        ]

        for i in tqdm(range(length), desc="reformating"):
            cur_idx = self.frame_index[i]
            # if not all_annos[cur_idx]["is_key_frame"]: ## 是否去除  labels未作判断
            #     continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]
            # occ
            if det_annos_occ is not None:
                all_annos[i]["preds_occ"] = det_annos_occ[i]["perception_result"].transpose(1, 0, 2)

            # god
            if det_annos_god is not None:
                preds_god = []
                for j in range(len(det_annos_god[i]["boxes_3d"])):
                    d = {}
                    # print(det_annos[i].keys())
                    d["det_score"] = det_annos_god[i]["score"][j]
                    d["xyz_lidar"] = {}
                    d["xyz_lidar"]["x"] = det_annos_god[i]["boxes_3d"][j][0]
                    d["xyz_lidar"]["y"] = det_annos_god[i]["boxes_3d"][j][1]
                    d["xyz_lidar"]["z"] = det_annos_god[i]["boxes_3d"][j][2]
                    d["lwh"] = {}
                    d["lwh"]["l"] = det_annos_god[i]["boxes_3d"][j][3]
                    d["lwh"]["w"] = det_annos_god[i]["boxes_3d"][j][4]
                    d["lwh"]["h"] = det_annos_god[i]["boxes_3d"][j][5]
                    angle = R.from_euler(seq="z", angles=det_annos_god[i]["boxes_3d"][j][6]).as_quat()
                    d["angle_lidar"] = {}
                    d["angle_lidar"]["x"] = angle[0]
                    d["angle_lidar"]["y"] = angle[1]
                    d["angle_lidar"]["z"] = angle[2]
                    d["angle_lidar"]["w"] = angle[3]
                    d["category"] = det_annos_god[i]["name"][j]
                    d["json_path"] = json_path
                    d["json_frame_id"] = frame_id
                    preds_god.append(d)

                all_annos[i]["preds_god"] = preds_god

            # e2e
            if det_annos_e2e is not None:
                preds_e2e = []
                boxes_3d = det_annos_e2e[i]["boxes_3d"].tensor
                for j in range(len(det_annos_e2e[i]["boxes_3d"])):
                    d = {}
                    d["det_score"] = det_annos_e2e[i]["scores_3d"][j].tolist()
                    d["xyz_lidar"] = {}
                    d["xyz_lidar"]["x"] = boxes_3d[j][0].tolist()
                    d["xyz_lidar"]["y"] = boxes_3d[j][1].tolist()
                    d["xyz_lidar"]["z"] = (boxes_3d[j][2] + boxes_3d[j][5] / 2.0).tolist()
                    d["lwh"] = {}
                    d["lwh"]["l"] = boxes_3d[j][3].tolist()
                    d["lwh"]["w"] = boxes_3d[j][4].tolist()
                    d["lwh"]["h"] = boxes_3d[j][5].tolist()
                    angle = R.from_euler(seq="z", angles=boxes_3d[j][6]).as_quat()
                    d["angle_lidar"] = {}
                    d["angle_lidar"]["x"] = angle[0]
                    d["angle_lidar"]["y"] = angle[1]
                    d["angle_lidar"]["z"] = angle[2]
                    d["angle_lidar"]["w"] = angle[3]
                    d["velocity_lidar"] = {}
                    if "velocity" not in det_annos_e2e[i].keys():
                        d["velocity_lidar"]["x"] = boxes_3d[j][7].tolist()
                        d["velocity_lidar"]["y"] = boxes_3d[j][8].tolist()
                        d["velocity_lidar"]["z"] = 0
                    else:
                        d["velocity_lidar"]["x"] = det_annos_e2e[i]["velocity"][j][0][0].tolist()
                        d["velocity_lidar"]["y"] = det_annos_e2e[i]["velocity"][j][0][1].tolist()
                        d["velocity_lidar"]["z"] = 0
                    d["category"] = self.class_names[det_annos_e2e[i]["labels_3d"][j]]

                    # overwrite
                    if "track_scores" in det_annos_e2e[i].keys():
                        d["det_score"] = det_annos_e2e[i]["track_scores"][j].tolist()

                    # (T，2) ： offset relative to the obstacle position in current frame
                    if "forecasting" in det_annos_e2e[i].keys():
                        d["forecasting"] = det_annos_e2e[i]["forecasting"][j].tolist()
                    if "velocity" in det_annos_e2e[i].keys():
                        d["velocity_forecasting"] = det_annos_e2e[i]["velocity"][j].tolist()  # all future frames
                    d["track_id"] = (
                        det_annos_e2e[i]["track_ids"][j].tolist() if "track_scores" in det_annos_e2e[i].keys() else -1
                    )

                    d["json_path"] = json_path
                    d["json_frame_id"] = frame_id
                    preds_e2e.append(d)
                    # 可以增加label的判断

                all_annos[i]["preds_e2e"] = preds_e2e

            results[calibrated_sensors_id[i]]["frames"].append(all_annos[i])
            results[calibrated_sensors_id[i]]["json_path"] = json_path

        for j in range(len(results)):
            results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results


def check_length_equal(det_annos_list):
    # 创建一个列表，包含所有非 None 的元素
    elements = [el for el in det_annos_list if el is not None]

    # 如果有两个以上的元素
    if len(elements) > 1:
        lengths = [len(el) for el in elements]

        if len(set(lengths)) == 1:
            return lengths[0]
        else:
            raise ValueError("The lengths of the non-None elements are not equal.")
    else:
        return lengths[0]


def dump_each_clip(results, kwargs):
    output_dir = refile.smart_path_join(
        kwargs["jsonfile_prefix"],
        "eval_results",
    )

    for item in results:
        json_path = item["json_path"]
        item["occ_range"] = kwargs["occ_range"]
        filename = os.path.basename(json_path).replace(".json", "_infer_occ_god_e2e.pkl")
        dump_pkl(item, refile.smart_path_join(output_dir, filename))
