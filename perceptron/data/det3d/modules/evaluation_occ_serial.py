import os
import numpy as np
import refile
from tqdm import tqdm
from loguru import logger

from perceptron.utils.file_io import dump_pkl

from perceptron_eval.occupancy.metric import Metric_mIoU
from perceptron.data.det3d.modules.evaluation import EvaluationBase

try:
    from perceptron_eval.occupancy.metric import Metric_Border  # noqa
except ImportError as e:
    logger.error(f"{e}: To use occ Metric_Border, please checkout the Perceptron-eval to branch: e2e_master")


class EvaluationSerialOcc(EvaluationBase):
    def __init__(
        self,
        loader_output,
        annotation,
        output_mode="freespace",
        label_mapping=None,
        num_classes=-1,
        class_names=None,
        annotaion_cls=None,
        use_image_mask=False,
        use_lidar_mask=False,
        surround=False,
        only_front=False,
        use_visible_head=False,
    ) -> None:
        super().__init__(
            loader_output,
            annotation,
            None,
            False,
        )
        assert output_mode in ["freespace", "occupancy"]
        self.output_mode = output_mode
        self.label_mapping = label_mapping
        self.num_classes = num_classes
        self.class_names = class_names
        self.annotaion_cls = annotaion_cls
        self.use_image_mask = use_image_mask
        self.use_lidar_mask = use_lidar_mask
        self.surround = surround
        self.only_front = only_front
        self.use_visible_head = use_visible_head

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None, output_mode="freespace"):
        def generate_single_sample_dict(pred_dict):
            if output_mode == "freespace":
                perception_result = pred_dict["pred_seg"][..., None].cpu().numpy().astype(np.uint8)  # 扩展到与occ同维度
                if "vismask_pred" in pred_dict:
                    vismask_pred = pred_dict["vismask_pred"][..., None].cpu().numpy().astype(np.uint8)

            else:
                perception_result = pred_dict["pred_seg"].cpu().numpy().astype(np.uint8)
                if "vismask_pred" in pred_dict:
                    vismask_pred = pred_dict["vismask_pred"].cpu().numpy().astype(np.uint8)
            pred_dict["perception_result"] = perception_result
            pred_dict.pop("pred_seg")
            if "vismask_pred" in pred_dict:
                pred_dict["vismask_pred"] = vismask_pred
            return pred_dict

        annos = []
        for index, pred_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(pred_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def evaluation(
        self,
        occ_results,
        class_names,
        output_dir,
    ):
        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]

        eval_result_dir = refile.smart_path_join(output_dir, "eval_results_occ")
        frames, multi_layers = self.reformater(
            occ_results,
            all_annos,
        )
        if os.environ.get("DUMP", False):
            dump_pkl(frames, refile.smart_path_join(eval_result_dir, "occ_results.pkl"))
        if not multi_layers:
            mIoU, print_str = self.evalute_process(frames, eval_result_dir, -1)
        else:
            mIoU, print_str = self.evalute_process(frames, eval_result_dir, 0)
            mIoU_1, print_str_1 = self.evalute_process(frames, eval_result_dir, 1)
            for iou_name, iou_value in mIoU_1.items():
                new_iou_name = iou_name + "_layer2"
                mIoU[new_iou_name] = iou_value
                # print_str += f"\n {new_iou_name} : {iou_value}"

        if self.use_visible_head:
            self.vismask_eval_metrics = Metric_mIoU(  # evaluate visible mask output
                num_classes=2,
                class_names=[
                    "non_visible",
                    "visible",
                ],
                use_lidar_mask=False,
                use_image_mask=False,
            )

            print("\nStarting Vismask MIoU Evaluation...")
            for index, frame in enumerate(tqdm(frames)):
                _, mask_camera, mask_lidar = (
                    frame["semantic"][0],
                    frame["semantic_mask"][0],
                    frame["semantic_mask"][0],  # mask lidar not accessible
                )
                mask_lidar = mask_lidar.astype(bool)  # not used
                mask_camera = mask_camera.astype(bool)

                gt_vismask = frame["vismask_gt"]
                vismask_pred = frame["vismask_pred"]

                self.vismask_eval_metrics.add_batch(vismask_pred, gt_vismask, mask_lidar, mask_camera)

            mIoU_vismask, print_str_vismask = self.vismask_eval_metrics.count_miou()
            print(f"=-=-=-=miou vismask keys:{mIoU_vismask.keys()}")

            mIoU["IoU_visible"] = mIoU_vismask["IoU_visible"]
            IoU_vismask_str = "\n Visible IoU : {0}".format(mIoU["IoU_visible"])
            print_str += IoU_vismask_str

        logger.info(print_str)

        return mIoU, print_str

    def evalute_process(self, frames, eval_result_dir, layer_idx):
        self.occ_eval_metrics = Metric_mIoU(
            num_classes=self.num_classes,
            class_names=self.class_names,
            use_lidar_mask=self.use_lidar_mask,
            use_image_mask=self.use_image_mask,
        )
        self.IoU_eval_metrics = Metric_mIoU(
            num_classes=2,
            class_names=[
                "empty",
                "occupied",
            ],
            use_lidar_mask=self.use_lidar_mask,
            use_image_mask=self.use_image_mask,
        )

        print(f"\nStarting MIoU Evaluation of layer [{layer_idx}] ...")
        frames_boarder = []
        for index, frame in enumerate(tqdm(frames)):
            if layer_idx != -1:
                frames_boarder.append(
                    {
                        "json_path": frame["json_path"],
                        "perception_result": frame["perception_result"][:, :, layer_idx, :],
                        "semantic": frame["semantic"][layer_idx],
                        "semantic_mask": frame["semantic_mask"][layer_idx],
                    }
                )
                gt_semantics, mask_camera, mask_lidar = (
                    frame["semantic"][layer_idx],
                    frame["semantic_mask"][layer_idx],
                    frame["semantic_mask"][layer_idx],
                )
                occ_pred = frame["perception_result"][:, :, layer_idx, :]  # (304, 816, 1)
            else:
                gt_semantics, mask_camera, mask_lidar = (
                    frame["semantic"],
                    frame["semantic_mask"],
                    frame["semantic_mask"],  # mask lidar not accessible
                )
                occ_pred = frame["perception_result"]
            mask_lidar = mask_lidar.astype(bool)  # not used
            mask_camera = mask_camera.astype(bool)

            self.occ_eval_metrics.add_batch(occ_pred, gt_semantics, mask_lidar, mask_camera)
            occ_pred_ = np.where(occ_pred >= self.num_classes - 1, 1, 0)
            gt_semantics_ = np.where(gt_semantics >= self.num_classes - 1, 1, 0)
            self.IoU_eval_metrics.add_batch(occ_pred_, gt_semantics_, mask_lidar, mask_camera)

        mIoU, print_str = self.occ_eval_metrics.count_miou()
        mIoU_, _ = self.IoU_eval_metrics.count_miou()
        mIoU["IoU_occupied"] = mIoU_["IoU_occupied"]
        IoU_str = "\n Occupied IoU : {0}".format(mIoU["IoU_occupied"])
        print_str += IoU_str
        logger.info(print_str)
        if not self.surround:
            print(f"\nStarting Border Evaluation of layer [{layer_idx}] ...")
            self.border_eval_metric = Metric_Border(
                VOXEL_SIZE=[[0.1, 0.1, 8]],
                GROUND_LABEL=1,
                DYNAMIC_LABEL=2,
                SEP_DISTANCE=[100, 200, 300, 400, 500, 600, 700, 800],
                EGO_GRID_CENTER=[152, 0],  # 自车中心点在BEVgrid的坐标，用于作为极坐标原点
                tp_thred=20,
                dis_norm_type="relative",
            )
            if layer_idx != -1:
                self.border_eval_metric.count_border_results(frames_boarder)
            else:
                self.border_eval_metric.count_border_results(frames)

        if eval_result_dir:
            self.save_eval_results(mIoU, refile.smart_path_join(eval_result_dir, "eval.log"))
        return mIoU, print_str

    def save_eval_results(self, res_dict, path):
        str_head = "mIoU"  # mIoU前置
        str_content = "| " + str(np.round(res_dict["mIoU"], 4))
        str_line = "| ---"
        for k, v in res_dict.items():
            if k == "mIoU":
                continue
            v = np.round(v, 4)
            str_head += "| " + k.split("IoU_")[-1]
            str_content += f"| {v}"
            str_line += "| ---"
        print_str = "\n".join([str_head, str_line, str_content])
        with refile.smart_open(path, mode="a") as f:
            f.writelines(print_str)

    def reformater(self, preds, all_annos):
        results = []
        multi_layers = False
        for i in tqdm(range(len(preds)), desc="reformating:"):
            cur_idx = self.frame_index[i]
            if not all_annos[cur_idx]["is_key_frame"]:
                continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]

            if preds[i]["perception_result"].ndim == 4:
                multi_layers = True
                all_annos[i]["perception_result"] = preds[i]["perception_result"].transpose(
                    1, 0, 2, 3
                )  # (816, 304, 1)--> (304, 816, 1)
            else:
                all_annos[i]["perception_result"] = preds[i]["perception_result"].transpose(1, 0, 2)
            if self.use_visible_head:
                all_annos[i]["vismask_pred"] = preds[i]["vismask_pred"].transpose(1, 0, 2)
            if not os.environ.get("INFER", False):
                gt = preds[cur_idx]
                if gt["semantic"].ndim == 4:
                    gt["semantic"] = gt["semantic"].squeeze(0)
                    gt["semantic_mask"] = gt["semantic_mask"].squeeze(0)
                    gt["semantic"] = np.stack(gt["semantic"], axis=0)
                    gt["semantic_mask"] = np.stack(gt["semantic_mask"], axis=0)
                    if not self.surround:
                        all_annos[i]["semantic"] = gt["semantic"][
                            :, :, (gt["semantic"].shape[2] - all_annos[i]["perception_result"].shape[1]) :
                        ]
                        all_annos[i]["semantic_mask"] = gt["semantic_mask"][
                            :, :, (gt["semantic"].shape[2] - all_annos[i]["perception_result"].shape[1]) :
                        ]
                    else:
                        all_annos[i]["semantic"] = gt["semantic"]
                        all_annos[i]["semantic_mask"] = gt["semantic_mask"]
                else:
                    if gt["semantic"].ndim == 3:
                        gt["semantic"] = gt["semantic"].squeeze(0)
                        gt["semantic_mask"] = gt["semantic_mask"].squeeze(0)
                    if self.only_front:
                        all_annos[i]["perception_result"] = all_annos[i]["perception_result"][:, -816:]
                        all_annos[i]["semantic"] = gt["semantic"][:, -816:]
                        all_annos[i]["semantic_mask"] = gt["semantic_mask"][:, -816:]

                    elif not self.surround:
                        all_annos[i]["semantic"] = gt["semantic"][
                            :, (gt["semantic"].shape[1] - all_annos[i]["perception_result"].shape[1]) :
                        ]
                        all_annos[i]["semantic_mask"] = gt["semantic_mask"][
                            :, (gt["semantic"].shape[1] - all_annos[i]["perception_result"].shape[1]) :
                        ]
                    else:
                        all_annos[i]["semantic"] = gt["semantic"]
                        all_annos[i]["semantic_mask"] = gt["semantic_mask"]

                if self.output_mode == "freepspace":
                    all_annos[i]["semantic"] = all_annos[i]["semantic"][..., None]
                    all_annos[i]["semantic_mask"] = all_annos[i]["semantic_mask"][..., None]
                if self.use_visible_head:
                    all_annos[i]["vismask_gt"] = np.zeros_like(all_annos[i]["semantic_mask"][0], dtype=np.int64)
                    all_annos[i]["vismask_gt"][all_annos[i]["semantic_mask"][0]] = 1
                    all_annos[i]["vismask_gt"] = all_annos[i]["vismask_gt"][
                        :, (all_annos[i]["vismask_gt"].shape[1] - all_annos[i]["vismask_pred"].shape[1]) :
                    ]

            all_annos[i]["json_path"] = json_path
            all_annos[i]["frame_id"] = frame_id

            results.append(all_annos[i])
        return results, multi_layers


class EvaluationSerialOccHeightLayer(EvaluationBase):
    def __init__(
        self,
        loader_output,
        annotation,
        output_mode="freespace",
        label_mapping=None,
        num_classes=-1,
        class_names=None,
        annotaion_cls=None,
        use_image_mask=False,
        use_lidar_mask=False,
        surround=False,
        only_front=True,
    ) -> None:
        super().__init__(
            loader_output,
            annotation,
            None,
            False,
        )
        assert output_mode in ["freespace", "occupancy"]
        self.output_mode = output_mode
        self.label_mapping = label_mapping
        self.num_classes = num_classes
        self.class_names = class_names
        self.annotaion_cls = annotaion_cls
        self.use_image_mask = use_image_mask
        self.use_lidar_mask = use_lidar_mask
        self.surround = surround
        self.only_front = only_front

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None, output_mode="freespace"):
        def generate_single_sample_dict(pred_dict):
            if output_mode == "freespace":
                perception_result = (
                    pred_dict["pred_seg"][..., None].cpu().numpy().astype(np.uint8)
                )  # 扩展到与occ同维度 ([816, 304]) --> (816, 304, 1)
                # vismask_pred = pred_dict["vismask_pred"][..., None].cpu().numpy().astype(np.uint8)  # 扩展到与occ同维度 ([816, 304]) --> (816, 304, 1)
            else:
                perception_result = pred_dict["pred_seg"].cpu().numpy().astype(np.uint8)
                # vismask_pred = pred_dict["vismask_pred"].cpu().numpy().astype(np.uint8)
            pred_dict["perception_result"] = perception_result  # (816, 304, 1)
            # pred_dict["vismask_pred"] = vismask_pred # (816, 304, 1)
            pred_dict.pop("pred_seg")
            # pred_dict.pop("vismask_pred")  # 不可以重复删除!
            return pred_dict

        annos = []
        for index, pred_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]  # batch id
            single_pred_dict = generate_single_sample_dict(pred_dict)
            single_pred_dict["frame_id"] = frame_id  # frame_id = 0
            annos.append(single_pred_dict)  # 有几个 batch 就加几次

            if output_path is not None:
                raise NotImplementedError
        return annos  # 'perceptron_result' & 'frame_id'

    def evalute_process(self, frames, eval_result_dir, layer_idx):
        self.occ_eval_metrics = Metric_mIoU(
            num_classes=self.num_classes,
            class_names=self.class_names,  # free, freespace, dynamic, static, water
            use_lidar_mask=self.use_lidar_mask,
            use_image_mask=self.use_image_mask,
        )
        self.IoU_eval_metrics = Metric_mIoU(
            num_classes=2,
            class_names=[
                "empty",
                "occupied",
            ],
            use_lidar_mask=self.use_lidar_mask,
            use_image_mask=self.use_image_mask,
        )

        print(f"\nStarting MIoU Evaluation of layer [{layer_idx}] ...")
        frames_boarder = []
        for index, frame in enumerate(tqdm(frames)):
            # info = self.infos[index]
            # occ_gt_path = info["occ_info"]["occ_gt_path"]
            # gt_semantics, mask_camera, mask_lidar = self.loader.load_gt_occ(occ_gt_path)
            frames_boarder.append(
                {
                    "json_path": frame["json_path"],
                    "perception_result": frame["perception_result"][:, :, layer_idx, :],
                    "semantic": frame["semantic"][layer_idx],
                    "semantic_mask": frame["semantic_mask"][layer_idx],
                }
            )
            gt_semantics, mask_camera, mask_lidar = (
                frame["semantic"][layer_idx],
                frame["semantic_mask"][layer_idx],
                frame["semantic_mask"][layer_idx],  # mask lidar not accessible
            )
            occ_pred = frame["perception_result"][:, :, layer_idx, :]  # (304, 816, 1)
            mask_lidar = mask_lidar.astype(bool)  # not used
            mask_camera = mask_camera.astype(bool)
            # occ_pred = occ_pred

            self.occ_eval_metrics.add_batch(occ_pred, gt_semantics, mask_lidar, mask_camera)
            occ_pred_ = np.where(occ_pred >= self.num_classes - 1, 1, 0)
            gt_semantics_ = np.where(gt_semantics >= self.num_classes - 1, 1, 0)
            self.IoU_eval_metrics.add_batch(occ_pred_, gt_semantics_, mask_lidar, mask_camera)

        mIoU, print_str = self.occ_eval_metrics.count_miou()
        mIoU_, _ = self.IoU_eval_metrics.count_miou()
        mIoU["IoU_occupied"] = mIoU_["IoU_occupied"]
        IoU_str = "\n Occupied IoU : {0}".format(mIoU["IoU_occupied"])
        print_str += IoU_str
        # print(print_str)
        logger.info(print_str)

        print(f"\nStarting Border Evaluation of layer [{layer_idx}] ...")
        self.border_eval_metric = Metric_Border(
            VOXEL_SIZE=[[0.1, 0.1, 8]],
            GROUND_LABEL=1,
            DYNAMIC_LABEL=2,
            SEP_DISTANCE=[100, 200, 300, 400, 500, 600, 700, 800],
            EGO_GRID_CENTER=[152, 0],  # 自车中心点在BEVgrid的坐标，用于作为极坐标原点
            tp_thred=20,
            dis_norm_type="relative",
        )
        # self.border_eval_metric.count_border_results(frames)
        self.border_eval_metric.count_border_results(frames_boarder)

        if eval_result_dir:
            self.save_eval_results(mIoU, refile.smart_path_join(eval_result_dir, "eval.log"))
        return mIoU, print_str

    def evaluation(
        self,
        occ_results,
        class_names,
        output_dir,
    ):
        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]

        eval_result_dir = refile.smart_path_join(
            output_dir, "eval_result_occ"
        )  # '/data/downloads/ckpts/occ/occ__freespace_only_1l4v_nomask_moredata_z10_1358_warp_deform_lidar_init_vx0p1/2025-02-24T13:12:39/carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps/eval_result_occ'
        # print(f"+++ occ result k:{occ_results.keys()}")
        frames = self.reformater(
            occ_results,  # [bs, ['perception_result']:[num_frames, 816, 304, 1], ['frame_id']:0], # 816, 304, 1
            all_annos,
        )
        if os.environ.get("DUMP", False):
            dump_pkl(frames, refile.smart_path_join(eval_result_dir, "shiyao_occ_results.pkl"))
        mIoU, print_str = self.evalute_process(frames, eval_result_dir, 0)
        mIoU_1, print_str_1 = self.evalute_process(frames, eval_result_dir, 1)

        return mIoU, print_str

    def save_eval_results(self, res_dict, path):
        str_head = "mIoU"  # mIoU前置
        str_content = "| " + str(np.round(res_dict["mIoU"], 4))
        str_line = "| ---"
        for k, v in res_dict.items():
            if k == "mIoU":
                continue
            v = np.round(v, 4)
            str_head += "| " + k.split("IoU_")[-1]
            str_content += f"| {v}"
            str_line += "| ---"
        print_str = "\n".join([str_head, str_line, str_content])
        with refile.smart_open(path, mode="a") as f:
            f.writelines(print_str)

    def reformater(self, preds, all_annos):

        results = []

        for i in tqdm(range(len(preds)), desc="reformating:"):
            cur_idx = self.frame_index[i]
            if not all_annos[cur_idx]["is_key_frame"]:
                continue
            json_path = self._get_scene_name(cur_idx)  # 定位 json_path
            frame_id = all_annos[cur_idx]["frame_id"]

            # print(f"== preds out keys:{preds[i].keys()}")
            all_annos[i]["perception_result"] = preds[i]["perception_result"].transpose(
                1, 0, 2, 3
            )  # (816, 304, 1)--> (304, 816, 1)
            # TODO delete hard code
            if not os.environ.get("INFER", False):
                gt = preds[cur_idx]
                # gt = gt_all[cur_idx]
                if gt["semantic"].ndim == 4:
                    gt["semantic"] = gt["semantic"].squeeze(0)
                    gt["semantic_mask"] = gt["semantic_mask"].squeeze(0)
                gt["semantic"] = np.stack(gt["semantic"], axis=0)
                gt["semantic_mask"] = np.stack(gt["semantic_mask"], axis=0)
                all_annos[i]["semantic"] = gt["semantic"][
                    :, :, (gt["semantic"].shape[2] - all_annos[i]["perception_result"].shape[1]) :
                ]
                all_annos[i]["semantic_mask"] = gt["semantic_mask"][
                    :, :, (gt["semantic"].shape[2] - all_annos[i]["perception_result"].shape[1]) :
                ]
                if self.output_mode == "freepspace":
                    all_annos[i]["semantic"] = all_annos[i]["semantic"][..., None]  # (304, 816)
                    all_annos[i]["semantic_mask"] = all_annos[i]["semantic_mask"][..., None]
            all_annos[i]["json_path"] = json_path
            all_annos[i]["frame_id"] = frame_id  # 0

            results.append(all_annos[i])

        # for j in range(len(results)):
        #     results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results
