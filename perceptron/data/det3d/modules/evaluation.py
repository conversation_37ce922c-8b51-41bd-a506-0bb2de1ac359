import bisect
import copy

import numpy as np
import refile
from scipy.spatial.transform import Rotation as R
from tqdm import tqdm
from loguru import logger

from perceptron.utils.file_io import JsonEncoder, dump_json, dump_pkl
from perceptron_eval.detection.config import config_factory
from perceptron_eval.detection.evaluation import Evaluator
from perceptron_eval.detection.reporter import (
    CamSummaryReporter,
    DistanceReporter,
    JsonReporter,
    TextReporter,
    TFboardReporter,
    DistanceWithFixedRecallReporter,
)
from perceptron_eval import (
    DetectionEvaluator,
    TrackingEvaluator,
    tracking_eval_cfg,
    PredictionEvaluator,
    prediciton_eval_cfg,
)


class EvaluationBase:
    def __init__(
        self,
        loader_output,
        annotation,
        category_map,
        dump_det_results=True,
        around_occluded_mode=False,  # 有时候存在部分相机没有遮挡属性的情况，但是遮挡属性一般是覆盖全视角的，因此只要保证输入相机环视覆盖，可遍历全部存在的遮挡属性来近似。
    ) -> None:
        self.annotation = annotation
        self.category_map = category_map
        self.dump_det_results = dump_det_results

        self.frame_data_list = loader_output["frame_data_list"]
        self.frame_index = loader_output["frame_index"]
        self.calibrated_sensors = loader_output["calibrated_sensors"]
        self.calibrated_sensors_id = loader_output["calibrated_sensors_id"]
        self.data_paths = loader_output["json_collection"]
        # copy sensor dict before undistort
        self.calibrated_sensors_dict_org = copy.deepcopy(self.calibrated_sensors)
        self.around_occluded_mode = around_occluded_mode

    @staticmethod
    def generate_prediction_dicts(batch_dict, pred_dicts, class_names, output_path=None):
        def get_template_prediction(num_samples):
            ret_dict = {
                "name": np.zeros(num_samples),
                "score": np.zeros(num_samples),
                "boxes_3d": np.zeros((num_samples, 7)),
            }
            return ret_dict

        def generate_single_sample_dict(box_dict):
            pred_scores = box_dict["pred_scores"].cpu().numpy()
            pred_boxes = box_dict["pred_boxes"].cpu().numpy()
            pred_labels = box_dict["pred_labels"].cpu().numpy()
            pred_dict = get_template_prediction(pred_scores.shape[0])
            if pred_scores.shape[0] == 0:
                return pred_dict

            pred_dict["name"] = np.array(class_names)[pred_labels]
            pred_dict["score"] = pred_scores
            pred_dict["boxes_3d"] = pred_boxes
            return pred_dict

        annos = []
        for index, box_dict in enumerate(pred_dicts):
            frame_id = batch_dict["frame_id"][index]
            single_pred_dict = generate_single_sample_dict(box_dict)
            single_pred_dict["frame_id"] = frame_id
            annos.append(single_pred_dict)

            if output_path is not None:
                raise NotImplementedError
        return annos

    def evaluation(self, det_annos, class_names, **kwargs):
        if self.dump_det_results:
            self._dump_inference_results(det_annos, **kwargs)
        return self._evaluation(det_annos, **kwargs)

    def _get_scene_name(self, idx):
        # 定位当前idx数据存在于哪个json文件中
        json_idx = bisect.bisect_right(self.frame_data_list.cumulative_sizes, idx)
        json_path = self.data_paths[json_idx]
        return json_path

    def _reverse_format(self, det_annos):
        annos = []
        for i, box_anno in enumerate(det_annos["boxes_3d"]):
            anno = {"xyz_lidar": {}, "lwh": {}, "angle_lidar": {}}
            anno["xyz_lidar"]["x"] = box_anno[0]
            anno["xyz_lidar"]["y"] = box_anno[1]
            anno["xyz_lidar"]["z"] = box_anno[2]
            anno["lwh"]["l"] = box_anno[3]
            anno["lwh"]["w"] = box_anno[4]
            anno["lwh"]["h"] = box_anno[5]
            quat = R.from_euler("xyz", (0.0, 0.0, box_anno[6])).as_quat()
            anno["angle_lidar"]["x"] = quat[0]
            anno["angle_lidar"]["y"] = quat[1]
            anno["angle_lidar"]["z"] = quat[2]
            anno["angle_lidar"]["w"] = quat[3]
            anno["score"] = det_annos["score"][i]
            anno["category"] = self.category_map[det_annos["name"][i]]
            anno["track_id"] = -1
            annos.append(anno)
        return annos

    def _add_json_path_to_annos(self, annos):
        for anno_idx, anno in enumerate(annos):
            cur_idx = self.frame_index[anno_idx]
            json_path = self._get_scene_name(cur_idx)
            frame_id = self.frame_data_list[cur_idx]["frame_id"]
            anno["json_path"] = json_path
            anno["json_frame_id"] = frame_id
        return annos

    def _dump_inference_results(self, det_annos, **kwargs):
        def dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index):
            ori_det_data = {
                "frames": ori_det_annos,
                "calibrated_sensors": calibrated_sensors,
                "key_frame_idx": key_frame_index,
            }
            json_path = self.data_paths[current_scene]
            json_file = json_path[5:]
            path_splits = json_path.split("/")
            name = ""
            for split in path_splits[-4:]:
                name = name + "-" + split
            json_file = name
            dump_json(
                ori_det_data,
                refile.smart_path_join(data_root, "%s_dt.json" % str(json_file)),
                encoder=JsonEncoder,
            )

        output_dir = kwargs["output_dir"]
        ori_gt_annos = self.frame_data_list
        ori_det_annos = []
        key_frame_index = []
        current_idx = 0
        current_scene = None
        calibrated_sensors = self.calibrated_sensors_dict_org[0]
        data_root = refile.smart_path_join(output_dir, "dump_vis")

        for i in range(len(ori_gt_annos)):
            item = copy.deepcopy(ori_gt_annos[i])
            item["labels"] = self._reverse_format(det_annos[i])
            scene = self.calibrated_sensors_id[i]
            if current_scene is None:
                current_scene = scene
            elif current_scene != scene:
                dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index)
                current_scene = scene
                ori_det_annos = []
                key_frame_index = []
                current_idx = 0
            ori_det_annos.append(item)
            if item["is_key_frame"]:
                key_frame_index.append(current_idx)
            current_idx += 1
        # dump last scene
        dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index)

    def _evaluation(self, det_annos, **kwargs):
        from perceptron_eval.evaluation.det3d.configs.dynamic_thres_distance_with_merge_4_cls import (
            EVAL_CONFIGS,
        )
        from perceptron_eval.evaluation.det3d.main import Evaluation

        gt_annos = [
            copy.deepcopy(self.annotation.get_annos(self.frame_index[idx])) for idx in range(len(self.frame_index))
        ]
        det_annos = self._add_json_path_to_annos(det_annos)
        gt_annos = self._add_json_path_to_annos(gt_annos)
        # 将gt 和 det 结果dump，供perceptron_eval使用c
        dump_pkl(det_annos, refile.smart_path_join(kwargs["output_dir"], "det_annos.pkl"))
        dump_pkl(gt_annos, refile.smart_path_join(kwargs["output_dir"], "ground_truth.pkl"))
        self.eval_cfg = EVAL_CONFIGS

        if "vel_err" in self.eval_cfg["tp_metrics"]:
            pop_idx = self.eval_cfg["tp_metrics"].index("vel_err")
            self.eval_cfg["tp_metrics"].pop(pop_idx)
        if "vel_err" in self.eval_cfg["tp_metrics_err_ratio"]:
            self.eval_cfg["tp_metrics_err_ratio"].pop("vel_err")
        assert isinstance(self.eval_cfg["roi"]["expected_recall"], (list, tuple))
        eval_output_dir = refile.smart_path_join(
            kwargs["output_dir"],
            "eval_results",
        )
        eval_obj = Evaluation(
            eval_cfg=self.eval_cfg,
            det_annos=det_annos,
            gt_annos=gt_annos,
            output_dir=eval_output_dir,
            use_cache=False,
        )
        metrics_summary = eval_obj.eval()
        ap_dict = metrics_summary

        return None, ap_dict


class EvaluationV2(EvaluationBase):
    def __init__(
        self,
        loader_output,
        annotation,
        category_map,
        dump_det_results=True,
        eval_cfg_l3="cam_l3",
        eval_cfg_l2="cam_l2",
        dist_bins=[-30, 70, 10],
        around_occluded_mode=False,
    ):
        super().__init__(
            loader_output=loader_output,
            annotation=annotation,
            category_map=category_map,
            dump_det_results=dump_det_results,
        )
        self.eval_cfg_l3 = eval_cfg_l3
        self.eval_cfg_l2 = eval_cfg_l2
        # self.dist_bins = list(range(*dist_bins))
        self.around_occluded_mode = around_occluded_mode

    def reformater(self, det_annos, calibrated_sensors_dict, calibrated_sensors_id, all_annos):
        results = [
            {"frames": [], "calibrated_sensors": {}, "json_path": None} for i in range(len(set(calibrated_sensors_id)))
        ]

        for i in tqdm(range(len(det_annos)), desc="reformating:"):
            cur_idx = self.frame_index[i]
            if not all_annos[cur_idx]["is_key_frame"]:
                continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]
            preds = []
            for j in range(len(det_annos[i]["boxes_3d"])):
                d = {}
                d["det_score"] = det_annos[i]["score"][j]
                d["xyz_lidar"] = {}
                d["xyz_lidar"]["x"] = det_annos[i]["boxes_3d"][j][0]
                d["xyz_lidar"]["y"] = det_annos[i]["boxes_3d"][j][1]
                d["xyz_lidar"]["z"] = det_annos[i]["boxes_3d"][j][2]
                d["lwh"] = {}
                d["lwh"]["l"] = det_annos[i]["boxes_3d"][j][3]
                d["lwh"]["w"] = det_annos[i]["boxes_3d"][j][4]
                d["lwh"]["h"] = det_annos[i]["boxes_3d"][j][5]
                angle = R.from_euler(seq="z", angles=det_annos[i]["boxes_3d"][j][6]).as_quat()
                d["angle_lidar"] = {}
                d["angle_lidar"]["x"] = angle[0]
                d["angle_lidar"]["y"] = angle[1]
                d["angle_lidar"]["z"] = angle[2]
                d["angle_lidar"]["w"] = angle[3]
                d["category"] = det_annos[i]["name"][j]
                d["json_path"] = json_path
                d["json_frame_id"] = frame_id
                preds.append(d)
            if "labels" not in all_annos[i]:
                all_annos[i]["labels"] = all_annos[i]["pre_labels"] if "pre_labels" in all_annos[i] else []
            for j in range(len(all_annos[i]["labels"])):
                gt_box = all_annos[i]["labels"][j]
                gt_box["json_path"] = json_path
                gt_box["json_frame_id"] = frame_id
                # Mark occlusion
                if self.annotation._get_occlusion_attr(
                    gt_box,
                    camera_keys=self.annotation.camera_keys,
                    around_occluded_mode=self.around_occluded_mode,
                ):
                    gt_box["is_occluded"] = False
                else:
                    gt_box["is_occluded"] = True

            all_annos[i]["preds"] = preds
            results[calibrated_sensors_id[i]]["frames"].append(all_annos[i])
            results[calibrated_sensors_id[i]]["json_path"] = json_path

        for j in range(len(results)):
            results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results

    def _evaluation(self, det_annos, **kwargs):

        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]

        eval_output_dir = refile.smart_path_join(
            kwargs["output_dir"],
            "eval_results",
        )

        frames = self.reformater(det_annos, self.calibrated_sensors, self.calibrated_sensors_id, all_annos)
        dump_pkl(frames, refile.smart_path_join(eval_output_dir, "eval_frames.pkl"))

        reporters = [
            JsonReporter(eval_output_dir),
            TFboardReporter(eval_output_dir),
            DistanceReporter(save_path=eval_output_dir),  # bins=self.dist_bins
            TextReporter(eval_output_dir),
            CamSummaryReporter(eval_output_dir),
        ]

        eval_obj = Evaluator(config_factory(self.eval_cfg_l3), reporters)
        eval_obj.eval(frames, frames)

        reporters = [JsonReporter(eval_output_dir), TextReporter(eval_output_dir)]
        eval_obj = Evaluator(config_factory(self.eval_cfg_l2), reporters)
        eval_obj.eval(frames, frames)
        return None, {}


class EvaluationV3(EvaluationV2):
    def __init__(
        self,
        loader_output,
        annotation,
        category_map,
        dump_det_results=True,
        eval_cfg_l3="cam_l3",
        eval_cfg_l2="cam_l2",
        dist_bins=[-30, 70, 10],
        around_occluded_mode=False,
        extra_eval_cfgs=[],
    ):
        super(EvaluationV3, self).__init__(
            loader_output,
            annotation,
            category_map,
            dump_det_results,
            eval_cfg_l3,
            eval_cfg_l2,
            dist_bins,
            around_occluded_mode,
        )
        self.extra_eval_cfgs = extra_eval_cfgs

    def _evaluation(self, det_annos, **kwargs):
        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]

        eval_output_dir = refile.smart_path_join(
            kwargs["output_dir"],
            "eval_results_od",
        )

        frames = self.reformater(det_annos, self.calibrated_sensors, self.calibrated_sensors_id, all_annos)

        dump_pkl(frames, refile.smart_path_join(eval_output_dir, "eval_frames.pkl"))  # saved to vis

        reporters = [
            JsonReporter(eval_output_dir),
            # TFboardReporter(eval_output_dir),
            # DistanceWithFixedRecallReporter(save_path=eval_output_dir),  # bins=self.dist_bins
            TextReporter(eval_output_dir),
        ]

        if self.eval_cfg_l3:
            if len(self.extra_eval_cfgs) == 0:
                reporters.append(DistanceWithFixedRecallReporter(save_path=eval_output_dir, name=self.eval_cfg_l3))
            eval_obj = Evaluator(config_factory(self.eval_cfg_l3), reporters)
            eval_obj.eval(frames, frames)

        if self.eval_cfg_l2:
            reporters = [
                JsonReporter(eval_output_dir),
                TextReporter(eval_output_dir),
                # TFboardReporter(eval_output_dir, "near"),
                DistanceWithFixedRecallReporter(save_path=eval_output_dir, name=self.eval_cfg_l2),
            ]
            eval_obj = Evaluator(config_factory(self.eval_cfg_l2), reporters)
            eval_obj.eval(frames, frames)

        for extra_eval_cfg in self.extra_eval_cfgs:
            reporters = [TextReporter(refile.smart_path_join(eval_output_dir, extra_eval_cfg))]
            if getattr(config_factory(extra_eval_cfg), "bins", False):
                reporters.append(
                    DistanceWithFixedRecallReporter(save_path=refile.smart_path_join(eval_output_dir, extra_eval_cfg))
                )
            eval_obj = Evaluator(config_factory(extra_eval_cfg), reporters)
            eval_obj.eval(frames, frames)
        return None, {}


class EvaluationE2E(EvaluationV3):
    def __init__(
        self,
        loader_output,
        annotation,
        category_map,
        dump_det_results=True,
        eval_cfg_l3="cam_l3",
        eval_cfg_l2="cam_l2",
        dist_bins=[-30, 70, 10],
        around_occluded_mode=False,
        hist_len=3,
        pred_len=8,
    ):
        super(EvaluationE2E, self).__init__(
            loader_output,
            annotation,
            category_map,
            dump_det_results,
            eval_cfg_l3,
            eval_cfg_l2,
            dist_bins,
            around_occluded_mode,
        )
        self.class_names = [
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "motorcycle",
            "bicycle",
            "tricycle",
            "cyclist",
            "pedestrian",
            # "masked_area",
        ]
        self.hist_len = hist_len
        self.pred_len = pred_len

    def reformater(self, det_annos, calibrated_sensors_dict, calibrated_sensors_id, all_annos):
        """
        args:
            det_annos: list(frame_nums)[
                dict{
                    'boxes_3d': .tensor torch.Size([300, 9])
                    'scores_3d': torch.Size([300])
                    'labels_3d': torch.Size([300])
                }
            ]
        """
        results = [
            {
                "frames": [],
                "calibrated_sensors": {},
                "json_path": None,
            }
            for i in range(len(set(calibrated_sensors_id)))
        ]

        for i in tqdm(range(len(det_annos)), desc="reformating:"):
            cur_idx = self.frame_index[i]
            # if not all_annos[cur_idx]["is_key_frame"]:
            #     continue
            json_path = self._get_scene_name(cur_idx)
            frame_id = all_annos[cur_idx]["frame_id"]
            preds = []
            boxes_3d = det_annos[i]["boxes_3d"].tensor
            for j in range(len(det_annos[i]["boxes_3d"])):
                d = {}
                d["det_score"] = det_annos[i]["scores_3d"][j].tolist()
                d["xyz_lidar"] = {}
                d["xyz_lidar"]["x"] = boxes_3d[j][0].tolist()
                d["xyz_lidar"]["y"] = boxes_3d[j][1].tolist()
                d["xyz_lidar"]["z"] = (boxes_3d[j][2] + boxes_3d[j][5] / 2.0).tolist()
                d["lwh"] = {}
                d["lwh"]["l"] = boxes_3d[j][3].tolist()
                d["lwh"]["w"] = boxes_3d[j][4].tolist()
                d["lwh"]["h"] = boxes_3d[j][5].tolist()
                angle = R.from_euler(seq="z", angles=boxes_3d[j][6]).as_quat()
                d["angle_lidar"] = {}
                d["angle_lidar"]["x"] = angle[0]
                d["angle_lidar"]["y"] = angle[1]
                d["angle_lidar"]["z"] = angle[2]
                d["angle_lidar"]["w"] = angle[3]
                d["velocity_lidar"] = {}
                if "velocity" not in det_annos[i].keys():
                    d["velocity_lidar"]["x"] = boxes_3d[j][7].tolist()
                    d["velocity_lidar"]["y"] = boxes_3d[j][8].tolist()
                    d["velocity_lidar"]["z"] = 0
                else:
                    d["velocity_lidar"]["x"] = det_annos[i]["velocity"][j][0][0].tolist()
                    d["velocity_lidar"]["y"] = det_annos[i]["velocity"][j][0][1].tolist()
                    d["velocity_lidar"]["z"] = 0
                d["category"] = self.class_names[det_annos[i]["labels_3d"][j]]

                # overwrite
                if "track_scores" in det_annos[i].keys():
                    d["det_score"] = det_annos[i]["track_scores"][j].tolist()

                # (T，2) ： offset relative to the obstacle position in current frame
                if "forecasting" in det_annos[i].keys():
                    d["forecasting"] = det_annos[i]["forecasting"][j].tolist()
                if "velocity" in det_annos[i].keys():
                    d["velocity_forecasting"] = det_annos[i]["velocity"][j].tolist()  # all future frames
                d["track_id"] = det_annos[i]["track_ids"][j].tolist() if "track_scores" in det_annos[i].keys() else -1

                d["json_path"] = json_path
                d["json_frame_id"] = frame_id
                preds.append(d)

            if "labels" in all_annos[i]:
                for j in range(len(all_annos[i]["labels"])):
                    gt_box = all_annos[i]["labels"][j]
                    gt_box["json_path"] = json_path
                    gt_box["json_frame_id"] = frame_id
                    # Mark occlusion
                    if self.annotation._get_occlusion_attr_val(
                        gt_box,
                        camera_keys=self.annotation.camera_keys,
                        around_occluded_mode=self.around_occluded_mode,
                    ):
                        gt_box["is_occluded"] = False
                    else:
                        gt_box["is_occluded"] = True

            all_annos[i]["preds"] = preds
            results[calibrated_sensors_id[i]]["frames"].append(all_annos[i])
            results[calibrated_sensors_id[i]]["json_path"] = json_path

        for j in range(len(results)):
            results[j]["calibrated_sensors"] = calibrated_sensors_dict[j]

        return results

    def _dump_inference_results(self, det_annos, **kwargs):
        def dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index):
            ori_det_data = {
                "frames": ori_det_annos,
                "calibrated_sensors": calibrated_sensors,
                "key_frame_idx": key_frame_index,
            }
            json_path = self.data_paths[current_scene]
            save_path = refile.smart_path_join(data_root, *refile.SmartPath(json_path).parts[5:])
            dump_json(ori_det_data, save_path, encoder=JsonEncoder)

        output_dir = kwargs["output_dir"]
        ori_gt_annos = self.frame_data_list
        ori_det_annos = []
        key_frame_index = []
        current_idx = 0
        current_scene = None
        calibrated_sensors = self.calibrated_sensors_dict_org[0]
        data_root = refile.smart_path_join(output_dir, "gt_dt")

        for i in range(len(ori_gt_annos)):
            frame_info = copy.deepcopy(ori_gt_annos[i])
            frame_info["pre_labels_camera"] = self._reverse_format(det_annos[i])
            scene = self.calibrated_sensors_id[i]
            if current_scene is None:
                current_scene = scene
            elif current_scene != scene:
                dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index)
                current_scene = scene
                ori_det_annos = []
                key_frame_index = []
                current_idx = 0
            ori_det_annos.append(frame_info)
            # if frame_info["is_key_frame"]:
            #     key_frame_index.append(current_idx)
            key_frame_index.append(current_idx)
            current_idx += 1
        # dump last scene
        dump_one_scene(ori_det_annos, calibrated_sensors, data_root, current_scene, key_frame_index)

    def format_and_dump(self, det_annos, **kwargs):
        # dump to json in the fomart of pre_labels
        if self.dump_det_results:
            self._dump_inference_results(det_annos, **kwargs)

        all_annos = [
            self.frame_data_list[i].copy() for i in tqdm(range(len(self.frame_data_list)), desc="[Load all_annos]")
        ]

        eval_output_dir = refile.smart_path_join(
            kwargs["jsonfile_prefix"],
            "eval_results",
        )

        frames = self.reformater(det_annos, self.calibrated_sensors, self.calibrated_sensors_id, all_annos)
        dump_pkl(frames, refile.smart_path_join(eval_output_dir, "eval_frames.pkl"))

        return frames, eval_output_dir

    def evaluate_detection(self, det_annos, frames, eval_output_dir, **kwargs):
        reporters = [
            JsonReporter(eval_output_dir),
            TFboardReporter(eval_output_dir),
            DistanceWithFixedRecallReporter(save_path=eval_output_dir),  # bins=self.dist_bins
            TextReporter(eval_output_dir),
        ]

        eval_obj = DetectionEvaluator(config_factory(self.eval_cfg_l3), reporters)
        eval_obj.eval(frames, frames)

        reporters = [JsonReporter(eval_output_dir), TextReporter(eval_output_dir)]
        eval_obj = DetectionEvaluator(config_factory(self.eval_cfg_l2), reporters)
        eval_obj.eval(frames, frames)

    def evaluate(self, det_annos, **kwargs):
        frames, eval_output_dir = self.format_and_dump(det_annos, **kwargs)
        self.evaluate_detection(det_annos, frames, eval_output_dir, **kwargs)
        return None, {}

    def evaluate_tracking(self, det_annos, **kwargs):
        frames, eval_output_dir = self.format_and_dump(det_annos, **kwargs)
        if "detection" in kwargs["eval_cfg"]["eval_ppl"]:
            self.evaluate_detection(det_annos, frames, eval_output_dir, **kwargs)

        if "tracking" in kwargs["eval_cfg"]["eval_ppl"]:
            for eval_level in tracking_eval_cfg.eval_levels:
                track_eval = TrackingEvaluator(
                    out_dir=eval_output_dir,
                    eval_level=eval_level,
                    eval_cfg=tracking_eval_cfg,
                    dt_label="preds",
                    gt_label="labels",
                )
                track_eval_res = track_eval.eval_multi_scenes(frames)
                logger.info(f"track_eval_res: {eval_level}\n {track_eval_res}")

        if "prediction" in kwargs["eval_cfg"]["eval_ppl"]:
            prediciton_eval_cfg.hist_len = self.hist_len
            prediciton_eval_cfg.pred_len = self.pred_len
            pred_eval = PredictionEvaluator(
                out_dir=eval_output_dir, eval_cfg=prediciton_eval_cfg, dt_label="preds", gt_label="labels"
            )
            _, metric_table_str = pred_eval.eval_multi_scenes(frames)
            logger.info(f"prediction_eval_res:\n,{metric_table_str[0]}")

        logger.info(f"eval results saved in :{eval_output_dir}")

        return None, {}


if __name__ == "__main__":
    import pickle

    frames = pickle.load(
        refile.smart_open(
            "/data/Envs/megvii/outputs/fusion__det_only_7v5r_bsl_9w6_y200m_x32m_80e_dn_vr_loadwmpt_80e/2024-09-06T15:11:29/eval_results/eval_frames.pkl",
            "rb",
        )
    )
    eval_output_dir = "/data/Envs/megvii/outputs/fusion__det_only_7v5r_bsl_9w6_y200m_x32m_80e_dn_vr_loadwmpt_80e/2024-09-06T15:11:29/eval_frames_full_roi_test"
    reporters = [
        JsonReporter(eval_output_dir),
        TFboardReporter(eval_output_dir),
        DistanceWithFixedRecallReporter(save_path=eval_output_dir),  # bins=self.dist_bins
        TextReporter(eval_output_dir),
    ]

    eval_obj = Evaluator(config_factory("e2e_l3_far"), reporters)
    eval_obj.eval(frames, frames)

    reporters = [JsonReporter(eval_output_dir), TextReporter(eval_output_dir)]
    eval_obj = DetectionEvaluator(config_factory("e2e_gaosu_l3"), reporters)
    eval_obj.eval(frames, frames)
