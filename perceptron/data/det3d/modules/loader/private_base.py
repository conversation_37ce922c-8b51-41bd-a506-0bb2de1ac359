import bisect
import json
import os
import gc
import copy
import refile
import time
import random
import zipfile
import tempfile
import shutil

from tqdm import tqdm
from loguru import logger
from urllib.parse import urlparse
import numpy as np
import pandas as pd
import concurrent.futures
from collections import defaultdict


from torch.utils.data.dataset import Dataset, ConcatDataset

from perceptron.data.det3d.utils.redis_cache_stepmind import RedisCachedData, OSSEtagHelper

from perceptron.utils.file_io import load_json, load_json_accel
from perceptron.data.det3d.utils.functional import trans_oss_to_gpfs_path

from perceptron.data.det3d.utils.local_cache import LocalCache, LocalCacheCalib, create_cache, hash_dict
from perceptron.data.det3d.modules.utils.distributed import is_master

from perceptron.utils.env import get_cluster


USE_REDIS = False  # Set to False to use local cache. local cache is slower when gpfs json file is not available
USE_LOCAL_CACHE = True
CHECK_SUM = False  # Set to True only for debug, otherwise it will slow down the process

if not USE_REDIS and USE_LOCAL_CACHE:
    path_sizes_cache = LocalCacheCalib("/data/cache/loader_v3", key="loader_cache_v3", init=False)
else:
    path_sizes_cache = None

INVALID_BUCKETS = ["plat-hub-bmk", "plat-hub-train"]


class CachePath:
    # 使用独立的 cache 实例，避免污染 FrameDataset 对象，造成 dataloader Copy-On-Write
    def __init__(self):
        self.cur_path = None
        self.cur_data = None
        
worker_cache = CachePath()


def get_bucket_name(path):
    parsed = urlparse(path)
    return parsed.netloc


def get_gpfs_renori_path(path):
    """
    尝试获取GPFS下的renori路径
    """
    if path.endswith(".json"):
        # gpfs_renori_path = path.replace('.json', '_renori.json').replace("s3://", "/mnt/acceldata/dynamic/")
        if "_renori.json" not in path:
            gpfs_renori_path = trans_oss_to_gpfs_path(path.replace(".json", "_renori.json"))
        else:
            gpfs_renori_path = trans_oss_to_gpfs_path(path)
        bucket_name = get_bucket_name(path)
        if bucket_name in INVALID_BUCKETS:
            # 特例处理，针对特定的bucket
            gpfs_renori_path = gpfs_renori_path.replace(bucket_name, bucket_name + "-acc")

        if refile.smart_exists(gpfs_renori_path):
            return gpfs_renori_path

        return path

    print(f"Path is not a valid JSON file or does not end with '.json': {path}")
    logger.debug(f"Path is not a valid JSON file or does not end with '.json': {path}")
    return None


# 尝试使用，但是失败了
# 目前的思路是通过Collection把相同的参数合并到同一个内存地址，再把calibrated_sensors里的key-value对中value相同但内存不同的值改成value相同的值，但是可能是导致多个数据对同一地址的value进行了修改，导致后续处理过程中出现读取到的图像为None的情况，需要进一步寻找可行方法
class CalibratedSensorsCollection:
    def __init__(self):
        self.values: dict[str, object] = {}
        self.calibrated_sensors: dict[int, dict[str, list[str]]] = {}

    def __getitem__(self, idx):
        ret = {}
        for key, hash_value in self.calibrated_sensors[idx].items():
            ret[key] = self.values[hash_value]
        return ret

    def __setitem__(self, idx, dict_value: dict):
        self.calibrated_sensors[idx] = {}
        for key, value in dict_value.items():
            hash1 = hash_dict(value)
            if hash1 in self.values.keys():
                assert value == self.values[hash1], "duplicated hash value"
            else:
                self.values[hash1] = value
            self.calibrated_sensors[idx][key] = hash1

    def set(self, idx, key, value):
        hash1 = hash_dict(value)
        if hash1 in self.values.keys():
            assert value == self.values[hash1], "duplicated hash value"
        else:
            self.values[hash1] = value
        self.calibrated_sensors[idx][key] = hash1

    def keys(self):
        return self.calibrated_sensors.keys()


class CalibratedSensorsCacheCollection(CalibratedSensorsCollection):
    def __init__(self):
        super().__init__()
        self.calib_cache = LocalCache("/data/cache/calib", key="calib")
        self.in_cache_index = {}

    def __setitem__(self, idx, dict_key: str):
        self.calibrated_sensors[idx] = {}
        if dict_key in self.in_cache_index:
            self.calibrated_sensors[idx] = self.in_cache_index[dict_key]
        else:
            self.in_cache_index[dict_key] = {}
            dict_value = self.calib_cache[dict_key]
            for key, value in dict_value.items():
                hash_value = hash_dict(value)
                if hash_value in self.values.keys():
                    assert value == self.values[hash_value], "duplicated hash value"
                else:
                    self.values[hash_value] = value
                self.calibrated_sensors[idx][key] = hash_value
                self.in_cache_index[dict_key][key] = hash_value


class FrameDataset(Dataset):
    """
    通过添加一个中间层，实现延迟读取的目的
    具体来讲，对数据的访问从直接访问本地缓存的redis 改为 通过预处理的结果将访问转换到路径，再从路径创建redis访问数据
    后续如果需要修改访问格式（如跳过redis），可以直接修改 `_get_item_from_path` 方法
    """

    def __init__(
        self,
        num_json_path: int,
        redis_param,
        key_frame_only: bool,
        dataset_names=None,
        log_bad_data=False,
        log_bad_data_filename="bad_data_info",
    ):
        self.dataset_paths = [None] * num_json_path
        self.frame_len_list = np.zeros((num_json_path,), dtype=np.int32)
        self.cum_idx: list[int] = []
        self.redis_param = redis_param
        self.key_frame_only: bool = key_frame_only
        self.cur_path = None
        self.cur_data = None

        # 添加has_nori标志，避免COW问题
        self.has_nori = False

        self.useless_data = 0
        self.dataset_names = dataset_names
        self.log_bad_data = log_bad_data
        self.log_bad_data_filename = log_bad_data_filename

        if USE_REDIS:
            self.oss_etag_helper = OSSEtagHelper(check_etag=False)

    def cal_useless_data(self, index, reason, module=None):
        """
        计算无效数据的数量，只在 CAL 模式下进行统计，
        并输出问题数据的 s3 path、sample_idx、原因及当前可用数据量（len(frame_data_list)）。
        每次调用时只把新增的数据追加到 Excel 表格中，防止漏统计。

        参数:
        index: 当前待处理样本的索引
        reason: 标记无效数据的原因（字符串描述）
        """
        # logger.info(f"cal useless data for index {index}, reason: {reason}")

        # 仅在 CAL 模式下执行
        if not self.log_bad_data:
            return None

        # 从 loader_output 中获取 frame_data_list，并解析出 s3_path 和 sample_idx
        # frame_data_list = self.loader_output['frame_data_list']
        s3_path, sample_idx = self.frame_info(index)

        # 更新无效数据计数（仅计数，不在内存中累积所有记录）
        if not hasattr(self, "useless_data"):
            self.useless_data = 0
        self.useless_data += 1
        available_num = self.__len__()

        # 构造当前记录
        record = {
            "s3_path": s3_path,
            "sample_idx": sample_idx,
            "module": module,
            "reason": reason,
            "available_num": available_num,
        }
        # 输出调试信息，包括无效数据计数、s3_path、sample_idx、原因以及当前可用数据量
        print(
            f"useless data count: {self.useless_data}, s3_path: {s3_path}, "
            f"sample_idx: {sample_idx}, reason: {reason}, available data: {available_num}"
        )

        excel_path = os.path.join(os.getcwd(), f"useless_data_{self.log_bad_data_filename}.xlsx")

        # Create a temporary file to avoid corruption
        temp_file = tempfile.NamedTemporaryFile(delete=False, mode="w", newline="")

        try:
            if os.path.exists(excel_path):
                # 如果文件存在，读取已有数据，再追加当前记录
                try:
                    existing_df = pd.read_excel(excel_path, engine="openpyxl")
                except (FileNotFoundError, zipfile.BadZipFile, Exception) as e:
                    print(
                        f"Warning: Failed to load existing excel file {excel_path}. Error: {e}. "
                        "A new file will be created."
                    )
                    existing_df = pd.DataFrame()
                new_df = pd.DataFrame([record])
                self.existing_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                # 如果文件不存在，则直接用当前记录创建 DataFrame
                self.existing_df = pd.DataFrame([record])

            # Save to temporary file
            self.existing_df.to_excel(temp_file.name, index=False, engine="openpyxl")

            # After saving to temporary file, move it to the target location
            shutil.move(temp_file.name, excel_path)

            print(f"Excel file updated: {excel_path}")

        except Exception as e:
            print(f"Error occurred while saving Excel file: {e}")
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file.name):
                os.remove(temp_file.name)

        return None

    def __len__(self) -> int:
        return self.cum_idx[-1]

    def add(self, path_idx: int, path: str, length: int):
        """
        Add a dataset path and its length to the dataset paths and cumulative index.
        If the path already exists, it will not be added again.
        """
        self.frame_len_list[path_idx] = length
        self.dataset_paths[path_idx] = path

    def finalize(self):
        """
        Finalize the dataset paths and cumulative index after all paths have been added.
        This method should be called after all `add` calls are done.
        """
        assert all(self.dataset_paths), "All dataset paths should be added before finalizing."
        self.cum_idx = np.cumsum(self.frame_len_list.astype(np.int64))
        self.cum_idx = self.cum_idx.tolist()

        # 检测数据集是否使用nori格式，避免后续COW问题
        self._detect_has_nori()

    def _detect_has_nori(self):
        """
        检测数据集是否包含has_nori字段，在初始化时设置标志避免COW问题
        """
        if len(self.dataset_paths) == 0:
            return

        # 检查第一个数据集文件来确定是否使用nori
        try:
            first_path = self.dataset_paths[0]
            if USE_REDIS:
                if get_cluster() == "https://qy.machdrive.cn":
                    if refile.smart_exists(trans_oss_to_gpfs_path(first_path)):
                        first_path = trans_oss_to_gpfs_path(first_path)
                json_data = RedisCachedData(first_path, oss_etag_helper=self.oss_etag_helper, **self.redis_param)
            else:
                try:
                    json_data = load_json_accel(first_path)
                except Exception:
                    json_data = load_json(first_path)

            # 检测has_nori字段
            self.has_nori = json_data.get("has_nori", False)

        except Exception as e:
            # 如果检测失败，默认为False
            self.has_nori = False
            print(f"Warning: Failed to detect has_nori flag: {e}")

    def _get_item_from_path(self, dataset_idx, sample_idx):
        path = self.dataset_paths[dataset_idx]
        if path != worker_cache.cur_path:
            if USE_REDIS:
                try:
                    if get_cluster() == "https://qy.machdrive.cn":
                        if refile.smart_exists(trans_oss_to_gpfs_path(path)):  # 注意json不在gpfs上的情况
                            path = trans_oss_to_gpfs_path(path)
                    json_data = RedisCachedData(path, oss_etag_helper=self.oss_etag_helper, **self.redis_param)
                    self.oss_etag_helper.join()
                except RuntimeError as e:
                    print(f"Error loading {path}: {e}")
                    rebuild = self.redis_param.get("rebuild", False)
                    self.redis_param["rebuild"] = True
                    json_data = RedisCachedData(path, oss_etag_helper=self.oss_etag_helper, **self.redis_param)
                    self.redis_param["rebuild"] = rebuild
                    self.oss_etag_helper.join()
            else:
                try:
                    json_data = load_json_accel(path)
                except Exception:
                    pass
                    # logger.warning(f"load json {path} on gpfs failed, use s3 path instead.")
                    json_data = load_json(path)
                json_data["key_frame_idx"] = [i for i, x in enumerate(json_data["frames"]) if x["is_key_frame"]]
            worker_cache.cur_data = json_data
            worker_cache.cur_path = path

        if self.key_frame_only:
            sample_idx = worker_cache.cur_data["key_frame_idx"][sample_idx]
        return worker_cache.cur_data["frames"][sample_idx]

    @property
    def cumulative_sizes(self):
        return self.cum_idx

    @property
    def cummulative_sizes(self):
        return self.cum_idx

    def __getitem__(self, index: int):
        """
        when self.cum_idx = [0, 5, 10, 15, 20]
        if search for index = 9
        path_idx = 1, sample_idx = 9 - 5 = index - self.cum_idx[path_idx]
        """
        dataset_idx = bisect.bisect_right(a=self.cum_idx, x=index)
        if dataset_idx == 0:
            sample_idx = index
        else:
            sample_idx = index - self.cum_idx[dataset_idx - 1]
        assert sample_idx >= 0, "error at get data from frames"
        ret = self._get_item_from_path(dataset_idx, sample_idx)
        return ret

    def frame_info(self, index):
        dataset_idx = bisect.bisect_right(a=self.cum_idx, x=index)
        if dataset_idx == 0:
            sample_idx = index
        else:
            sample_idx = index - self.cum_idx[dataset_idx - 1]
        assert sample_idx >= 0, "error at get data from frames"
        # ret = self._get_item_from_path(dataset_idx, sample_idx)

        path = self.dataset_paths[dataset_idx]
        if path != self.cur_path:
            if "s3://tf-rhea-data-bpp" in path and get_cluster() == "https://qy.machdrive.cn":
                path = path.replace("s3://", "/mnt/")
            if USE_REDIS:
                try:
                    if get_cluster() == "https://qy.machdrive.cn":
                        if refile.smart_exists(trans_oss_to_gpfs_path(path)):  # 注意json不在gpfs上的情况
                            path = trans_oss_to_gpfs_path(path)
                    json_data = RedisCachedData(path, oss_etag_helper=self.oss_etag_helper, **self.redis_param)
                    self.oss_etag_helper.join()
                except RuntimeError as e:
                    print(f"Error loading {path}: {e}")
                    rebuild = self.redis_param.get("rebuild", False)
                    self.redis_param["rebuild"] = True
                    json_data = RedisCachedData(path, oss_etag_helper=self.oss_etag_helper, **self.redis_param)
                    self.redis_param["rebuild"] = rebuild
                    self.oss_etag_helper.join()
            else:
                json_data = load_json(path)
                json_data["key_frame_idx"] = [i for i, x in enumerate(json_data["frames"]) if x["is_key_frame"]]
            self.cur_data = json_data
            self.cur_path = path

        if self.key_frame_only:
            sample_idx = self.cur_data["key_frame_idx"][sample_idx]

        return path, sample_idx


class LoaderBase:
    def __init__(
        self,
        car,
        camera_names,
        mode,
        datasets_names,
        only_key_frame=True,
        rebuild=False,
        log_bad_data=False,
        log_bad_data_filename=None,
    ) -> None:
        self.car = car
        self.camera_names = camera_names
        self.mode = mode
        self.datasets_names = datasets_names
        self.only_key_frame = only_key_frame
        self.redis_param = {"rebuild": rebuild}
        self._parse_camera_name_mapping()
        if USE_REDIS:
            self.oss_etag_helper = OSSEtagHelper(check_etag=False)
        else:
            self.path_sizes = dict()
        self.log_bad_data = log_bad_data
        self.log_bad_data_filename = log_bad_data_filename

    @property
    def output(self):
        return self._loader_out

    @property
    def camera_name_mapping(self):
        return self._camera_name_mapping

    def _load_primary_paths(self, dataset_name):
        if self.mode == "train":
            json_path = self.car.trainset_partial[dataset_name]
        else:
            if dataset_name not in self.car.benchmark_partial:
                json_path = [dataset_name]
            else:
                json_path = self.car.benchmark_partial[dataset_name]

        if isinstance(json_path, str):
            if os.path.exists(trans_oss_to_gpfs_path(json_path)):
                json_path = trans_oss_to_gpfs_path(json_path)
            json_data = json.load(refile.smart_open(json_path))
            if isinstance(json_data, list):
                json_paths = json_data
            elif isinstance(json_data, dict) and "paths" not in json_data.keys():
                json_paths = []
                for key in json_data.keys():
                    json_paths.append(key)
            else:
                json_paths = json_data["paths"]
                # assert "information" in json_data and json_data["information"], "dataset should be described."
                # assert "author" in json_data and json_data["author"], "author should be declared."
        elif isinstance(json_path, list):
            json_paths = json_path
        else:
            raise TypeError("Only json file (str type) and json list (list type) are supported!")

        assert len(json_paths) > 0, f"There should be more than one json in {json_path}"
        return json_paths

    def _parse_name_to_json_path(self):
        json_collection = []
        for dataset_name in self.datasets_names:
            primary_paths = self._load_primary_paths(dataset_name)

            if not USE_REDIS and USE_LOCAL_CACHE:
                local_key = hash_dict(primary_paths)
                master_proc = is_master()
                while local_key not in path_sizes_cache:
                    if master_proc:
                        path_sizes_cache[local_key] = create_cache(primary_paths)
                    else:
                        time.sleep(2)
                try:
                    self.path_sizes.update(path_sizes_cache[local_key])
                except Exception:
                    from loguru import logger

                    logger.error(f"local_key is {local_key}")

                if path_sizes_cache.calib_cache.rebuild:
                    path_sizes_cache[local_key] = create_cache(primary_paths)

                if CHECK_SUM:
                    rebuild_cache = False
                    for path in tqdm(primary_paths, desc="check md5"):
                        if refile.smart_getmd5(path) != self.path_sizes[path]["md5"]:
                            print(f"md5 check failed for {path}, the cache will be rebuilt")
                            rebuild_cache = True
                    if rebuild_cache:
                        del path_sizes_cache[local_key]
                    while local_key not in path_sizes_cache:
                        if master_proc:
                            path_sizes_cache[local_key] = create_cache(primary_paths)
                        else:
                            time.sleep(2)
                    self.path_sizes.update(path_sizes_cache[local_key])

            if os.environ.get("DEBUG", False):
                primary_paths = primary_paths[::50]
            for primary_path in primary_paths:
                # assert refile.smart_exists(primary_path), "dataset path don't exists."
                if primary_path.endswith("json"):
                    json_collection.append(primary_path)
                else:
                    json_paths = refile.smart_glob(os.path.join(primary_path, "**[0-9].json"))
                    json_collection.extend(json_paths)
        json_collection = list(set(json_collection))  # Remove duplicates
        json_collection.sort()
        return json_collection

    def _parse_camera_name_mapping(self):
        camera_name_mapping_h2s = defaultdict(dict)
        camera_name_mapping_s2h = defaultdict(dict)

        for sensor_name in self.camera_names:
            if not sensor_name.startswith("cam"):
                continue
            sensor_ins = getattr(self.car.sensors, sensor_name)
            camera_name_mapping_h2s[sensor_ins.name] = {
                "standard_name": sensor_name,
                "resolution": sensor_ins.resolution,
            }
            camera_name_mapping_s2h[sensor_name] = {
                "hidden_name": sensor_ins.name,
                "resolution": sensor_ins.resolution,
            }
        self._camera_name_mapping = {
            "hidden": camera_name_mapping_h2s,
            "standard": camera_name_mapping_s2h,
        }

    def __call__(self):
        json_collection = self._parse_name_to_json_path()
        assert len(json_collection) > 0, "There should be more one Json Files! Please check!"

        frame_index = [[-1]]
        frame_data_list = FrameDataset(
            len(json_collection),
            self.redis_param,
            self.only_key_frame,
            log_bad_data=self.log_bad_data,
            log_bad_data_filename=self.log_bad_data_filename,
        )
        if USE_REDIS:
            calibrated_sensors_collection = CalibratedSensorsCollection()
        else:
            calibrated_sensors_collection = CalibratedSensorsCacheCollection()
        calibrated_sensors = {}

        if USE_LOCAL_CACHE:
            calibrated_sensors_id = []
            cumsum = 0
            for json_idx, json_path in enumerate(
                tqdm(json_collection, disable=(not is_master()), desc="[Load Dataset]")
            ):
                # json_path = get_gpfs_renori_path(json_path)  # 针对occ数据 是否还需要特殊路径判断
                calibrated_sensors_collection[json_idx] = copy.deepcopy(self.path_sizes[json_path]["calib"])
                num_frames, num_kf = (
                    self.path_sizes[json_path]["num_frames"],
                    self.path_sizes[json_path]["num_key_frames"],
                )
                calibrated_sensors[json_idx] = calibrated_sensors_collection[json_idx]

                if self.only_key_frame:
                    frame_data_list.add(json_idx, json_path, num_kf)
                    if num_kf == 0:
                        print(f"filter out json_path {json_path} because of no key frames")
                        continue
                    frame_index.append(np.arange(num_kf) + frame_index[-1][-1] + 1)
                    idx_len = num_kf
                else:
                    frame_data_list.add(json_idx, json_path, num_frames)
                    frame_index.append(np.arange(num_frames) + cumsum)
                    cumsum += num_frames
                    idx_len = num_frames
                calibrated_sensors_id += [json_idx] * idx_len
            frame_data_list.finalize()

        else:
            # Determine the number of threads to use (adjust based on your system)
            max_workers = 30
            batch_size = 8
            # Use ThreadPoolExecutor to load JSON files in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                # 批量准备任务，然后一次性提交
                start_time = time.time()
                tasks = [(json_idx, json_path) for json_idx, json_path in enumerate(json_collection)]
                random.shuffle(tasks)

                def process_batch(batch):
                    results = []
                    for task in batch:
                        results.append(
                            _load_single_json(
                                *task,
                                redis_param=self.redis_param,
                                oss_etag_helper=self.oss_etag_helper if USE_REDIS else None,
                            )
                        )
                    return results

                futures = [executor.submit(process_batch, batch) for batch in chunks(tasks, batch_size)]

                end_time = time.time()
                print(f"submit {len(futures)} batched files in {end_time - start_time:.2f} seconds.")

                # pbar = tqdm(total=len(tasks), desc=f"[Load Dataset rank: {torch_dist.get_rank()}]")
                pbar = tqdm(total=len(tasks), disable=(not is_master()), desc="[Load Dataset]")
                # Process results as they complete
                for future in concurrent.futures.as_completed(futures):
                    batch_result = future.result()

                    for json_idx, json_path, json_data in batch_result:
                        calibrated_sensors_collection[json_idx] = json_data["calibrated_sensors"]
                        calibrated_sensors[json_idx] = calibrated_sensors_collection[json_idx]
                        frames, idx = json_data["frames"], json_data["key_frame_idx"]

                        if self.only_key_frame:
                            valid_frame_len = len(idx)
                        else:
                            valid_frame_len = len(frames)
                        frame_data_list.add(json_idx, json_path, valid_frame_len)
                    pbar.update(len(batch_result))

            frame_data_list.finalize()
            frame_index = np.arange(frame_data_list.cum_idx[-1], dtype=np.int64)
            calibrated_sensors_id = np.repeat(
                np.arange(len(frame_data_list.frame_len_list)), repeats=frame_data_list.frame_len_list
            ).tolist()

        if hasattr(self, "path_sizes"):
            del self.path_sizes
        if USE_REDIS:
            self.oss_etag_helper.join()

        gc.collect()

        self._loader_out = {
            "frame_data_list": frame_data_list,
            "frame_index": ConcatDataset(frame_index[1:]) if USE_LOCAL_CACHE else frame_index,
            "calibrated_sensors": calibrated_sensors,
            "calibrated_sensors_id": calibrated_sensors_id,
            "json_collection": json_collection,
            "camera_name_mapping": self.camera_name_mapping,
            "cummulative_sizes": frame_data_list.cum_idx,
        }

    def get_scene_name(self, idx):
        # 定位当前idx数据存在于哪个json文件，json文件地址作为 scene_name。
        json_idx = bisect.bisect_right(self.output["frame_data_list"].cumulative_sizes, idx)
        json_path = self.output["json_collection"][json_idx][1]
        return json_path


def _load_single_json(json_idx, json_path, redis_param, oss_etag_helper=None):
    """Worker function to load a single JSON file"""
    # json_path = get_gpfs_renori_path(json_path)  # 针对occ数据 是否还需要特殊路径判断
    if get_cluster() == "https://qy.machdrive.cn":
        if refile.smart_exists(trans_oss_to_gpfs_path(json_path)):  # 注意json不在gpfs上的情况
            json_path = trans_oss_to_gpfs_path(json_path)
    # Create a thread-local etag helper for thread safety
    if USE_REDIS:
        try:
            json_data = RedisCachedData(json_path, oss_etag_helper=oss_etag_helper, **redis_param)
        except RuntimeError as e:
            print(f"Error loading {json_path}: {e}")
            redis_param_copy = dict(**redis_param)
            redis_param_copy["rebuild"] = True
            json_data = RedisCachedData(json_path, oss_etag_helper=oss_etag_helper, **redis_param_copy)
    else:
        json_data = load_json(json_path)
        json_data["key_frame_idx"] = [i for i, x in enumerate(json_data["frames"]) if x["is_key_frame"]]
    return json_idx, json_path, json_data


def chunks(lst, n):
    for i in range(0, len(lst), n):
        yield lst[i : i + n]
