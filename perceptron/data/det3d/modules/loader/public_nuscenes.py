import io
import numpy as np
from skimage import io as skimage_io
from pyquaternion import Quaternion
from .public_base import BaseLoader


_name_mapping = {
    "human.pedestrian.adult": "pedestrian",
    "human.pedestrian.child": "pedestrian",
    "human.pedestrian.wheelchair": "ignore",
    "human.pedestrian.stroller": "ignore",
    "human.pedestrian.personal_mobility": "ignore",
    "human.pedestrian.police_officer": "pedestrian",
    "human.pedestrian.construction_worker": "pedestrian",
    "animal": "ignore",
    "vehicle.car": "car",
    "vehicle.motorcycle": "motorcycle",
    "vehicle.bicycle": "bicycle",
    "vehicle.bus.bendy": "bus",
    "vehicle.bus.rigid": "bus",
    "vehicle.truck": "truck",
    "vehicle.construction": "construction_vehicle",
    "vehicle.emergency.ambulance": "ignore",
    "vehicle.emergency.police": "ignore",
    "vehicle.trailer": "trailer",
    "movable_object.barrier": "barrier",
    "movable_object.trafficcone": "traffic_cone",
    "movable_object.pushable_pullable": "ignore",
    "movable_object.debris": "ignore",
    "static_object.bicycle_rack": "ignore",
}


class NuscenesLoader(BaseLoader):
    r"""This class is Nuscenes multi-modal dataset, such as lidar, camera."""

    def __init__(
        self,
        class_names=None,
        data_split="training",
        lidar_dict=None,
        camera_dict=None,
        radar_dict=None,
        mode="train",
    ):

        super().__init__(
            dataset="nuScenes",
            class_names=class_names,
            data_split=data_split,
            lidar_dict=lidar_dict,
            camera_dict=camera_dict,
            radar_dict=radar_dict,
            mode=mode,
        )
        self.name_mapping = _name_mapping

    def _camera_loader(self, item_info):
        frame_idx = item_info["frame_idx"]
        sensor_list = item_info["cam_infos"].keys()
        result = []
        for k in sensor_list:
            nori_img_id = self.nori_list.loc[frame_idx][k]
            img_file = io.BytesIO(self.nori_fetcher.get(nori_img_id))
            result.append(skimage_io.imread(img_file))
        return result

    def _lidar_loader(self, item_info, load_dim, use_dim, num_sweeps):
        if not isinstance(use_dim, list):
            use_dim = list(range(use_dim))
        sensor_list = list(item_info["lidar_infos"].keys())
        frame_idx = item_info["frame_idx"]
        nori_id = self.nori_list.loc[frame_idx][sensor_list[0]]
        data = self.nori_fetcher.get(nori_id)
        points = np.frombuffer(data, dtype=np.float32, count=-1).reshape(-1, load_dim)[:, use_dim].copy()

        if points.shape[-1] == 5:
            points[:, -1] = 0
        sweep_points = [points]
        key_ego_to_global = np.linalg.inv(item_info["car_from_global"])
        key_lidar_to_ego = np.linalg.inv(item_info["ref_from_car"])

        choices = np.arange(min(len(item_info["lidar_sweeps"]), num_sweeps))

        for idx in choices:
            sw_img_nori = item_info["lidar_sweeps"][idx][sensor_list[0]]["nori_id"]
            sweep_ego_to_global = np.linalg.inv(item_info["lidar_sweeps"][idx][sensor_list[0]]["car_from_global"])
            sweep_lidar_timestamp = item_info["lidar_sweeps"][idx][sensor_list[0]]["timestamp"]
            lidar_data = self.nori_fetcher.get(sw_img_nori)
            frame = np.frombuffer(lidar_data, dtype=np.float32, count=-1).reshape(-1, load_dim)[:, use_dim].copy()
            homogeneous_point = np.ones((frame.shape[0], 4))
            homogeneous_point[:, :3] = frame[:, :3]
            sweep_on_key = (
                np.linalg.inv(key_lidar_to_ego)
                @ np.linalg.inv(key_ego_to_global)
                @ sweep_ego_to_global
                @ key_lidar_to_ego
                @ homogeneous_point.T
            ).T
            frame[:, :3] = sweep_on_key[:, :3]
            if points.shape[-1] == 5:
                frame[:, -1] = (item_info["timestamp"] - sweep_lidar_timestamp) / 1e6
            sweep_points.append(frame)
        result = np.concatenate(sweep_points)
        return result

    def _radar_loader(self, item_info, load_dim, max_points_num):
        # Noting: lidar in "sensor2lidar" refers to key frame rather than cur frame (used in Lidar Loader)
        frame_idx = item_info["frame_idx"]
        sensor_list = item_info["radar_infos"].keys()
        result = []
        key_info = {k: item_info["radar_infos"][k][0] for k in sensor_list}
        for k in sensor_list:
            key_info = item_info["radar_infos"][k][0]
            nori_id = self.nori_list.loc[frame_idx][k]
            data = self.nori_fetcher.get(nori_id)
            data = np.frombuffer(data, dtype=np.float64).reshape(load_dim, -1).transpose(1, 0).copy()
            result.append(self._radar_to_lidar(data, key_info, key_info["timestamp"]))

            sweep_infos = item_info["radar_sweeps"]
            for swid in range(len(sweep_infos)):
                s_info = sweep_infos[swid][k]
                s_nori_id = s_info["nori_id"]
                s_data = self.nori_fetcher.get(s_nori_id)
                s_data = np.frombuffer(s_data, dtype=np.float64).reshape(load_dim, -1).transpose(1, 0).copy()
                result.append(self._radar_to_lidar(s_data, s_info, key_info["timestamp"]))
        result = np.concatenate(result)
        result = self._radar_pad_or_drop(result, max_points_num)
        return result

    def _radar_to_lidar(self, radar_points, infos, key_ts):
        def trans_func(radar_points, trans, rot, use_trans=False):
            org_dims = radar_points.shape[1]
            if org_dims == 2:
                radar_points = np.concatenate([radar_points, np.zeros((radar_points.shape[0], 1))], axis=-1)
            radar_points = radar_points @ rot.T
            if use_trans:
                radar_points = radar_points + trans
            radar_points = radar_points[:, :org_dims]
            return radar_points

        rot = infos["sensor2lidar_rotation"]
        trans = infos["sensor2lidar_translation"]
        # 计算timestamp
        ts = key_ts * 1e-6
        ts_cur = infos["timestamp"] * 1e-6
        time_diff = ts - ts_cur
        time_diff = np.ones((radar_points.shape[0], 1)) * time_diff
        # transform
        velo = trans_func(radar_points[:, 6:8], trans, rot)
        velo_comp = trans_func(radar_points[:, 8:10], trans, rot)
        xyz = trans_func(radar_points[:, :3], trans, rot, use_trans=True)
        radar_points = np.concatenate([xyz, radar_points[:, 3:6], velo, velo_comp, radar_points[:, 10:], time_diff], -1)

        return radar_points

    def _radar_pad_or_drop(self, points, max_points_num):
        num_points = points.shape[0]
        if num_points == max_points_num:
            masks = np.ones((num_points, 1), dtype=points.dtype)

            return np.concatenate((points, masks), axis=1)

        if num_points > max_points_num:
            points = np.random.permutation(points)[:max_points_num, :]
            masks = np.ones((max_points_num, 1), dtype=points.dtype)

            return np.concatenate((points, masks), axis=1)

        if num_points < max_points_num:
            zeros = np.zeros((max_points_num - num_points, points.shape[1]), dtype=points.dtype)
            masks = np.ones((num_points, 1), dtype=points.dtype)

            points = np.concatenate((points, zeros), axis=0)
            masks = np.concatenate((masks, zeros.copy()[:, [0]]), axis=0)
            return np.concatenate((points, masks), axis=1)

    def _generate_data_dict(self, item):
        data_dict = dict()

        if self.is_train:
            gt_names = np.array(list(map(lambda x: _name_mapping[x], item["info"]["gt_names"])))
            mask = [
                gt_names[i] in self.class_names
                and (item["info"]["num_lidar_pts"][i] + item["info"]["num_radar_pts"][i]) > 0
                for i in range(len(gt_names))
            ]
            data_dict["gt_boxes"] = item["info"]["gt_boxes"][mask]
            data_dict["gt_boxes"][np.isnan(data_dict["gt_boxes"])] = 0
            data_dict["gt_labels"] = np.array([self.class_names.index(i) for i in gt_names[mask]])

        data_dict["ego_to_global"] = np.linalg.inv(item["info"]["car_from_global"])

        if self.with_lidar:
            data_dict["lidar_to_ego"] = np.linalg.inv(item["info"]["ref_from_car"])
            data_dict["points"] = item["points"]

        if self.with_camera:
            lidar2imgs = []
            for cam_name in item["info"]["cam_infos"].keys():
                cam2ego = np.eye(4)
                cam2ego[:3, :3] = Quaternion(item["info"]["sensor2ego_rotations"][cam_name]).rotation_matrix
                cam2ego[:3, 3] = np.array(item["info"]["sensor2ego_translations"][cam_name])
                cam2lidar = item["info"]["ref_from_car"] @ cam2ego
                intrin_mat = np.eye(4)
                intrin_mat[:3, :3] = np.array(
                    item["info"]["cam_infos"][cam_name]["calibrated_sensor"]["camera_intrinsic"]
                )
                lidar2img = intrin_mat @ np.linalg.inv(cam2lidar)
                lidar2imgs.append(lidar2img)
            data_dict["lidar2imgs"] = np.stack(lidar2imgs)
            data_dict["imgs"] = item["imgs"]

        if self.with_radar:
            data_dict["radar_points"] = item["radar_points"]
        return data_dict
