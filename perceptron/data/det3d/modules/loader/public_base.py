import copy
import nori2 as nori
import pandas as pd
import pickle
from refile import smart_open
from functools import partial


class NoriListPathManager:
    _nori_list_wrt_name = {
        "kitti": {
            "training": "s3://generalDetection/3DDatasets/Kitti/nori_list/train.list",
            "validation": "s3://generalDetection/3DDatasets/Kitti/nori_list/val.list",
            "testing": "s3://generalDetection/3DDatasets/Kitti/nori_list/test.list",
        },
        "nuScenes": {
            "training": "s3://e2emodel-data/nuScenes_fusion/train_with_radar.tsv",
            "validation": "s3://e2emodel-data/nuScenes_fusion/val_with_radar.tsv",
            "test": "s3://generalDetection/3DDatasets/nuScenes/test.tsv",
            "trainval": "s3://generalDetection/3DDatasets/nuScenes/trainval.tsv",
        },
        "waymo": {
            "training": "s3://generalDetection/3DDatasets/waymo/training.list",
            "validation": "s3://generalDetection/3DDatasets/waymo/validation.list",
            "testing": "s3://generalDetection/3DDatasets/waymo/testing.list",
        },
    }

    @classmethod
    def get_nori_list_path_by_name(cls, nori_list_name):
        return cls._nori_list_wrt_name[nori_list_name]


class InfoPathManager:
    info_paths = {
        "kitti": {
            "training": "s3://generalDetection/3DDatasets/Kitti/info/kitti_infos_train.pkl",
            "validation": "s3://generalDetection/3DDatasets/Kitti/info/kitti_infos_val.pkl",
        },
        "nuScenes": {
            "training": "s3://e2emodel-data/nuScenes_fusion/radar_new_train_info.pkl",
            "validation": "s3://e2emodel-data/nuScenes_fusion/radar_new_val_info.pkl",
        },
        "waymo": {
            "training": "s3://generalDetection/3DDatasets/waymo/infos/waymo_name_timestamp_train.pkl",
            "validation": "s3://generalDetection/3DDatasets/waymo/infos/waymo_name_timestamp_val.pkl",
            "testing": "s3://generalDetection/3DDatasets/waymo/infos/waymo_name_timestamp_test.pkl",
        },
    }

    @classmethod
    def get_info_path_by_name(cls, name):
        return cls.info_paths[name]

    @classmethod
    def check_dataset(cls, dataset):
        return dataset in cls.info_paths.keys()

    @classmethod
    def check_split(cls, dataset, data_split):
        return data_split in cls.info_paths[dataset].keys()


class BaseLoader:
    r"""This class is Nuscenes multi-modal dataset, such as lidar, camera."""

    def __init__(
        self,
        dataset=None,
        class_names=None,
        data_split="training",
        lidar_dict=None,
        camera_dict=None,
        radar_dict=None,
        mode="train",
    ):
        assert InfoPathManager.check_dataset(dataset), f"{dataset} is not supported"
        assert InfoPathManager.check_split(dataset, data_split), f"{data_split} is not supported in {dataset}"
        self.class_names = class_names
        self.mode = mode

        self.nori_fetcher = nori.Fetcher()
        self._load_infos(dataset, data_split)
        self.input_modality = dict(use_lidar=False, use_camera=False, use_radar=False)
        if lidar_dict is not None:
            self.input_modality["use_lidar"] = True
            self._lidar_loader = partial(self._lidar_loader, **lidar_dict)
        if camera_dict is not None:
            self.input_modality["use_camera"] = True
            self._camera_loader = partial(self._camera_loader, **camera_dict)
        if radar_dict is not None:
            self.input_modality["use_radar"] = True
            self._radar_loader = partial(self._radar_loader, **radar_dict)

    def _load_infos(self, dataset, data_split):
        nori_list_path = NoriListPathManager.get_nori_list_path_by_name(dataset)[data_split]
        with smart_open(nori_list_path, "r") as f:
            self.nori_list = pd.read_csv(f, sep="\t")

        nori_info_path = InfoPathManager.get_info_path_by_name(dataset)[data_split]
        with smart_open(nori_info_path, "rb") as f:
            self.infos = pickle.load(f)
        assert len(self.infos) == len(self.nori_list)

    @property
    def with_lidar(self):
        """bool: whether the dataset has lidar"""
        return self.input_modality["use_lidar"]

    @property
    def with_camera(self):
        """bool: whether the dataset has camera"""
        return self.input_modality["use_camera"]

    @property
    def with_radar(self):
        """bool: whether the dataset has radar"""
        return self.input_modality["use_radar"]

    @property
    def is_train(self):
        return self.mode == "train"

    def __call__(self, idx: int):
        """
        loading data-of-all-sensors with given index

        Args:
            idx: int, Sampled index
        Returns:
            dict
        """
        item = {}
        item_info = copy.deepcopy(self.infos[idx])
        item_info["frame_idx"] = idx

        if self.with_camera:
            item["imgs"] = self._camera_loader(item_info)

        if self.with_lidar:
            item["points"] = self._lidar_loader(item_info)

        if self.with_radar:
            item["radar_points"] = self._radar_loader(item_info)

        item["info"] = item_info
        data_dict = self._generate_data_dict(item)
        return data_dict

    def _camera_loader(self):
        raise NotImplementedError

    def _lidar_loader(self):
        raise NotImplementedError

    def _radar_loader(self):
        raise NotImplementedError

    def _generate_data_dict(self):
        raise NotImplementedError
