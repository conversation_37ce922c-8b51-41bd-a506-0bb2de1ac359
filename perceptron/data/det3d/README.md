# 3D检测数据说明

3D检测的原始数据和数据供应的说明文档

## 设计目标
* 数据供应模块化，方便对各个模块做扩展，比如：输入图片和内参的各种魔改和抠图
* 数据可维护性和复用性高，比如：丢掉1k+行的原始数据记录，各车统一对外接口名
* 适用于未来半年的模型训练，比如：多帧、鱼眼、动态输入、PETR等
* 适用于其他3D检测任务的数据供应，比如：雷达、多模
* 摆脱相关库的依赖，比如：data3d


## 目录结构
```zsh
./
├── det3d   # torch dataloader的dataset参数
│   ├── modules   # 数据集的子模块
│   │   ├── annotation   # 标签处理
│   │   ├── pipelines   # 数据归一化、增强
│   │   ├── image   # 图像处理
│   │   ├── loader   # 原始数据载入和预处理
│   │   ├── utils   # 存放常用的工具
│   │   ├── evaluation.py   # 推理评测
│   ├── private   # 私有数据集
│   │   ├── base.py   # 数据集基类
│   │   └── depth.py   # depth数据集（待完善）
│   ├── public   # 公开数据集
│   │   ├── nuscenes.py
│   │   └── once.py
│   ├── utils   # 各种函数
└── └── source   # 原始数据的索引
        ├── base.py   # 定义所使用到的基类
        ├── config.py   # 数据对外接口
        ├── filter.py   # 各种数据集过滤的脚本
        ├── geely_car_1.py   # 吉利1号车
        ├── geely_car_2.py   # 吉利2号车
        ├── synthetic_vcar.py   # 合成数据
        ├── wm_car_1.py   # 威马1号车
        ├── wm_car_2.py   # 威马2号车
        ├── wm_car_3.py   # 威马3号车
        ├── wm_car_4.py   # 威马4号车
        └── wm_car_5.py   # 威马5号车
```
