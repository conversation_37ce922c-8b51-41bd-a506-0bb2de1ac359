import itertools
from typing import Optional
from collections import defaultdict

import torch
import torch.distributed as dist
from torch.utils.data.sampler import <PERSON><PERSON>
import math
import random

import numpy as np
from perceptron.utils.det3d_utils.common_utils import get_dist_info
from torch.utils.data import DistributedSampler as _DistributedSampler
import copy


class InfiniteSampler(Sampler):
    """
    In training, we only care about the "infinite stream" of training data.
    So this sampler produces an infinite stream of indices and
    all workers cooperate to correctly shuffle the indices and sample different indices.
    The samplers in each worker effectively produces `indices[worker_id::num_workers]`
    where `indices` is an infinite stream of indices consisting of
    `shuffle(range(size)) + shuffle(range(size)) + ...` (if shuffle is True)
    or `range(size) + range(size) + ...` (if shuffle is False)
    """

    def __init__(self, size: int, shuffle: bool = True, seed: Optional[int] = 0, rank=0, world_size=1, drop_last=False):
        """
        Args:
            size (int): the total number of data of the underlying dataset to sample from
            shuffle (bool): whether to shuffle the indices or not
            seed (int): the initial seed of the shuffle. Must be the same
                across all workers. If None, will use a random seed shared
                among workers (require synchronization among all workers).
        """
        self._size = size
        assert size > 0
        self._shuffle = shuffle
        self._seed = int(seed)
        self.drop_last = drop_last

        if dist.is_available() and dist.is_initialized():
            self._rank = dist.get_rank()
            self._world_size = dist.get_world_size()
        else:
            self._rank = rank
            self._world_size = world_size

    def set_epoch(self, epoch):
        pass

    def __iter__(self):
        start = self._rank
        yield from itertools.islice(self._infinite_indices(), start, None, self._world_size)

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                yield from torch.randperm(self._size, generator=g).tolist()
            else:
                # yield from torch.arange(self._size)
                yield from list(range(self._size))

    def __len__(self):
        if self.drop_last:
            return self._size // self._world_size
        else:
            return (self._size + self._world_size - 1) // self._world_size


class InfiniteIntervalSampler(InfiniteSampler):
    def __init__(
        self,
        size: int,
        shuffle: bool = True,
        seed: Optional[int] = 0,
        rank=0,
        world_size=1,
        drop_last=False,
        interval=1,
        with_inter_random=True,
    ):
        super().__init__(size, shuffle, seed, rank, world_size, drop_last)
        self.interval = interval
        self._size //= self.interval
        self.with_inter_random = with_inter_random

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                list_temp = (torch.randperm(self._size, generator=g) * self.interval).tolist()
            else:
                list_temp = list(range(0, self._size * self.interval, self.interval))
            if self.with_inter_random:
                yield from [idx + np.random.randint(0, self.interval) for idx in list_temp]
            else:
                yield from [idx for idx in list_temp]


class InfiniteIntervalSamplerDeterministic(InfiniteSampler):
    def __init__(
        self,
        size: int,
        shuffle: bool = True,
        seed: Optional[int] = 0,
        rank=0,
        world_size=1,
        drop_last=False,
        interval=1,
    ):
        super().__init__(size, shuffle, seed, rank, world_size, drop_last)
        self.interval = interval
        self._size //= self.interval

    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                list_temp = (torch.randperm(self._size, generator=g) * self.interval).tolist()
            else:
                list_temp = list(range(0, self._size * self.interval, self.interval))
            yield from list_temp


class GroupEachSampleInBatchSampler(Sampler):
    """
    Pardon this horrendous name. Basically, we want every sample to be from its own group.
    If batch size is 4 and # of GPUs is 8, each sample of these 32 should be operating on
    its own group.
    Shuffling is only done for group order, not done within groups.
    """

    def __init__(
        self,
        dataset,
        samples_per_gpu=1,
        num_replicas=None,
        rank=None,
        seed=0,
        shuffle=False,
        drop_last=False,
    ):
        assert not shuffle, "shuffle should be False"
        assert not drop_last, "drop_last should be False"
        _rank, _num_replicas = get_dist_info()
        if num_replicas is None:
            num_replicas = _num_replicas
        if rank is None:
            rank = _rank

        self.dataset = dataset
        self.batch_size = samples_per_gpu
        self.num_replicas = num_replicas
        self.rank = rank
        self.seed = seed  # seed is useless here.

        assert hasattr(
            self.dataset, "scene_flag"
        )  # using sceen_flag not flag to avoid conflicts in mmcv. flag in mmcv is used to figure out the different size of imgs which will affect random sampling.
        self.scene_flag = self.dataset.scene_flag
        self.group_sizes = np.bincount(self.scene_flag)

        self.groups_num = len(self.group_sizes)

        self.global_batch_size = samples_per_gpu * num_replicas
        assert (
            self.groups_num >= self.global_batch_size
        ), f"self.groups_num {self.groups_num} < self.global_batch_size {self.global_batch_size}. please use less gpu"

        # Now, for efficiency, make a dict group_idx: List[dataset sample_idxs]
        self.group_idx_to_sample_idxs = defaultdict(list)
        for idx, group in enumerate(self.scene_flag):
            self.group_idx_to_sample_idxs[group].append(idx)

        self.group_idx_to_sample_idxs = dict(self.group_idx_to_sample_idxs)
        # self.group_idx_to_sample_idxs = {
        #     group_idx: np.where(self.scene_flag == group_idx)[0].tolist() for group_idx in range(self.groups_num)
        # }
        # print("len group_idx_to_sample_idxs", len(self.group_idx_to_sample_idxs))
        # print("each group len", [len(v) for v in self.group_idx_to_sample_idxs.values()])
        assert (
            min([len(v) for v in self.group_idx_to_sample_idxs.values()]) > 0
        ), f"{min([len(v) for v in self.group_idx_to_sample_idxs.values()])}"

        # Get a generator per sample idx. Considering samples over all
        # GPUs, each sample position has its own generator
        list_of_group_idx = self._group_indices_per_global_sample_idx()
        self.group_indices_per_global_sample_idx = [
            iter(list_of_group_idx[self.rank * self.batch_size + local_sample_idx])
            for local_sample_idx in range(self.batch_size)
        ]

        # Keep track of a buffer of dataset sample idxs for each local sample idx
        self.buffer_per_local_sample = [[] for _ in range(self.batch_size)]

        cur_group = list_of_group_idx[self.rank * self.batch_size + 0]
        self.size = 0
        for i in cur_group:
            self.size += self.group_sizes[i]

        # print("size of this batch sampler", self.size)

    def _group_indices_per_global_sample_idx(self):
        group_idx = list(range(self.groups_num))
        assert len(group_idx) > 0
        # 按照group进行均分
        step = math.floor(self.groups_num / (self.num_replicas * self.batch_size))
        add_one_index = self.groups_num - self.num_replicas * self.batch_size * step
        list_of_group_idx = []
        e = 0
        for i in range(self.num_replicas * self.batch_size):
            s = e
            e = min(s + step, self.groups_num)
            e = (e + 1) if i < add_one_index else e
            list_of_group_idx.append(group_idx[s:e])
        assert len(list_of_group_idx) == self.num_replicas * self.batch_size
        assert (
            list_of_group_idx[0][0] == 0 and list_of_group_idx[-1][-1] == group_idx[-1]
        ), f"list_of_group_idx not right, {list_of_group_idx}, groups_num, {self.groups_num}"
        return list_of_group_idx

    def __iter__(self):
        idx = 0
        while True:
            curr_batch = []
            for local_sample_idx in range(self.batch_size):

                idx += 1
                if len(self.buffer_per_local_sample[local_sample_idx]) == 0:
                    # Finished current group, refill with next group
                    try:
                        new_group_idx = next(self.group_indices_per_global_sample_idx[local_sample_idx])
                    except StopIteration:
                        print(f"StopIteration, no next iter for rank {self.rank}")
                        new_group_idx = 0  # to avoid StopIteration Error when num_workers > 0
                    self.buffer_per_local_sample[local_sample_idx] = copy.deepcopy(
                        self.group_idx_to_sample_idxs[new_group_idx]
                    )
                curr_batch.append(self.buffer_per_local_sample[local_sample_idx].pop(0))
            yield curr_batch

    def __len__(self):
        """Length of base dataset."""
        return self.size

    def set_epoch(self, epoch):
        self.epoch = epoch


class DistributedSampler(_DistributedSampler):
    def __init__(self, dataset=None, num_replicas=None, rank=None, shuffle=False, seed=0):
        super().__init__(dataset, num_replicas=num_replicas, rank=rank, shuffle=shuffle)

        self.seed = seed if seed is not None else 0
        self.scene_flag = self.dataset.scene_flag
        self.group_sizes = np.bincount(self.scene_flag)
        self.groups_num = len(self.group_sizes)
        self.groups = list(set(self.scene_flag))
        assert self.groups == list(range(self.groups_num))

        # {group_idx: List[dataset sample_idxs]}
        self.group_idx_to_sample_idxs = defaultdict(list)
        for idx, group in enumerate(self.scene_flag):
            self.group_idx_to_sample_idxs[group].append(idx)

        self.group_idx_to_sample_idxs = dict(self.group_idx_to_sample_idxs)
        # self.group_idx_to_sample_idxs = {
        #     group_idx: np.where(self.scene_flag == group_idx)[0].tolist() for group_idx in range(self.groups_num)
        # }

        # num_groups_per_gpu = math.ceil(len(self.groups) / self.num_replicas)
        # assign groups (continuous videos) to each gpu rank
        # self.sample_group_idx = self.groups[self.rank*num_groups_per_gpu: min(len(self.groups), (self.rank+1)*num_groups_per_gpu)]
        self.sample_group_idx = self.groups[self.rank :: self.num_replicas]

        self.sample_idxs = []
        for i in self.sample_group_idx:
            self.sample_idxs.extend(self.group_idx_to_sample_idxs[i])

        self.num_samples = len(self.sample_idxs)
        self.total_size = len(self.dataset)

    def __iter__(self):
        # only support batchsize = 1
        if self.shuffle:
            assert False

        return iter(self.sample_idxs)


def split_list_into_chunks(lst, chunk_size=30):
    # 计算可以容纳完整chunk的数量
    num_full_chunks = len(lst) // chunk_size + bool(len(lst) % chunk_size)

    # 从原始列表中切片出完整的chunks
    chunks = [lst[i * chunk_size : (i + 1) * chunk_size] for i in range(num_full_chunks)]

    return chunks


def split_list_into_chunks_dropLast(lst, chunk_size=30):
    # 计算可以容纳完整chunk的数量
    num_full_chunks = len(lst) // chunk_size

    # 从原始列表中切片出完整的chunks
    chunks = [lst[i * chunk_size : (i + 1) * chunk_size] for i in range(num_full_chunks)]

    return chunks


class DistributedSamplerInterval2Stack(DistributedSampler):
    def __init__(
        self, dataset=None, num_replicas=None, rank=None, shuffle=False, seed=0, stream_len=20, interval=4, num_frames=1
    ):
        super().__init__(dataset, num_replicas=num_replicas, rank=rank, shuffle=shuffle, seed=seed)

        self.seed = seed if seed is not None else 0
        self.scene_flag = self.dataset.scene_flag
        self.group_sizes = np.bincount(self.scene_flag)
        self.groups_num = len(self.group_sizes)
        self.groups = list(set(self.scene_flag))
        self.stream_len = stream_len
        self.interval = interval
        self.num_frames = num_frames
        assert self.stream_len % self.num_frames == 0
        assert self.groups == list(range(self.groups_num))

        # {group_idx: List[dataset sample_idxs]}
        self.group_idx_to_sample_idxs = defaultdict(list)
        for idx, group in enumerate(self.scene_flag):
            self.group_idx_to_sample_idxs[group].append(idx)

        self.group_idx_to_sample_idxs = dict(self.group_idx_to_sample_idxs)
        # self.group_idx_to_sample_idxs = {
        #     group_idx: np.where(self.scene_flag == group_idx)[0].tolist() for group_idx in range(self.groups_num)
        # }

        # assign groups (continuous videos) to each gpu rank
        # self.sample_group_idx = self.groups[self.rank*num_groups_per_gpu: min(len(self.groups), (self.rank+1)*num_groups_per_gpu)]
        # split the clip
        self.sample_idxs_chunks = []
        for i in self.groups:
            chunks = split_list_into_chunks_dropLast(self.group_idx_to_sample_idxs[i], self.stream_len)
            self.sample_idxs_chunks.extend(chunks)

        random.shuffle(self.sample_idxs_chunks)
        tail_data = len(self.sample_idxs_chunks) % (self.num_replicas * self.interval)
        if tail_data > 0:
            self.sample_idxs_chunks = self.sample_idxs_chunks[:-(tail_data)]
        # remove tail of data to make it evenly divisible.
        self.sample_idxs = list(
            itertools.chain(*self.sample_idxs_chunks[self.rank :: (self.num_replicas * self.interval)])
        )

        self.num_samples = len(self.sample_idxs) // self.num_frames
        self.total_size = self.num_samples * self.num_replicas

        # assert self.interval > self.stream_len

    def __iter__(self):
        # only support batchsize = 1
        if self.shuffle:
            assert False

        # random.shuffle(self.sample_idxs_chunks)
        # # remove tail of data to make it evenly divisible.
        # self.sample_idxs = list(
        #     itertools.chain(*self.sample_idxs_chunks[self.rank :: (self.num_replicas * self.interval)])
        # )
        yield from self._infinite_indices()

    def _infinite_indices(self):
        while True:
            random.shuffle(self.sample_idxs_chunks)
            # remove tail of data to make it evenly divisible.
            self.sample_idxs = list(
                itertools.chain(*self.sample_idxs_chunks[self.rank :: (self.num_replicas * self.interval)])
            )
            yield from self.sample_idxs[:: self.num_frames]


class DistributedSamplerInterval2StackBatch(DistributedSampler):
    def __init__(
        self,
        dataset=None,
        num_replicas=None,
        rank=None,
        shuffle=False,
        seed=0,
        stream_len=20,
        interval=4,
        num_frames=1,
        samples_per_gpu=1,
    ):
        super().__init__(dataset, num_replicas=num_replicas, rank=rank, shuffle=shuffle, seed=seed)

        self.seed = seed if seed is not None else 0
        self.scene_flag = self.dataset.scene_flag
        self.group_sizes = np.bincount(self.scene_flag)
        self.groups_num = len(self.group_sizes)
        self.groups = list(set(self.scene_flag))
        self.stream_len = stream_len
        self.interval = interval
        self.num_frames = num_frames
        self.batch_size = samples_per_gpu
        self.global_batch_size = samples_per_gpu * self.num_replicas
        assert self.stream_len % self.num_frames == 0
        assert self.groups == list(range(self.groups_num))
        assert (
            self.groups_num >= self.global_batch_size
        ), f"self.groups_num {self.groups_num} < self.global_batch_size {self.global_batch_size}. please use less gpu"

        # {group_idx: List[dataset sample_idxs]}
        self.group_idx_to_sample_idxs = defaultdict(list)
        for idx, group in enumerate(self.scene_flag):
            self.group_idx_to_sample_idxs[group].append(idx)

        self.group_idx_to_sample_idxs = dict(self.group_idx_to_sample_idxs)
        # self.group_idx_to_sample_idxs = {
        #     group_idx: np.where(self.scene_flag == group_idx)[0].tolist() for group_idx in range(self.groups_num)
        # }

        # assign groups (continuous videos) to each gpu rank
        # self.sample_group_idx = self.groups[self.rank*num_groups_per_gpu: min(len(self.groups), (self.rank+1)*num_groups_per_gpu)]
        # split the clip
        self.sample_idxs_chunks = []
        for i in self.groups:
            chunks = split_list_into_chunks_dropLast(self.group_idx_to_sample_idxs[i], self.stream_len)
            self.sample_idxs_chunks.extend(chunks)

        random.shuffle(self.sample_idxs_chunks)
        tail_data = len(self.sample_idxs_chunks) % (self.num_replicas * self.interval)
        if tail_data > 0:
            self.sample_idxs_chunks = self.sample_idxs_chunks[:-(tail_data)]

        self.buffer_per_local_sample = [[] for _ in range(self.batch_size)]
        # remove tail of data to make it evenly divisible.
        self.sample_idxs = list(
            itertools.chain(*self.sample_idxs_chunks[self.rank :: (self.num_replicas * self.interval)])
        )

        self.num_samples = len(self.sample_idxs) // self.num_frames
        self.total_size = self.num_samples * self.num_replicas

        # assert self.interval > self.stream_len

    def __iter__(self):
        # only support batchsize = 1
        if self.shuffle:
            assert False

        # random.shuffle(self.sample_idxs_chunks)
        # # remove tail of data to make it evenly divisible.
        # self.sample_idxs = list(
        #     itertools.chain(*self.sample_idxs_chunks[self.rank :: (self.num_replicas * self.interval)])
        # )
        yield from self._infinite_indices()

    def _infinite_indices(self):
        while True:
            curr_batch = []
            for local_sample_idx in range(self.batch_size):
                if len(self.buffer_per_local_sample[local_sample_idx]) == 0:
                    random.shuffle(self.sample_idxs_chunks)
                    # remove tail of data to make it evenly divisible.
                    self.sample_idxs = list(
                        itertools.chain(
                            *self.sample_idxs_chunks[self.rank :: (self.num_replicas * self.interval * self.batch_size)]
                        )
                    )
                    self.buffer_per_local_sample[local_sample_idx] = copy.deepcopy(self.sample_idxs[:: self.num_frames])
                curr_batch.append(self.buffer_per_local_sample[local_sample_idx].pop(0))

            yield from curr_batch


class InfinitePartIntervalSampler(InfiniteSampler):
    def __init__(
        self,
        size: int,
        shuffle: bool = True,
        seed: Optional[int] = 0,
        rank=0,
        world_size=1,
        drop_last=False,
        interval=1,
        _len_dataset_for_not_interval=-1,
        _len_dataset_for_interval=-1,
    ):
        super().__init__(size, shuffle, seed, rank, world_size, drop_last)
        self.interval = interval
        # self._size //= self.interval
        self._len_dataset_for_not_interval = _len_dataset_for_not_interval
        self._len_dataset_for_interval = _len_dataset_for_interval

        self._size = _len_dataset_for_not_interval + _len_dataset_for_interval // self.interval

    # make sure that dataset_for_not_interval is in the first place
    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                list_for_interval = (
                    self._len_dataset_for_not_interval
                    + torch.randperm(self._len_dataset_for_interval, generator=g)[
                        : self._len_dataset_for_interval // self.interval
                    ]
                ).tolist()
                if self._len_dataset_for_not_interval != 0:
                    list_for_not_inverval = torch.randperm(self._len_dataset_for_not_interval, generator=g).tolist()
                    list_for_sample = list_for_not_inverval + list_for_interval
                else:
                    list_for_sample = list_for_interval
                np.random.shuffle(list_for_sample)
            else:
                list_for_sample = list(range(self._len_dataset_for_not_interval)) + list(
                    range(
                        self._len_dataset_for_not_interval,
                        self._len_dataset_for_not_interval + self._len_dataset_for_interval,
                        self.interval,
                    )
                )

            yield from list_for_sample


class InfiniteTwoPartIntervalSampler(InfiniteSampler):
    def __init__(
        self,
        size: int,
        shuffle: bool = True,
        seed: Optional[int] = 0,
        rank=0,
        world_size=1,
        drop_last=False,
        interval=1,
        interval2=2,
        _len_dataset_for_interval=-1,
        _len_dataset_for_interval2=-1,
    ):
        super().__init__(size, shuffle, seed, rank, world_size, drop_last)
        self.interval = interval
        self.interval2 = interval2
        # self._size //= self.interval
        # self._len_dataset_for_not_interval = _len_dataset_for_not_interval
        self._len_dataset_for_interval = _len_dataset_for_interval
        self._len_dataset_for_interval2 = _len_dataset_for_interval2

        self._size = _len_dataset_for_interval // self.interval + _len_dataset_for_interval2 // self.interval2

    # make sure that dataset_for_not_interval is in the first place
    def _infinite_indices(self):
        g = torch.Generator()
        g.manual_seed(self._seed)
        while True:
            if self._shuffle:
                list_for_interval = torch.randperm(self._len_dataset_for_interval, generator=g)[
                    : self._len_dataset_for_interval // self.interval
                ].tolist()
                list_for_interval2 = (
                    self._len_dataset_for_interval
                    + torch.randperm(self._len_dataset_for_interval2, generator=g)[
                        : self._len_dataset_for_interval2 // self.interval2
                    ]
                ).tolist()
                list_for_sample = list_for_interval + list_for_interval2

                np.random.shuffle(list_for_sample)
            else:
                list_for_sample = list(range(0, self._len_dataset_for_interval, self.interval)) + list(
                    range(
                        self._len_dataset_for_interval,
                        self._len_dataset_for_interval + self._len_dataset_for_interval2,
                        self.interval2,
                    )
                )

            yield from list_for_sample
