from collections import defaultdict
from megfile import smart_isdir, smart_listdir, smart_path_join
import numpy as np
from numpy.lib import recfunctions as rfn
import tqdm
import refile
from loguru import logger
import json
import io
import pickle
import nori2 as nori
import tempfile
import os

from sklearn import linear_model
import torch
from torch.utils.data.dataset import ConcatDataset

from data3d.datasets.private import is_master
from data3d.transforms import transforms3d
from data3d.datasets.base import Dataset3D
from data3d.utils.redis_cache import OSSEtagHelper, RedisCachedIterator

from perceptron.data.seg3d.dataset.private_data.eval_utils.evaluation import (
    FreespaceEvalBatch,
    FreespaceMultiClassEvalBatch,
)

from urllib.parse import urlparse
from io import BytesIO
import boto3

from freespace.modules.filter import (
    ransac_filter,
)


def load_pkl(path):
    with refile.smart_open(path, "rb") as f:
        return pickle.load(f)


class RedisCachedData:
    def __init__(self, path, oss_etag_helper, rebuild=False, only_key_frame=True, **kwargs):
        self.path = path
        self.cache = RedisCachedIterator(oss_etag_helper.get_etag(self.path), **kwargs)
        if not self.cache.exist() or rebuild:
            self.oss = "aws --endpoint-url=http://oss.hh-b.brainpp.cn s3"
            self.only_key_frame = only_key_frame
            logger.info("cannot find cache {}, building...".format(path))
            logger.info("cache key: {}".format(self.cache.prefix))
            self._init_cache()
        self.meta = self.cache._get("meta")

    def _init_cache(self):
        try:
            data = load_pkl(self.path)  # labeled data is dumped as pkl
            frames = data.pop("frames")
            frames = self.load_npy_dir(data, frames)
        except:  # noqa
            with refile.smart_open(self.path) as rf:
                data = json.load(rf)
                frames = data.pop("frames")

        self.cache._set("meta", data)
        self.cache._insert_iterable(frames)

    def load_npy_dir(self, data, frames):
        """load label(npy_file) in given dir and add token for key_frame"""
        remote_dir = data["freespace_label_dir"]
        tmp_dir = tempfile.TemporaryDirectory()
        json_path = self.path[: -len(".pkl")]
        os.system(f"{self.oss} sync {remote_dir} {tmp_dir.name}")

        valid_frames = []
        for frame in frames:
            if self.only_key_frame and not frame["is_key_frame"]:
                continue
            frame_id = frame["frame_id"]
            npy_path = refile.smart_path_join(tmp_dir.name, f"{frame_id}.npy")
            if refile.smart_exists(npy_path):
                frame_token = "%".join([json_path, str(frame_id)])
                frame["frame_token"] = frame_token
                with refile.smart_open(npy_path, "rb") as f:
                    flag_label = np.load(f)
                    frame["freespace_label"] = flag_label["freespace_label"].tolist()
                    valid_frames.append(frame)
        tmp_dir.cleanup()
        return valid_frames

    def __getitem__(self, key):
        if key == "frames":
            return self.cache
        else:
            return self.meta[key]


class PrivateDatasetFreespace(Dataset3D):
    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
        data_configs_output=None,
    ):
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.data_configs = data_configs
        self.model_config = model_configs
        self.data_configs_output = (
            data_configs_output if data_configs_output is not None else data_configs
        )  # used when output voxel size is different from input
        self.class_names = model_configs.labels.values()
        self.lidar_key_list = lidar_key_list
        self.pc_fields = pc_fields
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)
        self.training = training
        self.with_contur = getattr(data_configs, "with_contur", False)

        self.N_grid = model_configs.N_grid
        self.neighbor_roi = data_configs.neighbor_roi
        self.freespace_label = 1  # freespace label mapping

        self._get_split_data_paths(data_paths)
        self.frame_data_list = self._load_redis_data(self.data_paths)

        if is_nori_read:
            self.nori_fetcher = None

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi * 0.1, np.pi * 0.1]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )

    def _get_split_data_paths(self, input_data_paths):
        """
        Compile json paths from given dirs
        Args:
            input_data_paths(List[Str]): list of label data directories.
        Returns:
            None
        """
        self.data_paths = []
        for data_dir_item in input_data_paths:
            if self.training and refile.smart_isdir(data_dir_item):  # det
                for ppl in refile.smart_listdir(data_dir_item):
                    json_paths = refile.smart_glob(refile.smart_path_join(data_dir_item, ppl, "*.pkl"))
                    self.data_paths.extend(json_paths)
            elif not self.training and refile.smart_isdir(data_dir_item):  # tracking
                for sub_dir in refile.smart_listdir(data_dir_item):
                    sub_dir = smart_path_join(data_dir_item, sub_dir)
                    if not smart_isdir(sub_dir):
                        continue
                    for ppl in smart_listdir(sub_dir):
                        json_paths = refile.smart_glob(refile.smart_path_join(sub_dir, ppl, "*.pkl"))
                        self.data_paths.extend(json_paths)
            elif data_dir_item.endswith(".pkl"):
                self.data_paths.append(data_dir_item)
            else:
                raise ValueError(f"pkl Files / pkl files dir expected, but got <{data_dir_item}>")

        self.data_paths = sorted(self.data_paths)

    def _load_redis_data(self, data_paths):
        oss_etag_helper = OSSEtagHelper(check_etag=is_master())

        key_frame_data_list = [[]]
        for scene_idx, json_path in enumerate(tqdm.tqdm(data_paths, disable=(not is_master()), desc="[Load Dataset]")):
            json_data = RedisCachedData(json_path, oss_etag_helper)
            frames = json_data["frames"]
            key_frame_data_list.append(frames)

        frame_data_list = ConcatDataset(key_frame_data_list[1:])
        return frame_data_list

    def _run_aug_and_process(self, data_dict):
        if self.training:
            data_dict = self.data_augmentor.forward(data_dict={**data_dict, "gt_boxes": []})
            data_dict.pop("gt_boxes")
        data_dict = self.processor.forward(data_dict=data_dict)
        return data_dict

    def __len__(self):
        return len(self.frame_data_list)

    def __getitem__(self, idx: int):
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        item = {}
        item.update(self._get_point_cloud(idx, keys=self.lidar_key_list))
        item["points"] = self._point_process(item)
        item["gt_panotic"] = np.array([])
        item["frame_token"] = self.frame_data_list[idx]["frame_token"]

        item.update(self._get_seg_gt(idx))
        assert len(item["points"]) == len(item["gt_lidarseg"])
        item = self._run_aug_and_process(item)
        if self.with_contur:
            item.update(self._get_contur_gt(item["points"], item["gt_lidarseg"]))
        return item

    def _point_process(self, item):
        if "fuser_lidar" in item:
            return item.pop("fuser_lidar")
        elif "middle_lidar" in item:
            return item.pop("middle_lidar")
        else:
            raise ValueError("No valid lidar('fuser_lidar', 'middle_lidar') can be used!")

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points", "frame_token", "frame_id", "gt_lidarseg", "gt_panotic"]:
                    ret[key] = val
                else:
                    ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret

    def _get_point_cloud(self, idx, keys):
        """
        Load point cloud with given sample index.

        Args:
            idx (int): Sampled index
            keys(str): Lidar name
        Returns:
            point cloud (Dict[str, np.ndarray]): (N, 3), (x-y-z)
        """
        result = {}
        frame = self.frame_data_list[idx]
        frame_id = idx
        result["frame_id"] = frame_id
        lidar_nori_id = frame["sensor_data"]
        for k in keys:
            nori_id = lidar_nori_id[k]["nori_id"]
            try:
                data = self.nori_fetcher.get(nori_id)
            except nori.exception.DataIDError:
                print("error getting lidar nori")
                return None
            pc_data_raw = np.load(io.BytesIO(data)).copy()
            pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(self.pc_fields)])
            result[k] = pc_data
        return result

    def _get_seg_gt(self, idx):
        """
        load segmentation label
        """
        label = self.frame_data_list[idx]["freespace_label"]
        label = np.array(label).reshape(-1, 1)

        reorder_label = label.copy()
        for k, v in self.model_config.learning_map.items():
            reorder_label[label == k] = v  # reorder by class priority
        return {"gt_lidarseg": reorder_label}

    def _get_contur_gt(self, points, seg_label):
        """
        Compute contur ground truth from point label.
        Args:
            points (np.ndarray): points (N, 4)
            seg_label (np.ndarray): points segment class label, (N, 1)
        Returns:
            freespace contur (dict): contur raidus (N_grid), contur classes (N_grid)

        """
        obstacle_points = points[seg_label.reshape(-1) != self.freespace_label]
        free_space = np.ones((self.N_grid)) * 10000
        distances = np.sqrt(obstacle_points[:, 0] ** 2 + obstacle_points[:, 1] ** 2)
        thetas = np.arctan2(obstacle_points[:, 1], obstacle_points[:, 0]) / np.pi * 180 + 180  # 与x轴的角度
        thetaids = np.round(thetas % self.N_grid).astype(np.int64)
        thetaids[thetaids == self.N_grid] = 0

        label = seg_label[seg_label.reshape(-1) != self.freespace_label]
        boundary_cls = np.zeros((self.N_grid))
        for i, point in enumerate(obstacle_points):
            if np.abs(point[0]) < self.neighbor_roi[0] and np.abs(point[1]) < self.neighbor_roi[1]:
                continue
            if np.abs(point[2] > self.neighbor_roi[2]):  # 如果障碍物高度高于车高，不会记为最近可行驶距离
                continue
            thetaid = thetaids[i]
            d = distances[i]
            if free_space[thetaid] > d and d > 1:
                free_space[thetaid] = d
                boundary_cls[thetaid] = label[i]
        boundary_cls[free_space >= 10000] = -1
        free_space[free_space >= 10000] = self.model_config.target_assigner_infinite
        return {"contur_rs": free_space, "contur_cs": boundary_cls}

    def get_contur_from_bevgrid(self, seg_label, gt_labels):
        """
        由BEV grid生成轮廓, 需要依赖gt生成mask
        Args:
            seg_label (np.ndarray): Pred/GT 的BEV grid, (H, W)
            gt_labels (np.ndarray): GT的BEV grid, (H, W)
        Returns:
            freespace contur (np.ndarray): (N_grid,)
        """
        mask = gt_labels != -1
        free_space = np.ones((self.N_grid)) * 10000
        flag = mask * (seg_label < self.model_config.seg_head_confidence_threshold)
        x, y = np.meshgrid(np.arange(seg_label.shape[1]), np.arange(seg_label.shape[0]))
        x = x[flag] * self.data_configs_output.voxel_size[0] + self.data_configs_output.point_cloud_range[0]
        y = y[flag] * self.data_configs_output.voxel_size[1] + self.data_configs_output.point_cloud_range[1]

        distances = np.sqrt(x ** 2 + y ** 2)
        thetas = np.arctan2(y, x) / np.pi * 180 + 180  # 与x轴的角度
        thetaids = np.round(thetas % self.N_grid).astype(np.int64)
        thetaids[thetaids == self.N_grid] = 0

        for i, (x, y) in enumerate(zip(x, y)):
            if np.abs(x) < self.neighbor_roi[0] and np.abs(y) < self.neighbor_roi[1]:
                continue
            thetaid = thetaids[i]
            d = distances[i]
            if free_space[thetaid] > d and d > 1:
                free_space[thetaid] = d
        return free_space

    def generate_prediction_dicts(self, pred_item, data):
        n = data.pop("batch_size")
        for k, v in pred_item.items():
            if isinstance(v, torch.Tensor):
                pred_item[k] = v.cpu().numpy()
            elif isinstance(v, list):
                pred_item[k] = [x.cpu().numpy() for x in v]

        res = []
        data_list = []
        for i in range(n):
            res.append({k: v[i] for k, v in pred_item.items()})
            data_list.append({k: v[i] for k, v in data.items()})

        for frame, raw in zip(res, data_list):
            frame["frame_token"] = raw["frame_token"]
            point = raw["points"].cpu().numpy()
            frame["gt_lidarseg"] = raw["gt_lidarseg"].cpu().numpy().reshape(-1)
            frame["bev_labels"] = frame["bev_labels"][0]
            frame["pred_seg"] = frame["pred_seg"][0]
            if "pred_lidarseg" not in frame:
                frame["pred_lidarseg"] = self.bev_predition_to_point(point, frame["pred_seg"])
            if "pred_coords" in frame:
                frame["pred_contur"] = frame.pop("pred_coords")
                frame["pred_contur_cls"] = frame.pop("pred_contur_cls")
            else:
                frame["pred_contur"] = self.get_contur_from_bevgrid(frame["pred_seg"], frame["bev_labels"])
            frame["gt_contur"] = self.get_contur_from_bevgrid(frame["bev_labels"], frame["bev_labels"])
        return res

    def bev_predition_to_point(self, frame_point, frame_bev_seg):
        """
        bev segmenation prediction transform to point prediction
        Args:
            frame_point (np.ndarray): 单帧的原始数据，已过滤ROI (N, 4)
            frame_bev_seg (np.ndarray): 单帧的BEV map分割结果 (H, W)
        Return:
            pointwise prediction: np.ndarray, (Nx1)
        """
        gx = self.data_configs_output.voxel_size[0]
        gy = self.data_configs_output.voxel_size[1]
        offset_x = self.data_configs_output.point_cloud_range[0]
        offset_y = self.data_configs_output.point_cloud_range[1]
        nx = self.data_configs_output.grid_size[0]
        ny = self.data_configs_output.grid_size[1]

        idx_xs = np.minimum(np.maximum(0, (frame_point[:, 0] - offset_x) // gx), nx - 1)
        idx_ys = np.minimum(np.maximum(0, (frame_point[:, 1] - offset_y) // gy), ny - 1)
        idx_xs = idx_xs.astype(np.int32)
        idx_ys = idx_ys.astype(np.int32)

        tensor_0 = frame_bev_seg[idx_ys]
        pred = tensor_0[np.arange(0, len(frame_point)), idx_xs]

        # pred = self.ransac_filter(frame_point, pred)
        return pred

    @staticmethod
    def ransac_filter(points, pred, thickness_th=0.1, pred_th=0.4):
        """
        根据point wise预测结果生成ransac方程，根据地面方程重新预测地面
        Args:
            points (np.ndarray): 单帧的原始数据，已过滤ROI
            pred (np.ndarray): point wise 分类结果
            thinkness (float): 地面厚度
            pred_th (float): 分割阈值
        Return:
            pred (np.ndarray): point wise 分类结果
        """
        print("calculating ransac")
        floor_points = points[pred > pred_th]  # 用ransac过滤只能固定参数了

        XY_f = floor_points[:, :2]
        z_f = floor_points[:, 2]
        ransac = linear_model.RANSACRegressor(max_trials=1000)
        ransac.fit(XY_f, z_f)

        XY = points[:, :2]
        z = points[:, 2]
        z_ransac = ransac.predict(XY)
        flag = np.abs(z_ransac - z) < thickness_th
        return pred * flag

    def _gt_eval_formatter(self, preds):
        # convert gt data into eval format
        data = {}
        for frame_token in preds:
            label = {
                "gt_lidarseg": preds[frame_token].pop("gt_lidarseg"),
                "bev_labels": preds[frame_token].pop("bev_labels"),
                "gt_contur": preds[frame_token]["gt_contur"],
                "gt_contur_cls": preds[frame_token].pop("gt_contur_cls", None),
            }
            data[frame_token] = label
        return data

    def _pred_eval_formatter(self, preds):
        data = {}
        for pred in preds:
            frame_token = pred["frame_token"]
            data[frame_token] = pred
        return data

    def evaluation_batch(self, preds, **kwargs):
        preds = self._pred_eval_formatter(preds)
        gts = self._gt_eval_formatter(preds)
        eval_output_dir = refile.smart_path_join(
            kwargs["output_dir"],
            "eval_results",
        )
        evaluator = FreespaceEvalBatch(
            gts=gts, preds=preds, output_dir=eval_output_dir, n_boundary_cls=len(self.class_names)
        )
        res_dict = evaluator.main()
        return res_dict

    def evaluation_result_reduce(self, results, **kwargs):
        evalutaor = FreespaceEvalBatch({}, {})
        res_str, md_str, res_dict = evalutaor.result_reduce(results)
        return res_str, md_str, res_dict

    def select_dump_result(self, pred_batch, result_dict={}):
        # 筛选需要dump的数据
        pop_keys = ["gt_lidarseg", "pred_lidarseg", "pred_seg", "bev_labels"]
        save_keys = ["bevmap_iou@th0.4", "boundary_length_error"]
        for frame in pred_batch:
            for key in pop_keys:
                frame.pop(key, None)
            for key in save_keys:
                frame[key] = result_dict[frame["frame_token"]][key]
        return pred_batch


class PrivateDatasetFreespaceInference(PrivateDatasetFreespace):
    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
        data_configs_output=None,
    ):
        """
        没有label，专门用于inference的data, 输入是json, 不用redis。只inference关键帧
        """
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.data_configs = data_configs
        self.data_configs_output = (
            data_configs_output if data_configs_output is not None else data_configs
        )  # used when output voxel size is different from input
        self.lidar_key_list = lidar_key_list
        self.pc_fields = pc_fields
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)
        self.training = False
        self.downsample_rate = getattr(data_configs, "downsample_rate", 1)

        # self._get_split_data_paths(data_paths)
        self._get_split_data_paths_car3(data_paths)
        self.data_paths = ppl_dir_filter(self.data_paths)
        print("Number of jsons", len(self.data_paths))
        self.frame_data_list = self._prepare_data(self.data_paths)

        if is_nori_read:
            self.nori_fetcher = None

        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )

    def _get_split_data_paths(self, input_data_paths):
        """
        get json paths
        input_data_paths: json root, for train: root/ppl_xxx_det/jsons; for test: root/cq/ppl/jsons
        return: s3 json_paths
        """
        self.data_paths = []
        for data_dir_item in input_data_paths:
            if refile.smart_isdir(data_dir_item):
                if "det" in data_dir_item:
                    for ppl in smart_listdir(data_dir_item):
                        json_paths = refile.smart_glob(refile.smart_path_join(data_dir_item, ppl, "*.json"))
                        self.data_paths.extend(json_paths)
                else:
                    for sub_dir in refile.smart_listdir(data_dir_item):
                        sub_dir = smart_path_join(data_dir_item, sub_dir)
                        if not smart_isdir(sub_dir):
                            continue
                        for ppl in smart_listdir(sub_dir):
                            json_paths = refile.smart_glob(refile.smart_path_join(sub_dir, ppl, "*.json"))
                            self.data_paths.extend(json_paths)
            elif data_dir_item.endswith(".json"):
                self.data_paths.append(data_dir_item)
            else:
                raise ValueError(f"Json Files / json files dir expected, but got <{data_dir_item}>")

        self.data_paths = sorted(self.data_paths)

    def _get_split_data_paths_car3(self, input_data_paths):
        """
        get json paths for car3-like format
        input_data_paths: json root, for train: root/ppl_xxx_det/jsons; for test: root/cq/ppl/jsons
        return: s3 json_paths
        """
        self.data_paths = []
        for data_dir_item in input_data_paths:
            data_dir_item, version = data_dir_item
            if refile.smart_isdir(data_dir_item):
                if "det" in data_dir_item:
                    for ppl in smart_listdir(data_dir_item):
                        json_paths = refile.smart_glob(refile.smart_path_join(data_dir_item, ppl, "*/*.json"))
                        json_paths = [p for p in json_paths if version in p]
                        self.data_paths.extend(json_paths)
                else:
                    for sub_dir in refile.smart_listdir(data_dir_item):
                        sub_dir = smart_path_join(data_dir_item, sub_dir)
                        if not smart_isdir(sub_dir):
                            continue
                        for ppl in smart_listdir(sub_dir):
                            json_paths = refile.smart_glob(refile.smart_path_join(sub_dir, ppl, "*/*.json"))
                            json_paths = [p for p in json_paths if version in p]
                            self.data_paths.extend(json_paths)
            elif data_dir_item.endswith(".json"):
                self.data_paths.append(data_dir_item)
            else:
                raise ValueError(f"Json Files / json files dir expected, but got <{data_dir_item}>")

        self.data_paths = sorted(self.data_paths)

    def _prepare_data(self, data_paths, only_key_frame=True):
        """add frame token and extract keep_frame"""
        sample_frames = []
        for json_path in tqdm.tqdm(data_paths, desc="preparing data"):
            with refile.smart_open(json_path) as rf:
                json_data = json.load(rf)
                frames = json_data.pop("frames")
                n_key_frame = -1
                for frame in frames:
                    if only_key_frame and not frame["is_key_frame"]:
                        continue
                    n_key_frame += 1
                    if n_key_frame % self.downsample_rate:
                        continue
                    frame_id = frame["frame_id"]
                    frame_token = "%".join([json_path, str(frame_id)])
                    frame["frame_token"] = frame_token
                    sample_frames.append(frame)
        return sample_frames

    def __getitem__(self, idx: int):
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        item = {}
        item.update(self._get_point_cloud(idx, keys=self.lidar_key_list))
        item["points"] = self._point_process(item)
        item["gt_panotic"] = np.array([])
        item["frame_token"] = self.frame_data_list[idx]["frame_token"]

        item = self._run_aug_and_process(item)
        return item

    def generate_prediction_dicts(self, pred_item, data):
        n = len(pred_item["pred_seg"])

        for k, v in pred_item.items():
            pred_item[k] = v.cpu().numpy()
        pred_item["frame_token"] = data["frame_token"]

        res = []
        for i in range(n):
            res.append({k: v[i] for k, v in pred_item.items()})
        return res

    def select_dump_result(self, pred_batch, result_dict={}):
        # 筛选需要dump的数据
        pop_keys = ["pred_seg_binary", "bev_labels"]
        for frame in pred_batch:
            for key in pop_keys:
                frame.pop(key, None)
        return pred_batch


class PrivateDatasetFreespaceInferenceCODA(PrivateDatasetFreespaceInference):
    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        coda_save_path,
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
    ):
        super().__init__(
            data_configs, model_configs, data_paths, lidar_key_list, img_key_list, is_nori_read, pc_fields, training
        )
        self.coda_save_path = coda_save_path

    def _prepare_data(self, data_paths, only_key_frame=True):
        """add frame token and extract keep_frame"""
        sample_frames = []
        for json_path in tqdm.tqdm(data_paths, desc="preparing data"):
            with refile.smart_open(json_path) as rf:
                json_data = json.load(rf)
                frames = json_data.pop("frames")
                for i, frame in enumerate(frames):
                    if only_key_frame and not frame["is_key_frame"]:
                        continue
                    frame_id = frame["frame_id"]
                    frame_token = "%".join([json_path, str(frame_id)])
                    frame["frame_token"] = frame_token
                    sample_frames.append(frame)
        return sample_frames

    def __getitem__(self, idx: int):
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        item = {}
        item.update(self._get_point_cloud(idx, keys=self.lidar_key_list))  # 获取原始帧的点云（包含了人机非等原始点云）
        item["points"] = self._point_process(item)
        item["raw_points"] = item["points"].copy()
        item["gt_panotic"] = np.array([])
        item["frame_token"] = self.frame_data_list[idx]["frame_token"]
        # 人机非障碍物box label & prelabel
        item["labels"] = self.frame_data_list[idx]["labels"] if "labels" in self.frame_data_list[idx] else []
        item["pre_labels"] = (
            self.frame_data_list[idx]["pre_labels"] if "pre_labels" in self.frame_data_list[idx] else []
        )
        item = self._run_aug_and_process(item)
        return item

    def generate_prediction_dicts(self, pred_item, data):
        n = data.pop("batch_size")
        for k, v in pred_item.items():
            if isinstance(v, torch.Tensor):
                pred_item[k] = v.cpu().numpy()
            elif isinstance(v, list):
                pred_item[k] = [x.cpu().numpy() for x in v]

        res = []
        data_list = []
        for i in range(n):
            res.append({k: v[i] for k, v in pred_item.items()})
            data_list.append({k: v[i] for k, v in data.items()})

        for frame, raw in zip(res, data_list):
            frame["frame_token"] = raw["frame_token"]
            point = raw["raw_points"].cpu().numpy()
            label = raw["labels"] if "labels" in raw else raw["pre_labels"]
            flag = self.run_model(point, label, frame["pred_seg"])
            token = frame["frame_token"].split(".")[0]
            token = token.split("s3://")[-1]
            token = os.path.join(self.coda_save_path, token)
            frame_id = frame["frame_token"].split("%")[1]
            npy_path = refile.smart_path_join(token, f"{frame_id}.npy")
            self.to_s3_npy(flag, npy_path)
            logger.info(f"Freespace-label save in {npy_path}")
        return []

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in [
                    "points",
                    "raw_points",
                    "frame_token",
                    "frame_id",
                    "gt_lidarseg",
                    "gt_panotic",
                    "labels",
                    "pre_labels",
                ]:
                    ret[key] = val
                else:
                    ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret

    @staticmethod
    def to_s3_npy(data: np.array, s3_uri: str):
        host = "http://oss.i.brainpp.cn"
        client = boto3.client("s3", endpoint_url=host)
        bytes_ = BytesIO()
        np.save(bytes_, data, allow_pickle=True)
        bytes_.seek(0)
        parsed_s3 = urlparse(s3_uri)
        client.upload_fileobj(Fileobj=bytes_, Bucket=parsed_s3.netloc, Key=parsed_s3.path[1:])

    def run_model(self, raw_points, label, bevseg, lane_points=None):
        # 带模型预测的可行驶区域分割
        filters_backup = self.data_configs.filters_backup
        if bevseg.shape[0] == 0:
            filters = filters_backup
        else:
            filters = self.data_configs.filters

        points = raw_points[:, :3]
        N = len(points)
        floor_flags = np.ones((len(points)))

        try:
            for i, filter in enumerate(filters):
                res = filter(points, floor_flags, labels=label, bevseg=bevseg)
                points, floor_flags = res["points"], res["floor_flags"]
                if i == 0:
                    roi_flags = floor_flags
                if "dynamic_flags" in res:
                    dynamic_flags = res["dynamic_flags"]

            # 根据地面方程过滤地面点 car3 car4需要出远处的点
            floor_point, floor_flags = ransac_filter(points, floor_flags, thickness_th=0.3)
        except ValueError:  # 模型失效，ransace没有点
            floor_flags = np.ones((len(points)))
            for i, filter in enumerate(filters_backup):
                res = filter(points, floor_flags, labels=label, bevseg=bevseg)
                points, floor_flags = res["points"], res["floor_flags"]
                if i == 0:
                    roi_flags = floor_flags
                if "dynamic_flags" in res:
                    dynamic_flags = res["dynamic_flags"]

            # 根据地面方程过滤地面点
            if floor_flags.astype(np.float32).sum() >= 2:
                floor_point, floor_flags = ransac_filter(points, floor_flags, thickness_th=0.2)

        # coda不需要过滤被dynamic物体所遮挡的点云
        if lane_points:  # 车道线的点有无中生有的，不会作为预测结果，但因为不影响 free_distance_filter, 返回前再截断
            floor_flags = floor_flags[:N]
            points = points[:N, :]
        floor_flags = floor_flags * roi_flags
        flags = floor_flags.astype(np.int8)  # label=0：背景点，1：“可行驶区域”，2：“静态障碍物边界”，3：“动态障碍物边界”
        flags[dynamic_flags] = 3

        return flags


class PrivateDatasetFreespaceInferenceContinus(PrivateDatasetFreespaceInference):
    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
        data_configs_output=None,
    ):
        """
        没有label，专门用于inference的data, 所有帧都inference
        """
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.data_configs = data_configs
        self.data_configs_output = (
            data_configs_output if data_configs_output is not None else data_configs
        )  # used when output voxel size is different from input

        self.lidar_key_list = lidar_key_list
        self.pc_fields = pc_fields
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)
        self.training = False

        self._get_split_data_paths(data_paths)
        self.data_paths = ppl_dir_filter(self.data_paths)
        print("Number of jsons", len(self.data_paths))
        self.frame_data_list = self._prepare_data(self.data_paths, only_key_frame=False)

        if is_nori_read:
            self.nori_fetcher = None

        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )

    def _prepare_data(self, data_paths, only_key_frame=True):
        """add frame token and extract keep_frame"""
        sample_frames = []
        for json_path in tqdm.tqdm(data_paths, desc="preparing data"):
            down_sample_rate = 1
            with refile.smart_open(json_path) as rf:
                json_data = json.load(rf)
                frames = json_data.pop("frames")
                for i, frame in enumerate(frames):
                    if only_key_frame and not frame["is_key_frame"]:
                        continue
                    if i % down_sample_rate:
                        continue
                    frame_id = frame["frame_id"]
                    frame_token = "%".join([json_path, str(frame_id)])
                    frame["frame_token"] = frame_token
                    sample_frames.append(frame)
        return sample_frames


def ppl_dir_filter(ppl_dirs):
    # 过滤ppl路径，用于有选择地inference, 每批数据可以根据不同的规则改 注意这里是json名称规则，和freespace里不一样
    valid_ppl_dirs = ppl_dirs
    assert len(valid_ppl_dirs) > 0, "No valid ppl dirs."
    return valid_ppl_dirs


class PrivateDatasetFreespaceMultiClass(PrivateDatasetFreespace):
    """
    多分类模型dataset, 主要是评测和后处理不同
    """

    def evaluation_batch(self, preds, **kwargs):
        preds = self._pred_eval_formatter(preds)
        gts = self._gt_eval_formatter(preds)
        eval_output_dir = refile.smart_path_join(
            kwargs["output_dir"],
            "eval_results",
        )
        evaluator = FreespaceMultiClassEvalBatch(
            gts=gts, preds=preds, output_dir=eval_output_dir, n_boundary_cls=len(self.class_names)
        )
        res_dict = evaluator.main()
        return res_dict

    def evaluation_result_reduce(self, results, **kwargs):
        evalutaor = FreespaceMultiClassEvalBatch({}, {}, n_boundary_cls=len(self.class_names))
        res_str, md_str, res_dict = evalutaor.result_reduce(results)
        return res_str, md_str, res_dict

    def select_dump_result(self, pred_batch, result_dict={}):
        # 筛选需要dump的数据
        preds = self._pred_eval_formatter(pred_batch)
        gts = self._gt_eval_formatter(preds)
        pop_keys = ["gt_lidarseg", "pred_lidarseg", "pred_seg", "bev_labels", "pred_seg_binary"]
        save_keys = ["bevmap_iou", "boundary_length_error"]

        output = []
        for frame_token in preds:
            frame = preds[frame_token]
            frame.update(gts[frame_token])
            frame["frame_token"] = frame_token
            for key in pop_keys:
                frame.pop(key, None)
            for key in save_keys:
                frame[key] = result_dict[frame["frame_token"]][key]
            output.append(frame)
        return output


class PrivateDatasetFreespaceBEVMultiClass(PrivateDatasetFreespaceMultiClass):
    def get_contur_from_bevgrid(self, seg_label, gt_labels):
        """
        由BEV grid生成轮廓, 需要依赖gt生成mask
        Args:
            seg_label (np.ndarray): Pred/GT 的BEV grid, (H, W)
            gt_labels (np.ndarray): GT的BEV grid, (H, W)
        Returns:
            freespace contur (np.ndarray): (N_grid,)
            contur type (np.ndarray): (N_grid,)
        """
        mask = gt_labels != -1
        free_space = np.ones((self.N_grid)) * 10000
        boundary_cls = np.zeros((self.N_grid))
        flag = mask * (seg_label != self.freespace_label)
        gridx, gridy = np.meshgrid(np.arange(seg_label.shape[1]), np.arange(seg_label.shape[0]))
        xs = gridx[flag] * self.data_configs_output.voxel_size[0] + self.data_configs_output.point_cloud_range[0]
        ys = gridy[flag] * self.data_configs_output.voxel_size[1] + self.data_configs_output.point_cloud_range[1]

        distances = np.sqrt(xs ** 2 + ys ** 2)
        thetas = np.arctan2(ys, xs) / np.pi * 180 + 180  # 与x轴的角度
        thetaids = np.round(thetas % self.N_grid).astype(np.int64)
        thetaids[thetaids == self.N_grid] = 0

        for i, (x, y, gx, gy) in enumerate(zip(xs, ys, gridx[flag], gridy[flag])):
            if np.abs(x) < self.neighbor_roi[0] and np.abs(y) < self.neighbor_roi[1]:
                continue
            thetaid = thetaids[i]
            d = distances[i]
            if free_space[thetaid] > d and d > 1:
                free_space[thetaid] = d
                boundary_cls[thetaid] = seg_label[gy][gx]
        boundary_cls[free_space >= 10000] = -1
        return free_space, boundary_cls

    def generate_prediction_dicts(self, pred_item, data):
        n = data.pop("batch_size")
        for k, v in pred_item.items():
            if isinstance(v, torch.Tensor):
                pred_item[k] = v.cpu().numpy()
            elif isinstance(v, list):
                pred_item[k] = [x.cpu().numpy() for x in v]

        res = []
        data_list = []
        for i in range(n):
            res.append({k: v[i] for k, v in pred_item.items()})
            data_list.append({k: v[i] for k, v in data.items()})

        for frame, raw in zip(res, data_list):
            frame["frame_token"] = raw["frame_token"]
            point = raw["points"].cpu().numpy()
            frame["bev_labels"] = frame["bev_labels"][0]
            frame["gt_lidarseg"] = raw["gt_lidarseg"].cpu().numpy()
            if "pred_lidarseg" not in frame:
                frame["pred_lidarseg"] = self.bev_predition_to_point(point, frame["pred_seg"])
            if "contur_rs" in raw:
                frame["gt_contur"] = raw["contur_rs"].cpu().numpy()
                frame["gt_contur_cls"] = raw["contur_cs"].cpu().numpy()
            else:
                frame["gt_contur"], frame["gt_contur_cls"] = self.get_contur_from_bevgrid(
                    frame["bev_labels"], frame["bev_labels"]
                )
            if "pred_contur_cls" in frame:  # 不会只预测contur_cs 不预测 contur_rs
                frame["pred_contur_cls"] = frame.pop("pred_contur_cls")
            if "pred_coords" in frame:
                frame["pred_contur"] = frame.pop("pred_coords")
            else:
                frame["pred_contur"], frame["pred_contur_cls"] = self.get_contur_from_bevgrid(
                    frame["pred_seg"], frame["bev_labels"]
                )
        return res
