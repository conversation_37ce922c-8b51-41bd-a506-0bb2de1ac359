import copy
from collections import defaultdict
from functools import lru_cache
import numpy as np
import refile
import pickle
import nori2 as nori
from tqdm import tqdm


from data3d.datasets.private import PrivateDataset
from data3d.transforms import transforms3d

from perceptron.data.seg3d.dataset.private_data.dataset_freespace import PrivateDatasetFreespace
from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval
from perceptron.utils.det3d_utils import common_utils


def load_pkl(path):
    with refile.smart_open(path, "rb") as f:
        return pickle.load(f)


@lru_cache()
def date_mapping(date):
    # seg ppl path map to det path
    MAPPing = {
        "20220521": "s3://tf-labeled-res/20220601_det_yueying_checked_footprintRFU/",
        "20220522": "s3://tf-labeled-res/20220601_det_yueying_checked_footprintRFU/",
        "20220523": "s3://tf-labeled-res/20220601_det_yueying_checked_footprintRFU/",
        "20220524": "s3://tf-labeled-res/20220601_det_yueying_checked_footprintRFU/",
        "20220525": "s3://tf-labeled-res/20220601_det_yueying_checked_footprintRFU/",
        "20220529": "s3://tf-labeled-res/20220608_det_yueying_checked/",
    }
    return MAPPing[date]


class PrivateDatasetFreespaceDet(PrivateDatasetFreespace, PrivateDatasetWithEval):
    """
    以freespace data为主建立framelist，每个fs frame都有det标注都会参与训练；不是每帧det都会参与训练。
    """

    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        data_paths_det=[],
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        class_names_det=None,
        only_key_frame=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
        data_configs_output=None,
    ):
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.lidar_key_list = lidar_key_list
        self.data_configs = data_configs
        self.model_config = model_configs
        self.data_configs_output = (
            data_configs_output if data_configs_output is not None else data_configs
        )  # used when output voxel size is different from input
        self.class_names_seg = model_configs.labels.values()
        self.class_names_det = class_names_det
        self.pc_fields = pc_fields
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)
        self.training = training
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features

        self.with_contur = getattr(data_configs, "with_contur", False)
        self.N_grid = model_configs.N_grid
        self.neighbor_roi = data_configs.neighbor_roi
        self.freespace_label = 1  # freespace label mapping

        self._get_split_data_paths(data_paths)  # get fs pkl path
        self.data_paths_det = data_paths_det
        self._get_split_data_paths_det()  # get det ppl paths
        self.frame_data_list = self._load_redis_data(self.data_paths)

        if is_nori_read:
            self.nori_fetcher = None

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 4, np.pi / 4]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )

        self.dataset_det = PrivateDataset(
            class_names=self.class_names_det,
            data_paths=self.data_paths_det,
            img_key_list=img_key_list,
            lidar_key_list=lidar_key_list,
            only_key_frame=only_key_frame,
            pc_fields=pc_fields,
            used_echo_id=[1, 2, 3],  # 因为lidar把几次回波的points都标了，所以无法限制
        )
        self._nori2det_idx()

    def _nori2det_idx(self):
        # 因为self.dataset_det制作过程中会根据原始ppl路径重新sort一次，导致self.framelist和self.dataset_det.framelist无法一一对应
        assert len(self.lidar_key_list) == 1
        self.nori2det_idx = {}
        for i, item in tqdm(enumerate(self.dataset_det.frame_data_list), desc="preparing nori2idx"):
            self.nori2det_idx[item["sensor_data"][self.lidar_key_list[0]]["nori_id"]] = i

    def _get_split_data_paths_det(self):
        # add freespace dataset path if not in input.data_paths_det
        for ppl in tqdm(self.data_paths, desc="collecting det paths"):
            ppl_name = ppl.split("/")[-2]
            date = ppl_name.split("_")[2]
            if self.training:
                det_ppl_path = f"s3://tf-labeled-res/{date}_det_yueying_checked/{ppl_name}"
                if not refile.smart_exists(det_ppl_path):
                    det_ppl_path = date_mapping(date) + f"{ppl_name}"
                if det_ppl_path not in self.data_paths_det and det_ppl_path + "/" not in self.data_paths_det:
                    self.data_paths_det.append(det_ppl_path)

    def _run_aug_and_process(self, data_dict):
        if self.training:
            assert "gt_boxes" in data_dict, "gt_boxes should be provided for training"
            if len(data_dict["gt_boxes"]) == 0:
                data_dict = self.data_augmentor.forward(data_dict={**data_dict, "gt_boxes": []})
            else:
                gt_boxes_mask = np.array([n in self.class_names_det for n in data_dict["gt_names"]], dtype=np.bool_)
                data_dict = self.data_augmentor.forward(data_dict={**data_dict, "gt_boxes_mask": gt_boxes_mask})
        elif hasattr(self, "test_augmentor"):
            data_dict = self.test_augmentor.forward(data_dict=data_dict)

        if len(data_dict.get("gt_boxes", [])) > 0:
            selected = common_utils.keep_arrays_by_name(data_dict["gt_names"], self.class_names_det)
            data_dict["gt_boxes"] = data_dict["gt_boxes"][selected]
            data_dict["gt_names"] = data_dict["gt_names"][selected]
            gt_classes = np.array([self.class_names_det.index(n) + 1 for n in data_dict["gt_names"]], dtype=np.int32)
            gt_boxes = np.concatenate((data_dict["gt_boxes"], gt_classes.reshape(-1, 1).astype(np.float32)), axis=1)
            data_dict["gt_boxes"] = gt_boxes
        else:
            data_dict["gt_boxes"] = np.zeros((1, 8))

        data_dict = self.processor.forward(data_dict=data_dict)
        data_dict.pop("gt_names", None)
        data_dict.pop("num_points_in_gt", None)
        data_dict.pop("gt_boxes_mask", None)
        return data_dict

    def __len__(self):
        return len(self.frame_data_list)

    def __getitem__(self, idx: int):
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        item = {}
        frame = self.frame_data_list[idx]
        idx_det = self.nori2det_idx[frame["sensor_data"][self.lidar_key_list[0]]["nori_id"]]
        item_det = self.dataset_det[idx_det]  # get raw points, det_labels, and det_boxes
        item["points"] = self._point_process(item_det)[:, : self.data_configs.use_num_point_features]
        item["frame_token"] = frame["frame_token"]
        # seg label
        item.update(self._get_seg_gt(idx))
        item["gt_panotic"] = np.array([])
        assert len(item["points"]) == len(item["gt_lidarseg"])
        # det label
        if "labels" in item_det:
            item.update(
                {
                    "gt_names": copy.deepcopy(item_det["labels"]),
                    "gt_boxes": copy.deepcopy(item_det["gt_boxes"]),
                    "num_points_in_gt": copy.deepcopy(item_det["num_lidar_points"]),
                }
            )
        else:
            item["gt_boxes"] = []
        item = self._run_aug_and_process(item)
        if self.with_contur:
            item.update(self._get_contur_gt(item["points"], item["gt_lidarseg"]))
        return item

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        batch_size = len(batch_list)
        ret = {}

        for key, val in data_dict.items():
            try:
                if key in ["points", "frame_token", "frame_id", "gt_lidarseg", "gt_panotic"]:
                    ret[key] = val
                elif key in ["gt_boxes"]:
                    max_gt = max([len(x) for x in val])
                    batch_gt_boxes3d = np.zeros((batch_size, max_gt, val[0].shape[-1]), dtype=np.float32)
                    for k in range(batch_size):
                        batch_gt_boxes3d[k, : val[k].__len__(), :] = val[k]
                    ret[key] = batch_gt_boxes3d
                else:
                    ret[key] = np.stack(val, axis=0)
            except Exception:
                print("Error in collate_batch: key=%s" % key)
                raise TypeError
        ret["batch_size"] = batch_size
        return ret


class PrivateDatasetDetFreespace(PrivateDatasetFreespaceDet):
    """
    以det data为主建立framelist，每帧都有det标注都会参与训练。不一定每帧都含fs label, fs数据集不保证遍历。
    """

    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        data_paths_det=[],
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        class_names_det=None,
        only_key_frame=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
        data_configs_output=None,
    ):
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.lidar_key_list = lidar_key_list
        self.data_configs = data_configs
        self.model_config = model_configs
        self.data_configs_output = (
            data_configs_output if data_configs_output is not None else data_configs
        )  # used when output voxel size is different from input
        self.class_names_seg = model_configs.labels.values()
        self.class_names_det = class_names_det
        self.pc_fields = pc_fields
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)
        self.training = training
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features

        self.with_contur = getattr(data_configs, "with_contur", False)
        self.N_grid = model_configs.N_grid
        self.neighbor_roi = data_configs.neighbor_roi
        self.freespace_label = 1  # freespace label mapping

        self._get_split_data_paths(data_paths)  # get fs pkl path
        self.data_paths_det = data_paths_det
        self._get_split_data_paths_det()  # get det ppl paths
        self.frame_data_list = self._load_redis_data(self.data_paths)

        if is_nori_read:
            self.nori_fetcher = None

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="X"),
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi / 4, np.pi / 4]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )

        self.dataset_det = PrivateDataset(
            class_names=self.class_names_det,
            data_paths=self.data_paths_det,
            img_key_list=img_key_list,
            lidar_key_list=lidar_key_list,
            only_key_frame=only_key_frame,
            pc_fields=pc_fields,
            used_echo_id=[1, 2, 3],  # 因为lidar把几次回波的points都标了，所以无法限制
        )
        self._nori2fs_idx()

    def _nori2fs_idx(self):
        assert len(self.lidar_key_list) == 1
        self.nori2fs_idx = {}
        for i, item in tqdm(enumerate(self.frame_data_list), desc="preparing nori2idx"):
            self.nori2fs_idx[item["sensor_data"][self.lidar_key_list[0]]["nori_id"]] = i

    def __len__(self):
        return len(self.dataset_det.frame_data_list)

    def __getitem__(self, idx: int):
        if self.nori_fetcher is None:
            self.nori_fetcher = nori.Fetcher()
        item = {}

        item_det = self.dataset_det[idx]
        item["points"] = self._point_process(item_det)[:, : self.data_configs.use_num_point_features]

        lidar_nid = self.dataset_det.frame_data_list[idx]["sensor_data"][self.lidar_key_list[0]]["nori_id"]
        idx_fs = self.nori2fs_idx.get(lidar_nid, None)
        if idx_fs:
            frame = self.frame_data_list[idx_fs]
            item["frame_token"] = frame["frame_token"]
        # seg label
        item.update(self._get_seg_gt(idx_fs, len(item["points"])))
        item["gt_panotic"] = np.array([])
        assert len(item["points"]) == len(item["gt_lidarseg"])

        # det label
        if "labels" in item_det:
            item.update(
                {
                    "gt_names": copy.deepcopy(item_det["labels"]),
                    "gt_boxes": copy.deepcopy(item_det["gt_boxes"]),
                    "num_points_in_gt": copy.deepcopy(item_det["num_lidar_points"]),
                }
            )
        else:
            item["gt_boxes"] = []
        item = self._run_aug_and_process(item)
        if self.with_contur:
            item.update(self._get_contur_gt(item["points"], item["gt_lidarseg"]))
        return item

    def _get_seg_gt(self, idx, n_points=-1):
        """
        load segmentation label
        """
        if idx is not None:
            label = self.frame_data_list[idx]["freespace_label"]
            label = np.array(label).reshape(-1, 1)
        else:
            assert n_points > 0, "lack label, please provider number of points to generate default labels."
            label = np.ones((n_points, 1)) * -1

        reorder_label = label.copy()
        for k, v in self.model_config.learning_map.items():
            reorder_label[label == k] = v  # reorder by class priority
        return {"gt_lidarseg": reorder_label}


class PrivateDatasetDetFreespaceAug(PrivateDatasetDetFreespace):
    def __init__(
        self,
        data_configs,
        model_configs,
        data_paths,
        data_paths_det=[],
        lidar_key_list=["fuser_lidar"],
        img_key_list=[],
        is_nori_read=True,
        class_names_det=None,
        only_key_frame=True,
        pc_fields=["x", "y", "z", "i"],
        training=True,
        data_configs_output=None,
    ):
        assert lidar_key_list == ["middle_lidar"] or lidar_key_list == ["fuser_lidar"]
        self.lidar_key_list = lidar_key_list
        self.data_configs = data_configs
        self.model_config = model_configs
        self.data_configs_output = (
            data_configs_output if data_configs_output is not None else data_configs
        )  # used when output voxel size is different from input
        self.class_names_seg = model_configs.labels.values()
        self.class_names_det = class_names_det
        self.pc_fields = pc_fields
        self.point_cloud_range = np.array(self.data_configs.point_cloud_range)
        self.training = training
        self.use_lead_xyz = True
        self.num_point_features = self.data_configs.use_num_point_features

        self.with_contur = getattr(data_configs, "with_contur", False)
        self.N_grid = model_configs.N_grid
        self.neighbor_roi = data_configs.neighbor_roi
        self.freespace_label = 1  # freespace label mapping

        self._get_split_data_paths(data_paths)  # get fs pkl path
        self.data_paths_det = data_paths_det
        self._get_split_data_paths_det()  # get det ppl paths
        self.frame_data_list = self._load_redis_data(self.data_paths)

        if is_nori_read:
            self.nori_fetcher = None

        self.data_augmentor = transforms3d.Compose(
            [
                transforms3d.RandomFlip3D(along_axis="Y"),
                transforms3d.GlobalRotation(rot_range=[-np.pi * 0.1, np.pi * 0.1]),
                transforms3d.GlobalScaling(scale_range=(0.95, 1.05)),
                transforms3d.GlobalTranslation(noise_translate_std=[0.2, 0.2, 0.2]),
            ]
        )
        self.processor = transforms3d.Compose(
            [
                transforms3d.ObjectRangeFilter(self.point_cloud_range),
                transforms3d.PointShuffle(),
            ]
        )

        self.dataset_det = PrivateDataset(
            class_names=self.class_names_det,
            data_paths=self.data_paths_det,
            img_key_list=img_key_list,
            lidar_key_list=lidar_key_list,
            only_key_frame=only_key_frame,
            pc_fields=pc_fields,
            used_echo_id=[1, 2, 3],
        )
        self._nori2fs_idx()
