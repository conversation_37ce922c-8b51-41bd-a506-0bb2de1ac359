# nuScenes dev-kit.
# Code written by <PERSON><PERSON><PERSON> & <PERSON>, 2018.

from collections import defaultdict

import numpy as np
from sklearn import metrics as M

import pandas as pd
from pandas.io.json import json_normalize


class FreespaceEvalBatch:
    def __init__(self, gts, preds, output_dir: str = None, n_boundary_cls: int = 1):
        """
        # 逐帧计算结果并储存
        gts: ground_truth lane points, Dict[List[np.array]]
        preds: predict lane points, Dict[List[np.array]]
        n_boundary_cls: number of boundary type
        """
        self.compile(gts, preds)
        self.n_boundary_cls = n_boundary_cls
        self.ths = [np.round(th, 3) for th in np.arange(0.2, 0.8, 0.1)]  # compute iou at different thrsholds
        self.target_precsions = [0.9, 0.99]

    def compile(self, gts, preds):
        """List[dict] to Dict[dict], format name"""
        assert all([token in gts for token in preds])
        self.gt_point, self.pred_point, self.gt_bev, self.pred_bev = {}, {}, {}, {}
        self.pred_contur, self.gt_contur = {}, {}
        self.pred_contur_cls, self.gt_contur_cls = {}, {}

        for token in preds:
            gt_item = gts[token]
            pred_item = preds[token]
            self.gt_point[token] = gt_item["gt_lidarseg"]
            self.gt_bev[token] = gt_item["bev_labels"]
            self.pred_point[token] = pred_item["pred_lidarseg"]
            self.pred_bev[token] = pred_item["pred_seg"]

            if "pred_contur" in pred_item:
                self.pred_contur[token] = pred_item["pred_contur"]
                self.gt_contur[token] = gt_item["gt_contur"]
            if "pred_contur_cls" in pred_item:
                self.pred_contur_cls[token] = pred_item["pred_contur_cls"]
                self.gt_contur_cls[token] = gt_item["gt_contur_cls"]

    def IoU_binary_single(self, pred, gt, threshold):
        """
        compute IoU of single frame
        """
        mask = gt != -1
        y_true = (gt == 1).astype(np.int)[mask]
        y_pred = (pred >= threshold).astype(np.int)[mask]
        iou = M.jaccard_score(y_true.reshape(-1), y_pred.reshape(-1))
        return iou

    def boundary_length_error_single(self, pred, gt):
        return np.abs(pred - gt).mean()

    def recall_precision_single(self, pred, gt, n_class=1):
        """preds是softmax的结果，直接计算分类准确率。"""
        if gt is None or pred is None:
            return 0, 0
        mask = gt != -1
        y_true = gt[mask]
        y_pred = pred[mask]
        precision, recall, _, _ = M.precision_recall_fscore_support(y_true, y_pred, labels=np.arange(n_class))

        uniques_true = np.unique(y_true)
        uniques_pred = np.unique(y_pred)
        for i in range(n_class):
            if i not in uniques_true:
                recall[i] = -1
            if i not in uniques_pred:
                precision[i] = -1
        return recall, precision

    def main(self):
        # evaluation
        res_dict = defaultdict(dict)
        for token in self.pred_bev:
            # compute bev iou with different threshold
            for th in self.ths:
                iou = self.IoU_binary_single(self.pred_bev[token], self.gt_bev[token], threshold=th)
                res_dict[token][f"bevmap_iou@th{th}"] = iou
            # compute point iou with different threshold
            for th in self.ths:
                iou = self.IoU_binary_single(self.pred_point[token], self.gt_point[token], threshold=th)
                res_dict[token][f"point_iou@th{th}"] = iou

            # compute boundary classification p/r and distance error
            if len(self.pred_contur):
                res_dict[token]["boundary_length_error"] = self.boundary_length_error_single(
                    self.pred_contur[token], self.gt_contur[token]
                )
            else:
                res_dict[token]["boundary_length_error"] = 0
            if len(self.pred_contur_cls):
                r, p = self.recall_precision_single(
                    self.pred_contur_cls[token], self.gt_contur_cls[token], n_class=self.n_boundary_cls
                )
                res_dict[token]["boundary_type_precision"] = p
                res_dict[token]["boundary_type_recall"] = r
            else:
                res_dict[token]["boundary_type_precision"] = np.array([0])
                res_dict[token]["boundary_type_recall"] = np.array([0])

        return res_dict

    def result_reduce(self, results):
        """Merge results from each step. Merge by scene.
        results: List[dict] evaluation result of each step. Dict key is frame_token
        """
        # gather
        res_dict = {}
        for ress in results:
            for token, res in ress.items():
                scene = token.split("/")[-3]
                res_dict.setdefault(scene, defaultdict(float))
                res_dict[scene]["n_pair"] += 1
                for th in self.ths:
                    res_dict[scene][f"bevmap_iou@th{th}"] += res[f"bevmap_iou@th{th}"]
                    res_dict[scene][f"point_iou@th{th}"] += res[f"point_iou@th{th}"]
                res_dict[scene]["boundary_length_error"] += res["boundary_length_error"]
                res_dict[scene]["boundary_type_precision"] += res["boundary_type_precision"]
                res_dict[scene]["boundary_type_recall"] += res["boundary_type_recall"]

        # average by scene
        res_str = "EvalResults: \n"
        for th in self.ths:
            bev_iou_all = 0
            point_iou_all = 0
            n_pair = 0
            res_str1 = "Th {:.02f} BEV map IoU   | ".format(th)
            res_str2 = "Th {:.02f} point-wise IoU| ".format(th)
            for scene in res_dict:
                if scene == "all":
                    continue
                n_pair += res_dict[scene]["n_pair"]
                bev_iou_all += res_dict[scene][f"bevmap_iou@th{th}"]
                iou = res_dict[scene][f"bevmap_iou@th{th}"] / res_dict[scene]["n_pair"]
                res_dict[scene][f"bevmap_iou@th{th}"] = iou
                res_str1 += "{}-{:.04f}| ".format(scene, iou)

                point_iou_all += res_dict[scene][f"point_iou@th{th}"]
                iou = res_dict[scene][f"point_iou@th{th}"] / res_dict[scene]["n_pair"]
                res_dict[scene][f"point_iou@th{th}"] = iou
                res_str2 += "{}-{:.04f}| ".format(scene, iou)
            # all scene
            scene = "all"
            res_dict.setdefault(scene, defaultdict(float))
            res_dict[scene]["n_pair"] = n_pair
            iou = bev_iou_all / n_pair
            res_dict[scene][f"bevmap_iou@th{th}"] = iou
            res_str1 += "{}-{:.04f}| ".format(scene, iou)
            iou = point_iou_all / n_pair
            res_dict[scene][f"point_iou@th{th}"] = iou
            res_str2 += "{}-{:.04f}| ".format(scene, iou)

            res_str1 += "\n"
            res_str2 += "\n"
            res_str += res_str1 + res_str2
        boundary_e_all = 0
        boundary_p_all, boundary_r_all = 0, 0
        res_str1 = ""
        for scene in res_dict:
            if scene == "all":
                continue
            boundary_e_all += res_dict[scene]["boundary_length_error"]
            boundary_p_all += res_dict[scene]["boundary_type_precision"]
            boundary_r_all += res_dict[scene]["boundary_type_recall"]
            e = res_dict[scene]["boundary_length_error"] / res_dict[scene]["n_pair"]
            p = res_dict[scene]["boundary_type_precision"] / res_dict[scene]["n_pair"]
            r = res_dict[scene]["boundary_type_recall"] / res_dict[scene]["n_pair"]
            res_dict[scene]["boundary_length_error"] = e
            res_dict[scene]["boundary_type_precision"] = p
            res_dict[scene]["boundary_type_recall"] = r
            e_str = " | {:.02f}".format(e)
            p_str = " | ".join(["{:.04f}".format(x) for x in p])
            r_str = " | ".join(["{:.04f}".format(x) for x in r])
            res_str1 += f"{scene} Precision " + p_str + ". Recall " + r_str + ". Length error " + e_str + "\n"
        scene = "all"
        e = boundary_e_all / n_pair
        p = boundary_p_all / n_pair
        r = boundary_r_all / n_pair
        res_dict[scene]["boundary_length_error"] = e
        res_dict[scene]["boundary_type_recall"] = r
        res_dict[scene]["boundary_type_precision"] = p
        e_str = "{:.02f}".format(e)
        p_str = " | ".join(["{:.04f}".format(x) for x in p])
        r_str = " | ".join(["{:.04f}".format(x) for x in r])
        res_str1 += f"{scene} Precision " + p_str + ". Recall " + r_str + ". Length error " + e_str + "\n"
        res_str += res_str1

        # bmk构成
        res_str += "BMK scene distribution: \n"
        for scene in res_dict.keys():
            res_str += "{}-{:.04f}; ".format(scene, res_dict[scene]["n_pair"])
        table_str = None
        table_str = self.make_table(res_dict)
        return res_str, table_str, res_dict

    def make_table(self, res_dict):
        # 将结果做成md表格的内容
        bev = []
        point = []

        other_str = ""  # 不能做成表的
        for scene in res_dict:
            for k, v in res_dict[scene].items():
                v = np.round(v, 4)
                if "iou" in k:
                    th = float(k.split("@")[-1][2:])
                    if "bev" in k:
                        bev.append({"scene": scene, "th": th, "iou": v})
                    else:
                        point.append({"scene": scene, "th": th, "iou": v})
                elif isinstance(v, np.ndarray):
                    other_str += f"{scene} {k}: {str(v.tolist())} \n"
                else:
                    other_str += f"{scene} {k}: {v} \n"

        res_str = "BEV:\n"
        df = json_normalize(bev)
        s = pd.Series(list(df["iou"]), index=[df["th"], df["scene"]])  # 利用一维数据表df2构造一个二级索引Series对象
        s = s.unstack()
        res_str += s.to_string()

        res_str += "\n Point: \n"
        df = json_normalize(point)
        s = pd.Series(list(df["iou"]), index=[df["th"], df["scene"]])
        s = s.unstack()
        res_str += s.to_string()

        res_str += "\n" + other_str
        return res_str


class FreespaceMultiClassEvalBatch(FreespaceEvalBatch):
    def IoU_single(self, pred, gt):
        # mutlicalss IoU， 算每个类的IoU按类取平均
        mask = gt != -1
        pred = pred.reshape(gt.shape)
        ious = np.zeros((self.n_boundary_cls))
        for cls in range(self.n_boundary_cls):
            y_true = (gt == cls).astype(np.int)[mask]
            y_pred = (pred == cls).astype(np.int)[mask]
            ious[cls] = M.jaccard_score(y_true.reshape(-1), y_pred.reshape(-1))
        return ious

    def main(self):
        # evaluation
        res_dict = defaultdict(dict)
        for token in self.pred_bev:
            # compute bev iou
            iou = self.IoU_single(self.pred_bev[token], self.gt_bev[token])
            res_dict[token]["bevmap_iou"] = iou
            # compute point iou
            iou = self.IoU_single(self.pred_point[token], self.gt_point[token])
            res_dict[token]["point_iou"] = iou

            # compute boundary classification p/r and distance error
            if len(self.pred_contur):
                res_dict[token]["boundary_length_error"] = self.boundary_length_error_single(
                    self.pred_contur[token], self.gt_contur[token]
                )
            else:
                res_dict[token]["boundary_length_error"] = 0
            if len(self.pred_contur_cls):
                r, p = self.recall_precision_single(
                    self.pred_contur_cls[token], self.gt_contur_cls[token], n_class=self.n_boundary_cls
                )
                res_dict[token]["boundary_type_precision"] = p
                res_dict[token]["boundary_type_recall"] = r
            else:
                res_dict[token]["boundary_type_precision"] = np.array([0])
                res_dict[token]["boundary_type_recall"] = np.array([0])

        return res_dict

    def result_reduce(self, results):
        """Merge results from each step. Merge by scene.
        results: List[dict] evaluation result of each step. Dict key is frame_token
        """
        # gather
        res_dict = {}
        for ress in results:
            for token, res in ress.items():
                scene = token.split("/")[-3]
                res_dict.setdefault(scene, defaultdict(float))
                res_dict[scene]["n_pair"] += 1

                res_dict[scene]["bevmap_iou"] += res["bevmap_iou"]
                res_dict[scene]["point_iou"] += res["point_iou"]
                res_dict[scene]["boundary_length_error"] += res["boundary_length_error"]

                res_dict[scene]["n_pair_p"] += res["boundary_type_precision"] != -1
                res_dict[scene]["n_pair_r"] += res["boundary_type_recall"] != -1
                res_dict[scene]["boundary_type_precision"] += res["boundary_type_precision"] * (
                    res["boundary_type_precision"] != -1
                )
                res_dict[scene]["boundary_type_recall"] += res["boundary_type_recall"] * (
                    res["boundary_type_recall"] != -1
                )

        # average by scene
        res_str = "EvalResults: \n"

        bev_iou_all = 0
        point_iou_all = 0
        n_pair = 0
        n_pair_r, n_pair_p = 0, 0
        boundary_e_all = 0
        boundary_p_all, boundary_r_all = 0, 0
        res_str1 = ""
        for scene in res_dict:
            if scene == "all":
                continue
            n_pair += res_dict[scene]["n_pair"]
            n_pair_p += res_dict[scene]["n_pair_p"]
            n_pair_r += res_dict[scene]["n_pair_r"]

            bev_iou_all += res_dict[scene]["bevmap_iou"]
            point_iou_all += res_dict[scene]["point_iou"]
            boundary_e_all += res_dict[scene]["boundary_length_error"]
            boundary_p_all += res_dict[scene]["boundary_type_precision"]
            boundary_r_all += res_dict[scene]["boundary_type_recall"]
            bev_iou = res_dict[scene]["bevmap_iou"] / res_dict[scene]["n_pair"]
            point_iou = res_dict[scene]["point_iou"] / res_dict[scene]["n_pair"]
            e = res_dict[scene]["boundary_length_error"] / res_dict[scene]["n_pair"]
            p = res_dict[scene]["boundary_type_precision"] / (res_dict[scene]["n_pair_p"] + 1e-4)
            r = res_dict[scene]["boundary_type_recall"] / (res_dict[scene]["n_pair_r"] + 1e-4)
            res_dict[scene]["bevmap_iou"] = bev_iou
            res_dict[scene]["point_iou"] = point_iou
            res_dict[scene]["boundary_length_error"] = e
            res_dict[scene]["boundary_type_precision"] = p
            res_dict[scene]["boundary_type_recall"] = r
            b_str = "| ".join(["{:.04f}".format(x) for x in bev_iou])
            pi_str = "| ".join(["{:.04f}".format(x) for x in point_iou])
            e_str = " | {:.02f}".format(e)
            p_str = " | ".join(["{:.04f}".format(x) for x in p])
            r_str = " | ".join(["{:.04f}".format(x) for x in r])
            res_str1 += f"{scene} BEV IoU " + b_str + ". PointIoU " + pi_str + "\n"
            res_str1 += f"{scene} Precision " + p_str + ". Recall " + r_str + ". Length error " + e_str + "\n"
        scene = "all"
        res_dict.setdefault(scene, defaultdict(float))
        bev_iou = bev_iou_all / n_pair
        point_iou = point_iou_all / n_pair
        e = boundary_e_all / n_pair
        p = boundary_p_all / (n_pair_p + 1e-4)
        r = boundary_r_all / (n_pair_r + 1e-4)
        res_dict[scene]["bevmap_iou"] = bev_iou
        res_dict[scene]["point_iou"] = point_iou
        res_dict[scene]["boundary_length_error"] = e
        res_dict[scene]["boundary_type_recall"] = r
        res_dict[scene]["boundary_type_precision"] = p
        b_str = "| ".join(["{:.04f}".format(x) for x in bev_iou])
        pi_str = "| ".join(["{:.04f}".format(x) for x in point_iou])
        e_str = "{:.02f}".format(e)
        p_str = " | ".join(["{:.04f}".format(x) for x in p])
        r_str = " | ".join(["{:.04f}".format(x) for x in r])
        res_str1 += f"{scene} BEV IoU " + b_str + ". PointIoU " + pi_str + "\n"
        res_str1 += f"{scene} Precision " + p_str + ". Recall " + r_str + ". Length error " + e_str + "\n"
        res_str += res_str1

        # bmk构成
        res_str += "BMK scene distribution: \n"
        for scene in res_dict.keys():
            res_str += "{}-{:.04f}; ".format(scene, res_dict[scene]["n_pair"])
        table_str = None
        table_str = self.make_table(res_dict)
        return res_str, table_str, res_dict

    def make_table(self, res_dict):
        # 将结果做成md表格的内容
        other_str = ""  # 不能做成表的
        for scene in res_dict:
            for k, v in res_dict[scene].items():
                v = np.round(v, 4)
                if isinstance(v, np.ndarray):
                    other_str += f"{scene} {k}: {str(v.tolist())} \n"
                else:
                    other_str += f"{scene} {k}: {v} \n"

        return other_str
