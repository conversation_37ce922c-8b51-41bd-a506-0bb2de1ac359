import copy
import torch
import numpy as np
import mmcv

from pyquaternion import Quaternion
from mmdet3d.core.bbox.structures.lidar_box3d import LiDARInstance3DBoxes

from perceptron.data.detbev.dataset.nuscenes import NuscBevDetData
from perceptron.data.detbev.dataset.nuscenes import img_transform, depth_transform, bev_transform

__all__ = ["NuscVideoBevDetData"]


class NuscVideoBevDetData(NuscBevDetData):
    def get_image(self, sweeps_data, cams):
        """Given data and cam_names, return image data needed.

        Args:
            sweeps_data (list): Raw data used to generate the data we needed.
            cams (list): Camera names.

        Returns:
            Tensor: Image data after processing.
            Tensor: Transformation matrix from camera to ego.
            Tensor: Intrinsic matrix.
            Tensor: Transformation matrix for ida.
            Tensor: Transformation matrix from key frame camera to sweep frame camera.
            Tensor: timestamps.
            dict: meta infos needed for evaluation.
        """
        assert len(sweeps_data) > 0
        sweep_imgs = list()
        sweep_sensor2ego_mats = list()
        sweep_ego2global_mats = list()
        sweep_sensor2sweepego_mats = list()
        sweep_intrin_mats = list()
        sweep_ida_mats = list()
        sweep_sensor2sensor_mats = list()
        sweep_timestamps = list()
        if self.return_depth:
            sweep_gt_depth = list()
        for cam in cams:
            imgs = list()
            sensor2ego_mats = list()
            ego2global_mats = list()
            sensor2sweepego_mats = list()
            intrin_mats = list()
            ida_mats = list()
            sensor2sensor_mats = list()
            timestamps = list()
            if self.return_depth:
                gt_depth = list()
            key_info = sweeps_data[0]["info"]
            resize, resize_dims, crop, flip, rotate_ida = self.sample_ida_augmentation()
            for sweep_idx, sweep_data in enumerate(sweeps_data):
                sweep_info = sweep_data["info"]
                img = sweep_data[cam]
                w, x, y, z = sweep_info["cam_infos"][cam]["calibrated_sensor"]["rotation"]
                # sweep sensor to sweep ego
                sweepsensor2sweepego_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                sweepsensor2sweepego_tran = torch.Tensor(
                    sweep_info["cam_infos"][cam]["calibrated_sensor"]["translation"]
                )
                sweepsensor2sweepego = sweepsensor2sweepego_rot.new_zeros((4, 4))
                sweepsensor2sweepego[3, 3] = 1
                sweepsensor2sweepego[:3, :3] = sweepsensor2sweepego_rot
                sweepsensor2sweepego[:3, -1] = sweepsensor2sweepego_tran
                # sweep ego to global
                w, x, y, z = sweep_info["cam_infos"][cam]["ego_pose"]["rotation"]
                sweepego2global_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                sweepego2global_tran = torch.Tensor(sweep_info["cam_infos"][cam]["ego_pose"]["translation"])
                sweepego2global = sweepego2global_rot.new_zeros((4, 4))
                sweepego2global[3, 3] = 1
                sweepego2global[:3, :3] = sweepego2global_rot
                sweepego2global[:3, -1] = sweepego2global_tran

                # global sensor to cur ego
                w, x, y, z = key_info["cam_infos"][cam]["ego_pose"]["rotation"]
                keyego2global_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                keyego2global_tran = torch.Tensor(key_info["cam_infos"][cam]["ego_pose"]["translation"])
                keyego2global = keyego2global_rot.new_zeros((4, 4))
                keyego2global[3, 3] = 1
                keyego2global[:3, :3] = keyego2global_rot
                keyego2global[:3, -1] = keyego2global_tran
                global2keyego = keyego2global.inverse()

                # cur ego to sensor
                w, x, y, z = key_info["cam_infos"][cam]["calibrated_sensor"]["rotation"]
                keysensor2keyego_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                keysensor2keyego_tran = torch.Tensor(key_info["cam_infos"][cam]["calibrated_sensor"]["translation"])
                keysensor2keyego = keysensor2keyego_rot.new_zeros((4, 4))
                keysensor2keyego[3, 3] = 1
                keysensor2keyego[:3, :3] = keysensor2keyego_rot
                keysensor2keyego[:3, -1] = keysensor2keyego_tran
                keyego2keysensor = keysensor2keyego.inverse()
                keysensor2sweepsensor = (
                    keyego2keysensor @ global2keyego @ sweepego2global @ sweepsensor2sweepego
                ).inverse()
                sweepsensor2keyego = global2keyego @ sweepego2global @ sweepsensor2sweepego
                sensor2ego_mats.append(sweepsensor2keyego)
                ego2global_mats.append(sweepego2global)
                sensor2sweepego_mats.append(sweepsensor2sweepego)
                sensor2sensor_mats.append(keysensor2sweepsensor)
                intrin_mat = torch.zeros((4, 4))
                intrin_mat[3, 3] = 1
                intrin_mat[:3, :3] = torch.Tensor(sweep_info["cam_infos"][cam]["calibrated_sensor"]["camera_intrinsic"])
                if self.return_depth:
                    depth_key = "DEPTH_" + cam
                    point_depth = sweep_data[depth_key]
                    point_depth_augmented = depth_transform(
                        point_depth, resize, self.ida_aug_conf["final_dim"], crop, flip, rotate_ida
                    )
                    gt_depth.append(point_depth_augmented)
                img, ida_mat = img_transform(
                    img,
                    resize=resize,
                    resize_dims=resize_dims,
                    crop=crop,
                    flip=flip,
                    rotate=rotate_ida,
                )
                ida_mats.append(ida_mat)
                img = mmcv.imnormalize(np.array(img), self.img_mean, self.img_std, self.to_rgb)
                img = torch.from_numpy(img).permute(2, 0, 1)
                imgs.append(img)
                intrin_mats.append(intrin_mat)
                timestamps.append(sweep_info["cam_infos"][cam]["timestamp"])
            sweep_imgs.append(torch.stack(imgs))
            sweep_sensor2ego_mats.append(torch.stack(sensor2ego_mats))
            sweep_ego2global_mats.append(torch.stack(ego2global_mats))
            sweep_sensor2sweepego_mats.append(torch.stack(sensor2sweepego_mats))
            sweep_intrin_mats.append(torch.stack(intrin_mats))
            sweep_ida_mats.append(torch.stack(ida_mats))
            sweep_sensor2sensor_mats.append(torch.stack(sensor2sensor_mats))
            sweep_timestamps.append(torch.tensor(timestamps))
            if self.return_depth:
                sweep_gt_depth.append(torch.stack(gt_depth))
        # Get mean pose of all cams.
        ego2global_rotation = np.mean([key_info["cam_infos"][cam]["ego_pose"]["rotation"] for cam in cams], 0)
        ego2global_translation = np.mean([key_info["cam_infos"][cam]["ego_pose"]["translation"] for cam in cams], 0)
        img_metas = dict(
            box_type_3d=LiDARInstance3DBoxes,
            token=key_info["sample_token"],
            ego2global_translation=ego2global_translation,
            ego2global_rotation=ego2global_rotation,
        )

        ret_list = (
            torch.stack(sweep_imgs).permute(1, 0, 2, 3, 4),
            torch.stack(sweep_sensor2ego_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_ego2global_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_sensor2sweepego_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_intrin_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_ida_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_sensor2sensor_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_timestamps).permute(1, 0),
            img_metas,
        )
        if self.return_depth:
            ret_list += (torch.stack(sweep_gt_depth).permute(1, 0, 2, 3),)
        return ret_list

    def __getitem__(self, idx):
        cams = self.choose_cams()
        if self.use_cbgs:
            idx = self.sample_indices[idx]
        key_data = self._get_single_item(idx, cams)
        scene_token = key_data[0]["info"]["scene_token"]
        sweep_datas = copy.deepcopy(key_data)
        last_sweep_data = copy.deepcopy(key_data)
        for key_idx in self.key_idxes:
            if idx + key_idx < 0:
                sweep_data = copy.deepcopy(last_sweep_data)
            else:
                sweep_data = self._get_single_item(idx + key_idx, cams)
                # If `sweep_data` and key_data have different `scene_token`, it will be abandoned.
                if scene_token != sweep_data[0]["info"]["scene_token"]:
                    sweep_data = copy.deepcopy(last_sweep_data)
                else:
                    last_sweep_data = sweep_data
            sweep_datas.extend(sweep_data)
        # `sweep_datas` contains the following data: [current_key_data, sweeps of current_data, (other_keydata, sweeps of other_keydata) * n]
        image_data_list = self.get_image(sweep_datas, cams)
        (
            sweep_imgs,
            sweep_sensor2ego_mats,
            sweep_ego2global_mats,
            sweep_sensor2sweepego_mats,
            sweep_intrins,
            sweep_ida_mats,
            sweep_sensor2sensor_mats,
            sweep_timestamps,
            img_metas,
        ) = image_data_list[:9]
        if self.is_train:
            gt_boxes, gt_labels = self.get_gt(sweep_datas[0]["info"], cams)
        # Temporary solution for test.
        else:
            gt_boxes = sweep_imgs.new_zeros(0, 7)
            gt_labels = sweep_imgs.new_zeros(
                0,
            )

        rotate_bda, scale_bda, flip_dx, flip_dy = self.sample_bda_augmentation()
        bda_mat = sweep_imgs.new_zeros(4, 4)
        bda_mat[3, 3] = 1
        gt_boxes, bda_rot = bev_transform(gt_boxes, rotate_bda, scale_bda, flip_dx, flip_dy)
        bda_mat[:3, :3] = bda_rot
        ret_list = (
            sweep_imgs,
            sweep_sensor2ego_mats,
            sweep_ego2global_mats,
            sweep_sensor2sweepego_mats,
            sweep_intrins,
            sweep_ida_mats,
            sweep_sensor2sensor_mats,
            bda_mat,
            sweep_timestamps,
            img_metas,
            gt_boxes,
            gt_labels,
        )
        if self.return_depth:
            ret_list += (image_data_list[9],)
        return ret_list


def collate_fn(data, is_return_depth=False):
    imgs_batch = list()
    sensor2ego_mats_batch = list()
    ego2global_mats_batch = list()
    sensor2sweepego_mats_batch = list()
    intrin_mats_batch = list()
    ida_mats_batch = list()
    sensor2sensor_mats_batch = list()
    bda_mat_batch = list()
    timestamps_batch = list()
    gt_boxes_batch = list()
    gt_labels_batch = list()
    img_metas_batch = list()
    depth_labels_batch = list()
    for iter_data in data:
        (
            sweep_imgs,
            sweep_sensor2ego_mats,
            sweep_ego2global_mats,
            sweep_sensor2sweepego_mats,
            sweep_intrins,
            sweep_ida_mats,
            sweep_sensor2sensor_mats,
            bda_mat,
            sweep_timestamps,
            img_metas,
            gt_boxes,
            gt_labels,
        ) = iter_data[:12]
        if is_return_depth:
            gt_depth = iter_data[12]
            depth_labels_batch.append(gt_depth)
        imgs_batch.append(sweep_imgs)
        sensor2ego_mats_batch.append(sweep_sensor2ego_mats)
        ego2global_mats_batch.append(sweep_ego2global_mats)
        sensor2sweepego_mats_batch.append(sweep_sensor2sweepego_mats)
        intrin_mats_batch.append(sweep_intrins)
        ida_mats_batch.append(sweep_ida_mats)
        sensor2sensor_mats_batch.append(sweep_sensor2sensor_mats)
        bda_mat_batch.append(bda_mat)
        timestamps_batch.append(sweep_timestamps)
        img_metas_batch.append(img_metas)
        gt_boxes_batch.append(gt_boxes)
        gt_labels_batch.append(gt_labels)
    mats_dict = dict()
    mats_dict["sensor2ego_mats"] = torch.stack(sensor2ego_mats_batch)
    mats_dict["ego2global_mats"] = torch.stack(ego2global_mats_batch)
    mats_dict["sensor2sweepego_mats"] = torch.stack(sensor2sweepego_mats_batch)
    mats_dict["intrin_mats"] = torch.stack(intrin_mats_batch)
    mats_dict["ida_mats"] = torch.stack(ida_mats_batch)
    mats_dict["sensor2sensor_mats"] = torch.stack(sensor2sensor_mats_batch)
    mats_dict["bda_mat"] = torch.stack(bda_mat_batch)
    ret_list = (
        torch.stack(imgs_batch),
        mats_dict,
        torch.stack(timestamps_batch),
        img_metas_batch,
        gt_boxes_batch,
        gt_labels_batch,
    )
    if is_return_depth:
        ret_list += (torch.stack(depth_labels_batch),)
    return ret_list
