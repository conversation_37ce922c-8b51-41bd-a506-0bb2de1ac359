import copy
import torch
import numpy as np
import mmcv

from PIL import Image
from pyquaternion import Quaternion
from nuscenes.utils.data_classes import Box
from mmdet3d.core.bbox.structures.lidar_box3d import LiDARInstance3DBoxes


from data3d.datasets.nuScenes import NuscenesDataset, map_name_from_general_to_detection

__all__ = ["NuscBevDetData"]


def get_rot(h):
    return torch.Tensor(
        [
            [np.cos(h), np.sin(h)],
            [-np.sin(h), np.cos(h)],
        ]
    )


def img_transform(img, resize, resize_dims, crop, flip, rotate):
    ida_rot = torch.eye(2)
    ida_tran = torch.zeros(2)
    # adjust image
    img = img.resize(resize_dims)
    img = img.crop(crop)
    if flip:
        img = img.transpose(method=Image.FLIP_LEFT_RIGHT)
    img = img.rotate(rotate)

    # post-homography transformation
    ida_rot *= resize
    ida_tran -= torch.Tensor(crop[:2])
    if flip:
        A = torch.Tensor([[-1, 0], [0, 1]])
        b = torch.Tensor([crop[2] - crop[0], 0])
        ida_rot = A.matmul(ida_rot)
        ida_tran = A.matmul(ida_tran) + b
    A = get_rot(rotate / 180 * np.pi)
    b = torch.Tensor([crop[2] - crop[0], crop[3] - crop[1]]) / 2
    b = A.matmul(-b) + b
    ida_rot = A.matmul(ida_rot)
    ida_tran = A.matmul(ida_tran) + b
    ida_mat = ida_rot.new_zeros(4, 4)
    ida_mat[3, 3] = 1
    ida_mat[2, 2] = 1
    ida_mat[:2, :2] = ida_rot
    ida_mat[:2, 3] = ida_tran
    return img, ida_mat


def bev_transform(gt_boxes, rotate_angle, scale_ratio, flip_dx, flip_dy):
    rotate_angle = torch.tensor(rotate_angle / 180 * np.pi)
    rot_sin = torch.sin(rotate_angle)
    rot_cos = torch.cos(rotate_angle)
    rot_mat = torch.Tensor([[rot_cos, -rot_sin, 0], [rot_sin, rot_cos, 0], [0, 0, 1]])
    scale_mat = torch.Tensor([[scale_ratio, 0, 0], [0, scale_ratio, 0], [0, 0, scale_ratio]])
    flip_mat = torch.Tensor([[1, 0, 0], [0, 1, 0], [0, 0, 1]])
    if flip_dx:
        flip_mat = flip_mat @ torch.Tensor([[-1, 0, 0], [0, 1, 0], [0, 0, 1]])
    if flip_dy:
        flip_mat = flip_mat @ torch.Tensor([[1, 0, 0], [0, -1, 0], [0, 0, 1]])
    rot_mat = flip_mat @ (scale_mat @ rot_mat)
    if gt_boxes.shape[0] > 0:
        gt_boxes[:, :3] = (rot_mat @ gt_boxes[:, :3].unsqueeze(-1)).squeeze(-1)
        gt_boxes[:, 3:6] *= scale_ratio
        gt_boxes[:, 6] += rotate_angle
        if flip_dx:
            gt_boxes[:, 6] = 2 * torch.asin(torch.tensor(1.0)) - gt_boxes[:, 6]
        if flip_dy:
            gt_boxes[:, 6] = -gt_boxes[:, 6]
        if gt_boxes.shape[1] > 7:
            gt_boxes[:, 7:] = (rot_mat[:2, :2] @ gt_boxes[:, 7:].unsqueeze(-1)).squeeze(-1)
    return gt_boxes, rot_mat


def depth_transform(cam_depth, resize, resize_dims, crop, flip, rotate):
    """
    Input:
        cam_depth: Nx3, 3: x,y,d
        resize: a float value
        resize_dims: self.ida_aug_conf["final_dim"] -> [H, W]
        crop: x1, y1, x2, y2
        flip: bool value
        rotate: an angle
    Output:
        cam_depth: [h/down_ratio, w/down_ratio, d]
    """

    H, W = resize_dims
    cam_depth[:, :2] = cam_depth[:, :2] * resize
    cam_depth[:, 0] -= crop[0]
    cam_depth[:, 1] -= crop[1]
    if flip:
        cam_depth[:, 0] = resize_dims[1] - cam_depth[:, 0]

    cam_depth[:, 0] -= W / 2.0
    cam_depth[:, 1] -= H / 2.0

    h = rotate / 180 * np.pi
    rot_matrix = [
        [np.cos(h), np.sin(h)],
        [-np.sin(h), np.cos(h)],
    ]
    cam_depth[:, :2] = np.matmul(rot_matrix, cam_depth[:, :2].T).T

    cam_depth[:, 0] += W / 2.0
    cam_depth[:, 1] += H / 2.0

    depth_coords = cam_depth[:, :2].astype(np.int16)

    depth_map = np.zeros(resize_dims)
    valid_mask = (
        (depth_coords[:, 1] < resize_dims[0])
        & (depth_coords[:, 0] < resize_dims[1])
        & (depth_coords[:, 1] >= 0)
        & (depth_coords[:, 0] >= 0)
    )
    depth_map[depth_coords[valid_mask, 1], depth_coords[valid_mask, 0]] = cam_depth[valid_mask, 2]

    return torch.Tensor(depth_map)


# TODO: Change data3d to support lss.
class NuscBevDetData(NuscenesDataset):
    def __init__(
        self,
        ida_aug_conf,
        bda_aug_conf,
        classes,
        use_cbgs=False,
        num_sweeps=1,
        img_conf=dict(img_mean=[123.675, 116.28, 103.53], img_std=[58.395, 57.12, 57.375], to_rgb=True),
        data_split="training_12hz",
        return_depth=False,
        sweeps_idx=None,
        key_idxes=[],
    ):
        """Dataset used for bevdetection task. It is worth noting that sweeps of current key frame
            , other key frames and sweeps of other key frames can be used as sweeps.

        Args:
            ida_aug_conf (dict): Config for ida augmentation.
            bda_aug_conf (dict): Config for bda augmentation.
            classes(list): Class names.
            use_cbgs(bool): Whether to use cbgs stragety, Defaults to be False.
            num_sweeps(int): Number of sweeps to be used for each sample, defaults to be 1.
            img_conf(dict): Config for image.
            data_split(str): type of data.
            key_idxes(list): Indexes of key frames used as sweep.
        """
        super().__init__(data_split=data_split, num_sweeps=num_sweeps, sweeps_idx=sweeps_idx)
        self.is_train = data_split in ("training_12hz", "trainval_12hz")
        self.ida_aug_conf = ida_aug_conf
        self.bda_aug_conf = bda_aug_conf
        self.classes = classes
        self.use_cbgs = use_cbgs
        if self.use_cbgs:
            self.cat2id = {name: i for i, name in enumerate(self.classes)}
            self.sample_indices = self._get_sample_indices()
        self.num_sweeps = num_sweeps
        self.img_mean = np.array(img_conf["img_mean"], np.float32)
        self.img_std = np.array(img_conf["img_std"], np.float32)
        self.to_rgb = img_conf["to_rgb"]
        self.return_depth = return_depth
        if self.return_depth:
            assert self.num_sweeps == 1, "Only key frame depth is supported"
        for key_idx in key_idxes:
            assert key_idx < 0, "key_idx must less than 0"
        self.key_idxes = key_idxes

    def _get_sample_indices(self):
        """Load annotations from ann_file.

        Args:
            ann_file (str): Path of the annotation file.

        Returns:
            list[dict]: List of annotations after class sampling.
        """
        class_sample_idxs = {cat_id: [] for cat_id in self.cat2id.values()}
        for idx, info in enumerate(self.infos):
            gt_names = set([ann_info["category_name"] for ann_info in info["ann_infos"]])
            for gt_name in gt_names:
                gt_name = map_name_from_general_to_detection[gt_name]
                if gt_name not in self.classes:
                    continue
                class_sample_idxs[self.cat2id[gt_name]].append(idx)
        duplicated_samples = sum([len(v) for _, v in class_sample_idxs.items()])
        class_distribution = {k: len(v) / duplicated_samples for k, v in class_sample_idxs.items()}

        sample_indices = []

        frac = 1.0 / len(self.classes)
        ratios = [frac / v for v in class_distribution.values()]
        for cls_inds, ratio in zip(list(class_sample_idxs.values()), ratios):
            sample_indices += np.random.choice(cls_inds, int(len(cls_inds) * ratio)).tolist()
        return sample_indices

    def sample_ida_augmentation(self):
        """Generate ida augmentation values based on ida_config."""
        H, W = self.ida_aug_conf["H"], self.ida_aug_conf["W"]
        fH, fW = self.ida_aug_conf["final_dim"]
        if self.is_train:
            resize = np.random.uniform(*self.ida_aug_conf["resize_lim"])
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.random.uniform(*self.ida_aug_conf["bot_pct_lim"])) * newH) - fH
            crop_w = int(np.random.uniform(0, max(0, newW - fW)))
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            if self.ida_aug_conf["rand_flip"] and np.random.choice([0, 1]):
                flip = True
            rotate_ida = np.random.uniform(*self.ida_aug_conf["rot_lim"])
        else:
            resize = max(fH / H, fW / W)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.mean(self.ida_aug_conf["bot_pct_lim"])) * newH) - fH
            crop_w = int(max(0, newW - fW) / 2)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate_ida = 0
        return resize, resize_dims, crop, flip, rotate_ida

    def sample_bda_augmentation(self):
        """Generate bda augmentation values based on bda_config."""
        if self.is_train:
            rotate_bda = np.random.uniform(*self.bda_aug_conf["rot_lim"])
            scale_bda = np.random.uniform(*self.bda_aug_conf["scale_lim"])
            flip_dx = np.random.uniform() < self.bda_aug_conf["flip_dx_ratio"]
            flip_dy = np.random.uniform() < self.bda_aug_conf["flip_dy_ratio"]
        else:
            rotate_bda = 0
            scale_bda = 1.0
            flip_dx = False
            flip_dy = False
        return rotate_bda, scale_bda, flip_dx, flip_dy

    def get_image(self, sweeps_data, cams):
        """Given data and cam_names, return image data needed.

        Args:
            sweeps_data (list): Raw data used to generate the data we needed.
            cams (list): Camera names.

        Returns:
            Tensor: Image data after processing.
            Tensor: Transformation matrix from camera to ego.
            Tensor: Intrinsic matrix.
            Tensor: Transformation matrix for ida.
            Tensor: Transformation matrix from key frame camera to sweep frame camera.
            Tensor: timestamps.
            dict: meta infos needed for evaluation.
        """
        assert len(sweeps_data) > 0
        sweep_imgs = list()
        sweep_sensor2ego_mats = list()
        sweep_intrin_mats = list()
        sweep_ida_mats = list()
        sweep_sensor2sensor_mats = list()
        sweep_timestamps = list()
        if self.return_depth:
            sweep_gt_depth = list()
        for cam in cams:
            imgs = list()
            sensor2ego_mats = list()
            intrin_mats = list()
            ida_mats = list()
            sensor2sensor_mats = list()
            timestamps = list()
            if self.return_depth:
                gt_depth = list()
            key_info = sweeps_data[0]["info"]
            resize, resize_dims, crop, flip, rotate_ida = self.sample_ida_augmentation()
            for sweep_idx, sweep_data in enumerate(sweeps_data):
                sweep_info = sweep_data["info"]
                img = sweep_data[cam]
                w, x, y, z = sweep_info["cam_infos"][cam]["calibrated_sensor"]["rotation"]
                # sweep sensor to sweep ego
                sweepsensor2sweepego_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                sweepsensor2sweepego_tran = torch.Tensor(
                    sweep_info["cam_infos"][cam]["calibrated_sensor"]["translation"]
                )
                sweepsensor2sweepego = sweepsensor2sweepego_rot.new_zeros((4, 4))
                sweepsensor2sweepego[3, 3] = 1
                sweepsensor2sweepego[:3, :3] = sweepsensor2sweepego_rot
                sweepsensor2sweepego[:3, -1] = sweepsensor2sweepego_tran
                # sweep ego to global
                w, x, y, z = sweep_info["cam_infos"][cam]["ego_pose"]["rotation"]
                sweepego2global_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                sweepego2global_tran = torch.Tensor(sweep_info["cam_infos"][cam]["ego_pose"]["translation"])
                sweepego2global = sweepego2global_rot.new_zeros((4, 4))
                sweepego2global[3, 3] = 1
                sweepego2global[:3, :3] = sweepego2global_rot
                sweepego2global[:3, -1] = sweepego2global_tran

                # global sensor to cur ego
                w, x, y, z = key_info["cam_infos"][cam]["ego_pose"]["rotation"]
                keyego2global_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                keyego2global_tran = torch.Tensor(key_info["cam_infos"][cam]["ego_pose"]["translation"])
                keyego2global = keyego2global_rot.new_zeros((4, 4))
                keyego2global[3, 3] = 1
                keyego2global[:3, :3] = keyego2global_rot
                keyego2global[:3, -1] = keyego2global_tran
                global2keyego = keyego2global.inverse()

                # cur ego to sensor
                w, x, y, z = key_info["cam_infos"][cam]["calibrated_sensor"]["rotation"]
                keysensor2keyego_rot = torch.Tensor(Quaternion(w, x, y, z).rotation_matrix)
                keysensor2keyego_tran = torch.Tensor(key_info["cam_infos"][cam]["calibrated_sensor"]["translation"])
                keysensor2keyego = keysensor2keyego_rot.new_zeros((4, 4))
                keysensor2keyego[3, 3] = 1
                keysensor2keyego[:3, :3] = keysensor2keyego_rot
                keysensor2keyego[:3, -1] = keysensor2keyego_tran
                keyego2keysensor = keysensor2keyego.inverse()
                keysensor2sweepsensor = (
                    keyego2keysensor @ global2keyego @ sweepego2global @ sweepsensor2sweepego
                ).inverse()
                sweepsensor2keyego = global2keyego @ sweepego2global @ sweepsensor2sweepego
                sensor2ego_mats.append(sweepsensor2keyego)
                sensor2sensor_mats.append(keysensor2sweepsensor)
                intrin_mat = torch.zeros((4, 4))
                intrin_mat[3, 3] = 1
                intrin_mat[:3, :3] = torch.Tensor(sweep_info["cam_infos"][cam]["calibrated_sensor"]["camera_intrinsic"])
                if self.return_depth:
                    depth_key = "DEPTH_" + cam
                    point_depth = sweep_data[depth_key]
                    point_depth_augmented = depth_transform(
                        point_depth, resize, self.ida_aug_conf["final_dim"], crop, flip, rotate_ida
                    )
                    gt_depth.append(point_depth_augmented)
                img, ida_mat = img_transform(
                    img,
                    resize=resize,
                    resize_dims=resize_dims,
                    crop=crop,
                    flip=flip,
                    rotate=rotate_ida,
                )
                ida_mats.append(ida_mat)
                img = mmcv.imnormalize(np.array(img), self.img_mean, self.img_std, self.to_rgb)
                img = torch.from_numpy(img).permute(2, 0, 1)
                imgs.append(img)
                intrin_mats.append(intrin_mat)
                timestamps.append(sweep_info["cam_infos"][cam]["timestamp"])
            sweep_imgs.append(torch.stack(imgs))
            sweep_sensor2ego_mats.append(torch.stack(sensor2ego_mats))
            sweep_intrin_mats.append(torch.stack(intrin_mats))
            sweep_ida_mats.append(torch.stack(ida_mats))
            sweep_sensor2sensor_mats.append(torch.stack(sensor2sensor_mats))
            sweep_timestamps.append(torch.tensor(timestamps))
            if self.return_depth:
                sweep_gt_depth.append(torch.stack(gt_depth))
        # Get mean pose of all cams.
        ego2global_rotation = np.mean([key_info["cam_infos"][cam]["ego_pose"]["rotation"] for cam in cams], 0)
        ego2global_translation = np.mean([key_info["cam_infos"][cam]["ego_pose"]["translation"] for cam in cams], 0)
        img_metas = dict(
            box_type_3d=LiDARInstance3DBoxes,
            token=key_info["sample_token"],
            ego2global_translation=ego2global_translation,
            ego2global_rotation=ego2global_rotation,
        )

        ret_list = (
            torch.stack(sweep_imgs).permute(1, 0, 2, 3, 4),
            torch.stack(sweep_sensor2ego_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_intrin_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_ida_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_sensor2sensor_mats).permute(1, 0, 2, 3),
            torch.stack(sweep_timestamps).permute(1, 0),
            img_metas,
        )
        if self.return_depth:
            ret_list += (torch.stack(sweep_gt_depth).permute(1, 0, 2, 3),)
        return ret_list

    def get_gt(self, info, cams):
        """Generate gt labels from info.

        Args:
            info(dict): Infos needed to generate gt labels.
            cams(list): Camera names.

        Returns:
            Tensor: GT bboxes.
            Tensor: GT labels.
        """
        ego2global_rotation = np.mean([info["cam_infos"][cam]["ego_pose"]["rotation"] for cam in cams], 0)
        ego2global_translation = np.mean([info["cam_infos"][cam]["ego_pose"]["translation"] for cam in cams], 0)
        trans = -np.array(ego2global_translation)
        rot = Quaternion(ego2global_rotation).inverse
        gt_boxes = list()
        gt_labels = list()
        for ann_info in info["ann_infos"]:
            # Use ego coordinate.
            if (
                map_name_from_general_to_detection[ann_info["category_name"]] not in self.classes
                or ann_info["num_lidar_pts"] + ann_info["num_radar_pts"] <= 0
            ):
                continue
            box = Box(
                ann_info["translation"],
                ann_info["size"],
                Quaternion(ann_info["rotation"]),
                velocity=ann_info["velocity"],
            )
            box.translate(trans)
            box.rotate(rot)
            box_xyz = np.array(box.center)
            box_dxdydz = np.array(box.wlh)[[1, 0, 2]]
            box_yaw = np.array([box.orientation.yaw_pitch_roll[0]])
            box_velo = np.array(box.velocity[:2])
            gt_box = np.concatenate([box_xyz, box_dxdydz, box_yaw, box_velo])
            gt_boxes.append(gt_box)
            gt_labels.append(self.classes.index(map_name_from_general_to_detection[ann_info["category_name"]]))
        return torch.Tensor(gt_boxes), torch.tensor(gt_labels)

    def choose_cams(self):
        if self.is_train and self.ida_aug_conf["Ncams"] < len(self.ida_aug_conf["cams"]):
            cams = np.random.choice(self.ida_aug_conf["cams"], self.ida_aug_conf["Ncams"], replace=False)
        else:
            cams = self.ida_aug_conf["cams"]
        return cams

    def _get_single_item(self, idx, cams):
        data = super(NuscBevDetData, self).__getitem__(idx)
        sweep_datas = [data]
        sweeps_imgs = data["sweeps_imgs"]
        # Some frames may don't have all 6 sweeps.
        for i, sweep in enumerate(data["info"]["sweeps"]):
            if len(sweep_datas) == self.num_sweeps:
                break
            sweep_data = dict()
            for cam in cams:
                sweep_data[cam] = sweeps_imgs[i][cam]
            sweep_data["info"] = dict()
            sweep_data["info"]["cam_infos"] = sweep
            sweep_datas.append(sweep_data)
        for i in range(self.num_sweeps - len(sweep_datas)):
            sweep_datas.append(data)
        return sweep_datas

    def __getitem__(self, idx):
        cams = self.choose_cams()
        if self.use_cbgs:
            idx = self.sample_indices[idx]
        key_data = self._get_single_item(idx, cams)
        scene_token = key_data[0]["info"]["scene_token"]
        sweep_datas = copy.deepcopy(key_data)
        last_sweep_data = copy.deepcopy(key_data)
        for key_idx in self.key_idxes:
            if idx + key_idx < 0:
                sweep_data = copy.deepcopy(last_sweep_data)
            else:
                sweep_data = self._get_single_item(idx + key_idx, cams)
                # If `sweep_data` and key_data have different `scene_token`, it will be abandoned.
                if scene_token != sweep_data[0]["info"]["scene_token"]:
                    sweep_data = copy.deepcopy(last_sweep_data)
                else:
                    last_sweep_data = sweep_data
            sweep_datas.extend(sweep_data)
        # `sweep_datas` contains the following data: [current_key_data, sweeps of current_data, (other_keydata, sweeps of other_keydata) * n]
        image_data_list = self.get_image(sweep_datas, cams)
        (
            sweep_imgs,
            sweep_sensor2ego_mats,
            sweep_intrins,
            sweep_ida_mats,
            sweep_sensor2sensor_mats,
            sweep_timestamps,
            img_metas,
        ) = image_data_list[:7]
        if self.is_train:
            gt_boxes, gt_labels = self.get_gt(sweep_datas[0]["info"], cams)
        # Temporary solution for test.
        else:
            gt_boxes = sweep_imgs.new_zeros(0, 7)
            gt_labels = sweep_imgs.new_zeros(
                0,
            )

        rotate_bda, scale_bda, flip_dx, flip_dy = self.sample_bda_augmentation()
        bda_mat = sweep_imgs.new_zeros(4, 4)
        bda_mat[3, 3] = 1
        gt_boxes, bda_rot = bev_transform(gt_boxes, rotate_bda, scale_bda, flip_dx, flip_dy)
        bda_mat[:3, :3] = bda_rot
        ret_list = (
            sweep_imgs,
            sweep_sensor2ego_mats,
            sweep_intrins,
            sweep_ida_mats,
            sweep_sensor2sensor_mats,
            bda_mat,
            sweep_timestamps,
            img_metas,
            gt_boxes,
            gt_labels,
        )
        if self.return_depth:
            ret_list += (image_data_list[7],)
        return ret_list

    def __str__(self):
        return f"""NuscData: {len(self)} samples. Split: {"train" if self.is_train else "val"}.
                   Augmentation Conf: {self.ida_aug_conf}"""

    def __len__(self):
        if self.use_cbgs:
            return len(self.sample_indices)
        else:
            return super(NuscBevDetData, self).__len__()


def collate_fn(data, is_return_depth=False):
    imgs_batch = list()
    sensor2ego_mats_batch = list()
    intrin_mats_batch = list()
    ida_mats_batch = list()
    sensor2sensor_mats_batch = list()
    bda_mat_batch = list()
    timestamps_batch = list()
    gt_boxes_batch = list()
    gt_labels_batch = list()
    img_metas_batch = list()
    depth_labels_batch = list()
    for iter_data in data:
        (
            sweep_imgs,
            sweep_sensor2ego_mats,
            sweep_intrins,
            sweep_ida_mats,
            sweep_sensor2sensor_mats,
            bda_mat,
            sweep_timestamps,
            img_metas,
            gt_boxes,
            gt_labels,
        ) = iter_data[:10]
        if is_return_depth:
            gt_depth = iter_data[10]
            depth_labels_batch.append(gt_depth)
        imgs_batch.append(sweep_imgs)
        sensor2ego_mats_batch.append(sweep_sensor2ego_mats)
        intrin_mats_batch.append(sweep_intrins)
        ida_mats_batch.append(sweep_ida_mats)
        sensor2sensor_mats_batch.append(sweep_sensor2sensor_mats)
        bda_mat_batch.append(bda_mat)
        timestamps_batch.append(sweep_timestamps)
        img_metas_batch.append(img_metas)
        gt_boxes_batch.append(gt_boxes)
        gt_labels_batch.append(gt_labels)
    mats_dict = dict()
    mats_dict["sensor2ego_mats"] = torch.stack(sensor2ego_mats_batch)
    mats_dict["intrin_mats"] = torch.stack(intrin_mats_batch)
    mats_dict["ida_mats"] = torch.stack(ida_mats_batch)
    mats_dict["sensor2sensor_mats"] = torch.stack(sensor2sensor_mats_batch)
    mats_dict["bda_mat"] = torch.stack(bda_mat_batch)
    ret_list = (
        torch.stack(imgs_batch),
        mats_dict,
        torch.stack(timestamps_batch),
        img_metas_batch,
        gt_boxes_batch,
        gt_labels_batch,
    )
    if is_return_depth:
        ret_list += (torch.stack(depth_labels_batch),)
    return ret_list
