import numpy as np
from tqdm import tqdm
from perceptron.utils.traffic_det2d_utils.tracker.tracker.linear_assignment_tracker import LinearAssignmentTracker_Dist


def tracker_label_update_assign(timeline, all_dt, track_gt_boxes, nori_id_track):
    iou_thr = 0.05
    tl_tracker = LinearAssignmentTracker_Dist(max_dist=6)
    pre_time = None
    frame_id = 0
    track_result = {}
    track_filter_dt = {}
    for transfered_time, nori_id in tqdm(enumerate(timeline)):
        # 如果绝对时间超过了2秒(切换了数据集,同一天内数据集时间戳越来越大)，则手动断track
        if pre_time is None or transfered_time - pre_time >= 2:
            return_track = tl_tracker.clear_all_tracks()
            track_result.update(return_track)
        # 根据当前的bbox,生成一批track
        cur_bboxes = np.array(all_dt[nori_id][1])
        if len(cur_bboxes) > 0:
            (valid,) = np.where(cur_bboxes[:, 4] >= iou_thr)
            cur_bboxes = cur_bboxes[valid]
        return_track = tl_tracker.update_frame(cur_bboxes, frame_id, nori_id)
        track_result.update(return_track)
        frame_id += 1
        pre_time = transfered_time
    return_track = tl_tracker.clear_all_tracks()
    track_result.update(return_track)

    # 遍历所有的track结果,得到track_gt_boxes为track label assign的结果
    for predict_track_id, tracking in track_result.items():
        # 当前track的所有box和对应所在的nori_id
        bboxes = tracking["bboxes"]
        nori_ids = tracking["nori_ids"]
        for box, nori_id in zip(bboxes, nori_ids):
            # 如果track中的nori_id未能在dt中找到,即track全都是fp,gt miss了
            if nori_id not in nori_id_track:
                continue
            # valid_tracks:通过nori_id查找到track的gt
            valid_tracks = nori_id_track[nori_id]
            # 遍历所有的gt_track中对应的box
            for vt in valid_tracks:
                for i in range(len(track_gt_boxes[vt])):
                    # 如果track_id,nori_id,box三者一致,则该predict_track_id为该box的track预测结果
                    if (
                        track_gt_boxes[vt][i]["nori_id"] == nori_id
                        and (np.array(track_gt_boxes[vt][i]["dt"]) == box[:4]).all()
                    ):
                        track_gt_boxes[vt][i]["track_predict"] = predict_track_id
    # track_filter_dt
    for nori_id in all_dt.keys():
        if nori_id not in track_filter_dt:
            track_filter_dt[nori_id] = {}
            track_filter_dt[nori_id][1] = []
        if nori_id in tl_tracker.filter_dt:
            track_filter_dt[nori_id][1] += tl_tracker.filter_dt[nori_id]

    for nori_id in track_filter_dt.keys():
        for cls_name in track_filter_dt[nori_id].keys():
            track_filter_dt[nori_id][cls_name] = sorted(
                track_filter_dt[nori_id][cls_name], key=lambda item: item[-1], reverse=True
            )
    return track_result, track_gt_boxes, track_filter_dt
