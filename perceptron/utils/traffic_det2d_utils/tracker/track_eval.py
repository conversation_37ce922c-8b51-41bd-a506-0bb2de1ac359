import numpy as np


def track_eval(track_result, track_gt_boxes):
    track_pre_calculate = {}
    for track_id, result in track_gt_boxes.items():
        if track_id not in track_pre_calculate:
            track_pre_calculate[track_id] = {}
            track_pre_calculate[track_id]["pre"] = []
            track_pre_calculate[track_id]["gt"] = []
        for item in result:
            track_pre_calculate[track_id]["pre"].append(item["track_predict"])
            track_pre_calculate[track_id]["gt"].append(item["track_id"])

    frequency = {}
    for track_id, assign_result in track_pre_calculate.items():
        vals, counts = np.unique(np.array(assign_result["pre"]), return_counts=True)
        frequency[track_id] = {}
        for val, count in zip(vals, counts):
            frequency[track_id][val] = count
        frequency[track_id] = sorted(frequency[track_id].items(), key=lambda x: x[1], reverse=True)

    # idf1=2*precision*recall/(precision+recall)
    idf1 = {}
    for track_id in frequency.keys():
        top = frequency[track_id][:2]
        top1 = top[0]
        if top1[0] == -1:
            if len(top) == 1:
                idf1[track_id] = 0
                continue
            else:
                top1 = top[1]
        recall = top1[1] / len(track_gt_boxes[track_id])
        precision = top1[1] / len(track_result[top1[0]]["bboxes"])
        idf1[track_id] = 2 * precision * recall / (precision + recall)
    mean_idf1 = np.mean(list(idf1.values()))

    # idswitch
    id_switch = {}
    for track_id in track_pre_calculate.keys():
        cur_track = []
        gt_length = len(track_pre_calculate[track_id]["pre"])
        switch = 0
        for item in track_pre_calculate[track_id]["pre"]:
            if item != -1:
                cur_track += [item]
        last_track_id = None
        for i in range(len(cur_track)):
            cur_track_id = cur_track[i]
            if not last_track_id:
                last_track_id = cur_track_id
            else:
                if cur_track_id != last_track_id:
                    switch += 1
                last_track_id = cur_track_id
        id_switch[track_id] = {"idswitch": switch, "gt_length": gt_length}

    # track总长度
    track_length_sum = 0
    # idswitch总次数
    idswitch_sum = 0
    # idswitch次数占track长度的比例
    idswitch_rate = []
    for track_id in id_switch.keys():
        track_length_sum += id_switch[track_id]["gt_length"]
        idswitch_sum += id_switch[track_id]["idswitch"]
        idswitch_rate += [id_switch[track_id]["idswitch"] / id_switch[track_id]["gt_length"]]
    track_metric = {
        "mean_idf1": mean_idf1,
        "track_length_sum": track_length_sum,
        "idswitch_sum": idswitch_sum,
        "idswitch_rate": np.mean(idswitch_rate),
    }
    return track_metric
