import numpy as np
from scipy.optimize import linear_sum_assignment as linear_assignment
from sklearn.metrics import pairwise_distances

from perceptron.utils.traffic_det2d_utils import bbox as bbox_utils
from perceptron.utils.traffic_det2d_utils.tracker.tracker.base_tracker import BaseTracker, Tracker


class LinearAssignmentTracker(BaseTracker):
    def calc_iou_cost(self, frame_results):
        if len(self.active_tracks) == 0 or len(frame_results) == 0:
            return None
        track_boxes = np.array([self.active_tracks[key].bboxes[-1] for key in self.active_tracks])[:, :4]
        det_boxes = np.array(frame_results)[:, :4]
        iou, _, _ = bbox_utils.iouab(track_boxes, det_boxes)
        return 1 - iou

    def _map_index_to_trackid(self):
        indices2trackid = {}
        for i, key in enumerate(self.active_tracks):
            indices2trackid[i] = key
        return indices2trackid

    def update_frame(self, frame_results, frame_id):
        self.delete_died_tracks(frame_id)
        # match every box with active tracks
        cost_matrix = self.calc_iou_cost(frame_results)
        if cost_matrix is None:
            if len(frame_results) == 0:
                return
            unmatched_detections = frame_results
        else:
            row_ind, col_ind = linear_assignment(cost_matrix)
            unmatched_tracks, unmatched_detections = [], []
            for col, det_box in enumerate(frame_results):
                if len(row_ind) == 0 or col not in col_ind:
                    unmatched_detections.append(det_box)
            indices2trackid = self._map_index_to_trackid()
            for row, track_id in enumerate(self.active_tracks):
                if row not in row_ind:
                    unmatched_tracks.append(indices2trackid[row])
            if len(row_ind) > 0:
                for row, col in zip(row_ind, col_ind):
                    track_id = indices2trackid[row]
                    cur_bbox = frame_results[col]
                    self.active_tracks[track_id].update_bbox(cur_bbox, frame_id)
        for bbox in unmatched_detections:
            cur_track_id = self._get_next_track_id()
            new_track = Tracker(bbox, frame_id, cur_track_id, self.min_track_len)
            self.active_tracks.update({cur_track_id: new_track})


class LinearAssignmentTracker_Dist(LinearAssignmentTracker):
    # add dist as cost
    def __init__(
        self,
        score_thr=0,
        miss_tolerence=6,
        min_track_len=3,
        max_dist=100,
        metric="euclidean",
        distance="boundary",
        norm=True,
    ):
        super(LinearAssignmentTracker_Dist, self).__init__(score_thr, miss_tolerence, min_track_len)
        assert distance in ["boundary", "center"], "distance should be boundary or center"
        self.max_dist = max_dist
        self.metric = metric
        self.distance = distance
        self.norm = norm

    def calc_dist_cost(self, frame_results, frame_id):
        if len(self.active_tracks) == 0 or len(frame_results) == 0:
            return None
        track_boxes = np.array([self.active_tracks[key].bboxes[-1] for key in self.active_tracks])[:, :4]
        det_boxes = np.array(frame_results)[:, :4]
        if self.distance == "center":
            track_centers_x = (track_boxes[:, 0] + track_boxes[:, 2]) / 2
            track_centers_y = (track_boxes[:, 1] + track_boxes[:, 3]) / 2
            det_centers_x = (det_boxes[:, 0] + det_boxes[:, 2]) / 2
            det_centers_y = (det_boxes[:, 1] + det_boxes[:, 3]) / 2
            feat_track = np.vstack([track_centers_x, track_centers_y]).T
            feat_det = np.vstack([det_centers_x, det_centers_y]).T
        elif self.distance == "boundary":
            feat_track = track_boxes
            feat_det = det_boxes
        dist = pairwise_distances(feat_track, feat_det, self.metric)
        return dist

    def update_frame(self, frame_results, frame_id, nori_id):
        return_track = self.delete_died_tracks(frame_id)
        # match every box with active tracks
        cost_matrix = self.calc_dist_cost(frame_results, frame_id)
        if cost_matrix is None:
            if len(frame_results) == 0:
                unmatched_tracks = [key for key in self.active_tracks]
                unmatched_detections = []
            else:
                unmatched_detections = frame_results
                unmatched_tracks = []
        else:
            row_ind, col_ind = linear_assignment(cost_matrix)
            unmatched_tracks, unmatched_detections = [], []
            for col, det_box in enumerate(frame_results):
                if len(row_ind) == 0 or col not in col_ind:
                    unmatched_detections.append(det_box)
            indices2trackid = self._map_index_to_trackid()
            for row, track_id in enumerate(self.active_tracks):
                if row not in row_ind:
                    unmatched_tracks.append(indices2trackid[row])
            if len(row_ind) > 0:
                for row, col in zip(row_ind, col_ind):
                    track_id = indices2trackid[row]
                    cur_bbox = frame_results[col]
                    cost = cost_matrix[row][col]
                    if self.norm:
                        track_bbox = self.active_tracks[track_id].bboxes[-1]
                        track_area = np.sqrt((track_bbox[3] - track_bbox[1]) * (track_bbox[2] - track_bbox[0]))
                        det_area = np.sqrt((cur_bbox[3] - cur_bbox[1]) * (cur_bbox[2] - cur_bbox[0]))
                        cost = cost / np.sqrt(track_area * det_area)
                    if cost < self.max_dist:
                        self.active_tracks[track_id].update_bbox(cur_bbox, frame_id, nori_id)
                    else:
                        unmatched_detections.append(cur_bbox)
                        unmatched_tracks.append(indices2trackid[row])
        for bbox in unmatched_detections:
            cur_track_id = self._get_next_track_id()
            new_track = Tracker(bbox, nori_id, frame_id, cur_track_id, self.min_track_len)
            self.active_tracks.update({cur_track_id: new_track})
        # FP太多了，得连续检出才算检出，如果是一个非valid的track，miss一次直接杀掉
        for track_id in unmatched_tracks:
            if not self.active_tracks[track_id].is_valid:
                extra_return_track = self.delete_track_by_track_id(track_id)
                return_track.update(extra_return_track)
        return return_track
