from collections import defaultdict


class Tracker:
    def __init__(self, init_pos, init_nori_id, start_frame_id, track_id, min_track_len):
        self.bboxes = [init_pos]
        self.frame_ids = [start_frame_id]
        self.track_id = track_id
        self.min_track_len = min_track_len
        self.is_valid = False
        self.nori_ids = [init_nori_id]

    def update_bbox(self, bbox, cur_frame, nori_id):
        self.frame_ids.append(cur_frame)
        self.bboxes.append(bbox)
        self.nori_ids.append(nori_id)
        if len(self.bboxes) >= self.min_track_len:
            self.is_valid = True


class BaseTracker:
    """
    Args:
        score_thr: match score thr
        miss_tolerence: track will die if there's no new match for miss_tolerence frames
        min_track_len: when there's more than min_track_len bboxes in a track, it's a valid track
        trackers: object of class Tracker
    """

    def __init__(self, score_thr=0.1, miss_tolerence=6, min_track_len=4):
        self.tracker_count = 0
        self.min_track_len = min_track_len
        self.score_thr = score_thr
        self.miss_tolerence = miss_tolerence
        self.active_tracks = defaultdict(dict)
        self.filter_dt = {}

    def _get_next_track_id(self):
        self.tracker_count += 1
        return self.tracker_count

    def delete_died_tracks(self, cur_frame):
        delete_keys = []
        return_track = {}
        for track_id in self.active_tracks:
            last_frame = self.active_tracks[track_id].frame_ids[-1]
            if cur_frame - last_frame > self.miss_tolerence:
                delete_keys.append(track_id)
        for key in delete_keys:
            bboxes = self.active_tracks[key].bboxes
            nori_ids = self.active_tracks[key].nori_ids
            return_track[key] = {
                "bboxes": bboxes,
                "nori_ids": nori_ids,
            }
            if self.active_tracks[key].is_valid:
                for box, nori_id in zip(bboxes, nori_ids):
                    if nori_id not in self.filter_dt:
                        self.filter_dt[nori_id] = []
                    self.filter_dt[nori_id] += [list(box)]
            self.active_tracks.pop(key)
        return return_track

    def delete_track_by_track_id(self, track_id):
        return_track = {}
        return_track[track_id] = {
            "bboxes": self.active_tracks[track_id].bboxes,
            "nori_ids": self.active_tracks[track_id].nori_ids,
        }
        self.active_tracks.pop(track_id)
        return return_track

    def clear_all_tracks(self):
        return_track = {}
        for track_id in self.active_tracks:
            bboxes = self.active_tracks[track_id].bboxes
            nori_ids = self.active_tracks[track_id].nori_ids
            return_track[track_id] = {
                "bboxes": bboxes,
                "nori_ids": nori_ids,
            }
            if self.active_tracks[track_id].is_valid:
                for box, nori_id in zip(bboxes, nori_ids):
                    if nori_id not in self.filter_dt:
                        self.filter_dt[nori_id] = []
                    self.filter_dt[nori_id] += [list(box)]
        self.active_tracks = defaultdict(dict)
        return return_track

    def update_frame(self, frame_results, frame_id):
        pass
