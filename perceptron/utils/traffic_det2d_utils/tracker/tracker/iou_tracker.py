from perceptron.utils.traffic_det2d_utils.tracker.tracker.base_tracker import BaseTracker, Tracker


class IoUTracker(BaseTracker):
    def __init__(self, score_thr=0.2, miss_tolerence=6, min_track_len=3):
        super(IoUTracker, self).__init__(score_thr, miss_tolerence, min_track_len)

    def iou(self, bbox, track):
        track_bbox = track.bboxes[-1]
        bbox_x1, bbox_y1, bbox_x2, bbox_y2 = bbox[0:4]
        track_bbox_x1, track_bbox_y1, track_bbox_x2, track_bbox_y2 = track_bbox[0:4]
        left = max(bbox_x1, track_bbox_x1)
        top = max(bbox_y1, track_bbox_y1)
        right = min(bbox_x2, track_bbox_x2)
        bottom = min(bbox_y2, track_bbox_y2)
        inter = max(0, right - left) * max(0, bottom - top)
        bbox_area = (bbox_x2 - bbox_x1) * (bbox_y2 - bbox_y1)
        track_bbox_area = (track_bbox_x2 - track_bbox_x1) * (track_bbox_y2 - track_bbox_y1)
        iou = inter / (bbox_area + track_bbox_area - inter)
        return iou

    def update_frame(self, frame_results, frame_id):
        self.delete_died_tracks(frame_id)
        # match every box with active tracks
        is_matched = {}
        # 默认每个track都未被match
        for key in self.active_tracks:
            is_matched[key] = False
        # 可能的新track
        potential_new_tracks = []
        for bbox in frame_results:
            max_match_key = None
            max_match_score = -1
            for track_id in self.active_tracks:
                # 　基于一个frame中的每个bbox遍历每个track的最后一个bbox,计算iou
                cur_match_score = self.iou(bbox, self.active_tracks[track_id])
                # 　如果iou大于最大匹配分数,并且这个track未被匹配,则更新最大匹配分数和匹配到的track id
                if cur_match_score > max_match_score and not is_matched[track_id]:
                    max_match_key = track_id
                    max_match_score = cur_match_score
            # 　如果有匹配到的track,并且最大的匹配分数大于设定的阈值,则更新这个track
            if max_match_key is not None and max_match_score > self.score_thr:
                self.active_tracks[max_match_key].update_bbox(bbox, frame_id)
                is_matched[max_match_key] = True
            # 否则这个bbox视为一个新track
            else:
                potential_new_tracks.append(bbox)

        # add unmatched bboxes as new tracks
        for bbox in potential_new_tracks:
            cur_track_id = self._get_next_track_id()
            new_track = Tracker(bbox, frame_id, cur_track_id, self.min_track_len)
            self.active_tracks.update({cur_track_id: new_track})
