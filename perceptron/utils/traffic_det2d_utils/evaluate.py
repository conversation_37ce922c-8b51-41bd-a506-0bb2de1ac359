from collections import defaultdict

import numpy as np
import tabulate
from cvpack2.utils import comm
from perceptron.utils.traffic_det2d_utils import bbox as bbox_utils
from perceptron.utils.traffic_det2d_utils.tracker.label_assign import tracker_label_update_assign
from perceptron.utils.traffic_det2d_utils.tracker.track_eval import track_eval


class TrafficEvaluator:
    def __init__(
        self,
        distributed,
        iou_thr=0.5,
        score_thr={"traffic_light": 0.05},
        min_side_len=None,
        class2dim={"traffic_light": 1},  # 每个类别在检测结果中的对应的key，目前只有交通灯，后续可能加交通标志等其他类别
        sub_areas={"traffic_light": [0, 500, 1500, 4500, np.float("inf")]},
        timeline=[],
        fp_rate=0.05,
    ):
        self._iou_thr = iou_thr
        self._score_thr = score_thr
        self._min_area_thr = min_side_len ** 2 if min_side_len else None
        self._class2dim = class2dim
        self._sub_areas = sub_areas
        self._distributed = distributed
        self.fp_rate = fp_rate
        self.init(timeline=timeline)

    def init(self, timeline=[]):
        self.dt = dict()
        self.gt = dict()
        self.all_dt = dict()
        self.all_gt = dict()
        self.timeline = timeline

    def process(self, dt, gt):
        self.dt.update(dt)
        self.gt.update(gt)

    def _filter_small_box(self, box_list):
        if not len(box_list):
            return box_list
        boxes = box_list[:, :4]
        box_area = bbox_utils.area(boxes)
        if box_area is not None:
            filter_box_idx = np.where(box_area < self._min_area_thr)[0]
            filtered_box_list = np.delete(box_list, filter_box_idx, axis=0)
        else:
            filtered_box_list = box_list
        return filtered_box_list

    def filter_masked_gt(self, gt_list, ign_list):
        # set gt boxes ignored which are covered by masks
        gt_ioa = bbox_utils.ioa(gt_list[:, :4], ign_list)
        ign_gt_idx = np.where(gt_ioa > self._iou_thr)[0]
        if len(ign_gt_idx):
            masked_gt_list = gt_list[ign_gt_idx]
        else:
            masked_gt_list = np.zeros((0, 4))
        filtered_gt_list = np.delete(gt_list, ign_gt_idx, axis=0)
        return filtered_gt_list, masked_gt_list

    def filter_under_thr_dt(self, dt_list, class_name):
        # filter box under dt thr and sort the rest by scores descendly
        dt_boxes, dt_scores = dt_list[:, :4], dt_list[:, 4]
        (keeped_dt_idxes,) = np.where(dt_scores > self._score_thr[class_name])
        dt_boxes, dt_scores = dt_boxes[keeped_dt_idxes], dt_scores[keeped_dt_idxes]
        dt_order = np.argsort(-dt_scores)
        dt_boxes, dt_scores = dt_boxes[dt_order], dt_scores[dt_order]
        return dt_boxes, dt_scores

    def evaluate_single(self, dt_list, gt_list, ign_list=[], class_name="traffic_light"):
        if self._min_area_thr:
            dt_list = self._filter_small_box(dt_list)
            gt_list = self._filter_small_box(gt_list)
        filtered_gtboxes, masked_gtboxes = self.filter_masked_gt(gt_list, ign_list)
        eval_result = {"dt_list": [], "miss_list": []}
        if not len(dt_list):
            eval_result["miss_list"] = filtered_gtboxes.tolist()
            return eval_result, filtered_gtboxes
        dt_boxes, dt_scores = self.filter_under_thr_dt(dt_list, class_name)
        gt_iou, gt_ioa, gt_iob = bbox_utils.iouab(dt_boxes, filtered_gtboxes[:, :4])
        matched_list = bbox_utils.match_boxes(gt_iou, self._iou_thr)
        matched_gt_idx, fp_idx_list, box_type = [], [], {}
        for item in matched_list:
            dt_idx, gt_idx = item["dt_idx"], item["gt_idx"]
            if gt_idx is None:
                upper_iou = max(gt_iou[dt_idx])
                if upper_iou > self._iou_thr:  # 在iou>thr时,未被assign到gt: 多峰fp
                    box_type[dt_idx] = "high_iou_fp"
                else:  # 真fp+框不准fp,这里不做进一步区分
                    box_type[dt_idx] = "low_iou_fp"
                fp_idx_list.append(dt_idx)
            else:
                box_type[dt_idx] = "tp"
                gt = filtered_gtboxes[gt_idx].tolist()
                matched_gt_idx.append(gt_idx)
                eval_result["dt_list"].append(
                    {
                        "dt": dt_boxes[dt_idx].tolist(),
                        "gt": gt[:4],
                        "score": dt_scores[dt_idx].item(),
                        "type": box_type[dt_idx],
                        "track_id": int(gt[4]) if len(gt) > 4 else -1,
                        "track_predict": -1,
                    }
                )
        # 再和masked_gt匹配一次，如果没匹配中，才是fp
        _fp_dtboxes, _fp_scores = dt_boxes[fp_idx_list], dt_scores[fp_idx_list]
        ign_ioa = bbox_utils.ioa(_fp_dtboxes, ign_list)
        ignored_idx_set = set(bbox_utils.match_ignored(ign_ioa, self._iou_thr))
        masked_iou, masked_ioa, masked_iob = bbox_utils.iouab(_fp_dtboxes, masked_gtboxes[:, :4])
        matched_list = bbox_utils.match_boxes(masked_iou, self._iou_thr)
        for item in matched_list:
            dt_idx, gt_idx = item["dt_idx"], item["gt_idx"]
            # 匹配中了masked gt，算作ignore
            if gt_idx is not None:
                continue
            if dt_idx in ignored_idx_set:
                continue
            # 真正的fp
            eval_result["dt_list"].append(
                {
                    "dt": _fp_dtboxes[dt_idx].tolist(),
                    "gt": None,
                    "score": _fp_scores[dt_idx].item(),
                    "type": box_type[fp_idx_list[dt_idx]],
                    "track_id": None,
                }
            )
        eval_result["miss_list"] = np.delete(filtered_gtboxes, matched_gt_idx, axis=0).tolist()
        return eval_result, filtered_gtboxes

    def area2key(self, area, sub_areas):
        for i in range(0, len(sub_areas) - 1):
            if area <= sub_areas[i + 1]:
                return (sub_areas[i], sub_areas[i + 1])

    def evaluate(self, all_gt, all_dt, class_name="traffic_light", sub_areas=[0, 500, 1500, 4500, np.float("inf")]):
        """
        all_gt: dict, gt of all image, {nid: {class_name: list_of_gt_bboxes}}
        all_dt: dict, dt of all image, {nid: {class_name: list_of_dt_bboxes_and_scores}}
        class_name: str, one of class_names in benchmark, should exist in score_thr or class2dim
        sub_areas: list, intervals between each two num are closed at the left and opened at the right
        returns:
            eval_result: dict, format: {nid: [{dt: xx, gt: xx, score: xx, type: xx}, ...]}
            all_gt_cnt: count of all_gt
            bucket_area_cnt: count of each sub_area
        """
        bucket_area_cnt = dict()  # 对面积进行分桶
        for i in range(0, len(sub_areas) - 1):
            key = (sub_areas[i], sub_areas[i + 1])
            bucket_area_cnt[key] = 0  # 每个面积范围的数量
        eval_result = {}
        all_gt_cnt = 0
        for key in all_gt.keys():
            single_eval_result, filtered_gt_list = self.evaluate_single(
                np.array(all_dt[key][self._class2dim[class_name]]), np.array(all_gt[key][self._class2dim[class_name]])
            )
            eval_result[key] = single_eval_result
            all_gt_cnt += len(filtered_gt_list)
            for gt_bbox in filtered_gt_list:
                area = (gt_bbox[3] - gt_bbox[1]) * (gt_bbox[2] - gt_bbox[0])
                bucket_area_cnt[self.area2key(area, sub_areas)] += 1
        return eval_result, all_gt_cnt, bucket_area_cnt

    @staticmethod
    def calculate_map(eval_result):
        area = 0
        for pre_idx, eval_item in enumerate(eval_result[1:]):
            pre_item = eval_result[pre_idx]
            delta_h = (pre_item["precision"] + eval_item["precision"]) / 2
            delta_w = eval_item["recall"] - pre_item["recall"]
            area += delta_w * delta_h
        return area

    def gen_statistic(
        self,
        eval_result,
        gt_count,
        bucket_gt_cnt,
        image_count,
        sub_areas=[0, 500, 1500, 4500, np.float("inf")],
    ):
        tp, fp, all_eval_result = 0.0, 0.0, []
        high_iou_fp, low_iou_fp = 0.0, 0.0
        bucket_tp = defaultdict(int)
        for item in eval_result.values():
            all_eval_result.extend(item["dt_list"])
        # item["miss_list"]是miss的列表，暂未使用
        all_eval_result = sorted(all_eval_result, key=lambda item: item["score"], reverse=True)
        bucket_result = defaultdict(list)
        result = []
        for item in all_eval_result:
            if item["gt"] is not None:
                assert item["type"] == "tp"
                tp += 1
                bbox = item["gt"]
                area = (bbox[3] - bbox[1]) * (bbox[2] - bbox[0])
                key = self.area2key(area, sub_areas)
                bucket_tp[key] += 1
                bucket_result[key].append(
                    {
                        "gt_num": bucket_gt_cnt[key],
                        "recall": bucket_tp[key] / max(bucket_gt_cnt[key], np.spacing(1)),
                        "thres": item["score"],
                    }
                )
            else:
                assert "fp" in item["type"]
                if item["type"] == "high_iou_fp":
                    high_iou_fp += 1
                else:
                    low_iou_fp += 1
                fp += 1
            result.append(
                {
                    "gt_num": gt_count,
                    "recall_num": tp,
                    "fp_num": fp,
                    "high_iou_fp": high_iou_fp,
                    "low_iou_fp": low_iou_fp,
                    "recall": tp / max(gt_count, np.spacing(1)),
                    "precision": tp / max(tp + fp, np.spacing(1)),
                    "fppi": fp / image_count,
                    "thres": item["score"],
                }
            )
        mAP = self.calculate_map(result)
        n = len(result)
        recallThrs = np.linspace(0.0, 1.00, 101, endpoint=True)
        cur = 0
        result_filtered = []

        for i in range(n):
            if result[i]["recall"] > recallThrs[cur] or i == n - 1:
                result_filtered.append(result[i])
                cur += 1
        bucket_result_filtered = defaultdict(list)
        for key in bucket_result:
            cur = 0
            for i in range(len(bucket_result[key])):
                if bucket_result[key][i]["recall"] > recallThrs[cur] or i == len(bucket_result[key]) - 1:
                    bucket_result_filtered[key].append(bucket_result[key][i])
                    cur += 1
        return mAP, result_filtered, bucket_result_filtered, item["score"]

    def recall_base_fixed_fp(
        self,
        eval_result,
        gt_count,
        score_thresh,
    ):
        tp, fp, all_eval_result, fp_list, res = 0.0, 0.0, [], [], {}
        for item in eval_result.values():
            all_eval_result.extend(item["dt_list"])

        all_eval_result = sorted(all_eval_result, key=lambda item: item["score"], reverse=True)

        for item in all_eval_result:
            if item["gt"] is None:
                assert "fp" in item["type"]
                fp_list.append(item)

        fp_num_thresh = int(gt_count * self.fp_rate)

        if len(fp_list) <= fp_num_thresh:
            if len(fp_list) == 0:
                recallThrs = score_thresh - 0.01
            else:
                recallThrs = fp_list[-1]["score"] - 0.01
        else:
            recallThrs = fp_list[fp_num_thresh]["score"]

        for i in range(len(all_eval_result)):
            if all_eval_result[i]["score"] > recallThrs and all_eval_result[i]["type"] == "tp":
                tp += 1
            if all_eval_result[i]["score"] > recallThrs and "fp" in all_eval_result[i]["type"]:
                fp += 1

        res["gt_num"] = gt_count
        res["dt_num"] = tp + fp
        res["tp_num"] = tp
        res["fp_num"] = fp
        res["recall"] = tp / max(gt_count, np.spacing(1))
        res["precision"] = tp / (tp + fp)
        res["thres"] = recallThrs

        return res

    def evaluate_and_statistic_without_track(self, all_dt, cls_name, sub_areas):
        # evaluate
        eval_result, all_gt_cnt, bucket_gt_cnt = self.evaluate(self.all_gt, all_dt, cls_name, sub_areas)
        # 通过track_id查找对应的box序列
        track_gt_boxes = dict()
        # 通过nori_id查找当前图中的box属于哪个track_id
        nori_id_track = dict()
        for nori_id in self.timeline:
            for item in eval_result[nori_id]["dt_list"]:
                if item["track_id"]:
                    if item["track_id"] not in track_gt_boxes:
                        track_gt_boxes[item["track_id"]] = list()
                    item["nori_id"] = nori_id
                    track_gt_boxes[item["track_id"]] += [item]
                    if nori_id not in nori_id_track:
                        nori_id_track[nori_id] = list()
                    nori_id_track[nori_id] += [item["track_id"]]
        # statistic mAP and other metric
        mAP, result, bucket_result, score_thresh = self.gen_statistic(
            eval_result,
            all_gt_cnt,
            bucket_gt_cnt,
            len(self.all_gt),
            sub_areas,
        )

        recall_dict = self.recall_base_fixed_fp(eval_result, all_gt_cnt, score_thresh)

        # calculate mIoU
        mIoU = list()
        for mapping_result in eval_result.values():
            dt_list = mapping_result["dt_list"]
            for match in dt_list:
                if match["gt"]:
                    iou = bbox_utils.iou(np.array([match["dt"]]), np.array([match["gt"][:4]]))
                    assert iou.shape == (1, 1)
                    mIoU.append(iou.sum())
        headers = ["metric", "value"]
        metrics = [["AP", np.round(mAP, 4)]]
        for key, value in result[-1].items():
            metrics.append([key, np.round(value, 4)])
        metrics.append([cls_name + "_mIoU", np.round(np.mean(mIoU), 4)])
        s = tabulate.tabulate(metrics, headers, tablefmt="github", numalign="left")
        print(s)

        headers = ["bucket_metric", "gt_num", "recall"]
        metrics = []
        for bucket, bucket_metric in bucket_result.items():
            metrics.append(
                [
                    cls_name + "@" + str(bucket),
                    bucket_metric[-1]["gt_num"],
                    np.round(bucket_metric[-1]["recall"], 4),
                ]
            )
        s = tabulate.tabulate(metrics, headers, tablefmt="github", numalign="left")
        print(s)

        headers = ["metric@fpr=" + str(self.fp_rate), "value"]
        metrics = []
        for key, value in recall_dict.items():
            metrics.append([key, np.round(value, 4)])
        s = tabulate.tabulate(metrics, headers, tablefmt="github", numalign="left")
        print(s)

        return track_gt_boxes, nori_id_track

    def evaluate_and_statistic(self):
        # 多卡分布式测试时, 通过卡间通信传输到一张卡上
        if self._distributed:
            comm.synchronize()
            self.dt = comm.gather(self.dt, dst=0)
            self.gt = comm.gather(self.gt, dst=0)
            if not comm.is_main_process():
                return 0, None
            for item in self.dt:
                self.all_dt.update(item)
            for item in self.gt:
                self.all_gt.update(item)
        else:
            self.all_dt = self.dt
            self.all_gt = self.gt

        for cls_name in self._class2dim.keys():
            sub_areas = self._sub_areas[cls_name]
            track_gt_boxes, nori_id_track = self.evaluate_and_statistic_without_track(self.all_dt, cls_name, sub_areas)

            # track eval&statistic
            if not self.timeline or (len(track_gt_boxes.keys()) == 1 and -1 in track_gt_boxes):
                continue
            track_result, track_gt_boxes, track_filter_dt = tracker_label_update_assign(
                self.timeline, self.all_dt, track_gt_boxes, nori_id_track
            )
            track_metric = track_eval(track_result, track_gt_boxes)
            headers = ["track_metric", "value"]
            metrics = []
            for key, value in track_metric.items():
                metrics.append([key, np.round(value, 4)])
            s = tabulate.tabulate(metrics, headers, tablefmt="github", numalign="left")
            print(s)

            track_gt_boxes, nori_id_track = self.evaluate_and_statistic_without_track(
                track_filter_dt, cls_name, sub_areas
            )
