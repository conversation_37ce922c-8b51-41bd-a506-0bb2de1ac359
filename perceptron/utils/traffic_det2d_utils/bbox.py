import numpy as np


def area(boxes):
    x1, y1, x2, y2 = np.split(boxes, 4, axis=1)
    area = (x2 - x1) * (y2 - y1)
    return np.maximum(area, 0)


def intersection_area_xywh(boxes1, boxes2, gap=0):
    x1, y1, w1, h1 = np.split(boxes1, 4, axis=1)
    x2, y2, w2, h2 = np.split(boxes2, 4, axis=1)
    # We broadcast from (N, 1) and (1, M) to (N, M).
    x0 = np.maximum(x1, np.transpose(x2))
    y0 = np.maximum(y1, np.transpose(y2))
    w0 = np.minimum(x1 + w1, np.transpose(x2 + w2)) - x0 + gap
    h0 = np.minimum(y1 + h1, np.transpose(y2 + h2)) - y0 + gap
    w0 = np.maximum(w0, 0)
    h0 = np.maximum(h0, 0)
    return w0 * h0


def intersection_area(boxes1, boxes2, gap=0):
    xl1, yl1, xr1, yr1 = np.split(boxes1, 4, axis=1)  # m*1
    xl2, yl2, xr2, yr2 = np.split(boxes2, 4, axis=1)  # n*1

    xx1 = np.maximum(xl1, np.transpose(xl2))  # m*n
    yy1 = np.maximum(yl1, np.transpose(yl2))  # m*n
    xx2 = np.minimum(xr1, np.transpose(xr2))
    yy2 = np.minimum(yr1, np.transpose(yr2))

    w = np.maximum(xx2 - xx1 + gap, 0)
    h = np.maximum(yy2 - yy1 + gap, 0)

    return w * h


def iou(boxes1, boxes2):
    _iou, _, _ = iouab(boxes1, boxes2)
    return _iou


def ioa(boxes1, boxes2):
    _, _ioa, _ = iouab(boxes1, boxes2)
    return _ioa


def iouab(boxes1, boxes2):
    if not len(boxes1) or not len(boxes2):
        return (
            np.zeros((len(boxes1), len(boxes2))),
            np.zeros((len(boxes1), len(boxes2))),
            np.zeros((len(boxes1), len(boxes2))),
        )
    area1 = area(boxes1)
    area2 = area(boxes2)
    area0 = intersection_area(boxes1, boxes2)
    iou, ioa, iob = (area0 / (area1 + np.transpose(area2) - area0), area0 / area1, area0 / np.transpose(area2))
    return iou, ioa, iob


def match_boxes(iou_mat, iou_thr):
    num_detected, num_groundtruth = iou_mat.shape
    matched = []
    gt_dict = {i: True for i in range(num_groundtruth)}
    for i in range(num_detected):
        if len(gt_dict) == 0:
            matched.append({"dt_idx": i, "gt_idx": None})
            continue
        max_idx, max_iou = None, -1
        for j, is_valid in gt_dict.items():
            _iou = iou_mat[i, j]
            if is_valid and _iou > max_iou:
                max_iou, max_idx = _iou, j
        gt_idx = None
        if max_iou > iou_thr:
            gt_idx = max_idx
            gt_dict[max_idx] = False
        else:
            gt_idx = None
        matched.append({"dt_idx": i, "gt_idx": gt_idx})
    return matched


def match_ignored(ioa_mat, ioa_threshold):
    num_detected, num_ignored = ioa_mat.shape
    if num_ignored == 0:
        return []
    return [i for i in range(num_detected) if max(ioa_mat[i]) > ioa_threshold]
