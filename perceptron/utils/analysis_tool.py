import pynvml
import torch


def check_gpu_memory():
    """
    Return GPU memory usage information.
    Return: a_str
    """
    pynvml.nvmlInit()
    handle = pynvml.nvmlDeviceGetHandleByIndex(0)
    meminfo = pynvml.nvmlDeviceGetMemoryInfo(handle)
    print_str = "Total {}, allocated {}, free {}".format(
        meminfo.total / 1024 / 1024,
        torch.cuda.memory_allocated("cuda") / 1024 / 1024,
        (meminfo.total - torch.cuda.memory_allocated("cuda")) / 1024 / 1024,
    )
    return print_str
