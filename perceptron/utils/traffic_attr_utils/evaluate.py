import numpy as np
import torch
import torch.nn.functional as F
from cvpack2.utils import comm
from loguru import logger
from perceptron.utils.traffic_attr_utils.metric import (
    compute_pred_gt_thr,
    compute_cfm_acc,
    compute_fp_pre_recall,
    convert_cfm_to_wiki,
    convert_three_index_to_wiki,
)


class TrafficAttrEvaluator:
    def __init__(
        self,
        distributed,
        type_class,
        color_class,
        val_list,
        fpr_thr=1e-2,
    ):
        self._distributed = distributed
        self.type_class = type_class
        self.color_class = color_class
        self.val_list = val_list
        self.fpr_thr = fpr_thr
        self.results = {}
        self.results["metric_per_epoch"] = []
        self.task2label_map = {
            "color": self.color_class,
            "type": self.type_class,
        }
        self.neg_thrs = {
            "color": [],
            "type": [],
        }
        self.init()

    def init(self):
        self.shape_score = {}
        self.color_score = {}
        self.shape_labels = {}
        self.color_labels = {}
        self.all_shape_score = {}
        self.all_color_score = {}
        self.all_shape_labels = {}
        self.all_color_labels = {}
        for dataset in self.val_list.keys():
            self.shape_score[dataset] = []
            self.color_score[dataset] = []
            self.shape_labels[dataset] = []
            self.color_labels[dataset] = []
            self.all_shape_score[dataset] = []
            self.all_color_score[dataset] = []
            self.all_shape_labels[dataset] = []
            self.all_color_labels[dataset] = []

    def process(self, datasets, shape_scores, color_scores, shape_labels, color_labels):
        for dataset, shape_score, color_score, shape_label, color_label in zip(
            datasets, shape_scores, color_scores, shape_labels, color_labels
        ):
            self.shape_score[dataset] += [shape_score.tolist()]
            self.color_score[dataset] += [color_score.tolist()]
            self.shape_labels[dataset] += [shape_label.tolist()]
            self.color_labels[dataset] += [color_label.tolist()]

    def gather(self):
        if self._distributed:
            comm.synchronize()
            for dataset in self.val_list.keys():
                self.shape_score[dataset] = comm.gather(self.shape_score[dataset], dst=0)
                self.color_score[dataset] = comm.gather(self.color_score[dataset], dst=0)
                self.shape_labels[dataset] = comm.gather(self.shape_labels[dataset], dst=0)
                self.color_labels[dataset] = comm.gather(self.color_labels[dataset], dst=0)

                for item in self.shape_score[dataset]:
                    self.all_shape_score[dataset] += item
                for item in self.color_score[dataset]:
                    self.all_color_score[dataset] += item
                for item in self.shape_labels[dataset]:
                    self.all_shape_labels[dataset] += item
                for item in self.color_labels[dataset]:
                    self.all_color_labels[dataset] += item
            if not comm.is_main_process():
                return False
        else:
            self.all_shape_score = self.shape_score
            self.all_color_score = self.color_score
            self.all_shape_labels = self.shape_labels
            self.all_color_labels = self.color_labels
        return True

    def evaluate(self):
        attr_pred = {}
        attr_gt = {}
        attr_pred["all"] = {}
        attr_gt["all"] = {}
        for dataset in self.val_list.keys():
            attr_pred[dataset] = {}
            attr_gt[dataset] = {}
            attr_pred[dataset]["color"] = np.array([np.array(item).argmax(0) for item in self.all_color_score[dataset]])
            attr_pred[dataset]["color_logits"] = np.array(
                [F.softmax(torch.tensor(item), dim=0).tolist() for item in self.all_color_score[dataset]]
            )
            attr_pred[dataset]["type"] = np.array([np.array(item).argmax(0) for item in self.all_shape_score[dataset]])
            attr_pred[dataset]["type_logits"] = np.array(
                [F.softmax(torch.tensor(item), dim=0).tolist() for item in self.all_shape_score[dataset]]
            )
            attr_gt[dataset]["color"] = np.array([item for item in self.all_color_labels[dataset]])
            attr_gt[dataset]["type"] = np.array([item for item in self.all_shape_labels[dataset]])

        pred_keys = ["color", "color_logits", "type", "type_logits"]
        gt_keys = ["color", "type"]
        for key in pred_keys:
            attr_pred["all"][key] = np.concatenate(
                [v[key] for k, v in attr_pred.items() if k != "all" and v[key].any()]
            )
        for key in gt_keys:
            attr_gt["all"][key] = np.concatenate([v[key] for k, v in attr_gt.items() if k != "all" and v[key].any()])

        return attr_pred, attr_gt

    def statistic(self, attr_pred, attr_gt):
        for dataset in attr_pred.keys():
            for task, value in self.task2label_map.items():
                logits = attr_pred[dataset][task + "_logits"]
                assert logits.any(), "not data in dataset {}".format(dataset)
                gt = attr_gt[dataset][task]
                if dataset == "all":
                    pred, gt, self.neg_thrs[task] = compute_pred_gt_thr(
                        logits, gt, task, self.task2label_map, self.fpr_thr
                    )
                    fpr, precision, recall = compute_fp_pre_recall(pred, gt, task, self.task2label_map)
                    print(
                        convert_three_index_to_wiki(
                            fpr, precision, recall, task, self.task2label_map, self.fpr_thr, dataset
                        )
                    )
                    logger.info(task + "_neg_thrs is {}".format(self.neg_thrs[task]))
                    if task == "color":
                        self.results["metric_per_epoch"].append(np.mean(recall[: len(value) - 1]))
                        update_best_ckpt = max(self.results["metric_per_epoch"]) == self.results["metric_per_epoch"][-1]
                        logger.info(
                            "当前epoch color_mean_recall@{}={}".format(self.fpr_thr, self.results["metric_per_epoch"][-1])
                        )
                else:
                    class_num = len(value) - 1
                    pred = np.array([class_num for _ in range(len(gt))])
                    for cls_num in range(class_num):
                        pred[np.where(logits[:, cls_num] > self.neg_thrs[task][cls_num])[0]] = cls_num
                    fpr, precision, recall = compute_fp_pre_recall(pred, gt, task, self.task2label_map)
                    conflict = (logits[:, :class_num] > self.neg_thrs[task]).sum(axis=1) > 1
                    pred[conflict] = logits.argmax(axis=1)[conflict]
                    print(
                        convert_three_index_to_wiki(
                            fpr, precision, recall, task, self.task2label_map, self.fpr_thr, dataset
                        )
                    )
                cfm, acc = compute_cfm_acc(pred, gt, label_shape=len(value))
                print(convert_cfm_to_wiki(cfm, task, self.task2label_map, self.fpr_thr, dataset))

        return update_best_ckpt

    def evaluate_and_statistic(self):
        is_main_process = self.gather()
        if not is_main_process:
            return
        attr_pred, attr_gt = self.evaluate()
        update_best_ckpt = self.statistic(attr_pred, attr_gt)
        return update_best_ckpt
