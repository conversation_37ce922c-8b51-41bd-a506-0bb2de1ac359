import numpy as np
import tabulate


def compute_pred_gt_thr(logits, gt, task, TASK2LABEL_MAP, fpr=0, fake_cls=True):
    class_num = len(TASK2LABEL_MAP[task]) - 1 if fake_cls else len(TASK2LABEL_MAP[task])
    neg_scores = [[] for _ in range(class_num)]
    neg_thrs = [[] for _ in range(class_num)]
    for i in range(class_num):
        neg_scores[i] = list(logits[gt != i][:, i])
        neg_thrs[i] = sorted(neg_scores[i])[int(len(neg_scores[i]) * (1 - fpr))]
    pred = np.array([class_num for _ in range(len(gt))])
    for cls_num in range(class_num):
        pred[np.where(logits[:, cls_num] > neg_thrs[cls_num])[0]] = cls_num
    conflict = (logits[:, :class_num] > neg_thrs).sum(axis=1) > 1
    pred[conflict] = logits.argmax(axis=1)[conflict]
    return pred, gt, neg_thrs


def compute_fp_pre_recall(pred, gt, task, TASK2LABEL_MAP):
    fp, precision, recall = [], [], []
    for idx in range(len(TASK2LABEL_MAP[task])):
        fp.append(np.sum((pred == idx) * (gt != idx)) / (1.0 * np.sum(gt != idx)))
        precision.append(np.sum((pred == idx) * (gt == idx)) / (1.0 * np.sum(pred == idx)))
        recall.append(np.sum((pred == idx) * (gt == idx)) / (1.0 * np.sum(gt == idx)))
    return [fp, precision, recall]


def convert_three_index_to_wiki(fp, precision, recall, task, TASK2LABEL_MAP, thr, dataset):
    data = []
    class_num = len(TASK2LABEL_MAP[task])
    headers = [dataset + "_" + task + " metric@" + str(thr), "fpr", "precision", "recall"]
    for i in range(class_num):
        sub_data = []
        sub_data.append(TASK2LABEL_MAP[task][i])
        sub_data.append(np.round(fp[i], 4))
        sub_data.append(np.round(precision[i], 4))
        sub_data.append(np.round(recall[i], 4))
        data.append(sub_data)
    s = tabulate.tabulate(data, headers, tablefmt="github")
    lines = s.split("\n")
    lines[1] = lines[1].replace("+", "|")
    return "\n".join(lines)


def confusion_matrix_self_defined(y_true, y_pred, label_shape):
    cfm = np.zeros((label_shape, label_shape))
    for gt, pred in zip(y_true, y_pred):
        cfm[gt][pred] += 1
    return cfm


def compute_cfm_acc(y_pred, y_true, label_shape=0, is_norm=False):
    """
    calculate the confusion matrix
    :param pred:
    :param gt:
    :param label_shape:
    :param is_norm:
    :return:
    """
    acc = (y_pred == y_true).mean()
    cfm = confusion_matrix_self_defined(y_true, y_pred, label_shape)
    if is_norm:
        for i in range(label_shape):
            gt_num = np.sum(cfm, axis=1)[i]
            for j in range(label_shape):
                cfm[i][j] = cfm[i][j] / gt_num if gt_num != 0 else 0
    # compute the avg acc for each class
    cfm_diag = np.diagonal(cfm)
    cfm_r_sum = np.sum(cfm, axis=0)
    cfm_c_sum = np.sum(cfm, axis=1)
    cfm_precision = []
    cfm_recall = []
    for i in range(label_shape):
        cfm_precision.extend([cfm_diag[i] / cfm_r_sum[i] if cfm_r_sum[i] != 0 else 1])
        cfm_recall.extend([cfm_diag[i] / cfm_c_sum[i] if cfm_c_sum[i] != 0 else 1])
    cfm = np.r_[cfm, [cfm_precision]]  # add row represent acc to the cfm
    avg_pre = float(np.mean(cfm_precision))
    avg_rec = float(np.mean(cfm_recall))
    f1_score = 2 * avg_pre * avg_rec / (avg_pre + avg_rec)
    cfm = np.c_[cfm, np.append(cfm_recall, "{:.4f}/{:.4f}/{:.4f}".format(avg_pre, avg_rec, f1_score))]
    for i in range(cfm.shape[0]):
        for j in range(cfm.shape[1]):
            if i == cfm.shape[0] - 1 and j == cfm.shape[1] - 1:
                continue
            cfm[i, j] = "{:.4f}".format(float(cfm[i, j]))
    return cfm, acc


def convert_cfm_to_wiki(cfm, task, TASK2LABEL_MAP, thr, dataset):
    """
    convert the confusion matrix into the wiki formated tabulate
    :param cfm:
    :param task:
    :param tablefmt:
    :return:
    """
    data = []
    class_num = len(TASK2LABEL_MAP[task])
    headers = [dataset + "_" + task + " confusion matrix@" + str(thr)]
    for i in range(class_num):
        headers.append(TASK2LABEL_MAP[task][i])
    headers.append("recall")
    for i in range(len(cfm)):
        if i == len(cfm) - 1:
            data0 = ["precision"]
        else:
            data0 = [TASK2LABEL_MAP[task][i]]
        data0.extend(cfm[i])
        data.append(data0)
    s = tabulate.tabulate(data, headers, tablefmt="github")
    lines = s.split("\n")
    lines[1] = lines[1].replace("+", "|")
    return "\n".join(lines)
