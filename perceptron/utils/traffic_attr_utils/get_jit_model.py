"""
//<! The Prj of getting JitModel for Traffic Lights Attrs
@author: PengXs
@date: 2022.1.7

"""
import os
import torch
import refile
from loguru import logger
from perceptron.exps.cls2d.traffic_attr_res50 import resnet
from torchvision.models.resnet import Bottleneck


class JitModel:
    def __init__(self, model_dir, save_dir):
        assert refile.s3_exists(model_dir), "model_dir should exist in remote path"
        self.model_dir = model_dir
        self.save_dir = save_dir
        self.tmp_dir = "./model.tmp"
        self.img_W = 64
        self.img_H = 64
        self.img_C = 3

        self.color_class = ["red", "yellow", "green", "others"]
        self.type_class = [
            "circle",
            "turnleft",
            "turnright",
            "gostraight",
            "turnaround",
            "turnleftandaround",
            "bicycle",
            "sidewalk",
            "countdown",
            "others",
        ]

        self.model = self._init_model()
        self.model.cuda()
        self.model.eval()

    def _init_model(self):
        kwargs = {
            "type_class": self.type_class,
            "color_class": self.color_class,
        }
        model = resnet("resnet50", Bottleneck, [3, 4, 6, 3], pretrained=True, progress=True, **kwargs)
        model.load_state_dict(torch.load(refile.smart_open(self.model_dir, "rb"))["model_state"])
        return model

    def get_git_model(self):
        dummy_input = torch.ones(1, self.img_C, self.img_H, self.img_W).cuda()
        torchscript_model = torch.jit.trace(self.model, dummy_input)
        torch.jit.save(torchscript_model, self.tmp_dir)
        refile.s3.s3_upload(self.tmp_dir, self.save_dir)
        cmd = "rm -rf {}".format(self.tmp_dir)
        os.system(cmd)
        logger.info("finish convert {} -> {}".format(self.model_dir, self.save_dir))


if __name__ == "__main__":
    net = JitModel(
        model_dir="s3://wfy/temp/checkpoint_epoch_0.pth",
        save_dir="s3://wfy/temp/checkpoint_epoch_0.jit",
    )
    net.get_git_model()
