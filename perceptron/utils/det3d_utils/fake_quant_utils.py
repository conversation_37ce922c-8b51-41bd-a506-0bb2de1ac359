import numpy as np
import torch
import torch.nn as nn


def fake_quantize(x, scale, minmax=128, int8_mode=False):
    is_numpy = False
    if isinstance(x, np.ndarray):
        is_numpy = True
        x = torch.from_numpy(x)
    # x = torch.clamp(torch.floor(x / scale + 0.5), -minmax, minmax - 1)
    x = x / scale + 0.5
    x = (torch.floor(x) - x).detach() + x
    x = torch.clamp(x, -minmax, minmax - 1)
    if int8_mode:
        x = x.to(torch.int8)
    else:
        x = x * scale
    if is_numpy:
        x = x.numpy()
    return x


class Clamp(nn.Module):
    def __init__(self, use_fake_quant=False, fake_quant_scale=1 / 16, quant_scale=8):
        super(Clamp, self).__init__()
        self.quant_scale = quant_scale
        self.fake_quant_scale = fake_quant_scale
        self.use_fake_quant = use_fake_quant

    def forward(self, x):
        if self.use_fake_quant:
            x = fake_quantize(x, self.fake_quant_scale)

        return torch.clamp(x, -self.quant_scale, self.quant_scale * 127 / 128)
