import numpy as np
import torch


def assert_allclose_or_equal(key, value1, value2, index=None, sub_key=None):
    """Helper to validate equality or approximate equality."""
    location = f"{key}" + (f"[{index}]" if index is not None else "") + (f".{sub_key}" if sub_key else "")

    if isinstance(value1, np.ndarray):
        if not np.allclose(value1, value2, rtol=1e-4, atol=1e-4):
            print(f"Data {location} does not match. (np.ndarray)")
    elif isinstance(value1, torch.Tensor):
        if not torch.allclose(value1, value2, rtol=1e-4, atol=1e-4):
            print(f"Data {location} does not match. (torch.Tensor)")
    else:
        if value1 != value2:
            print(f"Data {location} does not match. (Other type)")


def recursive_compare(key, value1, value2, index=None, sub_key=None):
    """Recursively compare elements in nested structures."""
    location = f"{key}" + (f"[{index}]" if index is not None else "") + (f".{sub_key}" if sub_key else "")

    # 如果是 points 或 radar_points，进行排序操作
    if "points" in key:
        if isinstance(value1, torch.Tensor) and value1.ndim > 1:
            value1 = torch.sort(value1, dim=-2).values
        elif isinstance(value1, np.ndarray) and value1.ndim > 1:
            value1 = np.sort(value1, axis=-2)

        if isinstance(value2, torch.Tensor) and value2.ndim > 1:
            value2 = torch.sort(value2, dim=-2).values
        elif isinstance(value2, np.ndarray) and value2.ndim > 1:
            value2 = np.sort(value2, axis=-2)

    if isinstance(value1, dict) and isinstance(value2, dict):
        assert set(value1.keys()) == set(value2.keys()), f"Data {location} keys do not match."
        for sub_key in value1:
            if isinstance(value1[sub_key], (list, tuple)) and isinstance(value2[sub_key], (list, tuple)):
                assert len(value1[sub_key]) == len(
                    value2[sub_key]
                ), f"Data {location} length does not match at sub_key {sub_key}."
                for idx, (item1, item2) in enumerate(zip(value1[sub_key], value2[sub_key])):
                    if isinstance(item1, dict) and isinstance(item2, dict):
                        for inner_sub_key in item1:
                            recursive_compare(
                                key, item1[inner_sub_key], item2[inner_sub_key], index=idx, sub_key=inner_sub_key
                            )
                    else:
                        recursive_compare(key, item1, item2, index=idx, sub_key=sub_key)
            else:
                recursive_compare(key, value1[sub_key], value2[sub_key], sub_key=sub_key)
    elif isinstance(value1, (list, tuple)) and isinstance(value2, (list, tuple)):
        assert len(value1) == len(value2), f"Data {location} length does not match."
        for idx, (item1, item2) in enumerate(zip(value1, value2)):
            recursive_compare(key, item1, item2, index=idx, sub_key=sub_key)
    elif hasattr(value1, "tensor"):
        value1 = value1.tensor
        value2 = value2.tensor
        assert_allclose_or_equal(key, value1, value2, index, sub_key)
    else:
        assert_allclose_or_equal(key, value1, value2, index, sub_key)
