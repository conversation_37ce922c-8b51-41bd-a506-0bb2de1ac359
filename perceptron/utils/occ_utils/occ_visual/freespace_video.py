import pickle
import numpy as np
import cv2
import json
import io
import copy
import nori2 as nori
from skimage import io as skimage_io
from typing import Dict, List, Tuple
from skimage import transform
from pathlib import Path
import os
from tqdm import tqdm
from numpy.lib import recfunctions as rfn
import argparse
import concurrent.futures
from datetime import datetime
import rrun
import logging
import refile
from itertools import groupby
import matplotlib.colors as mcolors

# import sys
# project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
# sys.path.append(project_root)
from perceptron.utils.occ_utils.occ_postprocess.freespace_postprocess import FreespacePostProcessor


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def get_cluster():
    return os.environ.get("KUBEBRAIN_CLUSTER_ENTRY", None)


nori_available = get_cluster() == "https://hh-d.brainpp.cn"


def spec_config(name, charged_group):
    spec = rrun.RunnerSpec()
    spec.name = name
    log_dir = os.path.abspath("/data/.rrun_log/{}/{}".format(name, datetime.now().strftime("%Y-%m-%dT%H:%M")))
    spec.log_dir = log_dir
    spec.charged_group = charged_group
    spec.resources.cpu = 6
    spec.resources.gpu = 0
    spec.priority = "Midiuem"
    spec.resources.memory_in_mb = 32 * 1024
    spec.max_wait_duration = "24h"
    spec.preemptible = False
    # spec.scheduling_hint.positive_tags.extend(["2080ti"])
    return spec


def load_pkl(fp):
    data = pickle.load(refile.smart_open(fp, "rb"))
    return data


def load_json(fp):
    data = json.load(refile.smart_open(fp, "r"))
    return data


def draw_clouds_on_imgs(images, points, calibrated_sensors, camera_list) -> None:
    pcd_trans = np.concatenate([points[:, :3].copy(), np.ones((len(points), 1))], -1)
    colors = points[:, 3:]
    for cid in range(len(camera_list)):
        lidar2img = np.array(calibrated_sensors[camera_list[cid]]["T_lidar_to_pixel"])
        img_pts = lidar2img @ pcd_trans.T
        cur_img_pts = img_pts[:, img_pts[2] > 0]
        cur_img_pts_2d = cur_img_pts[:2] / cur_img_pts[2]
        colors = colors[img_pts[2] > 0]
        colors = (colors * 255).astype(np.uint8)
        H, W = images[cid].shape[:2]
        for i in range(cur_img_pts_2d.shape[1]):
            x = int(cur_img_pts_2d[0, i])
            y = int(cur_img_pts_2d[1, i])
            if x >= 0 and x < W:
                if y >= 0 and y < H:
                    cv2.circle(images[cid], (x, y), radius=3, color=tuple(map(int, colors[i])), thickness=-1)
    return images


def draw_freespace_points_on_imgs(images, points, calibrated_sensors, camera_list) -> None:
    pcd_trans = np.concatenate([points[:, :3].copy(), np.ones((len(points), 1))], -1)
    colors = points[:, 3:]
    for cid in range(len(camera_list)):
        lidar2img = np.array(calibrated_sensors[camera_list[cid]]["T_lidar_to_pixel"])
        img_pts = lidar2img @ pcd_trans.T
        cur_img_pts = img_pts[:, img_pts[2] > 0]
        cur_img_pts_2d = cur_img_pts[:2] / cur_img_pts[2]
        colors = colors[img_pts[2] > 0]
        colors = (colors * 255).astype(np.uint8)
        H, W = images[cid].shape[:2]
        for i in range(cur_img_pts_2d.shape[1]):
            x = int(cur_img_pts_2d[0, i])
            y = int(cur_img_pts_2d[1, i])
            if x >= 0 and x < W:
                if y >= 0 and y < H:
                    cv2.circle(images[cid], (x, y), radius=3, color=tuple(map(int, colors[i])), thickness=-1)
    return images


def draw_result(
    y_pred_cls,
    mask,
    y_label_cls,
    img=None,
    lidar_points=None,
    calibrated_sensors=None,
    camera_list=[],
    vis_image=None,
    vaild_freespace_points=None,
    post_cfg=None,
):
    if y_label_cls is not None:
        y_label_cls[y_label_cls == 255] = 5

    cmap = mcolors.ListedColormap(
        [
            (0.85, 0.85, 0.85),  # 类别0，灰色
            (1.0, 0.0, 0.0),  # 类别1，蓝色
            (0.0, 1.0, 0.0),  # 类别2，绿色
            (0.0, 0.0, 1.0),  # 类别3，红色
            (0.0, 1.0, 1.0),  # 类别4，黄色
            (0.0, 0.0, 0.0),  # 类别5，黑色
            (1.0, 1.0, 0.0),  # 类别6，
        ]
    )

    norm = mcolors.Normalize(vmin=0, vmax=6)
    points_color = cmap(norm(lidar_points[..., -1]))[..., :3]

    # 定义要查找的值和替换值
    old_value = [1.0, 1.0, 0]
    new_value = [0.0, 0.0, 1.0]
    # 找到满足条件的行
    replace_mask = (
        (points_color[:, 0] == old_value[0])
        & (points_color[:, 1] == old_value[1])
        & (points_color[:, 2] == old_value[2])
    )
    # 替换满足条件的行
    points_color[replace_mask] = new_value
    lidar_points = np.concatenate([lidar_points[..., :-1], points_color], -1)

    img = draw_clouds_on_imgs([img], lidar_points, calibrated_sensors, camera_list)[0]
    width, height = mask.shape

    # 物理分辨率
    delta_x = post_cfg["voxel_size"][0]  # 横向每像素0.1米
    delta_y = post_cfg["voxel_size"][1]  # 纵向每像素0.1米

    # 生成网格
    y, x = np.indices((height, width))  # 张量的像素坐标
    Y = y * delta_y  # 纵向物理坐标
    X = (x - width // 2) * delta_x  # 横向物理坐标
    # Z = np.zeros_like(Y)  # 高度为0
    # 合并为三维坐标
    # coordinates = np.stack([X, Y, Z], axis=-1)
    freespace_lidar_points = []
    contour_points_idxes = vaild_freespace_points

    for i in range(contour_points_idxes.shape[0]):
        freespace_x = X[0, :][int(contour_points_idxes[i, 0])]
        freespace_y = Y[:, 0][int(contour_points_idxes[i, 1])]
        freespace_z = 0
        freespace_lidar_points.append([freespace_x, freespace_y, freespace_z, 1, 1, 0])

    fp_points = np.array(freespace_lidar_points)
    draw_freespace_points_on_imgs([img], fp_points, calibrated_sensors, camera_list)[0]
    freespace_pred = y_pred_cls[:, ::-1].transpose(1, 0)
    # freespace_gt = y_label_cls[:, ::-1].transpose(1, 0)
    mask_i = mask[:, ::-1].transpose(1, 0)
    target_h, target_w = freespace_pred.shape[:2]
    origin_h, origin_w, _ = img.shape
    img = transform.resize(img, (target_h, int(target_h * (origin_w / origin_h)), 3), anti_aliasing=True)

    if y_label_cls is not None:
        freespace_gt = y_label_cls[:, ::-1].transpose(1, 0)
        non_mask_freespace = np.concatenate([freespace_gt, freespace_pred], axis=1)
        mask_freespace = np.concatenate([freespace_gt * mask_i, freespace_pred * mask_i], axis=1)
    else:
        freespace_gt = None
        non_mask_freespace = freespace_pred
        mask_freespace = freespace_pred * mask_i

    # non_mask_freespace = np.concatenate([freespace_gt, freespace_pred], axis=1)
    non_mask_freespace = cmap(norm(non_mask_freespace))[..., :3]

    meter_positions = [20, 40, 60, 80]
    pixel_positions = [int((81.6 - m) * (freespace_pred.shape[0] / 81.6)) for m in meter_positions]

    # 在对应位置画黑线并写上标签
    for pos, meter in zip(pixel_positions, meter_positions):
        # 画横线
        non_mask_freespace[pos, :, :] = 0  # 将该行的像素设置为黑色

    non_mask_freespace[:, width : width * 2] = vis_image / 255
    non_mask_freespace[:, width : width * 2] = np.flipud(non_mask_freespace[:, width : width * 2])

    non_mask_freespace = np.concatenate(
        [
            img,
            non_mask_freespace,
        ],
        axis=1,
    )

    # 可视化分割图
    # mask_freespace = np.concatenate([freespace_gt * mask_i, freespace_pred * mask_i], axis=1)
    mask_freespace = cmap(norm(mask_freespace))[..., :3]
    mask_freespace = np.concatenate(
        [
            img,
            mask_freespace,
        ],
        axis=1,
    )
    non_mask_freespace[
        :,
    ]

    non_mask_freespace = transform.resize(
        non_mask_freespace, (non_mask_freespace.shape[0] * 2, non_mask_freespace.shape[1] * 2, 3), anti_aliasing=True
    )
    mask_freespace = transform.resize(
        mask_freespace, (mask_freespace.shape[0] * 2, mask_freespace.shape[1] * 2, 3), anti_aliasing=True
    )
    return non_mask_freespace, mask_freespace


def _get_cameras_undistort_map(one_scene_label: Dict, dims: Tuple, camera_list: List) -> List:
    cameras_undistort_map = {}
    for camera_id in camera_list:
        camera_calib = one_scene_label["calibrated_sensors"][camera_id]
        intrinsic_K = np.array(camera_calib["intrinsic"]["K"]).reshape(3, 3)
        intrinsic_D = np.array(camera_calib["intrinsic"]["D"])
        assert camera_calib["intrinsic"]["distortion_model"] in [
            "fisheye",
            "pinhole",
        ], "distortion model {} not supported".format(camera_calib["intrinsic"]["distortion_model"])
        if camera_calib["intrinsic"]["distortion_model"] == "fisheye":
            map1, map2 = cv2.fisheye.initUndistortRectifyMap(
                intrinsic_K, intrinsic_D, np.eye(3), intrinsic_K, dims[camera_id], cv2.CV_16SC2
            )
        else:
            map1, map2 = cv2.initUndistortRectifyMap(
                intrinsic_K, intrinsic_D, np.eye(3), intrinsic_K, dims[camera_id], cv2.CV_16SC2
            )
        cameras_undistort_map[camera_id] = (map1, map2)
    return cameras_undistort_map


def get_image(sensor_data, cam_key, distort_map):
    if nori_available:
        nori_fetcher = nori.Fetcher()
        nori_img_id = sensor_data[cam_key]["nori_id"]
        img = io.BytesIO(nori_fetcher.get(nori_img_id))
        img = skimage_io.imread(img)
    else:
        nori_img_id = sensor_data[cam_key]["nori_id"]
        vid = int(nori_img_id.split(",")[0])
        nori_path = sensor_data[cam_key]["nori_path"]  # .replace("s3://", "/mnt/")
        vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
        img = cv2.imdecode(np.frombuffer(vreader.get(nori_img_id), dtype=np.uint8), 1)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    # img = skimage_io.imread(img)
    img = cv2.resize(
        img,
        (
            3840,
            2160,
        ),
    )
    img = cv2.remap(img, *distort_map[cam_key], cv2.INTER_LINEAR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    return img


def get_points(
    sensor_data,
    pc_fields=["x", "y", "z", "lidar_id"],
    used_echo_id=[1],
    roi_range=[-32.0, -80.8, -5.0, 32.0, 120.8, 3.0],
    lidar_id=1,
):
    # 读点云

    if "rfu_front_2_lidar" in sensor_data:
        nori_id = sensor_data["rfu_front_2_lidar"]["nori_id"]
        nori_path = sensor_data["rfu_front_2_lidar"]["nori_path"]
    else:
        nori_id = sensor_data["fuser_lidar"]["nori_id"]
        nori_path = sensor_data["fuser_lidar"]["nori_path"]
    if nori_available:
        fetcher = nori.Fetcher()
        data = fetcher.get(nori_id)
    else:
        vid = int(nori_id.split(",")[0])
        vreader = nori.nori_reader.VolumesReader(nori_path, [vid], "meta.{0:08x}".format(vid), 2)
        data = vreader.get(nori_id)
    pc_data_raw = np.load(io.BytesIO(data)).copy()
    pc_data = rfn.structured_to_unstructured(pc_data_raw[np.array(pc_fields)])
    pc_data = pc_data[np.isin(pc_data_raw["echo_id"], used_echo_id)]
    pc_data = pc_data[pc_data[:, -1] == lidar_id][:, :-1]
    # 根据 ROI 范围过滤点云
    xmin, ymin, zmin, xmax, ymax, zmax = roi_range
    mask = (
        (pc_data[:, 0] >= xmin)
        & (pc_data[:, 0] <= xmax)
        & (pc_data[:, 1] >= ymin)
        & (pc_data[:, 1] <= ymax)
        & (pc_data[:, 2] >= zmin)
        & (pc_data[:, 2] <= zmax)
    )
    # 返回过滤后的点云
    pc_data = pc_data[mask][..., :3]
    return pc_data


def get_points_mcap(
    sensor_data,
    pc_fields=["x", "y", "z", "lidar_id"],
    used_echo_id=[1],
    roi_range=[-32.0, -80.8, -5.0, 32.0, 120.8, 3.0],
    lidar_id=1,
):
    # 读点云

    s3_path = sensor_data["front_2_lidar"]["s3_path"]
    pc_data = load_pkl(s3_path)
    # pc_data_raw = np.load(io.BytesIO(data)).copy()
    pc_data = rfn.structured_to_unstructured(pc_data[np.array(["x", "y", "z"])])
    # pc_data = pc_data[np.isin(pc_data_raw["echo_id"], used_echo_id)]
    # pc_data = pc_data[pc_data[:, -1] == lidar_id][:, :-1]
    # 根据 ROI 范围过滤点云
    xmin, ymin, zmin, xmax, ymax, zmax = roi_range
    mask = (
        (pc_data[:, 0] >= xmin)
        & (pc_data[:, 0] <= xmax)
        & (pc_data[:, 1] >= ymin)
        & (pc_data[:, 1] <= ymax)
        & (pc_data[:, 2] >= zmin)
        & (pc_data[:, 2] <= zmax)
    )

    # 返回过滤后的点云
    pc_data = pc_data[mask][..., :3]
    return pc_data


def get_image_mcap(sensor_data, cam_key, distort_map):
    img = refile.smart_load_image(sensor_data[cam_key]["s3_path"])
    img = cv2.resize(
        img,
        (
            3840,
            2160,
        ),
    )
    img = cv2.remap(img, *distort_map[cam_key], cv2.INTER_LINEAR)
    return img


def get_points_label(lidar_points, y_pred_cls, roi_range):
    # BEV map 的范围
    xmin, ymin, zmin, xmax, ymax, zmax = roi_range
    bev_map_width, bev_map_height = y_pred_cls.shape  # BEV map 的尺寸，152x408
    # LiDAR 点的 x 和 y 坐标
    x_lidar = lidar_points[:, 0]
    y_lidar = lidar_points[:, 1]
    # 将 LiDAR 点的坐标映射到 BEV map 上的索引
    row_indices = np.clip(((x_lidar - xmin) / (xmax - xmin) * bev_map_width).astype(int), 0, bev_map_width - 1)
    col_indices = np.clip((y_lidar / ymax * bev_map_height).astype(int), 0, bev_map_height - 1)

    # 从 BEV map 中提取对应的语义标签
    semantic_labels = y_pred_cls[row_indices, col_indices]

    # 打印每个点的语义标签
    return np.concatenate([lidar_points, semantic_labels[:, None]], -1)


def update_output_videos(images: Dict[str, np.array], save_dir: str = "", output_videos=None) -> None:
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    for name, img in images.items():
        save_path = os.path.join(save_dir, name + ".mp4")
        os.makedirs(Path(save_path).parents[0], exist_ok=True)
        if output_videos[name] is None:
            output_videos[name] = cv2.VideoWriter(save_path, fourcc, 10, img.shape[0:2][::-1])
        output_videos[name].write((img * 255).astype(np.uint8))
    return output_videos


def video_convert(work_dir: str, keep_orig: bool = False) -> None:
    keep_orig = False
    output_root = refile.smart_path_join(work_dir)
    for i in refile.SmartPath(output_root).scan():
        filename = i.split("/")[-1]
        if len(filename) > 4 and filename.split(".")[-1] == "mp4":
            command = (
                "/usr/bin/ffmpeg -i " + i + " -pix_fmt yuv420p -vcodec libx264 " + i[:-4] + "_x264.mp4 -hide_banner"
            )
            os.system(command)
            if not keep_orig:
                os.system("rm " + i)


def release_video(output_videos):
    for k, v in output_videos.items():
        output_videos[k] = None
    return output_videos


def muti_process(data, output_dir, camera_list, dims, roi_range, lidar_id, post_cfg=None):
    loaded_json_data = None
    distort_map = None
    json_path = None
    postprocessor = FreespacePostProcessor(post_cfg)

    for idx, data_ in enumerate(tqdm(data)):

        if json_path != data_["json_path"]:
            json_path = data_["json_path"]
            loaded_json_data = None
            distort_map = None
            video_name = ("car_" + json_path.split("/car_")[1][:-5]).replace("/", "%")
            video_dir = refile.smart_path_join(output_dir, video_name)
            output_videos = {
                "non_mask_freespace": None,
                "mask_freespace": None,
            }
        if loaded_json_data is None:
            loaded_json_data = load_json(data_["json_path"])
            distort_map = _get_cameras_undistort_map(loaded_json_data, dims, camera_list)
        if os.environ.get("MCAP", False):
            img = get_image_mcap(data_["sensor_data"], "cam_front_120", distort_map)
        else:
            img = get_image(data_["sensor_data"], "cam_front_120", distort_map)
        try:
            if os.environ.get("MCAP", False):
                points = get_points_mcap(data_["sensor_data"], roi_range=roi_range, lidar_id=lidar_id)
            else:
                points = get_points(data_["sensor_data"], roi_range=roi_range, lidar_id=lidar_id)
        except Exception:
            points = np.array([[1.0, 1.0, 1.0]])
            continue

        all_contour_points, vis_image, vaild_freespace_points = postprocessor.decode(
            copy.deepcopy(data_["perception_result"].transpose(1, 0, 2).reshape(-1)), {}
        )  # 720

        contour_points_idxes = vaild_freespace_points
        rows, cols = contour_points_idxes[:, 0], contour_points_idxes[:, 1]
        rows = rows.astype(int)
        cols = cols.astype(int)
        # 批量赋值
        data_["perception_result"][rows, cols, 0] = 6 + 1
        point_with_labels = get_points_label(points, data_["perception_result"][:, :, 0], roi_range)
        calibrated_sensors = loaded_json_data["calibrated_sensors"]

        W, H, _ = data_["perception_result"].shape
        if "semantic_mask" not in data_:
            data_["semantic_mask"] = np.ones((W, H), dtype=np.int64)
        if "semantic" not in data_:
            data_["semantic"] = np.ones((W, H), dtype=np.int64)

        non_mask_freespace, mask_freespace = draw_result(
            data_["perception_result"][:, :, 0],
            data_["semantic_mask"],
            data_["semantic"],
            img,
            point_with_labels,
            calibrated_sensors,
            camera_list,
            vis_image,
            vaild_freespace_points,
            post_cfg,
        )
        output_videos = update_output_videos(
            {"non_mask_freespace": non_mask_freespace, "mask_freespace": mask_freespace},
            save_dir=video_dir,
            output_videos=output_videos,
        )
        if idx + 1 == len(data) or data[idx + 1]["json_path"] != json_path:
            output_videos = release_video(output_videos)
            video_convert(video_dir)
        # if idx>10:
        #     output_videos = release_video(output_videos)
        #     video_convert(video_dir)
        #     break
    return 1


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--task_name", default="occ_freespace_visual")
    # parser.add_argument(
    #     "--nr-runner", type=int, default=64, help="number of rrun executors"
    # )
    parser.add_argument("--save_name", type=str, help="", default="post")
    parser.add_argument(
        "--charged-group",
        type=str,
        default="galvatron",
    )
    parser.add_argument("--file", type=str, default="galvatron", help="path of occ_results.pkl")
    parser.add_argument("--nr-runner", type=int, default=64, help="number of rrun executors")
    args = parser.parse_args()

    file = args.file

    if os.environ.get("DEBUG", False):
        output_dir = refile.smart_path_join(os.path.dirname(file), "visualization_debug")
    else:
        name = file.split("/")[-1][:-4]
        output_dir = refile.smart_path_join(os.path.dirname(file), f"visualization_0218_7_{name}", args.save_name)

    # output_dir = output_dir.replace('/data/tmp/', '/data/Envs/megvii/demo/202500216_v28_16/')
    # print('output_dir', output_dir)

    data = load_pkl(file)
    camera_list = ["cam_front_120"]
    lidar_id = 4
    dims = {"cam_front_120": [1920 * 2, 1080 * 2]}
    roi_range = [-15.2, 0, -5.0, 15.2, 81.6, 3.0]  # 对应freespace的距离  0-81.6 15.2
    voxel_size_ = 0.1
    scale = 0.2 // voxel_size_
    voxel_size = [voxel_size_, voxel_size_, voxel_size_]

    post_cfg = {
        "upsample_ratio": 1,
        "pc_range": [-15.2, 0],
        "voxel_size": (voxel_size_, voxel_size_),
        "azim_step": 0.5,
        "static_eps": 1,
        "static_min_samples": 3,
        "STATIC": 3 + 1,
        "feat_size_width": int(152 * scale),  # 304
        "feat_size": int(408 * 152 * scale * scale),  # 816，  816*304
        "num_bins": 20000,
        "cmap": mcolors.ListedColormap(
            [
                (0.85, 0.85, 0.85),  # 类别0，灰色
                (1.0, 0.0, 0.0),  # 类别1，蓝色
                (0.0, 1.0, 0.0),  # 类别2，绿色
                (0.0, 0.0, 1.0),  # 类别3，红色
                (0.0, 1.0, 1.0),  # 类别4，黄色
                (0.0, 0.0, 0.0),  # 类别5，黑色
                (1.0, 1.0, 0.0),  # 类别6，
            ]
        ),
    }

    metas = []
    for json_path, group in groupby(data, key=lambda x: x["json_path"]):
        metas.append(list(group))

    if os.environ.get("DEBUG", False):
        muti_process(metas[0], output_dir, camera_list, dims, roi_range, lidar_id, post_cfg)
        exit()
    logger.info("get {} collections".format(len(metas)))

    all_volume = set()

    spec = spec_config(args.task_name, args.charged_group)
    success, nr_finish, nr_total = True, 0, len(metas)
    executor = rrun.RRunExecutor(spec, min(nr_total, args.nr_runner), 1)

    futures = [
        executor.submit(muti_process, metas[idx], output_dir, camera_list, dims, roi_range, lidar_id, post_cfg)
        for idx in range(len(metas))
    ]
    success_list = set()

    all_task_list = set([idx for idx in range(len(metas))])

    cts = 0
    for future in concurrent.futures.as_completed(futures):
        try:
            ct = future.result()
            # all_volume=all_volume.union(sub_volume)
            # success_list.add(idx)
            cts += ct
            nr_finish += 1
            logger.info("[{}/{}] {} ".format(nr_finish, nr_total, cts))

        except Exception as e:
            logger.error(e)
            success = False

    executor.shutdown(wait=False)
    # loaded_json_data = None
    # distort_map = None
    # json_path = None
    # for idx,data_ in tqdm(enumerate(data)):
    #     if json_path!=data_['json_path']:
    #         json_path = data_['json_path']
    #         loaded_json_data = None
    #         distort_map = None
    #         video_name = ('car_'+json_path.split('/car_')[1][:-5]).replace('/','%')
    #         video_dir = refile.smart_path_join(output_dir,video_name)
    #         output_videos = {'non_mask_freespace':None,'mask_freespace':None,}
    #     if loaded_json_data is None:
    #         loaded_json_data = load_json(data_['json_path'])
    #         distort_map = _get_cameras_undistort_map(loaded_json_data,dims,camera_list)

    #     img = get_image(data_['sensor_data'],'cam_front_120',distort_map)
    #     points = get_points(data_['sensor_data'],roi_range=roi_range)
    #     point_with_labels = get_points_label(points,data_['perception_result'][:,:,0],roi_range)
    #     calibrated_sensors = loaded_json_data['calibrated_sensors']
    #         # img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    #     non_mask_freespace, mask_freespace = draw_result(data_['perception_result'][:,:,0],data_['semantic_mask'],data_['semantic'],img,point_with_labels,calibrated_sensors,camera_list)
    #     output_videos = update_output_videos({'non_mask_freespace':non_mask_freespace, 'mask_freespace':mask_freespace},save_dir=video_dir,output_videos=output_videos)

    #     if idx+1 == len(data) or data[idx+1]['json_path'] != json_path:

    #         output_videos = release_video(output_videos)
    #         video_convert(video_dir)
