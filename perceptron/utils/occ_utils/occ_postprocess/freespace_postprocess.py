import numpy as np
import cv2
from sklearn.cluster import DBSCAN


class FreespacePostProcessor:
    def __init__(self, cfg):
        self.cfg = cfg
        self.feat_size_width = cfg["feat_size_width"]
        self.feat_size_height = cfg["feat_size"] // cfg["feat_size_width"]

        # 生成精确的坐标转换表
        self.x_grid = np.linspace(
            cfg["pc_range"][0] + cfg["voxel_size"][0] / 2,
            cfg["pc_range"][0] + cfg["voxel_size"][0] * (self.feat_size_width - 0.5),
            self.feat_size_width,
        )
        self.y_grid = np.linspace(
            cfg["pc_range"][1] + cfg["voxel_size"][1] / 2,
            cfg["pc_range"][1] + cfg["voxel_size"][1] * (self.feat_size_height - 0.5),
            self.feat_size_height,
        )
        self.num_bins = cfg["num_bins"]
        self.cmap = cfg["cmap"]

    def decode(self, mask_seg_tensor, freespace):
        # 构建freespace二值掩码
        ori_mask_seg_offline = mask_seg_tensor.reshape(
            self.feat_size_height, self.feat_size_width
        ).copy()  # BEV 304*816

        mask_seg_offline = mask_seg_tensor.reshape(self.feat_size_height, self.feat_size_width)
        mask_seg_offline[mask_seg_offline == 2] = 1  # As Freespace 1 # freespace 1， 动态 2， 静态 3 unknow 0 ， 类别定义

        # Freespace膨胀，小空洞去除
        mask_seg_offline = self.dilate_occ(mask_seg_offline, class_idx=1, kernel_size=3)  # Freespace膨胀  ，利羣空洞去除, +1,

        # 创建颜色映射数组
        color_map = (np.array(self.cmap.colors) * 255).astype(np.uint8)
        height, width = mask_seg_offline.shape

        fs = mask_seg_offline.copy()

        fs[fs == 2] = 1  # 动态障碍物忽略
        fs = self.get_edge_points((fs == 1))

        fs[fs == 1] = ori_mask_seg_offline[fs == 1]  # 获取静态障碍物与freespace的交界，为了获取静态障碍物的内边界, 障碍物距离freespace 距离最近的边

        p_xy = np.where(fs > 0)
        pf = np.stack([p_xy[1], p_xy[0]], axis=1)

        clustering = DBSCAN(eps=self.cfg["static_eps"], min_samples=self.cfg["static_min_samples"]).fit(pf)
        static_clusters = [[] for _ in range(max(clustering.labels_) + 1)]
        for point, label in zip(pf, clustering.labels_):
            if label != -1:  # Exclude noise points
                static_clusters[label].append(point)

        if len(static_clusters) > 0:
            merged_contour = np.concatenate(static_clusters)
            num, dim = merged_contour.shape
            merged_contour = merged_contour.reshape([num, dim])
            vaild_freespace_points = merged_contour
        else:
            static_clusters[0].append((np.inf, np.inf))
            vaild_freespace_points = np.array([np.inf, np.inf])

        ##############################################################################################################
        color_image = np.zeros((height, width, 3), dtype=np.uint8)
        for i in range(height):
            for j in range(width):
                class_id = ori_mask_seg_offline[i, j]
                color_image[i, j] = color_map[class_id]

        for point in vaild_freespace_points:
            x, y = int(point[0]), int(point[1])
            cv2.circle(color_image, (x, y), 1, (255, 255, 0), -1)
        vis_image = color_image
        ###############################################################################################################

        freespace = {}
        freespace["static_contour_points"] = []
        for i in range(len(static_clusters)):
            contour_point = np.array(static_clusters[i])
            tmp_info = {}
            if np.all(contour_point == np.inf):
                tmp_info["class_type"] = -1  # 没有任何静态障碍物
                tmp_info["contour_point"] = contour_point  # 无效点
                tmp_info["instance_id"] = -1
                freespace["static_contour_points"].append(tmp_info)
            else:
                tmp_info["class_type"] = 1  # static
                tmp_info["contour_point"] = contour_point  # 静态轮廓点集 Freespace array
                tmp_info["instance_id"] = i  # 实例序号
                freespace["static_contour_points"].append(tmp_info)

        return freespace, vis_image, vaild_freespace_points  # BEV 視角下 freespace point

    def dilate_occ(self, bev_grid, class_idx=None, kernel_size=None):
        occ_grids = bev_grid == class_idx
        occ_grids = cv2.dilate(occ_grids.astype(np.uint8), np.ones((kernel_size, kernel_size), np.uint8), iterations=1)
        return occ_grids

    def get_edge_points(self, img):
        """
        使用 OpenCV 获取二值分割结果的边缘点
        """
        dim = len(img.shape)
        # 根据图像维度生成结构元素
        if dim == 2:
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        else:
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3, 3))
        # 对图像进行腐蚀操作
        if img.dtype != np.uint8:
            img = (img * 1).astype(np.uint8)
        ero = cv2.erode(img, kernel)
        # 计算边缘点
        edge = np.asarray(img, np.uint8) - np.asarray(ero, np.uint8)
        return edge
