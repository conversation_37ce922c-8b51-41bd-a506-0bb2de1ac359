import os
import cv2
import refile
import torch
import math
import pickle
from tabulate import tabulate
import numpy as np
from itertools import product
from scipy.optimize import linear_sum_assignment
from scipy.interpolate import splprep, splev
from shapely.geometry import LineString, Point, Polygon
from shapely.ops import unary_union
from perceptron.utils.map_utils.evaltools.metric.chamfer_distance import ChamferDistance as CD_func
from perceptron.utils.map_utils.vistools.map_vis import Visual_Map_Data


class MapEvaluator:
    def __init__(
        self,
        loader_output=None,
        annotation=None,
        map_range=[-15.0, 0.0, -6.0, 15.0, 100, 6],
        eval_range=[
            -15.0,
            0.0,
            -6.0,
            15.0,
            100,
            6,
        ],  # lidar [xmin, ymin, zmin, xmax, ymax, zmax]; map_ego_range 与 map_lidar_range需要联动更改
        core_range=[-6, 5, -2, 6, 50, 2],  # 评测核心关注区域
        subdivision_range=[0, 30, 60, 100],  # 评测细分区间0-30、30-60、60-100
        line_eval_cfg=dict(
            inter_dis=0.1,  # cd 需要较小的inter_dis, pl 能使用更大的inter_dis 1m；
            threash_dict=dict(
                fp_cd_thresh_list=[0.3, 0.6, 0.9, 1.2], inf_th_dis=20  # dt 作为 fp 距离判定阈值， 单位 voxel / m；
            ),  # 无效 inf_th
            metric_conf=dict(keep_ratio=0.8, mode="d<->g"),
            decay_dis=dict(x=60, y=600, z=12),  # 评测完全衰减距离；
        ),
        box_eval_cfg=dict(
            iou_th=[0.5, 0.3, 0.1],
            inf_iou=0.0,
        ),
        map_class_dict=None,  # 进行评测道路元素
        arrow_name_id_map=None,  # 箭头 name-id
        arrow_id_name_map=None,  # 箭头 id-name
        with_attr=False,  # 评测过程是否考虑属性；
        with_cd=False,  # 评测 Line 使用ChamferDistanceMetric
        with_vd=False,  # 评测 Line 使用VoxelDistanceMetric (todo)
        with_pl=False,  # 评测 Line 使用PointLineDistanceMetric
        with_iou=False,  # 评测 Box 使用 IOU Metric
        with_kl_d=False,  # 评测 Box 使用 KL Metric
        with_wt_d=False,  # 评测 Box 使用 Wasserstein Metric
        with_vis=True,
        warp_tgt_json=None,
        tgt_resolution=[1920, 1080],  # 只有在需要使用 warp 操作时用到，这里需要和模型设置一致
        onestage_combine_maps=None,  # 包含了 warp+undistort
        val_output_dir=None,  # 输出文件夹名称
    ) -> None:
        self.map_range = map_range
        self.eval_range = eval_range
        self.core_range = core_range
        self.subdivision_range = subdivision_range
        self.subdivision_num = len(self.subdivision_range) - 1
        self.inter_dis = line_eval_cfg["inter_dis"]
        self.threash_dict = line_eval_cfg["threash_dict"]
        self.metric_conf = line_eval_cfg["metric_conf"]
        self.decay_dis = line_eval_cfg["decay_dis"]
        self.box_eval_cfg = box_eval_cfg
        self.map_class_dict = map_class_dict
        self.arrow_name_id_map = arrow_name_id_map  # 箭头 name-id
        self.arrow_id_name_map = arrow_id_name_map  # 箭头 id-name

        self.with_attr = with_attr
        self.with_cd = with_cd
        self.with_vd = with_vd
        self.with_pl = with_pl

        self.with_iou = with_iou
        self.with_kl_d = with_kl_d
        self.with_wt_d = with_wt_d

        self.with_vis = with_vis

        assert map_class_dict is not None, "pls check map_class_dict"

        self.init_eval_result()

        if self.with_cd:
            self.cd_class = ChamferDistanceMetric(metric_conf=self.metric_conf)
        if self.with_pl:
            self.pl_class = PointLineDistanceMetric(
                metric_conf=self.metric_conf,
                decay_dis=self.decay_dis,
                core_range=self.core_range,
                subdivision_range=self.subdivision_range,
            )

        self.box_class = BoxDistanceMetric(
            decay_dis=self.decay_dis, core_range=self.core_range, subdivision_range=self.subdivision_range
        )

        self.map_class_dict_id = {}
        for k, v in map_class_dict.items():
            self.map_class_dict_id[v["id"]] = v

        self.sub_range_str = []
        for range_idx in range(self.subdivision_num + 1):
            if range_idx == 0:
                eval_sub_range = f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}"
            else:
                eval_sub_range = f"{self.subdivision_range[range_idx-1]}-{self.subdivision_range[range_idx]}"

            self.sub_range_str.append(eval_sub_range)

        self.master_range_th_line = (
            f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}_{self.threash_dict['fp_cd_thresh_list'][0]}"
        )
        self.master_range_th_box = (
            f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}_{self.box_eval_cfg['iou_th'][0]}"
        )

        self.warp_tgt_json = warp_tgt_json
        self.tgt_resolution = tgt_resolution
        self.onestage_combine_maps = onestage_combine_maps

        self.vis_tool = self.init_vis_tool()

        # add vis_dir
        self.val_output_dir = val_output_dir

    def init_vis_tool(self):
        return Visual_Map_Data(
            map_class_dict_id=self.map_class_dict_id,  # 大类 id-info
            arrow_name_id_map=self.arrow_name_id_map,  # 箭头 name-id
            arrow_id_name_map=self.arrow_id_name_map,  # 箭头 id-name
            warp_tgt_json=self.warp_tgt_json,
            tgt_resolution=self.tgt_resolution,
            onestage_combine_maps=self.onestage_combine_maps,
        )

    def __call__(self, step, gt_item, dt_item, loss_dict=None, output_dir=None, data_info=None):
        bs = len(dt_item)
        result_dict_list = []
        for b_idx in range(bs):
            gt_dict, mask_dict = self.parse_gt(gt_item, b_idx)
            dt_dict, dt_scores = self.parse_dt(dt_item[b_idx], mask_dict)

            # TODO：无效区域mask中的dt不参与评估
            result_dict = {}
            for cls, cls_info in self.map_class_dict.items():
                if cls_info["id"] not in gt_dict or cls_info["id"] not in dt_dict:
                    continue

                dt_cls_one = dt_dict[cls_info["id"]]
                gt_cls_one = gt_dict[cls_info["id"]]
                # if len(dt_cls_one) <= 0 or len(gt_cls_one) <= 0:continue
                if cls in ["laneline", "curb", "stopline", "entrance"]:
                    result_dict[cls] = self.eval_line(cls_info["id"], dt_cls_one, gt_cls_one, dt_scores)
                elif cls in ["crosswalk", "arrow", "noparking"]:
                    result_dict[cls] = self.eval_box(cls_info["id"], dt_cls_one, gt_cls_one, dt_scores)
                else:
                    print(f"Find unknow cls {cls}")
            self.add_result(result_dict)
            result_dict_list.append(result_dict)
        # vis map_gt and map_dt
        if self.with_vis:
            # save_path = refile.smart_path_join(output_dir, "./pl_vis/") # TODO 后期存储在output路径下
            save_path = refile.smart_path_join(output_dir, self.val_output_dir)  # fix 0318, adaptive dir
            if not os.path.exists(save_path):
                os.makedirs(save_path, exist_ok=True)

            (
                imgs_rv_list,
                imgs_dt_list,
                imgs_seg_list,
                imgs_bev_list,
                debug_dts,
                final_bev_dt_list,
            ) = self.vis_tool.vis_data(gt_item, dt_item, do_undistort=True)
            bs = len(imgs_rv_list)
            vis_cam_id = [0, 1, 2, 3]
            color_info = (100, 255, 255)

            font = cv2.FONT_HERSHEY_SIMPLEX
            fontScale = 1
            thickness = 1
            for b_id in range(bs):
                cam_id = 0
                h, w = 100, 100
                token = dt_item[b_id]["token"]  # eg. 'scene_0_frame_id_0'  发现这版对应值有问题，需要修正
                for cls, res_cls_dict in result_dict_list[b_id].items():  # TODO: 后续支持subrange dt result vis
                    if cls in ["laneline", "curb", "stopline", "entrance"]:
                        res_cls = res_cls_dict[self.master_range_th_line]
                    elif cls in ["crosswalk", "arrow", "noparking"]:
                        res_cls = res_cls_dict[self.master_range_th_box]
                    for dt_i, info in enumerate(res_cls["dt_status"]):
                        info[0], info[1], info[3] = int(info[0]), round(float(info[1]), 3), round(float(info[3]), 3)
                        dt_res_str = f"{cls} {dt_i} {str(info)}"
                        cv2.putText(
                            imgs_dt_list[b_id][cam_id],
                            dt_res_str,
                            (w, h),
                            font,
                            fontScale,
                            color_info,
                            thickness,
                            cv2.LINE_AA,
                        )
                        h += 60
                vis_idx = gt_item["index_in_dataset"][b_id][-1]  # TODO: vis_idx 字段也需要加到 dt_item 里，用来可视化没有 gt 的结果
                if loss_dict and "arrow_attr_loss" in loss_dict and "line_attr_loss" in loss_dict:
                    arrow_attr_loss = loss_dict["arrow_attr_loss"].cpu().item()  # TODO 目前仅支持all batch size loss
                    cv2.putText(
                        imgs_dt_list[b_id][cam_id],
                        f"arrow attr loss: {arrow_attr_loss}",
                        (w, h),
                        font,
                        fontScale,
                        color_info,
                        thickness,
                        cv2.LINE_AA,
                    )
                    h += 60
                    line_attr_loss = loss_dict["line_attr_loss"].cpu().item()  # TODO 目前仅支持all batch size loss
                    cv2.putText(
                        imgs_dt_list[b_id][cam_id],
                        f"line attr loss: {line_attr_loss}",
                        (w, h),
                        font,
                        fontScale,
                        color_info,
                        thickness,
                        cv2.LINE_AA,
                    )

                for cam_id in vis_cam_id:
                    vis_idx = gt_item["index_in_dataset"][b_id][-1]  # TODO: vis_idx 字段也需要加到 dt_item 里，用来可视化没有 gt 的结果
                    cv2.imwrite(
                        refile.smart_path_join(save_path, f"{vis_idx}_{token}_{step}_{b_id}_{cam_id}_rv_gt.jpg"),
                        imgs_rv_list[b_id][cam_id],
                    )
                    cv2.imwrite(
                        refile.smart_path_join(save_path, f"{vis_idx}_{token}_{step}_{b_id}_{cam_id}_rv_dt.jpg"),
                        imgs_dt_list[b_id][cam_id],
                    )
                cv2.imwrite(
                    refile.smart_path_join(save_path, f"{vis_idx}_{token}_{step}_{b_id}_bev_gt.jpg"),
                    imgs_bev_list[b_id],
                )
                cv2.imwrite(
                    refile.smart_path_join(save_path, f"{vis_idx}_{token}_{step}_{b_id}_bev_dt.jpg"),
                    final_bev_dt_list[b_id],
                )

        return result_dict_list

    def save_dt_data(self, output_dir):
        self.vis_tool.save_dt_data(output_dir)

    def eval_line(self, cls, item_dt, item_gt, dt_scores):
        dist_mat = self.distance_line_mat(item_dt, item_gt)
        eval_res = self.calculate_results_by_dist_mat(cls, item_dt, item_gt, dist_mat, dt_scores)
        return eval_res

    def eval_box(self, cls, item_dt, item_gt, dt_scores):
        """
        box dt-gt eval metric: IOU, Kullback-Leibler 散度, Wasserstein 距离,
        """
        dist_mat = self.distance_box_mat(item_dt, item_gt)
        eval_res = self.calculate_results_by_dist_mat_for_box(
            cls, item_dt, item_gt, dist_mat, dt_scores
        )  # TODO box metric 需要修改
        return eval_res

    def distance_line_mat(self, item_dt, item_gt):
        if len(item_dt) == 0 or len(item_gt) == 0:
            return None

        if self.with_cd:
            dist_mat = self.cd_class(item_dt, item_gt)
        elif self.with_pl:
            dist_mat = self.pl_class(item_dt, item_gt)

        return dist_mat

    def distance_box_mat(self, item_dt, item_gt):
        if len(item_dt) == 0 or len(item_gt) == 0:
            return None

        if self.with_iou:
            dist_mat = self.box_class(
                item_dt, item_gt, with_iou=self.with_iou, with_kl_d=self.with_kl_d, with_wt_d=self.with_wt_d
            )

        return dist_mat

    def calculate_results_by_dist_mat(self, cls, dt_lanes, gt_lanes, dist_mat, dt_scores=None):
        dt_status_dict, gt_status_dict = self.calculate_results_by_hungary(cls, dt_lanes, gt_lanes, dist_mat, dt_scores)
        return self._get_res_dict(dt_status_dict, gt_status_dict)

    def calculate_results_by_hungary(self, cls, dt_lanes, gt_lanes, dist_mat, dt_scores):
        inf_dis = self.threash_dict["inf_th_dis"]
        dt_status_dict, gt_status_dict = {}, {}
        for range_idx in range(self.subdivision_num + 1):
            if range_idx == 0:
                eval_sub_range = f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}"
            else:
                eval_sub_range = f"{self.subdivision_range[range_idx-1]}-{self.subdivision_range[range_idx]}"

            for th in self.threash_dict["fp_cd_thresh_list"]:
                dt_status = [[-1, inf_dis, "FP", dt_scores[cls][idx], 0] for idx in range(len(dt_lanes))]
                gt_status = [[-1, inf_dis, "FN", 0, 0] for idx in range(len(gt_lanes))]
                # dist_mat[-1, np.logical_or(dist_mat[0] >= self.thresh_conf["xy"], dist_mat[1] >= self.thresh_conf["xz"])] = 1e3
                if dist_mat is not None:
                    dist_mat_save = dist_mat.copy()
                    dist_mat[dist_mat >= inf_dis] = inf_dis
                    # -------for inf----------
                    dist_mat = torch.minimum(
                        torch.tensor(dist_mat).clone().detach(), torch.tensor(1e3).clone().detach()
                    ).numpy()
                    dist_mat = torch.maximum(
                        torch.tensor(dist_mat).clone().detach(), torch.tensor(-1e3).clone().detach()
                    ).numpy()
                    # ------------------------
                    dist_mat = dist_mat.astype(np.float16)
                    match_index_list = linear_sum_assignment(dist_mat[0])  # 每一个dt和gt都找到一个最优匹配

                    for dt_ind, gt_ind in zip(*match_index_list):
                        dist_xyz = min(dist_mat[range_idx, dt_ind, gt_ind], inf_dis)
                        dist_xyz_save = dist_mat_save[range_idx, dt_ind, gt_ind]
                        if dist_xyz <= th:
                            dt_status[dt_ind] = [gt_ind, dist_xyz, "TP", dt_scores[cls][dt_ind], dt_status[dt_ind][-1]]
                            gt_status[gt_ind] = [dt_ind, dist_xyz, "TP", dt_scores[cls][dt_ind], gt_status[gt_ind][-1]]
                        else:
                            status_ok = dist_xyz <= th
                            dt_status[dt_ind] = [
                                gt_ind if status_ok else -1,
                                dist_xyz_save,
                                "FP",
                                dt_scores[cls][dt_ind],
                                dt_status[dt_ind][-1],
                            ]
                            gt_status[gt_ind] = [
                                dt_ind if status_ok else -1,
                                dist_xyz,
                                "FN",
                                dt_scores[cls][dt_ind],
                                gt_status[gt_ind][-1],
                            ]

                    res_info_key = f"{eval_sub_range}_{th}"
                    dt_status_dict[res_info_key] = dt_status
                    gt_status_dict[res_info_key] = gt_status
                else:
                    res_info_key = f"{eval_sub_range}_{th}"
                    dt_status_dict[res_info_key] = dt_status
                    gt_status_dict[res_info_key] = gt_status
        return dt_status_dict, gt_status_dict

    def _get_res(self, dt_status, gt_status, dist_mat):
        num_dt, num_gt = len(dt_status), len(gt_status)
        num_tp, num_fp, num_fn, dist_tp, dist_fp, dist_fn = 0, 0, 0, 0, 0, 0
        for idx, status in enumerate(dt_status):
            num_tp += 1 if status[2] == "TP" else 0
            dist_tp += status[1] if status[2] == "TP" else 0
            # dist_tp += status[1][0] if status[2] == "TP" else 0
            num_fp += 1 if status[2] == "FP" else 0
            dist_fp += status[1] if status[2] == "FP" else 0
            # dist_fp += status[1][0] if status[2] == "FP" else 0
        for idx, status in enumerate(gt_status):
            num_fn += 1 if status[2] == "FN" else 0
            dist_fn += status[1] if status[2] == "FN" else 0
            # dist_fn += status[1][0] if status[2] == "FN" else 0

        dist_all = (dist_tp + dist_fp + dist_fn) / max(num_gt + num_dt, 1)
        dist_tp /= max(num_tp, 1)
        dist_fp /= max(num_fp, 1)
        dist_fn /= max(num_fn, 1)

        # score
        precision = num_tp / max(num_dt, 1)
        recall = num_tp / max(num_gt, 1)
        f_score = 2 * recall * precision / max(recall + precision, 1e-6)

        res = dict(
            num_gt=num_gt,
            num_dt=num_dt,
            num_tp=num_tp,
            num_fp=num_fp,
            num_fn=num_fn,
            precision=precision,
            recall=recall,
            f_score=f_score,
            dist_tp=dist_tp,
            dist_fp=dist_fp,
            dist_fn=dist_fn,
            dist_all=dist_all,
            dt_status=dt_status,
            gt_status=gt_status,
            dist_mat=dist_mat,
        )
        return res

    def _get_res_dict(self, dt_status_dict, gt_status_dict):
        res_dict = {}
        for dt_status_k, dt_status in dt_status_dict.items():
            gt_status = gt_status_dict[dt_status_k]

            num_dt, num_gt = len(dt_status), len(gt_status)
            num_tp, num_fp, num_fn, dist_tp, dist_fp, dist_fn = 0, 0, 0, 0, 0, 0
            dist_all_list, dist_tp_list, dist_fp_list, dist_fn_list = [], [], [], []

            for idx, status in enumerate(dt_status):
                if status[2] == "TP":
                    num_tp += 1
                    dist_tp += status[1]
                    dist_tp_list.append(status[1])

                if status[2] == "FP":
                    num_fp += 1
                    dist_fp += status[1]
                    dist_fp_list.append(status[1])

                dist_all_list.append(status[1])

            for idx, status in enumerate(gt_status):
                if status[2] == "FN":
                    num_fn += 1
                    dist_fn += status[1]
                    dist_fn_list.append(status[1])

            dist_all = (dist_tp + dist_fp + dist_fn) / max(num_gt + num_dt, 1)
            dist_tp /= max(num_tp, 1)
            dist_fp /= max(num_fp, 1)
            dist_fn /= max(num_fn, 1)

            # score
            precision = num_tp / max(num_dt, 1)
            recall = num_tp / max(num_gt, 1)
            f_score = 2 * recall * precision / max(recall + precision, 1e-6)

            res = dict(
                num_gt=num_gt,
                num_dt=num_dt,
                num_tp=num_tp,
                num_fp=num_fp,
                num_fn=num_fn,
                precision=precision,
                recall=recall,
                f_score=f_score,
                dist_tp=dist_tp,
                dist_fp=dist_fp,
                dist_fn=dist_fn,
                dist_all=dist_all,
                dist_tp_list=dist_tp_list,
                dist_fp_list=dist_fp_list,
                dist_all_list=dist_all_list,
                dt_status=dt_status,
                gt_status=gt_status,
            )
            res_dict[dt_status_k] = res

        return res_dict

    def calculate_results_by_dist_mat_for_box(self, cls, dt_boxs, gt_boxs, dist_mat, dt_scores=None):
        dt_status_dict, gt_status_dict = self.calculate_results_by_hungary_for_box(
            cls, dt_boxs, gt_boxs, dist_mat, dt_scores
        )
        return self._get_res_for_box_dict(dt_status_dict, gt_status_dict)

    def calculate_results_by_hungary_for_box(self, cls, dt_boxs, gt_boxs, dist_mat, dt_scores):
        inf_iou = self.box_eval_cfg["inf_iou"]
        dt_status_dict, gt_status_dict = {}, {}

        for range_idx in range(self.subdivision_num + 1):
            if range_idx == 0:
                eval_sub_range = f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}"
            else:
                eval_sub_range = f"{self.subdivision_range[range_idx-1]}-{self.subdivision_range[range_idx]}"

            for th in self.box_eval_cfg["iou_th"]:
                dt_status = [[-1, -1, "FP", dt_scores[cls][idx], 0] for idx in range(len(dt_boxs))]
                gt_status = [[-1, -1, "FN", 0, 0] for _ in range(len(gt_boxs))]

                if dist_mat is not None:
                    dist_mat[range_idx, dist_mat[range_idx] <= inf_iou] = inf_iou
                    dist_mat = dist_mat.astype(np.float16)
                    match_index_list = linear_sum_assignment(-dist_mat[range_idx])  # 每一个dt和gt都找到一个最优匹配

                    for dt_ind, gt_ind in zip(*match_index_list):
                        iou = dist_mat[range_idx, dt_ind, gt_ind]
                        if iou >= th:
                            dt_status[dt_ind] = [gt_ind, iou, "TP", dt_scores[cls][dt_ind], dt_status[dt_ind][-1]]
                            gt_status[gt_ind] = [dt_ind, iou, "TP", dt_scores[cls][dt_ind], gt_status[gt_ind][-1]]
                        else:
                            status_ok = iou >= th
                            dt_status[dt_ind] = [
                                gt_ind if status_ok else -1,
                                iou,
                                "FP",
                                dt_scores[cls][dt_ind],
                                dt_status[dt_ind][-1],
                            ]
                            gt_status[gt_ind] = [
                                dt_ind if status_ok else -1,
                                iou,
                                "FN",
                                dt_scores[cls][dt_ind],
                                gt_status[gt_ind][-1],
                            ]

                    res_info_key = f"{eval_sub_range}_{th}"

                    dt_status_dict[res_info_key] = dt_status
                    gt_status_dict[res_info_key] = gt_status
                else:
                    res_info_key = f"{eval_sub_range}_{th}"
                    dt_status_dict[res_info_key] = dt_status
                    gt_status_dict[res_info_key] = gt_status
        return dt_status_dict, gt_status_dict

    def _get_res_for_box(self, dt_status, gt_status, dist_mat):
        num_dt, num_gt = len(dt_status), len(gt_status)
        num_tp, num_fp, num_fn, dist_tp, dist_fp, dist_fn = 0, 0, 0, 0, 0, 0
        for idx, status in enumerate(dt_status):
            num_tp += 1 if status[2] == "TP" else 0
            dist_tp += status[1] if status[2] == "TP" else 0
            # dist_tp += status[1][0] if status[2] == "TP" else 0
            num_fp += 1 if status[2] == "FP" else 0
            dist_fp += status[1] if status[2] == "FP" else 0
            # dist_fp += status[1][0] if status[2] == "FP" else 0
        for idx, status in enumerate(gt_status):
            num_fn += 1 if status[2] == "FN" else 0
            dist_fn += status[1] if status[2] == "FN" else 0
            # dist_fn += status[1][0] if status[2] == "FN" else 0

        dist_all = (dist_tp + dist_fp + dist_fn) / max(num_gt + num_dt, 1)
        dist_tp /= max(num_tp, 1)
        dist_fp /= max(num_fp, 1)
        dist_fn /= max(num_fn, 1)

        # score
        precision = num_tp / max(num_dt, 1)
        recall = num_tp / max(num_gt, 1)
        f_score = 2 * recall * precision / max(recall + precision, 1e-6)

        res = dict(
            num_gt=num_gt,
            num_dt=num_dt,
            num_tp=num_tp,
            num_fp=num_fp,
            num_fn=num_fn,
            precision=precision,
            recall=recall,
            f_score=f_score,
            chamfer_dist_tp=dist_tp,
            chamfer_dist_fp=dist_fp,
            chamfer_dist_fn=dist_fn,
            chamfer_dist_all=dist_all,
            dt_status=dt_status,
            gt_status=gt_status,
            dist_mat=dist_mat,
        )
        return res

    def _get_res_for_box_dict(self, dt_status_dict, gt_status_dict):
        res_dict = {}
        for dt_status_k, dt_status in dt_status_dict.items():
            gt_status = gt_status_dict[dt_status_k]

            num_dt, num_gt = len(dt_status), len(gt_status)
            num_tp, num_fp, num_fn, dist_tp, dist_fp, dist_fn = 0, 0, 0, 0, 0, 0
            dist_all_list, dist_tp_list, dist_fp_list, dist_fn_list = [], [], [], []

            for idx, status in enumerate(dt_status):
                if status[2] == "TP":
                    num_tp += 1
                    dist_tp += status[1]
                    dist_tp_list.append(status[1])

                if status[2] == "FP":
                    num_fp += 1
                    dist_fp += status[1]
                    dist_fp_list.append(status[1])

                dist_all_list.append(status[1])

            for idx, status in enumerate(gt_status):
                if status[2] == "FN":
                    num_fn += 1
                    dist_fn += status[1]
                    dist_fn_list.append(status[1])

            dist_all = (dist_tp + dist_fp + dist_fn) / max(num_gt + num_dt, 1)
            dist_tp /= max(num_tp, 1)
            dist_fp /= max(num_fp, 1)
            dist_fn /= max(num_fn, 1)

            # score
            precision = num_tp / max(num_dt, 1)
            recall = num_tp / max(num_gt, 1)
            f_score = 2 * recall * precision / max(recall + precision, 1e-6)

            res = dict(
                num_gt=num_gt,
                num_dt=num_dt,
                num_tp=num_tp,
                num_fp=num_fp,
                num_fn=num_fn,
                precision=precision,
                recall=recall,
                f_score=f_score,
                dist_tp=dist_tp,
                dist_fp=dist_fp,
                dist_fn=dist_fn,
                dist_all=dist_all,
                dist_tp_list=dist_tp_list,
                dist_fp_list=dist_fp_list,
                dist_all_list=dist_all_list,
                dt_status=dt_status,
                gt_status=gt_status,
            )
            res_dict[dt_status_k] = res
        return res_dict

    def add_result(self, result_dict):
        for cls, cls_info in self.map_class_dict.items():
            if cls in result_dict:
                for eval_k, eval_res in result_dict[cls].items():
                    self.num_tp[cls][eval_k] += eval_res["num_tp"]
                    self.num_gt[cls][eval_k] += eval_res["num_gt"]
                    self.num_dt[cls][eval_k] += eval_res["num_dt"]

                    self.tp_metrics[cls][eval_k].extend(eval_res["dist_tp_list"])
                    self.dt_metrics[cls][eval_k].extend(eval_res["dist_all_list"])

    def summary_single_cls(self, cls_name="laneline"):
        return (
            self.num_tp[cls_name],
            self.num_dt[cls_name],
            self.num_gt[cls_name],
            self.tp_metrics[cls_name],
            self.dt_metrics[cls_name],
        )

    def summary_all_cls(self):
        num_tp_all, num_dt_all, num_gt_all = {}, {}, {}
        line_all_key = "0-100_0.3"  # TODO 后期放在init完成；
        for cls, cls_info in self.map_class_dict.items():
            num_tp_all += self.num_tp[cls][line_all_key]
            num_dt_all += self.num_dt[cls][line_all_key]
            num_gt_all += self.num_gt[cls][line_all_key]

        # all sumary metrics 意义不明确，可以不用统计
        return num_tp_all, num_dt_all, num_gt_all

    def get_interpolate_number(self, points, inter_dis=1):
        """
        累加弦长计算插值点数目
        """
        length = 0
        for i in range(len(points) - 1):
            # utm_x1, utm_y1 = utm.from_latlon(nodes[i].lat, nodes[i].lon)[:2]
            # utm_x2, utm_y2 = utm.from_latlon(nodes[i + 1].lat, nodes[i + 1].lon)[:2]
            utm_x1, utm_y1 = float(points[i][0]), float(points[i][1])
            utm_x2, utm_y2 = float(points[i + 1][0]), float(points[i + 1][1])
            length += math.sqrt((utm_x1 - utm_x2) ** 2 + (utm_y1 - utm_y2) ** 2)
        return math.ceil(length / inter_dis)

    def rebuild_line(self, points, inter_dis):

        order = 2
        if len(points) == 2:
            order = 1

        points = np.array(points)
        inter_num = self.get_interpolate_number(points, inter_dis)
        if inter_num <= points.shape[0]:
            return points
        points = np.unique(points, axis=0)
        tck, u = splprep([points[:, 0], points[:, 1], points[:, 2]], k=order, s=0)
        unew = np.linspace(0, 1, inter_num)
        out = splev(unew, tck)
        rebuild_pts = np.array([out[0][:], out[1][:], out[2][:]]).T

        return rebuild_pts

    def collect_results_gpu(self, metrics_list, dist, rank, world_size):
        # Step 1: Serialize and move to GPU
        result_bytes = pickle.dumps(metrics_list)
        part_tensor = torch.tensor(bytearray(result_bytes), dtype=torch.float32, device="cuda")

        # Step 2: Gather shapes
        shape_tensor = torch.tensor([part_tensor.size(0)], device="cuda")
        shape_list = [torch.zeros_like(shape_tensor) for _ in range(world_size)]
        torch.distributed.all_gather(shape_list, shape_tensor)

        # Step 3: Determine max length and pad
        shape_max = torch.stack(shape_list).max()
        padded_tensor = torch.zeros(shape_max, dtype=torch.float32, device="cuda")
        padded_tensor[: part_tensor.size(0)] = part_tensor

        # Step 4: Gather padded tensors
        part_recv_list = [torch.zeros_like(padded_tensor) for _ in range(world_size)]
        torch.distributed.all_gather(part_recv_list, padded_tensor)

        # Step 5: Deserialize results
        all_results = []
        for recv_tensor, recv_shape in zip(part_recv_list, shape_list):
            valid_tensor = recv_tensor[: recv_shape.item()]  # 去除填充部分
            result_bytes = bytes([int(v) for v in valid_tensor.tolist()])
            all_results.extend(pickle.loads(result_bytes))
        return all_results

    def init_eval_result(self):
        self.num_tp, self.num_dt, self.num_gt = {}, {}, {}
        self.tp_metrics, self.dt_metrics = {}, {}
        self.name_id_map = {}
        for cls, cls_info in self.map_class_dict.items():
            self.name_id_map[cls] = cls_info["id"]
            self.name_id_map[cls_info["id"]] = cls
            self.num_tp[cls] = {}
            self.num_gt[cls] = {}
            self.num_dt[cls] = {}

            self.tp_metrics[cls] = {}
            self.dt_metrics[cls] = {}
            for range_idx in range(self.subdivision_num + 1):
                if range_idx == 0:
                    eval_sub_range = f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}"
                else:
                    eval_sub_range = f"{self.subdivision_range[range_idx-1]}-{self.subdivision_range[range_idx]}"

                if cls in ["laneline", "curb", "stopline", "entrance"]:  # line th 度量是 dist，单位m
                    for th in self.threash_dict["fp_cd_thresh_list"]:
                        res_info_key = f"{eval_sub_range}_{th}"

                        self.num_tp[cls][res_info_key] = 0
                        self.num_gt[cls][res_info_key] = 0
                        self.num_dt[cls][res_info_key] = 0

                        self.tp_metrics[cls][res_info_key] = []
                        self.dt_metrics[cls][res_info_key] = []
                elif cls in ["crosswalk", "arrow", "noparking"]:
                    for th in self.box_eval_cfg["iou_th"]:
                        res_info_key = f"{eval_sub_range}_{th}"

                        self.num_tp[cls][res_info_key] = 0
                        self.num_gt[cls][res_info_key] = 0
                        self.num_dt[cls][res_info_key] = 0

                        self.tp_metrics[cls][res_info_key] = []
                        self.dt_metrics[cls][res_info_key] = []

    def parse_gt(self, gt_item, b_idx, f_idx=-1):
        gt_dict, mask_dict = {}, {}
        gt_dict = self.recover_line_and_inter(gt_item["map_gt"][b_idx][f_idx], self.map_range)
        mask_dict = self.recover_line_and_inter(gt_item["map_label_mask"][b_idx][f_idx], self.map_range)
        return gt_dict, mask_dict

    def build_new_points_vectors(self, line_pts, length=10):
        start_pts = [line_pts[0], line_pts[2]]
        center_pt = np.mean(start_pts, axis=0)
        d1 = self.restore_vector(line_pts[1][0], line_pts[1][1], length=length)
        d2 = self.restore_vector(line_pts[3][0], line_pts[3][1], length=length)
        # d1_line = np.array([np.array(line_pts[0]), np.array(line_pts[0]) + d1])
        # d2_line = np.array([np.array(line_pts[2]), np.array(line_pts[2]) + d2])
        d_mean = (d1 + d2) / (np.linalg.norm(d1 + d2)) * length
        f = self.compute_normal_direction(d_mean)
        width = line_pts[1][2]
        line_direction = [center_pt, center_pt + d_mean]
        line_width = [center_pt + 0.5 * width * f, center_pt - 0.5 * width * f]
        return line_direction, line_width

    @staticmethod
    def restore_vector(sin_theta, cos_theta, length=1):
        vector = np.array([cos_theta, sin_theta, 0])
        vector = vector / np.linalg.norm(vector)
        vector = vector * length
        return vector

    @staticmethod
    def compute_normal_direction(direction):
        norm_direction = [-direction[1], direction[0], 0]  # RFU 法向量（仅考虑XY平面）选择Y轴(前向)正向的法向量
        return norm_direction / np.linalg.norm(norm_direction)  # 单位化

    def summary_result_subrange(self, logger_api, dist, local_rank, rank, world_size):
        # logger_api.info("✨ Map Evaluation Results[th: Num(TP/DT/GT), P/R/F1, TP(mid/mean/.95), DT(mid/mean/.95): ")
        logger_api.info("✨ Map Evaluation Results[th: Num(TP/DT/GT), P/R/F1, TP(mid/mean/.80/.95): ")
        table_headers = ["cls"]
        for range_str in self.sub_range_str:
            table_headers.append(range_str + " th: Num(TP/DT/GT), P/R/F1, TP(mid/mean/.80/.95)")
        table_values = []
        map_class_dict = self.map_class_dict
        for cls, cls_info in map_class_dict.items():
            table_value_subrange = []
            for range_idx in range(self.subdivision_num + 1):
                if range_idx == 0:
                    eval_sub_range = f"{self.subdivision_range[0]}-{self.subdivision_range[-1]}"
                else:
                    eval_sub_range = f"{self.subdivision_range[range_idx-1]}-{self.subdivision_range[range_idx]}"

                table_value_th = []
                if cls in ["laneline", "curb", "stopline", "entrance"]:
                    for th in self.threash_dict["fp_cd_thresh_list"]:
                        res_info_key = f"{eval_sub_range}_{th}"

                        num_tp, num_dt, num_gt, tp_metrics_list, dt_metrics_list = (
                            self.num_tp[cls][res_info_key],
                            self.num_dt[cls][res_info_key],
                            self.num_gt[cls][res_info_key],
                            self.tp_metrics[cls][res_info_key],
                            self.dt_metrics[cls][res_info_key],
                        )

                        num_tp = dist.reduce_sum(torch.tensor(num_tp).cuda()).item()
                        num_dt = dist.reduce_sum(torch.tensor(num_dt).cuda()).item()
                        num_gt = dist.reduce_sum(torch.tensor(num_gt).cuda()).item()

                        tp_metrics_list = self.collect_results_gpu(tp_metrics_list, dist, rank, world_size)
                        dt_metrics_list = self.collect_results_gpu(dt_metrics_list, dist, rank, world_size)

                        (
                            tp_metrics_mid,
                            tp_metrics_mean,
                            tp_metrics_95,
                            _,
                            _,
                            tp_metrics_80,
                            _,
                        ) = (0, 0, 0, 0, 0, 0, 0)
                        if len(tp_metrics_list) > 0:
                            tp_metrics_mean = np.mean(tp_metrics_list)
                            tp_metrics_mid = np.median(tp_metrics_list)
                            tp_metrics_80 = np.percentile(tp_metrics_list, 80)
                            tp_metrics_95 = np.percentile(tp_metrics_list, 95)
                        # if len(dt_metrics_list) > 0:
                        #     dt_metrics_mean = np.mean(dt_metrics_list)
                        #     dt_metrics_mid = np.median(dt_metrics_list)
                        #     dt_metrics_95 = np.percentile(dt_metrics_list, 95)

                        precision = num_tp / max(num_dt, 1)
                        recall = num_tp / max(num_gt, 1)
                        f_score = 2 * recall * precision / max(recall + precision, 1e-6)

                        eval_res_str = "th {:.2f} : {:.0f}/{:.0f}/{:.0f}, {:.4f}/{:.4f}/{:.4f}, {:.3f}/{:.3f}/{:.3f}/{:.3f}".format(
                            th,
                            num_tp,
                            num_dt,
                            num_gt,
                            precision,
                            recall,
                            f_score,
                            tp_metrics_mid,
                            tp_metrics_mean,
                            tp_metrics_80,
                            tp_metrics_95,
                        )
                        # dt_metrics_mid, dt_metrics_mean, dt_metrics_95)
                        table_value_th.append(eval_res_str)
                    table_value_subrange.append("\n".join(table_value_th))
                elif cls in ["crosswalk", "arrow", "noparking"]:
                    for th in self.box_eval_cfg["iou_th"]:
                        res_info_key = f"{eval_sub_range}_{th}"

                        num_tp, num_dt, num_gt, tp_metrics_list, dt_metrics_list = (
                            self.num_tp[cls][res_info_key],
                            self.num_dt[cls][res_info_key],
                            self.num_gt[cls][res_info_key],
                            self.tp_metrics[cls][res_info_key],
                            self.dt_metrics[cls][res_info_key],
                        )

                        num_tp = dist.reduce_sum(torch.tensor(num_tp).cuda()).item()
                        num_dt = dist.reduce_sum(torch.tensor(num_dt).cuda()).item()
                        num_gt = dist.reduce_sum(torch.tensor(num_gt).cuda()).item()

                        tp_metrics_list = self.collect_results_gpu(tp_metrics_list, dist, rank, world_size)
                        dt_metrics_list = self.collect_results_gpu(dt_metrics_list, dist, rank, world_size)

                        (
                            tp_metrics_mid,
                            tp_metrics_mean,
                            tp_metrics_95,
                            _,  # dt_metrics_mid,
                            _,  # dt_metrics_mean,
                            _,  # dt_metrics_95,
                        ) = (0, 0, 0, 0, 0, 0)
                        if len(tp_metrics_list) > 0:
                            tp_metrics_mean = np.mean(tp_metrics_list)
                            tp_metrics_mid = np.median(tp_metrics_list)
                            tp_metrics_80 = np.percentile(tp_metrics_list, 80)
                            tp_metrics_95 = np.percentile(tp_metrics_list, 95)
                        # if len(dt_metrics_list) > 0:
                        #     dt_metrics_mean = np.mean(dt_metrics_list)
                        #     dt_metrics_mid = np.median(dt_metrics_list)
                        #     dt_metrics_95 = np.percentile(dt_metrics_list, 95)

                        precision = num_tp / max(num_dt, 1)
                        recall = num_tp / max(num_gt, 1)
                        f_score = 2 * recall * precision / max(recall + precision, 1e-6)

                        eval_res_str = "th {:.2f} : {:.0f}/{:.0f}/{:.0f}, {:.4f}/{:.4f}/{:.4f}, {:.3f}/{:.3f}/{:.3f}/{:.3f}".format(
                            th,
                            num_tp,
                            num_dt,
                            num_gt,
                            precision,
                            recall,
                            f_score,
                            tp_metrics_mid,
                            tp_metrics_mean,
                            tp_metrics_80,
                            tp_metrics_95,
                        )
                        # dt_metrics_mid, dt_metrics_mean, dt_metrics_95)
                        table_value_th.append(eval_res_str)
                    table_value_subrange.append("\n".join(table_value_th))

            table_values_one_raw = [cls] + table_value_subrange
            table_values.append(table_values_one_raw)

        if local_rank == 0:

            # build table
            table = tabulate(table_values, headers=table_headers, tablefmt="grid")
            logger_api.info("\n" + table)

    def rm_inmask_dt(self, dt_dict, mask_dict):
        save_dt_dict = {k: [] for k in list(dt_dict.keys())}
        mask_obj_list = []
        if "invalid_mask" not in mask_dict:
            return dt_dict
        for mask in mask_dict["invalid_mask"]:
            mask_obj = Polygon(mask)
            mask_obj_list.append(mask_obj)
        merged_mask = unary_union(mask_obj_list)
        if merged_mask.is_valid:
            for cls_id, cls_dict in dt_dict.items():
                for idx, line_pts in enumerate(cls_dict):
                    out_mask_line_pts = []
                    if cls_id in [0, 1, 2, 6]:
                        line_obj = LineString(line_pts)
                        out_mask_line = line_obj.difference(merged_mask)
                        if out_mask_line.geom_type == "LineString":
                            tmp_line = np.array(out_mask_line.coords)
                            if len(tmp_line) > 1:
                                out_mask_line_pts = [tmp_line.tolist()]
                        elif (
                            out_mask_line.geom_type == "MultiLineString"
                        ):  # 如果被切成两个 instance, 就按切分后合并的线来算 metric, 后面应该取消合并
                            merge_line = []
                            for new_line in out_mask_line.geoms:
                                tmp_line = np.array(new_line.coords)
                                if len(tmp_line) > 1:
                                    merge_line.extend(tmp_line.tolist())
                            out_mask_line_pts = [merge_line]
                        else:
                            continue
                    else:
                        line_obj = Polygon(line_pts)
                        if line_obj.is_valid:
                            out_mask_line = line_obj.difference(merged_mask)
                            if out_mask_line.geom_type == "Polygon":
                                tmp_line = np.array(out_mask_line.exterior.coords)
                                if len(tmp_line) > 1:
                                    out_mask_line_pts.append(tmp_line.tolist())
                            else:
                                continue
                        else:
                            continue
                    save_dt_dict[cls_id].extend(out_mask_line_pts)
        else:
            return dt_dict
        return save_dt_dict

    def parse_dt(self, dt_item, mask_dict):
        dt_dict, dt_scores = {v["id"]: [] for k, v in self.map_class_dict.items()}, {
            v["id"]: [] for k, v in self.map_class_dict.items()
        }
        for i, dt_cls in enumerate(dt_item["line_labels"]):
            if dt_item["line_scores"][i] >= self.map_class_dict_id[int(dt_cls)]["dt_scores_th"]:
                dt_scores[dt_cls].append(dt_item["line_scores"][i])
                dt_dict[dt_cls].append([dt_item["lines"][i]])
        dt_dict = self.recover_line_and_inter(dt_dict, self.map_range, self.inter_dis)
        try:
            dt_dict = self.rm_inmask_dt(dt_dict, mask_dict)
        except Exception as e:
            print("parse dt ", e)
            print("rm inmask fail, remain dt..")
        return dt_dict, dt_scores

    def recover_line_and_inter(self, map_dict, map_lidar_range, inter_dis=1):
        recover_map_dict = {k: [] for k in list(map_dict.keys())}
        for cls_id, cls_dict in map_dict.items():
            for idx, line_pts in enumerate(cls_dict):
                # for i, pts in enumerate(line_pts): # eval 仅保留每个permute第一个点组
                pts = np.array(line_pts[0])
                pts_recover = pts * (np.array(map_lidar_range[3:6]) - np.array(map_lidar_range[:3])) + np.array(
                    map_lidar_range[:3]
                )
                if cls_id in [0, 1]:
                    rebuild_line = self.rebuild_line(pts_recover, inter_dis)
                    recover_map_dict[cls_id].append(rebuild_line.tolist())
                elif cls_id in [2, 6]:  # stopline, entrance new gt
                    # TODO new gt eval 开发
                    pts_norm = np.array([pts[0], pts[2]])
                    pts_rec = pts_norm * (np.array(map_lidar_range[3:6]) - np.array(map_lidar_range[:3])) + np.array(
                        map_lidar_range[:3]
                    )
                    width = pts[1, 2] * (map_lidar_range[3] - map_lidar_range[0])
                    pts_recover = np.array(
                        [
                            [pts_rec[0, 0], pts_rec[0, 1], pts_rec[0, 2]],
                            [self.denormalize_trig(pts[1, 0]), self.denormalize_trig(pts[1, 1]), width],
                            [pts_rec[1, 0], pts_rec[1, 1], pts_rec[1, 2]],
                            [self.denormalize_trig(pts[3, 0]), self.denormalize_trig(pts[3, 1]), width],
                        ]
                    )
                    line_direction, line_width = self.build_new_points_vectors(pts_recover)
                    recover_map_dict[cls_id].append(np.array(line_width).tolist())
                else:
                    pts_recover = pts_recover.tolist()
                    pts_recover_closed = pts_recover + [pts_recover[0]]
                    recover_map_dict[cls_id].append(pts_recover_closed)
        return recover_map_dict

    @staticmethod
    def denormalize_trig(value):
        """
        将归一化后的值从 [0, 1] 还原到 [-1, 1]
        """
        return 2 * value - 1


class PointLineDistanceMetric(object):
    def __init__(
        self,
        inf_dis=99999,  # m
        metric_conf=dict(keep_ratio=0.8, mode="d<->g"),
        decay_dis=dict(x=60, y=600, z=12),
        core_range=[-6, 5, -2, 6, 50, 2],
        subdivision_range=[0, 30, 60, 100],  # 评测细分区间0-30、30-60、60-100
    ):
        self.inf_dis = inf_dis
        self.keep_ratio, self.mode = metric_conf["keep_ratio"], metric_conf["mode"]
        self.decay_dis = decay_dis
        self.core_range = core_range
        self.subdivision_range = subdivision_range
        self.subdivision_num = len(self.subdivision_range) - 1

    def __call__(self, dt_lanes, gt_lanes):
        if len(dt_lanes) == 0 or len(gt_lanes) == 0:
            return None
        dist_mat = np.zeros((1 + self.subdivision_num, len(dt_lanes), len(gt_lanes)))
        for s_idx, t_idx in product(range(len(dt_lanes)), range(len(gt_lanes))):
            distance = self._single_lane_pldis(dt_lanes[s_idx], gt_lanes[t_idx])
            dist_mat[:, s_idx, t_idx] = distance

        dist_mat[np.isnan(dist_mat)] = self.inf_dis
        return dist_mat

    def _single_lane_pldis(self, dt_lane, gt_lane):
        dt_lane = np.array(dt_lane)
        gt_lane = np.array(gt_lane)
        # assert dt_lane.shape[1] == gt_lane.shape[1], "dims must be same"

        if dt_lane.shape[1] == 2:
            dt_lane = np.concatenate([dt_lane, np.zeros((dt_lane.shape[0], 1))], axis=1)
            gt_lane = np.concatenate([gt_lane, np.zeros((gt_lane.shape[0], 1))], axis=1)

        d2g_xyz, g2d_xyz = self.pointline_distance(dt_lane, gt_lane)

        dist_xyz, dis_subdivision_range = 0, np.zeros(self.subdivision_num)
        if self.mode in ["d->g", "d<->g"]:
            dist_xyz += self.get_dis_with_weight_ratio(d2g_xyz, dt_lane)
            dis_subdivision_range += self.get_dis_with_subdivision_range(d2g_xyz, dt_lane)

        if self.mode in ["g->d", "d<->g"]:
            dist_xyz += self.get_dis_with_weight_ratio(g2d_xyz, gt_lane)
            dis_subdivision_range += self.get_dis_with_subdivision_range(g2d_xyz, gt_lane)

        dist_xyz = dist_xyz / 2
        dis_subdivision_range = dis_subdivision_range / 2
        dist_norm = np.array([dist_xyz])
        dist_concat = np.concatenate((dist_norm, dis_subdivision_range), axis=0)
        return dist_concat

    def pointline_distance(self, dt_line, gt_line):
        d2g_xyz = self.get_pldis_from_tar2src(dt_line, gt_line)
        g2d_xyz = self.get_pldis_from_tar2src(gt_line, dt_line)
        return np.array(d2g_xyz), np.array(g2d_xyz)

    def get_pldis_from_tar2src(self, target_line_pts, source_line_pts):
        source_line_xy = LineString(source_line_pts[:, :2])
        source_line_xz = LineString(source_line_pts[:, ::2])
        tar2src_xyz, tar2src_xy, tar2src_xz = [], [], []
        for pt in target_line_pts:
            pt_xy = np.array([pt[0], pt[1]])
            pt_xz = np.array([pt[0], pt[2]])
            pt_xy_obj = Point(pt_xy)
            pt_xz_obj = Point(pt_xz)
            xy_dis = pt_xy_obj.distance(source_line_xy)
            xz_dis = pt_xz_obj.distance(source_line_xz)
            tar2src_xy.append(xy_dis)
            tar2src_xz.append(xz_dis)
            tar2src_xyz.append(np.linalg.norm([xy_dis, xz_dis]))
        return tar2src_xyz

    def get_dis_with_weight_ratio(self, dis_res, line_pt):
        dis_w, core_idx = self.get_dis_weight_and_core_pt(line_pt)
        dis_res_w = dis_res * dis_w

        core_dis = dis_res_w[core_idx]
        no_core_dis = dis_res_w[~core_idx]

        sorted_arr = np.sort(no_core_dis)
        cutoff_index = int(len(sorted_arr) * self.keep_ratio)

        if sorted_arr[:cutoff_index].shape[0] > 0:
            valid_dis_res = np.concatenate((core_dis.ravel(), sorted_arr[:cutoff_index].ravel()))
        else:
            valid_dis_res = core_dis
        mean_dis = np.mean(valid_dis_res)

        return mean_dis

    def get_dis_weight_and_core_pt(self, line_pt):
        dis_weight, core_idx = [], []
        decay_dis = self.decay_dis
        core_range = self.core_range
        for pt in line_pt:
            if (
                core_range[0] <= pt[0] <= core_range[3]
                and core_range[1] <= pt[1] <= core_range[4]
                and core_range[2] <= pt[2] <= core_range[5]
            ):
                core_idx.append(1)
                w = 1
            else:
                core_idx.append(0)
                w = (
                    (decay_dis["x"] - abs(pt[0])) / decay_dis["x"]
                    + (decay_dis["y"] - abs(pt[1])) / decay_dis["y"]
                    + (decay_dis["z"] - abs(pt[2])) / decay_dis["z"]
                ) / 3

            dis_weight.append(w)

        return np.array(dis_weight), np.array(core_idx).astype(np.bool)

    def get_dis_with_subdivision_range(self, dis_res, line_pt):
        assert len(dis_res) == len(line_pt), "dis_res and line_pt lenght should same"
        dis_subdivision_range = np.zeros(self.subdivision_num)
        dis_subdivision_pt_num = np.zeros(self.subdivision_num)
        for i, pt in enumerate(line_pt):
            for n in range(self.subdivision_num):
                if self.subdivision_range[n] <= pt[1] <= self.subdivision_range[n + 1]:
                    dis_subdivision_range[n] += dis_res[i]
                    dis_subdivision_pt_num[n] += 1
        dis_subdivision_pt_num = np.where(dis_subdivision_pt_num == 0, 1, dis_subdivision_pt_num)
        dis_subdivision_range = dis_subdivision_range / dis_subdivision_pt_num
        # eval range 范围内若dis_subdivision_range为0，代表该区间没有有效线段，将距离置为9999
        dis_subdivision_range = np.where(dis_subdivision_range == 0, 9999, dis_subdivision_range)
        return dis_subdivision_range


class BoxDistanceMetric(object):
    def __init__(
        self,
        decay_dis=dict(x=60, y=600, z=12),
        core_range=[-6, 5, -2, 6, 50, 2],
        subdivision_range=[0, 30, 60, 100],
    ):
        self.decay_dis = decay_dis
        self.core_range = core_range
        self.subdivision_range = subdivision_range
        self.subdivision_num = len(self.subdivision_range) - 1

    def __call__(self, dt_boxs, gt_boxs, with_iou=False, with_kl_d=False, with_wt_d=False):
        if len(dt_boxs) == 0 or len(gt_boxs) == 0:
            return None
        dist_mat = np.zeros((1 + self.subdivision_num, len(dt_boxs), len(gt_boxs)))  # all_range + sub_range
        for s_idx, t_idx in product(range(len(dt_boxs)), range(len(gt_boxs))):
            distance = self._single_box_metric(
                dt_boxs[s_idx], gt_boxs[t_idx], with_iou=with_iou, with_kl_d=with_kl_d, with_wt_d=with_wt_d
            )
            dist_mat[:, s_idx, t_idx] = distance

        # dist_mat[np.isnan(dist_mat)] = self.inf_dis # box dis 不会出现 NAN异常
        return dist_mat

    def _single_box_metric(self, dt_lane, gt_lane, with_iou=False, with_kl_d=False, with_wt_d=False):
        dt_lane = np.array(dt_lane)
        gt_lane = np.array(gt_lane)
        dt_lane_xy = dt_lane[:, :2]
        gt_lane_xy = gt_lane[:, :2]
        # assert dt_lane.shape[1] == gt_lane.shape[1], "dims must be same"

        gt_lane_mean = np.mean(gt_lane, axis=0)
        dis_subdivision_iou = np.zeros(self.subdivision_num)  # GT 不在 sub range时， iou置为0；

        if dt_lane.shape[1] == 2:
            dt_lane = np.concatenate([dt_lane, np.zeros((dt_lane.shape[0], 1))], axis=1)
            gt_lane = np.concatenate([gt_lane, np.zeros((gt_lane.shape[0], 1))], axis=1)

        iou = None  # iuo
        # kl_d = None  # kl_divergence
        # wt_d = None  # wasserstein_distance
        if with_iou:
            iou = self.calculate_iou_with_curve(dt_lane_xy, gt_lane_xy)
        # elif with_kl_d:
        #     kl_d = None
        # elif with_wt_d:
        #     wt_d = None

        for i in range(self.subdivision_num):
            if self.subdivision_range[i] <= gt_lane_mean[1] <= self.subdivision_range[i + 1]:
                dis_subdivision_iou[i] = iou

        dist_norm = np.array([iou])
        dis_mat_one = np.concatenate((dist_norm, dis_subdivision_iou), axis=0)
        return dis_mat_one

    def calculate_iou_with_curve(self, curve1_points, curve2_points):
        # Create polygons from the curve points
        poly1 = Polygon(curve1_points)
        poly2 = Polygon(curve2_points)

        if poly1.is_valid and poly2.is_valid:
            # Calculate the intersection area
            intersection_area = poly1.intersection(poly2).area

            # Calculate the union area
            union_area = poly1.union(poly2).area

            # Calculate IOU
            iou = intersection_area / union_area if union_area > 0 else 0
        else:
            iou = 0
        return iou

    def calculate_iou_2d(self, box1, box2):
        # box format: [x_min, y_min, x_max, y_max]

        # Calculate intersection coordinates
        x_min_inter = max(box1[0], box2[0])
        y_min_inter = max(box1[1], box2[1])
        x_max_inter = min(box1[2], box2[2])
        y_max_inter = min(box1[3], box2[3])

        # Compute intersection area
        inter_width = max(0, x_max_inter - x_min_inter)
        inter_height = max(0, y_max_inter - y_min_inter)
        inter_area = inter_width * inter_height

        # Compute areas of both boxes
        area_box1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area_box2 = (box2[2] - box2[0]) * (box2[3] - box2[1])

        # Compute union area
        union_area = area_box1 + area_box2 - inter_area

        # Compute IOU
        iou = inter_area / union_area if union_area > 0 else 0
        return iou

    def calculate_iou_3d(self, box1, box2):
        # box format: [x_min, y_min, z_min, x_max, y_max, z_max]

        # Calculate intersection coordinates
        x_min_inter = max(box1[0], box2[0])
        y_min_inter = max(box1[1], box2[1])
        z_min_inter = max(box1[2], box2[2])

        x_max_inter = min(box1[3], box2[3])
        y_max_inter = min(box1[4], box2[4])
        z_max_inter = min(box1[5], box2[5])

        # Compute intersection volume
        inter_volume = (
            max(0, x_max_inter - x_min_inter) * max(0, y_max_inter - y_min_inter) * max(0, z_max_inter - z_min_inter)
        )

        # Compute volumes of both boxes
        volume_box1 = (box1[3] - box1[0]) * (box1[4] - box1[1]) * (box1[5] - box1[2])
        volume_box2 = (box2[3] - box2[0]) * (box2[4] - box2[1]) * (box2[5] - box2[2])

        # Compute union volume
        union_volume = volume_box1 + volume_box2 - inter_volume

        # Compute IOU
        iou = inter_volume / union_volume if union_volume > 0 else 0
        return iou

    def kl_divergence(self, mu1, cov1, mu2, cov2):
        d = mu1.shape[0]
        term1 = np.log(np.linalg.det(cov2) / np.linalg.det(cov1))
        term2 = np.trace(np.linalg.solve(cov2, cov1))
        term3 = (mu2 - mu1).T @ np.linalg.solve(cov2, (mu2 - mu1))
        return 0.5 * (term1 + term2 + term3 - d)

    def wasserstein_distance(self, mu1, cov1, mu2, cov2):
        d = np.linalg.norm(mu1 - mu2) ** 2
        term2 = np.trace(cov1 + cov2 - 2 * np.linalg.matrix_square_root(cov1 @ cov2))
        return d + term2


class ChamferDistanceMetric(object):
    def __init__(
        self,
        region_conf=dict(
            range=[100, 0, 15, -15, 10, -10],
            step=1,
            decay_ratio=0,
            fov_region=None,
            x_start_valid=300,
        ),
        metric_conf=dict(keep_ratio=0.8, mode="d<->g"),
        inf_cd=1000,
    ) -> None:
        self.chamfer_dist = CD_func()
        self.keep_ratio, self.mode = metric_conf["keep_ratio"], metric_conf["mode"]
        self.regions, self.weights = None, None
        if region_conf["decay_ratio"] > 0:
            x_range = region_conf["range"][:2]
            interval = torch.linspace(
                x_range[1],
                x_range[0],
                int((x_range[0] - x_range[1]) / region_conf["step"]) + 1,
            )
            self.regions = torch.stack([interval[:-1], interval[1:]], dim=1).unsqueeze(0).cuda()  # (1, m, 2)
            self.weights = 1 - (torch.abs(interval - max(0, x_range[1])) * region_conf["decay_ratio"]).cuda()  # (m, )
            # assert np.all(self.weights > 0), "please adjust decay"
        self.inf_cd = inf_cd
        self.use_common_dt_gt = True

    def __call__(self, dt_lanes, gt_lanes):
        if len(dt_lanes) == 0 or len(gt_lanes) == 0:
            return None
        dist_mat = np.zeros((3, len(dt_lanes), len(gt_lanes)))
        for s_idx, t_idx in product(range(len(dt_lanes)), range(len(gt_lanes))):
            distance = self._single_lane_cd(dt_lanes[s_idx], gt_lanes[t_idx])
            dist_mat[:, s_idx, t_idx] = distance

        dist_mat[np.isnan(dist_mat)] = self.inf_cd
        return dist_mat

    def get_common_dt_gt(self, dt_lane, gt_lane):
        dt_lane_y = dt_lane[:, 1]
        gt_lane_y = gt_lane[:, 1]
        dt_lane_y_min, dt_lane_y_max = dt_lane_y.min(), dt_lane_y.max()
        gt_lane_y_min, gt_lane_y_max = gt_lane_y.min(), gt_lane_y.max()
        y_min = max(dt_lane_y_min, gt_lane_y_min)
        y_max = min(dt_lane_y_max, gt_lane_y_max)
        dt_lane_valid = np.bitwise_and(dt_lane_y >= y_min, dt_lane_y <= y_max)
        gt_lane_valid = np.bitwise_and(gt_lane_y >= y_min, gt_lane_y <= y_max)
        return_dt_lane, return_gt_lane = dt_lane[dt_lane_valid], gt_lane[gt_lane_valid]
        return return_dt_lane, return_gt_lane

    def _single_lane_cd(self, dt_lane, gt_lane):
        dt_lane = np.array(dt_lane)
        gt_lane = np.array(gt_lane)
        if self.use_common_dt_gt:
            dt_lane_common, gt_lane_common = self.get_common_dt_gt(dt_lane, gt_lane)
            if len(dt_lane_common) and len(gt_lane_common):
                dt_lane, gt_lane = dt_lane_common, gt_lane_common
        assert dt_lane.shape[1] == gt_lane.shape[1], "dims must be same"

        if dt_lane.shape[1] == 2:
            dt_lane = np.concatenate([dt_lane, np.zeros((dt_lane.shape[0], 1))], axis=1)
            gt_lane = np.concatenate([gt_lane, np.zeros((gt_lane.shape[0], 1))], axis=1)
        dt_lane = torch.FloatTensor(dt_lane).cuda().unsqueeze(0)  # (1, M, 3)
        gt_lane = torch.FloatTensor(gt_lane).cuda().unsqueeze(0)  # (1, N, 3)
        d2g_xyz, d2g_ids, g2d_xyz, g2d_ids = self.chamfer_dist(dt_lane, gt_lane)
        dt_lane, d2g_xyz, d2g_ids = dt_lane[0], d2g_xyz[0], d2g_ids[0].long()
        gt_lane, g2d_xyz, g2d_ids = gt_lane[0], g2d_xyz[0], g2d_ids[0].long()

        dist_xyz, dist_xy, dist_xz = 0, 0, 0
        if self.mode in ["d->g", "d<->g"]:
            x_decay_w = self._get_weights(dt_lane[:, 0])
            dist_xyz += (d2g_xyz * x_decay_w).mean()
            dist_xy += self._get_dist(dt_lane[:, [0, 1]], gt_lane[d2g_ids][:, [0, 1]], x_decay_w)
            dist_xz += self._get_dist(dt_lane[:, [0, 2]], gt_lane[d2g_ids][:, [0, 2]], x_decay_w)

        if self.mode in ["g->d", "d<->g"]:
            x_decay_w = self._get_weights(gt_lane[:, 0])
            dist_xyz += (g2d_xyz * x_decay_w).mean()
            dist_xy += self._get_dist(dt_lane[g2d_ids][:, [0, 1]], gt_lane[:, [0, 1]], x_decay_w)
            dist_xz += self._get_dist(dt_lane[g2d_ids][:, [0, 2]], gt_lane[:, [0, 2]], x_decay_w)
        dist_xyz = dist_xyz / 2
        return dist_xy.cpu().detach(), dist_xz.cpu().detach(), dist_xyz.cpu().detach()

    def _get_dist(self, src_lane, tgt_lane, x_decay_w):
        assert src_lane.shape == tgt_lane.shape
        distance = torch.norm(src_lane - tgt_lane, dim=1, p=2, keepdim=True).squeeze(1)
        kept_ids = torch.argsort(distance)[: int(src_lane.shape[0] * self.keep_ratio)]
        return (distance * x_decay_w)[kept_ids].mean()

    def _get_weights(self, lane_x):
        decay_weights = torch.ones_like(lane_x).cuda()
        if self.weights is not None and self.regions is not None:
            diff = lane_x.unsqueeze(1).unsqueeze(2) - self.regions
            cond = torch.bitwise_and(diff[:, :, 0] >= 0, diff[:, :, 1] < 0)
            cond[cond.sum(1) == 0, -1] = True
            indices = torch.where(cond)[1]
            decay_weights = self.weights[indices]
        assert lane_x.shape[0] == decay_weights.shape[0]
        return decay_weights
