import torch
import cupy as cp
import matplotlib
import numpy as np
from itertools import product
from scipy.optimize import linear_sum_assignment
from perceptron.utils.map_utils.evaltools.metric.chamfer_distance import ChamferDistance as CD_func


class Lane3DEvaluator:
    def __init__(
        self, region_conf, thresh_conf, metric_conf, match_by_score=False, use_category=True, error_distance=2
    ) -> None:
        """
        :param region_conf: dict, 区域配置, 包含四个key: range, step, weight, fov_region
            (1) range: 评测范围, [front back left right up down], 内部数据ego系方向: 前x左y上z, eg. (60, 0, 15, -15, 3, -1)
            (2) step: 区域步长, 越远对最终距离计算的贡献越小, 沿着x的方向每前进 step(m) 该区域的重要性下降 decay_ratio 倍
            (3) decay_ratio: 下降倍率k, y=1-k|x|
            (4) fov_region: point-list, 要评测的fov区域, metric内部不负责fov-polygon的计算, 若是多camera, 需要用户提前计算
        :param thresh_conf: dict, 距离阈值, 当两条车道线之间的距离 '同时低于xy/xz' 时认为是TP-matching
            (1) xy: XY平面上的距离阈值
            (2) xz: XZ平面上的距离阈值
            (3) INF: 最大的距离阈值, 避免CD值无限大导致指标失效
        :param metric_conf: dict, 评测配置, 包含三个key: interp_dist, keep_ratio, mode
            (1) interp_dist: 插值间隔, 默认沿着x方向插值,
            (2) keep_ratio: 参与计算的点比例, 只有最匹配的前keep_ratio个点参与计算, 比如 keep_ratio=0.8
            (3) mode: 提供不同模式的chamfer-dist距离计算, 取值有 'd<->g', 'd->g', 'g->d'

        """
        self.region_conf = region_conf
        self.fov_region = region_conf["fov_region"]
        self.x_start_valid = region_conf["x_start_valid"]
        self.decay_ratio = region_conf["decay_ratio"]
        self.thresh_conf = thresh_conf
        self.metric_conf = metric_conf  # dict(interp_dist=0.1, keep_ratio=0.9, mode="d<->g")
        self.cd_func = ChamferDistanceMetric(region_conf, metric_conf)
        self.fov_region = None if self.fov_region is None else matplotlib.path.Path(self.fov_region, closed=True)
        self.match_by_score = match_by_score
        self.metric_type = "cd"
        self.use_category = use_category
        self.use_numpy_interp = False

    def calulate_error(self, points, deg=4):
        x, y = points[:, 0], points[:, 1]
        distance = np.sqrt((np.polyval(np.polyfit(x, y, deg), x) - y) ** 2)
        max_distance = distance.max()
        return max_distance

    def _get_match_mask(self, dt_attrs, gt_attrs, use_category):
        nr_dt, nr_gt = len(dt_attrs), len(gt_attrs)
        match_mask = np.zeros((nr_dt, nr_gt))
        for dt_idx, dt_attr in enumerate(dt_attrs):
            for gt_idx, gt_attr in enumerate(gt_attrs):
                if use_category:
                    if dt_attr["road_curb"] == 1 and gt_attr["road_curb"] == 1:
                        match_mask[dt_idx, gt_idx] = 1
                    if dt_attr["stop_lane"] == 1 and gt_attr["stop_lane"] == 1:
                        match_mask[dt_idx, gt_idx] = 1
                    if dt_attr["guardrail"] == 1 and gt_attr["guardrail"] == 1:
                        match_mask[dt_idx, gt_idx] = 1
                    if (
                        dt_attr["road_curb"] == 0
                        and gt_attr["road_curb"] == 0
                        and dt_attr["stop_lane"] == 0
                        and gt_attr["stop_lane"] == 0
                        and dt_attr["guardrail"] == 0
                        and gt_attr["guardrail"] == 0
                    ):
                        match_mask[dt_idx, gt_idx] = 1
                else:
                    if dt_attr["stop_lane"] == 1 and gt_attr["stop_lane"] == 1:
                        match_mask[dt_idx, gt_idx] = 1
                    if dt_attr["stop_lane"] == 0 and gt_attr["stop_lane"] == 0:
                        match_mask[dt_idx, gt_idx] = 1

        return match_mask.astype("bool")

    def add_once(self, item_dt, item_gt):
        dt_lanes = [np.array(lane[0]) for lane in item_dt]
        dt_scores = [1.0 for lane in item_dt]
        dt_attrs = ["unknown" for lane in item_dt]
        gt_lanes = [np.array(lane[0]) for lane in item_gt]
        gt_attrs = ["unknown" for lane in item_gt]

        if self.metric_conf["interp_dist"] > 0:
            dt_lanes = self.interpolate(dt_lanes)
        if self.fov_region is not None:
            dt_lanes, dt_valid_idx = self.points_filter_by_fov(dt_lanes)
            dt_scores = [1.0 for idx in dt_valid_idx]
            dt_attrs = [dt_attrs[idx] for idx in dt_valid_idx]
        else:
            dt_lanes, dt_valid_idx = self.points_filter_by_range(dt_lanes)
            dt_scores = [1.0 for idx in dt_valid_idx]
            dt_attrs = [dt_attrs[idx] for idx in dt_valid_idx]

        if self.metric_conf["interp_dist"] > 0:
            gt_lanes = self.interpolate(gt_lanes)
        if self.fov_region is not None:
            gt_lanes, gt_valid_idx = self.points_filter_by_fov(gt_lanes)
            gt_attrs = [gt_attrs[idx] for idx in gt_valid_idx]
        else:
            gt_lanes, gt_valid_idx = self.points_filter_by_range(gt_lanes)
            gt_attrs = [gt_attrs[idx] for idx in gt_valid_idx]

        res_dict = {"nid": "1"}
        dist_mat = self.cd_func(dt_lanes=dt_lanes, gt_lanes=gt_lanes)
        if dist_mat is not None:
            dist_mat_category = dist_mat.copy()
        else:
            dist_mat_category = None
        status = self.calculate_results(
            dt_lanes=dt_lanes,
            gt_lanes=gt_lanes,
            dist_mat_category=dist_mat_category,
            dist_mat_no_category=dist_mat_category,
            dt_lanes_scores=dt_scores,
        )
        status["gt_valid_idx"] = gt_valid_idx
        status["dt_valid_idx"] = dt_valid_idx
        status["gt_points"] = [lane.astype(np.float32) for lane in gt_lanes]
        status["dt_points"] = [lane.astype(np.float32) for lane in dt_lanes]
        res_dict.update(status)
        return res_dict

    def interpolate(self, lanes):
        lanes_inter = [self.inter_lane(lane) for lane in lanes]
        return lanes_inter

    def inter_lane(self, lane):
        if len(lane) < 2:
            return lane
        lane = lane[lane[:, 0].argsort()]
        lane_x, lane_y, lane_z = lane[:, 0], lane[:, 1], np.zeros(lane[:, 2].shape)  # lane[:, 2]
        lane_x_min, lane_x_max = min(lane_x), max(lane_x)
        lane_x_inter = np.append(np.arange(lane_x_min, lane_x_max, self.metric_conf["interp_dist"]), lane_x_max)
        if self.use_numpy_interp:
            lane_y_inter = np.interp(lane_x_inter, lane_x, lane_y)
            lane_z_inter = np.interp(lane_x_inter, lane_x, lane_z)
        else:
            lane_x_inter_cp = cp.array(lane_x_inter)
            lane_x, lane_y, lane_z = cp.array(lane_x), cp.array(lane_y), cp.array(lane_z)
            lane_y_inter = cp.interp(lane_x_inter_cp, lane_x, lane_y).get()
            lane_z_inter = cp.interp(lane_x_inter_cp, lane_x, lane_z).get()
            lane_x_inter, lane_y_inter, lane_z_inter = lane_x_inter, lane_y_inter, lane_z_inter
        lane_inter = np.stack([lane_x_inter, lane_y_inter, lane_z_inter], axis=1)
        return lane_inter

    def points_filter_by_range(self, lanes):
        new_lane, return_idx = [], []
        for idx, lane in enumerate(lanes):
            keep_idx = np.ones(lane.shape[0])
            if len(lane) > 0:
                for dim_id in range(3):  # 3d-lane
                    keep_idx *= np.bitwise_and(
                        lane[:, dim_id] <= self.region_conf["range"][dim_id * 2],
                        lane[:, dim_id] >= self.region_conf["range"][dim_id * 2 + 1],
                    )
                lane = lane[keep_idx > 0]
            if lane.shape[0] > 2:
                new_lane.append(lane)
                return_idx.append(idx)
        return new_lane, return_idx

    def lane_points_filter_by_mask(self, lane, item_mask):
        # 只要有点在mask区域外，那么就不算mask lane, 不被滤除
        is_mask = True
        for mask_polygen in item_mask:
            pass
        return is_mask

    def points_filter_by_mask(self, lanes, item_mask):
        return_lanes, return_idx = [], []
        for idx, lane in enumerate(lanes):
            pass
        return return_lanes, return_idx

    def points_filter_by_fov(self, lanes):
        new_lanes, return_idx = [], []
        z_range = self.region_conf["range"][-2:]
        for idx, lane in enumerate(lanes):
            keep_ids_by_xy = self.fov_region.contains_points(lane[:, :2])
            keep_ids_by_z = np.bitwise_and(lane[:, 2] >= z_range[1], lane[:, 2] < z_range[0])
            keep_ids = np.bitwise_and(keep_ids_by_xy, keep_ids_by_z)
            if keep_ids.sum() >= 2:
                new_lanes.append(lane[keep_ids])
                return_idx.append(idx)
        return new_lanes, return_idx

    def lanes_filter_by_xstart(self, lanes):
        new_lanes, return_idx = [], []
        x_start_valid = self.region_conf["x_start_valid"]
        for idx, lane in enumerate(lanes):
            if lane.size != 0 and lane[0][0] > lane[-1][0]:
                lane = lane[::-1]
            if lane.size != 0 and lane[0][0] >= x_start_valid:
                continue
            else:
                return_idx.append(idx)
                new_lanes.append(lane)

        return new_lanes, return_idx

    def _get_res(self, dt_status, gt_status, dist_mat):
        num_dt, num_gt = len(dt_status), len(gt_status)
        num_tp, num_fp, num_fn, dist_tp, dist_fp, dist_fn = 0, 0, 0, 0, 0, 0
        for idx, status in enumerate(dt_status):
            num_tp += 1 if status[2] == "TP" else 0
            dist_tp += status[1] if status[2] == "TP" else 0
            # dist_tp += status[1][0] if status[2] == "TP" else 0
            num_fp += 1 if status[2] == "FP" else 0
            dist_fp += status[1] if status[2] == "FP" else 0
            # dist_fp += status[1][0] if status[2] == "FP" else 0
        for idx, status in enumerate(gt_status):
            num_fn += 1 if status[2] == "FN" else 0
            dist_fn += status[1] if status[2] == "FN" else 0
            # dist_fn += status[1][0] if status[2] == "FN" else 0

        dist_all = (dist_tp + dist_fp + dist_fn) / max(num_gt + num_dt, 1)
        dist_tp /= max(num_tp, 1)
        dist_fp /= max(num_fp, 1)
        dist_fn /= max(num_fn, 1)

        # score
        precision = num_tp / max(num_dt, 1)
        recall = num_tp / max(num_gt, 1)
        f_score = 2 * recall * precision / max(recall + precision, 1e-6)

        res = dict(
            num_gt=num_gt,
            num_dt=num_dt,
            num_tp=num_tp,
            num_fp=num_fp,
            num_fn=num_fn,
            precision=precision,
            recall=recall,
            f_score=f_score,
            chamfer_dist_tp=dist_tp,
            chamfer_dist_fp=dist_fp,
            chamfer_dist_fn=dist_fn,
            chamfer_dist_all=dist_all,
            dt_status=dt_status,
            gt_status=gt_status,
            dist_mat=dist_mat,
        )
        return res

    def calculate_results_by_hungary(self, dt_lanes, gt_lanes, dist_mat, dt_lanes_scores):
        cd_inf = self.thresh_conf["INF"]
        dt_status = [[-1, cd_inf, "FP", dt_lanes_scores[idx], 0] for idx in range(len(dt_lanes))]
        gt_status = [[-1, cd_inf, "FN", 0, 0] for idx in range(len(gt_lanes))]
        # dist_mat[-1, np.logical_or(dist_mat[0] >= self.thresh_conf["xy"], dist_mat[1] >= self.thresh_conf["xz"])] = 1e3
        if dist_mat is not None:
            dist_mat_save = dist_mat.copy()
            dist_mat[-1, dist_mat[2] >= self.thresh_conf["xyz"]] = 1e3  # 1e6
            # -------for inf----------
            dist_mat = torch.minimum(torch.tensor(dist_mat), torch.tensor(1e3))
            dist_mat = torch.maximum(
                torch.tensor(dist_mat).clone().detach(), torch.tensor(-1e3).clone().detach()
            ).numpy()
            # ------------------------
            dist_mat = dist_mat.astype(np.float16)
            match_index_list = linear_sum_assignment(dist_mat[-1])  # 每一个dt和gt都找到一个最优匹配

            for dt_ind, gt_ind in zip(*match_index_list):
                # dist_xy = min(dist_mat[0, dt_ind, gt_ind], cd_inf)
                # dist_xz = min(dist_mat[1, dt_ind, gt_ind], cd_inf)
                dist_xyz = min(dist_mat[2, dt_ind, gt_ind], cd_inf)
                dist_xyz_save = dist_mat_save[2, dt_ind, gt_ind]
                if dist_xyz <= self.thresh_conf["xyz"]:
                    dt_status[dt_ind] = [gt_ind, dist_xyz, "TP", dt_lanes_scores[dt_ind], dt_status[dt_ind][-1]]
                    gt_status[gt_ind] = [dt_ind, dist_xyz, "TP", dt_lanes_scores[dt_ind], gt_status[gt_ind][-1]]
                else:
                    status_ok = dist_xyz < cd_inf
                    dt_status[dt_ind] = [
                        gt_ind if status_ok else -1,
                        dist_xyz_save,
                        "FP",
                        dt_lanes_scores[dt_ind],
                        dt_status[dt_ind][-1],
                    ]
                    gt_status[gt_ind] = [
                        dt_ind if status_ok else -1,
                        dist_xyz,
                        "FN",
                        dt_lanes_scores[dt_ind],
                        gt_status[gt_ind][-1],
                    ]
        return dt_status, gt_status

    def calculate_results_by_dist_mat(self, dt_lanes, gt_lanes, dist_mat, dt_lanes_scores):
        dt_status, gt_status = self.calculate_results_by_hungary(dt_lanes, gt_lanes, dist_mat, dt_lanes_scores)
        return self._get_res(dt_status, gt_status, dist_mat)

    def calculate_results(self, dt_lanes, gt_lanes, dist_mat_category, dist_mat_no_category, dt_lanes_scores):
        res_category = self.calculate_results_by_dist_mat(dt_lanes, gt_lanes, dist_mat_category, dt_lanes_scores)
        # res_no_category = self.calculate_results_by_dist_mat(dt_lanes, gt_lanes, dist_mat_no_category, dt_lanes_scores)
        # dt_status_category, gt_status_category = res_category["dt_status"], res_category["gt_status"]
        # dt_status_no_category, gt_status_no_category = res_no_category["dt_status"], res_category["gt_status"]
        # return_gt_status = [[*x, *y] for x, y in zip(gt_status_category, gt_status_no_category)]
        # return_dt_status = [[*x, *y] for x, y in zip(dt_status_category, dt_status_no_category)]
        # res_category["dt_status"] = return_dt_status
        # res_category["gt_status"] = return_gt_status
        return res_category


class ChamferDistanceMetric(object):
    def __init__(self, region_conf, metric_conf, inf_cd=1000) -> None:
        self.chamfer_dist = CD_func()
        self.keep_ratio, self.mode = metric_conf["keep_ratio"], metric_conf["mode"]
        self.regions, self.weights = None, None
        if region_conf["decay_ratio"] > 0:
            x_range = region_conf["range"][:2]
            interval = torch.linspace(
                x_range[1],
                x_range[0],
                int((x_range[0] - x_range[1]) / region_conf["step"]) + 1,
            )
            self.regions = torch.stack([interval[:-1], interval[1:]], dim=1).unsqueeze(0).cuda()  # (1, m, 2)
            self.weights = 1 - (torch.abs(interval - max(0, x_range[1])) * region_conf["decay_ratio"]).cuda()  # (m, )
            # assert np.all(self.weights > 0), "please adjust decay"
        self.inf_cd = inf_cd
        self.use_common_dt_gt = True

    def __call__(self, dt_lanes, gt_lanes):
        if len(dt_lanes) == 0 or len(gt_lanes) == 0:
            return None
        dist_mat = np.zeros((3, len(dt_lanes), len(gt_lanes)))
        for s_idx, t_idx in product(range(len(dt_lanes)), range(len(gt_lanes))):
            # align_dt, align_gt = self.align_dt_gt(dt_lanes[s_idx], gt_lanes[t_idx])
            # if len(align_dt.shape) < 2 or len(align_gt.shape) < 2:
            #     distance = 99999
            # else:
            #     distance = self._single_lane_cd(align_dt, align_gt)
            distance = self._single_lane_cd(dt_lanes[s_idx], gt_lanes[t_idx])
            dist_mat[:, s_idx, t_idx] = distance

        dist_mat[np.isnan(dist_mat)] = self.inf_cd
        return dist_mat

    def align_dt_gt(self, dt_lane, gt_lane, type="lane"):
        align_dt, align_gt = None, None
        """
        功能与下面get_common_dt_gt()相似，无法解决线中间的离群点；
        CD 距离在以下情况会出错(不必要也不充分)：
            1. 点集离群点；
            2. 点集噪声；
            3. 点集局部形状差异；
            4. 变换不确定性；
            5. 参数设置不合理；
        对齐能解决以上问题吗？
            1. 点集噪声、离群点无法准确判断并去除；
            2. 点集局部差异客观存在，无法消除->看什么差异能够容忍；
        合适评估指标具有那些特性：
            1. 至少为充分条件，充要条件更佳；
            2. 在现有基础上能够实现且便于计算；

        """
        # 根据dt、gt倾斜程度进行x or y 方向对齐 (不过，x or y方向的对齐仅在车道线几乎平行y or x 轴有效， 没必要)
        dt_lane, gt_lane = np.array(dt_lane), np.array(gt_lane)
        # from IPython import embed;embed()
        # 对dt、gt 首尾在x轴上对齐->
        dt_lane_st, gt_lane_st = dt_lane[0][0], gt_lane[0][0]
        dt_lane_en, gt_lane_en = dt_lane[-1][0], gt_lane[-1][0]

        dt_keep_idx = np.ones(dt_lane.shape[0])
        dt_keep_idx *= np.bitwise_and(
            dt_lane[:, 0] <= min(dt_lane_en, gt_lane_en),
            dt_lane[:, 0] >= max(dt_lane_st, gt_lane_st),
        )
        align_dt = dt_lane[dt_keep_idx > 0]

        gt_keep_idx = np.ones(gt_lane.shape[0])
        gt_keep_idx *= np.bitwise_and(
            gt_lane[:, 0] <= min(dt_lane_en, gt_lane_en),
            gt_lane[:, 0] >= max(dt_lane_st, gt_lane_st),
        )
        align_gt = gt_lane[gt_keep_idx > 0]
        return align_dt, align_gt

    def get_common_dt_gt(self, dt_lane, gt_lane):
        dt_lane_x = dt_lane[:, 0]
        gt_lane_x = gt_lane[:, 0]
        dt_lane_x_min, dt_lane_x_max = dt_lane_x.min(), dt_lane_x.max()
        gt_lane_x_min, gt_lane_x_max = gt_lane_x.min(), gt_lane_x.max()
        x_min = max(dt_lane_x_min, gt_lane_x_min)
        x_max = min(dt_lane_x_max, gt_lane_x_max)
        dt_lane_valid = np.bitwise_and(dt_lane_x >= x_min, dt_lane_x <= x_max)
        gt_lane_valid = np.bitwise_and(gt_lane_x >= x_min, gt_lane_x <= x_max)
        return_dt_lane, return_gt_lane = dt_lane[dt_lane_valid], gt_lane[gt_lane_valid]

        # x方向按0.1划分网格，每个区间内仅保留一个点
        first_x = -1
        save_flag = np.ones(return_dt_lane[:, 0].shape, dtype="bool")
        for i, x in enumerate(return_dt_lane[:, 0]):
            x_10cm = x // 0.1
            if x_10cm == first_x:
                save_flag[i] = False
            else:
                first_x = x_10cm
        return_dt_lane = return_dt_lane[save_flag]
        # print(f" DT: {save_flag.shape[0] - np.count_nonzero(save_flag)}")

        first_x = -1
        save_flag = np.ones(return_gt_lane[:, 0].shape, dtype="bool")
        for j, x in enumerate(return_gt_lane[:, 0]):
            x_10cm = x // 0.1
            if x_10cm == first_x:
                save_flag[j] = False
            else:
                first_x = x_10cm
        return_gt_lane = return_gt_lane[save_flag]
        # print(f" GT: {save_flag.shape[0] - np.count_nonzero(save_flag)}")

        return return_dt_lane, return_gt_lane

    def _single_lane_cd(self, dt_lane, gt_lane):
        if self.use_common_dt_gt:
            dt_lane_common, gt_lane_common = self.get_common_dt_gt(dt_lane, gt_lane)
            if len(dt_lane_common) and len(gt_lane_common):
                dt_lane, gt_lane = dt_lane_common, gt_lane_common
        # assert dt_lane.shape[1] == gt_lane.shape[1], "dims must be same"

        if dt_lane.shape[1] == 2:
            dt_lane = np.concatenate([dt_lane, np.zeros((dt_lane.shape[0], 1))], axis=1)
            gt_lane = np.concatenate([gt_lane, np.zeros((gt_lane.shape[0], 1))], axis=1)
        dt_lane = torch.FloatTensor(dt_lane).cuda().unsqueeze(0)  # (1, M, 3)
        gt_lane = torch.FloatTensor(gt_lane).cuda().unsqueeze(0)  # (1, N, 3)
        d2g_xyz, d2g_ids, g2d_xyz, g2d_ids = self.chamfer_dist(dt_lane, gt_lane)
        dt_lane, d2g_xyz, d2g_ids = dt_lane[0], d2g_xyz[0], d2g_ids[0].long()
        gt_lane, g2d_xyz, g2d_ids = gt_lane[0], g2d_xyz[0], g2d_ids[0].long()

        dist_xyz, dist_xy, dist_xz = 0, 0, 0
        if self.mode in ["d->g", "d<->g"]:
            x_decay_w = self._get_weights(dt_lane[:, 0])
            dist_xyz += (d2g_xyz * x_decay_w).mean()
            dist_xy += self._get_dist(dt_lane[:, [0, 1]], gt_lane[d2g_ids][:, [0, 1]], x_decay_w)
            dist_xz += self._get_dist(dt_lane[:, [0, 2]], gt_lane[d2g_ids][:, [0, 2]], x_decay_w)

        if self.mode in ["g->d", "d<->g"]:
            x_decay_w = self._get_weights(gt_lane[:, 0])
            dist_xyz += (g2d_xyz * x_decay_w).mean()
            dist_xy += self._get_dist(dt_lane[g2d_ids][:, [0, 1]], gt_lane[:, [0, 1]], x_decay_w)
            dist_xz += self._get_dist(dt_lane[g2d_ids][:, [0, 2]], gt_lane[:, [0, 2]], x_decay_w)

        return dist_xy.cpu().detach(), dist_xz.cpu().detach(), dist_xyz.cpu().detach()

    def _get_dist(self, src_lane, tgt_lane, x_decay_w):
        assert src_lane.shape == tgt_lane.shape
        distance = torch.norm(src_lane - tgt_lane, dim=1, p=2, keepdim=True).squeeze(1)
        kept_ids = torch.argsort(distance)[: int(src_lane.shape[0] * self.keep_ratio)]
        return (distance * x_decay_w)[kept_ids].mean()

    def _get_weights(self, lane_x):
        decay_weights = torch.ones_like(lane_x).cuda()
        if self.weights is not None and self.regions is not None:
            diff = lane_x.unsqueeze(1).unsqueeze(2) - self.regions
            cond = torch.bitwise_and(diff[:, :, 0] >= 0, diff[:, :, 1] < 0)
            cond[cond.sum(1) == 0, -1] = True
            indices = torch.where(cond)[1]
            decay_weights = self.weights[indices]
        assert lane_x.shape[0] == decay_weights.shape[0]
        return decay_weights
