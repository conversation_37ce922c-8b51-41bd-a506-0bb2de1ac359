"""
简化版可视化, 用于训练阶段可视化检查
"""
import copy
import math
import refile
import torch
import random
import cv2
import json
import numpy as np
from scipy.interpolate import splprep, splev

from pyquaternion import Quaternion
from shapely.geometry import LineString, MultiPoint, box
from collections import defaultdict


# 色卡见：https://oss.iap.hh-d.brainpp.cn/cjz-share/color_map/color_card.png
color_gt = (0, 255, 100)  # 深绿色
color_solid = (100, 255, 0)  # 浅绿色
color_dotted = (0, 255, 100)  # 深绿色
color_curb = (0, 0, 250)  # 红色
color_rail = (0, 0, 250)  # 红色
color_diversion = (150, 0, 250)  # 洋红色
color_virtual = (200, 200, 200)  # 灰色

color_crosswalk = (255, 0, 0)  # 蓝色
color_stopline = (0, 0, 250)  # 红色

color_noparking = (32, 240, 160)  # 紫色
color_arrow = (255, 255, 100)  # 浅蓝色
color_entrance = (100, 150, 255)  # 橘色
color_unknow = (255, 255, 255)  # 白色

color_info = (100, 255, 255)  # 黄色

font = cv2.FONT_HERSHEY_SIMPLEX
fontScale = 2
thickness = 2
line_thickness = 3


COLOR_DICT = {
    0: (255, 0, 0),  # laneline
    1: (0, 0, 255),  # curb
}


def get_unnorm_img(img_0):
    mean = [123.675, 116.28, 103.53]
    std = [58.395, 57.12, 57.375]
    img_0[:, :, 0] = np.uint8((img_0[:, :, 0] * std[0]) + mean[0])
    img_0[:, :, 1] = np.uint8((img_0[:, :, 1] * std[1]) + mean[1])
    img_0[:, :, 2] = np.uint8((img_0[:, :, 2] * std[2]) + mean[2])
    img_0 = np.uint8(np.ascontiguousarray(img_0))
    return img_0


def get_interpolate_number(points, inter_dis=1):
    """
    累加弦长计算插值点数目
    """
    length = 0
    for i in range(len(points) - 1):
        # utm_x1, utm_y1 = utm.from_latlon(nodes[i].lat, nodes[i].lon)[:2]
        # utm_x2, utm_y2 = utm.from_latlon(nodes[i + 1].lat, nodes[i + 1].lon)[:2]
        utm_x1, utm_y1 = float(points[i][0]), float(points[i][1])
        utm_x2, utm_y2 = float(points[i + 1][0]), float(points[i + 1][1])
        length += math.sqrt((utm_x1 - utm_x2) ** 2 + (utm_y1 - utm_y2) ** 2)
    return math.ceil(length / inter_dis)


def interp_lines(points, inter_dis=0.1):
    order = 2
    if len(points) == 2:
        order = 1
    points = np.array(points)
    inter_num = get_interpolate_number(points, inter_dis)
    if inter_num <= points.shape[0]:
        return points
    tck, u = splprep([points[:, 0], points[:, 1], points[:, 2]], k=order, s=0)
    unew = np.linspace(0, 1, inter_num)
    out = splev(unew, tck)
    rebuild_pts = np.array([out[0][:], out[1][:], out[2][:]]).T
    return rebuild_pts


def vis_rv_idaimg(
    img_i, bs_map_dt, map_gt, map_attr, post_rot_bda, tran_mats_dict, img_metas, map_lidar_range, index_in_dataset_list
):
    bs, n_f, n_cam, _, h, w = img_i.shape
    f_i = 0  # 仅一帧
    vis_thr = 0.4  # todo: hardcode

    bs_cat_img_list = []
    for b_i in range(bs):
        tmp_rot_bda = post_rot_bda[b_i][f_i].cpu()
        map_dt = bs_map_dt[b_i]  # 取第b_i个dt
        gt_img_list = []
        dt_img_list = []
        # cat_img_list = []
        for cam_id in range(n_cam):
            tmp_img = img_i[b_i][f_i][cam_id]  # (3, 512, 960)
            tmp_img = get_unnorm_img(tmp_img.clone().permute(1, 2, 0).cpu().numpy())
            gt_img = tmp_img.copy()
            dt_img = tmp_img.copy()

            # get mats
            tmp_tran_mats_dict = tran_mats_dict[b_i][f_i]
            cam2img = tmp_tran_mats_dict["cam2img"][cam_id].clone().cpu().numpy()
            homo_cam2img = np.eye(4, 4)
            homo_cam2img[:3, :3] = cam2img
            cam2img = homo_cam2img
            # ego2cam = tmp_tran_mats_dict["ego2cam"][cam_id].clone().cpu().numpy()
            # lidar2ego = tmp_tran_mats_dict["lidar2ego"][cam_id].clone().cpu().numpy()

            ida_mat = img_metas[b_i]["ida_mats"][f_i][cam_id]
            lidar2img = img_metas[b_i]["lidar2imgs"][f_i][cam_id]

            # get lidar gt
            tmp_lidar_gt = map_gt[b_i][f_i]
            # bev_img = np.zeros((1000, 600, 3))
            for cls_id, ins_points in tmp_lidar_gt.items():
                if cls_id not in [0, 1]:
                    # 先只画line
                    continue
                for ins_id, points in enumerate(ins_points):
                    pts = np.array(points[0])  # permute 2
                    # line_attr = map_attr[b_i][f_i][cls_id][ins_id]
                    pts_recover = pts * (np.array(map_lidar_range[3:6]) - np.array(map_lidar_range[:3])) + np.array(
                        map_lidar_range[:3]
                    )  # unnorm
                    # lidar_pts = interp_lines(pts_recover)  # 插值
                    lidar_pts = pts_recover
                    homo_lidar_pts = np.concatenate([lidar_pts, np.ones((lidar_pts.shape[0], 1))], axis=-1)

                    # lidar2img = img_metas[0]['ida_mats']@img_metas[0]["lidar2imgs"] # (B, 1, 4, 4, 4)
                    # lidar2img = cam2img @ ego2cam @ lidar2ego
                    lidar2img = torch.from_numpy(lidar2img).float()
                    lidar2img = torch.inverse(tmp_rot_bda @ torch.inverse(lidar2img))  # add bda, (B, 1, 4, 4, 4)
                    lidar2img = lidar2img.numpy()
                    homo_img_pts = (lidar2img @ homo_lidar_pts.T).T
                    homo_img_pts[:, :2] = homo_img_pts[:, :2] / homo_img_pts[:, 2:3]
                    homo_idaimg_pts = (ida_mat @ homo_img_pts.T).T
                    for pt_idx, pt in enumerate(homo_idaimg_pts[:-1, :]):
                        start_pt = homo_idaimg_pts[pt_idx]
                        end_pt = homo_idaimg_pts[pt_idx + 1]
                        if start_pt[2] < 0 or end_pt[2] < 0:
                            continue
                        try:
                            cv2.line(
                                gt_img,
                                (int(start_pt[0]), int(start_pt[1])),
                                (int(end_pt[0]), int(end_pt[1])),
                                COLOR_DICT[cls_id],
                                2,
                            )
                            cv2.circle(gt_img, (int(start_pt[0]), int(start_pt[1])), 6, (0, 255, 255), -1)
                        except cv2.error as e:
                            print("vis_rv_ida img exception")
                            print(e)
                            continue

                    # # bev 可视化
                    # for point_idx in range(len(pts)-1):
                    #     point = pts[point_idx]
                    #     x_int, y_int = round(point[0] * 600), round(point[1] * 1000)
                    #     next_x_int, next_y_int = round(pts[point_idx+1][0] * 600), round(pts[point_idx+1][1] * 1000)

                    #     cv2.line(bev_img, (x_int, y_int), (next_x_int, next_y_int), (0, 255, 255), 2)
                    #     cv2.circle(bev_img, (x_int, y_int), 4, (0, 0, 255), -1)
            gt_img_list.append(gt_img)

            # get lidar dt
            tmp_lidar_dt = map_dt["lines"]  # (num_query, num_pts, coord_dim)
            for ins_id, ins_points in enumerate(tmp_lidar_dt):
                ins_score = map_dt["line_scores"][ins_id]
                if ins_score < vis_thr:
                    continue
                print(f"ins id: {ins_id}, score: {ins_score}")
                pts = ins_points
                # line_attr = map_attr[b_i][f_i][cls_id][ins_id]
                pts_recover = pts * (np.array(map_lidar_range[3:6]) - np.array(map_lidar_range[:3])) + np.array(
                    map_lidar_range[:3]
                )  # unnorm
                # lidar_pts = interp_lines(pts_recover)  # 插值
                lidar_pts = pts_recover  # 不插值
                homo_lidar_pts = np.concatenate([lidar_pts, np.ones((lidar_pts.shape[0], 1))], axis=-1)

                # lidar2img = img_metas[0]['ida_mats']@img_metas[0]["lidar2imgs"] # (B, 1, 4, 4, 4)
                # lidar2img = cam2img @ ego2cam @ lidar2ego
                lidar2img = torch.from_numpy(lidar2img).float()
                lidar2img = torch.inverse(tmp_rot_bda @ torch.inverse(lidar2img))  # add bda, (B, 1, 4, 4, 4)
                lidar2img = lidar2img.numpy()
                homo_img_pts = (lidar2img @ homo_lidar_pts.T).T
                #
                # homo_ego_pts = (lidar2ego @ homo_lidar_pts.T).T
                # homo_cam_pts = (ego2cam @ homo_ego_pts.T).T
                # homo_img_pts = (cam2img @ homo_cam_pts.T).T
                homo_img_pts[:, :2] = homo_img_pts[:, :2] / homo_img_pts[:, 2:3]
                homo_idaimg_pts = (ida_mat @ homo_img_pts.T).T
                for pt_idx, pt in enumerate(homo_idaimg_pts[:-1, :]):
                    # 当前点的属性预测
                    # cls_id = dt_attr[ins_id][pt_idx][3] > 0  # 1/2为路沿栏杆, 0为车道线
                    cls_id = 0

                    start_pt = homo_idaimg_pts[pt_idx]
                    end_pt = homo_idaimg_pts[pt_idx + 1]
                    if start_pt[2] < 0 or end_pt[2] < 0:
                        continue
                    try:
                        cv2.line(
                            dt_img,
                            (int(start_pt[0]), int(start_pt[1])),
                            (int(end_pt[0]), int(end_pt[1])),
                            COLOR_DICT[cls_id],
                            2,
                        )
                    except cv2.error as e:
                        print(e)
                        continue
                    # 在结束点附近写上id
                    if pt_idx == len(homo_idaimg_pts[:-1, :]) - 2:
                        cv2.putText(
                            dt_img,
                            f"{ins_id}",
                            (int(end_pt[0]), int(end_pt[1])),
                            cv2.FONT_HERSHEY_SIMPLEX,
                            2,
                            COLOR_DICT[cls_id],
                            4,
                        )

            dt_img_list.append(dt_img)

        # tmp_vis
        gt_img_vis = np.concatenate(gt_img_list, axis=1)
        dt_img_vis = np.concatenate(dt_img_list, axis=1)
        cat_img = np.concatenate([gt_img_vis, dt_img_vis], axis=0)
        bs_cat_img_list.append(cat_img)
    return bs_cat_img_list


class Visual_Map_Data_Callback_Maptr:
    def __init__(self):
        pass

    def vis_train_data(self, data_dict, pred_maps):
        """
        在ida去畸变图上可视化dt/gt
        """
        img_i = data_dict["imgs"].clone().cpu()  # torch.Size([6, 1, 4, 3, 512, 960])
        # ida_mats = data_dict["img_metas"][0]["ida_mats"][0]  # (4, 4, 4)
        tran_mats_dict = data_dict["tran_mats_dict"]  # bs-list: dict()
        map_gt = data_dict["map_gt"]  # bs-list: ts-list : cls-dict()
        map_attr = data_dict["map_attr"]  # bs-list: ts-list : cls-dict()
        map_lidar_range = data_dict["map_tran_mats_dict"][0][0]["map_lidar_range"]
        map_dt = pred_maps  # pred
        index_in_dataset_list = data_dict["index_in_dataset"]  # dataset id

        post_rot_bda = data_dict["post_rot_bda"]  # bs-list: ts-list:
        img_metas = data_dict["img_metas"]  # (4, 4, 4)
        # vis gt on undistort img
        # img_metas = data_dict["img_metas"][0]
        # img_metas["lidar2imgs"]
        # import pdb; pdb.set_trace()
        bs_cat_img_list = vis_rv_idaimg(
            img_i,
            map_dt,
            map_gt,
            map_attr,
            post_rot_bda,
            tran_mats_dict,
            img_metas,
            map_lidar_range,
            index_in_dataset_list,
        )
        return bs_cat_img_list, index_in_dataset_list

    def vis_data(self, gt_item, dt_item=None, do_undistort=False):
        f_idx = -1  # 20241014: 当前只支持可视化最后一帧
        nori_id = gt_item["nori_id"]
        imgs = gt_item["ori_imgs"]  # [bs, num_frame, N, C, H, W ]
        bs, n_f, n_cam = imgs.shape[:3]

        final_imgs_rv_list, final_imgs_dt_list, final_imgs_seg_list, final_imgs_bev_list = [], [], [], []
        final_bev_dt_list = []
        debug_dts = []
        for b_i in range(bs):
            tmp_imgs = imgs[b_i][f_idx]
            cam_num = tmp_imgs.shape[0]
            # 1. vis_gt
            cam_names = [
                "cam_front_120",
                "cam_front_30",
                "cam_front_left_120",
                "cam_front_right_120",
            ]  # 需要和输入的 cam 顺序对齐
            cam_names = cam_names[:cam_num]
            if self.warp_tgt_json is not None:  # warp 模式用  target 的内外参做投影, 注意两阶段可以在线算 warp，所以测试集可以有多车型；单阶段只能有单车型
                calibrated_sensors = json.load(refile.smart_open(self.warp_tgt_json))["calibrated_sensors"]
                lidar2cams, cam2imgs = [], []
                distort_mode = []  # fisheye / pinhole  etc
                for cam in cam_names:
                    K = np.array(calibrated_sensors[cam]["intrinsic"]["K"])
                    ext_R = calibrated_sensors[cam]["extrinsic"]["transform"]["rotation"]
                    ext_t = calibrated_sensors[cam]["extrinsic"]["transform"]["translation"]
                    lidar2cam = self.transform_matrix(
                        rotation=Quaternion([ext_R["w"], ext_R["x"], ext_R["y"], ext_R["z"]]),
                        translation=[ext_t["x"], ext_t["y"], ext_t["z"]],
                        inverse=False,
                    )
                    reso = calibrated_sensors[cam]["intrinsic"]["resolution"]  # 原始 reso  [3840, 2160]
                    ratio = self.tgt_resolution[0] / reso[0]
                    K[:2] = K[:2] * ratio
                    lidar2cams.append(lidar2cam)
                    cam2imgs.append(K)
                    distort_mode.append(calibrated_sensors[cam]["intrinsic"]["distortion_model"])
                cam_resolutions = gt_item["map_tran_mats_dict"][b_i][f_idx]["cam_resolution"]
                cam_undistorts = gt_item["map_tran_mats_dict"][b_i][f_idx]["cam_undistort_list"]
                if self.onestage_combine_maps is None:  # 两阶段 warp
                    tmp_imgs = self.img_undistort(
                        tmp_imgs, cam2imgs, cam_undistorts, cam_resolutions, distort_mode
                    )  # 两阶段 warp 时，这里的 tmp_imgs 是已经 warp 过的
                else:
                    combine_maps = dict(np.load(self.onestage_combine_maps))
                    map1_new, map2_new = combine_maps[cam]
                    new_tmp_imgs = []
                    for kk in tmp_imgs:
                        kk = kk.permute(1, 2, 0).cpu().numpy()
                        new_tmp_imgs.append(
                            cv2.remap(kk, map1_new.astype(np.float32), map2_new.astype(np.float32), cv2.INTER_LINEAR)
                        )  # 这里对应的就是warp完且去完畸变的图，也就是车上单阶段处理后的图
                    tmp_imgs = new_tmp_imgs
            else:
                lidar2cams = gt_item["map_tran_mats_dict"][b_i][f_idx]["lidar2cam"]
                cam2imgs = gt_item["map_tran_mats_dict"][b_i][f_idx]["cam2img"]
                cam_resolutions = gt_item["map_tran_mats_dict"][b_i][f_idx]["cam_resolution"]
                cam_undistorts = gt_item["map_tran_mats_dict"][b_i][f_idx]["cam_undistort_list"]
                lidar2cams = [lidar2cam.cpu().numpy() for lidar2cam in lidar2cams]
                distort_mode = ["fisheye", "pinhole", "fisheye", "fisheye"]
                tmp_imgs = self.img_undistort(tmp_imgs, cam2imgs, cam_undistorts, cam_resolutions, distort_mode)

            map_lidar_range = np.array(gt_item["map_tran_mats_dict"][b_i][f_idx]["map_lidar_range"])
            map_gt, map_attr = self.recover_line(
                gt_item["map_gt"][b_i][f_idx], gt_item["map_attr"][b_i][f_idx], map_lidar_range
            )  # Dcit("cls_id": [Num_ins, P_num, pt_num, xyz]) -> Dcit("cls_id": [Num_ins, pt_num, xyz])

            vis_inter_pixel = 10  # 远端点投影后过于密集，所以如果相邻点 uv 距离大于这个值才把点画上去, 线型按模型直出的点来画
            vis_cam_id = [0, 1, 2, 3]
            imgs_rv_list = self.vis_rv(
                tmp_imgs,
                lidar2cams,
                cam2imgs,
                cam_resolutions,
                map_gt,
                vis_cam_id=vis_cam_id,
                vis_inter_pixel=vis_inter_pixel,
                map_attr=map_attr,
            )
            # img_semantic_seg = gt_item["img_semantic_seg"][b_i][f_idx]                             # cam_num, cls_num, H, W
            # imgs_seg_list = self.vis_seg(tmp_imgs, img_semantic_seg, cam_resolutions)
            imgs_seg_list = []
            imgs_bev = self.vis_bev(map_gt, map_lidar_range, vis_shape=(400, 210), inter_w=2, inter_h=5)

            final_imgs_rv_list.append(imgs_rv_list)
            final_imgs_seg_list.append(imgs_seg_list)
            final_imgs_bev_list.append(imgs_bev)

            # 2. vis dt
            if dt_item:  # 最后一帧的 dt
                this_item = dt_item[b_i]  # 为了包成一个后处理函数map_tran_mats_dict
                map_dt, map_dt_attr = self.post_process(this_item, map_lidar_range)

                imgs_dt_list = self.vis_rv(
                    tmp_imgs,
                    lidar2cams,
                    cam2imgs,
                    cam_resolutions,
                    map_dt,
                    vis_cam_id=vis_cam_id,
                    vis_inter_pixel=vis_inter_pixel,
                    map_attr=map_dt_attr,
                )
                imgs_bev_dt = self.vis_bev(map_dt, map_lidar_range, vis_shape=(400, 210), inter_w=2, inter_h=5)
                final_bev_dt_list.append(imgs_bev_dt)
                debug_dts.append(map_dt)
                self.add_dt_data(b_i, nori_id, map_dt, map_dt_attr)
            else:
                imgs_dt_list = []
            final_imgs_dt_list.append(imgs_dt_list)
        return (
            final_imgs_rv_list,
            final_imgs_dt_list,
            final_imgs_seg_list,
            final_imgs_bev_list,
            debug_dts,
            final_bev_dt_list,
        )

    def add_dt_data(self, b_i, nori_id, map_dt, map_dt_attr):
        map_dt_str, map_dt_attr_str = {}, {}
        if not isinstance(map_dt, dict):
            map_dt = {}
        if not isinstance(map_dt_attr, dict):
            map_dt_attr = {}
        for k, v in map_dt.items():
            map_dt_str[str(k)] = v
        for k, v in map_dt_attr.items():
            map_dt_attr_str[str(k)] = v
        self.dt_dict[nori_id[b_i]] = {
            "map_dt": map_dt_str,
            "map_dt_attr": map_dt_attr_str,
        }

    def save_dt_data(self, out_put_dir):
        save_path = refile.smart_path_join(out_put_dir, "dt_data.json")
        with refile.smart_open(save_path, "w") as f:
            json.dump(self.dt_dict, f)

    @staticmethod
    def transform_matrix(
        translation: np.ndarray = np.array([0, 0, 0]),
        rotation: Quaternion = Quaternion([1, 0, 0, 0]),
        inverse: bool = False,
    ) -> np.ndarray:
        tm = np.eye(4)
        if inverse:
            rot_inv = rotation.rotation_matrix.T
            trans = np.transpose(-np.array(translation))
            tm[:3, :3] = rot_inv
            tm[:3, 3] = rot_inv.dot(trans)
        else:
            tm[:3, :3] = rotation.rotation_matrix
            tm[:3, 3] = np.transpose(np.array(translation))
        return tm

    # 绘制虚线函数
    @staticmethod
    def draw_dashed_line(img, start_point, end_point, color, thickness=1, dash_length=10):
        # 计算起点到终点的向量
        line_length = np.linalg.norm(np.array(end_point) - np.array(start_point))
        # 计算虚线段的个数
        dashes = max(int(line_length // dash_length), 1)
        # 计算每个虚线段的增量
        for i in range(dashes + 1):
            start = (
                int(start_point[0] + (end_point[0] - start_point[0]) * i / dashes),
                int(start_point[1] + (end_point[1] - start_point[1]) * i / dashes),
            )
            end = (
                int(start_point[0] + (end_point[0] - start_point[0]) * (i + 0.5) / dashes),
                int(start_point[1] + (end_point[1] - start_point[1]) * (i + 0.5) / dashes),
            )
            # 画线段
            cv2.line(img, start, end, color, thickness)

    def xyz_to_uv_depth_new(self, proj_matrix, K, xyz_coords, img_size, type="lane", vis_inter_pixel=0):
        """
        Transform world / ego xyz coordinates to img uv and depth
        args:
            proj_matrix: numpy.array,
            K: camera intrinsics, 3 x 3
            xyz_coords: numpy.array, n x 3
            img_size: (h, w)
            vis_inter_pixel: 间隔多长画一个点

        return:
            [[u, v, depth], [u, v, depth], ... ]
        """
        new_lane = np.ones((np.array(xyz_coords).shape[0], 4))
        new_lane[:, :3] = xyz_coords
        try:
            cam_coords = (proj_matrix.cpu() @ new_lane.T).T  # cam 坐标系下点 [3, n]
        except Exception as e:
            print(e)
            cam_coords = (proj_matrix @ new_lane.T).T  #
        idxes = np.array(range(len(cam_coords)))

        idxes = idxes[cam_coords[:, 2] > 0]
        cam_coords = cam_coords[cam_coords[:, 2] > 0]

        if not len(cam_coords) > 1:
            return [], [], []

        # K = cam_coords.new_tensor(K)
        uv_line = (K @ cam_coords[:, :3].T).T
        if not len(uv_line) > 1:
            return [], []
        uv_line = uv_line / uv_line[:, 2:3]
        uv_pts = uv_line[:, :2].round().astype(np.int32)

        filtered_line_pts_after = []
        for i, pt in enumerate(uv_pts):
            if i == 0 or i == len(uv_pts) - 1:
                filtered_line_pts_after.append(pt)
            elif abs(pt[1] - filtered_line_pts_after[-1][1]) >= vis_inter_pixel:
                filtered_line_pts_after.append(pt)
        assert len(uv_pts) == len(idxes)
        return uv_pts, filtered_line_pts_after, idxes

    def img_undistort(self, imgs, Ks, Ds, cam_sizes, distort_mode):
        img_undistort_list = []
        for cam_id in range(len(cam_sizes)):
            if torch.is_tensor(imgs):
                img = imgs[cam_id].cpu().numpy()
            else:
                img = imgs[cam_id]
            img = img.transpose(1, 2, 0).astype(np.uint8)
            img = self.single_img_undistort(img, Ks[cam_id], Ds[cam_id], cam_sizes[cam_id], distort_mode[cam_id])
            img_undistort_list.append(img)
        return img_undistort_list

    def single_img_undistort(self, img, K, D, cam_size, mode):
        if isinstance(K, list):
            K = np.array(K)
        if isinstance(D, list):
            D = np.array(D)
        if mode == "fisheye":
            mapx, mapy = cv2.fisheye.initUndistortRectifyMap(K, D, None, K, tuple(cam_size), 5)
        elif mode == "pinhole":
            mapx, mapy = cv2.initUndistortRectifyMap(
                K,
                D,
                np.eye(3),
                K,
                tuple(cam_size),
                cv2.CV_16SC2,
            )
        else:
            raise NotImplementedError
        undistort_img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        return undistort_img

    def vis_rv(
        self, imgs, lidar2cams, cam2imgs, cam_resolutions, map_gt, vis_cam_id=[0], vis_inter_pixel=0, map_attr=None
    ):
        rv_img_list = []
        for cam_id in vis_cam_id:
            pt_img = cv2.resize(imgs[cam_id], tuple(cam_resolutions[cam_id]))
            pt_shape = tuple(pt_img.shape[:2])
            # *********************************** Add GT info to img *****************************************#
            blank_mask = np.zeros((pt_shape[0], pt_shape[1], 3), np.uint8)
            for cls_id, cls_dict in map_gt.items():
                for idx, line_pts in enumerate(cls_dict):
                    if not len(line_pts) > 1:
                        continue

                    if cls_id in [2, 6]:
                        line_direction, line_width = self.build_new_points_vectors(line_pts)

                        if cls_id in ["stopline", 2]:
                            target_color = color_stopline
                        elif cls_id in ["entrance", 6]:
                            target_color = color_entrance

                        uvs, filtered_uvs, uv_idxes = self.xyz_to_uv_depth_new(
                            lidar2cams[cam_id],
                            cam2imgs[cam_id],
                            line_width,
                            img_size=pt_shape,
                            type=cls_id,
                            vis_inter_pixel=vis_inter_pixel,
                        )
                        if len(uvs) > 0:
                            cv2.polylines(blank_mask, [uvs], True, target_color, thickness)
                        uvs, filtered_uvs, uv_idxes = self.xyz_to_uv_depth_new(
                            lidar2cams[cam_id],
                            cam2imgs[cam_id],
                            line_direction,
                            img_size=pt_shape,
                            type=cls_id,
                            vis_inter_pixel=vis_inter_pixel,
                        )
                        if len(uvs) > 0:
                            cv2.arrowedLine(blank_mask, tuple(uvs[0]), tuple(uvs[-1]), target_color)
                        for i, pt in enumerate(filtered_uvs):
                            if i == 1:
                                cv2.putText(
                                    blank_mask,
                                    str(idx),
                                    (pt[0] + random.randint(10, 30), pt[1] + random.randint(10, 30)),
                                    font,
                                    fontScale - 1,
                                    target_color,
                                    thickness - 1,
                                    cv2.LINE_AA,
                                )
                                cv2.circle(blank_mask, pt, 3, target_color, 3)

                    else:
                        # uvs, filtered_uvs = self.xyz_to_uv_depth(lidar2cams[cam_id], cam2imgs[cam_id], line_pts, img_size=pt_shape, type=cls_id, vis_inter_pixel=vis_inter_pixel)
                        uvs, filtered_uvs, uv_idxes = self.xyz_to_uv_depth_new(
                            lidar2cams[cam_id],
                            cam2imgs[cam_id],
                            line_pts,
                            img_size=pt_shape,
                            type=cls_id,
                            vis_inter_pixel=vis_inter_pixel,
                        )

                        if not len(uvs) > 1:
                            continue

                        uv = np.array(uvs)[:, :2].round().astype(np.int32)
                        if not len(filtered_uvs) > 1:
                            continue

                        filtered_uvs = np.array(filtered_uvs)[:, :2].round().astype(np.int32)

                        if cls_id in ["laneline", 0]:
                            target_color = color_gt
                        elif cls_id in ["curb", 1]:
                            target_color = color_curb
                        elif cls_id in ["crosswalk", 3]:
                            target_color = color_crosswalk
                        elif cls_id in ["arrow", 4]:
                            target_color = color_arrow
                        elif cls_id in ["noparking", 5]:
                            target_color = color_noparking
                        elif cls_id in ["mask", 7]:
                            target_color = color_unknow

                        if map_attr is not None and cls_id in ["laneline", "curb", 0, 1]:
                            for i, pt in enumerate(uv):
                                if i == 0:
                                    continue

                                attr_idx = uv_idxes[i]
                                attr = map_attr[cls_id][idx][attr_idx]
                                if cls_id in ["laneline", 0]:
                                    dashed, color, shape = attr[:3]
                                    if color == 1:  # 白线
                                        rgb = (128, 128, 128)  # 灰色
                                    elif color == 2:  # 黄线
                                        rgb = (255, 255, 0)  # 黄色
                                    else:
                                        rgb = (0, 0, 0)  # 黑色

                                    if dashed == 2:
                                        try:
                                            self.draw_dashed_line(
                                                blank_mask, uv[i - 1], pt, color=rgb, thickness=5, dash_length=100
                                            )
                                        except Exception as e:
                                            print(e)
                                            continue
                                    else:
                                        tmp_line = np.array([uv[i - 1].tolist(), pt.tolist()])
                                        cv2.polylines(blank_mask, [tmp_line], False, rgb, thickness=5)  # 默认实线
                                elif cls_id in ["curb", 1]:
                                    guardrail = attr[3]
                                    tmp_line = np.array([uv[i - 1].tolist(), pt.tolist()])
                                    if guardrail == 2:
                                        rgb = (255, 0, 255)
                                        cv2.polylines(blank_mask, [tmp_line], False, rgb, thickness=5)
                                    else:
                                        cv2.polylines(blank_mask, [tmp_line], False, target_color, thickness=5)
                        elif cls_id in ["arrow", 4]:
                            attr = map_attr[cls_id][idx][0]  # 使用arrow首点属性 vis
                            arrow_attr_id = attr[4]
                            arrow_direction = attr[5]
                            arrow_name = (
                                self.arrow_id_abbreviation_map[arrow_attr_id]
                                if arrow_attr_id in self.arrow_id_abbreviation_map
                                else "NAN"
                            )
                            cv2.polylines(blank_mask, [uv], True, target_color, thickness)
                            if arrow_direction in [1]:  # 正向 / 双向
                                arrow_name += "_FD"
                            if arrow_direction in [2]:  # 正向 / 双向
                                arrow_name += "_RE"
                            center_pt = np.mean(uv, axis=0)
                            cv2.putText(
                                blank_mask,
                                arrow_name,
                                [int(center_pt[0]), int(center_pt[1])],
                                font,
                                fontScale - 1,
                                color_info,
                                thickness - 1,
                                cv2.LINE_AA,
                            )
                        elif cls_id in ["crosswalk", "noparking", 3, 5]:
                            cv2.polylines(blank_mask, [uv], True, target_color, thickness)
                        else:
                            cv2.polylines(blank_mask, [uv], False, target_color, thickness)

                        for i, pt in enumerate(filtered_uvs):
                            if i == 1:
                                cv2.putText(
                                    blank_mask,
                                    str(idx),
                                    (pt[0] + random.randint(10, 30), pt[1] + random.randint(10, 30)),
                                    font,
                                    fontScale - 1,
                                    target_color,
                                    thickness - 1,
                                    cv2.LINE_AA,
                                )
                                cv2.circle(blank_mask, pt, 2, target_color, 2)
            pt_img = cv2.resize(pt_img, (pt_shape[1], pt_shape[0]))
            rv_img = cv2.addWeighted(pt_img, 1, blank_mask, 0.5, 0.0)
            rv_img_list.append(rv_img)
        return rv_img_list

    def vis_bev(self, map_gt, pc_range, vis_shape=(400, 210), inter_w=2, inter_h=5):
        """
        可视化 Lidar 坐标系下的车道线图像 (BEV 视角)，并绘制网格线。

        参数:
        - map_gt:
        - pc_range: (min_x, min_y, min_z, max_x, max_y, max_z) -> Lidar 坐标系的范围
        - y_range: (min_y, max_y) -> Lidar 坐标系的 y 方向范围
        - vis_shape: (h, w) -> 输出图像的尺寸
        - inter_w: 横向网格线间隔, 单位 m
        - inter_h: 纵向网格线间隔, 单位 m
        """
        img_h, img_w = vis_shape
        min_x, min_y, min_z, max_x, max_y, max_z = pc_range

        # 将 Lidar 坐标映射到图像坐标系
        def lidar_to_image_coords(line):
            # 映射 x, y 到图像像素坐标
            new_line = copy.deepcopy(line)
            new_line[:, 0] = (line[:, 0] - min_x) / (max_x - min_x) * img_w
            new_line[:, 1] = (1 - (line[:, 1] - min_y) / (max_y - min_y)) * img_h  # y 轴反转，向前的 y 越大，图像坐标越小
            new_line = new_line.astype(np.int32)
            return new_line

        # 创建空白图像，背景为黑色
        bev_image = np.ones((img_h, img_w, 3), dtype=np.uint8) * 255

        for cls_id, cls_dict in map_gt.items():
            for idx, line_pts in enumerate(cls_dict):
                if not len(line_pts) > 1:
                    continue

                closed = False
                if cls_id in [2, 6]:
                    line_direction, line_width = self.build_new_points_vectors(line_pts)

                    if cls_id in ["stopline", 2]:
                        target_color = color_stopline
                    elif cls_id in ["entrance", 6]:
                        target_color = color_entrance

                    # 绘制 line_direction
                    xy_coords = np.array(line_direction)[:, :2]
                    uvs = lidar_to_image_coords(xy_coords)
                    cv2.arrowedLine(bev_image, tuple(uvs[0]), tuple(uvs[-1]), target_color)
                    # 绘制 line_width
                    xy_coords = np.array(line_width)[:, :2]
                    uvs = lidar_to_image_coords(xy_coords)

                    cv2.polylines(bev_image, [uvs], closed, target_color, thickness=2)
                    for pt in uvs:
                        cv2.circle(bev_image, pt, 2, target_color, 2)

                else:
                    if cls_id in ["laneline", 0]:
                        target_color = color_gt
                        closed = False
                    elif cls_id in ["curb", 1]:
                        target_color = color_curb
                        closed = False
                    elif cls_id in ["stopline", 2]:
                        target_color = color_stopline
                        closed = False
                    elif cls_id in ["crosswalk", 3]:
                        target_color = color_crosswalk
                        closed = True
                    elif cls_id in ["arrow", 4]:
                        target_color = color_arrow
                        closed = True
                    elif cls_id in ["noparking", 5]:
                        target_color = color_noparking
                        closed = True
                    elif cls_id in ["entrance", 6]:
                        target_color = color_entrance
                        closed = True
                    elif cls_id in ["mask", 7]:
                        target_color = color_unknow
                        closed = True

                    xy_coords = np.array(line_pts)[:, :2]
                    uvs = lidar_to_image_coords(xy_coords)

                    cv2.polylines(bev_image, [uvs], closed, target_color, thickness=2)
                    for pt in uvs:
                        cv2.circle(bev_image, pt, 2, target_color, 2)

        # 绘制网格线
        # 纵向网格线
        inter_w = inter_w * img_w / (max_x - min_x)
        inter_h = inter_h * img_h / (max_y - min_y)
        for x in range(0, img_w, int(inter_w)):
            cv2.line(bev_image, (x, 0), (x, img_h), (229, 229, 229), 1)

        # 横向网格线
        for y in range(0, img_h, int(inter_h)):
            cv2.line(bev_image, (0, y), (img_w, y), (229, 229, 229), 1)

        car_pos1 = (int(img_w / 2), int(img_h) - 1)
        car_pos2 = (int(img_w / 2), int(img_h) - 10)
        cv2.line(bev_image, car_pos1, car_pos2, (100, 100, 100), 10)

        return bev_image

    def build_new_points_vectors(self, line_pts, length=10):
        start_pts = [line_pts[0], line_pts[2]]
        center_pt = np.mean(start_pts, axis=0)
        d1 = self.restore_vector(line_pts[1][0], line_pts[1][1], length=length)
        d2 = self.restore_vector(line_pts[3][0], line_pts[3][1], length=length)
        # d1_line = np.array([np.array(line_pts[0]), np.array(line_pts[0]) + d1])
        # d2_line = np.array([np.array(line_pts[2]), np.array(line_pts[2]) + d2])
        d_mean = (d1 + d2) / (np.linalg.norm(d1 + d2)) * length
        f = self.compute_normal_direction(d_mean)
        width = line_pts[1][2]
        line_direction = [center_pt, center_pt + d_mean]
        line_width = [center_pt + 0.5 * width * f, center_pt - 0.5 * width * f]
        return line_direction, line_width

    @staticmethod
    def restore_vector(sin_theta, cos_theta, length=1):
        vector = np.array([cos_theta, sin_theta, 0])
        vector = vector / np.linalg.norm(vector)
        vector = vector * length
        return vector

    @staticmethod
    def compute_normal_direction(direction):
        norm_direction = [-direction[1], direction[0], 0]  # RFU 法向量（仅考虑XY平面）选择Y轴(前向)正向的法向量
        return norm_direction / np.linalg.norm(norm_direction)  # 单位化

    def post_process(self, this_item, map_lidar_range):
        map_dt, map_dt_attr = defaultdict(list), defaultdict(list)
        for i, dt_cls in enumerate(this_item["line_labels"]):
            if this_item["line_scores"][i] >= self.map_class_dict_id[int(dt_cls)]["dt_scores_th"]:
                vectors = this_item["lines"][i]
                attrs = this_item["attrs_lines"][i]
                map_dt[dt_cls].append([vectors])
                map_dt_attr[dt_cls].append([attrs])

        for i, dt_cls in enumerate(this_item["stop_labels"]):
            if this_item["stop_scores"][i] >= self.map_class_dict_id[int(dt_cls)]["dt_scores_th"]:
                vectors = this_item["stops"][i]
                attrs = np.array([])
                map_dt[dt_cls].append([vectors])
                map_dt_attr[dt_cls].append([attrs])

        for i, dt_cls in enumerate(this_item["box_labels"]):
            if this_item["box_scores"][i] >= self.map_class_dict_id[int(dt_cls)]["dt_scores_th"]:
                vectors = this_item["boxes"][i]
                attrs = this_item["attrs_boxes"][i]
                map_dt[dt_cls].append([vectors])
                map_dt_attr[dt_cls].append([attrs])

        for i, dt_cls in enumerate(this_item["entrance_labels"]):
            if this_item["entrance_scores"][i] >= self.map_class_dict_id[int(dt_cls)]["dt_scores_th"]:
                vectors = this_item["entrances"][i]
                attrs = np.array([])
                map_dt[dt_cls].append([vectors])
                map_dt_attr[dt_cls].append([attrs])

        map_dt, map_dt_attr = self.recover_line(map_dt, map_dt_attr, map_lidar_range)
        return map_dt, map_dt_attr

    def recover_line(self, map_dict, map_attr_dict, map_lidar_range):
        recover_map_dict = {k: [] for k in list(map_dict.keys())}
        if len(map_attr_dict) == 0:
            recover_map_attr_dict = None
        else:
            recover_map_attr_dict = {k: [] for k in list(map_attr_dict.keys())}
        for cls_id, cls_dict in map_dict.items():
            # assert len(cls_dict) == len(map_attr_dict[cls_id])
            for idx, line_pts in enumerate(cls_dict):
                # for i, pts in enumerate(line_pts): # eval 仅保留每个permute第一个点组
                pts = np.array(line_pts[0])
                if cls_id in [2, 6]:
                    pts_norm = np.array([pts[0], pts[2]])
                    pts_rec = pts_norm * (np.array(map_lidar_range[3:6]) - np.array(map_lidar_range[:3])) + np.array(
                        map_lidar_range[:3]
                    )
                    width = pts[1, 2] * (map_lidar_range[3] - map_lidar_range[0])
                    pts_recover = np.array(
                        [
                            [pts_rec[0, 0], pts_rec[0, 1], pts_rec[0, 2]],
                            [self.denormalize_trig(pts[1, 0]), self.denormalize_trig(pts[1, 1]), width],
                            [pts_rec[1, 0], pts_rec[1, 1], pts_rec[1, 2]],
                            [self.denormalize_trig(pts[3, 0]), self.denormalize_trig(pts[3, 1]), width],
                        ]
                    )
                    recover_map_dict[cls_id].append(pts_recover.tolist())
                    if len(map_attr_dict) != 0:
                        attr_pts = np.array(map_attr_dict[cls_id][idx][0])
                        recover_map_attr_dict[cls_id].append(attr_pts.tolist())
                else:
                    pts_recover = pts * (np.array(map_lidar_range[3:6]) - np.array(map_lidar_range[:3])) + np.array(
                        map_lidar_range[:3]
                    )
                    recover_map_dict[cls_id].append(pts_recover.tolist())
                    if len(map_attr_dict) != 0:
                        attr_pts = np.array(map_attr_dict[cls_id][idx][0])
                        recover_map_attr_dict[cls_id].append(attr_pts.tolist())
        return recover_map_dict, recover_map_attr_dict

    @staticmethod
    def denormalize_trig(value):
        """
        将归一化后的值从 [0, 1] 还原到 [-1, 1]
        """
        return 2 * value - 1


class Undistort(object):
    def __init__(
        self,
        img_sizes,
        camera_ids,
        camera_calibr,
        # img_heigh,
        # img_width,
        # cam_calibration_path="s3://zhuqiong-oss-hhb/data/LaneDetection/CameraCalibration_0630_car_00002",
    ):
        # self.img_height = img_height
        # self.img_width = img_width
        # self.cam_calibration_path = cam_calibration_path
        self.img_sizes = img_sizes
        self.camera_calibr = camera_calibr
        self.camera_ids = camera_ids
        self.camera_params = {}
        # load camera params
        self.load_camera_params()

    def load_camera_params(self):
        """ Load camera calibration parameters only when initialization """
        for i, cam_id in enumerate(self.camera_ids):
            self.camera_params[cam_id] = {}

            # root_path = self.cam_calibration_path
            # cam_intrinsic_param_path = os.path.join(root_path, "camera_params",
            #                                         "{}_intrinsic.json".format(cam_id))
            # with smart_open(cam_intrinsic_param_path, "r") as f:
            #     cam_intrinsic_params = json.load(f)
            cam_intrinsic_params = self.camera_calibr[cam_id]["intrinsic"]

            mode = cam_intrinsic_params["distortion_model"]
            K = np.array(cam_intrinsic_params["K"], dtype=np.float32).reshape(3, 3)
            D = np.array(cam_intrinsic_params["D"], dtype=np.float32)
            self.camera_params[cam_id]["K"] = K
            self.camera_params[cam_id]["D"] = D
            self.camera_params[cam_id]["mode"] = mode

            # store undistort mapx,mapy
            if mode == "pinhole":
                mapx, mapy = cv2.initUndistortRectifyMap(K, D, None, K, (self.img_sizes[i][1], self.img_sizes[i][0]), 5)
                self.camera_params[cam_id]["mapx"] = mapx
                self.camera_params[cam_id]["mapy"] = mapy
            elif mode == "fisheye":
                mapx, mapy = cv2.fisheye.initUndistortRectifyMap(
                    K, D, None, K, (self.img_sizes[i][1], self.img_sizes[i][0]), 5
                )
                self.camera_params[cam_id]["mapx"] = mapx
                self.camera_params[cam_id]["mapy"] = mapy

    def __call__(self, img, cam_id):
        """
        Undistort img &lane points using stored camera params
        """
        mapx = self.camera_params[cam_id]["mapx"]
        mapy = self.camera_params[cam_id]["mapy"]
        # print(type(img),len(img),len(img[0]))
        undistort_img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        return undistort_img


def get_img_from_nid(fetcher, nid):
    try:
        ns = np.frombuffer(fetcher.get(nid), dtype=np.uint8)
        img = cv2.imdecode(ns, cv2.IMREAD_COLOR)
        return img
    except Exception as e:
        print(e)
        print(nid, " error")
        return None


def filter_and_intersect(pts_list, change_idx, z):
    for idx in change_idx:
        x1, y1, z1 = pts_list[idx]
        x2, y2, z2 = pts_list[idx + 1]
        x = (x1 - x2) / (z1 - z2 + 1e-6) * (z - z2) + x2
        y = (y1 - y2) / (z1 - z2 + 1e-6) * (z - z2) + y2
        if z1 < 0 and z2 > 0:
            # new_pts_list = [[x, y, z]] + pts_list[idx+1:]
            pts_list[idx] = [x, y, z]
        elif z1 > 0 and z2 < 0:
            pts_list[idx + 1] = [x, y, z]
    return pts_list


def xyz_to_uv_depth_pt(proj_matrix, K, xyz_coords, img_size):
    """
    Transform world / ego xyz coordinates to img uv and depth
    args:
        proj_matrix: numpy.array,
        K: camera intrinsics, 3 x 3
        xyz_coords: numpy.array, n x 3
        img_size: (h, w)

    return:
        [[u, v, depth], [u, v, depth], ... ]
    """
    h, w = img_size
    patch = box(0, 0, w - 1, h - 1)

    new_fork_pts = np.ones((xyz_coords.shape[0], 4))
    new_fork_pts[:, :3] = xyz_coords

    cam_coords = proj_matrix @ new_fork_pts.T  # cam 坐标系下点 [3, n]

    # if cam_coords[2, 0] > cam_coords[2, 1]:  # depth 由近到远 排序
    #     cam_coords = cam_coords[:, ::-1]
    # depth = cam_coords[2:3, :]
    #
    # change_idx = get_change_idx(depth[0])  # depth 由近到远
    # if change_idx is not None:
    #     cam_coords_tmp = filter_and_intersect(cam_coords[:3].T.tolist(), change_idx,
    #                                           z=1)  # magic num 1, 保证在相机前且不在图片内 在idx idx+1 之间插入一个值
    # else:
    cam_coords_tmp = cam_coords[:3].T  # [n, 3]
    cam_coords_tmp = np.array(cam_coords_tmp)
    cam_coords_tmp = cam_coords_tmp[cam_coords_tmp[:, 2] > 0]
    if cam_coords_tmp.shape[0] < 1:  # 都在相机后
        return []

    cam_coords = np.array(cam_coords_tmp).T  # [3, n]
    u_v = K @ cam_coords[:3, :]
    u_v = u_v[:2] / (u_v[2] + 1e-6)  # [2, n]

    cam_coords[:2] = u_v

    line_geom_before = MultiPoint(np.array(cam_coords.T))

    line_geom_after = line_geom_before.intersection(patch)

    coords_after = []
    if line_geom_after.geom_type == "MultiPoint":
        for geo_af in line_geom_after:
            coords_after.extend(geo_af.coords)
        line_geom_after = LineString(coords_after)

    line_pts_after = list(line_geom_after.coords)
    # try:
    #     line_pts_after = rectify_coords(linegeo_before=line_geom_before, linegeo_after=line_geom_after)  # list
    # except Exception as e:
    #     return []
    return line_pts_after


def get_change_idx(depth):
    """
    args:
        flags: eg. [False, False, True]
    return:
        change_index eg. 1
    """
    change_idx = []
    for i in range(len(depth) - 1):
        if depth[i] * depth[i + 1] < 0:
            change_idx.append(i)  # i 小于 0， i+1 对应>0
    return change_idx


if __name__ == "__main__":
    import numpy as np
    import cv2
    import webcolors

    # 将 BGR 转换为 RGB
    def bgr_to_rgb(bgr_color):
        return (bgr_color[2], bgr_color[1], bgr_color[0])

    # 找到最接近的颜色名称
    def closest_color(requested_color):
        min_colors = {}
        for key, name in webcolors.CSS3_HEX_TO_NAMES.items():
            r_c, g_c, b_c = webcolors.hex_to_rgb(key)
            rd = (r_c - requested_color[0]) ** 2
            gd = (g_c - requested_color[1]) ** 2
            bd = (b_c - requested_color[2]) ** 2
            min_colors[(rd + gd + bd)] = name
        return min_colors[min(min_colors.keys())]

    # 获取精确的颜色名称或最接近的名称
    def get_color_name(rgb_color):
        try:
            return webcolors.rgb_to_name(rgb_color)
        except ValueError:
            return closest_color(rgb_color)

    # 创建一个空白的色卡图像
    color_card = np.zeros((350, 800, 3), dtype=np.uint8)  # 增加高度为350以适应文字

    # 绘制不同类型的颜色矩形和对应的文字
    cv2.rectangle(color_card, (10, 10), (190, 70), color_gt, -1)
    cv2.putText(color_card, "GT", (10, 65), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (210, 10), (390, 70), color_solid, -1)
    cv2.putText(color_card, "Solid", (210, 65), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (410, 10), (590, 70), color_dotted, -1)
    cv2.putText(color_card, "Dotted", (410, 65), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (610, 10), (790, 70), color_curb, -1)
    cv2.putText(color_card, "Curb", (610, 65), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (10, 90), (190, 150), color_rail, -1)
    cv2.putText(color_card, "Rail", (10, 145), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (210, 90), (390, 150), color_diversion, -1)
    cv2.putText(color_card, "Diversion", (210, 145), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (410, 90), (590, 150), color_virtual, -1)
    cv2.putText(color_card, "Virtual", (410, 145), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (610, 90), (790, 150), color_crosswalk, -1)
    cv2.putText(color_card, "Crosswalk", (610, 145), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (10, 170), (190, 230), color_stopline, -1)
    cv2.putText(color_card, "Stopline", (10, 225), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (210, 170), (390, 230), color_noparking, -1)
    cv2.putText(color_card, "No Parking", (210, 225), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (410, 170), (590, 230), color_arrow, -1)
    cv2.putText(color_card, "Arrow", (410, 225), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (610, 170), (790, 230), color_entrance, -1)
    cv2.putText(color_card, "Entrance", (610, 225), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (10, 250), (190, 310), color_unknow, -1)
    cv2.putText(color_card, "Unknown", (10, 305), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.rectangle(color_card, (210, 250), (390, 310), color_info, -1)
    cv2.putText(color_card, "Info", (210, 305), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.imwrite("color_card.png", color_card)
