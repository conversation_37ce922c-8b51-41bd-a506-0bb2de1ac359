import cv2
import torch
import numpy as np
from PIL import Image, ImageDraw
import copy
from scipy.optimize import linear_sum_assignment


def draw_polylines(vecs, roi_size, origin, canvas_size, thickness=6):
    results = []
    for line_coords in vecs:
        canvas = np.zeros((canvas_size[1], canvas_size[0]), dtype=np.uint8)
        coords = (line_coords - origin) / roi_size
        coords = coords[..., :2] * torch.tensor(canvas_size)  # 只考虑 x y 不考虑 z
        coords = coords.numpy()
        cv2.polylines(canvas, np.int32(coords), False, color=1, thickness=thickness)
        result = np.flipud(canvas)
        if result.sum() < 20:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            result = cv2.dilate(result, kernel, iterations=1)
        results.append(result)
    return results


def draw_polygons(vecs, roi_size, origin, canvas_size):
    # TODO: 用城区数据 debug
    results = []
    for poly_coords in vecs:
        mask = Image.new("L", size=(canvas_size[0], canvas_size[1]), color=0)
        coords = (poly_coords - origin) / roi_size
        coords = coords[0, :, :2] * torch.tensor(canvas_size)  # 只考虑 x y 不考虑 z
        coords = coords.numpy()
        vert_list = [(x, y) for x, y in coords]
        if not (coords[0] == coords[-1]).all():
            vert_list.append(vert_list[0])
        ImageDraw.Draw(mask).polygon(vert_list, outline=1, fill=1)
        result = np.flipud(np.array(mask))
        if result.sum() < 20:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            result = cv2.dilate(result, kernel, iterations=1)
        results.append(result)
    return results


def draw_instance_masks(vectors, roi_size, origin, canvas_size):
    masks = {}
    for label, vecs in vectors.items():
        if label in [3, 4, 5, 6]:  # Note: 这里和 dataset config 里的 map_class_dict 中的编号要对应
            masks[label] = draw_polygons(vecs, roi_size, origin, canvas_size)
        else:
            masks[label] = draw_polylines(vecs, roi_size, origin, canvas_size, thickness=6)
    return masks


def _mask_iou(mask1, mask2):
    intersection = (mask1 * mask2).sum()
    if intersection == 0:
        return 0.0
    union = np.logical_or(mask1, mask2).sum()
    return intersection / union


def find_matchings(src_masks, tgt_masks, thresh=0.1):
    """Find the matching of map elements between two temporally
    connected frame

    Args:
        src_masks (_type_): instance masks of prev frame
        tgt_masks (_type_): instance masks of current frame
        thresh (float, optional): IOU threshold for matching. Defaults to 0.1.
    """
    matchings = {}
    for label, src_instances in src_masks.items():
        tgt_instances = tgt_masks[label]
        cost = np.zeros([len(src_instances), len(tgt_instances)])
        for i, src_ins in enumerate(src_instances):
            for j, tgt_ins in enumerate(tgt_instances):
                iou = _mask_iou(src_ins, tgt_ins)
                cost[i, j] = -iou
        row_ind, col_ind = linear_sum_assignment(cost)

        label_matching = [-1 for _ in range(len(src_instances))]
        label_matching_reverse = [-1 for _ in range(len(tgt_instances))]
        for i, j in zip(row_ind, col_ind):
            if -cost[i, j] > thresh:
                label_matching[i] = j
                label_matching_reverse[j] = i

        matchings[label] = (label_matching, label_matching_reverse)
    return matchings


def match_two_consecutive_frames(
    prev_lidar2global, prev_vectors, curr_lidar2global, curr_vectors, roi_size, origin, canvas_size
):
    # get relative pose
    prev_l2g_matrix = torch.from_numpy(prev_lidar2global)

    curr_l2g_rot = torch.from_numpy(curr_lidar2global)[:3, :3]
    curr_l2g_trans = torch.from_numpy(curr_lidar2global)[:3, 3]

    curr_g2l_matrix = torch.eye(4, dtype=torch.float64)
    curr_g2l_matrix[:3, :3] = curr_l2g_rot.T
    curr_g2l_matrix[:3, 3] = -(curr_l2g_rot.T @ curr_l2g_trans)

    prev2curr_matrix = curr_g2l_matrix @ prev_l2g_matrix

    # transform prev vectors
    prev2curr_vectors = dict()
    roi_size = torch.tensor(roi_size).reshape(1, 1, 3)
    origin = torch.tensor(origin).reshape(1, 1, 3)
    for label, vecs in prev_vectors.items():
        if len(vecs) > 0:
            vecs = np.stack(vecs, 0)
            vecs = torch.tensor(vecs)
            vecs = vecs[:, 0]  # 去掉 n_permute 维
            N, num_points, _ = vecs.shape
            denormed_vecs = vecs * roi_size + origin  # (num_prop, num_pts, 3)
            denormed_vecs = torch.cat(
                [denormed_vecs, denormed_vecs.new_ones((N, num_points, 1))], dim=-1  # 4-th dim
            )  # (num_prop, num_pts, 4)

            transformed_vecs = torch.einsum("lk,ijk->ijl", prev2curr_matrix, denormed_vecs.double()).float()
            normed_vecs = (transformed_vecs[..., :3] - origin) / roi_size  # (num_prop, num_pts, 2)
            normed_vecs = torch.clip(normed_vecs, min=0.0, max=1.0)
            prev2curr_vectors[label] = normed_vecs
        else:
            prev2curr_vectors[label] = vecs

    # convert to ego space for visualization
    new_curr_vectors = copy.deepcopy(curr_vectors)
    for label in prev2curr_vectors:
        if len(prev2curr_vectors[label]) > 0:
            prev2curr_vectors[label] = prev2curr_vectors[label] * roi_size + origin
        if len(curr_vectors[label]) > 0:
            curr_vecs = torch.tensor(np.stack(curr_vectors[label]))
            new_curr_vectors[label] = curr_vecs * roi_size + origin

    prev2curr_masks = draw_instance_masks(prev2curr_vectors, roi_size, origin, canvas_size)
    curr_masks = draw_instance_masks(new_curr_vectors, roi_size, origin, canvas_size)

    prev2curr_matchings = find_matchings(prev2curr_masks, curr_masks, thresh=0.01)

    # For viz purpose, may display the maps in perspective images
    # viz_dir = os.path.join(scene_dir, '{}_viz_perspective'.format(local_idx))
    # if not os.path.exists(viz_dir):
    #    os.makedirs(viz_dir)
    # renderer.render_camera_views_from_vectors(curr_vectors, imgs,
    #            cam_extrinsics, cam_intrinsics, ego2cams, 2, viz_dir)

    # renderer.render_bev_from_vectors(curr_vectors, out_dir=None, specified_path='cur.png')
    # renderer.render_bev_from_vectors(prev2curr_vectors, out_dir=None, specified_path='prev2cur.png')
    # from PIL import Image
    # background = Image.open("cur.png")
    # overlay = Image.open("prev2cur.png")
    # background = background.convert("RGBA")
    # overlay = overlay.convert("RGBA")
    # new_img = Image.blend(background, overlay, 0.5)
    # new_img.save("cur_overlapped.png","PNG")

    return prev2curr_matchings


def assign_global_ids(matchings_seq, vectors_seq):
    global_map_index = {key: 0 for key in vectors_seq[0].keys()}

    ids_seq = []
    ids_0 = dict()
    for label, vectors in vectors_seq[0].items():
        id_mapping = dict()
        for i, _ in enumerate(vectors):
            id_mapping[i] = global_map_index[label]
            global_map_index[label] += 1
        ids_0[label] = id_mapping
    ids_seq.append(ids_0)

    # Trace all frames following the consecutive matching
    for t, vectors_t in enumerate(vectors_seq[1:]):
        ids_t = dict()
        for label, vectors in vectors_t.items():
            reverse_matching = matchings_seq[t][label][1]
            id_mapping = dict()
            for i, _ in enumerate(vectors):
                if reverse_matching[i] != -1:
                    prev_id = reverse_matching[i]
                    global_id = ids_seq[-1][label][prev_id]
                else:
                    global_id = global_map_index[label]
                    global_map_index[label] += 1
                id_mapping[i] = global_id
            ids_t[label] = id_mapping
        ids_seq.append(ids_t)
    return ids_seq
