# encoding: utf-8
"""
@author: zeming li
@contact: <EMAIL>
"""
from torch import distributed as dist
import math
from tqdm import tqdm


def get_rank() -> int:
    if not dist.is_available():
        return 0
    if not dist.is_initialized():
        return 0
    return dist.get_rank()


def get_world_size() -> int:
    if not dist.is_available():
        return 1
    if not dist.is_initialized():
        return 1
    return dist.get_world_size()


def synchronize():
    """Helper function to synchronize (barrier) among all processes when using distributed training"""
    if not dist.is_available():
        return
    if not dist.is_initialized():
        return
    current_world_size = dist.get_world_size()
    if current_world_size == 1:
        return
    dist.barrier()


def reduce_sum(tensor, group=None):
    world_size = get_world_size()
    if world_size < 2:
        return tensor
    tensor = tensor.clone()
    dist.all_reduce(tensor, op=dist.ReduceOp.SUM, group=group)
    return tensor


def reduce_mean(tensor, group=None):
    return reduce_sum(tensor, group=group) / float(get_world_size())


def all_gather_object(obj):
    world_size = get_world_size()
    if world_size < 2:
        return [obj]
    output = [None for _ in range(world_size)]
    dist.all_gather_object(output, obj)
    return output


def is_distributed() -> bool:
    if not dist.is_available():
        return False
    if not dist.is_initialized():
        return False
    return True


def is_available() -> bool:
    return dist.is_available()


def is_master():
    if not dist.is_available():
        rank = 0
    elif not dist.is_initialized():
        rank = 0
    else:
        rank = dist.get_rank()
    return rank == 0


def chunk_list(lst, chunk_size):
    for i in range(0, len(lst), chunk_size):
        yield lst[i : i + chunk_size]


def rank0_gather_object_chunked(obj_list, chunk_size=100, dst=0, show_progress=True):
    """
    仅主进程（dst，通常为0）收集所有进程的obj_list，分段避免OOM。
    其他进程返回None。
    主进程用tqdm显示进度。
    """
    rank = dist.get_rank()
    world_size = dist.get_world_size()
    my_len = len(obj_list)
    my_num_chunks = math.ceil(my_len / chunk_size)

    # Gather number of chunks from all ranks
    all_num_chunks = [None for _ in range(world_size)] if rank == dst else None
    dist.gather_object(my_num_chunks, all_num_chunks, dst=dst)

    if rank == dst:
        max_chunks = max(all_num_chunks)
    else:
        max_chunks = None

    # 广播max_chunks给所有进程
    max_chunks_list = [max_chunks] if rank == dst else [None]
    dist.broadcast_object_list(max_chunks_list, src=dst)
    max_chunks = max_chunks_list[0]

    gathered = [[] for _ in range(world_size)] if rank == dst else None
    if rank == dst and show_progress:
        try:
            progress_iter = tqdm(range(max_chunks), desc="Gathering chunks", total=max_chunks)
        except ImportError:
            progress_iter = range(max_chunks)
    else:
        progress_iter = range(max_chunks)

    for chunk_idx in progress_iter:

        this_chunk = obj_list[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]
        this_chunk_size = len(this_chunk)

        if rank == dst:
            # 目标进程先处理自己的chunk
            gathered_chunks = [None for _ in range(world_size)]
            gathered_chunks[rank] = this_chunk
            # 使用gather_object来接收其他进程的chunk
            dist.gather_object(this_chunk, gathered_chunks, dst=dst)
        else:
            # 非目标进程直接发送chunk
            dist.gather_object(this_chunk, None, dst=dst)

        if rank == dst and gathered is not None:
            for rank_idx, proc_chunk in enumerate(gathered_chunks):
                if proc_chunk:  # 确保proc_chunk不是None
                    gathered[rank_idx].extend(proc_chunk)

        obj_list[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size] = [
            None
        ] * this_chunk_size  # Clear the chunk to free memory

    if rank == dst:
        return gathered
    else:
        return None
