import pandas as pd
import refile
import pickle


def merge_dataFrames(eval_frame):
    import pandas as pd

    dts, gts = [], []
    for frames in eval_frame:
        dts.append(pd.json_normalize(sum([frame["preds"] for frame in frames["frames"] if frame["is_key_frame"]], [])))
        gts.append(pd.json_normalize(sum([frame["labels"] for frame in frames["frames"] if frame["is_key_frame"]], [])))
    # summary
    dts, gts = pd.concat(dts), pd.concat(gts)
    return dts, gts


def compare_dataframes(df1, df2):
    # 找出共同行
    common = pd.merge(df1, df2, how="inner")

    # 找出df1独有的行
    df1_only = df1.merge(df2, how="left", indicator=True).query('_merge == "left_only"').drop("_merge", axis=1)

    # 找出df2独有的行
    df2_only = df2.merge(df1, how="left", indicator=True).query('_merge == "left_only"').drop("_merge", axis=1)

    return common, df1_only, df2_only


# path = "s3://szc-share/2025-06-08T13:45:31/f3t_30w_det_pretrain_detection_tracking_prediction/Sun_Jun__8_18_40_28_2025/eval_results/tfboard.pkl"
path = "s3://pengbo/det_exp/det__det_private_base_exp_8v1l_y300x32_deformable_120m_400q_refine_28w_fixoptim_finetune_18w_clip_prelabel_vru_multi/2025-05-03T16:56:45/Z10_eval_bmk02_epoch_71/eval_results/tfboard.pkl"
with refile.smart_open(path, "rb") as fin:
    eval_frame = pickle.load(fin)
dts_new, gts_new = merge_dataFrames(eval_frame)

# path = "s3://gongjiahao-share/end2end/Z10/exp/deform/f15_8v1L_bsl_detvel_f5WithPrelabelPretrainYuTao_HF_tsFix/track_private_base_exp_8v1l_deformable_z10_f15_freeze_detvel_HF_tsfix_detection_tracking_prediction/Tue_May_20_20_03_07_2025/eval_results/tfboard.pkl"
path = "/data/outputs/od_occ__det_occ_private_base_exp_8v1l_y120x32_deformable_occ_0310_v2_crop_decouple_loss_add_new_data_0520_od_18w_v2/2025-05-30T12:07:08/Z10_eval_od_bmk02_occ_bmk02_filter_vis/eval_results_od/tfboard.pkl"
with refile.smart_open(path, "rb") as fin:
    eval_frame = pickle.load(fin)
dts_old, gts_old = merge_dataFrames(eval_frame)

for category in gts_new["merged_category"].unique():
    fn_new = gts_new[
        (gts_new["match_tag"] == "fn") & (~gts_new["is_ignore"]) & (gts_new["merged_category"] == category)
    ][["track_id", "category", "json_path", "json_frame_id", "xyz_lidar.x", "xyz_lidar.y", "xyz_lidar.z"]]
    fn_old = gts_old[
        (gts_old["match_tag"] == "fn") & (~gts_old["is_ignore"]) & (gts_old["merged_category"] == category)
    ][["track_id", "category", "json_path", "json_frame_id", "xyz_lidar.x", "xyz_lidar.y", "xyz_lidar.z"]]
    print(f"{category}, {len(fn_new.index)}, {len(fn_old.index)}")
    common_rows, fn_new_unique, fn_old_unique = compare_dataframes(fn_new, fn_old)
    print(len(common_rows.index), len(fn_new_unique.index), len(fn_old_unique.index))
    bad1_set = set()
    bad2_set = set()
    print(fn_new_unique[["json_path"]])
    print("-" * 20)
    print(fn_old_unique[["json_path"]])

    # print(fn_new_unique[["track_id", "category", 'json_path', 'json_frame_id', 'xyz_lidar.x', 'xyz_lidar.y', 'xyz_lidar.z']].to_string(index=False))
    # print(fn_new_unique[[ "category", 'json_path', 'json_frame_id']].to_string(index=False))
