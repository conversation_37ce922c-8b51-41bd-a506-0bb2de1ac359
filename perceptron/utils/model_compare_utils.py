import torch
import numpy as np


from perceptron.exps.end2end.private.multitask.deploy.combine_model_od_occ_god_p177 import (
    Exp as Exp1,
)
from perceptron.exps.end2end.private.multitask.deploy.combine_model_od_occ_god_z10_0726 import (
    Exp as Exp2,
)


if __name__ == "__main__":

    def check_value(path, value1, value2):
        if isinstance(value1, dict) and isinstance(value2, dict):
            for k in value1.keys():
                if k not in value2:
                    print(f"Warning: {path}.{k} not in value2, ")
                else:
                    check_value(path + "." + k, value1[k], value2[k])
        elif isinstance(value1, (list, tuple)) and isinstance(value2, (list, tuple)):
            if len(value1) != len(value2):
                print(f"Warning: {path} length not match, {len(value1)} vs {len(value2)}")
            else:
                for i in range(len(value1)):
                    check_value(path + "[" + str(i) + "]", value1[i], value2[i])
        elif isinstance(value1, (np.ndarray, torch.Tensor)) and isinstance(value2, (np.ndarray, torch.Tensor)):
            if value1.shape != value2.shape:
                print(f"Warning: {path} shape not match, {value1.shape} vs {value2.shape}")
            else:
                if not np.allclose(value1, value2, rtol=1e-05, atol=1e-08):
                    print(f"Warning: {path} value not match, {value1} vs {value2}")
        else:
            try:
                if value1 != value2:
                    print(f"Warning: {path} not match, {value1} vs {value2}")
            except Exception as e:
                print(f"Warning: {path} comparison failed, {value1} vs {value2}, error: {e}")

    exp1 = Exp1()
    exp2 = Exp2()
    model_cfg1 = exp1.model_cfg
    model_cfg2 = exp2.model_cfg
    # model_cfg1 = exp1.data_train_god
    # model_cfg2 = exp2.data_train_god
    # 对比两个cfg中差异的部分
    print("value2 = target_cfg")
    for k in model_cfg1.keys():
        if k not in model_cfg2:
            print(f"Warning: {k} not in model_cfg, ")
        else:
            check_value(k, model_cfg1[k], model_cfg2[k])

    print("--------------------------------------------")
    print("value2 = source_cfg")
    for k in model_cfg2.keys():
        if k not in model_cfg1:
            print(f"Warning: {k} not in model_cfg, ")
        else:
            check_value(k, model_cfg2[k], model_cfg1[k])
