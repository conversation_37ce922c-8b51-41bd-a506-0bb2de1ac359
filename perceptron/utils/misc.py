import os
import re
from typing import Callable, Mapping, Optional, TypeVar
import unicodedata
import argparse


def sanitize_filename(value, allow_unicode=False):
    value = str(value)
    if allow_unicode:
        value = unicodedata.normalize("NFKC", value)
    else:
        value = unicodedata.normalize("NFKD", value).encode("ascii", "ignore").decode("ascii")
    value = re.sub(r"[^\w\s-]", "", value.lower())
    return re.sub(r"[-\s]+", "-", value).strip("-_")


def parse_devices(gpu_ids):
    if "-" in gpu_ids:
        gpus = gpu_ids.split("-")
        gpus[0] = int(gpus[0])
        gpus[1] = int(gpus[1]) + 1
        parsed_ids = ",".join(map(lambda x: str(x), list(range(*gpus))))
        return parsed_ids
    else:
        return gpu_ids


class PyDecorator:
    @staticmethod
    def overrides(interface_class):
        def overrider(method):
            assert method.__name__ in dir(interface_class), "{} function not in {}".format(
                method.__name__, interface_class
            )

            return method

        return overrider


_T = TypeVar("_T")


class EnvironWrapper:
    _environ: Mapping[str, str]

    def __init__(self, environ: Optional[Mapping[str, str]] = None) -> None:
        if environ is None:
            self._environ = os.environ
        else:
            self._environ = environ

    def _get_value(self, envName: str, ctor: Callable[[str], _T], defaultOrErr: _T) -> _T:
        strValue = self._environ.get(envName)
        if strValue is None:
            return defaultOrErr
        try:
            return ctor(strValue)
        except ValueError:
            return defaultOrErr

    def getInt(self, envName: str, defaultOrErr: int) -> int:
        return self._get_value(envName, int, defaultOrErr)

    @staticmethod
    def _str_to_bool(value: str) -> bool:
        validTrueValues = ("true", "on", "1", "yes", "y")
        validFalseValues = ("false", "off", "0", "no", "n")

        lowerCaseValue = value.lower()
        if lowerCaseValue in validTrueValues:
            return True
        elif lowerCaseValue in validFalseValues:
            return False
        else:
            raise ValueError(f"unrecognized boolean str: '{value}'")

    def getBool(self, envName: str, defaultOrErr: bool) -> bool:
        return self._get_value(envName, self._str_to_bool, defaultOrErr)

    def getRaw(self, envName: str) -> Optional[str]:
        return self._environ.get(envName)

    def getString(self, envName: str, default: str) -> str:
        rawValue = self.getRaw(envName)
        return rawValue if rawValue is not None else default


class CKPTAction(argparse.Action):
    def __call__(self, parser, namespace, values, option_string=None):
        # values 是一个 list，因为 nargs=1
        value = values[0]
        ckpt = getattr(namespace, self.dest, None)
        if "=" in value:
            k, v = value.split("=", 1)
            if ckpt is None or isinstance(ckpt, str):
                ckpt = {}
            ckpt[k] = v
            setattr(namespace, self.dest, ckpt)
        else:
            if ckpt is None:
                setattr(namespace, self.dest, value)
            # 如果已经是 dict，忽略；如果已经是 str，忽略
