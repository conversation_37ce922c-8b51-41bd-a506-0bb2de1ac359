# ------------------------------------------------------------------------
# Copyright (c) 2023 toyota research instutute.
# ------------------------------------------------------------------------
import math
import torch
import numpy as np
from scipy.spatial.transform import Rotation as R


def pos2posemb3d(pos, num_pos_feats=128, temperature=10000):
    import math

    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    pos_z = pos[..., 2, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_z = torch.stack((pos_z[..., 0::2].sin(), pos_z[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x, pos_z), dim=-1)
    return posemb


def pos2embed(pos, num_pos_feats=128, temperature=10000):
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    # pos_z = pos[..., 2, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    # pos_z = torch.stack((pos_z[..., 0::2].sin(), pos_z[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x), dim=-1)
    return posemb


def ts2tsemb1d(ts, num_pos_feats=128, temperature=10000):
    scale = 2 * math.pi
    ts = ts * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=ts.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    pos = ts[..., 0, None] / dim_t
    posemb = torch.stack((pos[..., 0::2].sin(), pos[..., 1::2].cos()), dim=-1).flatten(-2)
    return posemb


def time_position_embedding(track_num, frame_num, embed_dims, device):
    ts = torch.arange(0, 1 + 1e-5, 1 / (frame_num - 1), dtype=torch.float32, device=device)
    ts = ts[None, :] * torch.ones((track_num, frame_num), dtype=torch.float32, device=device)
    ts_embed = ts2tsemb1d(ts.view(track_num * frame_num, 1), num_pos_feats=embed_dims).view(
        track_num, frame_num, embed_dims
    )
    return ts_embed


def xyz_ego_transformation(xyz, l2g0, l2g1, pc_range, src_normalized=True, tgt_normalized=True):
    """Transform xyz coordinates from l2g0 to l2g1
    Args:
        xyz: (n, 3)
        l2g0, l2g1: (4, 4), float32/float64
        pc_range:
        src_normalized:
        tgt_normalized:
    Returns:
        xyz: float32
    """
    # denormalized to the physical coordinates
    if src_normalized:
        xyz = denormalize(xyz, pc_range)

    # to global, then to next local
    if torch.__version__ < "1.9.0":
        g2l1 = torch.tensor(np.linalg.inv(l2g1.cpu().numpy())).to(l2g1.device)
    else:
        g2l1 = torch.linalg.inv(l2g1)
    if l2g0.dtype == torch.float64:
        xyz = xyz.to(torch.float64)
    xyz = xyz @ l2g0[:3, :3].T + l2g0[:3, 3] - l2g1[:3, 3]
    xyz = xyz @ g2l1[:3, :3].T
    if xyz.dtype == torch.float64:
        xyz = xyz.to(torch.float32)

    # normalize to 0-1
    if tgt_normalized:
        xyz = normalize(xyz, pc_range)
    return xyz


def angle_ego_transformation(hist_angles, l2g0, l2g1):
    """Transform angle from l2g0 to l2g1
    Args:
        xyz: (n, 2) (sin, cos)
        l2g0, l2g1: (4, 4), float32/float64
    Returns:
        angle: float32
    """
    # denormalized to the physical coordinates

    # to global, then to next local
    rot_sine = hist_angles[..., 0]
    rot_cosine = hist_angles[..., 1]
    rot = torch.atan2(rot_sine, rot_cosine).cpu().numpy()

    rotation_matrix = R.from_euler("z", rot, degrees=False).as_matrix()

    combined_matrix = np.dot(rotation_matrix, l2g0[:3, :3].cpu().numpy())
    combined_matrix = np.dot(combined_matrix, torch.linalg.inv(l2g1[:3, :3]).cpu().numpy())

    new_angle = torch.tensor(R.from_matrix(combined_matrix).as_euler("xyz")[:, -1])

    return torch.hstack((new_angle.sin().unsqueeze(1), new_angle.cos().unsqueeze(1)))


def normalize(xyz, pc_range):
    xyz[..., 0:1] = (xyz[..., 0:1] - pc_range[0]) / (pc_range[3] - pc_range[0])
    xyz[..., 1:2] = (xyz[..., 1:2] - pc_range[1]) / (pc_range[4] - pc_range[1])
    xyz[..., 2:3] = (xyz[..., 2:3] - pc_range[2]) / (pc_range[5] - pc_range[2])
    return xyz


def denormalize(xyz, pc_range):
    xyz[..., 0:1] = xyz[..., 0:1] * (pc_range[3] - pc_range[0]) + pc_range[0]
    xyz[..., 1:2] = xyz[..., 1:2] * (pc_range[4] - pc_range[1]) + pc_range[1]
    xyz[..., 2:3] = xyz[..., 2:3] * (pc_range[5] - pc_range[2]) + pc_range[2]
    return xyz
