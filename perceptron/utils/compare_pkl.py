import pickle
import os


def load_pkl(file_path):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"找不到文件: {file_path}")
    with open(file_path, "rb") as f:
        return pickle.load(f)


def compare_dict_lists(list1, list2):
    if len(list1) != len(list2):
        print(f"列表长度不同: list1={len(list1)}, list2={len(list2)}")
        return

    for idx, (d1, d2) in enumerate(zip(list1, list2)):
        if not isinstance(d1, dict) or not isinstance(d2, dict):
            print(f"第 {idx} 个元素不是字典")
            continue

        keys1 = set(d1.keys())
        keys2 = set(d2.keys())

        if keys1 != keys2:
            print(f"第 {idx} 个字典的 key 不一致:")
            print(f"    list1 keys: {keys1}")
            print(f"    list2 keys: {keys2}")
            continue

        for key in keys1:
            v1 = d1[key]
            v2 = d2[key]

            # 比较 shape（如果有 shape 属性）否则比较 type
            shape1 = getattr(v1, "shape", type(v1))
            shape2 = getattr(v2, "shape", type(v2))

            if shape1 != shape2:
                print(f"第 {idx} 个字典中 key='{key}' 的值形状或类型不同:")
                print(f"    list1: {shape1}")
                print(f"    list2: {shape2}")

    print("比较完成")


def compare_dict_data(d1, d2):
    if not isinstance(d1, dict) or not isinstance(d2, dict):
        raise TypeError("输入的两个对象都必须是字典")

    keys1 = set(d1.keys())
    keys2 = set(d2.keys())

    if keys1 != keys2:
        print("两个字典的 key 不一致")
        print(f"dict1 keys - dict2 keys: {keys1 - keys2}")
        print(f"dict2 keys - dict1 keys: {keys2 - keys1}")
    else:
        print("两个字典的 key 完全一致")

    for key in keys1 & keys2:  # 遍历两个字典都拥有的 key
        v1 = d1[key]
        v2 = d2[key]

        shape1 = getattr(v1, "shape", None)
        shape2 = getattr(v2, "shape", None)

        if shape1 is not None and shape2 is not None:
            if shape1 != shape2:
                print(f"key '{key}' 的 shape 不一致: {shape1} vs {shape2}")
        else:
            if not isinstance(v1, type(v2)):
                print(f"key '{key}' 的类型不一致: {type(v1)} vs {type(v2)}")

        if key == "preds":
            l1 = len(v1)
            l2 = len(v2)

            if l1 != l2:
                print("length 不一致")


if __name__ == "__main__":
    path1 = "outputs/2025-07-12T14:59:30/god/combine_model_od_occ_god_detection_tracking_prediction/Wed_Jul_30_17_14_24_2025/eval_results/0006_infer_occ_god_e2e.pkl"
    path2 = "/home/<USER>/zhy_data/od_occ_frames.pkl"

    # debug
    import debugpy

    debugpy.listen(("0.0.0.0", 5678))
    print("waiting for debug")
    debugpy.wait_for_client()

    list1 = load_pkl(path1)
    list2 = load_pkl(path2)

    # 比较一个元素
    data1 = list1[0]["frames"][0]
    data2 = list2[0]["frames"][0]

    compare_dict_data(data1, data2)
