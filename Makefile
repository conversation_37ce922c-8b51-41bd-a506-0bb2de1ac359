format:
	autoflake -i --ignore-init-module-imports --remove-all-unused-imports -r perceptron
	black --line-length 120 . --force-exclude submodule

style_check:
	autoflake --ignore-init-module-imports --remove-all-unused-imports -r perceptron
	black --line-length 120 --diff --check . --force-exclude submodule

lint:
	pylint --rcfile=.pylintrc --errors-only perceptron | tee pylint.txt || true
	score=`sed -n 's/^Your code has been rated at \([-0-9.]*\)\/.*/\1/p' pylint.txt`; \
	anybadge --value="$$score" --value-format=" %.2f " --file=pylint.svg pylint

unittest:
	pytest --cov=perceptron --cov-report=html --cov-report term test
