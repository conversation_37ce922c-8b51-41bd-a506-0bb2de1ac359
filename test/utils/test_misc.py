from typing import Dict
from unittest import TestCase

from perceptron.utils.misc import EnvironWrapper


class EnvironWrapperTest(TestCase):
    _fakeEnviron: Dict[str, str]

    def setUp(self) -> None:
        super().setUp()
        self._fakeEnviron = dict()

    def testGetInt(self) -> None:
        key = "MY_COUNTER"
        value = 10
        self._fakeEnviron[key] = str(value)
        envWrapper = EnvironWrapper(self._fakeEnviron)
        self.assertEqual(value, envWrapper.getInt(key, defaultOrErr=value + 1))

    def testGetBool(self) -> None:
        flag1 = "MY_TRUE_FLAG"
        value1 = "true"
        flag2 = "MY_FALSE_FLAG"
        value2 = "False"
        self._fakeEnviron[flag1] = value1
        self._fakeEnviron[flag2] = value2
        envWrapper = EnvironWrapper(self._fakeEnviron)

        self.assertTrue(envWrapper.getBool(flag1, defaultOrErr=False))
        self.assertFalse(envWrapper.getBool(flag2, defaultOrErr=True))

    def testGetRaw(self) -> None:
        key = "MY_ENV"
        value = "MY_VALUE"
        nonExistingKey = "MY_NON_EXISTING_KEY"
        self._fakeEnviron[key] = value
        envWrapper = EnvironWrapper(self._fakeEnviron)

        self.assertIsNotNone(envWrapper.getRaw(key))
        self.assertEqual(value, envWrapper.getRaw(key))

        self.assertIsNone(envWrapper.getRaw(nonExistingKey))

    def testGetString(self) -> None:
        envKey = "SHELL"
        envValue = "/bin/zsh"
        self._fakeEnviron[envKey] = envValue
        envWrapper = EnvironWrapper(self._fakeEnviron)

        self.assertEqual(envValue, envWrapper.getString(envKey, default="/bin/bash"))
