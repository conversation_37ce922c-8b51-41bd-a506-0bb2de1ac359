import pickle
import unittest

from refile import smart_open

from perceptron.utils.traffic_det2d_utils.tracker.label_assign import tracker_label_update_assign
from perceptron.utils.traffic_det2d_utils.tracker.track_eval import track_eval


class TestTracker(unittest.TestCase):
    def setUp(self):
        self.timeline = "s3://perceptor-share/shared-files/unittest/utils/traffic_det2d_utils/tracker/timeline.pkl"
        self.all_dt = "s3://perceptor-share/shared-files/unittest/utils/traffic_det2d_utils/tracker/all_dt.pkl"
        self.track_gt_boxes = (
            "s3://perceptor-share/shared-files/unittest/utils/traffic_det2d_utils/tracker/track_gt_boxes.pkl"
        )
        self.nori_id_track = (
            "s3://perceptor-share/shared-files/unittest/utils/traffic_det2d_utils/tracker/nori_id_track.pkl"
        )
        self.timeline = pickle.load(smart_open(self.timeline, "rb"))
        self.all_dt = pickle.load(smart_open(self.all_dt, "rb"))
        self.track_gt_boxes = pickle.load(smart_open(self.track_gt_boxes, "rb"))
        self.nori_id_track = pickle.load(smart_open(self.nori_id_track, "rb"))

    def tearDown(self):
        pass

    def test_tracker(self):
        track_result, track_gt_boxes, track_filter_dt = tracker_label_update_assign(
            self.timeline, self.all_dt, self.track_gt_boxes, self.nori_id_track
        )
        track_metric = track_eval(track_result, track_gt_boxes)
        expected_results = {
            "mean_idf1": 0.5540164301904387,
            "track_length_sum": 8402,
            "idswitch_sum": 1961,
            "idswitch_rate": 0.27537500051525765,
        }
        for key in track_metric.keys():
            self.assertAlmostEqual(expected_results[key], track_metric[key])


if __name__ == "__main__":
    unittest.main()
