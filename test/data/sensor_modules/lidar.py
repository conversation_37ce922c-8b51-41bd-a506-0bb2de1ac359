import unittest
import random
import numpy as np
import pickle
from refile import smart_open
from perceptron.utils.same_iter import recursive_compare
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.data.det3d.utils.functional import initialize_object
from perceptron.data.det3d.source.config import Z10
from perceptron.utils.env import get_cluster
from perceptron.data.det3d.modules.lidar import LidarBase

_LIDAR_LIST = ["fuser_lidar"]

config_z10_train = dict(
    car=Z10,
    camera_names=[],
    mode="train",
    datasets_names=["z10_label_1230_train"],
    only_key_frame=False,
    rebuild=False,
)

config_z10_val = dict(
    car=Z10,
    camera_names=[],
    mode="validate",
    datasets_names=["z1_label_1230_bmk_qy"] if get_cluster() == "https://qy.machdrive.cn" else ["z1_label_1230_bmk"],
    only_key_frame=False,
    rebuild=False,
)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)
target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))

config_z10_train_lidar = dict(
    type=LidarBase,
    car=config_z10_train["car"],
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],  # 激光雷达回波
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[1],  # 1 在hf中对应前视lidar #TODO 需要检查是否其他hf数据如此
)

config_z10_val_lidar = dict(
    type=LidarBase,
    car=config_z10_val["car"],
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[1],
)

loader_configs = [
    config_z10_train,
    config_z10_val,
]
lidar_configs = [
    config_z10_train_lidar,
    config_z10_val_lidar,
]


class TestE2eGetLidar(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def testLidar(self):
        for loader_config, lidar_config in zip(loader_configs, lidar_configs):
            loader = LoaderSimFov(**loader_config)
            loader()
            lidar_config.update({"loader_output": loader.output, "mode": loader_config["mode"]})
            get_lidar = initialize_object(lidar_config)
            lidar_info = get_lidar.get_lidars(10, dict())
            pkl_path = f"s3://zyk-share/unittest_z10/lidar_info_{loader_config['datasets_names'][0]}.pkl"
            lidar_data = pickle.load(smart_open(pkl_path, "rb"))
            for k, v in lidar_data.items():
                recursive_compare(k, lidar_info[k], v)


if __name__ == "__main__":
    import os

    if os.environ.get("DEBUG"):
        test = TestE2eGetLidar()
        test.testLidar()
    else:
        unittest.main()
