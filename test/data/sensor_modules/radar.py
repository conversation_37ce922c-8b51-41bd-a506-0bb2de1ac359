import unittest
import random
import numpy as np
from refile import smart_open
import pickle
from perceptron.utils.same_iter import recursive_compare
from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.data.det3d.utils.functional import initialize_object
from perceptron.data.det3d.source.config import Z10
from perceptron.utils.env import get_cluster
from perceptron.data.det3d.modules.radar import HFRadar


config_z10_train = dict(
    car=Z10,
    camera_names=[],
    mode="train",
    datasets_names=["z10_label_1230_train"],
    only_key_frame=False,
    rebuild=False,
)

config_z10_val = dict(
    car=Z10,
    camera_names=[],
    mode="validate",
    datasets_names=["z1_label_1230_bmk_qy"] if get_cluster() == "https://qy.machdrive.cn" else ["z1_label_1230_bmk"],
    only_key_frame=False,
    rebuild=False,
)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)
target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))

config_z10_train_radar = dict(
    type=HFRadar,
    car=config_z10_train["car"],
    radar_mode="mlp",
    with_virtual_radar=True,
)

config_z10_val_radar = dict(
    type=HFRadar,
    car=config_z10_val["car"],
    radar_mode="mlp",
    with_virtual_radar=True,
)

loader_configs = [
    config_z10_train,
    config_z10_val,
]
radar_configs = [
    config_z10_train_radar,
    config_z10_val_radar,
]


class TestE2eGetRadar(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def testRadar(self):
        for loader_config, radar_config in zip(loader_configs, radar_configs):
            loader = LoaderSimFov(**loader_config)
            loader()
            radar_config.update({"loader_output": loader.output, "mode": loader_config["mode"]})
            get_radar = initialize_object(radar_config)
            radar_info = get_radar.get_radars(10, dict())
            pkl_path = f"s3://zyk-share/unittest_z10/radar_info_{loader_config['datasets_names'][0]}.pkl"
            radar_data = pickle.load(smart_open(pkl_path, "rb"))
            for k, v in radar_data.items():
                recursive_compare(k, radar_info[k], v)


if __name__ == "__main__":
    import os

    if os.environ.get("DEBUG"):
        test = TestE2eGetRadar()
        test.testRadar()
    else:
        unittest.main()
