import unittest
import random
import numpy as np
import pickle
import cv2
from refile import smart_open
from perceptron.utils.same_iter import recursive_compare

from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.data.det3d.modules.image import ImageSimFov, UndistortWarp
from perceptron.data.det3d.utils.functional import initialize_object
from perceptron.data.det3d.source.config import Z10
from perceptron.utils.env import get_cluster

_CAMERA_LIST = [
    "cam_front_120",
    "cam_front_120_sim_fov70",
    "cam_front_30",
    "cam_back_70",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
    "cam_back_left_100_sim_fov104",
    "cam_back_right_100_sim_fov104",
]

config_z10_train = dict(
    car=Z10,
    camera_names=_CAMERA_LIST,
    mode="train",
    datasets_names=["z10_label_1230_train"],
    only_key_frame=False,
    rebuild=False,
)

config_z10_val = dict(
    car=Z10,
    camera_names=_CAMERA_LIST,
    mode="validate",
    datasets_names=["z1_label_1230_bmk_qy"] if get_cluster() == "https://qy.machdrive.cn" else ["z1_label_1230_bmk"],
    only_key_frame=False,
    rebuild=False,
)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)
target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))

_CAMERA_UNDISTORT_FUNC_Z10 = dict(
    cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    # front and back camera
    cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120_sim_fov70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
)

config_z10_train_img = dict(
    type=ImageSimFov,
    car=config_z10_train["car"],
    camera_names=config_z10_train["camera_names"],
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC_Z10,
)
config_z10_val_img = dict(
    type=ImageSimFov,
    car=config_z10_val["car"],
    camera_names=config_z10_val["camera_names"],
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC_Z10,
)

loader_configs = [
    config_z10_train,
    config_z10_val,
]
image_configs = [
    config_z10_train_img,
    config_z10_val_img,
]


class TestE2eGetImage(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def testImage(self):
        for loader_config, img_config in zip(loader_configs, image_configs):
            loader = LoaderSimFov(**loader_config)
            loader()
            img_config.update({"loader_output": loader.output, "mode": loader_config["mode"]})
            get_image = initialize_object(img_config)
            img_info = get_image.get_images(10, dict())

            pkl_path = f"s3://zyk-share/unittest_z10/image_info_{loader_config['datasets_names'][0]}.pkl"
            image_data = pickle.load(smart_open(pkl_path, "rb"))

            for i in range(len(img_info["imgs"])):
                img = cv2.remap(img_info["imgs"][i], img_info["map1s"][i], img_info["map2s"][i], cv2.INTER_LINEAR)
                img_info["imgs"][i] = img

            for key, value in image_data.items():
                recursive_compare(key, img_info[key], value)


if __name__ == "__main__":
    import os

    # 行为差异是在 LoaderBase 中，如果是 DEBUG 模式，间隔 100 个 frame 进行一次测试
    if os.environ.get("DEBUG"):
        test = TestE2eGetImage()
        test.setUp()
        test.testImage()
    else:
        unittest.main()
