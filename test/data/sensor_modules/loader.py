import unittest
import random
import numpy as np

from perceptron.data.det3d.modules import LoaderSimFov
from perceptron.data.det3d.source.config import HFCar9, Z10
from perceptron.utils.env import get_cluster

config_hf_train = dict(
    car=HFCar9,
    camera_names=[
        "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
        "cam_front_120_sim_fov70",
        "cam_front_30",
        "cam_back_120_sim_fov70",
        "cam_front_left_120",
        "cam_front_right_120",
        "cam_back_left_120",
        "cam_back_right_120",
    ],
    mode="train",
    datasets_names=["hf_label_track_1120_car15"],
    only_key_frame=False,
    rebuild=False,
)

config_hf_val = dict(
    car=HFCar9,
    camera_names=[
        "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
        "cam_front_120_sim_fov70",
        "cam_front_30",
        "cam_back_120_sim_fov70",
        "cam_front_left_120",
        "cam_front_right_120",
        "cam_back_left_120",
        "cam_back_right_120",
    ],
    mode="validate",
    datasets_names=["val_hf_data_urban_occlusion_bmk"],
    only_key_frame=False,
    rebuild=False,
)

# config_hf_test = dict(
#     car=Z10,
#     camera_names=[
#         "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
#         "cam_front_120_sim_fov70",
#         "cam_front_30",
#         "cam_back_70",
#         "cam_front_left_120",
#         "cam_front_right_120",
#         "cam_back_left_120",
#         "cam_back_right_120",
#     ],
#     mode="train",
#     datasets_names=["z10_label_1230_train"],
#     only_key_frame=False,
#     rebuild=False,
# )

config_z10_train = dict(
    car=Z10,
    camera_names=[
        "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
        "cam_front_120_sim_fov70",
        "cam_front_30",
        "cam_back_70",
        "cam_front_left_100_sim_fov104",
        "cam_front_right_100_sim_fov104",
        "cam_back_left_100_sim_fov104",
        "cam_back_right_100_sim_fov104",
    ],
    mode="train",
    datasets_names=["z10_label_1230_train"],
    only_key_frame=False,
    rebuild=False,
)

config_z10_val = dict(
    car=Z10,
    camera_names=[
        "cam_front_120",  # 地图需要, 这里需要放【第1个】，用来和 rv 监督对齐
        "cam_front_120_sim_fov70",
        "cam_front_30",
        "cam_back_70",
        "cam_front_left_100_sim_fov104",
        "cam_front_right_100_sim_fov104",
        "cam_back_left_100_sim_fov104",
        "cam_back_right_100_sim_fov104",
    ],
    mode="validate",
    datasets_names=["z1_label_1230_bmk_qy"] if get_cluster() == "https://qy.machdrive.cn" else ["z1_label_1230_bmk"],
    only_key_frame=False,
    rebuild=False,
)


class TestE2eLoader(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def testLoader(self):
        for config in [config_hf_train, config_hf_val, config_z10_train, config_z10_val]:
            loader = LoaderSimFov(**config)
            loader()
            frame = loader.output["frame_data_list"][10]
            print(frame.keys())


if __name__ == "__main__":
    import os

    if os.environ.get("DEBUG"):
        test = TestE2eLoader()
        test.testLoader()
    else:
        unittest.main()
