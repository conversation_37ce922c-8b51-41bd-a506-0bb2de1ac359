import unittest
import random
import numpy as np
import pickle
import torch
import copy
import os
import mmcv
from refile import smart_open
from perceptron.utils.env import get_cluster
from perceptron.utils.same_iter import recursive_compare
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.det3d.source.config import Z10
from perceptron.data.det3d.modules.radar import HFRadar
from perceptron.data.det3d.modules import (
    LoaderSimFov,
    UndistortWarp,
    LidarBase,
    ImageSimFov,
    MultiFrameImageAffineTransformation,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)
from perceptron.data.det3d.modules.evaluation_occ import EvaluationOcc

from perceptron.data.det3d.modules.annotation import AnnotationFreespace

_CAMERA_LIST = [
    "cam_front_120",
    "cam_front_30",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
]

_LIDAR_LIST = ["fuser_lidar"]

_RADAR_KEY_LIST = ["0", "1", "2", "3", "4"]

_SENSOR_NAMES = dict(camera_names=_CAMERA_LIST, lidar_names=_LIDAR_LIST, radar_names=_RADAR_KEY_LIST)

point_cloud_range = [-15.2, -27.2, -5.0, 15.2, 81.6, 3.0]

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)
target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))

_CAMERA_UNDISTORT_FUNC_Z10 = dict(
    cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
)

loader_cfg = dict(
    type=LoaderSimFov,
    car=Z10,
    camera_names=_CAMERA_LIST,
    mode="train",
    datasets_names=["carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_train699"],
    only_key_frame=False,
    rebuild=False,
)

lidar_cfg = dict(
    type=LidarBase,
    car=Z10,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[4],
)

image_cfg = dict(
    type=ImageSimFov,
    car=Z10,
    camera_names=_CAMERA_LIST,
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC_Z10,
)

radar_cfg = dict(
    type=HFRadar,
    car=Z10,
    radar_mode="mlp",
    with_virtual_radar=True,
)

annotation_cfg = dict(
    occ=dict(type=AnnotationFreespace, label_mapping={0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 255, 6: 255, 255: 255}),
)

pipeline_cfg = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
        multiframe=True,
    ),
    point_shuffle=dict(
        type=PointShuffle,
        multiframe=True,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
        gpu_aug=True,
        multiframe=True,
    ),
    ida_aug=dict(
        type=MultiFrameImageAffineTransformation,
        aug_conf=dict(
            final_dim=(512, 960),
            # resize_lim=((0.472, 0.5), (0.472, 0.5)),
            resize_lim=(0.5, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
    ),
)

base_dataset_cfg = dict(
    car=Z10,
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=1,
    loader=loader_cfg,
    lidar=lidar_cfg,
    image=image_cfg,
    radar=None,
    annotation=annotation_cfg,
    pipeline=pipeline_cfg,
)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)

val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
bmk_name = (
    "carz10_1358_occ_labels_z10_20250124_huiliu_data_vx0p1_restore_split_bmk316_2fps"
    if get_cluster() == "https://qy.machdrive.cn"
    else "z1_label_1230_bmk"
)
val_dataset_cfg["loader"].update(datasets_names=[bmk_name])
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationOcc,
        output_mode="freespace",
        label_mapping=annotation_cfg["occ"]["label_mapping"],
        num_classes=5,
        class_names=["free", "freespace", "dynamic", "static", "water"],
        annotaion_cls=AnnotationFreespace,
    )
)


data_val_cfg = mmcv.Config(val_dataset_cfg)
data_train_cfg = mmcv.Config(base_dataset_cfg)

# data_val_cfg["annotation"]["box"]["occlusion_threshold"] = 1
# data_val_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
# data_val_cfg["pipeline"]["ida_aug"]["one_stage"] = False

# data_train_cfg = mmcv.Config(base_dataset_cfg)
# data_train_cfg["annotation"]["box"]["label_key"] = "pre_labels"
# data_train_cfg["annotation"]["box"]["occlusion_threshold"] = 1
# data_train_cfg["annotation"]["box"]["with_occlusion"] = True
# data_train_cfg["num_frames_per_sample"] = 1
# data_train_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
# data_train_cfg["pipeline"]["ida_aug"]["one_stage"] = False
# data_train_cfg["pipeline"]["ida_aug"]["target_extrinsic"] = Z10_NPZ_PATH


class TestPrivateE2EDataset(unittest.TestCase):
    def setUp(self) -> None:

        os.environ["DEBUG"] = "1"
        np.random.seed(0)
        random.seed(0)
        torch.manual_seed(0)

        self.train_dataset = PrivateE2EDataset(
            convert_fp32=False,
            **data_train_cfg,
        )

        self.val_dataset = PrivateE2EDataset(
            **data_val_cfg,
        )

    def test_batch_collation_train(self):
        batch_size = 1
        train_dataloader = torch.utils.data.DataLoader(
            self.train_dataset,
            batch_size=batch_size,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=None,
            pin_memory=False,
            num_workers=0,
        )

        batch = next(iter(train_dataloader))

        if hasattr(self.train_dataset, "batch_postcollate_fn"):
            self.train_dataset.batch_postcollate_fn(batch)
        if hasattr(self.train_dataset, "batch_preforward_fn"):
            self.train_dataset.batch_preforward_fn(batch)

        batch_data_dir = "s3://wyh-share/unittest_z10/occ/"
        with smart_open(os.path.join(batch_data_dir, "train_batch_data.pkl"), "rb") as f:
            batch_data = pickle.load(f)
        print("test")
        for key, value in batch.items():
            recursive_compare(key, value, batch_data[key])

    def test_batch_collation_val(self):
        batch_size = 1
        val_dataloader = torch.utils.data.DataLoader(
            self.val_dataset,
            batch_size=batch_size,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            batch_sampler=None,
            pin_memory=False,
        )

        batch = next(iter(val_dataloader))

        with torch.no_grad():
            if hasattr(self.val_dataset, "batch_postcollate_fn"):
                self.val_dataset.batch_postcollate_fn(batch)
            if hasattr(self.val_dataset, "batch_preforward_fn"):
                self.val_dataset.batch_preforward_fn(batch)
        batch_data_dir = "s3://wyh-share/unittest_z10/occ/"
        with smart_open(os.path.join(batch_data_dir, "test_batch_data.pkl"), "rb") as f:
            batch_data = pickle.load(f)

        for key, value in batch.items():
            recursive_compare(key, value, batch_data[key])


if __name__ == "__main__":
    unittest.main()
