import unittest
import random
import numpy as np
import pickle
import torch
import copy
import os
import mmcv
from refile import smart_open
from perceptron.utils.env import get_cluster
from perceptron.utils.same_iter import recursive_compare
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.data.det3d.source.config import Z10
from perceptron.data.det3d.modules.radar import HFRadar
from perceptron.data.det3d.modules import (
    LoaderSimFov,
    UndistortWarp,
    LidarBase,
    ImageSimFov,
    EvaluationE2E,
    AnnotationTrack,
    MultiFrameImageAffineTransformationWarp,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)

from perceptron.exps.end2end.private.object.data_cfg.det_annos_hf_y200m_x32m_8v5r1l import (
    category_map,
    class_names,
    category_map_reverse,
)

_CAMERA_LIST = [
    "cam_front_120",
    "cam_front_120_sim_fov70",
    "cam_front_30",
    "cam_back_70",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
    "cam_back_left_100_sim_fov104",
    "cam_back_right_100_sim_fov104",
]

_LIDAR_LIST = ["fuser_lidar"]

_RADAR_KEY_LIST = ["0", "1", "2", "3", "4"]

_SENSOR_NAMES = dict(camera_names=_CAMERA_LIST, lidar_names=_LIDAR_LIST, radar_names=_RADAR_KEY_LIST)

point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)
target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))

_CAMERA_UNDISTORT_FUNC_Z10 = dict(
    cam_front_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_left_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_right_100_sim_fov104=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_30=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_front_120_sim_fov70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
    cam_back_70=(UndistortWarp, dict(target_extrinsic=target_z10_extrinsic)),
)

loader_cfg = dict(
    type=LoaderSimFov,
    car=Z10,
    camera_names=_CAMERA_LIST,
    mode="train",
    datasets_names=["z10_label_1230_train"],
    only_key_frame=False,
    rebuild=False,
)

lidar_cfg = dict(
    type=LidarBase,
    car=Z10,
    lidar_names=_LIDAR_LIST,
    referen_lidar="front_lidar",
    pc_fields=["x", "y", "z", "i", "lidar_id"],
    used_echo_id=[1],
    lidar_sweeps_idx=[],
    lidar_with_timestamp=False,
    lidar_ids=[4],
)

image_cfg = dict(
    type=ImageSimFov,
    car=Z10,
    camera_names=_CAMERA_LIST,
    undistort=True,
    undistort_func=_CAMERA_UNDISTORT_FUNC_Z10,
)

radar_cfg = dict(
    type=HFRadar,
    car=Z10,
    radar_mode="mlp",
    with_virtual_radar=True,
)

annotation_cfg = dict(
    box=dict(
        type=AnnotationTrack,
        category_map=category_map,
        class_names=class_names,
        occlusion_threshold=-1,
        filter_outlier_boxes=True,
        filter_outlier_frames=True,
        filter_empty_2d_bboxes=False,
        filter_empty_frames=True,
        roi_range=point_cloud_range,
        with_predict=False,
        HF=False,
        hist_traj_len=20,
        fut_traj_len=13,
    ),
)

pipeline_cfg = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
        multiframe=True,
    ),
    point_shuffle=dict(
        type=PointShuffle,
        multiframe=True,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
        gpu_aug=True,
        multiframe=True,
    ),
    ida_aug=dict(
        type=MultiFrameImageAffineTransformationWarp,
        aug_conf=dict(
            final_dim=(512, 960),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
        one_stage=False,
        gpu_aug=True,
        multiframe=True,
        target_extrinsic=Z10_NPZ_PATH,
    ),
)

base_dataset_cfg = dict(
    car=Z10,
    mode="train",
    gpu_aug=True,
    postcollate_tensorize=True,
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=3,
    loader=loader_cfg,
    lidar=lidar_cfg,
    image=image_cfg,
    radar=radar_cfg,
    annotation=annotation_cfg,
    pipeline=pipeline_cfg,
    roi_mask=[-32, -80, 32, 120],
)

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)

val_dataset_cfg = copy.deepcopy(base_dataset_cfg)
val_dataset_cfg.update(mode="val")
bmk_name = "z1_label_1230_bmk_qy" if get_cluster() == "https://qy.machdrive.cn" else "z1_label_1230_bmk"
val_dataset_cfg["loader"].update(datasets_names=[bmk_name])
val_dataset_cfg["annotation"]["box"].update(occlusion_threshold=1)
val_dataset_cfg.update(num_frames_per_sample=1)
val_dataset_cfg.update(
    evaluator=dict(
        type=EvaluationE2E,
        category_map=category_map_reverse,
        dump_det_results=False,
        eval_cfg_l3="cam_l3_front",
        eval_cfg_l2="e2e_l3_far_32m_front",
    )
)
val_dataset_cfg.update(
    eval_cfg=dict(
        evaluation=dict(interval=12),
        eval_ppl=["detection", "tracking", "prediction"],
        format_only=False,
        eval=True,
    )
)

data_val_cfg = mmcv.Config(val_dataset_cfg)

data_val_cfg["annotation"]["box"]["occlusion_threshold"] = 1
data_val_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
data_val_cfg["pipeline"]["ida_aug"]["one_stage"] = False

data_train_cfg = mmcv.Config(base_dataset_cfg)
data_train_cfg["annotation"]["box"]["label_key"] = "labels"
data_train_cfg["annotation"]["box"]["occlusion_threshold"] = 1
data_train_cfg["annotation"]["box"]["with_occlusion"] = True
data_train_cfg["num_frames_per_sample"] = 2
data_train_cfg["pipeline"]["ida_aug"]["type"] = MultiFrameImageAffineTransformationWarp
data_train_cfg["pipeline"]["ida_aug"]["one_stage"] = False
data_train_cfg["pipeline"]["ida_aug"]["target_extrinsic"] = Z10_NPZ_PATH


class TestPrivateE2EDataset(unittest.TestCase):
    def setUp(self) -> None:

        os.environ["DEBUG"] = "1"
        np.random.seed(0)
        random.seed(0)
        torch.manual_seed(0)

        self.train_dataset = PrivateE2EDataset(
            convert_fp32=False,
            **data_train_cfg,
        )

        self.val_dataset = PrivateE2EDataset(
            **data_val_cfg,
        )

    def test_batch_collation_train(self):
        batch_size = 1
        train_dataloader = torch.utils.data.DataLoader(
            self.train_dataset,
            batch_size=batch_size,
            drop_last=False,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            sampler=None,
            pin_memory=False,
            num_workers=0,
        )

        batch = next(iter(train_dataloader))

        if hasattr(self.train_dataset, "batch_postcollate_fn"):
            self.train_dataset.batch_postcollate_fn(batch)
        if hasattr(self.train_dataset, "batch_preforward_fn"):
            self.train_dataset.batch_preforward_fn(batch)

        batch_data_dir = "s3://zyk-share/unittest_z10/"
        with smart_open(os.path.join(batch_data_dir, "train_batch_data.pkl"), "rb") as f:
            batch_data = pickle.load(f)

        for key, value in batch.items():
            recursive_compare(key, value, batch_data[key])

    def test_batch_collation_val(self):
        batch_size = 1
        val_dataloader = torch.utils.data.DataLoader(
            self.val_dataset,
            batch_size=batch_size,
            shuffle=False,
            collate_fn=PrivateE2EDataset.collate_fn,
            num_workers=0,
            batch_sampler=None,
            pin_memory=False,
        )

        batch = next(iter(val_dataloader))

        with torch.no_grad():
            if hasattr(self.val_dataset, "batch_postcollate_fn"):
                self.val_dataset.batch_postcollate_fn(batch)
            if hasattr(self.val_dataset, "batch_preforward_fn"):
                self.val_dataset.batch_preforward_fn(batch)

        batch_data_dir = "s3://zyk-share/unittest_z10/"
        with smart_open(os.path.join(batch_data_dir, "val_batch_data.pkl"), "rb") as f:
            batch_data = pickle.load(f)

        for key, value in batch.items():
            recursive_compare(key, value, batch_data[key])


if __name__ == "__main__":
    unittest.main()
