import os
import pickle
import unittest
import random
import numpy as np
from refile import smart_open
from perceptron.utils.same_iter import recursive_compare

from perceptron.data.det3d.modules.pipelines.compose import Compose
from perceptron.data.det3d.modules import (
    MultiFrameImageAffineTransformationWarp,
    CameraUndistortCPU,
    ObjectRangeFilter,
    PointShuffle,
)
from perceptron.utils.env import get_cluster

point_cloud_range = [-32.0, -80.0, -5.0, 32.0, 120, 3.0]

Z10_NPZ_PATH = (
    "s3://wangningzi-data-qy/perceptron_files/target_extrinsic_z08_modified.npz"
    if get_cluster() == "https://qy.machdrive.cn"
    else "s3://zyk-share/end2end/calib/target_extrinsic_z08_modified.npz"
)

_CAMERA_LIST = [
    "cam_front_120",
    "cam_front_120_sim_fov70",
    "cam_front_30",
    "cam_back_70",
    "cam_front_left_100_sim_fov104",
    "cam_front_right_100_sim_fov104",
    "cam_back_left_100_sim_fov104",
    "cam_back_right_100_sim_fov104",
]

config_z10_train_pipeline = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
        multiframe=True,
    ),
    point_shuffle=dict(
        type=PointShuffle,
        multiframe=True,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
        gpu_aug=True,
        multiframe=True,
    ),
    ida_aug=dict(
        type=MultiFrameImageAffineTransformationWarp,
        aug_conf=dict(
            final_dim=(512, 960),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
        one_stage=False,
        gpu_aug=True,
        multiframe=True,
        target_extrinsic=Z10_NPZ_PATH,
    ),
)

config_z10_val_pipeline = dict(
    object_range_filter=dict(
        type=ObjectRangeFilter,
        point_cloud_range=point_cloud_range,
        multiframe=True,
    ),
    point_shuffle=dict(
        type=PointShuffle,
        multiframe=True,
    ),
    undistort=dict(
        type=CameraUndistortCPU,
        gpu_aug=True,
        multiframe=True,
    ),
    ida_aug=dict(
        type=MultiFrameImageAffineTransformationWarp,
        aug_conf=dict(
            final_dim=(512, 960),
            resize_lim=(0.472, 0.5),
            bot_pct_lim=(0.0, 0.0),
            H=1080,
            W=1920,
            rand_flip=False,
            rot_lim=(-0.0, 0.0),
        ),
        camera_names=_CAMERA_LIST,
        img_norm=True,
        img_conf={"img_mean": [123.675, 116.28, 103.53], "img_std": [58.395, 57.12, 57.375], "to_rgb": False},
        one_stage=False,
        gpu_aug=True,
        multiframe=True,
    ),
)

target_z10_extrinsic = dict(np.load(smart_open(Z10_NPZ_PATH, "rb")))

pipeline_configs = [
    config_z10_train_pipeline,
    config_z10_val_pipeline,
]


class TestPipeline(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def testPipeline(self):
        for pipeline_config in pipeline_configs:
            for key, transform in pipeline_config.items():
                transform.update({"mode": "val"})
            pipeline = Compose(pipeline_config)

            pre_data_dir = "s3://zyk-share/unittest_z10/test_pipeline_data/"
            post_data_dir = "s3://zyk-share/unittest_z10/test_post_pipeline_data/"
            num_samples = 6

            for i in range(num_samples):
                pre_pkl_path = os.path.join(pre_data_dir, f"sample_queue_{i}.pkl")
                post_pkl_path = os.path.join(post_data_dir, f"sample_queue_{i}.pkl")
                with smart_open(pre_pkl_path, "rb") as f:
                    sample_queue = pickle.load(f)
                data_new = pipeline(sample_queue)[0]
                with smart_open(post_pkl_path, "rb") as f:
                    data_old = pickle.load(f)[0]
                for key, value in data_new.items():
                    recursive_compare(key, value, data_old[key])


if __name__ == "__main__":
    unittest.main()
