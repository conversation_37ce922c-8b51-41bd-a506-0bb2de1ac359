import mmcv  # noqa
import unittest
import numpy as np
import random

from perceptron.data.det3d.source.config import GeelyCar1
from perceptron.data.det3d.modules import LoaderBase
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset
from perceptron.exps.end2end.private.data_cfg.e2e_sensors_sample import (
    lidar as lidar_cfg,
    image as image_cfg,
    _CAMERA_LIST,
    _SENSOR_NAMES,
    _PIPELINE_MULTIFRAME,
)
from perceptron.Perceptron.perceptron.exps.end2end.private.sensor_cfg.e2e_annos_sample import (
    annotation as annotation_cfg,
)

_CAR = GeelyCar1

base_dataset_cfg = dict(
    car=dict(type=_CAR),
    mode="train",
    sensor_names=_SENSOR_NAMES,
    num_frames_per_sample=3,
    loader=dict(
        type=LoaderBase,
        car=_CAR,
        camera_names=_CAMERA_LIST,
        datasets_names=["HUMAN_LABEL_TRACK"],
        only_key_frame=True,
        rebuild=False,
    ),
    lidar=lidar_cfg,
    image=image_cfg,
    annotation=annotation_cfg,
    pipeline=_PIPELINE_MULTIFRAME,
)


class TestE2eDataset(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def test_e2e_dataset_tracking(self):
        """
        Evaluator部分代码暂未合并，仅测试train状态
        """
        for mode in [
            "train",
            # "val"
        ]:
            base_dataset_cfg["loader"]["car"].trainset_partial = dict(
                HUMAN_LABEL_TRACK=["s3://wangningzi-data/dataset/e2e_map_dataset/sample_v3.json"]
            )

            base_dataset_cfg["loader"]["car"].benchmark_partial = dict(
                HUMAN_LABEL_TRACK=["s3://wangningzi-data/dataset/e2e_map_dataset/sample_v3.json"]
            )

            base_dataset_cfg["mode"] = mode
            # 路径待后续更新
            data_train_cfg = mmcv.Config(base_dataset_cfg)

            train_dataset = PrivateE2EDataset(
                **data_train_cfg,
            )
            data = train_dataset[0]

            expected_keys = [
                "frame_id",
                "gt_labels_3d",
                "gt_bboxes_3d",
                "instance_inds",
                "imgs",
                "points",
                "lidar2ego",
                "ego2global",
                "img_metas",
            ]

            self.assertTrue(all(x in data for x in expected_keys))


if __name__ == "__main__":
    unittest.main()
