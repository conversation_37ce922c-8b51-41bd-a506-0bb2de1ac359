import unittest

import numpy as np
from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import cummean
from perceptron.data.det3d.dataset.private_data.eval_utils.detection.config import config_factory
from perceptron.data.det3d.dataset.private_data.eval_utils.evaluation import DetectionEval
from perceptron.data.det3d.dataset.private_data.eval_utils.common.utils import LinearThres2D


def scale_iou(gt_size, pred_size) -> float:
    """
    This method compares predictions to the ground truth in terms of scale.
    It is equivalent to intersection over union (IOU) between the two boxes in 3D,
    if we assume that the boxes are aligned, i.e. translation and rotation are considered identical.
    :param sample_annotation: GT annotation sample.
    :param sample_result: Predicted sample.
    :return: Scale IOU.
    """
    # Validate inputs.
    assert np.all(gt_size > 0), "Error: sample_annotation sizes must be >0."
    assert np.all(pred_size > 0), "Error: sample_result sizes must be >0."

    # Compute IOU.
    min_wlh = np.minimum(gt_size, pred_size)
    volume_annotation = np.prod(gt_size, axis=1)
    volume_result = np.prod(pred_size, axis=1)
    intersection = np.prod(min_wlh, axis=1)  # type: float
    union = volume_annotation + volume_result - intersection  # type: float
    iou = intersection / union

    return iou


def angle_diff(x: float, y: float, period: float) -> float:
    """
    Get the smallest angle difference between 2 angles: the angle from y to x.
    :param x: To angle.
    :param y: From angle.
    :param period: Periodicity in radians for assessing angle difference.
    :return: <float>. Signed smallest between-angle difference in range (-pi, pi).
    """

    # calculate angle difference, modulo to [0, 2*pi]
    diff = (x - y + period / 2) % period - period / 2
    mask = diff > np.pi
    diff[mask] = diff[mask] - (2 * np.pi)  # shift (pi, 2*pi] to (-pi, 0]

    return np.abs(diff)


class TestPrivateEval(unittest.TestCase):
    def setUp(self) -> None:
        self.total_candidate = 10
        pred_boxes = []
        gt_boxes = []
        names = ["car"] * self.total_candidate
        num_pts = [300] * self.total_candidate
        scores = []

        for i in range(self.total_candidate):
            # 添加 ego car 过滤逻辑后，需要整体平移，防止过滤框，缺框。
            pred_boxes.append([i + 1, i + 1, i + 1, (i + 1) * 2, (i + 1) * 2, (i + 1) * 2, np.pi * 0.5])
            gt_boxes.append([i + 1, i + 1, i + 1, (i + 1), (i + 1), (i + 1), 0])
            scores.append(1 - (i + 1) * 0.05)

        pred_boxes = np.stack(pred_boxes, axis=0)
        pred_boxes[[3, 4, 8], :3] += 0.4
        pred_boxes[[0, 1], :3] += 0.1

        self.pred = {
            "boxes_3d": np.stack(pred_boxes, axis=0),
            "score": np.array(scores),
            "name": np.array(names),
        }

        self.gt = {
            "gt_boxes": np.stack(gt_boxes, axis=0),
            "labels": np.array(names),
            "num_lidar_points": np.array(num_pts),
        }

    def test_private_eval_center_dist(self):
        eval_cfg = {
            "class_names": [
                "car",
            ],
            "ignored_names": ["other", "ghost", "masked_area", "tuogua"],
            "class_mapping_rules": None,  # If class_mapping_rules is None, No mapping process run.
            "roi": {
                "mode": "rectangle",
                "range": {
                    "car": [-20, -30, 20, 30],
                },
                "expected_recall": [0.7],
                "dynamic_range": {
                    "range": [-2, 0, 2, 50],
                    # 此处设置最小距离是防止cipo在自车旁边，y值过小，导致roi范围过小。
                    "min_front_dist": 5,  # 前向最小roi距离，设置为0时，表示使用前向cipo的y值。
                },
            },
            "dist_fcn": "center_distance",
            "dist_ths": [0.3],
            "dist_th_tp": 0.3,
            "tp_metrics": ["trans_err", "scale_err", "orient_err"],
            "tp_metrics_err_ratio": {
                "trans_err": [0, 0.2, 1.0, 2.0],
                "orient_err": [0, 0.1, 0.2, 1.0],
            },
            "max_boxes_per_sample": 500,
            "mean_ap_weight": 5,
        }

        eval = DetectionEval(
            config=config_factory(configuration_dict=eval_cfg),
            pred_annos=[self.pred],
            gt_annos=[self.gt],
            output_dir="s3://video3-cache/perceptron/test/data/det3d/test_private_eval/",
            verbose=True,
            use_cache=False,
        )
        metric_summary, metric_extra = eval.main(render_curves=False)

        dist_thres = eval_cfg["dist_th_tp"]
        center_dist = np.sqrt(np.sum((self.pred["boxes_3d"][:, :2] - self.gt["gt_boxes"][:, :2]) ** 2, axis=1))
        tp = center_dist < dist_thres
        fp = center_dist >= dist_thres

        scale_diff = 1 - scale_iou(self.gt["gt_boxes"][:, 3:6], self.pred["boxes_3d"][:, 3:6])
        orientation_diff = angle_diff(self.gt["gt_boxes"][:, 6], self.pred["boxes_3d"][:, 6], 2 * np.pi)

        tp_cumsum = np.cumsum(tp)
        fp_cumsum = np.cumsum(fp)
        prec = tp_cumsum / (tp_cumsum + fp_cumsum)
        rec = tp_cumsum / self.total_candidate
        conf = np.array(self.pred["score"])
        rec_interp = np.linspace(0, 1, 101)

        prec_interp = np.interp(rec_interp, rec, prec, right=0)
        conf_interp = np.interp(rec_interp, rec, conf, right=0)
        conf_nonzero_idx = np.nonzero(conf_interp)[0]
        max_valid_idx = conf_nonzero_idx[-1] if len(conf_nonzero_idx) > 0 else 0

        tp_conf = self.pred["score"][tp]
        tp_trans_error = center_dist[tp]
        tp_scale_error = scale_diff[tp]
        tp_orientation_error = orientation_diff[tp]

        trans_error_cummean = cummean(tp_trans_error)
        scale_error_cummean = cummean(tp_scale_error)
        orientation_error_cummean = cummean(tp_orientation_error)

        trans_error_interp = np.interp(conf_interp[::-1], tp_conf[::-1], trans_error_cummean[::-1])[::-1]
        scale_error_interp = np.interp(conf_interp[::-1], tp_conf[::-1], scale_error_cummean[::-1])[::-1]
        orientation_error_interp = np.interp(conf_interp[::-1], tp_conf[::-1], orientation_error_cummean[::-1])[::-1]

        ap = np.mean(prec_interp)
        ATE = np.mean(trans_error_interp[: max_valid_idx + 1])
        ASE = np.mean(scale_error_interp[: max_valid_idx + 1])
        AOE = np.mean(orientation_error_interp[: max_valid_idx + 1])

        self.assertTrue(np.allclose(ap, metric_summary["mean_ap"]))
        self.assertTrue(np.allclose(ATE, metric_summary["tp_errors"]["trans_err"]))
        self.assertTrue(np.allclose(ASE, metric_summary["tp_errors"]["scale_err"]))
        self.assertTrue(np.allclose(AOE, metric_summary["tp_errors"]["orient_err"]))

        expected_recall = eval_cfg["roi"]["expected_recall"][0]
        above_recall_mask = rec >= expected_recall
        if above_recall_mask.any():
            conf_idx = np.where(above_recall_mask)[0][0]
        else:
            conf_idx = -1

        conf_th = conf[conf_idx]
        conf_idx_tp = np.where(tp_conf >= conf_th)[0][-1]
        trans_err = trans_error_cummean[conf_idx_tp]
        scale_err = scale_error_cummean[conf_idx_tp]
        orient_err = orientation_error_cummean[conf_idx_tp]

        self.assertTrue(
            np.allclose(trans_err, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["trans_err"])
        )
        self.assertTrue(
            np.allclose(scale_err, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["scale_err"])
        )
        self.assertTrue(
            np.allclose(orient_err, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["orient_err"])
        )
        self.assertTrue(np.allclose(np.sum(fp), metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["fp_roi"]))
        self.assertTrue(np.allclose(conf_th, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["conf_th"]))
        self.assertTrue(
            np.allclose(rec[conf_idx], metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["rec_roi"])
        )
        self.assertTrue(
            np.allclose(prec[conf_idx], metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["prec_roi"])
        )

    def test_private_eval_dyn_dist(self):
        eval_cfg = {
            "class_names": [
                "car",
            ],
            "ignored_names": ["other", "ghost", "masked_area", "tuogua"],
            "class_mapping_rules": None,  # If class_mapping_rules is None, No mapping process run.
            "roi": {
                "mode": "rectangle",
                "range": {
                    "car": [-20, -30, 20, 30],
                },
                "expected_recall": [0.7],
                "dynamic_range": {
                    "range": [-2, 0, 2, 50],
                    # 此处设置最小距离是防止cipo在自车旁边，y值过小，导致roi范围过小。
                    "min_front_dist": 5,  # 前向最小roi距离，设置为0时，表示使用前向cipo的y值。
                },
            },
            # "dist_fcn": "center_distance",
            "dist_fcn": {
                "dist_name": "center_distance_linear_threshold",
                "min_thres": (3.0, 0.3),
                "max_thres": (20.0, 0.3),
            },
            "dist_ths": [1.0],
            "dist_th_tp": 1.0,
            "tp_metrics": ["trans_err", "scale_err", "orient_err"],
            "tp_metrics_err_ratio": {
                "trans_err": [0, 0.2, 1.0, 2.0],
                "orient_err": [0, 0.1, 0.2, 1.0],
            },
            "max_boxes_per_sample": 500,
            "mean_ap_weight": 5,
        }

        eval = DetectionEval(
            config=config_factory(configuration_dict=eval_cfg),
            pred_annos=[self.pred],
            gt_annos=[self.gt],
            output_dir="s3://video3-cache/perceptron/test/data/det3d/test_private_eval/",
            verbose=True,
            use_cache=False,
        )
        metric_summary, metric_extra = eval.main(render_curves=False)

        dist_thres = eval_cfg["dist_th_tp"]
        ate_errs = np.sqrt(np.sum((self.pred["boxes_3d"][:, :2] - self.gt["gt_boxes"][:, :2]) ** 2, axis=1))
        linear_dist = LinearThres2D(eval_cfg["dist_fcn"]["min_thres"], eval_cfg["dist_fcn"]["max_thres"])
        center_dist = []

        for gt, pred in zip(eval.gt_boxes.all, eval.pred_boxes.all):
            center_dist.append(linear_dist.distance_calc(gt, pred))
        center_dist = np.array(center_dist)
        tp = center_dist < dist_thres
        fp = center_dist >= dist_thres

        scale_diff = 1 - scale_iou(self.gt["gt_boxes"][:, 3:6], self.pred["boxes_3d"][:, 3:6])
        orientation_diff = angle_diff(self.gt["gt_boxes"][:, 6], self.pred["boxes_3d"][:, 6], 2 * np.pi)

        tp_cumsum = np.cumsum(tp)
        fp_cumsum = np.cumsum(fp)
        prec = tp_cumsum / (tp_cumsum + fp_cumsum)
        rec = tp_cumsum / self.total_candidate
        conf = np.array(self.pred["score"])
        rec_interp = np.linspace(0, 1, 101)

        prec_interp = np.interp(rec_interp, rec, prec, right=0)
        conf_interp = np.interp(rec_interp, rec, conf, right=0)
        conf_nonzero_idx = np.nonzero(conf_interp)[0]
        max_valid_idx = conf_nonzero_idx[-1] if len(conf_nonzero_idx) > 0 else 0

        tp_conf = self.pred["score"][tp]
        tp_trans_error = ate_errs[tp]
        tp_scale_error = scale_diff[tp]
        tp_orientation_error = orientation_diff[tp]

        trans_error_cummean = cummean(tp_trans_error)
        scale_error_cummean = cummean(tp_scale_error)
        orientation_error_cummean = cummean(tp_orientation_error)

        trans_error_interp = np.interp(conf_interp[::-1], tp_conf[::-1], trans_error_cummean[::-1])[::-1]
        scale_error_interp = np.interp(conf_interp[::-1], tp_conf[::-1], scale_error_cummean[::-1])[::-1]
        orientation_error_interp = np.interp(conf_interp[::-1], tp_conf[::-1], orientation_error_cummean[::-1])[::-1]

        ap = np.mean(prec_interp)
        ATE = np.mean(trans_error_interp[: max_valid_idx + 1])
        ASE = np.mean(scale_error_interp[: max_valid_idx + 1])
        AOE = np.mean(orientation_error_interp[: max_valid_idx + 1])

        self.assertTrue(np.allclose(ap, metric_summary["mean_ap"]))
        self.assertTrue(np.allclose(ATE, metric_summary["tp_errors"]["trans_err"]))
        self.assertTrue(np.allclose(ASE, metric_summary["tp_errors"]["scale_err"]))
        self.assertTrue(np.allclose(AOE, metric_summary["tp_errors"]["orient_err"]))

        expected_recall = eval_cfg["roi"]["expected_recall"][0]
        above_recall_mask = rec >= expected_recall
        if above_recall_mask.any():
            conf_idx = np.where(above_recall_mask)[0][0]
        else:
            conf_idx = -1

        conf_th = conf[conf_idx]
        conf_idx_tp = np.where(tp_conf >= conf_th)[0][-1]
        trans_err = trans_error_cummean[conf_idx_tp]
        scale_err = scale_error_cummean[conf_idx_tp]
        orient_err = orientation_error_cummean[conf_idx_tp]

        self.assertTrue(
            np.allclose(trans_err, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["trans_err"])
        )
        self.assertTrue(
            np.allclose(scale_err, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["scale_err"])
        )
        self.assertTrue(
            np.allclose(orient_err, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["orient_err"])
        )
        self.assertTrue(np.allclose(np.sum(fp), metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["fp_roi"]))
        self.assertTrue(np.allclose(conf_th, metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["conf_th"]))
        self.assertTrue(
            np.allclose(rec[conf_idx], metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["rec_roi"])
        )
        self.assertTrue(
            np.allclose(prec[conf_idx], metric_extra[expected_recall][eval_cfg["dist_th_tp"]]["car"]["prec_roi"])
        )


if __name__ == "__main__":
    unittest.main()
