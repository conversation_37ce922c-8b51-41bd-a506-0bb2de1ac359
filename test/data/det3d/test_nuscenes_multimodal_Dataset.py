import unittest


_POINT_CLOUD_RANGE = [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
_IMG_DIM = (256, 704)
_CLASS_NAME = [
    "car",
    "truck",
    "construction_vehicle",
    "bus",
    "trailer",
    "barrier",
    "motorcycle",
    "bicycle",
    "pedestrian",
    "traffic_cone",
]

_AUG_CFG = dict(
    point_cloud_range=_POINT_CLOUD_RANGE,
    img_norm_cfg=dict(img_mean=[123.675, 116.28, 103.53], img_std=[58.395, 57.12, 57.375], to_rgb=True),
    ida_aug_cfg=dict(
        resize_lim=(0.386, 0.55),
        final_dim=_IMG_DIM,
        rot_lim=(-5.4, 5.4),
        H=900,
        W=1600,
        rand_flip=True,
        bot_pct_lim=(0.0, 0.0),
    ),
    bda_aug_cfg=dict(
        rot_lim=(-22.5, 22.5),
        scale_lim=(0.95, 1.05),
        trans_lim=(0.5, 0.5, 0.5),
        flip_dx_ratio=0.5,
        flip_dy_ratio=0.5,
    ),
    gt_sampling_cfg=dict(
        root_path="s3://generalDetection/3DDatasets/nuScenes/",
        data_name="nuScenes",
        data_split="training",
        use_road_plane=False,
        # database_with_fakelidar=True,  # set True to support mmdet3d dbinfo
        filter_by_min_points_cfg=[
            "car:5",
            "truck:5",
            "construction_vehicle:5",
            "bus:5",
            "trailer:5",
            "barrier:5",
            "motorcycle:5",
            "bicycle:5",
            "pedestrian:5",
            "traffic_cone:5",
        ],
        num_point_feature=5,
        remove_extra_width=[0.0, 0.0, 0.0],
        limit_whole_scene=True,
        sampler_groups=[
            "car:2",
            "truck:3",
            "construction_vehicle:7",
            "bus:4",
            "trailer:6",
            "barrier:2",
            "motorcycle:6",
            "bicycle:6",
            "pedestrian:2",
            "traffic_cone:2",
        ],
        class_names=_CLASS_NAME,
    ),
)


class TestNuscenesMultiModalData(unittest.TestCase):

    DATA_CFG = dict(
        root_path="s3://generalDetection/3DDatasets/nuScenes/",
        lidar_key_list=["LIDAR_TOP"],
        img_key_list=["CAM_BACK", "CAM_BACK_LEFT", "CAM_BACK_RIGHT", "CAM_FRONT", "CAM_FRONT_LEFT", "CAM_FRONT_RIGHT"],
        num_lidar_sweeps=10,
        num_cam_sweeps=0,
        lidar_with_timestamp=True,
        class_names=_CLASS_NAME,
        use_cbgs=True,
        aug_cfg=_AUG_CFG,
    )

    EXPECTED_KEYS = ["imgs", "points", "mats_dict", "img_metas", "gt_boxes", "gt_labels"]

    # @pytest.mark.skipif("CI" in os.environ, reason="In CI env, skip nuscenes multimodal test.")
    # def test_nusc_lidarcamera_train_dataset(self):
    #     train_dataset = NuscenesMultiModalData(**self.DATA_CFG, data_split="training")
    #     data = train_dataset[1]
    #     self.assertTrue(all(x in data.keys() for x in self.EXPECTED_KEYS))

    # def test_nusc_lidarcamera_val_dataset(self):
    #     val_dataset = NuscenesMultiModalData(
    #         **self.DATA_CFG,
    #         data_split="validation",
    #     )
    #     data = val_dataset[1]
    #     self.assertTrue(all(x in data.keys() for x in self.EXPECTED_KEYS))

    # image_aug_matrix = torch.tensor(
    #     [
    #         [0.4400, 0.0000, 0.0000, 0.0000],
    #         [0.0000, 0.4400, 0.0000, -140.0000],
    #         [0.0000, 0.0000, 1.0000, 0.0000],
    #         [0.0000, 0.0000, 0.0000, 1.0000],
    #     ]
    # ).expand(1, 6, 4, 4)
    # np.testing.assert_almost_equal(image_aug_matrix.numpy(), data["mats_dict"]["ida_mats"].numpy())

    # bev_aug_matrix = torch.eye(4)
    # np.testing.assert_almost_equal(bev_aug_matrix.numpy(), data["mats_dict"]["bda_mat"].numpy())


if __name__ == "__main__":
    r"""NOTE: just for debug: if you want to use, open it!

    tt = TestNuscenesMultiModalData()
    tt.test_nusc_lidarcamera_train_dataset()
    tt.test_nusc_lidarcamera_val_dataset()

    """
    unittest.main()
