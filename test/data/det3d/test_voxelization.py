import unittest
from perceptron.data.det3d.preprocess.voxelization import Voxelization
from data3d.datasets.once import OnceDataset
import torch


class TestVoxelization(unittest.TestCase):
    def test_infinite_sampler(self):
        dataset = OnceDataset()
        item = dataset[0]
        points = torch.from_numpy(item["lidar_roof"])
        voxelization = Voxelization(
            voxel_size=[0.1, 0.1, 0.2],
            point_cloud_range=[-75.2, -75.2, -5.0, 75.2, 75.2, 3.0],
            max_num_points=5,
            max_voxels=1000,
            num_point_features=4,
            device=torch.device("cpu"),
        )
        voxels, voxel_coords, voxel_num_points = voxelization(points)
        self.assertAlmostEqual(voxels.shape[0], 1000)
        self.assertAlmostEqual(voxels.shape[1], 5)
        self.assertAlmostEqual(voxels.shape[2], 4)
        self.assertAlmostEqual(voxel_coords.shape[0], 1000)
        self.assertAlmostEqual(voxel_coords.shape[1], 4)
        self.assertAlmostEqual(voxel_num_points.shape[0], 1000)


if __name__ == "__main__":
    unittest.main()
