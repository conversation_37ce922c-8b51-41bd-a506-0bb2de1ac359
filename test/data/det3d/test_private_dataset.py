import unittest

import numpy as np
import random

from perceptron.data.det3d.dataset.private_data.dataset import PrivateDatasetWithEval, PrivateDatasetMultiFramesWithEval


class DataConfigs:
    point_cloud_range = [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]
    voxel_size = [0.1, 0.1, 0.2]
    grid_size = np.round(
        (np.array(point_cloud_range[3:6]) - np.array(point_cloud_range[0:3])) / np.array(voxel_size)
    ).astype(np.int64)
    max_num_points = 5
    max_voxels = 60000
    src_num_point_features = 4
    use_num_point_features = 4


class TestPrivateDataset(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def test_private_dataset(self):
        private = PrivateDatasetWithEval(
            data_configs=DataConfigs(),
            data_paths=[
                "s3://video3-cache/data3d/test_dataset/private_dataset/20211119_tracking/21111116002_2021-11-15_changshu3/",
                "s3://video3-cache/data3d/test_dataset/private_dataset/20211119_tracking/21111116002_2021-11-15_changshu3/22.json",
            ],
            lidar_key_list=["middle_lidar"],
            training=False,
            only_key_frame=True,
            class_names=(
                "car",
                "truck",
                "construction_vehicle",
                "bus",
                "motorcycle",
                "bicycle",
                "tricycle",
                "cyclist",
                "pedestrian",
            ),
        )
        data = private.__getitem__(0)
        expected_keys = [
            "points",
            "frame_id",
            "use_lead_xyz",
            "gt_boxes",
        ]

        self.assertTrue(all(x in data for x in expected_keys))
        self.assertTrue(np.allclose(data["points"][0], [-27.723436, 21.70739, -0.8535119, 16]))
        self.assertTrue(
            np.allclose(
                data["gt_boxes"][0], [-0.14587681, 7.64096, -1.04814, 4.443314, 2.1540031, 1.6940444, 1.6165317, 1]
            )
        )

    def test_private_dataset_multi_frame(self):
        class_names = (
            "car",
            "truck",
            "construction_vehicle",
            "bus",
            "motorcycle",
            "bicycle",
            "tricycle",
            "cyclist",
            "pedestrian",
        )

        data_config = DataConfigs()
        data_config.src_num_point_features = 5
        data_config.use_num_point_features = 5
        private = PrivateDatasetMultiFramesWithEval(
            data_configs=data_config,
            data_paths=[
                "s3://video3-cache/data3d/test_dataset/private_dataset/20211119_tracking/21111116002_2021-11-15_changshu3/",
                "s3://video3-cache/data3d/test_dataset/private_dataset/20211119_tracking/21111116002_2021-11-15_changshu3/22.json",
            ],
            lidar_key_list=["middle_lidar"],
            use_adjacent_frames=[-5, -1, 0],
            training=False,
            only_key_frame=False,
            class_names=class_names,
        )
        data = private[1]
        current_idx = private.dataset.key_frame_index[1]
        last_key_frame_idx = current_idx - 5
        last_annos = private.dataset._get_annos(last_key_frame_idx)
        last_boxes_to_current_boxes = private.dataset.project_boxes_to_reference_frame(
            last_annos["gt_boxes"], current_idx=last_key_frame_idx, reference_idx=current_idx
        )

        expected_keys = [
            "points",
            "frame_id",
            "use_lead_xyz",
            "gt_boxes",
        ]

        self.assertTrue(all(x in data for x in expected_keys))
        self.assertTrue(np.allclose(data["points"][0], [-3.26541019, 3.58088309, -1.38305959, 5.0, -1.0]))
        self.assertTrue(
            np.allclose(
                data["gt_boxes"][0],
                [-0.16449326, 7.6609864, -1.0502243, 4.443314, 2.1540031, 1.6940444, 1.6165317, 1.0],
            )
        )
        self.assertTrue(
            np.allclose(
                last_boxes_to_current_boxes[0],
                [-0.14593933, 7.64094452, -1.05000812, 4.44331408, 2.15400314, 1.69404435, 1.61653173],
            )
        )
        # multi frame fuse point show url: https://oss.iap.hh-b.brainpp.cn/wjn-share/xviz/?s=https://oss.iap.hh-b.brainpp.cn/video3-cache/perceptron/test/data/det3d/test_private_dataset/test_private_dataset_multi_frame/xviz_data/

        # # 生成多帧融合可视化代码如下：
        # from data3d.visualization.xviz_pointcloud_visualizer import XvizVisualizer

        # xviz_vis = XvizVisualizer(
        #     save_path="s3://video3-cache/perceptron/test/data/det3d/test_private_dataset/test_private_dataset_multi_frame/xviz_data/",
        #     class_names=class_names,
        #     show_lidars=[f"points_{i}" for i in private.use_adjacent_frames],
        #     exist_ok=True,
        #     show_boxes=["gt"],
        #     show_boxes_color={"gt": (255, 0, 0)},
        # )
        # import time

        # st_time = time.time()
        # en_time = st_time + 2
        # xviz_vis.vis_init(st_time, en_time)

        # show_points = {}
        # show_points_color = {}
        # for ii, i in enumerate(private.use_adjacent_frames):
        #     show_points.update({f"points_{i}": data["points"][np.where(data["points"][:, -1] == i)][:, :3]})
        #     color = [0, 0]
        #     color.insert(ii, 255)
        #     show_points_color.update({f"points_{i}": color})
        # xviz_vis.add_frame(
        #     points=show_points,
        #     point_colors=show_points_color,
        #     frame_timestamp=st_time + 0.5,
        #     boxes_info={
        #         "gt": {
        #             "boxes": last_boxes_to_current_boxes,
        #             "name": last_annos["labels"],
        #         },
        #     },
        # )

        # xviz_vis.show()


if __name__ == "__main__":
    unittest.main()
