import unittest
import numpy as np
import random

from perceptron.data.det3d.modules import LoaderBase
from perceptron.data.det3d.modules.annotation import E2EAnnotations
from perceptron.data.det3d.utils.functional import initialize_object
from perceptron.data.det3d.source.config import GeelyCar1
from perceptron.Perceptron.perceptron.exps.end2end.private.sensor_cfg.e2e_annos_sample import (
    annotation as annotation_cfg,
)


_CAMERA_LIST = [
    "cam_front_left_120",
    "cam_front_right_120",
    "cam_back_left_120",
    "cam_back_right_120",
    "cam_front_70_left",
    "cam_back_120",
]


class TestE2eAnnotation(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    def test_e2e_annotation(self):
        for mode in ["train", "val"]:

            loader_cfg = dict(
                type=LoaderBase,
                car=Geely<PERSON>ar1,
                camera_names=_CAMERA_LIST,
                datasets_names=["HUMAN_LABEL_TRACK"],
                only_key_frame=True,
                rebuild=False,
            )
            loader_cfg["car"].trainset_partial = dict(
                HUMAN_LABEL_TRACK=["s3://wangningzi-data/dataset/e2e_map_dataset/sample_v3.json"]
            )
            loader_cfg["car"].benchmark_partial = dict(
                HUMAN_LABEL_TRACK=["s3://wangningzi-data/dataset/e2e_map_dataset/sample_v3.json"]
            )

            loader_cfg["mode"] = mode

            loader = initialize_object(loader_cfg)
            loader()

            annos = E2EAnnotations(loader.output, mode, annotation_cfg)

            data_dict = annos[0]

            excepted_key_det = ["gt_labels", "gt_boxes"]
            excepted_key_map = ["targets", "tran_mats_dict"]
            self.assertTrue(all(x in data_dict for x in excepted_key_det))
            self.assertTrue(all(x in data_dict for x in excepted_key_map))


if __name__ == "__main__":
    unittest.main()
