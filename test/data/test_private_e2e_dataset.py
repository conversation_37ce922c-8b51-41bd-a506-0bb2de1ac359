import mmcv  # noqa
import unittest
import numpy as np
import random

from perceptron.exps.end2end.private.data_cfg.track_data_cfg_jl1_256x704 import (
    base_dataset_cfg as DATA_TRAIN_CFG,
)  # noqa
from perceptron.data.det3d.private.private_multimodal import PrivateE2EDataset  # noqa


class TestE2eDataset(unittest.TestCase):
    def setUp(self) -> None:
        np.random.seed(0)
        random.seed(0)

    # def test_e2e_dataset_tracking(self):
    #     """
    #     test可能需要的判定项目较多，待后期完善
    #     """

    #     DATA_TRAIN_CFG["loader"]["car"].trainset_partial = dict(
    #         HUMAN_LABEL_TRACK=["s3://wangningzi-data/dataset/e2e_map_dataset/sample_v3.json"]
    #     )
    #     # 路径待后续更新
    #     data_train_cfg = mmcv.Config(DATA_TRAIN_CFG)

    # train_dataset = PrivateE2EDataset(
    #     **data_train_cfg,
    # )
    # data = train_dataset[0]

    # expected_keys = [
    #     "frame_id",
    #     "gt_labels_3d",
    #     "gt_bboxes_3d",
    #     "instance_inds",
    #     "gt_forecasting_locs",
    #     "gt_forecasting_angles",
    #     "gt_forecasting_masks",
    #     "gt_forecasting_bboxes",
    #     "imgs",
    #     "points",
    #     "lidar2ego",
    #     "ego2global",
    #     "img_metas",
    # ]

    # self.assertTrue(all(x in data for x in expected_keys))


if __name__ == "__main__":
    unittest.main()
