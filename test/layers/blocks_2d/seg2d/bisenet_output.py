import torch
import torch.nn as nn
import unittest
from perceptron.layers.blocks_2d.seg2d import BiSeNetOutput, BiSeNetRegOutput


class TestBiSeNetOutput(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 2
        self.input_channel = 4
        self.output_channel = 8
        self.height, self.width = 4, 4
        self.n_classes = 2
        self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)

        self.bisenet_head = nn.Sequential(BiSeNetOutput(self.input_channel, self.output_channel, self.n_classes))
        self.bisenet_head.train()
        self.bisenet_head_output = [
            [
                [
                    [0.0881565734744072, 0.19162896275520325, 0.07234320044517517, 0.23765915632247925],
                    [0.06994641572237015, -0.6032375693321228, 0.4288819134235382, -0.08938869088888168],
                    [0.0722566545009613, 0.9310369491577148, -0.34508973360061646, 0.638733983039856],
                    [-0.016538357362151146, -0.28222841024398804, 0.562014639377594, 0.6468972563743591],
                ],
                [
                    [0.29871803522109985, 0.12641602754592896, -0.017103705555200577, 0.4313451647758484],
                    [0.0019028877140954137, 1.1394349336624146, 0.19918522238731384, 0.13730233907699585],
                    [-0.002046997891739011, -0.09466326236724854, 0.5364086031913757, -0.47950974106788635],
                    [0.10689735412597656, 0.3211575150489807, -0.39918065071105957, 0.016602473333477974],
                ],
            ],
            [
                [
                    [0.075727179646492, 0.3703916072845459, 0.6983393430709839, 0.7036253809928894],
                    [-0.05665862560272217, 0.19478915631771088, 0.0, 0.46294718980789185],
                    [1.032031536102295, 0.1556188315153122, 0.22022253274917603, -0.3660832345485687],
                    [-0.47430217266082764, -0.6151316165924072, 0.1882653832435608, 0.5597728490829468],
                ],
                [
                    [-0.06966276466846466, -0.2148110717535019, -0.47153130173683167, -0.4668820798397064],
                    [0.19900380074977875, 0.2295500487089157, 0.0, 0.1284385323524475],
                    [-0.3100314438343048, -0.12805987894535065, -0.07234936952590942, 0.32061535120010376],
                    [0.5964765548706055, 0.8765348196029663, -0.01481166947633028, -0.39123889803886414],
                ],
            ],
        ]

    def tearDown(self):
        pass

    def test_bisenet_head(self):
        logits = self.bisenet_head(self.inputs)
        self.assertListEqual(logits.tolist(), self.bisenet_head_output)


class TestBiSeNetRegOutput(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 2
        self.input_channel = 4
        self.output_channel = 8
        self.height, self.width = 4, 4
        self.n_classes = 2
        self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)

        self.bisenet_reghead = nn.Sequential(BiSeNetRegOutput(self.input_channel, self.output_channel, self.n_classes))
        self.bisenet_reghead.train()
        self.bisenet_reghead_output = [
            [
                [
                    [-0.03672763705253601, -0.30869022011756897, 0.5327931642532349, -0.217587411403656],
                    [0.5515863299369812, -1.36587393283844, -0.12873108685016632, -0.04862133786082268],
                    [-0.05689989775419235, 1.2975562810897827, -0.5076908469200134, 0.7174079418182373],
                    [-0.2997777462005615, -0.5500138998031616, 0.7859654426574707, 0.584515392780304],
                ],
                [
                    [0.41185203194618225, 0.43504995107650757, -0.5515769720077515, 0.8355016112327576],
                    [-0.4697239100933075, 1.7600833177566528, 0.510399341583252, -0.485321044921875],
                    [0.00128334597684443, -0.4442053735256195, 0.4420940577983856, -0.9384945631027222],
                    [0.06434599310159683, 0.37784677743911743, -0.8981279730796814, 0.006381126586347818],
                ],
            ],
            [
                [
                    [-0.17070697247982025, 0.40333622694015503, 0.9023501873016357, 1.1529715061187744],
                    [-0.44784682989120483, 0.15665529668331146, -1.1356050968170166, 0.6871892809867859],
                    [1.0381945371627808, -0.5582990646362305, -0.18811659514904022, -0.6586180925369263],
                    [-1.287253975868225, -1.714773416519165, 0.22988638281822205, 0.6414259672164917],
                ],
                [
                    [-0.32028236985206604, -0.47168421745300293, -0.9079473614692688, -0.9981071949005127],
                    [0.29305118322372437, -0.06707300245761871, 0.2082599699497223, -0.03295367956161499],
                    [-0.3622671663761139, -0.32700520753860474, -0.11025391519069672, 0.3761724531650543],
                    [0.9570732116699219, 1.5881538391113281, -0.3160055875778198, -0.5665187835693359],
                ],
            ],
        ]

    def tearDown(self):
        pass

    def test_bisenet_reghead(self):
        logits = self.bisenet_reghead(self.inputs)
        self.assertListEqual(logits.tolist(), self.bisenet_reghead_output)


if __name__ == "__main__":
    unittest.main()
