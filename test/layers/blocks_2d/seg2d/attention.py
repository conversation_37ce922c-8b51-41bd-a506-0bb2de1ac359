import torch
import torch.nn as nn
import unittest
from perceptron.layers.blocks_2d.seg2d import AttentionRefinementModule


class TestAttentionRefinementModule(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 2
        self.input_channel = 4
        self.output_channel = 8
        self.height, self.width = 4, 4
        self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)

        self.attention_refinement_module = nn.Sequential(
            AttentionRefinementModule(self.input_channel, self.output_channel)
        )
        self.attention_refinement_module.train()
        self.attention_refinement_module_output = [
            [
                [
                    [0.8272488713264465, 0.0, 0.3303394317626953, 0.484374463558197],
                    [0.0, 1.8518272638320923, 0.0, 0.5355258584022522],
                    [0.0639660656452179, 0.6331510543823242, 1.0561434030532837, 0.0],
                    [0.0, 0.2699463963508606, 0.5898528099060059, 0.23805569112300873],
                ],
                [
                    [0.0, 0.0, 0.11136417090892792, 0.0],
                    [0.0, 0.0, 0.0, 0.14074908196926117],
                    [0.0, 0.24065399169921875, 0.0, 0.3378990888595581],
                    [0.0, 0.08768285810947418, 0.37717390060424805, 0.05355452373623848],
                ],
                [
                    [0.0, 0.0, 0.3113345205783844, 0.05089547485113144],
                    [0.07977839559316635, 0.1140991821885109, 0.0, 0.41660597920417786],
                    [0.0, 0.10622429847717285, 0.08392475545406342, 0.0],
                    [0.0, 0.06607373058795929, 0.3578927516937256, 0.10326948761940002],
                ],
                [
                    [0.6489489078521729, 0.0, 0.0, 0.6435180902481079],
                    [0.03249272704124451, 0.9067156910896301, 0.5171622037887573, 0.0],
                    [0.19230960309505463, 1.642616868019104, 0.0, 0.0],
                    [0.28116944432258606, 0.0, 0.0, 2.0096077919006348],
                ],
                [
                    [0.43687719106674194, 0.05867663398385048, 0.0, 1.5429081916809082],
                    [0.022920921444892883, 0.0, 1.5737355947494507, 0.0],
                    [0.0, 1.0737481117248535, 0.6909350156784058, 0.0],
                    [0.15100203454494476, 0.4585968255996704, 0.018264545127749443, 0.21481084823608398],
                ],
                [
                    [0.0, 0.0, 0.05315505713224411, 0.11647068709135056],
                    [0.0, 0.365780234336853, 0.0, 0.41103503108024597],
                    [0.0, 0.19004374742507935, 0.37136784195899963, 0.286810040473938],
                    [0.0, 0.420983225107193, 0.0, 0.0],
                ],
                [
                    [0.011135061271488667, 0.33717429637908936, 0.0, 0.3158130347728729],
                    [0.06903674453496933, 0.0, 0.3828382194042206, 0.0],
                    [0.0, 0.13693518936634064, 0.0, 0.1299590766429901],
                    [0.0, 0.0034940342884510756, 0.0, 0.0],
                ],
                [
                    [0.18522736430168152, 0.47237759828567505, 0.0, 0.1625477373600006],
                    [0.0, 1.484533429145813, 0.0, 0.0],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.2177213430404663, 0.3003961443901062, 0.0, 0.3264674246311188],
                ],
            ],
            [
                [
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 0.44545525312423706, 0.0, 0.1623440980911255],
                    [0.0, 0.0, 0.0795600488781929, 0.0],
                    [0.0, 0.0, 0.27434664964675903, 0.0],
                ],
                [
                    [0.13816887140274048, 0.6452242136001587, 0.857721745967865, 0.8792744874954224],
                    [0.0, 0.0, 0.0, 0.0],
                    [1.2497750520706177, 0.4554976522922516, 0.9475805163383484, 0.0],
                    [0.0, 0.0, 0.5406814217567444, 0.6081011891365051],
                ],
                [
                    [0.0, 0.0, 0.0, 0.7121425867080688],
                    [1.100563883781433, 0.31620708107948303, 0.0, 0.40682560205459595],
                    [0.14750948548316956, 0.0, 0.0, 0.0],
                    [1.1713542938232422, 0.09359347820281982, 0.0, 0.0],
                ],
                [
                    [0.0, 0.11646009236574173, 0.0, 0.0],
                    [0.1374836564064026, 0.2504776120185852, 0.0, 0.11263054609298706],
                    [0.2375427633523941, 0.0, 0.0, 0.0],
                    [0.0, 0.0, 0.0, 0.02292799763381481],
                ],
                [
                    [0.0, 0.20233739912509918, 0.0, 0.0],
                    [0.08535324037075043, 0.0, 0.0, 0.09565651416778564],
                    [0.05064798519015312, 0.0, 0.44943714141845703, 0.0],
                    [0.0, 0.20570048689842224, 0.0, 0.0],
                ],
                [
                    [0.09812171012163162, 0.5606388449668884, 0.0, 0.447366863489151],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.419241726398468, 0.0, 0.9338892698287964, 0.7735758423805237],
                    [0.5389366149902344, 0.0, 0.0, 0.08224225044250488],
                ],
                [
                    [0.0, 0.0, 0.4693150520324707, 0.5920069217681885],
                    [0.1373770534992218, 0.0, 0.0, 1.1707321405410767],
                    [1.3316642045974731, 0.0, 0.0, 0.0],
                    [0.570106029510498, 0.7550956010818481, 0.0, 0.30270862579345703],
                ],
                [
                    [0.0, 0.0, 0.07796478271484375, 0.0],
                    [0.0798037052154541, 0.0, 0.0, 0.07340241223573685],
                    [0.4435664713382721, 0.12177786231040955, 0.04356967657804489, 0.17861150205135345],
                    [0.39939233660697937, 0.6858954429626465, 0.12687565386295319, 0.0],
                ],
            ],
        ]

    def tearDown(self):
        pass

    def test_attention_refinement_module(self):
        logits = self.attention_refinement_module(self.inputs)
        self.assertListEqual(logits.tolist(), self.attention_refinement_module_output)


if __name__ == "__main__":
    unittest.main()
