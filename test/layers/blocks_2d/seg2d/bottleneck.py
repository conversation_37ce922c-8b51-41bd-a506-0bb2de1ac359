import torch
import torch.nn as nn
import unittest
from perceptron.layers.blocks_2d.seg2d import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CatBottleneck


class TestAddBottleneck(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 2
        self.input_channel = 4
        self.output_channel = 8
        self.height, self.width = 4, 4
        self.block_num = 3
        self.stride = 1
        self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)

        self.add_bottleneck_stride1 = nn.Sequential(
            AddBottleneck(self.input_channel, self.output_channel, block_num=self.block_num, stride=1)
        )
        self.add_bottleneck_stride1.train()
        self.add_bottleneck_stride1_output = [
            [
                [
                    [0.031551554799079895, 0.4982652962207794, -0.6628168821334839, 0.2797390818595886],
                    [0.3071689009666443, 1.214992880821228, 0.018442869186401367, 1.0726230144500732],
                    [-0.8288775086402893, 1.0368905067443848, 2.06364369392395, 1.039335012435913],
                    [-0.10032962262630463, 2.4814867973327637, 2.4926369190216064, 1.203658938407898],
                ],
                [
                    [0.0017973855137825012, 0.6971109509468079, 2.072309970855713, 0.5491500496864319],
                    [0.009548082947731018, 2.1083741188049316, 0.6310000419616699, 0.20341002941131592],
                    [0.5377139449119568, 1.0070220232009888, 0.05709909647703171, 0.9756811857223511],
                    [0.2768017053604126, -0.8225070238113403, 2.796619415283203, -0.7956824898719788],
                ],
                [
                    [0.11241601407527924, 0.5154967308044434, 0.046697892248630524, 1.1605943441390991],
                    [-0.7625917196273804, -0.49648064374923706, 1.5470777750015259, 0.7150135636329651],
                    [1.404162883758545, 3.3034422397613525, 2.253837823867798, -0.6875913739204407],
                    [1.3883252143859863, -1.1360101699829102, -0.8608701229095459, 1.5398709774017334],
                ],
                [
                    [0.6625896692276001, 1.3731300830841064, -0.9388664364814758, 1.4858206510543823],
                    [0.5711638927459717, 0.2830852270126343, 0.36508476734161377, 1.1727323532104492],
                    [0.11457939445972443, 1.6818156242370605, 1.8038229942321777, 0.3573262095451355],
                    [-0.5270606875419617, 0.05411887541413307, 2.234659433364868, 1.1601885557174683],
                ],
                [
                    [0.2868768572807312, -0.10482215136289597, 1.2587186098098755, 0.7511895298957825],
                    [-0.28508996963500977, 1.7847617864608765, 0.5987345576286316, 0.7426732778549194],
                    [1.8258743286132812, 1.9177525043487549, 1.8190710544586182, -0.3556976318359375],
                    [0.26123136281967163, 1.197259545326233, 1.8238532543182373, 0.7666959166526794],
                ],
                [
                    [-0.03244399279356003, 1.2092024087905884, 0.4560840129852295, 0.6107642650604248],
                    [0.33805468678474426, -0.9428526759147644, 0.07057566195726395, 1.1909356117248535],
                    [-0.9579535722732544, 0.8168562650680542, 3.0707848072052, -0.7979686260223389],
                    [0.24056531488895416, 1.4373574256896973, 1.3864840269088745, 1.3634201288223267],
                ],
                [
                    [-0.11822402477264404, -0.04146958515048027, -0.6435684561729431, -0.8750017285346985],
                    [-0.2540226876735687, 0.32511481642723083, -0.16572798788547516, -0.7403869032859802],
                    [1.4220654964447021, 0.06150329113006592, -0.22135859727859497, -0.5538237690925598],
                    [2.1353371143341064, 1.0901192426681519, -1.817535400390625, -0.3501818776130676],
                ],
                [
                    [0.20516471564769745, -0.13901597261428833, -0.19604116678237915, -0.8747025728225708],
                    [1.8986222743988037, 0.3917831778526306, -1.0675420761108398, 0.10445302724838257],
                    [-0.8303138613700867, -1.7402423620224, -1.5738271474838257, 1.3331047296524048],
                    [2.2589128017425537, 1.1396175622940063, 0.8012619614601135, -0.8615201711654663],
                ],
            ],
            [
                [
                    [0.013074612244963646, 1.707876443862915, 0.7115412354469299, 1.7054009437561035],
                    [-1.136643648147583, -1.17396080493927, -1.176929235458374, -0.8971042037010193],
                    [0.450448215007782, -0.7192105650901794, 2.017810583114624, 0.8405524492263794],
                    [0.16914813220500946, -0.21539491415023804, -1.6865447759628296, -0.8798779249191284],
                ],
                [
                    [1.3441619873046875, -0.36828771233558655, -1.6334539651870728, 2.0845065116882324],
                    [1.3646314144134521, -1.981547474861145, 1.0601418018341064, -0.6661390662193298],
                    [-1.6315103769302368, 1.0902601480484009, 0.29899489879608154, -0.8194964528083801],
                    [1.3856918811798096, 0.9669481515884399, -0.5378298163414001, 0.040145158767700195],
                ],
                [
                    [-0.043442487716674805, 0.13845296204090118, -0.817996621131897, 0.6576952338218689],
                    [-0.823689341545105, 0.3540191352367401, -0.18739694356918335, 0.2887958884239197],
                    [-1.1433594226837158, 0.4827291965484619, 3.7959532737731934, -0.2696667015552521],
                    [-1.4119831323623657, -0.10921482741832733, 0.8570054173469543, -0.0814174935221672],
                ],
                [
                    [0.15875142812728882, 0.8677979707717896, 1.0731574296951294, 0.24410457909107208],
                    [0.5208332538604736, -0.3323703110218048, -1.8409240245819092, -0.32095208764076233],
                    [1.0883691310882568, 0.18318378925323486, 2.613328218460083, 0.5532872080802917],
                    [-1.5269726514816284, -0.09738773107528687, -1.296254277229309, -0.30254194140434265],
                ],
                [
                    [0.1840324103832245, 0.03891513869166374, -1.7690398693084717, 1.5052464008331299],
                    [0.16730275750160217, -1.9118595123291016, -0.809354841709137, -0.6686669588088989],
                    [-0.9360239505767822, 0.5285420417785645, 0.12111721932888031, -0.8121806979179382],
                    [1.1523598432540894, 2.522531509399414, -0.2966444492340088, -0.2608878016471863],
                ],
                [
                    [1.0048928260803223, 2.002030372619629, 0.05154815688729286, 1.8691494464874268],
                    [-0.27237367630004883, 0.28904402256011963, -0.36243104934692383, -0.9639490246772766],
                    [-0.2732071280479431, -1.5815036296844482, 2.0104904174804688, 0.5725242495536804],
                    [0.5017417669296265, 0.4693112373352051, -1.5945159196853638, -0.8942275047302246],
                ],
                [
                    [-0.3616982400417328, -0.5790998935699463, 0.44027942419052124, -1.5669395923614502],
                    [1.6637046337127686, 2.5597269535064697, 2.054302453994751, 1.0636881589889526],
                    [4.013901233673096, 0.6743737459182739, -0.9722541570663452, 0.5253937840461731],
                    [1.304010033607483, 0.449441134929657, 2.023430824279785, 0.2718871533870697],
                ],
                [
                    [0.6356713175773621, 0.03566901013255119, 1.0852445363998413, 0.594542384147644],
                    [0.8311551809310913, -0.28975704312324524, 0.016704488545656204, -0.26074206829071045],
                    [2.1368885040283203, 2.278923749923706, -0.9158292412757874, 1.2121479511260986],
                    [3.689678907394409, -0.054139576852321625, -0.2770093083381653, 0.9721538424491882],
                ],
            ],
        ]

        self.add_bottleneck_stride2 = nn.Sequential(
            AddBottleneck(self.input_channel, self.output_channel, block_num=self.block_num, stride=2)
        )
        self.add_bottleneck_stride2.train()
        self.add_bottleneck_stride2_output = [
            [
                [[-0.27277541160583496, 0.3379136323928833], [1.5099036693572998, 2.0085794925689697]],
                [[-1.4638144969940186, -0.8000214695930481], [2.1520285606384277, 3.0298876762390137]],
                [[0.6051786541938782, 0.20967254042625427], [-2.113926887512207, -0.7369545102119446]],
                [[0.7003285884857178, 0.2800508737564087], [-1.75962233543396, -0.4135477542877197]],
                [[1.979001760482788, 0.006082803010940552], [-1.4085477590560913, 0.7702121138572693]],
                [[-1.1368529796600342, 0.34599435329437256], [2.4290707111358643, -0.2639504671096802]],
                [[-0.4494975805282593, -2.131309747695923], [1.7973861694335938, 0.4014597535133362]],
                [[-0.04762353375554085, -1.0534842014312744], [-0.06051608547568321, 4.031376838684082]],
            ],
            [
                [[-0.08096444606781006, -0.5344617962837219], [-0.18148881196975708, -2.7867064476013184]],
                [[-0.6499296426773071, -1.080723524093628], [-0.927189826965332, -0.2602371573448181]],
                [[-1.0919862985610962, 1.3676061630249023], [-1.2320241928100586, 2.9924347400665283]],
                [[-0.8878251314163208, 2.517733573913574], [-2.6038994789123535, 2.166781425476074]],
                [[-0.24866116046905518, 2.276332378387451], [-0.3225458860397339, 0.70344078540802]],
                [[1.612892508506775, -0.5925405025482178], [0.20405149459838867, 0.816591739654541]],
                [[2.235541820526123, 1.8652423620224], [0.43942850828170776, -0.6473739743232727]],
                [[0.04576960206031799, -0.22638700902462006], [0.3470410406589508, 0.3895186185836792]],
            ],
        ]

    def tearDown(self):
        pass

    def test_add_bottleneck_stride1(self):
        logits = self.add_bottleneck_stride1(self.inputs)
        self.assertListEqual(logits.tolist(), self.add_bottleneck_stride1_output)

    def test_add_bottleneck_stride2(self):
        logits = self.add_bottleneck_stride2(self.inputs)
        self.assertListEqual(logits.tolist(), self.add_bottleneck_stride2_output)


class TestCatBottleneck(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 2
        self.input_channel = 4
        self.output_channel = 8
        self.height, self.width = 4, 4
        self.block_num = 3
        self.stride = 1
        self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)

        self.cat_bottleneck_stride1 = nn.Sequential(
            CatBottleneck(self.input_channel, self.output_channel, block_num=self.block_num, stride=1)
        )
        self.cat_bottleneck_stride1.train()
        self.cat_bottleneck_stride1_output = [
            [
                [
                    [0.0, 1.0770186185836792, 0.0, 0.0],
                    [0.8434179425239563, 0.0, 2.837709426879883, 0.0],
                    [0.052217867225408554, 0.16897915303707123, 0.0, 1.3562525510787964],
                    [0.0, 0.49902820587158203, 0.0900166928768158, 0.0],
                ],
                [
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 2.0580127239227295, 0.0, 1.6860202550888062],
                    [0.4589217007160187, 0.26709502935409546, 0.8614266514778137, 0.8380205035209656],
                    [0.44125983119010925, 0.7424899935722351, 0.7175952792167664, 0.0],
                ],
                [
                    [1.1791670322418213, 1.3198953866958618, 0.0, 0.0],
                    [0.05167422443628311, 0.0, 0.0, 0.0],
                    [0.0, 0.0, 0.0, 0.07151763141155243],
                    [0.913430392742157, 0.0, 0.0, 0.0],
                ],
                [
                    [0.0, 0.0, 1.4019384384155273, 0.7547338008880615],
                    [0.12208832055330276, 0.0, 0.0, 0.7659004330635071],
                    [0.8384690880775452, 1.0438412427902222, 0.6961340308189392, 0.6655957698822021],
                    [0.0, 1.2026301622390747, 0.7967530488967896, 0.6732354164123535],
                ],
                [
                    [0.031551554799079895, 0.043569158762693405, -0.6628168821334839, -0.18654821813106537],
                    [0.3071689009666443, -1.284263253211975, -0.13069425523281097, 1.0726230144500732],
                    [-1.2732009887695312, 0.3705209493637085, 1.4843699932098389, -0.6323415637016296],
                    [-0.10032962262630463, 1.8522181510925293, 1.2601773738861084, 1.203658938407898],
                ],
                [
                    [-0.10802649706602097, -0.6049429774284363, 1.6259067058563232, -0.16635680198669434],
                    [-0.1419968605041504, 1.7259461879730225, -0.1734117865562439, 0.01425017137080431],
                    [0.014010263606905937, -0.02261701226234436, 0.0003336216905154288, 0.6627365350723267],
                    [0.2768017053604126, -0.8225070238113403, 1.5108729600906372, -0.7956824898719788],
                ],
                [
                    [0.11241601407527924, 0.5154967308044434, 0.046697892248630524, 1.1605943441390991],
                    [-0.7625917196273804, -0.49648064374923706, 1.0622044801712036, 0.5581377744674683],
                    [1.3760923147201538, 2.3210699558258057, 1.2677370309829712, -1.1537957191467285],
                    [0.1564406007528305, -1.1360101699829102, -0.8608701229095459, 1.5398709774017334],
                ],
                [
                    [0.6625896692276001, 0.6200937032699585, -0.9388664364814758, 0.9757612347602844],
                    [0.5711638927459717, -1.3898905515670776, 0.36508476734161377, 0.6939540505409241],
                    [0.051308710128068924, 1.6818156242370605, 0.316222608089447, -0.2170577496290207],
                    [-0.5567865967750549, 0.05411887541413307, 0.17801886796951294, 1.1601885557174683],
                ],
            ],
            [
                [
                    [0.0, 0.3808334469795227, 0.0, 0.06364522129297256],
                    [1.0113202333450317, 0.0, 0.0, 0.0],
                    [0.0, 1.4221594333648682, 1.901201844215393, 0.0],
                    [0.0, 0.587679922580719, 0.19598998129367828, 0.0],
                ],
                [
                    [0.22871652245521545, 0.0, 0.0, 0.18538202345371246],
                    [0.9309995174407959, 0.0, 0.9381058216094971, 0.7584559321403503],
                    [0.0, 0.5370711088180542, 0.0, 1.4577988386154175],
                    [0.0406230092048645, 0.0, 0.0, 0.0],
                ],
                [
                    [0.7332358360290527, 0.17433100938796997, 0.5241321921348572, 0.14894059300422668],
                    [1.2099103927612305, 0.9442870020866394, 0.2772786021232605, 0.0],
                    [1.0478206872940063, 0.46210721135139465, 0.0, 0.2181989848613739],
                    [0.6140636801719666, 0.0, 0.0, 1.8479911088943481],
                ],
                [
                    [0.4075227379798889, 1.2155481576919556, 0.4576238691806793, 0.9287061095237732],
                    [0.0, 0.0, 0.48873770236968994, 0.2591017782688141],
                    [0.0, 0.0, 0.61191725730896, 0.5089534521102905],
                    [0.0, 0.0, 0.0, 0.0],
                ],
                [
                    [0.013074612244963646, 1.0745936632156372, 0.7115412354469299, 1.1368310451507568],
                    [-1.136643648147583, -1.17396080493927, -1.8233516216278076, -0.8971042037010193],
                    [0.450448215007782, -1.2754155397415161, 1.3364412784576416, 0.8405524492263794],
                    [0.16914813220500946, -0.21539491415023804, -1.6865447759628296, -0.8798779249191284],
                ],
                [
                    [0.7622183561325073, -0.36828771233558655, -1.6334539651870728, 1.4450483322143555],
                    [1.1547256708145142, -1.981547474861145, 0.0012029409408569336, -0.6661390662193298],
                    [-1.6315103769302368, 0.8244426846504211, -1.7949825525283813, -0.9619889259338379],
                    [1.3856918811798096, 0.9669481515884399, -0.5378298163414001, 0.040145158767700195],
                ],
                [
                    [-0.6728934645652771, 0.018350837752223015, -0.817996621131897, -0.15048088133335114],
                    [-0.9449051022529602, 0.3540191352367401, -0.18739694356918335, 0.2887958884239197],
                    [-1.3311187028884888, -2.175076961517334, 1.5979111194610596, -0.2696667015552521],
                    [-1.4119831323623657, -0.10921482741832733, 0.1860644668340683, -0.0814174935221672],
                ],
                [
                    [-0.4233713150024414, 0.8677979707717896, 1.0731574296951294, 0.06345264613628387],
                    [-1.5329458713531494, -0.3323703110218048, -1.8409240245819092, -0.32095208764076233],
                    [0.3084622621536255, -1.341994047164917, 2.613328218460083, 0.5532872080802917],
                    [-1.5269726514816284, -0.7888779640197754, -1.296254277229309, -0.30254194140434265],
                ],
            ],
        ]

        self.cat_bottleneck_stride2 = nn.Sequential(
            CatBottleneck(self.input_channel, self.output_channel, block_num=self.block_num, stride=2)
        )
        self.cat_bottleneck_stride2.train()
        self.cat_bottleneck_stride2_output = [
            [
                [[0.3175014853477478, 1.4883882999420166], [-0.8327110409736633, -1.0733749866485596]],
                [[-0.45466333627700806, 0.45146679878234863], [-1.2720569372177124, 1.8629040718078613]],
                [[1.6573346853256226, 0.0], [0.39956164360046387, 0.0]],
                [[0.0, 0.27465611696243286], [1.3663586378097534, 0.0]],
                [[0.025361288338899612, 1.0342066287994385], [-0.09138418734073639, 0.5009716153144836]],
                [[0.23560106754302979, -1.4991859197616577], [-0.6083489060401917, 0.6699557304382324]],
                [[-0.39377719163894653, -0.10988961160182953], [0.6558473706245422, 0.1231938973069191]],
                [[-0.6144701242446899, -1.1371198892593384], [0.04558973386883736, -0.6811534762382507]],
            ],
            [
                [[0.31885096430778503, 1.0378432273864746], [-1.5950573682785034, 0.3385593593120575]],
                [[-0.07154542952775955, 0.4539428949356079], [0.46851497888565063, -1.4385629892349243]],
                [[0.0, 0.5693193674087524], [0.689175009727478, 0.0]],
                [[0.0, 0.24505829811096191], [0.41269001364707947, 0.7318037152290344]],
                [[-0.05291789397597313, -0.2240527868270874], [-2.3146207332611084, 1.122436285018921]],
                [[-0.3212597668170929, 0.6923103332519531], [1.8207937479019165, -0.9898662567138672]],
                [[0.6057340502738953, -0.4357232451438904], [1.611838459968567, -2.0572237968444824]],
                [[0.5189511179924011, -0.020648937672376633], [2.3244664669036865, -0.43561485409736633]],
            ],
        ]

    def tearDown(self):
        pass

    def test_cat_bottleneck_stride1(self):
        logits = self.cat_bottleneck_stride1(self.inputs)
        self.assertListEqual(logits.tolist(), self.cat_bottleneck_stride1_output)

    def test_cat_bottleneck_stride2(self):
        logits = self.cat_bottleneck_stride2(self.inputs)
        self.assertListEqual(logits.tolist(), self.cat_bottleneck_stride2_output)


if __name__ == "__main__":
    unittest.main()
