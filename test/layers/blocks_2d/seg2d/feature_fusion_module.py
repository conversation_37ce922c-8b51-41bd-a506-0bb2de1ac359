import torch
import torch.nn as nn
import unittest
from perceptron.layers.blocks_2d.seg2d import FeatureFusionModule


class TestFeatureFusionModule(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 2
        self.input_channel = 4
        self.output_channel = 8
        self.ffm_inplane = 8
        self.ffm_outplane = 16
        self.height, self.width = 4, 4
        self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)

        self.feature_fusion_module = nn.Sequential(FeatureFusionModule(self.ffm_inplane, self.ffm_outplane))
        self.feature_fusion_module.train()
        self.feature_fusion_module_output = [
            [
                [
                    [0.3683023154735565, 0.4160308837890625, 0.7373781204223633, 0.0],
                    [0.0, 0.4585987329483032, 0.0, 1.189558506011963],
                    [1.0251545906066895, 0.0, 0.0, 0.1310143619775772],
                    [0.0, 0.0, 0.0, 0.0],
                ],
                [
                    [0.06971442699432373, 0.0, 0.0, 1.1936423778533936],
                    [1.6666436195373535, 0.06367239356040955, 0.0, 0.0],
                    [0.0, 0.0, 5.0979743003845215, 0.0],
                    [0.052529796957969666, 1.4171488285064697, 1.457976222038269, 0.6613856554031372],
                ],
                [
                    [0.0, 0.6810348629951477, 0.0, 1.6745446920394897],
                    [0.48623672127723694, 0.1823941469192505, 0.4295133352279663, 0.0],
                    [0.0, 0.0, 3.7656729221343994, 0.2237829864025116],
                    [0.0, 2.399592638015747, 1.2329249382019043, 1.0740735530853271],
                ],
                [
                    [1.8359953165054321, 0.1026439517736435, 1.1330642700195312, 2.0409739017486572],
                    [0.0, 2.487565517425537, 0.0, 0.0],
                    [0.0240886602550745, 1.2306218147277832, 0.027833836153149605, 0.0],
                    [0.0, 0.0, 0.0, 0.3341548442840576],
                ],
                [
                    [0.0, 0.0, 2.106133222579956, 0.0],
                    [0.7785565853118896, 0.0, 0.0, 0.86631178855896],
                    [1.9920024871826172, 0.20113810896873474, 0.0, 0.019221283495426178],
                    [3.1729319095611572, 0.0, 0.0, 0.0],
                ],
                [
                    [0.0685369148850441, 1.1126407384872437, 0.0, 1.9670531749725342],
                    [0.0, 0.7466470003128052, 1.4481050968170166, 0.0],
                    [0.0, 0.2940601706504822, 1.0748125314712524, 0.3071046471595764],
                    [0.0, 2.043450355529785, 0.19640016555786133, 1.7883964776992798],
                ],
                [
                    [1.4952466487884521, 0.0, 1.0950924158096313, 1.8846824169158936],
                    [1.9683581590652466, 1.824973225593567, 0.0, 0.33669087290763855],
                    [0.0, 0.0, 4.375675201416016, 0.0],
                    [0.0, 0.7286360263824463, 1.1954662799835205, 0.0],
                ],
                [
                    [0.0, 0.547984778881073, 0.0, 0.0],
                    [0.0, 0.0, 1.4981257915496826, 0.0],
                    [0.14159318804740906, 0.3790227472782135, 0.0, 0.8801782727241516],
                    [1.2158188819885254, 0.0, 0.0, 0.6155220866203308],
                ],
                [
                    [0.4182679057121277, 0.0, 1.8969272375106812, 0.0],
                    [2.180683135986328, 0.037090033292770386, 0.0, 0.8056042790412903],
                    [0.8409514427185059, 0.0, 3.4496703147888184, 0.0],
                    [0.0, 0.0, 1.124896764755249, 0.0],
                ],
                [
                    [1.5862916707992554, 1.1399422883987427, 0.0, 2.52622127532959],
                    [0.0, 2.6079108715057373, 0.6756801605224609, 0.0],
                    [0.0, 0.34633201360702515, 0.0, 0.0],
                    [0.0, 0.8398518562316895, 0.0, 0.3523205518722534],
                ],
                [
                    [1.8082975149154663, 0.36063557863235474, 0.20357593894004822, 2.8099985122680664],
                    [0.20701643824577332, 2.6227834224700928, 0.10968756675720215, 0.0],
                    [0.0, 0.9964983463287354, 1.779214859008789, 0.0],
                    [0.0, 0.7081707715988159, 0.0, 0.7846419215202332],
                ],
                [
                    [0.0, 0.0, 0.5246225595474243, 0.0],
                    [0.43010908365249634, 0.0, 0.0, 1.0163295269012451],
                    [1.0804051160812378, 0.0, 0.0, 0.5326009392738342],
                    [0.914873480796814, 0.0, 0.330860435962677, 0.0],
                ],
                [
                    [0.0, 0.2664601802825928, 0.0, 0.0],
                    [0.0, 0.0, 1.2444087266921997, 0.07202553749084473],
                    [0.5479964017868042, 0.24621137976646423, 0.0, 0.9420468211174011],
                    [1.868333339691162, 0.0, 0.0, 0.3419513404369354],
                ],
                [
                    [1.2331732511520386, 1.3210382461547852, 0.0, 2.119149923324585],
                    [0.0, 2.1817455291748047, 0.5815381407737732, 0.0],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 1.046644687652588, 0.0, 0.0],
                ],
                [
                    [0.0, 0.6268376111984253, 0.0, 0.0],
                    [0.0, 0.0, 1.450876235961914, 0.0],
                    [0.0, 0.0, 1.465324878692627, 1.1565990447998047],
                    [1.5387459993362427, 2.0540692806243896, 0.7992992401123047, 1.8277380466461182],
                ],
                [
                    [0.0, 0.60530686378479, 0.0, 0.0],
                    [0.0, 0.0, 1.019308090209961, 0.0],
                    [0.0, 0.0, 0.0, 1.4886183738708496],
                    [1.3358451128005981, 0.6687861680984497, 0.08466200530529022, 0.12222178280353546],
                ],
            ],
            [
                [
                    [2.2618141174316406, 0.0, 2.5429961681365967, 0.0],
                    [0.0, 0.302475243806839, 0.0, 1.6198170185089111],
                    [0.979133129119873, 2.692749261856079, 0.0, 0.0],
                    [2.9867238998413086, 0.0, 0.0, 1.708794116973877],
                ],
                [
                    [0.0, 0.9771662950515747, 0.0, 2.5479583740234375],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 0.0, 0.23981277644634247, 0.0],
                    [0.0, 0.0, 1.096691370010376, 0.0],
                ],
                [
                    [0.0, 1.6803182363510132, 0.0, 2.0301311016082764],
                    [0.46743717789649963, 0.0, 0.0, 0.0],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 0.17301546037197113, 1.245518445968628, 0.0],
                ],
                [
                    [0.0, 0.17522457242012024, 0.0, 1.3302206993103027],
                    [0.0, 0.0, 0.42538708448410034, 2.5960607528686523],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.3034624755382538, 0.12466991692781448, 0.0, 1.129438877105713],
                ],
                [
                    [0.27905768156051636, 0.0, 0.0, 0.0],
                    [0.020100872963666916, 0.8475457429885864, 0.23842783272266388, 0.0],
                    [0.0, 3.756352424621582, 1.4092820882797241, 1.8259766101837158],
                    [0.3574691116809845, 0.0, 0.0, 0.8349839448928833],
                ],
                [
                    [0.0, 1.8245644569396973, 0.0, 1.0783464908599854],
                    [0.7189406156539917, 0.0, 0.6342047452926636, 1.6064646244049072],
                    [0.14369820058345795, 0.0, 0.0, 0.0],
                    [0.0, 1.6998558044433594, 0.6165602207183838, 0.0],
                ],
                [
                    [0.9111742973327637, 0.005258445627987385, 0.0, 2.702915906906128],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 0.0, 0.6593040227890015, 0.0],
                ],
                [
                    [0.0, 0.0, 1.7010064125061035, 0.0],
                    [1.2702964544296265, 0.2658894658088684, 0.9438806772232056, 3.2812960147857666],
                    [0.40502551198005676, 0.13750073313713074, 0.3984166383743286, 0.0],
                    [1.0640631914138794, 2.1896603107452393, 0.0, 0.47585582733154297],
                ],
                [
                    [1.3307791948318481, 0.0, 0.0, 1.4685475826263428],
                    [0.0, 0.2958732545375824, 0.0, 0.0],
                    [0.0, 2.8231041431427, 0.20738619565963745, 0.8652177453041077],
                    [0.0, 0.0, 0.43922021985054016, 0.054833464324474335],
                ],
                [
                    [0.0, 0.705264151096344, 1.09032142162323, 0.809198796749115],
                    [0.0, 0.0, 0.0, 3.372652053833008],
                    [0.5636044144630432, 0.0, 0.0, 0.0],
                    [0.67682284116745, 0.8858315944671631, 0.0, 0.4376397728919983],
                ],
                [
                    [0.0, 0.8365166783332825, 0.0, 2.1477296352386475],
                    [0.0, 0.0, 0.2899271249771118, 1.6432437896728516],
                    [0.0, 0.0, 0.0, 0.0],
                    [0.0, 0.11908645927906036, 0.08670242130756378, 0.12218227237462997],
                ],
                [
                    [2.499422788619995, 0.0, 0.8116891384124756, 0.0],
                    [0.30523672699928284, 0.9708307385444641, 0.0, 0.0],
                    [0.294691264629364, 5.73931360244751, 0.9467346668243408, 0.5621199607849121],
                    [1.0815997123718262, 0.0, 0.0, 0.21032190322875977],
                ],
                [
                    [0.0035867546685039997, 0.0, 1.558241367340088, 0.0],
                    [1.2987180948257446, 0.5107486248016357, 0.8523386716842651, 2.5029289722442627],
                    [0.31622928380966187, 1.403944492340088, 0.7540027499198914, 0.10110265761613846],
                    [1.137099266052246, 1.8418948650360107, 0.0, 0.5561980605125427],
                ],
                [
                    [0.38175565004348755, 0.6231549382209778, 1.7953883409500122, 0.44685637950897217],
                    [0.0, 0.0, 0.0, 3.0332186222076416],
                    [0.9015145301818848, 0.0, 0.0, 0.0],
                    [1.0883145332336426, 0.766563892364502, 0.0, 0.26838448643684387],
                ],
                [
                    [0.0, 1.6602389812469482, 0.0, 0.0],
                    [1.906740427017212, 0.25254666805267334, 0.7011406421661377, 0.0],
                    [0.0, 0.46311670541763306, 1.134772777557373, 0.0],
                    [0.0, 1.582761287689209, 1.0198869705200195, 0.0],
                ],
                [
                    [1.7806599140167236, 0.1837417036294937, 1.5530788898468018, 0.0],
                    [1.8509645462036133, 0.864559531211853, 0.0, 0.0],
                    [0.555732011795044, 4.203916549682617, 1.2101788520812988, 0.0],
                    [0.15257157385349274, 1.1971385478973389, 0.2612558901309967, 0.0],
                ],
            ],
        ]

    def tearDown(self):
        pass

    def test_feature_fusion_module(self):
        logits = self.feature_fusion_module([self.inputs, self.inputs])
        self.assertListEqual(logits.tolist(), self.feature_fusion_module_output)


if __name__ == "__main__":
    unittest.main()
