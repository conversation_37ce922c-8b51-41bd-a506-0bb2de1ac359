import torch
import torch.nn as nn
import unittest
from perceptron.layers.blocks_2d.seg2d import ConvBNReLU


class TesConvBNReLU(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 3
        self.input_channel = 3
        self.output_channel = 2
        self.height, self.width = 5, 5
        self.net1 = nn.Sequential(
            ConvBNReLU(self.input_channel, self.output_channel, kernel_size=3, stride=2, padding=1)
        )
        self.net1.train()
        self.inputs1 = torch.randn(self.batch_size, self.input_channel, self.height, self.width)
        self.labels1 = torch.randint(0, self.output_channel, [self.batch_size, self.height, self.width])
        self.outputs1 = torch.tensor(
            [
                [
                    [[0.0, 0.0, 1.1390479803085327], [0.0, 0.0, 0.0], [0.0, 0.17290239036083221, 0.0]],
                    [
                        [0.0, 0.0, 0.0],
                        [0.4426412880420685, 2.625995635986328, 1.7828624248504639],
                        [1.1621594429016113, 0.45313534140586853, 0.2071826457977295],
                    ],
                ],
                [
                    [
                        [1.3837844133377075, 0.0, 0.9165658950805664],
                        [0.6256345510482788, 0.1892991065979004, 0.0],
                        [0.8941973447799683, 0.0, 0.38890621066093445],
                    ],
                    [
                        [0.3807348608970642, 0.8487293720245361, 0.0],
                        [0.0, 0.0, 0.0],
                        [0.26011112332344055, 0.6611456274986267, 0.032373204827308655],
                    ],
                ],
                [
                    [
                        [0.0, 0.0, 1.612737774848938],
                        [0.03876330330967903, 2.0985941886901855, 1.0878725051879883],
                        [0.2884678244590759, 0.0, 0.0],
                    ],
                    [[0.0, 0.0, 0.0], [0.0594606027007103, 0.0, 0.0], [0.5752466320991516, 0.9944410920143127, 0.0]],
                ],
            ]
        )
        torch.manual_seed(55)
        self.batch_size = 2
        self.input_channel = 10
        self.output_channel = 2
        self.height, self.width = 4, 4
        self.net2 = nn.Sequential(ConvBNReLU(self.input_channel, self.output_channel, activation=None))
        self.net2.train()
        self.inputs2 = torch.randn(self.batch_size, self.input_channel, self.height, self.width)
        self.labels2 = torch.randint(0, self.output_channel, [self.batch_size, self.height, self.width])
        self.outputs2 = torch.tensor(
            [
                [
                    [
                        [0.5563409924507141, 0.7053717374801636, -3.0798168182373047, 0.5510640740394592],
                        [-0.008159254677593708, -0.15107688307762146, 0.9312033653259277, -0.7580091953277588],
                        [1.4668223857879639, -0.6791969537734985, 1.2287496328353882, 0.6891652941703796],
                        [0.15827925503253937, -0.13632327318191528, 1.739866852760315, -0.571185827255249],
                    ],
                    [
                        [-0.16304220259189606, -0.14377620816230774, 0.1505221426486969, -1.499430537223816],
                        [0.7170376181602478, 0.6362530589103699, 1.6209392547607422, 1.459397792816162],
                        [-0.5374383926391602, 1.4267964363098145, -2.1441845893859863, 1.5709258317947388],
                        [-0.5810278654098511, -0.5664023756980896, 0.9180704951286316, -0.799690842628479],
                    ],
                ],
                [
                    [
                        [-0.5990084409713745, 0.4689459800720215, -0.14959435164928436, -0.46836212277412415],
                        [1.0066637992858887, -0.19747363030910492, 1.1634007692337036, -0.6509700417518616],
                        [-0.08014433830976486, -2.519822120666504, -0.14038260281085968, 0.7423027157783508],
                        [0.5297960638999939, -0.3865226209163666, -0.7519334554672241, -0.6099907159805298],
                    ],
                    [
                        [1.0844066143035889, -1.085552453994751, 0.24695079028606415, -0.1440693587064743],
                        [-0.9584314227104187, 0.4355393350124359, -0.9909830093383789, 1.6280536651611328],
                        [-1.292490005493164, 0.6985509395599365, -0.6457741856575012, -0.8163390755653381],
                        [-1.160797357559204, 0.32816559076309204, -0.151945099234581, 0.759765625],
                    ],
                ],
            ]
        )

    def tearDown(self):
        pass

    def test_conv_bn_relu_case1(self):
        logits = self.net1(self.inputs1)
        self.assertListEqual(list(logits.shape), list(self.outputs1.shape))
        self.assertListEqual(logits.tolist(), self.outputs1.tolist())

    def test_conv_bn_relu_case2(self):
        logits = self.net2(self.inputs2)
        self.assertListEqual(list(logits.shape), list(self.outputs2.shape))
        self.assertListEqual(logits.tolist(), self.outputs2.tolist())


if __name__ == "__main__":
    unittest.main()
