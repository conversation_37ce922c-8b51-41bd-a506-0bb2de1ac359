import unittest

import torch
from perceptron.layers.losses.det3d import Center<PERSON><PERSON>eg<PERSON>oss, FocalLoss


class TestDet3dLosses(unittest.TestCase):
    def setUp(self) -> None:
        torch.manual_seed(0)

    def test_centernet_regloss(self):
        loss_func = CenterNetRegLoss()
        pred = torch.randn(1, 10, 2)
        gt = torch.ones(1, 10, 2)
        mask = torch.ones(1, 10)
        loss = loss_func._reg_loss(pred, gt, mask).mean()
        self.assertAlmostEqual(loss.item(), 1.006590485572815)

    def test_focal_loss(self):
        loss_func = FocalLoss(alpha=0.25, gamma=2)
        pred = torch.sigmoid(torch.randn(1, 1, 2, 2))
        gt = torch.ones(1, 1, 2, 2)
        loss = loss_func.forward(pred, gt)
        self.assertAlmostEqual(loss.item(), 0.13678912818431854)


if __name__ == "__main__":
    unittest.main()
