import torch
import torch.nn as nn
import torch.nn.functional as F
import unittest
from perceptron.layers.losses.seg2d import (
    OhemCELoss,
    WeightedOhemCELoss,
    DetailAggregateLoss,
    HeatMapLoss,
    OffsetVecLoss,
)


class TestSeg2dLosses(unittest.TestCase):
    def setUp(self):
        torch.manual_seed(15)
        self.batch_size = 16
        self.input_channel = 3
        self.output_channel = 10
        self.detail_channel = 1
        self.height, self.width = 20, 20
        self.heat_map_gamma, self.heat_map_beta = 2, 4
        self.mask_threshold = 100
        self.net = nn.Sequential(nn.Conv2d(self.input_channel, self.output_channel, kernel_size=3, stride=2, padding=1))
        self.sigmoid = nn.Sigmoid()
        self.net.train()
        self.detail_net = nn.Sequential(
            nn.Conv2d(self.input_channel, self.detail_channel, kernel_size=3, stride=2, padding=1)
        )
        self.detail_net.train()
        with torch.no_grad():
            self.inputs = torch.randn(self.batch_size, self.input_channel, self.height, self.width)
            self.labels = torch.randint(0, self.output_channel, [self.batch_size, self.height, self.width])

    def tearDown(self):
        pass

    def test_ohem_ce_loss(self):
        criteria = OhemCELoss(thresh=0.7, n_min=self.batch_size * self.height * self.width // self.batch_size)
        logits = self.net(self.inputs)
        logits = F.interpolate(logits, [self.height, self.width], mode="bilinear", align_corners=True)
        loss = criteria(logits, self.labels)
        self.assertAlmostEqual(loss.item(), 2.370147943496704)

    def test_weighted_ohem_ce_loss(self):
        criteria = WeightedOhemCELoss(
            thresh=0.7,
            n_min=self.batch_size * self.height * self.width // self.batch_size,
            num_classes=self.output_channel,
        )
        logits = self.net(self.inputs)
        logits = F.interpolate(logits, [self.height, self.width], mode="bilinear", align_corners=True)
        loss = criteria(logits, self.labels)
        self.assertAlmostEqual(loss.item(), 20.9058895111084)

    def test_detail_aggregate_loss(self):
        criteria = DetailAggregateLoss()
        logits = self.detail_net(self.inputs)
        bce_loss, dice_loss = criteria(logits, self.labels)
        self.assertAlmostEqual(bce_loss.item(), 0.6883653998374939)
        self.assertAlmostEqual(dice_loss.item(), 0.38874995708465576)

    def test_heatmap_loss(self):
        criteria = HeatMapLoss(self.heat_map_gamma, self.heat_map_beta)
        logits = self.net(self.inputs)
        logits = F.interpolate(logits, [self.height, self.width], mode="bilinear", align_corners=True)
        logits = self.sigmoid(logits)
        labels = self.labels.unsqueeze(1).repeat(1, self.output_channel, 1, 1)
        labels = self.sigmoid(labels)
        loss = criteria(logits, labels)
        self.assertAlmostEqual(loss.item(), 0.14983026683330536)

    def test_offsetvec_loss(self):
        criteria = OffsetVecLoss(self.mask_threshold)
        logits = self.net(self.inputs)
        logits = F.interpolate(logits, [self.height, self.width], mode="bilinear", align_corners=True)
        labels = self.labels.unsqueeze(1).repeat(1, self.output_channel, 1, 1)
        loss = criteria(logits, labels)
        self.assertAlmostEqual(loss.item(), 4.601118087768555)


if __name__ == "__main__":
    unittest.main()
