import unittest

import torch
from perceptron.layers.blocks_3d.det3d import MeanVFE


class TestDet3dLosses(unittest.TestCase):
    def setUp(self) -> None:
        torch.manual_seed(15)

    def test_mean_vfe(self):
        vfe_layer = MeanVFE(3)
        inp = {
            "voxels": torch.randn(10, 5, 3),
            "voxel_num_points": torch.Tensor(10),
        }
        out = vfe_layer.forward(inp)

        self.assertAlmostEqual(out["voxel_features"].shape[-1], vfe_layer.get_output_feature_dim())
        self.assertAlmostEqual(out["voxel_features"].ndim, 2)


if __name__ == "__main__":
    unittest.main()
