import unittest

import torch

from perceptron.layers.lr_scheduler import CosineLRScheduler, GroupLRScheduler, StepLRScheduler


class TestGroupLRScheduler(unittest.TestCase):
    def test_group_lr_scheduler(self):
        base_lr1 = 0.1
        base_lr2 = 0.2
        iters_per_epoch = 50
        total_epochs = 100

        optimizer1 = torch.optim.SGD(
            (torch.nn.Parameter(torch.randn(256)),),
            lr=base_lr1,
            momentum=0.9,
            weight_decay=0,
            nesterov=False,
        )
        optimizer2 = torch.optim.SGD(
            (torch.nn.Parameter(torch.randn(256)),),
            lr=base_lr2,
            momentum=0.9,
            weight_decay=0,
            nesterov=False,
        )

        scheduler1 = CosineLRScheduler(optimizer1, base_lr1, iters_per_epoch, total_epochs, end_lr=1e-3)
        scheduler2 = StepLRScheduler(
            optimizer2, base_lr2, iters_per_epoch, total_epochs, milestones=[30, 60, 90], gamma=0.1
        )

        group_optimizer = torch.optim.SGD(
            [
                {"name": "group1", "params": torch.nn.Parameter(torch.randn(256))},
                {"name": "group2", "params": torch.nn.Parameter(torch.randn(256))},
            ],
            lr=base_lr1,
            momentum=0.9,
            weight_decay=0,
            nesterov=False,
        )

        group_scheduler = GroupLRScheduler(
            {"group1": scheduler1, "group2": scheduler2},
            group_optimizer,
            {"group1": base_lr1, "group2": base_lr2},
            iters_per_epoch,
            total_epochs,
        )

        for epoch in range(total_epochs):
            for i in range(iters_per_epoch):
                iters = epoch * iters_per_epoch + i + 1
                scheduler1.step(iters)
                expected_lr1 = optimizer1.param_groups[0]["lr"]
                scheduler2.step(iters)
                expected_lr2 = optimizer2.param_groups[0]["lr"]

                group_scheduler.step(iters)
                lr1 = list(filter(lambda x: x["name"] == "group1", group_optimizer.param_groups))[0]["lr"]
                lr2 = list(filter(lambda x: x["name"] == "group2", group_optimizer.param_groups))[0]["lr"]

                msg1 = "GroupLRScheduler has changed! With epoch {}, iter {},'\
                    'Group2 the original lr: {}, current lr: {}".format(
                    epoch, i, expected_lr1, lr1
                )
                self.assertEqual(expected_lr1, lr1, msg1)
                msg2 = "GroupLRScheduler has changed! With epoch {}, iter {},'\
                    'Group2 the original lr: {}, current lr: {}".format(
                    epoch, i, expected_lr2, lr2
                )
                self.assertEqual(expected_lr2, lr2, msg2)


if __name__ == "__main__":
    unittest.main()
